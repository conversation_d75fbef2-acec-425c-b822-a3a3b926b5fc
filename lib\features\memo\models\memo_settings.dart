import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Note sorting options
enum NoteSortBy {
  dateCreated,
  dateModified,
  title,
  category,
  priority,
}

/// Note view modes
enum NoteViewMode {
  list,
  grid,
  compact,
}

/// Todo sorting options
enum TodoSortBy {
  dueDate,
  priority,
  dateCreated,
  title,
  category,
}

/// Voice memo quality options
enum VoiceMemoQuality {
  low,
  medium,
  high,
  lossless,
}

/// Export format options
enum ExportFormat {
  txt,
  md,
  pdf,
  docx,
  html,
}

/// Memo Suite application settings
@immutable
class MemoSettings {
  // Display Settings
  final NoteViewMode noteViewMode;
  final bool showNotePreview;
  final bool showWordCount;
  final bool showCharacterCount;
  final double fontSize;
  final String fontFamily;
  final bool useSystemFont;
  final double lineHeight;
  final bool enableMarkdownPreview;
  final bool enableSyntaxHighlighting;

  // Note Settings
  final NoteSortBy noteSortBy;
  final bool noteSortAscending;
  final bool autoSaveNotes;
  final int autoSaveIntervalSeconds;
  final String defaultNoteCategory;
  final bool enableSpellCheck;
  final bool enableAutoCorrect;
  final bool showLineNumbers;
  final bool enableWordWrap;
  final ExportFormat defaultExportFormat;

  // Todo Settings
  final TodoSortBy todoSortBy;
  final bool todoSortAscending;
  final bool showCompletedTodos;
  final bool enableTodoNotifications;
  final int reminderMinutesBefore;
  final bool enableRecurringTodos;
  final bool autoArchiveCompleted;
  final int autoArchiveDays;

  // Voice Memo Settings
  final VoiceMemoQuality voiceMemoQuality;
  final String audioFormat;
  final bool enableAutoTranscription;
  final int maxRecordingMinutes;
  final bool enableNoiseReduction;
  final bool enableAutoStop;
  final int autoStopSilenceSeconds;
  final bool saveToCloud;

  // Organization Settings
  final bool enableCategories;
  final bool enableTags;
  final bool enableFavorites;
  final bool enableArchive;
  final bool enableTrash;
  final int trashRetentionDays;
  final bool enableSearch;
  final bool enableGlobalSearch;

  // Sync & Backup Settings
  final bool enableAutoBackup;
  final int backupFrequencyHours;
  final bool backupToCloud;
  final bool syncAcrossDevices;
  final bool syncOnWifiOnly;
  final int maxBackupFiles;
  final String lastBackupDate;

  // Security Settings
  final bool enableEncryption;
  final bool requirePinForAccess;
  final String pinHash;
  final bool enableBiometricAuth;
  final bool hideContentInRecents;
  final bool enableScreenshotProtection;
  final int autoLockMinutes;

  // Notification Settings
  final bool enableNotifications;
  final bool enableDueDateReminders;
  final bool enableDailyDigest;
  final TimeOfDay? dailyDigestTime;
  final bool enableQuietHours;
  final TimeOfDay? quietHoursStart;
  final TimeOfDay? quietHoursEnd;

  // Advanced Settings
  final bool enableDebugMode;
  final bool enableBetaFeatures;
  final bool enableAnalytics;
  final bool enableCrashReporting;
  final bool enablePerformanceMonitoring;
  final int maxUndoSteps;
  final bool enableCollaboration;

  const MemoSettings({
    // Display Settings
    this.noteViewMode = NoteViewMode.list,
    this.showNotePreview = true,
    this.showWordCount = true,
    this.showCharacterCount = false,
    this.fontSize = 16.0,
    this.fontFamily = 'System',
    this.useSystemFont = true,
    this.lineHeight = 1.5,
    this.enableMarkdownPreview = true,
    this.enableSyntaxHighlighting = true,

    // Note Settings
    this.noteSortBy = NoteSortBy.dateModified,
    this.noteSortAscending = false,
    this.autoSaveNotes = true,
    this.autoSaveIntervalSeconds = 30,
    this.defaultNoteCategory = 'General',
    this.enableSpellCheck = true,
    this.enableAutoCorrect = true,
    this.showLineNumbers = false,
    this.enableWordWrap = true,
    this.defaultExportFormat = ExportFormat.md,

    // Todo Settings
    this.todoSortBy = TodoSortBy.dueDate,
    this.todoSortAscending = true,
    this.showCompletedTodos = false,
    this.enableTodoNotifications = true,
    this.reminderMinutesBefore = 15,
    this.enableRecurringTodos = true,
    this.autoArchiveCompleted = false,
    this.autoArchiveDays = 30,

    // Voice Memo Settings
    this.voiceMemoQuality = VoiceMemoQuality.medium,
    this.audioFormat = 'aac',
    this.enableAutoTranscription = false,
    this.maxRecordingMinutes = 60,
    this.enableNoiseReduction = true,
    this.enableAutoStop = false,
    this.autoStopSilenceSeconds = 5,
    this.saveToCloud = false,

    // Organization Settings
    this.enableCategories = true,
    this.enableTags = true,
    this.enableFavorites = true,
    this.enableArchive = true,
    this.enableTrash = true,
    this.trashRetentionDays = 30,
    this.enableSearch = true,
    this.enableGlobalSearch = true,

    // Sync & Backup Settings
    this.enableAutoBackup = true,
    this.backupFrequencyHours = 24,
    this.backupToCloud = false,
    this.syncAcrossDevices = false,
    this.syncOnWifiOnly = true,
    this.maxBackupFiles = 10,
    this.lastBackupDate = '',

    // Security Settings
    this.enableEncryption = false,
    this.requirePinForAccess = false,
    this.pinHash = '',
    this.enableBiometricAuth = false,
    this.hideContentInRecents = false,
    this.enableScreenshotProtection = false,
    this.autoLockMinutes = 5,

    // Notification Settings
    this.enableNotifications = true,
    this.enableDueDateReminders = true,
    this.enableDailyDigest = false,
    this.dailyDigestTime,
    this.enableQuietHours = false,
    this.quietHoursStart,
    this.quietHoursEnd,

    // Advanced Settings
    this.enableDebugMode = false,
    this.enableBetaFeatures = false,
    this.enableAnalytics = true,
    this.enableCrashReporting = true,
    this.enablePerformanceMonitoring = false,
    this.maxUndoSteps = 50,
    this.enableCollaboration = false,
  });

  /// Create a copy with modified values
  MemoSettings copyWith({
    NoteViewMode? noteViewMode,
    bool? showNotePreview,
    bool? showWordCount,
    bool? showCharacterCount,
    double? fontSize,
    String? fontFamily,
    bool? useSystemFont,
    double? lineHeight,
    bool? enableMarkdownPreview,
    bool? enableSyntaxHighlighting,
    NoteSortBy? noteSortBy,
    bool? noteSortAscending,
    bool? autoSaveNotes,
    int? autoSaveIntervalSeconds,
    String? defaultNoteCategory,
    bool? enableSpellCheck,
    bool? enableAutoCorrect,
    bool? showLineNumbers,
    bool? enableWordWrap,
    ExportFormat? defaultExportFormat,
    TodoSortBy? todoSortBy,
    bool? todoSortAscending,
    bool? showCompletedTodos,
    bool? enableTodoNotifications,
    int? reminderMinutesBefore,
    bool? enableRecurringTodos,
    bool? autoArchiveCompleted,
    int? autoArchiveDays,
    VoiceMemoQuality? voiceMemoQuality,
    String? audioFormat,
    bool? enableAutoTranscription,
    int? maxRecordingMinutes,
    bool? enableNoiseReduction,
    bool? enableAutoStop,
    int? autoStopSilenceSeconds,
    bool? saveToCloud,
    bool? enableCategories,
    bool? enableTags,
    bool? enableFavorites,
    bool? enableArchive,
    bool? enableTrash,
    int? trashRetentionDays,
    bool? enableSearch,
    bool? enableGlobalSearch,
    bool? enableAutoBackup,
    int? backupFrequencyHours,
    bool? backupToCloud,
    bool? syncAcrossDevices,
    bool? syncOnWifiOnly,
    int? maxBackupFiles,
    String? lastBackupDate,
    bool? enableEncryption,
    bool? requirePinForAccess,
    String? pinHash,
    bool? enableBiometricAuth,
    bool? hideContentInRecents,
    bool? enableScreenshotProtection,
    int? autoLockMinutes,
    bool? enableNotifications,
    bool? enableDueDateReminders,
    bool? enableDailyDigest,
    TimeOfDay? dailyDigestTime,
    bool? enableQuietHours,
    TimeOfDay? quietHoursStart,
    TimeOfDay? quietHoursEnd,
    bool? enableDebugMode,
    bool? enableBetaFeatures,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    bool? enablePerformanceMonitoring,
    int? maxUndoSteps,
    bool? enableCollaboration,
  }) {
    return MemoSettings(
      noteViewMode: noteViewMode ?? this.noteViewMode,
      showNotePreview: showNotePreview ?? this.showNotePreview,
      showWordCount: showWordCount ?? this.showWordCount,
      showCharacterCount: showCharacterCount ?? this.showCharacterCount,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      useSystemFont: useSystemFont ?? this.useSystemFont,
      lineHeight: lineHeight ?? this.lineHeight,
      enableMarkdownPreview: enableMarkdownPreview ?? this.enableMarkdownPreview,
      enableSyntaxHighlighting: enableSyntaxHighlighting ?? this.enableSyntaxHighlighting,
      noteSortBy: noteSortBy ?? this.noteSortBy,
      noteSortAscending: noteSortAscending ?? this.noteSortAscending,
      autoSaveNotes: autoSaveNotes ?? this.autoSaveNotes,
      autoSaveIntervalSeconds: autoSaveIntervalSeconds ?? this.autoSaveIntervalSeconds,
      defaultNoteCategory: defaultNoteCategory ?? this.defaultNoteCategory,
      enableSpellCheck: enableSpellCheck ?? this.enableSpellCheck,
      enableAutoCorrect: enableAutoCorrect ?? this.enableAutoCorrect,
      showLineNumbers: showLineNumbers ?? this.showLineNumbers,
      enableWordWrap: enableWordWrap ?? this.enableWordWrap,
      defaultExportFormat: defaultExportFormat ?? this.defaultExportFormat,
      todoSortBy: todoSortBy ?? this.todoSortBy,
      todoSortAscending: todoSortAscending ?? this.todoSortAscending,
      showCompletedTodos: showCompletedTodos ?? this.showCompletedTodos,
      enableTodoNotifications: enableTodoNotifications ?? this.enableTodoNotifications,
      reminderMinutesBefore: reminderMinutesBefore ?? this.reminderMinutesBefore,
      enableRecurringTodos: enableRecurringTodos ?? this.enableRecurringTodos,
      autoArchiveCompleted: autoArchiveCompleted ?? this.autoArchiveCompleted,
      autoArchiveDays: autoArchiveDays ?? this.autoArchiveDays,
      voiceMemoQuality: voiceMemoQuality ?? this.voiceMemoQuality,
      audioFormat: audioFormat ?? this.audioFormat,
      enableAutoTranscription: enableAutoTranscription ?? this.enableAutoTranscription,
      maxRecordingMinutes: maxRecordingMinutes ?? this.maxRecordingMinutes,
      enableNoiseReduction: enableNoiseReduction ?? this.enableNoiseReduction,
      enableAutoStop: enableAutoStop ?? this.enableAutoStop,
      autoStopSilenceSeconds: autoStopSilenceSeconds ?? this.autoStopSilenceSeconds,
      saveToCloud: saveToCloud ?? this.saveToCloud,
      enableCategories: enableCategories ?? this.enableCategories,
      enableTags: enableTags ?? this.enableTags,
      enableFavorites: enableFavorites ?? this.enableFavorites,
      enableArchive: enableArchive ?? this.enableArchive,
      enableTrash: enableTrash ?? this.enableTrash,
      trashRetentionDays: trashRetentionDays ?? this.trashRetentionDays,
      enableSearch: enableSearch ?? this.enableSearch,
      enableGlobalSearch: enableGlobalSearch ?? this.enableGlobalSearch,
      enableAutoBackup: enableAutoBackup ?? this.enableAutoBackup,
      backupFrequencyHours: backupFrequencyHours ?? this.backupFrequencyHours,
      backupToCloud: backupToCloud ?? this.backupToCloud,
      syncAcrossDevices: syncAcrossDevices ?? this.syncAcrossDevices,
      syncOnWifiOnly: syncOnWifiOnly ?? this.syncOnWifiOnly,
      maxBackupFiles: maxBackupFiles ?? this.maxBackupFiles,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
      enableEncryption: enableEncryption ?? this.enableEncryption,
      requirePinForAccess: requirePinForAccess ?? this.requirePinForAccess,
      pinHash: pinHash ?? this.pinHash,
      enableBiometricAuth: enableBiometricAuth ?? this.enableBiometricAuth,
      hideContentInRecents: hideContentInRecents ?? this.hideContentInRecents,
      enableScreenshotProtection: enableScreenshotProtection ?? this.enableScreenshotProtection,
      autoLockMinutes: autoLockMinutes ?? this.autoLockMinutes,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableDueDateReminders: enableDueDateReminders ?? this.enableDueDateReminders,
      enableDailyDigest: enableDailyDigest ?? this.enableDailyDigest,
      dailyDigestTime: dailyDigestTime ?? this.dailyDigestTime,
      enableQuietHours: enableQuietHours ?? this.enableQuietHours,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      enableDebugMode: enableDebugMode ?? this.enableDebugMode,
      enableBetaFeatures: enableBetaFeatures ?? this.enableBetaFeatures,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      enablePerformanceMonitoring: enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      maxUndoSteps: maxUndoSteps ?? this.maxUndoSteps,
      enableCollaboration: enableCollaboration ?? this.enableCollaboration,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'noteViewMode': noteViewMode.index,
      'showNotePreview': showNotePreview,
      'showWordCount': showWordCount,
      'showCharacterCount': showCharacterCount,
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'useSystemFont': useSystemFont,
      'lineHeight': lineHeight,
      'enableMarkdownPreview': enableMarkdownPreview,
      'enableSyntaxHighlighting': enableSyntaxHighlighting,
      'noteSortBy': noteSortBy.index,
      'noteSortAscending': noteSortAscending,
      'autoSaveNotes': autoSaveNotes,
      'autoSaveIntervalSeconds': autoSaveIntervalSeconds,
      'defaultNoteCategory': defaultNoteCategory,
      'enableSpellCheck': enableSpellCheck,
      'enableAutoCorrect': enableAutoCorrect,
      'showLineNumbers': showLineNumbers,
      'enableWordWrap': enableWordWrap,
      'defaultExportFormat': defaultExportFormat.index,
      'todoSortBy': todoSortBy.index,
      'todoSortAscending': todoSortAscending,
      'showCompletedTodos': showCompletedTodos,
      'enableTodoNotifications': enableTodoNotifications,
      'reminderMinutesBefore': reminderMinutesBefore,
      'enableRecurringTodos': enableRecurringTodos,
      'autoArchiveCompleted': autoArchiveCompleted,
      'autoArchiveDays': autoArchiveDays,
      'voiceMemoQuality': voiceMemoQuality.index,
      'audioFormat': audioFormat,
      'enableAutoTranscription': enableAutoTranscription,
      'maxRecordingMinutes': maxRecordingMinutes,
      'enableNoiseReduction': enableNoiseReduction,
      'enableAutoStop': enableAutoStop,
      'autoStopSilenceSeconds': autoStopSilenceSeconds,
      'saveToCloud': saveToCloud,
      'enableCategories': enableCategories,
      'enableTags': enableTags,
      'enableFavorites': enableFavorites,
      'enableArchive': enableArchive,
      'enableTrash': enableTrash,
      'trashRetentionDays': trashRetentionDays,
      'enableSearch': enableSearch,
      'enableGlobalSearch': enableGlobalSearch,
      'enableAutoBackup': enableAutoBackup,
      'backupFrequencyHours': backupFrequencyHours,
      'backupToCloud': backupToCloud,
      'syncAcrossDevices': syncAcrossDevices,
      'syncOnWifiOnly': syncOnWifiOnly,
      'maxBackupFiles': maxBackupFiles,
      'lastBackupDate': lastBackupDate,
      'enableEncryption': enableEncryption,
      'requirePinForAccess': requirePinForAccess,
      'pinHash': pinHash,
      'enableBiometricAuth': enableBiometricAuth,
      'hideContentInRecents': hideContentInRecents,
      'enableScreenshotProtection': enableScreenshotProtection,
      'autoLockMinutes': autoLockMinutes,
      'enableNotifications': enableNotifications,
      'enableDueDateReminders': enableDueDateReminders,
      'enableDailyDigest': enableDailyDigest,
      'dailyDigestTime': dailyDigestTime != null 
          ? '${dailyDigestTime!.hour}:${dailyDigestTime!.minute}' 
          : null,
      'enableQuietHours': enableQuietHours,
      'quietHoursStart': quietHoursStart != null 
          ? '${quietHoursStart!.hour}:${quietHoursStart!.minute}' 
          : null,
      'quietHoursEnd': quietHoursEnd != null 
          ? '${quietHoursEnd!.hour}:${quietHoursEnd!.minute}' 
          : null,
      'enableDebugMode': enableDebugMode,
      'enableBetaFeatures': enableBetaFeatures,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'enablePerformanceMonitoring': enablePerformanceMonitoring,
      'maxUndoSteps': maxUndoSteps,
      'enableCollaboration': enableCollaboration,
    };
  }

  /// Create from JSON
  factory MemoSettings.fromJson(Map<String, dynamic> json) {
    TimeOfDay? parseTimeOfDay(String? timeString) {
      if (timeString == null) return null;
      final parts = timeString.split(':');
      if (parts.length != 2) return null;
      final hour = int.tryParse(parts[0]);
      final minute = int.tryParse(parts[1]);
      if (hour == null || minute == null) return null;
      return TimeOfDay(hour: hour, minute: minute);
    }

    return MemoSettings(
      noteViewMode: NoteViewMode.values[json['noteViewMode'] ?? 0],
      showNotePreview: json['showNotePreview'] ?? true,
      showWordCount: json['showWordCount'] ?? true,
      showCharacterCount: json['showCharacterCount'] ?? false,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontFamily: json['fontFamily'] ?? 'System',
      useSystemFont: json['useSystemFont'] ?? true,
      lineHeight: (json['lineHeight'] as num?)?.toDouble() ?? 1.5,
      enableMarkdownPreview: json['enableMarkdownPreview'] ?? true,
      enableSyntaxHighlighting: json['enableSyntaxHighlighting'] ?? true,
      noteSortBy: NoteSortBy.values[json['noteSortBy'] ?? 1],
      noteSortAscending: json['noteSortAscending'] ?? false,
      autoSaveNotes: json['autoSaveNotes'] ?? true,
      autoSaveIntervalSeconds: json['autoSaveIntervalSeconds'] ?? 30,
      defaultNoteCategory: json['defaultNoteCategory'] ?? 'General',
      enableSpellCheck: json['enableSpellCheck'] ?? true,
      enableAutoCorrect: json['enableAutoCorrect'] ?? true,
      showLineNumbers: json['showLineNumbers'] ?? false,
      enableWordWrap: json['enableWordWrap'] ?? true,
      defaultExportFormat: ExportFormat.values[json['defaultExportFormat'] ?? 1],
      todoSortBy: TodoSortBy.values[json['todoSortBy'] ?? 0],
      todoSortAscending: json['todoSortAscending'] ?? true,
      showCompletedTodos: json['showCompletedTodos'] ?? false,
      enableTodoNotifications: json['enableTodoNotifications'] ?? true,
      reminderMinutesBefore: json['reminderMinutesBefore'] ?? 15,
      enableRecurringTodos: json['enableRecurringTodos'] ?? true,
      autoArchiveCompleted: json['autoArchiveCompleted'] ?? false,
      autoArchiveDays: json['autoArchiveDays'] ?? 30,
      voiceMemoQuality: VoiceMemoQuality.values[json['voiceMemoQuality'] ?? 1],
      audioFormat: json['audioFormat'] ?? 'aac',
      enableAutoTranscription: json['enableAutoTranscription'] ?? false,
      maxRecordingMinutes: json['maxRecordingMinutes'] ?? 60,
      enableNoiseReduction: json['enableNoiseReduction'] ?? true,
      enableAutoStop: json['enableAutoStop'] ?? false,
      autoStopSilenceSeconds: json['autoStopSilenceSeconds'] ?? 5,
      saveToCloud: json['saveToCloud'] ?? false,
      enableCategories: json['enableCategories'] ?? true,
      enableTags: json['enableTags'] ?? true,
      enableFavorites: json['enableFavorites'] ?? true,
      enableArchive: json['enableArchive'] ?? true,
      enableTrash: json['enableTrash'] ?? true,
      trashRetentionDays: json['trashRetentionDays'] ?? 30,
      enableSearch: json['enableSearch'] ?? true,
      enableGlobalSearch: json['enableGlobalSearch'] ?? true,
      enableAutoBackup: json['enableAutoBackup'] ?? true,
      backupFrequencyHours: json['backupFrequencyHours'] ?? 24,
      backupToCloud: json['backupToCloud'] ?? false,
      syncAcrossDevices: json['syncAcrossDevices'] ?? false,
      syncOnWifiOnly: json['syncOnWifiOnly'] ?? true,
      maxBackupFiles: json['maxBackupFiles'] ?? 10,
      lastBackupDate: json['lastBackupDate'] ?? '',
      enableEncryption: json['enableEncryption'] ?? false,
      requirePinForAccess: json['requirePinForAccess'] ?? false,
      pinHash: json['pinHash'] ?? '',
      enableBiometricAuth: json['enableBiometricAuth'] ?? false,
      hideContentInRecents: json['hideContentInRecents'] ?? false,
      enableScreenshotProtection: json['enableScreenshotProtection'] ?? false,
      autoLockMinutes: json['autoLockMinutes'] ?? 5,
      enableNotifications: json['enableNotifications'] ?? true,
      enableDueDateReminders: json['enableDueDateReminders'] ?? true,
      enableDailyDigest: json['enableDailyDigest'] ?? false,
      dailyDigestTime: parseTimeOfDay(json['dailyDigestTime']),
      enableQuietHours: json['enableQuietHours'] ?? false,
      quietHoursStart: parseTimeOfDay(json['quietHoursStart']),
      quietHoursEnd: parseTimeOfDay(json['quietHoursEnd']),
      enableDebugMode: json['enableDebugMode'] ?? false,
      enableBetaFeatures: json['enableBetaFeatures'] ?? false,
      enableAnalytics: json['enableAnalytics'] ?? true,
      enableCrashReporting: json['enableCrashReporting'] ?? true,
      enablePerformanceMonitoring: json['enablePerformanceMonitoring'] ?? false,
      maxUndoSteps: json['maxUndoSteps'] ?? 50,
      enableCollaboration: json['enableCollaboration'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MemoSettings &&
        other.noteViewMode == noteViewMode &&
        other.showNotePreview == showNotePreview &&
        other.showWordCount == showWordCount &&
        other.showCharacterCount == showCharacterCount &&
        other.fontSize == fontSize &&
        other.fontFamily == fontFamily &&
        other.useSystemFont == useSystemFont &&
        other.lineHeight == lineHeight &&
        other.enableMarkdownPreview == enableMarkdownPreview &&
        other.enableSyntaxHighlighting == enableSyntaxHighlighting;
    // Note: Truncated for brevity - in real implementation, compare all fields
  }

  @override
  int get hashCode => Object.hashAll([
        noteViewMode,
        showNotePreview,
        showWordCount,
        showCharacterCount,
        fontSize,
        fontFamily,
        useSystemFont,
        lineHeight,
        enableMarkdownPreview,
        enableSyntaxHighlighting,
        // Note: Truncated for brevity - in real implementation, hash all fields
      ]);

  @override
  String toString() => 'MemoSettings(noteViewMode: $noteViewMode, fontSize: $fontSize)';
}
