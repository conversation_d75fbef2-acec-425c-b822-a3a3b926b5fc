import 'dart:convert';

/// Spreadsheet cell data model for the Excel-like formula engine
class SpreadsheetCell {
  final String address; // A1, B2, etc.
  final dynamic value;
  final String? formula;
  final CellType type;
  final CellStyle style;
  final bool isSelected;
  final bool isEditing;
  final bool isInRange;
  final List<String> dependencies;
  final List<String> dependents;
  final DateTime? lastModified;
  final String? validationError;

  const SpreadsheetCell({
    required this.address,
    this.value,
    this.formula,
    required this.type,
    this.style = const CellStyle(),
    this.isSelected = false,
    this.isEditing = false,
    this.isInRange = false,
    this.dependencies = const [],
    this.dependents = const [],
    this.lastModified,
    this.validationError,
  });

  /// Create a copy with updated values
  SpreadsheetCell copyWith({
    String? address,
    dynamic value,
    String? formula,
    CellType? type,
    CellStyle? style,
    bool? isSelected,
    bool? isEditing,
    bool? isInRange,
    List<String>? dependencies,
    List<String>? dependents,
    DateTime? lastModified,
    String? validationError,
  }) {
    return SpreadsheetCell(
      address: address ?? this.address,
      value: value ?? this.value,
      formula: formula ?? this.formula,
      type: type ?? this.type,
      style: style ?? this.style,
      isSelected: isSelected ?? this.isSelected,
      isEditing: isEditing ?? this.isEditing,
      isInRange: isInRange ?? this.isInRange,
      dependencies: dependencies ?? this.dependencies,
      dependents: dependents ?? this.dependents,
      lastModified: lastModified ?? this.lastModified,
      validationError: validationError ?? this.validationError,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'value': value,
      'formula': formula,
      'type': type.name,
      'style': style.toJson(),
    };
  }

  /// Create from JSON
  factory SpreadsheetCell.fromJson(Map<String, dynamic> json) {
    return SpreadsheetCell(
      address: json['address'],
      value: json['value'],
      formula: json['formula'],
      type: CellType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CellType.text,
      ),
      style: CellStyle.fromJson(json['style'] ?? {}),
    );
  }

  /// Check if cell has a formula
  bool get hasFormula => formula != null && formula!.startsWith('=');

  /// Get display value
  String get displayValue {
    if (type == CellType.error) return value?.toString() ?? '#ERROR!';
    if (value == null) return '';
    
    // Format numbers appropriately
    if (type == CellType.number && value is num) {
      if (value == value.toInt()) {
        return value.toInt().toString();
      } else {
        return value.toStringAsFixed(2);
      }
    }
    
    return value.toString();
  }

  /// Check if cell is empty
  bool get isEmpty => value == null || value.toString().trim().isEmpty;

  /// Get cell column (A, B, C, etc.)
  String get column {
    final match = RegExp(r'^([A-Z]+)').firstMatch(address);
    return match?.group(1) ?? 'A';
  }

  /// Get cell row (1, 2, 3, etc.)
  int get row {
    final match = RegExp(r'([0-9]+)$').firstMatch(address);
    return int.tryParse(match?.group(1) ?? '1') ?? 1;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SpreadsheetCell &&
        other.address == address &&
        other.value == value &&
        other.formula == formula &&
        other.type == type;
  }

  @override
  int get hashCode {
    return Object.hash(address, value, formula, type);
  }

  @override
  String toString() {
    return 'SpreadsheetCell(address: $address, value: $value, formula: $formula, type: $type)';
  }
}

/// Cell data types
enum CellType { 
  text, 
  number, 
  boolean, 
  formula, 
  error 
}

/// Cell styling information
class CellStyle {
  final bool bold;
  final bool italic;
  final String? backgroundColor;
  final String? textColor;
  final TextAlign textAlign;
  final String? fontSize;

  const CellStyle({
    this.bold = false,
    this.italic = false,
    this.backgroundColor,
    this.textColor,
    this.textAlign = TextAlign.left,
    this.fontSize,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'bold': bold,
      'italic': italic,
      'backgroundColor': backgroundColor,
      'textColor': textColor,
      'textAlign': textAlign.name,
      'fontSize': fontSize,
    };
  }

  /// Create from JSON
  factory CellStyle.fromJson(Map<String, dynamic> json) {
    return CellStyle(
      bold: json['bold'] ?? false,
      italic: json['italic'] ?? false,
      backgroundColor: json['backgroundColor'],
      textColor: json['textColor'],
      textAlign: TextAlign.values.firstWhere(
        (e) => e.name == json['textAlign'],
        orElse: () => TextAlign.left,
      ),
      fontSize: json['fontSize'],
    );
  }

  /// Create a copy with updated values
  CellStyle copyWith({
    bool? bold,
    bool? italic,
    String? backgroundColor,
    String? textColor,
    TextAlign? textAlign,
    String? fontSize,
  }) {
    return CellStyle(
      bold: bold ?? this.bold,
      italic: italic ?? this.italic,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      textAlign: textAlign ?? this.textAlign,
      fontSize: fontSize ?? this.fontSize,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CellStyle &&
        other.bold == bold &&
        other.italic == italic &&
        other.backgroundColor == backgroundColor &&
        other.textColor == textColor &&
        other.textAlign == textAlign &&
        other.fontSize == fontSize;
  }

  @override
  int get hashCode {
    return Object.hash(bold, italic, backgroundColor, textColor, textAlign, fontSize);
  }
}

/// Text alignment options
enum TextAlign { 
  left, 
  center, 
  right 
}

/// Utility functions for spreadsheet operations
class SpreadsheetUtils {
  /// Convert column number to letter (1 -> A, 2 -> B, etc.)
  static String columnNumberToLetter(int columnNumber) {
    String result = '';
    while (columnNumber > 0) {
      columnNumber--;
      result = String.fromCharCode(65 + (columnNumber % 26)) + result;
      columnNumber ~/= 26;
    }
    return result;
  }

  /// Convert column letter to number (A -> 1, B -> 2, etc.)
  static int columnLetterToNumber(String columnLetter) {
    int result = 0;
    for (int i = 0; i < columnLetter.length; i++) {
      result = result * 26 + (columnLetter.codeUnitAt(i) - 64);
    }
    return result;
  }

  /// Generate cell address from row and column
  static String getCellAddress(int row, int column) {
    return '${columnNumberToLetter(column)}$row';
  }

  /// Parse cell address to get row and column
  static Map<String, int> parseCellAddress(String address) {
    final match = RegExp(r'^([A-Z]+)([0-9]+)$').firstMatch(address);
    if (match == null) {
      return {'row': 1, 'column': 1};
    }
    
    final columnLetter = match.group(1)!;
    final rowNumber = int.parse(match.group(2)!);
    
    return {
      'row': rowNumber,
      'column': columnLetterToNumber(columnLetter),
    };
  }
}
