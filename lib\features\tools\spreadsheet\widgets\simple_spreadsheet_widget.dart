import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/simple_spreadsheet_provider.dart';
import '../core/models/simple_worksheet.dart';

/// Simple spreadsheet widget
class SimpleSpreadsheetWidget extends ConsumerStatefulWidget {
  final Function(String worksheetId, String address, dynamic value)? onCellValueChanged;
  final Function(String worksheetId, String address)? onCellSelected;
  final VoidCallback? onImportExcel;
  final VoidCallback? onExportExcel;
  final VoidCallback? onLoadSample;

  const SimpleSpreadsheetWidget({
    super.key,
    this.onCellValueChanged,
    this.onCellSelected,
    this.onImportExcel,
    this.onExportExcel,
    this.onLoadSample,
  });

  @override
  ConsumerState<SimpleSpreadsheetWidget> createState() => _SimpleSpreadsheetWidgetState();
}

class _SimpleSpreadsheetWidgetState extends ConsumerState<SimpleSpreadsheetWidget> {
  String? _selectedCell;
  final TextEditingController _formulaController = TextEditingController();
  final int _visibleRows = 20;
  final int _visibleColumns = 10;

  @override
  void initState() {
    super.initState();
    
    // Initialize with a default worksheet if none exists
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final state = ref.read(simpleSpreadsheetProvider);
      if (state.worksheets.isEmpty) {
        ref.read(simpleSpreadsheetProvider.notifier).addWorksheet('Sheet1');
        _loadSampleData();
      }
    });
  }

  @override
  void dispose() {
    _formulaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final spreadsheetState = ref.watch(simpleSpreadsheetProvider);
    
    if (spreadsheetState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (spreadsheetState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Theme.of(context).colorScheme.error),
            const SizedBox(height: 16),
            Text('Error: ${spreadsheetState.error}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.read(simpleSpreadsheetProvider.notifier).clear(),
              child: const Text('Reset'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Toolbar
        _buildToolbar(),
        
        // Formula bar
        _buildFormulaBar(),
        
        // Spreadsheet grid
        Expanded(child: _buildGrid()),
      ],
    );
  }

  /// Build toolbar
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          ElevatedButton.icon(
            onPressed: widget.onLoadSample ?? _loadSampleData,
            icon: const Icon(Icons.data_object),
            label: const Text('Load Sample'),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: widget.onImportExcel,
            icon: const Icon(Icons.upload_file),
            label: const Text('Import CSV'),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: widget.onExportExcel,
            icon: const Icon(Icons.download),
            label: const Text('Export CSV'),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: _clearAll,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.errorContainer,
            ),
          ),
        ],
      ),
    );
  }

  /// Build formula bar
  Widget _buildFormulaBar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Cell reference
          Container(
            width: 80,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              _selectedCell ?? 'A1',
              style: const TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 8),
          
          // Formula input
          Expanded(
            child: TextField(
              controller: _formulaController,
              decoration: const InputDecoration(
                hintText: 'Enter value or formula (e.g., =A1+B1)',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              onSubmitted: _onFormulaSubmitted,
            ),
          ),
          const SizedBox(width: 8),
          
          // Apply button
          ElevatedButton(
            onPressed: () => _onFormulaSubmitted(_formulaController.text),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  /// Build spreadsheet grid
  Widget _buildGrid() {
    final spreadsheetState = ref.watch(simpleSpreadsheetProvider);
    final worksheet = spreadsheetState.worksheets[spreadsheetState.activeWorksheetId];
    
    if (worksheet == null) {
      return const Center(child: Text('No worksheet available'));
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          // Column headers
          _buildColumnHeaders(),
          
          // Data rows
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: List.generate(_visibleRows, (rowIndex) {
                  return _buildDataRow(rowIndex + 1, worksheet);
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build column headers
  Widget _buildColumnHeaders() {
    return SizedBox(
      height: 32,
      child: Row(
        children: [
          // Row header corner
          Container(
            width: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
            ),
          ),
          
          // Column headers
          ...List.generate(_visibleColumns, (colIndex) {
            final columnName = String.fromCharCode(65 + colIndex); // A, B, C, ...
            return Container(
              width: 100,
              height: 32,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
              ),
              child: Center(
                child: Text(
                  columnName,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Build data row
  Widget _buildDataRow(int rowNumber, SimpleWorksheet worksheet) {
    return SizedBox(
      height: 32,
      child: Row(
        children: [
          // Row header
          Container(
            width: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
            ),
            child: Center(
              child: Text(
                rowNumber.toString(),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          
          // Data cells
          ...List.generate(_visibleColumns, (colIndex) {
            final columnName = String.fromCharCode(65 + colIndex);
            final cellAddress = '$columnName$rowNumber';
            final cell = worksheet.getCell(cellAddress);
            final isSelected = _selectedCell == cellAddress;
            
            return GestureDetector(
              onTap: () => _selectCell(cellAddress),
              child: Container(
                width: 100,
                height: 32,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? Theme.of(context).colorScheme.primaryContainer
                      : Theme.of(context).colorScheme.surface,
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    cell?.displayValue ?? '',
                    style: Theme.of(context).textTheme.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Select cell
  void _selectCell(String address) {
    setState(() {
      _selectedCell = address;
    });
    
    final cellValue = _getSelectedCellValue();
    _formulaController.text = cellValue;
    
    widget.onCellSelected?.call(ref.read(simpleSpreadsheetProvider).activeWorksheetId, address);
  }

  /// Get selected cell value
  String _getSelectedCellValue() {
    if (_selectedCell == null) return '';
    
    final spreadsheetState = ref.read(simpleSpreadsheetProvider);
    final worksheet = spreadsheetState.worksheets[spreadsheetState.activeWorksheetId];
    final cell = worksheet?.getCell(_selectedCell!);
    
    return cell?.formula ?? cell?.value?.toString() ?? '';
  }

  /// Handle formula submission
  void _onFormulaSubmitted(String value) {
    if (_selectedCell == null) return;
    
    final worksheetId = ref.read(simpleSpreadsheetProvider).activeWorksheetId;
    ref.read(simpleSpreadsheetProvider.notifier).updateCell(worksheetId, _selectedCell!, value);
    
    widget.onCellValueChanged?.call(worksheetId, _selectedCell!, value);
  }

  /// Load sample data
  void _loadSampleData() {
    final notifier = ref.read(simpleSpreadsheetProvider.notifier);
    final worksheetId = ref.read(simpleSpreadsheetProvider).activeWorksheetId;
    
    // Sample data
    notifier.updateCell(worksheetId, 'A1', 'Product');
    notifier.updateCell(worksheetId, 'B1', 'Price');
    notifier.updateCell(worksheetId, 'C1', 'Quantity');
    notifier.updateCell(worksheetId, 'D1', 'Total');
    
    notifier.updateCell(worksheetId, 'A2', 'Widget A');
    notifier.updateCell(worksheetId, 'B2', 10.50);
    notifier.updateCell(worksheetId, 'C2', 5);
    notifier.updateCell(worksheetId, 'D2', '=B2*C2');
    
    notifier.updateCell(worksheetId, 'A3', 'Widget B');
    notifier.updateCell(worksheetId, 'B3', 15.75);
    notifier.updateCell(worksheetId, 'C3', 3);
    notifier.updateCell(worksheetId, 'D3', '=B3*C3');
  }

  /// Clear all data
  void _clearAll() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text('Are you sure you want to clear all data?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(simpleSpreadsheetProvider.notifier).clear();
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
