import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr_routine.dart';

// Dhikr routines provider
final dhikrRoutinesProvider = StateNotifierProvider<DhikrRoutinesNotifier, List<DhikrRoutine>>((ref) {
  return DhikrRoutinesNotifier();
});

// Active routine provider
final activeDhikrRoutineProvider = StateProvider<DhikrRoutine?>((ref) => null);

// Current session provider
final dhikrSessionProvider = StateNotifierProvider<DhikrSessionNotifier, DhikrSession?>((ref) {
  return DhikrSessionNotifier();
});

// Statistics provider
final dhikrRoutineStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final routines = ref.watch(dhikrRoutinesProvider);
  
  final totalRoutines = routines.length;
  final activeRoutines = routines.where((r) => r.isActive).length;
  final completedSessions = routines.fold(0, (sum, r) => sum + r.totalSessions);
  final totalTimeSpent = routines.fold(Duration.zero, (sum, r) => sum + r.totalTimeSpent);
  final totalDhikr = routines.fold(0, (sum, r) => sum + r.totalCurrentCount);
  
  // Calculate streak
  final today = DateTime.now();
  int currentStreak = 0;
  for (int i = 0; i < 365; i++) {
    final date = today.subtract(Duration(days: i));
    final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    
    bool hasCompletionToday = false;
    for (final routine in routines) {
      if (routine.dailyCompletions.containsKey(dateKey) && routine.dailyCompletions[dateKey]! > 0) {
        hasCompletionToday = true;
        break;
      }
    }
    
    if (hasCompletionToday) {
      currentStreak++;
    } else {
      break;
    }
  }
  
  return {
    'totalRoutines': totalRoutines,
    'activeRoutines': activeRoutines,
    'completedSessions': completedSessions,
    'totalTimeSpent': totalTimeSpent.inMinutes,
    'totalDhikr': totalDhikr,
    'currentStreak': currentStreak,
    'averageSessionTime': completedSessions > 0 ? (totalTimeSpent.inMinutes / completedSessions).round() : 0,
  };
});

// Daily completions provider
final dailyCompletionsProvider = Provider<Map<String, int>>((ref) {
  final routines = ref.watch(dhikrRoutinesProvider);
  final combined = <String, int>{};
  
  for (final routine in routines) {
    for (final entry in routine.dailyCompletions.entries) {
      combined[entry.key] = (combined[entry.key] ?? 0) + entry.value;
    }
  }
  
  return combined;
});

class DhikrRoutinesNotifier extends StateNotifier<List<DhikrRoutine>> {
  DhikrRoutinesNotifier() : super([]) {
    _loadRoutines();
  }

  void _loadRoutines() {
    // Load from database or create default routines
    final defaultRoutines = [
      DhikrRoutine(
        id: 1,
        name: 'Morning Adhkar',
        description: 'Traditional morning remembrance',
        steps: DhikrTemplates.morningAdhkar,
        userId: 'current_user',
      ),
      DhikrRoutine(
        id: 2,
        name: 'Evening Adhkar',
        description: 'Traditional evening remembrance',
        steps: DhikrTemplates.eveningAdhkar,
        userId: 'current_user',
      ),
      DhikrRoutine(
        id: 3,
        name: 'Salawat on Prophet',
        description: 'Sending prayers upon the Prophet',
        steps: DhikrTemplates.salawatOnProphet,
        userId: 'current_user',
      ),
    ];
    
    state = defaultRoutines;
  }

  Future<void> addRoutine(DhikrRoutine routine) async {
    try {
      // In a real implementation, save to database
      state = [...state, routine];
    } catch (e) {
      debugPrint('Error adding routine: $e');
      rethrow;
    }
  }

  Future<void> updateRoutine(DhikrRoutine routine) async {
    try {
      // In a real implementation, update in database
      state = state.map((r) => r.id == routine.id ? routine : r).toList();
    } catch (e) {
      debugPrint('Error updating routine: $e');
      rethrow;
    }
  }

  Future<void> deleteRoutine(int routineId) async {
    try {
      // In a real implementation, delete from database
      state = state.where((routine) => routine.id != routineId).toList();
    } catch (e) {
      debugPrint('Error deleting routine: $e');
      rethrow;
    }
  }

  Future<void> duplicateRoutine(int routineId) async {
    final original = state.firstWhere((r) => r.id == routineId);
    final duplicate = DhikrRoutine(
      id: DateTime.now().millisecondsSinceEpoch,
      name: '${original.name} (Copy)',
      description: original.description,
      steps: original.steps.map((step) => step.copyWith()).toList(),
      userId: original.userId,
      autoAdvance: original.autoAdvance,
      loopRoutine: original.loopRoutine,
      sessionDuration: original.sessionDuration,
      reminderTime: original.reminderTime,
      reminderDays: List.from(original.reminderDays),
    );
    
    await addRoutine(duplicate);
  }

  Future<void> toggleRoutineActive(int routineId) async {
    final routine = state.firstWhere((r) => r.id == routineId);
    routine.isActive = !routine.isActive;
    routine.updatedAt = DateTime.now();
    await updateRoutine(routine);
  }

  Future<void> resetRoutine(int routineId) async {
    final routine = state.firstWhere((r) => r.id == routineId);
    routine.resetRoutine();
    await updateRoutine(routine);
  }

  Future<void> incrementStep(int routineId, int stepId) async {
    final routine = state.firstWhere((r) => r.id == routineId);
    final step = routine.steps.firstWhere((s) => s.id == stepId);
    step.increment();
    routine.updatedAt = DateTime.now();
    await updateRoutine(routine);
  }

  Future<void> decrementStep(int routineId, int stepId) async {
    final routine = state.firstWhere((r) => r.id == routineId);
    final step = routine.steps.firstWhere((s) => s.id == stepId);
    step.decrement();
    routine.updatedAt = DateTime.now();
    await updateRoutine(routine);
  }

  Future<void> completeSession(int routineId, Duration sessionDuration) async {
    final routine = state.firstWhere((r) => r.id == routineId);
    routine.completeSession();
    routine.addTimeSpent(sessionDuration);
    await updateRoutine(routine);
  }

  List<DhikrRoutine> getActiveRoutines() {
    return state.where((routine) => routine.isActive).toList();
  }

  List<DhikrRoutine> getRoutinesByCompletion(bool completed) {
    return state.where((routine) => routine.isCompleted == completed).toList();
  }

  DhikrRoutine? getRoutineById(int id) {
    try {
      return state.firstWhere((routine) => routine.id == id);
    } catch (e) {
      return null;
    }
  }
}

class DhikrSession {
  final DhikrRoutine routine;
  final DateTime startTime;
  DateTime? endTime;
  bool isActive;
  Duration get duration => (endTime ?? DateTime.now()).difference(startTime);

  DhikrSession({
    required this.routine,
    required this.startTime,
    this.endTime,
    this.isActive = true,
  });

  void end() {
    endTime = DateTime.now();
    isActive = false;
  }
}

class DhikrSessionNotifier extends StateNotifier<DhikrSession?> {
  DhikrSessionNotifier() : super(null);

  void startSession(DhikrRoutine routine) {
    state = DhikrSession(
      routine: routine,
      startTime: DateTime.now(),
    );
  }

  void endSession() {
    if (state != null) {
      state!.end();
      state = null;
    }
  }

  void pauseSession() {
    if (state != null) {
      state!.isActive = false;
    }
  }

  void resumeSession() {
    if (state != null) {
      state!.isActive = true;
    }
  }

  bool get hasActiveSession => state != null && state!.isActive;
  Duration get sessionDuration => state?.duration ?? Duration.zero;
}

// Achievement system
enum AchievementType {
  firstSession,
  streak7Days,
  streak30Days,
  streak100Days,
  complete100Sessions,
  complete1000Dhikr,
  complete10000Dhikr,
  createCustomRoutine,
  completeAllSteps,
}

class Achievement {
  final AchievementType type;
  final String title;
  final String description;
  final String icon;
  final DateTime unlockedAt;

  Achievement({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    required this.unlockedAt,
  });
}

final achievementsProvider = Provider<List<Achievement>>((ref) {
  final stats = ref.watch(dhikrRoutineStatsProvider);
  final achievements = <Achievement>[];
  
  // Check for achievements based on stats
  if (stats['completedSessions'] >= 1) {
    achievements.add(Achievement(
      type: AchievementType.firstSession,
      title: 'First Steps',
      description: 'Complete your first dhikr session',
      icon: '🌟',
      unlockedAt: DateTime.now(),
    ));
  }
  
  if (stats['currentStreak'] >= 7) {
    achievements.add(Achievement(
      type: AchievementType.streak7Days,
      title: 'Week Warrior',
      description: 'Maintain a 7-day streak',
      icon: '🔥',
      unlockedAt: DateTime.now(),
    ));
  }
  
  if (stats['currentStreak'] >= 30) {
    achievements.add(Achievement(
      type: AchievementType.streak30Days,
      title: 'Monthly Master',
      description: 'Maintain a 30-day streak',
      icon: '💎',
      unlockedAt: DateTime.now(),
    ));
  }
  
  if (stats['completedSessions'] >= 100) {
    achievements.add(Achievement(
      type: AchievementType.complete100Sessions,
      title: 'Century Club',
      description: 'Complete 100 dhikr sessions',
      icon: '🏆',
      unlockedAt: DateTime.now(),
    ));
  }
  
  if (stats['totalDhikr'] >= 1000) {
    achievements.add(Achievement(
      type: AchievementType.complete1000Dhikr,
      title: 'Thousand Remembrances',
      description: 'Complete 1,000 dhikr',
      icon: '🌙',
      unlockedAt: DateTime.now(),
    ));
  }
  
  return achievements;
});
