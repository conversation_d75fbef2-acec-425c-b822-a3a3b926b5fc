import 'package:flutter/material.dart';
import '../../../shared/widgets/standardized_components.dart';

/// Financial icon data
class FinancialIcon {
  final IconData icon;
  final String name;
  final String category;

  const FinancialIcon({
    required this.icon,
    required this.name,
    required this.category,
  });
}

/// Account icon picker widget
class AccountIconPicker extends StatefulWidget {
  final IconData selectedIcon;
  final Function(IconData icon) onIconSelected;
  final Color? selectedColor;

  const AccountIconPicker({
    super.key,
    required this.selectedIcon,
    required this.onIconSelected,
    this.selectedColor,
  });

  @override
  State<AccountIconPicker> createState() => _AccountIconPickerState();
}

class _AccountIconPickerState extends State<AccountIconPicker> {
  String _selectedCategory = 'All';
  
  static const List<FinancialIcon> _financialIcons = [
    // Bank & Account Icons
    FinancialIcon(icon: Icons.account_balance, name: 'Bank', category: 'Bank'),
    FinancialIcon(icon: Icons.account_balance_wallet, name: 'Wallet', category: 'Bank'),
    FinancialIcon(icon: Icons.savings, name: 'Savings', category: 'Bank'),
    FinancialIcon(icon: Icons.credit_card, name: 'Credit Card', category: 'Bank'),
    FinancialIcon(icon: Icons.payments, name: 'Payments', category: 'Bank'),
    FinancialIcon(icon: Icons.monetization_on, name: 'Money', category: 'Bank'),
    FinancialIcon(icon: Icons.account_balance_wallet_outlined, name: 'Wallet Outline', category: 'Bank'),
    FinancialIcon(icon: Icons.credit_card_outlined, name: 'Credit Card Outline', category: 'Bank'),
    
    // Cash & Money Icons
    FinancialIcon(icon: Icons.attach_money, name: 'Dollar', category: 'Cash'),
    FinancialIcon(icon: Icons.money, name: 'Money', category: 'Cash'),
    FinancialIcon(icon: Icons.euro, name: 'Euro', category: 'Cash'),
    FinancialIcon(icon: Icons.currency_pound, name: 'Pound', category: 'Cash'),
    FinancialIcon(icon: Icons.currency_yen, name: 'Yen', category: 'Cash'),
    FinancialIcon(icon: Icons.currency_exchange, name: 'Exchange', category: 'Cash'),
    FinancialIcon(icon: Icons.local_atm, name: 'ATM', category: 'Cash'),
    FinancialIcon(icon: Icons.point_of_sale, name: 'POS', category: 'Cash'),
    
    // Investment Icons
    FinancialIcon(icon: Icons.trending_up, name: 'Trending Up', category: 'Investment'),
    FinancialIcon(icon: Icons.trending_down, name: 'Trending Down', category: 'Investment'),
    FinancialIcon(icon: Icons.show_chart, name: 'Chart', category: 'Investment'),
    FinancialIcon(icon: Icons.pie_chart, name: 'Pie Chart', category: 'Investment'),
    FinancialIcon(icon: Icons.bar_chart, name: 'Bar Chart', category: 'Investment'),
    FinancialIcon(icon: Icons.analytics, name: 'Analytics', category: 'Investment'),
    FinancialIcon(icon: Icons.assessment, name: 'Assessment', category: 'Investment'),
    FinancialIcon(icon: Icons.business, name: 'Business', category: 'Investment'),
    
    // Digital & Online Icons
    FinancialIcon(icon: Icons.phone_android, name: 'Mobile Banking', category: 'Digital'),
    FinancialIcon(icon: Icons.computer, name: 'Online Banking', category: 'Digital'),
    FinancialIcon(icon: Icons.qr_code, name: 'QR Payment', category: 'Digital'),
    FinancialIcon(icon: Icons.nfc, name: 'NFC Payment', category: 'Digital'),
    FinancialIcon(icon: Icons.contactless, name: 'Contactless', category: 'Digital'),
    FinancialIcon(icon: Icons.tap_and_play, name: 'Tap to Pay', category: 'Digital'),
    FinancialIcon(icon: Icons.security, name: 'Secure', category: 'Digital'),
    FinancialIcon(icon: Icons.verified, name: 'Verified', category: 'Digital'),
    
    // Loan & Debt Icons
    FinancialIcon(icon: Icons.home, name: 'Mortgage', category: 'Loan'),
    FinancialIcon(icon: Icons.directions_car, name: 'Car Loan', category: 'Loan'),
    FinancialIcon(icon: Icons.school, name: 'Student Loan', category: 'Loan'),
    FinancialIcon(icon: Icons.handshake, name: 'Personal Loan', category: 'Loan'),
    FinancialIcon(icon: Icons.gavel, name: 'Legal', category: 'Loan'),
    FinancialIcon(icon: Icons.warning, name: 'Debt Warning', category: 'Loan'),
    
    // General Icons
    FinancialIcon(icon: Icons.star, name: 'Star', category: 'General'),
    FinancialIcon(icon: Icons.favorite, name: 'Favorite', category: 'General'),
    FinancialIcon(icon: Icons.bookmark, name: 'Bookmark', category: 'General'),
    FinancialIcon(icon: Icons.flag, name: 'Flag', category: 'General'),
    FinancialIcon(icon: Icons.label, name: 'Label', category: 'General'),
    FinancialIcon(icon: Icons.folder, name: 'Folder', category: 'General'),
    FinancialIcon(icon: Icons.archive, name: 'Archive', category: 'General'),
    FinancialIcon(icon: Icons.inbox, name: 'Inbox', category: 'General'),
  ];

  List<String> get _categories {
    final categories = _financialIcons.map((icon) => icon.category).toSet().toList();
    categories.sort();
    return ['All', ...categories];
  }

  List<FinancialIcon> get _filteredIcons {
    if (_selectedCategory == 'All') {
      return _financialIcons;
    }
    return _financialIcons.where((icon) => icon.category == _selectedCategory).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        height: 600,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.palette,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Choose Account Icon',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Category filter
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = category == _selectedCategory;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                      selectedColor: Theme.of(context).colorScheme.primaryContainer,
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Current selection
            if (widget.selectedIcon != null) ...[
              Text(
                'Current Selection:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (widget.selectedColor ?? Theme.of(context).colorScheme.primary)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.selectedIcon,
                      color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _getIconName(widget.selectedIcon),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Icon grid
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 1,
                ),
                itemCount: _filteredIcons.length,
                itemBuilder: (context, index) {
                  final iconData = _filteredIcons[index];
                  final isSelected = iconData.icon == widget.selectedIcon;
                  
                  return Tooltip(
                    message: iconData.name,
                    child: InkWell(
                      onTap: () {
                        widget.onIconSelected(iconData.icon);
                        Navigator.of(context).pop();
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (widget.selectedColor ?? Theme.of(context).colorScheme.primary)
                                  .withValues(alpha: 0.2)
                              : Theme.of(context).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(12),
                          border: isSelected
                              ? Border.all(
                                  color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                                  width: 2,
                                )
                              : null,
                        ),
                        child: Icon(
                          iconData.icon,
                          color: isSelected
                              ? (widget.selectedColor ?? Theme.of(context).colorScheme.primary)
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          size: 24,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Done'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getIconName(IconData icon) {
    final iconData = _financialIcons.firstWhere(
      (item) => item.icon == icon,
      orElse: () => const FinancialIcon(icon: Icons.help, name: 'Unknown', category: 'General'),
    );
    return iconData.name;
  }
}
