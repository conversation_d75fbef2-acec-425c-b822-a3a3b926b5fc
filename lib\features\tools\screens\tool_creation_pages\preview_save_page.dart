import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/tool_creation_provider.dart';

/// Page D - Preview & Save: Test tool functionality and save to library
class PreviewSavePage extends ConsumerStatefulWidget {
  const PreviewSavePage({super.key});

  @override
  ConsumerState<PreviewSavePage> createState() => _PreviewSavePageState();
}

class _PreviewSavePageState extends ConsumerState<PreviewSavePage> {
  bool _isPreviewMode = true;
  final Map<String, dynamic> _testValues = {};

  @override
  Widget build(BuildContext context) {
    final creationState = ref.watch(toolCreationProvider);
    
    return Row(
      children: [
        // Preview Panel
        Expanded(
          flex: 2,
          child: Column(
            children: [
              // Preview Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.preview,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Tool Preview',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    SegmentedButton<bool>(
                      segments: const [
                        ButtonSegment(
                          value: true,
                          label: Text('Preview'),
                          icon: Icon(Icons.preview),
                        ),
                        ButtonSegment(
                          value: false,
                          label: Text('Test'),
                          icon: Icon(Icons.play_arrow),
                        ),
                      ],
                      selected: {_isPreviewMode},
                      onSelectionChanged: (selection) {
                        setState(() {
                          _isPreviewMode = selection.first;
                        });
                      },
                    ),
                  ],
                ),
              ),
              
              // Preview Content
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: _buildPreviewContent(creationState),
                ),
              ),
            ],
          ),
        ),
        
        // Save Panel
        Container(
          width: 350,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              left: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Save Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.save,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Save Tool',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Save Content
              Expanded(
                child: _buildSavePanel(creationState),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewContent(ToolCreationState creationState) {
    if (creationState.uiComponents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No UI components to preview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Go back to the UI Builder to add components',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tool Title
          Text(
            creationState.toolName.isNotEmpty ? creationState.toolName : 'Untitled Tool',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          if (creationState.toolDescription.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              creationState.toolDescription,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
          const SizedBox(height: 24),
          
          // Rendered Components
          ...creationState.uiComponents.map((component) => _buildPreviewComponent(component)),
        ],
      ),
    );
  }

  Widget _buildPreviewComponent(Map<String, dynamic> component) {
    final componentId = component['id'] as String;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: _isPreviewMode 
          ? _buildStaticComponent(component)
          : _buildInteractiveComponent(component, componentId),
    );
  }

  Widget _buildStaticComponent(Map<String, dynamic> component) {
    switch (component['type']) {
      case 'input':
      case 'number_input':
        return TextField(
          decoration: InputDecoration(
            labelText: component['label'] ?? 'Input',
            border: const OutlineInputBorder(),
            enabled: false,
          ),
        );
      case 'output':
      case 'number_display':
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                component['label'] ?? 'Output',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '0',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      case 'button':
      case 'calc_button':
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: null,
            child: Text(component['label'] ?? 'Button'),
          ),
        );
      default:
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text('${component['type']} Component'),
        );
    }
  }

  Widget _buildInteractiveComponent(Map<String, dynamic> component, String componentId) {
    switch (component['type']) {
      case 'input':
      case 'number_input':
        return TextField(
          decoration: InputDecoration(
            labelText: component['label'] ?? 'Input',
            border: const OutlineInputBorder(),
          ),
          keyboardType: component['type'] == 'number_input' 
              ? TextInputType.number 
              : TextInputType.text,
          onChanged: (value) {
            setState(() {
              _testValues[componentId] = value;
            });
            _updateCalculations();
          },
        );
      case 'output':
      case 'number_display':
        final cellRef = component['cellRef'] as String?;
        final value = _getCalculatedValue(cellRef);
        
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                component['label'] ?? 'Output',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value.toString(),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      case 'button':
      case 'calc_button':
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              _updateCalculations();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Calculation performed!')),
              );
            },
            child: Text(component['label'] ?? 'Calculate'),
          ),
        );
      default:
        return _buildStaticComponent(component);
    }
  }

  Widget _buildSavePanel(ToolCreationState creationState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tool Summary
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tool Summary',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildSummaryRow('Name', creationState.toolName.isNotEmpty ? creationState.toolName : 'Untitled'),
                  _buildSummaryRow('Category', creationState.toolCategory.isNotEmpty ? creationState.toolCategory : 'Other'),
                  _buildSummaryRow('Components', '${creationState.uiComponents.length}'),
                  _buildSummaryRow('Backend Cells', '${creationState.backendData['cells']?.length ?? 0}'),
                  _buildSummaryRow('Creation Method', _getCreationMethodLabel(creationState.creationMethod)),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Validation Status
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Validation Status',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildValidationItem('Tool Name', creationState.toolName.isNotEmpty),
                  _buildValidationItem('Backend Data', creationState.backendData.isNotEmpty),
                  _buildValidationItem('UI Components', creationState.uiComponents.isNotEmpty),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Save Options
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Save Options',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  CheckboxListTile(
                    title: const Text('Make tool active'),
                    subtitle: const Text('Tool will be available for use immediately'),
                    value: true,
                    onChanged: null,
                    dense: true,
                  ),
                  
                  CheckboxListTile(
                    title: const Text('Add to favorites'),
                    subtitle: const Text('Pin tool to favorites list'),
                    value: false,
                    onChanged: (value) {},
                    dense: true,
                  ),
                  
                  CheckboxListTile(
                    title: const Text('Share with community'),
                    subtitle: const Text('Make tool available as template'),
                    value: false,
                    onChanged: (value) {},
                    dense: true,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Save Actions
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ElevatedButton.icon(
                onPressed: _canSaveTool(creationState) ? () => _saveTool(creationState) : null,
                icon: const Icon(Icons.save),
                label: const Text('Save Tool'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
              const SizedBox(height: 12),
              OutlinedButton.icon(
                onPressed: () => _saveAsDraft(creationState),
                icon: const Icon(Icons.drafts),
                label: const Text('Save as Draft'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
              const SizedBox(height: 12),
              OutlinedButton.icon(
                onPressed: () => _exportTool(creationState),
                icon: const Icon(Icons.download),
                label: const Text('Export Tool'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationItem(String label, bool isValid) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.error,
            size: 20,
            color: isValid ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  String _getCreationMethodLabel(String method) {
    switch (method) {
      case 'scratch':
        return 'From Scratch';
      case 'excel_import':
        return 'Excel Import';
      case 'template':
        return 'Template';
      default:
        return 'Unknown';
    }
  }

  bool _canSaveTool(ToolCreationState creationState) {
    return creationState.toolName.isNotEmpty &&
           creationState.backendData.isNotEmpty &&
           creationState.uiComponents.isNotEmpty;
  }

  dynamic _getCalculatedValue(String? cellRef) {
    if (cellRef == null || cellRef.isEmpty) return 0;
    
    // Simple calculation based on test values
    // In a real implementation, this would use the formula engine
    return _testValues.values.fold(0.0, (sum, value) {
      final numValue = double.tryParse(value.toString()) ?? 0.0;
      return sum + numValue;
    });
  }

  void _updateCalculations() {
    // Trigger recalculation of all formulas
    setState(() {});
  }

  void _saveTool(ToolCreationState creationState) {
    ref.read(toolCreationProvider.notifier).saveTool();
  }

  void _saveAsDraft(ToolCreationState creationState) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tool saved as draft'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _exportTool(ToolCreationState creationState) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tool exported successfully'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
