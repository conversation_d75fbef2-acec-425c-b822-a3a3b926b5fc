import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/accessibility_settings.dart';

class AccessibilityService {
  static final AccessibilityService _instance = AccessibilityService._internal();
  factory AccessibilityService() => _instance;
  AccessibilityService._internal();

  static const String _settingsKey = 'accessibility_settings';


  AccessibilitySettings _settings = AccessibilitySettings();
  final StreamController<AccessibilitySettings> _settingsController = 
      StreamController<AccessibilitySettings>.broadcast();
  final StreamController<String> _announcementController = 
      StreamController<String>.broadcast();

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  Timer? _autoSaveTimer;

  // Focus management
  FocusNode? _currentFocus;
  final List<FocusNode> _focusHistory = [];
  final Map<String, FocusNode> _namedFocusNodes = {};

  // Getters
  Stream<AccessibilitySettings> get settingsStream => _settingsController.stream;
  Stream<String> get announcementStream => _announcementController.stream;
  AccessibilitySettings get settings => _settings;
  bool get isInitialized => _isInitialized;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      _detectSystemAccessibilitySettings();
      _startAutoSaveTimer();
      _isInitialized = true;
      debugPrint('AccessibilityService initialized');
    } catch (e) {
      debugPrint('Error initializing AccessibilityService: $e');
    }
  }

  // Settings management
  Future<void> updateSettings(AccessibilitySettings newSettings) async {
    try {
      _settings = newSettings;
      await _saveSettings();
      _settingsController.add(_settings);
      
      // Apply system-level changes
      await _applySystemSettings();
      
      debugPrint('Accessibility settings updated');
    } catch (e) {
      debugPrint('Error updating accessibility settings: $e');
    }
  }

  Future<void> updateSetting<T>(String key, T value) async {
    try {
      final updatedSettings = _updateSettingByKey(key, value);
      await updateSettings(updatedSettings);
    } catch (e) {
      debugPrint('Error updating accessibility setting $key: $e');
    }
  }

  // Screen reader support
  void announce(String message, {bool interrupt = false}) {
    if (!_settings.screenReaderEnabled || !_settings.announceChanges) return;

    try {
      if (interrupt) {
        // SemanticsService.announce(message, TextDirection.ltr);
      } else {
        _announcementController.add(message);
      }
      debugPrint('Accessibility announcement: $message');
    } catch (e) {
      debugPrint('Error making accessibility announcement: $e');
    }
  }

  void announcePageChange(String pageName) {
    if (_settings.screenReaderEnabled) {
      announce('Navigated to $pageName', interrupt: true);
    }
  }

  void announceAction(String action) {
    if (_settings.screenReaderEnabled) {
      announce(action);
    }
  }

  void announceError(String error) {
    if (_settings.screenReaderEnabled) {
      announce('Error: $error', interrupt: true);
    }
  }

  void announceSuccess(String message) {
    if (_settings.screenReaderEnabled) {
      announce('Success: $message');
    }
  }

  // Focus management
  void setCurrentFocus(FocusNode focusNode, {String? name}) {
    _currentFocus = focusNode;
    
    if (!_focusHistory.contains(focusNode)) {
      _focusHistory.add(focusNode);
      if (_focusHistory.length > 10) {
        _focusHistory.removeAt(0);
      }
    }

    if (name != null) {
      _namedFocusNodes[name] = focusNode;
    }
  }

  void focusPrevious() {
    if (_focusHistory.length > 1) {
      final currentIndex = _focusHistory.indexOf(_currentFocus!);
      if (currentIndex > 0) {
        _focusHistory[currentIndex - 1].requestFocus();
      }
    }
  }

  void focusNext() {
    if (_currentFocus != null) {
      _currentFocus!.nextFocus();
    }
  }

  void focusNamed(String name) {
    final focusNode = _namedFocusNodes[name];
    if (focusNode != null) {
      focusNode.requestFocus();
    }
  }

  // Keyboard navigation
  bool handleKeyEvent(KeyEvent event) {
    if (!_settings.usesKeyboardNavigation) return false;

    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.tab:
          if (HardwareKeyboard.instance.isShiftPressed) {
            focusPrevious();
          } else {
            focusNext();
          }
          return true;
        
        case LogicalKeyboardKey.arrowUp:
        case LogicalKeyboardKey.arrowDown:
        case LogicalKeyboardKey.arrowLeft:
        case LogicalKeyboardKey.arrowRight:
          if (_settings.arrowKeyNavigation) {
            _handleArrowKeyNavigation(event.logicalKey);
            return true;
          }
          break;
        
        case LogicalKeyboardKey.escape:
          _handleEscapeKey();
          return true;
        
        case LogicalKeyboardKey.enter:
        case LogicalKeyboardKey.space:
          _handleActivationKey();
          return true;
      }
    }

    return false;
  }

  // Color and contrast adjustments
  Color adjustColorForAccessibility(Color color) {
    if (!_settings.hasVisualImpairment) return color;

    Color adjustedColor = color;

    // Apply contrast adjustment
    if (_settings.contrastAdjustment != 0) {
      final hsl = HSLColor.fromColor(adjustedColor);
      final newLightness = (hsl.lightness + _settings.contrastAdjustment).clamp(0.0, 1.0);
      adjustedColor = hsl.withLightness(newLightness).toColor();
    }

    // Apply brightness adjustment
    if (_settings.brightnessAdjustment != 0) {
      final hsl = HSLColor.fromColor(adjustedColor);
      final newLightness = (hsl.lightness + _settings.brightnessAdjustment).clamp(0.0, 1.0);
      adjustedColor = hsl.withLightness(newLightness).toColor();
    }

    // Apply color blindness adjustments
    if (_settings.colorBlindnessType != ColorBlindnessType.none) {
      adjustedColor = _adjustForColorBlindness(adjustedColor, _settings.colorBlindnessType);
    }

    // Apply color inversion
    if (_settings.invertColors) {
      adjustedColor = Color.fromARGB(
        (adjustedColor.a * 255).round(),
        255 - (adjustedColor.r * 255).round(),
        255 - (adjustedColor.g * 255).round(),
        255 - (adjustedColor.b * 255).round(),
      );
    }

    return adjustedColor;
  }

  // Text scaling
  double getScaledTextSize(double baseSize) {
    double scaleFactor = _settings.textScaleFactor;
    
    if (_settings.largeTextMode) scaleFactor *= 1.2;
    if (_settings.extraLargeTextMode) scaleFactor *= 1.5;
    
    return baseSize * scaleFactor;
  }

  TextStyle getAccessibleTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontSize: getScaledTextSize(baseStyle.fontSize ?? 14),
      fontWeight: _settings.boldTextMode ? FontWeight.bold : baseStyle.fontWeight,
    );
  }

  // Touch target sizing
  double getAccessibleTouchTargetSize(double baseSize) {
    return (baseSize * _settings.buttonSize).clamp(_settings.touchTargetSize, double.infinity);
  }

  EdgeInsets getAccessiblePadding(EdgeInsets basePadding) {
    final multiplier = _settings.buttonSize;
    return EdgeInsets.only(
      left: basePadding.left * multiplier,
      top: basePadding.top * multiplier,
      right: basePadding.right * multiplier,
      bottom: basePadding.bottom * multiplier,
    );
  }

  // Animation and motion
  Duration getAccessibleAnimationDuration(Duration baseDuration) {
    if (_settings.reduceMotion || _settings.pauseAnimations) {
      return Duration.zero;
    }
    return baseDuration;
  }

  Curve getAccessibleAnimationCurve(Curve baseCurve) {
    if (_settings.reduceMotion) {
      return Curves.linear;
    }
    return baseCurve;
  }

  // Haptic feedback
  void performHapticFeedback(HapticFeedback type) {
    if (_settings.hapticFeedback) {
      HapticFeedback.vibrate();
    }
  }

  // Private methods
  void _detectSystemAccessibilitySettings() {
    // This would integrate with platform-specific accessibility APIs
    // For now, we'll use basic Flutter accessibility detection
    
    final platformDispatcher = WidgetsBinding.instance.platformDispatcher;
    
    if (platformDispatcher.accessibilityFeatures.boldText) {
      _settings = _settings.copyWith(boldTextMode: true);
    }
    
    if (platformDispatcher.accessibilityFeatures.reduceMotion) {
      _settings = _settings.copyWith(reduceMotion: true);
    }
    
    if (platformDispatcher.accessibilityFeatures.highContrast) {
      _settings = _settings.copyWith(highContrastMode: true);
    }
    
    final textScaleFactor = platformDispatcher.textScaleFactor;
    if (textScaleFactor > 1.0) {
      _settings = _settings.copyWith(
        textScaleFactor: textScaleFactor,
        largeTextMode: textScaleFactor > 1.2,
        extraLargeTextMode: textScaleFactor > 1.5,
      );
    }
  }

  Future<void> _applySystemSettings() async {
    // Apply settings that affect the entire system
    if (_settings.screenReaderEnabled) {
      // Enable screen reader optimizations
      // SemanticsBinding.instance.ensureSemantics();
    }
  }

  AccessibilitySettings _updateSettingByKey(String key, dynamic value) {
    switch (key) {
      case 'highContrastMode':
        return _settings.copyWith(highContrastMode: value as bool);
      case 'largeTextMode':
        return _settings.copyWith(largeTextMode: value as bool);
      case 'reduceMotion':
        return _settings.copyWith(reduceMotion: value as bool);
      case 'buttonSize':
        return _settings.copyWith(buttonSize: value as double);
      case 'screenReaderEnabled':
        return _settings.copyWith(screenReaderEnabled: value as bool);
      // Add more cases as needed
      default:
        return _settings;
    }
  }

  void _handleArrowKeyNavigation(LogicalKeyboardKey key) {
    // Implement arrow key navigation logic
    switch (key) {
      case LogicalKeyboardKey.arrowUp:
        focusPrevious();
        break;
      case LogicalKeyboardKey.arrowDown:
        focusNext();
        break;
      case LogicalKeyboardKey.arrowLeft:
        // Navigate to previous sibling
        break;
      case LogicalKeyboardKey.arrowRight:
        // Navigate to next sibling
        break;
    }
  }

  void _handleEscapeKey() {
    // Handle escape key - typically close dialogs or go back
    announce('Escape pressed');
  }

  void _handleActivationKey() {
    // Handle enter/space key - typically activate focused element
    if (_currentFocus != null) {
      // Simulate tap on focused element
      announce('Activated');
    }
  }

  Color _adjustForColorBlindness(Color color, ColorBlindnessType type) {
    // Simplified color blindness adjustment
    // In a real implementation, you'd use proper color transformation matrices
    switch (type) {
      case ColorBlindnessType.protanopia:
        // Red-blind: reduce red component
        return Color.fromARGB(
          (color.a * 255).round(),
          ((color.r * 255) * 0.3).round(),
          (color.g * 255).round(),
          (color.b * 255).round(),
        );
      case ColorBlindnessType.deuteranopia:
        // Green-blind: reduce green component
        return Color.fromARGB(
          (color.a * 255).round(),
          (color.r * 255).round(),
          ((color.g * 255) * 0.3).round(),
          (color.b * 255).round(),
        );
      case ColorBlindnessType.tritanopia:
        // Blue-blind: reduce blue component
        return Color.fromARGB(
          (color.a * 255).round(),
          (color.r * 255).round(),
          (color.g * 255).round(),
          ((color.b * 255) * 0.3).round(),
        );
      case ColorBlindnessType.achromatopsia:
        // Complete color blindness: convert to grayscale
        final gray = (0.299 * (color.r * 255) + 0.587 * (color.g * 255) + 0.114 * (color.b * 255)).round();
        return Color.fromARGB((color.a * 255).round(), gray, gray, gray);
      default:
        return color;
    }
  }

  void _startAutoSaveTimer() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (_settings.autoSave) {
        _saveSettings();
      }
    });
  }

  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs?.getString(_settingsKey);
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _settings = AccessibilitySettings.fromJson(settingsMap);
        _settingsController.add(_settings);
      }
    } catch (e) {
      debugPrint('Error loading accessibility settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final settingsJson = json.encode(_settings.toJson());
      await _prefs?.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving accessibility settings: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _autoSaveTimer?.cancel();
    _settingsController.close();
    _announcementController.close();
  }
}
