import 'package:flutter/material.dart';

enum ThemeMode { system, light, dark }
enum Language { english, arabic, french, spanish, german }
enum DateFormat { ddmmyyyy, mmddyyyy, yyyymmdd }
enum TimeFormat { twelve, twentyFour }
enum NotificationSound { default_, chime, bell, none }

class AppSettings {
  // Theme settings
  ThemeMode themeMode;
  Color primaryColor;
  Color accentColor;
  bool useMaterial3;
  double fontSize;
  bool useSystemFont;
  String customFontFamily;

  // Language and localization
  Language language;
  DateFormat dateFormat;
  TimeFormat timeFormat;
  String timezone;

  // Notifications
  bool notificationsEnabled;
  NotificationSound notificationSound;
  bool vibrationEnabled;
  bool showNotificationPreviews;
  TimeOfDay? quietHoursStart;
  TimeOfDay? quietHoursEnd;

  // Privacy and security
  bool requireAuthentication;
  bool biometricAuthentication;
  int autoLockMinutes;
  bool hideContentInRecents;
  bool analyticsEnabled;
  bool crashReportingEnabled;

  // Backup and sync
  bool autoBackupEnabled;
  bool cloudSyncEnabled;
  String backupFrequency; // daily, weekly, monthly
  bool syncOnWifiOnly;
  int maxBackupFiles;

  // Performance
  bool animationsEnabled;
  bool hapticFeedbackEnabled;
  bool autoSaveEnabled;
  int autoSaveIntervalSeconds;
  bool lowPowerMode;

  // Accessibility
  bool highContrastMode;
  bool largeTextMode;
  bool screenReaderSupport;
  bool reduceMotion;
  double buttonSize; // 1.0 = normal, 1.5 = large
  bool showTooltips;

  // Voice memos
  String audioQuality; // low, medium, high
  String audioFormat; // aac, mp3, wav
  bool autoTranscription;
  int maxRecordingMinutes;

  // Notes
  bool autoSaveNotes;
  String defaultNoteCategory;
  bool showWordCount;
  bool spellCheckEnabled;
  String exportFormat; // txt, md, pdf

  // Todos
  bool showCompletedTodos;
  int defaultReminderMinutes;
  bool autoArchiveCompleted;
  int autoArchiveDays;

  // Dhikr
  bool dhikrVibration;
  bool dhikrSound;
  int defaultDhikrTarget;
  bool showArabicText;
  bool showTransliteration;
  bool showTranslation;

  // Advanced
  bool developerMode;
  bool debugLogging;
  String logLevel; // error, warning, info, debug
  bool betaFeatures;

  AppSettings({
    this.themeMode = ThemeMode.system,
    this.primaryColor = Colors.deepPurple,
    this.accentColor = Colors.deepPurpleAccent,
    this.useMaterial3 = true,
    this.fontSize = 14.0,
    this.useSystemFont = true,
    this.customFontFamily = '',
    this.language = Language.english,
    this.dateFormat = DateFormat.ddmmyyyy,
    this.timeFormat = TimeFormat.twelve,
    this.timezone = 'UTC',
    this.notificationsEnabled = true,
    this.notificationSound = NotificationSound.default_,
    this.vibrationEnabled = true,
    this.showNotificationPreviews = true,
    this.quietHoursStart,
    this.quietHoursEnd,
    this.requireAuthentication = false,
    this.biometricAuthentication = false,
    this.autoLockMinutes = 5,
    this.hideContentInRecents = false,
    this.analyticsEnabled = true,
    this.crashReportingEnabled = true,
    this.autoBackupEnabled = true,
    this.cloudSyncEnabled = false,
    this.backupFrequency = 'daily',
    this.syncOnWifiOnly = true,
    this.maxBackupFiles = 10,
    this.animationsEnabled = true,
    this.hapticFeedbackEnabled = true,
    this.autoSaveEnabled = true,
    this.autoSaveIntervalSeconds = 30,
    this.lowPowerMode = false,
    this.highContrastMode = false,
    this.largeTextMode = false,
    this.screenReaderSupport = false,
    this.reduceMotion = false,
    this.buttonSize = 1.0,
    this.showTooltips = true,
    this.audioQuality = 'medium',
    this.audioFormat = 'aac',
    this.autoTranscription = false,
    this.maxRecordingMinutes = 60,
    this.autoSaveNotes = true,
    this.defaultNoteCategory = 'general',
    this.showWordCount = true,
    this.spellCheckEnabled = true,
    this.exportFormat = 'md',
    this.showCompletedTodos = false,
    this.defaultReminderMinutes = 15,
    this.autoArchiveCompleted = true,
    this.autoArchiveDays = 30,
    this.dhikrVibration = true,
    this.dhikrSound = true,
    this.defaultDhikrTarget = 33,
    this.showArabicText = true,
    this.showTransliteration = true,
    this.showTranslation = true,
    this.developerMode = false,
    this.debugLogging = false,
    this.logLevel = 'error',
    this.betaFeatures = false,
  });

  AppSettings copyWith({
    ThemeMode? themeMode,
    Color? primaryColor,
    Color? accentColor,
    bool? useMaterial3,
    double? fontSize,
    bool? useSystemFont,
    String? customFontFamily,
    Language? language,
    DateFormat? dateFormat,
    TimeFormat? timeFormat,
    String? timezone,
    bool? notificationsEnabled,
    NotificationSound? notificationSound,
    bool? vibrationEnabled,
    bool? showNotificationPreviews,
    TimeOfDay? quietHoursStart,
    TimeOfDay? quietHoursEnd,
    bool? requireAuthentication,
    bool? biometricAuthentication,
    int? autoLockMinutes,
    bool? hideContentInRecents,
    bool? analyticsEnabled,
    bool? crashReportingEnabled,
    bool? autoBackupEnabled,
    bool? cloudSyncEnabled,
    String? backupFrequency,
    bool? syncOnWifiOnly,
    int? maxBackupFiles,
    bool? animationsEnabled,
    bool? hapticFeedbackEnabled,
    bool? autoSaveEnabled,
    int? autoSaveIntervalSeconds,
    bool? lowPowerMode,
    bool? highContrastMode,
    bool? largeTextMode,
    bool? screenReaderSupport,
    bool? reduceMotion,
    double? buttonSize,
    bool? showTooltips,
    String? audioQuality,
    String? audioFormat,
    bool? autoTranscription,
    int? maxRecordingMinutes,
    bool? autoSaveNotes,
    String? defaultNoteCategory,
    bool? showWordCount,
    bool? spellCheckEnabled,
    String? exportFormat,
    bool? showCompletedTodos,
    int? defaultReminderMinutes,
    bool? autoArchiveCompleted,
    int? autoArchiveDays,
    bool? dhikrVibration,
    bool? dhikrSound,
    int? defaultDhikrTarget,
    bool? showArabicText,
    bool? showTransliteration,
    bool? showTranslation,
    bool? developerMode,
    bool? debugLogging,
    String? logLevel,
    bool? betaFeatures,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      primaryColor: primaryColor ?? this.primaryColor,
      accentColor: accentColor ?? this.accentColor,
      useMaterial3: useMaterial3 ?? this.useMaterial3,
      fontSize: fontSize ?? this.fontSize,
      useSystemFont: useSystemFont ?? this.useSystemFont,
      customFontFamily: customFontFamily ?? this.customFontFamily,
      language: language ?? this.language,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      timezone: timezone ?? this.timezone,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      notificationSound: notificationSound ?? this.notificationSound,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      showNotificationPreviews: showNotificationPreviews ?? this.showNotificationPreviews,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      requireAuthentication: requireAuthentication ?? this.requireAuthentication,
      biometricAuthentication: biometricAuthentication ?? this.biometricAuthentication,
      autoLockMinutes: autoLockMinutes ?? this.autoLockMinutes,
      hideContentInRecents: hideContentInRecents ?? this.hideContentInRecents,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      crashReportingEnabled: crashReportingEnabled ?? this.crashReportingEnabled,
      autoBackupEnabled: autoBackupEnabled ?? this.autoBackupEnabled,
      cloudSyncEnabled: cloudSyncEnabled ?? this.cloudSyncEnabled,
      backupFrequency: backupFrequency ?? this.backupFrequency,
      syncOnWifiOnly: syncOnWifiOnly ?? this.syncOnWifiOnly,
      maxBackupFiles: maxBackupFiles ?? this.maxBackupFiles,
      animationsEnabled: animationsEnabled ?? this.animationsEnabled,
      hapticFeedbackEnabled: hapticFeedbackEnabled ?? this.hapticFeedbackEnabled,
      autoSaveEnabled: autoSaveEnabled ?? this.autoSaveEnabled,
      autoSaveIntervalSeconds: autoSaveIntervalSeconds ?? this.autoSaveIntervalSeconds,
      lowPowerMode: lowPowerMode ?? this.lowPowerMode,
      highContrastMode: highContrastMode ?? this.highContrastMode,
      largeTextMode: largeTextMode ?? this.largeTextMode,
      screenReaderSupport: screenReaderSupport ?? this.screenReaderSupport,
      reduceMotion: reduceMotion ?? this.reduceMotion,
      buttonSize: buttonSize ?? this.buttonSize,
      showTooltips: showTooltips ?? this.showTooltips,
      audioQuality: audioQuality ?? this.audioQuality,
      audioFormat: audioFormat ?? this.audioFormat,
      autoTranscription: autoTranscription ?? this.autoTranscription,
      maxRecordingMinutes: maxRecordingMinutes ?? this.maxRecordingMinutes,
      autoSaveNotes: autoSaveNotes ?? this.autoSaveNotes,
      defaultNoteCategory: defaultNoteCategory ?? this.defaultNoteCategory,
      showWordCount: showWordCount ?? this.showWordCount,
      spellCheckEnabled: spellCheckEnabled ?? this.spellCheckEnabled,
      exportFormat: exportFormat ?? this.exportFormat,
      showCompletedTodos: showCompletedTodos ?? this.showCompletedTodos,
      defaultReminderMinutes: defaultReminderMinutes ?? this.defaultReminderMinutes,
      autoArchiveCompleted: autoArchiveCompleted ?? this.autoArchiveCompleted,
      autoArchiveDays: autoArchiveDays ?? this.autoArchiveDays,
      dhikrVibration: dhikrVibration ?? this.dhikrVibration,
      dhikrSound: dhikrSound ?? this.dhikrSound,
      defaultDhikrTarget: defaultDhikrTarget ?? this.defaultDhikrTarget,
      showArabicText: showArabicText ?? this.showArabicText,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      showTranslation: showTranslation ?? this.showTranslation,
      developerMode: developerMode ?? this.developerMode,
      debugLogging: debugLogging ?? this.debugLogging,
      logLevel: logLevel ?? this.logLevel,
      betaFeatures: betaFeatures ?? this.betaFeatures,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.name,
      'primaryColor': (primaryColor.r * 255).round() << 16 | (primaryColor.g * 255).round() << 8 | (primaryColor.b * 255).round() | (primaryColor.a * 255).round() << 24,
      'accentColor': (accentColor.r * 255).round() << 16 | (accentColor.g * 255).round() << 8 | (accentColor.b * 255).round() | (accentColor.a * 255).round() << 24,
      'useMaterial3': useMaterial3,
      'fontSize': fontSize,
      'useSystemFont': useSystemFont,
      'customFontFamily': customFontFamily,
      'language': language.name,
      'dateFormat': dateFormat.name,
      'timeFormat': timeFormat.name,
      'timezone': timezone,
      'notificationsEnabled': notificationsEnabled,
      'notificationSound': notificationSound.name,
      'vibrationEnabled': vibrationEnabled,
      'showNotificationPreviews': showNotificationPreviews,
      'quietHoursStart': quietHoursStart != null ? '${quietHoursStart!.hour}:${quietHoursStart!.minute}' : null,
      'quietHoursEnd': quietHoursEnd != null ? '${quietHoursEnd!.hour}:${quietHoursEnd!.minute}' : null,
      'requireAuthentication': requireAuthentication,
      'biometricAuthentication': biometricAuthentication,
      'autoLockMinutes': autoLockMinutes,
      'hideContentInRecents': hideContentInRecents,
      'analyticsEnabled': analyticsEnabled,
      'crashReportingEnabled': crashReportingEnabled,
      'autoBackupEnabled': autoBackupEnabled,
      'cloudSyncEnabled': cloudSyncEnabled,
      'backupFrequency': backupFrequency,
      'syncOnWifiOnly': syncOnWifiOnly,
      'maxBackupFiles': maxBackupFiles,
      'animationsEnabled': animationsEnabled,
      'hapticFeedbackEnabled': hapticFeedbackEnabled,
      'autoSaveEnabled': autoSaveEnabled,
      'autoSaveIntervalSeconds': autoSaveIntervalSeconds,
      'lowPowerMode': lowPowerMode,
      'highContrastMode': highContrastMode,
      'largeTextMode': largeTextMode,
      'screenReaderSupport': screenReaderSupport,
      'reduceMotion': reduceMotion,
      'buttonSize': buttonSize,
      'showTooltips': showTooltips,
      'audioQuality': audioQuality,
      'audioFormat': audioFormat,
      'autoTranscription': autoTranscription,
      'maxRecordingMinutes': maxRecordingMinutes,
      'autoSaveNotes': autoSaveNotes,
      'defaultNoteCategory': defaultNoteCategory,
      'showWordCount': showWordCount,
      'spellCheckEnabled': spellCheckEnabled,
      'exportFormat': exportFormat,
      'showCompletedTodos': showCompletedTodos,
      'defaultReminderMinutes': defaultReminderMinutes,
      'autoArchiveCompleted': autoArchiveCompleted,
      'autoArchiveDays': autoArchiveDays,
      'dhikrVibration': dhikrVibration,
      'dhikrSound': dhikrSound,
      'defaultDhikrTarget': defaultDhikrTarget,
      'showArabicText': showArabicText,
      'showTransliteration': showTransliteration,
      'showTranslation': showTranslation,
      'developerMode': developerMode,
      'debugLogging': debugLogging,
      'logLevel': logLevel,
      'betaFeatures': betaFeatures,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      themeMode: ThemeMode.values.firstWhere(
        (mode) => mode.name == json['themeMode'],
        orElse: () => ThemeMode.system,
      ),
      primaryColor: Color(json['primaryColor'] ?? ((Colors.deepPurple.r * 255).round() << 16 | (Colors.deepPurple.g * 255).round() << 8 | (Colors.deepPurple.b * 255).round() | (Colors.deepPurple.a * 255).round() << 24)),
      accentColor: Color(json['accentColor'] ?? ((Colors.deepPurpleAccent.r * 255).round() << 16 | (Colors.deepPurpleAccent.g * 255).round() << 8 | (Colors.deepPurpleAccent.b * 255).round() | (Colors.deepPurpleAccent.a * 255).round() << 24)),
      useMaterial3: json['useMaterial3'] ?? true,
      fontSize: (json['fontSize'] ?? 14.0).toDouble(),
      useSystemFont: json['useSystemFont'] ?? true,
      customFontFamily: json['customFontFamily'] ?? '',
      language: Language.values.firstWhere(
        (lang) => lang.name == json['language'],
        orElse: () => Language.english,
      ),
      dateFormat: DateFormat.values.firstWhere(
        (format) => format.name == json['dateFormat'],
        orElse: () => DateFormat.ddmmyyyy,
      ),
      timeFormat: TimeFormat.values.firstWhere(
        (format) => format.name == json['timeFormat'],
        orElse: () => TimeFormat.twelve,
      ),
      timezone: json['timezone'] ?? 'UTC',
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      notificationSound: NotificationSound.values.firstWhere(
        (sound) => sound.name == json['notificationSound'],
        orElse: () => NotificationSound.default_,
      ),
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      showNotificationPreviews: json['showNotificationPreviews'] ?? true,
      quietHoursStart: json['quietHoursStart'] != null ? _parseTimeOfDay(json['quietHoursStart']) : null,
      quietHoursEnd: json['quietHoursEnd'] != null ? _parseTimeOfDay(json['quietHoursEnd']) : null,
      requireAuthentication: json['requireAuthentication'] ?? false,
      biometricAuthentication: json['biometricAuthentication'] ?? false,
      autoLockMinutes: json['autoLockMinutes'] ?? 5,
      hideContentInRecents: json['hideContentInRecents'] ?? false,
      analyticsEnabled: json['analyticsEnabled'] ?? true,
      crashReportingEnabled: json['crashReportingEnabled'] ?? true,
      autoBackupEnabled: json['autoBackupEnabled'] ?? true,
      cloudSyncEnabled: json['cloudSyncEnabled'] ?? false,
      backupFrequency: json['backupFrequency'] ?? 'daily',
      syncOnWifiOnly: json['syncOnWifiOnly'] ?? true,
      maxBackupFiles: json['maxBackupFiles'] ?? 10,
      animationsEnabled: json['animationsEnabled'] ?? true,
      hapticFeedbackEnabled: json['hapticFeedbackEnabled'] ?? true,
      autoSaveEnabled: json['autoSaveEnabled'] ?? true,
      autoSaveIntervalSeconds: json['autoSaveIntervalSeconds'] ?? 30,
      lowPowerMode: json['lowPowerMode'] ?? false,
      highContrastMode: json['highContrastMode'] ?? false,
      largeTextMode: json['largeTextMode'] ?? false,
      screenReaderSupport: json['screenReaderSupport'] ?? false,
      reduceMotion: json['reduceMotion'] ?? false,
      buttonSize: (json['buttonSize'] ?? 1.0).toDouble(),
      showTooltips: json['showTooltips'] ?? true,
      audioQuality: json['audioQuality'] ?? 'medium',
      audioFormat: json['audioFormat'] ?? 'aac',
      autoTranscription: json['autoTranscription'] ?? false,
      maxRecordingMinutes: json['maxRecordingMinutes'] ?? 60,
      autoSaveNotes: json['autoSaveNotes'] ?? true,
      defaultNoteCategory: json['defaultNoteCategory'] ?? 'general',
      showWordCount: json['showWordCount'] ?? true,
      spellCheckEnabled: json['spellCheckEnabled'] ?? true,
      exportFormat: json['exportFormat'] ?? 'md',
      showCompletedTodos: json['showCompletedTodos'] ?? false,
      defaultReminderMinutes: json['defaultReminderMinutes'] ?? 15,
      autoArchiveCompleted: json['autoArchiveCompleted'] ?? true,
      autoArchiveDays: json['autoArchiveDays'] ?? 30,
      dhikrVibration: json['dhikrVibration'] ?? true,
      dhikrSound: json['dhikrSound'] ?? true,
      defaultDhikrTarget: json['defaultDhikrTarget'] ?? 33,
      showArabicText: json['showArabicText'] ?? true,
      showTransliteration: json['showTransliteration'] ?? true,
      showTranslation: json['showTranslation'] ?? true,
      developerMode: json['developerMode'] ?? false,
      debugLogging: json['debugLogging'] ?? false,
      logLevel: json['logLevel'] ?? 'error',
      betaFeatures: json['betaFeatures'] ?? false,
    );
  }

  static TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  // Helper methods for getting display strings
  String get themeModeDisplayName {
    switch (themeMode) {
      case ThemeMode.system:
        return 'System';
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
    }
  }

  String get languageDisplayName {
    switch (language) {
      case Language.english:
        return 'English';
      case Language.arabic:
        return 'العربية';
      case Language.french:
        return 'Français';
      case Language.spanish:
        return 'Español';
      case Language.german:
        return 'Deutsch';
    }
  }

  String get dateFormatDisplayName {
    switch (dateFormat) {
      case DateFormat.ddmmyyyy:
        return 'DD/MM/YYYY';
      case DateFormat.mmddyyyy:
        return 'MM/DD/YYYY';
      case DateFormat.yyyymmdd:
        return 'YYYY-MM-DD';
    }
  }

  String get timeFormatDisplayName {
    switch (timeFormat) {
      case TimeFormat.twelve:
        return '12 Hour';
      case TimeFormat.twentyFour:
        return '24 Hour';
    }
  }

  String get notificationSoundDisplayName {
    switch (notificationSound) {
      case NotificationSound.default_:
        return 'Default';
      case NotificationSound.chime:
        return 'Chime';
      case NotificationSound.bell:
        return 'Bell';
      case NotificationSound.none:
        return 'None';
    }
  }
}
