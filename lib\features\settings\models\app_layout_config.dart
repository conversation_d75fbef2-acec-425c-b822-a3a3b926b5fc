import 'package:flutter/material.dart';

/// App layout configuration model
class AppLayoutConfig {
  final String id;
  final String name;
  final String description;
  final LayoutType layoutType;
  final NavigationType navigationType;
  final SidebarPosition sidebarPosition;
  final double sidebarWidth;
  final bool showSidebarLabels;
  final bool compactMode;
  final bool showQuickActions;
  final bool showBreadcrumbs;
  final bool showStatusBar;
  final HeaderStyle headerStyle;
  final FooterStyle footerStyle;
  final double contentPadding;
  final double cardSpacing;
  final BorderRadius cardBorderRadius;
  final double elevation;
  final bool enableAnimations;
  final Duration animationDuration;
  final Curve animationCurve;
  final bool enableHapticFeedback;
  final bool enableSounds;
  final double iconSize;
  final double buttonHeight;
  final double inputHeight;
  final EdgeInsets screenPadding;
  final EdgeInsets cardPadding;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime? modifiedAt;

  const AppLayoutConfig({
    required this.id,
    required this.name,
    required this.description,
    this.layoutType = LayoutType.adaptive,
    this.navigationType = NavigationType.rail,
    this.sidebarPosition = SidebarPosition.left,
    this.sidebarWidth = 280.0,
    this.showSidebarLabels = true,
    this.compactMode = false,
    this.showQuickActions = true,
    this.showBreadcrumbs = true,
    this.showStatusBar = true,
    this.headerStyle = HeaderStyle.elevated,
    this.footerStyle = FooterStyle.minimal,
    this.contentPadding = 16.0,
    this.cardSpacing = 12.0,
    this.cardBorderRadius = const BorderRadius.all(Radius.circular(12.0)),
    this.elevation = 2.0,
    this.enableAnimations = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.enableHapticFeedback = true,
    this.enableSounds = false,
    this.iconSize = 24.0,
    this.buttonHeight = 48.0,
    this.inputHeight = 56.0,
    this.screenPadding = const EdgeInsets.all(16.0),
    this.cardPadding = const EdgeInsets.all(16.0),
    this.isDefault = false,
    required this.createdAt,
    this.modifiedAt,
  });

  factory AppLayoutConfig.fromJson(Map<String, dynamic> json) {
    return AppLayoutConfig(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      layoutType: LayoutType.values[json['layout_type'] as int? ?? 0],
      navigationType: NavigationType.values[json['navigation_type'] as int? ?? 0],
      sidebarPosition: SidebarPosition.values[json['sidebar_position'] as int? ?? 0],
      sidebarWidth: (json['sidebar_width'] as num?)?.toDouble() ?? 280.0,
      showSidebarLabels: json['show_sidebar_labels'] as bool? ?? true,
      compactMode: json['compact_mode'] as bool? ?? false,
      showQuickActions: json['show_quick_actions'] as bool? ?? true,
      showBreadcrumbs: json['show_breadcrumbs'] as bool? ?? true,
      showStatusBar: json['show_status_bar'] as bool? ?? true,
      headerStyle: HeaderStyle.values[json['header_style'] as int? ?? 0],
      footerStyle: FooterStyle.values[json['footer_style'] as int? ?? 0],
      contentPadding: (json['content_padding'] as num?)?.toDouble() ?? 16.0,
      cardSpacing: (json['card_spacing'] as num?)?.toDouble() ?? 12.0,
      cardBorderRadius: _borderRadiusFromJson(json['card_border_radius'] as Map<String, dynamic>?),
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      enableAnimations: json['enable_animations'] as bool? ?? true,
      animationDuration: Duration(milliseconds: json['animation_duration'] as int? ?? 300),
      animationCurve: _curveFromString(json['animation_curve'] as String? ?? 'easeInOut'),
      enableHapticFeedback: json['enable_haptic_feedback'] as bool? ?? true,
      enableSounds: json['enable_sounds'] as bool? ?? false,
      iconSize: (json['icon_size'] as num?)?.toDouble() ?? 24.0,
      buttonHeight: (json['button_height'] as num?)?.toDouble() ?? 48.0,
      inputHeight: (json['input_height'] as num?)?.toDouble() ?? 56.0,
      screenPadding: _edgeInsetsFromJson(json['screen_padding'] as Map<String, dynamic>?),
      cardPadding: _edgeInsetsFromJson(json['card_padding'] as Map<String, dynamic>?),
      isDefault: json['is_default'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      modifiedAt: json['modified_at'] != null ? DateTime.parse(json['modified_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'layout_type': layoutType.index,
      'navigation_type': navigationType.index,
      'sidebar_position': sidebarPosition.index,
      'sidebar_width': sidebarWidth,
      'show_sidebar_labels': showSidebarLabels,
      'compact_mode': compactMode,
      'show_quick_actions': showQuickActions,
      'show_breadcrumbs': showBreadcrumbs,
      'show_status_bar': showStatusBar,
      'header_style': headerStyle.index,
      'footer_style': footerStyle.index,
      'content_padding': contentPadding,
      'card_spacing': cardSpacing,
      'card_border_radius': _borderRadiusToJson(cardBorderRadius),
      'elevation': elevation,
      'enable_animations': enableAnimations,
      'animation_duration': animationDuration.inMilliseconds,
      'animation_curve': _curveToString(animationCurve),
      'enable_haptic_feedback': enableHapticFeedback,
      'enable_sounds': enableSounds,
      'icon_size': iconSize,
      'button_height': buttonHeight,
      'input_height': inputHeight,
      'screen_padding': _edgeInsetsToJson(screenPadding),
      'card_padding': _edgeInsetsToJson(cardPadding),
      'is_default': isDefault,
      'created_at': createdAt.toIso8601String(),
      'modified_at': modifiedAt?.toIso8601String(),
    };
  }

  AppLayoutConfig copyWith({
    String? id,
    String? name,
    String? description,
    LayoutType? layoutType,
    NavigationType? navigationType,
    SidebarPosition? sidebarPosition,
    double? sidebarWidth,
    bool? showSidebarLabels,
    bool? compactMode,
    bool? showQuickActions,
    bool? showBreadcrumbs,
    bool? showStatusBar,
    HeaderStyle? headerStyle,
    FooterStyle? footerStyle,
    double? contentPadding,
    double? cardSpacing,
    BorderRadius? cardBorderRadius,
    double? elevation,
    bool? enableAnimations,
    Duration? animationDuration,
    Curve? animationCurve,
    bool? enableHapticFeedback,
    bool? enableSounds,
    double? iconSize,
    double? buttonHeight,
    double? inputHeight,
    EdgeInsets? screenPadding,
    EdgeInsets? cardPadding,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? modifiedAt,
  }) {
    return AppLayoutConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      layoutType: layoutType ?? this.layoutType,
      navigationType: navigationType ?? this.navigationType,
      sidebarPosition: sidebarPosition ?? this.sidebarPosition,
      sidebarWidth: sidebarWidth ?? this.sidebarWidth,
      showSidebarLabels: showSidebarLabels ?? this.showSidebarLabels,
      compactMode: compactMode ?? this.compactMode,
      showQuickActions: showQuickActions ?? this.showQuickActions,
      showBreadcrumbs: showBreadcrumbs ?? this.showBreadcrumbs,
      showStatusBar: showStatusBar ?? this.showStatusBar,
      headerStyle: headerStyle ?? this.headerStyle,
      footerStyle: footerStyle ?? this.footerStyle,
      contentPadding: contentPadding ?? this.contentPadding,
      cardSpacing: cardSpacing ?? this.cardSpacing,
      cardBorderRadius: cardBorderRadius ?? this.cardBorderRadius,
      elevation: elevation ?? this.elevation,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      animationDuration: animationDuration ?? this.animationDuration,
      animationCurve: animationCurve ?? this.animationCurve,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      enableSounds: enableSounds ?? this.enableSounds,
      iconSize: iconSize ?? this.iconSize,
      buttonHeight: buttonHeight ?? this.buttonHeight,
      inputHeight: inputHeight ?? this.inputHeight,
      screenPadding: screenPadding ?? this.screenPadding,
      cardPadding: cardPadding ?? this.cardPadding,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? DateTime.now(),
    );
  }

  // Helper methods for JSON serialization
  static BorderRadius _borderRadiusFromJson(Map<String, dynamic>? json) {
    if (json == null) return const BorderRadius.all(Radius.circular(12.0));
    return BorderRadius.circular((json['radius'] as num?)?.toDouble() ?? 12.0);
  }

  static Map<String, dynamic> _borderRadiusToJson(BorderRadius radius) {
    return {'radius': 12.0}; // Simplified for now
  }

  static EdgeInsets _edgeInsetsFromJson(Map<String, dynamic>? json) {
    if (json == null) return const EdgeInsets.all(16.0);
    return EdgeInsets.only(
      top: (json['top'] as num?)?.toDouble() ?? 16.0,
      right: (json['right'] as num?)?.toDouble() ?? 16.0,
      bottom: (json['bottom'] as num?)?.toDouble() ?? 16.0,
      left: (json['left'] as num?)?.toDouble() ?? 16.0,
    );
  }

  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsets insets) {
    return {
      'top': insets.top,
      'right': insets.right,
      'bottom': insets.bottom,
      'left': insets.left,
    };
  }

  static Curve _curveFromString(String curve) {
    switch (curve) {
      case 'linear': return Curves.linear;
      case 'easeIn': return Curves.easeIn;
      case 'easeOut': return Curves.easeOut;
      case 'easeInOut': return Curves.easeInOut;
      case 'bounceIn': return Curves.bounceIn;
      case 'bounceOut': return Curves.bounceOut;
      case 'elasticIn': return Curves.elasticIn;
      case 'elasticOut': return Curves.elasticOut;
      default: return Curves.easeInOut;
    }
  }

  static String _curveToString(Curve curve) {
    if (curve == Curves.linear) return 'linear';
    if (curve == Curves.easeIn) return 'easeIn';
    if (curve == Curves.easeOut) return 'easeOut';
    if (curve == Curves.easeInOut) return 'easeInOut';
    if (curve == Curves.bounceIn) return 'bounceIn';
    if (curve == Curves.bounceOut) return 'bounceOut';
    if (curve == Curves.elasticIn) return 'elasticIn';
    if (curve == Curves.elasticOut) return 'elasticOut';
    return 'easeInOut';
  }
}

enum LayoutType {
  adaptive,
  mobile,
  tablet,
  desktop,
  custom,
}

enum NavigationType {
  rail,
  drawer,
  bottom,
  tabs,
  hybrid,
}

enum SidebarPosition {
  left,
  right,
  top,
  bottom,
}

enum HeaderStyle {
  minimal,
  standard,
  elevated,
  prominent,
  custom,
}

enum FooterStyle {
  none,
  minimal,
  standard,
  detailed,
  custom,
}
