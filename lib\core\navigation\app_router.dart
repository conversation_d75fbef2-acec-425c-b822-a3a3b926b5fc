import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/memo/screens/memo_dashboard_screen.dart';
import '../../features/memo/screens/note_editor_screen.dart';
import '../../features/memo/screens/todo_editor_screen.dart';
import '../../features/memo/screens/voice_memo_screen.dart';
import '../../features/memo/screens/memo_settings_screen.dart';
// Notes functionality merged into Memo Suite
import '../../features/islami/screens/islami_screen.dart';

import '../../features/tools/screens/tool_builder_main_screen.dart';
import '../../features/tools/screens/create_tool_screen.dart';
import '../../features/tools/screens/backend_formula_screen.dart';
import '../../features/tools/screens/ui_builder_screen.dart';
import '../../features/tools/screens/tool_preview_screen.dart';
import '../../features/tools/screens/my_tools_screen.dart';
import '../../features/tools/screens/import_excel_screen.dart';
import '../../features/tools/screens/tool_list_screen.dart';

import '../../features/tools/screens/live_excel_viewer_screen.dart';
import '../../features/tools/screens/template_gallery_screen.dart';
import '../../features/tools/screens/tools_settings_screen.dart';
import '../../features/money_manager/screens/money_manager_screen.dart';
import '../../features/money/screens/account_details_screen.dart';
import '../../features/money/screens/reports_screen.dart';
import '../../features/money/screens/charts_screen.dart';
import '../../features/money/screens/category_management_screen.dart';
import '../../features/money/screens/money_settings_screen.dart';
import '../../features/money/models/account.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../shared/screens/profile_setup_screen.dart';

class AppRoutes {
  static const String dashboard = '/';
  static const String memo = '/memo';
  static const String noteEditor = '/memo/note-editor';
  static const String todoEditor = '/memo/todo-editor';
  static const String voiceMemo = '/memo/voice-memo';
  // Notes functionality merged into Memo Suite
  static const String islami = '/islami'; // Islamic features consolidated
  static const String quran = '/islami/quran';
  static const String dhikr = '/islami/dhikr';
  // Removed separate quran route - now part of athkar-pro-quran
  static const String tools = '/tools';
  static const String money = '/money';
  static const String settings = '/settings';
  static const String profileSetup = '/profile-setup';
}

final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.dashboard,
  routes: [
    GoRoute(
      path: AppRoutes.dashboard,
      name: 'dashboard',
      builder: (context, state) => const DashboardScreen(),
    ),
    GoRoute(
      path: AppRoutes.profileSetup,
      name: 'profile-setup',
      builder: (context, state) => const ProfileSetupScreen(),
    ),
    GoRoute(
      path: AppRoutes.settings,
      name: 'settings',
      builder: (context, state) => const SettingsScreen(),
    ),
    GoRoute(
      path: AppRoutes.memo,
      name: 'memo',
      builder: (context, state) => const MemoDashboardScreen(),
      routes: [
        GoRoute(
          path: '/note-editor',
          name: 'note-editor',
          builder: (context, state) {
            final noteId = state.uri.queryParameters['id'];
            return NoteEditorScreen(noteId: noteId != null ? int.tryParse(noteId) : null);
          },
        ),
        GoRoute(
          path: '/todo-editor',
          name: 'todo-editor',
          builder: (context, state) {
            final todoId = state.uri.queryParameters['id'];
            return TodoEditorScreen(todoId: todoId != null ? int.tryParse(todoId) : null);
          },
        ),
        GoRoute(
          path: '/voice-memo',
          name: 'voice-memo',
          builder: (context, state) => const VoiceMemoScreen(),
        ),
        GoRoute(
          path: '/settings',
          name: 'memo-settings',
          builder: (context, state) => const MemoSettingsScreen(),
        ),
      ],
    ),
    // Notes functionality merged into Memo Suite - redirect to memo
    GoRoute(
      path: '/notes',
      name: 'notes',
      redirect: (context, state) => AppRoutes.memo,
    ),
    // Islami consolidated route (includes Dhikr, Quran, Prayer Times)
    GoRoute(
      path: AppRoutes.islami,
      name: 'islami',
      builder: (context, state) {
        return const IslamiScreen();
      },
    ),
    GoRoute(
      path: AppRoutes.tools,
      name: 'tools',
      builder: (context, state) => const ToolBuilderMainScreen(),
      routes: [
        // Tool Builder routes
        GoRoute(
          path: '/builder-home',
          name: 'tool-builder-home',
          builder: (context, state) => const ToolBuilderMainScreen(),
        ),
        GoRoute(
          path: '/create',
          name: 'create-tool',
          builder: (context, state) => const CreateToolScreen(),
        ),
        GoRoute(
          path: '/backend/:id',
          name: 'backend-formula',
          builder: (context, state) {
            final toolId = int.parse(state.pathParameters['id']!);
            return BackendFormulaScreen(toolId: toolId);
          },
        ),
        GoRoute(
          path: '/ui-builder/:id',
          name: 'ui-builder',
          builder: (context, state) {
            final toolId = int.parse(state.pathParameters['id']!);
            return UIBuilderScreen(toolId: toolId);
          },
        ),
        GoRoute(
          path: '/preview/:id',
          name: 'tool-preview',
          builder: (context, state) {
            final toolId = int.parse(state.pathParameters['id']!);
            return ToolPreviewScreen(toolId: toolId);
          },
        ),
        GoRoute(
          path: '/my-tools',
          name: 'my-tools',
          builder: (context, state) => const MyToolsScreen(),
        ),
        GoRoute(
          path: '/list',
          name: 'tool-list',
          builder: (context, state) => const ToolListScreen(),
        ),
        GoRoute(
          path: '/import-excel',
          name: 'import-excel',
          builder: (context, state) => const ImportExcelScreen(),
        ),
        GoRoute(
          path: '/excel-viewer',
          name: 'excel-viewer',
          builder: (context, state) => const LiveExcelViewerScreen(),
        ),
        GoRoute(
          path: '/templates',
          name: 'templates',
          builder: (context, state) => const TemplateGalleryScreen(),
        ),
        GoRoute(
          path: '/settings',
          name: 'tools-settings',
          builder: (context, state) => const ToolsSettingsScreen(),
        ),
      ],
    ),
    GoRoute(
      path: AppRoutes.money,
      name: 'money',
      builder: (context, state) => const MoneyManagerScreen(),
      routes: [
        GoRoute(
          path: '/account/:id',
          name: 'account-details',
          builder: (context, state) {
            final accountId = state.pathParameters['id']!;
            // For now, create a placeholder account - this should be fetched from provider
            final account = Account()
              ..id = int.tryParse(accountId) ?? 0
              ..name = 'Account Details'
              ..type = AccountType.checking;
            return AccountDetailsScreen(account: account);
          },
        ),
        GoRoute(
          path: '/reports',
          name: 'money-reports',
          builder: (context, state) => const ReportsScreen(),
        ),
        GoRoute(
          path: '/charts',
          name: 'money-charts',
          builder: (context, state) => const ChartsScreen(),
        ),
        GoRoute(
          path: '/categories',
          name: 'money-categories',
          builder: (context, state) => const CategoryManagementScreen(),
        ),
        GoRoute(
          path: '/settings',
          name: 'money-settings',
          builder: (context, state) => const MoneySettingsScreen(),
        ),
      ],
    ),
  ],
  errorBuilder: (context, state) => Scaffold(
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Page not found',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'The page you are looking for does not exist.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.go(AppRoutes.dashboard),
            child: const Text('Go to Dashboard'),
          ),
        ],
      ),
    ),
  ),
);
