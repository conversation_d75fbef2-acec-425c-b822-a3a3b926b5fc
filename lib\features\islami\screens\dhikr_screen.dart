import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr_models.dart';
import '../services/quran_service.dart';
import 'dhikr_counter_screen.dart';

/// Dhikr main screen with categories and dhikr list
class DhikrScreen extends ConsumerStatefulWidget {
  const DhikrScreen({super.key});

  @override
  ConsumerState<DhikrScreen> createState() => _DhikrScreenState();
}

class _DhikrScreenState extends ConsumerState<DhikrScreen> {
  final DhikrService _dhikrService = DhikrService();
  List<DhikrCategory> _categories = [];
  List<Dhikr> _dhikrs = [];
  String _selectedCategoryId = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeDhikr();
  }

  Future<void> _initializeDhikr() async {
    try {
      await _dhikrService.initialize();
      setState(() {
        _categories = _dhikrService.getCategories();
        _selectedCategoryId = _categories.isNotEmpty ? _categories.first.id : '';
        _dhikrs = _selectedCategoryId.isNotEmpty 
            ? _dhikrService.getDhikrsByCategory(_selectedCategoryId)
            : [];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading Dhikr: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading Dhikr...'),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Statistics and active session
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Statistics cards
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Today\'s Count',
                      '47',
                      Icons.today,
                      Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'Streak',
                      '12 days',
                      Icons.local_fire_department,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'Total',
                      '2,847',
                      Icons.star,
                      Colors.amber,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Active session card
              _buildActiveSessionCard(),
            ],
          ),
        ),

        // Category tabs
        if (_categories.isNotEmpty) ...[
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category.id == _selectedCategoryId;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category.name),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedCategoryId = category.id;
                          _dhikrs = _dhikrService.getDhikrsByCategory(category.id);
                        });
                      }
                    },
                    backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                    selectedColor: Theme.of(context).colorScheme.primaryContainer,
                    checkmarkColor: Theme.of(context).colorScheme.primary,
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Dhikr list
        Expanded(
          child: _dhikrs.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _dhikrs.length,
                  itemBuilder: (context, index) {
                    final dhikr = _dhikrs[index];
                    return _buildDhikrTile(dhikr);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveSessionCard() {
    final activeSession = _dhikrService.getActiveSession();
    
    if (activeSession == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.play_circle_outline,
                color: Theme.of(context).colorScheme.primary,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Start a Dhikr Session',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'Choose a dhikr below to begin',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Find the dhikr for the active session
    final dhikr = _dhikrService.getAllDhikrs()
        .where((d) => d.id == activeSession.dhikrId)
        .firstOrNull;

    return Card(
      color: Theme.of(context).colorScheme.primaryContainer,
      child: InkWell(
        onTap: () => _resumeSession(activeSession),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.play_circle_fill,
                    color: Theme.of(context).colorScheme.primary,
                    size: 32,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Active Session',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        if (dhikr != null)
                          Text(
                            dhikr.textTransliteration,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                      ],
                    ),
                  ),
                  Text(
                    '${activeSession.currentCount}/${activeSession.targetCount}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: activeSession.progressPercentage / 100,
                backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDhikrTile(Dhikr dhikr) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              dhikr.textArabic,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                height: 1.6,
              ),
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              dhikr.textTransliteration,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              dhikr.textTranslation,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 12),
          child: Row(
            children: [
              if (dhikr.recommendedCount > 1) ...[
                Icon(
                  Icons.repeat,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  '${dhikr.recommendedCount}x',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(width: 16),
              ],
              if (dhikr.source.isNotEmpty) ...[
                Icon(
                  Icons.book,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  dhikr.source,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
              const Spacer(),
              IconButton(
                icon: Icon(
                  dhikr.isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: dhikr.isFavorite ? Colors.red : null,
                ),
                onPressed: () => _toggleFavorite(dhikr),
              ),
            ],
          ),
        ),
        onTap: () => _startDhikrSession(dhikr),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No Dhikr Available',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Select a different category or check back later',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _startDhikrSession(Dhikr dhikr) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DhikrCounterScreen(dhikr: dhikr),
      ),
    );
  }

  void _resumeSession(DhikrSession session) {
    final dhikr = _dhikrService.getAllDhikrs()
        .where((d) => d.id == session.dhikrId)
        .firstOrNull;
    
    if (dhikr != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => DhikrCounterScreen(
            dhikr: dhikr,
            existingSession: session,
          ),
        ),
      );
    }
  }

  void _toggleFavorite(Dhikr dhikr) {
    _dhikrService.toggleFavorite(dhikr.id);
    setState(() {
      _dhikrs = _dhikrService.getDhikrsByCategory(_selectedCategoryId);
    });
  }
}
