import 'dart:async';
import 'package:flutter/foundation.dart';
import '../storage/persistent_storage_service.dart';
import '../../shared/models/user_profile.dart';
import '../../features/memo/models/note.dart';
import '../../features/memo/models/todo.dart';
import '../../features/memo/models/voice_memo.dart';
import '../../features/athkar/models/dhikr.dart';
import '../../features/athkar/models/dhikr_session.dart';
import '../../features/athkar/models/prayer_time.dart';
import '../../features/athkar/models/custom_dhikr_routine.dart';

import '../../features/tools/models/custom_tool.dart';
import '../../features/tools/models/calculator.dart';
import '../../features/tools/models/conversion.dart';
import '../../features/tools/models/calculation_history.dart';
import '../../features/money/models/account.dart';
import '../../features/money/models/transaction.dart';
import '../../features/money/models/category.dart';
import '../../features/money/models/budget.dart';
import '../../features/money/models/financial_goal.dart';

class DatabaseService {
  static DatabaseService? _instance;
  static DatabaseService get instance => _instance ??= DatabaseService._();

  DatabaseService._();

  final PersistentStorageService _storage = PersistentStorageService();
  bool _isInitialized = false;

  // In-memory storage with persistence
  UserProfile? _userProfile;
  final List<Note> _notes = [];
  final List<Todo> _todos = [];
  final List<VoiceMemo> _voiceMemos = [];
  final List<Dhikr> _dhikrs = [];
  final List<DhikrSession> _dhikrSessions = [];
  final List<PrayerTime> _prayerTimes = [];
  final List<CustomDhikrRoutine> _customDhikrRoutines = [];
  // Legacy Quran data - now handled by QuranService
  final List<CustomTool> _customTools = [];
  final List<Calculator> _calculators = [];
  final List<Conversion> _conversions = [];
  final List<CalculationHistory> _calculationHistory = [];
  final List<Account> _accounts = [];
  final List<Transaction> _transactions = [];
  final List<MoneyCategory> _categories = [];
  final List<Budget> _budgets = [];
  final List<FinancialGoal> _financialGoals = [];

  int _nextId = 1;

  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _storage.initialize();
      await _loadAllData();
      _isInitialized = true;
      debugPrint('DatabaseService initialized with persistent storage');

      // Create default user profile if none exists
      await _ensureDefaultUserProfile();

      // Set up auto-save timer to persist data every 30 seconds
      _setupAutoSave();

    } catch (e) {
      debugPrint('Error initializing DatabaseService: $e');
      rethrow;
    }
  }

  Timer? _autoSaveTimer;

  void _setupAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isInitialized) {
        _saveAllData();
      }
    });
  }

  Future<void> close() async {
    if (_isInitialized) {
      _autoSaveTimer?.cancel();
      await _saveAllData();
      _isInitialized = false;
    }
  }

  Future<void> _loadAllData() async {
    try {
      // Load user profile
      final profileData = await _storage.loadUserProfile();
      if (profileData != null) {
        _userProfile = UserProfile.fromJson(profileData);
      }

      // Load notes
      final notesData = await _storage.loadNotes();
      _notes.clear();
      for (final noteData in notesData) {
        _notes.add(Note.fromJson(noteData));
      }

      // Load todos
      final todosData = await _storage.loadTodos();
      _todos.clear();
      for (final todoData in todosData) {
        _todos.add(Todo.fromJson(todoData));
      }

      // Load voice memos
      final voiceMemosData = await _storage.loadVoiceMemos();
      _voiceMemos.clear();
      for (final memoData in voiceMemosData) {
        _voiceMemos.add(VoiceMemo.fromJson(memoData));
      }

      // Load dhikr sessions
      final dhikrSessionsData = await _storage.loadDhikrSessions();
      _dhikrSessions.clear();
      for (final sessionData in dhikrSessionsData) {
        _dhikrSessions.add(DhikrSession.fromJson(sessionData));
      }

      // Load custom dhikr routines
      final dhikrRoutinesData = await _storage.loadDhikrRoutines();
      _customDhikrRoutines.clear();
      for (final routineData in dhikrRoutinesData) {
        _customDhikrRoutines.add(CustomDhikrRoutine.fromJson(routineData));
      }

      // Load custom tools
      final customToolsData = await _storage.loadCustomTools();
      _customTools.clear();
      for (final toolData in customToolsData) {
        _customTools.add(CustomTool.fromJson(toolData));
      }

      // Load calculation history
      final calculationHistoryData = await _storage.loadCalculationHistory();
      _calculationHistory.clear();
      for (final historyData in calculationHistoryData) {
        _calculationHistory.add(CalculationHistory.fromJson(historyData));
      }

      // Load accounts
      final accountsData = await _storage.loadAccounts();
      _accounts.clear();
      for (final accountData in accountsData) {
        _accounts.add(Account.fromJson(accountData));
      }

      // Load transactions
      final transactionsData = await _storage.loadTransactions();
      _transactions.clear();
      for (final transactionData in transactionsData) {
        _transactions.add(Transaction.fromJson(transactionData));
      }

      // Load categories
      final categoriesData = await _storage.loadCategories();
      _categories.clear();
      for (final categoryData in categoriesData) {
        _categories.add(MoneyCategory.fromJson(categoryData));
      }

      // Load budgets
      final budgetsData = await _storage.loadBudgets();
      _budgets.clear();
      for (final budgetData in budgetsData) {
        _budgets.add(Budget.fromJson(budgetData));
      }

      // Load financial goals
      final financialGoalsData = await _storage.loadFinancialGoals();
      _financialGoals.clear();
      for (final goalData in financialGoalsData) {
        _financialGoals.add(FinancialGoal.fromJson(goalData));
      }

      // Update next ID based on loaded data
      _updateNextId();

      debugPrint('Loaded ${_notes.length} notes, ${_todos.length} todos, ${_voiceMemos.length} voice memos, ${_accounts.length} accounts, ${_transactions.length} transactions, ${_customTools.length} custom tools, ${_dhikrSessions.length} dhikr sessions');
    } catch (e) {
      debugPrint('Error loading data: $e');
    }
  }

  Future<void> _saveAllData() async {
    try {
      // Save user profile
      if (_userProfile != null) {
        await _storage.saveUserProfile(_userProfile!.toJson());
      }

      // Save notes
      final notesData = _notes.map((note) => note.toJson()).toList();
      await _storage.saveNotes(notesData);

      // Save todos
      final todosData = _todos.map((todo) => todo.toJson()).toList();
      await _storage.saveTodos(todosData);

      // Save voice memos
      final voiceMemosData = _voiceMemos.map((memo) => memo.toJson()).toList();
      await _storage.saveVoiceMemos(voiceMemosData);

      // Save dhikr sessions
      final dhikrSessionsData = _dhikrSessions.map((session) => session.toJson()).toList();
      await _storage.saveDhikrSessions(dhikrSessionsData);

      // Save custom dhikr routines
      final dhikrRoutinesData = _customDhikrRoutines.map((routine) => routine.toJson()).toList();
      await _storage.saveDhikrRoutines(dhikrRoutinesData);

      // Save custom tools
      final customToolsData = _customTools.map((tool) => tool.toJson()).toList();
      await _storage.saveCustomTools(customToolsData);

      // Save calculation history
      final calculationHistoryData = _calculationHistory.map((history) => history.toJson()).toList();
      await _storage.saveCalculationHistory(calculationHistoryData);

      // Save accounts
      final accountsData = _accounts.map((account) => account.toJson()).toList();
      await _storage.saveAccounts(accountsData);

      // Save transactions
      final transactionsData = _transactions.map((transaction) => transaction.toJson()).toList();
      await _storage.saveTransactions(transactionsData);

      // Save categories
      final categoriesData = _categories.map((category) => category.toJson()).toList();
      await _storage.saveCategories(categoriesData);

      // Save budgets
      final budgetsData = _budgets.map((budget) => budget.toJson()).toList();
      await _storage.saveBudgets(budgetsData);

      // Save financial goals
      final financialGoalsData = _financialGoals.map((goal) => goal.toJson()).toList();
      await _storage.saveFinancialGoals(financialGoalsData);

      debugPrint('Saved all data to persistent storage: ${_notes.length} notes, ${_accounts.length} accounts, ${_customTools.length} tools, ${_dhikrSessions.length} sessions');
    } catch (e) {
      debugPrint('Error saving data: $e');
    }
  }

  void _updateNextId() {
    int maxId = 0;

    for (final note in _notes) {
      if (note.id > maxId) maxId = note.id;
    }
    for (final todo in _todos) {
      if (todo.id > maxId) maxId = todo.id;
    }
    for (final memo in _voiceMemos) {
      if (memo.id > maxId) maxId = memo.id;
    }
    for (final session in _dhikrSessions) {
      if (session.id > maxId) maxId = session.id;
    }
    for (final routine in _customDhikrRoutines) {
      if (routine.id > maxId) maxId = routine.id;
    }
    for (final tool in _customTools) {
      final toolIdInt = int.tryParse(tool.id) ?? 0;
      if (toolIdInt > maxId) maxId = toolIdInt;
    }
    for (final history in _calculationHistory) {
      if (history.id > maxId) maxId = history.id;
    }
    for (final account in _accounts) {
      if (account.id > maxId) maxId = account.id;
    }
    for (final transaction in _transactions) {
      if (transaction.id > maxId) maxId = transaction.id;
    }
    for (final budget in _budgets) {
      if (budget.id > maxId) maxId = budget.id;
    }
    for (final goal in _financialGoals) {
      if (goal.id > maxId) maxId = goal.id;
    }

    _nextId = maxId + 1;
  }

  Future<void> _ensureDefaultUserProfile() async {
    if (_userProfile == null) {
      final defaultProfile = UserProfile.create(
        name: 'User',
        email: null,
      );
      defaultProfile.id = _nextId++;
      _userProfile = defaultProfile;
      await _saveAllData();
      debugPrint('Created default user profile');
    }
  }

  // User Profile operations
  Future<UserProfile?> getUserProfile() async {
    return _userProfile;
  }

  Future<UserProfile> createUserProfile({
    required String name,
    String? email,
  }) async {
    final profile = UserProfile.create(
      name: name,
      email: email,
    );
    profile.id = _nextId++;
    _userProfile = profile;
    await _saveUserProfileToStorage();
    return profile;
  }

  Future<void> updateUserProfile(UserProfile profile) async {
    profile.updateTimestamp();
    _userProfile = profile;
    await _saveUserProfileToStorage();
  }

  Future<void> _saveUserProfileToStorage() async {
    try {
      if (_userProfile != null) {
        await _storage.saveUserProfile(_userProfile!.toJson());
      }
    } catch (e) {
      debugPrint('Error saving user profile to storage: $e');
    }
  }

  // Note operations
  Future<List<Note>> getAllNotes({String? userId}) async {
    return _notes.where((note) {
      if (note.isDeleted) return false;
      if (userId != null && note.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<List<Note>> searchNotes(String searchTerm, {String? userId}) async {
    final allNotes = await getAllNotes(userId: userId);
    return allNotes.where((note) => note.containsSearchTerm(searchTerm)).toList();
  }

  Future<List<Note>> getNotesByTag(String tag, {String? userId}) async {
    final allNotes = await getAllNotes(userId: userId);
    return allNotes.where((note) => note.tags.contains(tag)).toList();
  }

  Future<List<Note>> getFavoriteNotes({String? userId}) async {
    return _notes.where((note) {
      if (note.isDeleted || !note.isFavorite) return false;
      if (userId != null && note.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<Note> saveNote(Note note) async {
    note.updateTimestamp();
    if (note.id == 0) {
      note.id = _nextId++;
      _notes.add(note);
    } else {
      final index = _notes.indexWhere((n) => n.id == note.id);
      if (index != -1) {
        _notes[index] = note;
      }
    }

    // Persist to storage
    await _saveNotesToStorage();
    return note;
  }

  Future<void> _saveNotesToStorage() async {
    try {
      final notesData = _notes.map((note) => note.toJson()).toList();
      await _storage.saveNotes(notesData);
    } catch (e) {
      debugPrint('Error saving notes to storage: $e');
    }
  }

  Future<void> deleteNote(int noteId) async {
    final note = _notes.firstWhere((n) => n.id == noteId, orElse: () => Note());
    if (note.id != 0) {
      note.isDeleted = true;
      note.updateTimestamp();
      await _saveNotesToStorage();
    }
  }

  Future<void> permanentlyDeleteNote(int noteId) async {
    _notes.removeWhere((note) => note.id == noteId);
  }

  // Todo operations
  Future<List<Todo>> getAllTodos({String? userId}) async {
    return _todos.where((todo) {
      if (todo.isDeleted) return false;
      if (userId != null && todo.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<List<Todo>> getActiveTodos({String? userId}) async {
    return _todos.where((todo) {
      if (todo.isDeleted || todo.isCompleted) return false;
      if (userId != null && todo.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) {
      if (a.dueDate == null && b.dueDate == null) return 0;
      if (a.dueDate == null) return 1;
      if (b.dueDate == null) return -1;
      return b.dueDate!.compareTo(a.dueDate!);
    });
  }

  Future<List<Todo>> getCompletedTodos({String? userId}) async {
    return _todos.where((todo) {
      if (todo.isDeleted || !todo.isCompleted) return false;
      if (userId != null && todo.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<List<Todo>> getOverdueTodos({String? userId}) async {
    final activeTodos = await getActiveTodos(userId: userId);
    return activeTodos.where((todo) => todo.isOverdue).toList();
  }

  Future<List<Todo>> getTodayTodos({String? userId}) async {
    final activeTodos = await getActiveTodos(userId: userId);
    return activeTodos.where((todo) => todo.isDueToday).toList();
  }

  Future<Todo> saveTodo(Todo todo) async {
    todo.updateTimestamp();
    if (todo.id == 0) {
      todo.id = _nextId++;
      _todos.add(todo);
    } else {
      final index = _todos.indexWhere((t) => t.id == todo.id);
      if (index != -1) {
        _todos[index] = todo;
      }
    }

    // Persist to storage
    await _saveTodosToStorage();
    return todo;
  }

  Future<void> _saveTodosToStorage() async {
    try {
      final todosData = _todos.map((todo) => todo.toJson()).toList();
      await _storage.saveTodos(todosData);
    } catch (e) {
      debugPrint('Error saving todos to storage: $e');
    }
  }

  Future<void> deleteTodo(int todoId) async {
    final todo = _todos.firstWhere((t) => t.id == todoId, orElse: () => Todo());
    if (todo.id != 0) {
      todo.isDeleted = true;
      todo.updateTimestamp();
      await _saveTodosToStorage();
    }
  }

  // Voice Memo operations
  Future<List<VoiceMemo>> getAllVoiceMemos({String? userId}) async {
    return _voiceMemos.where((memo) {
      if (memo.isDeleted) return false;
      if (userId != null && memo.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<VoiceMemo> saveVoiceMemo(VoiceMemo memo) async {
    memo.updateTimestamp();
    if (memo.id == 0) {
      memo.id = _nextId++;
      _voiceMemos.add(memo);
    } else {
      final index = _voiceMemos.indexWhere((m) => m.id == memo.id);
      if (index != -1) {
        _voiceMemos[index] = memo;
      }
    }

    // Persist to storage
    await _saveVoiceMemosToStorage();
    return memo;
  }

  Future<void> _saveVoiceMemosToStorage() async {
    try {
      final voiceMemosData = _voiceMemos.map((memo) => memo.toJson()).toList();
      await _storage.saveVoiceMemos(voiceMemosData);
    } catch (e) {
      debugPrint('Error saving voice memos to storage: $e');
    }
  }

  Future<void> deleteVoiceMemo(int memoId) async {
    final memo = _voiceMemos.firstWhere((m) => m.id == memoId, orElse: () => VoiceMemo());
    if (memo.id != 0) {
      memo.isDeleted = true;
      memo.updateTimestamp();
      await _saveVoiceMemosToStorage();
    }
  }

  // Utility methods
  Future<List<String>> getAllTags({String? userId}) async {
    final notes = await getAllNotes(userId: userId);
    final todos = await getAllTodos(userId: userId);
    final memos = await getAllVoiceMemos(userId: userId);

    final allTags = <String>{};
    for (final note in notes) {
      allTags.addAll(note.tags);
    }
    for (final todo in todos) {
      allTags.addAll(todo.tags);
    }
    for (final memo in memos) {
      allTags.addAll(memo.tags);
    }

    return allTags.toList()..sort();
  }

  // Dhikr operations
  Future<List<Dhikr>> getAllDhikrs({String? userId}) async {
    return _dhikrs.where((dhikr) {
      if (dhikr.isDeleted) return false;
      if (userId != null && dhikr.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.category.compareTo(b.category));
  }

  Future<List<Dhikr>> getDhikrsByCategory(String category, {String? userId}) async {
    return _dhikrs.where((dhikr) {
      if (dhikr.isDeleted || dhikr.category != category) return false;
      if (userId != null && dhikr.userId != userId) return false;
      return true;
    }).toList();
  }

  Future<Dhikr> saveDhikr(Dhikr dhikr) async {
    dhikr.updateTimestamp();
    if (dhikr.id == 0) {
      dhikr.id = _nextId++;
      _dhikrs.add(dhikr);
    } else {
      final index = _dhikrs.indexWhere((d) => d.id == dhikr.id);
      if (index != -1) {
        _dhikrs[index] = dhikr;
      }
    }
    return dhikr;
  }

  Future<void> deleteDhikr(int dhikrId) async {
    final dhikr = _dhikrs.firstWhere((d) => d.id == dhikrId, orElse: () => Dhikr());
    if (dhikr.id != 0) {
      dhikr.softDelete();
    }
  }

  // Dhikr Session operations
  Future<List<DhikrSession>> getAllDhikrSessions({String? userId}) async {
    return _dhikrSessions.where((session) {
      if (session.isDeleted) return false;
      if (userId != null && session.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.startTime.compareTo(a.startTime));
  }

  Future<List<DhikrSession>> getActiveDhikrSessions({String? userId}) async {
    return _dhikrSessions.where((session) {
      if (session.isDeleted || session.isCompleted) return false;
      if (userId != null && session.userId != userId) return false;
      return true;
    }).toList();
  }

  Future<List<DhikrSession>> getCompletedDhikrSessions({String? userId}) async {
    return _dhikrSessions.where((session) {
      if (session.isDeleted || !session.isCompleted) return false;
      if (userId != null && session.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.endTime!.compareTo(a.endTime!));
  }

  Future<DhikrSession> saveDhikrSession(DhikrSession session) async {
    session.updateTimestamp();
    if (session.id == 0) {
      session.id = _nextId++;
      _dhikrSessions.add(session);
    } else {
      final index = _dhikrSessions.indexWhere((s) => s.id == session.id);
      if (index != -1) {
        _dhikrSessions[index] = session;
      }
    }

    // Persist to storage
    await _saveDhikrSessionsToStorage();
    return session;
  }

  Future<void> _saveDhikrSessionsToStorage() async {
    try {
      final sessionsData = _dhikrSessions.map((session) => session.toJson()).toList();
      await _storage.saveDhikrSessions(sessionsData);
    } catch (e) {
      debugPrint('Error saving dhikr sessions to storage: $e');
    }
  }

  Future<void> deleteDhikrSession(int sessionId) async {
    final session = _dhikrSessions.firstWhere((s) => s.id == sessionId, orElse: () => DhikrSession());
    if (session.id != 0) {
      session.softDelete();
    }
  }

  // Prayer Time operations
  Future<List<PrayerTime>> getAllPrayerTimes({String? userId}) async {
    return _prayerTimes.where((prayer) {
      if (prayer.isDeleted) return false;
      if (userId != null && prayer.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.prayerTime.compareTo(b.prayerTime));
  }

  Future<List<PrayerTime>> getTodayPrayerTimes({String? userId}) async {
    final today = DateTime.now();
    return _prayerTimes.where((prayer) {
      if (prayer.isDeleted) return false;
      if (userId != null && prayer.userId != userId) return false;
      return prayer.prayerTime.year == today.year &&
             prayer.prayerTime.month == today.month &&
             prayer.prayerTime.day == today.day;
    }).toList()..sort((a, b) => a.prayerTime.compareTo(b.prayerTime));
  }

  Future<PrayerTime> savePrayerTime(PrayerTime prayerTime) async {
    prayerTime.updateTimestamp();
    if (prayerTime.id == 0) {
      prayerTime.id = _nextId++;
      _prayerTimes.add(prayerTime);
    } else {
      final index = _prayerTimes.indexWhere((p) => p.id == prayerTime.id);
      if (index != -1) {
        _prayerTimes[index] = prayerTime;
      }
    }
    return prayerTime;
  }

  Future<void> deletePrayerTime(int prayerTimeId) async {
    final prayerTime = _prayerTimes.firstWhere((p) => p.id == prayerTimeId, orElse: () => PrayerTime());
    if (prayerTime.id != 0) {
      prayerTime.softDelete();
    }
  }

  // Calculator operations
  Future<List<Calculator>> getAllCalculators({String? userId}) async {
    return _calculators.where((calc) {
      if (calc.isDeleted) return false;
      if (userId != null && calc.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<List<Calculator>> getFavoriteCalculators({String? userId}) async {
    return _calculators.where((calc) {
      if (calc.isDeleted || !calc.isFavorite) return false;
      if (userId != null && calc.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.usageCount.compareTo(a.usageCount));
  }

  Future<Calculator> saveCalculator(Calculator calculator) async {
    calculator.updateTimestamp();
    if (calculator.id == 0) {
      calculator.id = _nextId++;
      _calculators.add(calculator);
    } else {
      final index = _calculators.indexWhere((c) => c.id == calculator.id);
      if (index != -1) {
        _calculators[index] = calculator;
      }
    }
    return calculator;
  }

  Future<void> deleteCalculator(int calculatorId) async {
    final calculator = _calculators.firstWhere((c) => c.id == calculatorId, orElse: () => Calculator());
    if (calculator.id != 0) {
      calculator.softDelete();
    }
  }

  // Conversion operations
  Future<List<Conversion>> getAllConversions({String? userId}) async {
    return _conversions.where((conv) {
      if (conv.isDeleted) return false;
      if (userId != null && conv.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<List<Conversion>> getConversionsByCategory(ConversionCategory category, {String? userId}) async {
    return _conversions.where((conv) {
      if (conv.isDeleted || conv.category != category) return false;
      if (userId != null && conv.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<Conversion> saveConversion(Conversion conversion) async {
    conversion.updateTimestamp();
    if (conversion.id == 0) {
      conversion.id = _nextId++;
      _conversions.add(conversion);
    } else {
      final index = _conversions.indexWhere((c) => c.id == conversion.id);
      if (index != -1) {
        _conversions[index] = conversion;
      }
    }
    return conversion;
  }

  Future<void> deleteConversion(int conversionId) async {
    final conversion = _conversions.firstWhere((c) => c.id == conversionId, orElse: () => Conversion());
    if (conversion.id != 0) {
      conversion.softDelete();
    }
  }

  // Calculation History operations
  Future<List<CalculationHistory>> getAllCalculationHistory({String? userId}) async {
    return _calculationHistory.where((hist) {
      if (hist.isDeleted) return false;
      if (userId != null && hist.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<List<CalculationHistory>> getCalculationHistoryByType(CalculationType type, {String? userId}) async {
    return _calculationHistory.where((hist) {
      if (hist.isDeleted || hist.type != type) return false;
      if (userId != null && hist.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<CalculationHistory> saveCalculationHistory(CalculationHistory history) async {
    history.updateTimestamp();
    if (history.id == 0) {
      history.id = _nextId++;
      _calculationHistory.add(history);
    } else {
      final index = _calculationHistory.indexWhere((h) => h.id == history.id);
      if (index != -1) {
        _calculationHistory[index] = history;
      }
    }

    // Persist to storage
    await _saveCalculationHistoryToStorage();
    return history;
  }

  Future<void> _saveCalculationHistoryToStorage() async {
    try {
      final historyData = _calculationHistory.map((history) => history.toJson()).toList();
      await _storage.saveCalculationHistory(historyData);
    } catch (e) {
      debugPrint('Error saving calculation history to storage: $e');
    }
  }

  Future<void> deleteCalculationHistory(int historyId) async {
    final history = _calculationHistory.firstWhere((h) => h.id == historyId, orElse: () => CalculationHistory());
    if (history.id != 0) {
      history.softDelete();
    }
  }

  Future<void> clearCalculationHistory({String? userId}) async {
    if (userId != null) {
      _calculationHistory.removeWhere((hist) => hist.userId == userId);
    } else {
      _calculationHistory.clear();
    }
  }

  // Account operations
  Future<List<Account>> getAllAccounts({String? userId}) async {
    return _accounts.where((account) {
      if (account.isDeleted) return false;
      if (userId != null && account.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<List<Account>> getActiveAccounts({String? userId}) async {
    return _accounts.where((account) {
      if (account.isDeleted || !account.isActive) return false;
      if (userId != null && account.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<Account> saveAccount(Account account) async {
    account.updateTimestamp();
    if (account.id == 0) {
      account.id = _nextId++;
      _accounts.add(account);
    } else {
      final index = _accounts.indexWhere((a) => a.id == account.id);
      if (index != -1) {
        _accounts[index] = account;
      }
    }

    // Persist to storage
    await _saveAccountsToStorage();
    return account;
  }

  Future<void> _saveAccountsToStorage() async {
    try {
      final accountsData = _accounts.map((account) => account.toJson()).toList();
      await _storage.saveAccounts(accountsData);
    } catch (e) {
      debugPrint('Error saving accounts to storage: $e');
    }
  }

  Future<void> deleteAccount(int accountId) async {
    final account = _accounts.firstWhere((a) => a.id == accountId, orElse: () => Account());
    if (account.id != 0) {
      account.softDelete();
    }
  }

  // Transaction operations
  Future<List<Transaction>> getAllTransactions({String? userId}) async {
    return _transactions.where((transaction) {
      if (transaction.isDeleted) return false;
      if (userId != null && transaction.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.transactionDate.compareTo(a.transactionDate));
  }

  Future<List<Transaction>> getTransactionsByAccount(int accountId, {String? userId}) async {
    return _transactions.where((transaction) {
      if (transaction.isDeleted) return false;
      if (transaction.accountId != accountId && transaction.toAccountId != accountId) return false;
      if (userId != null && transaction.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.transactionDate.compareTo(a.transactionDate));
  }

  Future<List<Transaction>> getTransactionsByCategory(String category, {String? userId}) async {
    return _transactions.where((transaction) {
      if (transaction.isDeleted || transaction.category != category) return false;
      if (userId != null && transaction.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => b.transactionDate.compareTo(a.transactionDate));
  }

  Future<List<Transaction>> getTransactionsByDateRange(DateTime start, DateTime end, {String? userId}) async {
    return _transactions.where((transaction) {
      if (transaction.isDeleted) return false;
      if (userId != null && transaction.userId != userId) return false;
      return transaction.transactionDate.isAfter(start) && transaction.transactionDate.isBefore(end);
    }).toList()..sort((a, b) => b.transactionDate.compareTo(a.transactionDate));
  }

  Future<Transaction> saveTransaction(Transaction transaction) async {
    transaction.updateTimestamp();
    if (transaction.id == 0) {
      transaction.id = _nextId++;
      _transactions.add(transaction);
    } else {
      final index = _transactions.indexWhere((t) => t.id == transaction.id);
      if (index != -1) {
        _transactions[index] = transaction;
      }
    }

    // Persist to storage
    await _saveTransactionsToStorage();
    return transaction;
  }

  Future<void> _saveTransactionsToStorage() async {
    try {
      final transactionsData = _transactions.map((transaction) => transaction.toJson()).toList();
      await _storage.saveTransactions(transactionsData);
    } catch (e) {
      debugPrint('Error saving transactions to storage: $e');
    }
  }

  Future<void> deleteTransaction(int transactionId) async {
    final transaction = _transactions.firstWhere((t) => t.id == transactionId, orElse: () => Transaction());
    if (transaction.id != 0) {
      transaction.softDelete();
    }
  }

  // Budget operations
  Future<List<Budget>> getAllBudgets({String? userId}) async {
    return _budgets.where((budget) {
      if (budget.isDeleted) return false;
      if (userId != null && budget.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<List<Budget>> getActiveBudgets({String? userId}) async {
    return _budgets.where((budget) {
      if (budget.isDeleted || !budget.isActive) return false;
      if (userId != null && budget.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<Budget> saveBudget(Budget budget) async {
    budget.updateTimestamp();
    if (budget.id == 0) {
      budget.id = _nextId++;
      _budgets.add(budget);
    } else {
      final index = _budgets.indexWhere((b) => b.id == budget.id);
      if (index != -1) {
        _budgets[index] = budget;
      }
    }

    // Persist to storage
    await _saveBudgetsToStorage();
    return budget;
  }

  Future<void> _saveBudgetsToStorage() async {
    try {
      final budgetsData = _budgets.map((budget) => budget.toJson()).toList();
      await _storage.saveBudgets(budgetsData);
    } catch (e) {
      debugPrint('Error saving budgets to storage: $e');
    }
  }

  Future<void> deleteBudget(int budgetId) async {
    final budget = _budgets.firstWhere((b) => b.id == budgetId, orElse: () => Budget());
    if (budget.id != 0) {
      budget.softDelete();
    }
  }

  // Financial Goal operations
  Future<List<FinancialGoal>> getAllFinancialGoals({String? userId}) async {
    return _financialGoals.where((goal) {
      if (goal.isDeleted) return false;
      if (userId != null && goal.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<List<FinancialGoal>> getActiveFinancialGoals({String? userId}) async {
    return _financialGoals.where((goal) {
      if (goal.isDeleted || goal.status != GoalStatus.active) return false;
      if (userId != null && goal.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.targetDate.compareTo(b.targetDate));
  }

  Future<FinancialGoal> saveFinancialGoal(FinancialGoal goal) async {
    goal.updateTimestamp();
    if (goal.id == 0) {
      goal.id = _nextId++;
      _financialGoals.add(goal);
    } else {
      final index = _financialGoals.indexWhere((g) => g.id == goal.id);
      if (index != -1) {
        _financialGoals[index] = goal;
      }
    }

    // Persist to storage
    await _saveFinancialGoalsToStorage();
    return goal;
  }

  Future<void> _saveFinancialGoalsToStorage() async {
    try {
      final goalsData = _financialGoals.map((goal) => goal.toJson()).toList();
      await _storage.saveFinancialGoals(goalsData);
    } catch (e) {
      debugPrint('Error saving financial goals to storage: $e');
    }
  }

  Future<void> deleteFinancialGoal(int goalId) async {
    final goal = _financialGoals.firstWhere((g) => g.id == goalId, orElse: () => FinancialGoal());
    if (goal.id != 0) {
      goal.softDelete();
    }
  }

  // Custom Dhikr Routine operations
  Future<List<CustomDhikrRoutine>> getAllCustomDhikrRoutines({String? userId}) async {
    return _customDhikrRoutines.where((routine) {
      if (routine.isDeleted) return false;
      if (userId != null && routine.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<List<CustomDhikrRoutine>> getActiveCustomDhikrRoutines({String? userId}) async {
    return _customDhikrRoutines.where((routine) {
      if (routine.isDeleted || !routine.isActive) return false;
      if (userId != null && routine.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<CustomDhikrRoutine> saveCustomDhikrRoutine(CustomDhikrRoutine routine) async {
    routine.updateTimestamp();
    if (routine.id == 0) {
      routine.id = _nextId++;
      _customDhikrRoutines.add(routine);
    } else {
      final index = _customDhikrRoutines.indexWhere((r) => r.id == routine.id);
      if (index != -1) {
        _customDhikrRoutines[index] = routine;
      }
    }

    // Persist to storage
    await _saveCustomDhikrRoutinesToStorage();
    return routine;
  }

  Future<void> _saveCustomDhikrRoutinesToStorage() async {
    try {
      final routinesData = _customDhikrRoutines.map((routine) => routine.toJson()).toList();
      await _storage.saveDhikrRoutines(routinesData);
    } catch (e) {
      debugPrint('Error saving custom dhikr routines to storage: $e');
    }
  }

  Future<void> deleteCustomDhikrRoutine(int routineId) async {
    final routine = _customDhikrRoutines.firstWhere((r) => r.id == routineId, orElse: () => CustomDhikrRoutine());
    if (routine.id != 0) {
      routine.softDelete();
    }
  }

  // Legacy Quran operations - now handled by QuranService and providers

  // Custom Tool operations
  Future<List<CustomTool>> getAllCustomTools({String? userId}) async {
    return _customTools.where((tool) {
      return tool.isActive;
    }).toList()..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<CustomTool> saveCustomTool(CustomTool tool) async {
    CustomTool updatedTool;

    if (tool.id.isEmpty || tool.id == '0') {
      updatedTool = tool.copyWith(
        id: (_nextId++).toString(),
        updatedAt: DateTime.now(),
      );
      _customTools.add(updatedTool);
    } else {
      updatedTool = tool.copyWith(updatedAt: DateTime.now());
      final index = _customTools.indexWhere((t) => t.id == tool.id);
      if (index != -1) {
        _customTools[index] = updatedTool;
      } else {
        _customTools.add(updatedTool);
      }
    }

    // Persist to storage
    await _saveCustomToolsToStorage();
    return updatedTool;
  }

  Future<void> _saveCustomToolsToStorage() async {
    try {
      final toolsData = _customTools.map((tool) => tool.toJson()).toList();
      await _storage.saveCustomTools(toolsData);
    } catch (e) {
      debugPrint('Error saving custom tools to storage: $e');
    }
  }

  Future<void> deleteCustomTool(String toolId) async {
    final index = _customTools.indexWhere((t) => t.id == toolId);
    if (index != -1) {
      final tool = _customTools[index];
      final updatedTool = tool.copyWith(isActive: false);
      _customTools[index] = updatedTool;
      await _saveCustomToolsToStorage();
    }
  }

  // Category operations
  Future<List<MoneyCategory>> getAllCategories({String? userId}) async {
    return _categories.where((category) {
      if (category.isDeleted) return false;
      if (userId != null && category.userId != userId) return false;
      return true;
    }).toList()..sort((a, b) => a.name.compareTo(b.name));
  }

  Future<MoneyCategory> saveCategory(MoneyCategory category) async {
    category.updateTimestamp();
    if (category.id == 0) {
      category.id = _nextId++;
      _categories.add(category);
    } else {
      final index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
      }
    }

    await _saveCategoriesToStorage();
    return category;
  }

  Future<void> _saveCategoriesToStorage() async {
    try {
      final categoriesData = _categories.map((category) => category.toJson()).toList();
      await _storage.saveCategories(categoriesData);
    } catch (e) {
      debugPrint('Error saving categories to storage: $e');
    }
  }

  Future<void> deleteCategory(int categoryId) async {
    final category = _categories.firstWhere((c) => c.id == categoryId, orElse: () => MoneyCategory());
    if (category.id != 0) {
      category.softDelete();
      await _saveCategoriesToStorage();
    }
  }

  Future<void> clearAllData() async {
    _userProfile = null;
    _notes.clear();
    _todos.clear();
    _voiceMemos.clear();
    _dhikrs.clear();
    _dhikrSessions.clear();
    _prayerTimes.clear();
    _customDhikrRoutines.clear();
    // Legacy Quran data cleared by QuranService
    _customTools.clear();
    _calculators.clear();
    _conversions.clear();
    _calculationHistory.clear();
    _accounts.clear();
    _transactions.clear();
    _categories.clear();
    _budgets.clear();
    _financialGoals.clear();
    _nextId = 1;
  }
}
