import 'package:flutter/material.dart';

enum CategoryType { income, expense, transfer }

class MoneyCategory {
  int id = 0;
  String name = '';
  String description = '';
  CategoryType type = CategoryType.expense;
  IconData icon = Icons.category;
  Color color = Colors.blue;
  String? parentCategoryId; // For subcategories
  bool isActive = true;
  bool isDefault = false; // System default categories
  double budgetLimit = 0.0; // Monthly budget limit for expense categories
  bool enableBudgetAlert = false;
  double alertThreshold = 0.8; // Alert when 80% of budget is reached
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  MoneyCategory();

  MoneyCategory.create({
    required this.name,
    required this.type,
    required this.userId,
    this.description = '',
    this.icon = Icons.category,
    this.color = Colors.blue,
    this.parentCategoryId,
    this.isActive = true,
    this.isDefault = false,
    this.budgetLimit = 0.0,
    this.enableBudgetAlert = false,
    this.alertThreshold = 0.8,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);
  bool get isSubcategory => parentCategoryId != null;

  String get typeDisplayName {
    switch (type) {
      case CategoryType.income:
        return 'Income';
      case CategoryType.expense:
        return 'Expense';
      case CategoryType.transfer:
        return 'Transfer';
    }
  }

  // Get default categories for each type
  static List<MoneyCategory> getDefaultCategories(String userId) {
    return [
      // Income Categories
      MoneyCategory.create(
        name: 'Salary',
        type: CategoryType.income,
        userId: userId,
        icon: Icons.work,
        color: Colors.green,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Freelance',
        type: CategoryType.income,
        userId: userId,
        icon: Icons.laptop,
        color: Colors.teal,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Investment Returns',
        type: CategoryType.income,
        userId: userId,
        icon: Icons.trending_up,
        color: Colors.indigo,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Other Income',
        type: CategoryType.income,
        userId: userId,
        icon: Icons.attach_money,
        color: Colors.lightGreen,
        isDefault: true,
      ),

      // Expense Categories
      MoneyCategory.create(
        name: 'Food & Dining',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.restaurant,
        color: Colors.orange,
        isDefault: true,
        budgetLimit: 500.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Transportation',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.directions_car,
        color: Colors.blue,
        isDefault: true,
        budgetLimit: 300.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Shopping',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.shopping_bag,
        color: Colors.purple,
        isDefault: true,
        budgetLimit: 200.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Entertainment',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.movie,
        color: Colors.pink,
        isDefault: true,
        budgetLimit: 150.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Bills & Utilities',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.receipt_long,
        color: Colors.red,
        isDefault: true,
        budgetLimit: 800.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Healthcare',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.local_hospital,
        color: Colors.cyan,
        isDefault: true,
        budgetLimit: 200.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Education',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.school,
        color: Colors.amber,
        isDefault: true,
        budgetLimit: 100.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Travel',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.flight,
        color: Colors.deepOrange,
        isDefault: true,
        budgetLimit: 300.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Personal Care',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.spa,
        color: Colors.pinkAccent,
        isDefault: true,
        budgetLimit: 100.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Home & Garden',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.home,
        color: Colors.brown,
        isDefault: true,
        budgetLimit: 200.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Insurance',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.security,
        color: Colors.blueGrey,
        isDefault: true,
        budgetLimit: 400.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Taxes',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.account_balance,
        color: Colors.grey,
        isDefault: true,
        budgetLimit: 500.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Gifts & Donations',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.card_giftcard,
        color: Colors.deepPurple,
        isDefault: true,
        budgetLimit: 100.0,
        enableBudgetAlert: true,
      ),
      MoneyCategory.create(
        name: 'Other Expenses',
        type: CategoryType.expense,
        userId: userId,
        icon: Icons.more_horiz,
        color: Colors.grey,
        isDefault: true,
        budgetLimit: 100.0,
        enableBudgetAlert: true,
      ),

      // Transfer Categories
      MoneyCategory.create(
        name: 'Account Transfer',
        type: CategoryType.transfer,
        userId: userId,
        icon: Icons.swap_horiz,
        color: Colors.blue,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Investment Transfer',
        type: CategoryType.transfer,
        userId: userId,
        icon: Icons.trending_up,
        color: Colors.green,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Savings Transfer',
        type: CategoryType.transfer,
        userId: userId,
        icon: Icons.savings,
        color: Colors.teal,
        isDefault: true,
      ),
    ];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'icon': icon.codePoint,
      'color': (color.r * 255).round() << 16 | (color.g * 255).round() << 8 | (color.b * 255).round() | (color.a * 255).round() << 24,
      'parentCategoryId': parentCategoryId,
      'isActive': isActive,
      'isDefault': isDefault,
      'budgetLimit': budgetLimit,
      'enableBudgetAlert': enableBudgetAlert,
      'alertThreshold': alertThreshold,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory MoneyCategory.fromJson(Map<String, dynamic> json) {
    final category = MoneyCategory();
    category.id = json['id'] ?? 0;
    category.name = json['name'] ?? '';
    category.description = json['description'] ?? '';
    category.type = CategoryType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => CategoryType.expense,
    );
    category.icon = _getIconFromCodePoint(json['icon'] ?? Icons.category.codePoint);
    category.color = Color(json['color'] ?? ((Colors.blue.r * 255).round() << 16 | (Colors.blue.g * 255).round() << 8 | (Colors.blue.b * 255).round() | (Colors.blue.a * 255).round() << 24));
    category.parentCategoryId = json['parentCategoryId'];
    category.isActive = json['isActive'] ?? true;
    category.isDefault = json['isDefault'] ?? false;
    category.budgetLimit = json['budgetLimit']?.toDouble() ?? 0.0;
    category.enableBudgetAlert = json['enableBudgetAlert'] ?? false;
    category.alertThreshold = json['alertThreshold']?.toDouble() ?? 0.8;
    category.userId = json['userId'] ?? '';
    category.createdAt = DateTime.parse(json['createdAt']);
    category.updatedAt = DateTime.parse(json['updatedAt']);
    category.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    category.isDeleted = json['isDeleted'] ?? false;
    category.syncId = json['syncId'];
    return category;
  }

  static IconData _getIconFromCodePoint(int codePoint) {
    // Map of common category icon code points to their IconData constants
    const iconMap = {
      0xe574: Icons.category, // category
      0xe59c: Icons.shopping_cart, // shopping_cart
      0xe57c: Icons.monetization_on, // monetization_on
      0xe0c8: Icons.credit_card, // credit_card
      0xe227: Icons.savings, // savings
      0xe0c7: Icons.account_balance, // account_balance
      0xe263: Icons.payments, // payments
      0xe0ca: Icons.restaurant, // restaurant
      0xe0d9: Icons.local_gas_station, // local_gas_station
      0xe1a3: Icons.home, // home
      0xe1a4: Icons.directions_car, // directions_car
      0xe1a5: Icons.school, // school
      0xe1a6: Icons.medical_services, // medical_services
    };

    return iconMap[codePoint] ?? Icons.category;
  }
}
