import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculation_history.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Calculation history provider
final calculationHistoryProvider = StateNotifierProvider<CalculationHistoryNotifier, List<CalculationHistory>>((ref) {
  return CalculationHistoryNotifier(ref);
});

class CalculationHistoryNotifier extends StateNotifier<List<CalculationHistory>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  CalculationHistoryNotifier(this.ref) : super([]) {
    loadHistory();
  }

  Future<void> loadHistory() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final history = await _db.getAllCalculationHistory(userId: userProfile.id.toString());
      state = history;
    }
  }

  Future<void> addCalculation(CalculationHistory calculation) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      calculation.userId = userProfile.id.toString();
      final savedCalculation = await _db.saveCalculationHistory(calculation);
      state = [savedCalculation, ...state];
    }
  }

  Future<void> deleteCalculation(int calculationId) async {
    await _db.deleteCalculationHistory(calculationId);
    state = state.where((c) => c.id != calculationId).toList();
  }

  Future<void> clearHistory() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      await _db.clearCalculationHistory(userId: userProfile.id.toString());
      state = [];
    }
  }

  List<CalculationHistory> getHistoryByType(CalculationType type) {
    return state.where((calc) => calc.type == type).toList();
  }

  List<CalculationHistory> searchHistory(String query) {
    if (query.isEmpty) return state;
    
    final searchTerm = query.toLowerCase();
    return state.where((calc) => calc.containsSearchTerm(searchTerm)).toList();
  }

  List<CalculationHistory> getTodayHistory() {
    final today = DateTime.now();
    return state.where((calc) {
      return calc.createdAt.year == today.year &&
             calc.createdAt.month == today.month &&
             calc.createdAt.day == today.day;
    }).toList();
  }
}

// History by type provider
final historyByTypeProvider = Provider.family<List<CalculationHistory>, CalculationType>((ref, type) {
  final history = ref.watch(calculationHistoryProvider);
  return history.where((calc) => calc.type == type).toList();
});

// History statistics provider
final historyStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final history = ref.watch(calculationHistoryProvider);
  
  final typeStats = <CalculationType, int>{};
  for (final calc in history) {
    typeStats[calc.type] = (typeStats[calc.type] ?? 0) + 1;
  }
  
  final today = DateTime.now();
  final todayHistory = history.where((calc) {
    return calc.createdAt.year == today.year &&
           calc.createdAt.month == today.month &&
           calc.createdAt.day == today.day;
  }).length;
  
  final thisWeek = DateTime.now().subtract(const Duration(days: 7));
  final weekHistory = history.where((calc) => calc.createdAt.isAfter(thisWeek)).length;
  
  return {
    'total': history.length,
    'today': todayHistory,
    'thisWeek': weekHistory,
    'typeStats': typeStats,
    'mostUsedType': typeStats.isNotEmpty 
        ? typeStats.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : null,
  };
});

// Search history provider
final searchHistoryProvider = StateProvider<String>((ref) => '');

// Filter history provider
final filterHistoryProvider = StateProvider<CalculationType?>((ref) => null);

final filteredHistoryProvider = Provider<List<CalculationHistory>>((ref) {
  final history = ref.watch(calculationHistoryProvider);
  final searchQuery = ref.watch(searchHistoryProvider);
  final filterType = ref.watch(filterHistoryProvider);

  var filteredHistory = history;

  // Apply type filter
  if (filterType != null) {
    filteredHistory = filteredHistory.where((calc) => calc.type == filterType).toList();
  }

  // Apply search filter
  if (searchQuery.isNotEmpty) {
    final query = searchQuery.toLowerCase();
    filteredHistory = filteredHistory.where((calc) => calc.containsSearchTerm(query)).toList();
  }

  return filteredHistory;
});

// Recent history provider (last 10 items)
final recentHistoryProvider = Provider<List<CalculationHistory>>((ref) {
  final history = ref.watch(calculationHistoryProvider);
  return history.take(10).toList();
});
