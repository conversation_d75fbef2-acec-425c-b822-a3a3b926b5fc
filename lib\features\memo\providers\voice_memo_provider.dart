import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:record/record.dart'; // Temporarily disabled for build compatibility
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import '../models/voice_memo.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Voice Memos List Provider
final voiceMemosProvider = StateNotifierProvider<VoiceMemosNotifier, List<VoiceMemo>>((ref) {
  final userId = ref.watch(userProfileProvider.select((profile) => profile?.id.toString()));
  return VoiceMemosNotifier(userId);
});

class VoiceMemosNotifier extends StateNotifier<List<VoiceMemo>> {
  final String? userId;

  VoiceMemosNotifier(this.userId) : super([]) {
    loadVoiceMemos();
  }

  Future<void> loadVoiceMemos() async {
    final memos = await DatabaseService.instance.getAllVoiceMemos(userId: userId);
    state = memos;
  }

  Future<VoiceMemo> createVoiceMemo({
    required String title,
    required String filePath,
    required int durationMs,
    required double fileSizeBytes,
    String? description,
    String audioFormat = 'aac',
    bool isFavorite = false,
    List<String>? tags,
  }) async {
    final memo = VoiceMemo.create(
      title: title,
      filePath: filePath,
      durationMs: durationMs,
      fileSizeBytes: fileSizeBytes,
      userId: userId ?? '',
      description: description,
      audioFormat: audioFormat,
      isFavorite: isFavorite,
      tags: tags,
    );

    final savedMemo = await DatabaseService.instance.saveVoiceMemo(memo);
    await loadVoiceMemos(); // Refresh the list
    return savedMemo;
  }

  Future<void> updateVoiceMemo(VoiceMemo memo) async {
    await DatabaseService.instance.saveVoiceMemo(memo);
    await loadVoiceMemos(); // Refresh the list
  }

  Future<void> deleteVoiceMemo(int memoId) async {
    // Delete the audio file
    final memo = state.firstWhere((m) => m.id == memoId);
    final file = File(memo.filePath);
    if (await file.exists()) {
      await file.delete();
    }
    
    await DatabaseService.instance.deleteVoiceMemo(memoId);
    await loadVoiceMemos(); // Refresh the list
  }

  Future<void> toggleFavorite(int memoId) async {
    final memo = state.firstWhere((m) => m.id == memoId);
    memo.toggleFavorite();
    await updateVoiceMemo(memo);
  }

  List<VoiceMemo> get favoriteMemos => state.where((memo) => memo.isFavorite).toList();

  Future<void> addMemo(VoiceMemo memo) async {
    final savedMemo = await DatabaseService.instance.saveVoiceMemo(memo);
    state = [...state, savedMemo];
  }

  Future<void> updateMemo(VoiceMemo memo) async {
    await updateVoiceMemo(memo);
  }
}

// Recording Provider
final recordingProvider = StateNotifierProvider<RecordingNotifier, RecordingState>((ref) {
  return RecordingNotifier();
});

class RecordingState {
  final bool isRecording;
  final bool isPaused;
  final Duration duration;
  final String? filePath;

  const RecordingState({
    this.isRecording = false,
    this.isPaused = false,
    this.duration = Duration.zero,
    this.filePath,
  });

  RecordingState copyWith({
    bool? isRecording,
    bool? isPaused,
    Duration? duration,
    String? filePath,
  }) {
    return RecordingState(
      isRecording: isRecording ?? this.isRecording,
      isPaused: isPaused ?? this.isPaused,
      duration: duration ?? this.duration,
      filePath: filePath ?? this.filePath,
    );
  }
}

class RecordingNotifier extends StateNotifier<RecordingState> {
  // final AudioRecorder _recorder = AudioRecorder(); // Temporarily disabled
  
  RecordingNotifier() : super(const RecordingState());

  Future<bool> startRecording() async {
    try {
      // Recording temporarily disabled - record package not compatible
      // TODO: Re-enable when record package is compatible
      if (true) { // Simulate permission check
        final directory = await getApplicationDocumentsDirectory();
        final fileName = 'voice_memo_${DateTime.now().millisecondsSinceEpoch}.aac';
        final filePath = '${directory.path}/voice_memos/$fileName';
        
        // Create directory if it doesn't exist
        final dir = Directory('${directory.path}/voice_memos');
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        // Recording start temporarily disabled
        debugPrint('Recording would start at: $filePath');

        state = state.copyWith(
          isRecording: true,
          filePath: filePath,
        );
        
        return true;
      }
    } catch (e) {
      debugPrint('Error starting recording: $e');
    }
    return false;
  }

  Future<String?> stopRecording() async {
    try {
      // final path = await _recorder.stop(); // Temporarily disabled
      final path = state.filePath; // Use current file path
      state = state.copyWith(
        isRecording: false,
        isPaused: false,
        duration: Duration.zero,
      );
      return path;
    } catch (e) {
      debugPrint('Error stopping recording: $e');
      return null;
    }
  }

  Future<void> pauseRecording() async {
    try {
      // await _recorder.pause(); // Temporarily disabled
      state = state.copyWith(isPaused: true);
    } catch (e) {
      debugPrint('Error pausing recording: $e');
    }
  }

  Future<void> resumeRecording() async {
    try {
      // await _recorder.resume(); // Temporarily disabled
      state = state.copyWith(isPaused: false);
    } catch (e) {
      debugPrint('Error resuming recording: $e');
    }
  }

  void updateDuration(Duration duration) {
    state = state.copyWith(duration: duration);
  }

  @override
  void dispose() {
    // _recorder.dispose(); // Temporarily disabled
    super.dispose();
  }
}

// Audio Player Provider
final audioPlayerProvider = StateNotifierProvider<AudioPlayerNotifier, AudioPlayerState>((ref) {
  return AudioPlayerNotifier();
});

class AudioPlayerState {
  final bool isPlaying;
  final Duration duration;
  final Duration position;
  final String? currentFilePath;

  const AudioPlayerState({
    this.isPlaying = false,
    this.duration = Duration.zero,
    this.position = Duration.zero,
    this.currentFilePath,
  });

  AudioPlayerState copyWith({
    bool? isPlaying,
    Duration? duration,
    Duration? position,
    String? currentFilePath,
  }) {
    return AudioPlayerState(
      isPlaying: isPlaying ?? this.isPlaying,
      duration: duration ?? this.duration,
      position: position ?? this.position,
      currentFilePath: currentFilePath ?? this.currentFilePath,
    );
  }
}

class AudioPlayerNotifier extends StateNotifier<AudioPlayerState> {
  final AudioPlayer _player = AudioPlayer();
  
  AudioPlayerNotifier() : super(const AudioPlayerState()) {
    _setupPlayerListeners();
  }

  void _setupPlayerListeners() {
    _player.onDurationChanged.listen((duration) {
      state = state.copyWith(duration: duration);
    });

    _player.onPositionChanged.listen((position) {
      state = state.copyWith(position: position);
    });

    _player.onPlayerStateChanged.listen((playerState) {
      state = state.copyWith(isPlaying: playerState == PlayerState.playing);
    });
  }

  Future<void> play(String filePath) async {
    try {
      if (state.currentFilePath != filePath) {
        await _player.setSourceDeviceFile(filePath);
        state = state.copyWith(currentFilePath: filePath);
      }
      await _player.resume();
    } catch (e) {
      print('Error playing audio: $e');
    }
  }

  Future<void> pause() async {
    try {
      await _player.pause();
    } catch (e) {
      print('Error pausing audio: $e');
    }
  }

  Future<void> stop() async {
    try {
      await _player.stop();
      state = state.copyWith(
        isPlaying: false,
        position: Duration.zero,
        currentFilePath: null,
      );
    } catch (e) {
      print('Error stopping audio: $e');
    }
  }

  Future<void> seek(Duration position) async {
    try {
      await _player.seek(position);
    } catch (e) {
      print('Error seeking audio: $e');
    }
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }
}

// Search Provider
final voiceMemoSearchProvider = StateProvider<String>((ref) => '');

// Filtered Voice Memos Provider
final filteredVoiceMemosProvider = Provider<List<VoiceMemo>>((ref) {
  final memos = ref.watch(voiceMemosProvider);
  final searchTerm = ref.watch(voiceMemoSearchProvider);
  
  if (searchTerm.isEmpty) {
    return memos;
  }
  
  return memos.where((memo) => memo.containsSearchTerm(searchTerm)).toList();
});

// Voice Memo Statistics Provider
final voiceMemoStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final memos = ref.watch(voiceMemosProvider);
  
  final totalDuration = memos.fold<int>(0, (sum, memo) => sum + memo.durationMs);
  final totalSize = memos.fold<double>(0, (sum, memo) => sum + memo.fileSizeBytes);
  
  return {
    'total': memos.length,
    'favorites': memos.where((memo) => memo.isFavorite).length,
    'totalDuration': totalDuration,
    'totalSize': totalSize,
    'averageDuration': memos.isNotEmpty ? totalDuration / memos.length : 0,
  };
});
