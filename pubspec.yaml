name: shadowsuite
description: "ShadowSuite Tool Builder - Professional App Development Platform with Excel Integration and Mobile Themes"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # State Management
  flutter_riverpod: ^2.5.1

  # Navigation
  go_router: ^14.2.7

  # Database (temporarily disabled for web compatibility)
  # isar: ^3.1.0+1
  # isar_flutter_libs: ^3.1.0+1
  path_provider: ^2.1.4

  # Cloud Sync
  supabase_flutter: ^2.6.0

  # Notifications
  flutter_local_notifications: ^17.2.3
  timezone: ^0.9.4

  # File Operations
  file_picker: ^8.1.2
  permission_handler: ^11.3.1
  image_picker: ^1.1.2
  image_picker_for_web: ^3.0.5
  share_plus: ^10.0.2

  # Excel and Spreadsheet functionality for Tool Builder
  excel: ^4.0.6

  # Formula evaluation and parsing
  expressions: ^0.2.4
  petitparser: ^6.0.2

  # Audio
  # record: ^5.1.2  # Temporarily disabled for Windows build
  audioplayers: ^6.1.0

  # Utilities
  intl: any
  uuid: ^4.5.1
  crypto: ^3.0.5
  json_annotation: ^4.9.0
  equatable: ^2.0.5
  math_expressions: ^2.6.0

  # Storage
  shared_preferences: ^2.3.2
  # isar: ^3.1.0+1  # Temporarily disabled for Android build
  # isar_flutter_libs: ^3.1.0+1  # Temporarily disabled for Android build

  # HTTP
  http: ^1.2.2

  # Connectivity and offline support
  connectivity_plus: ^5.0.2

  # Security and encryption
  encrypt: ^5.0.3
  local_auth: ^2.3.0

  # Charts and visualization
  fl_chart: ^0.69.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  # isar_generator: ^3.1.0+1  # Temporarily disabled for Android build
  build_runner: ^2.4.7
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - Quran.txt
    - assets/audio/
    - assets/data/
    - assets/translations/

  generate: true
