import 'package:flutter/material.dart';
import '../../../shared/widgets/standardized_components.dart';

/// Category icon data
class CategoryIcon {
  final IconData icon;
  final String name;
  final String category;

  const CategoryIcon({
    required this.icon,
    required this.name,
    required this.category,
  });
}

/// Category icon picker widget
class CategoryIconPicker extends StatefulWidget {
  final IconData selectedIcon;
  final Function(IconData icon) onIconSelected;
  final Color? selectedColor;

  const CategoryIconPicker({
    super.key,
    required this.selectedIcon,
    required this.onIconSelected,
    this.selectedColor,
  });

  @override
  State<CategoryIconPicker> createState() => _CategoryIconPickerState();
}

class _CategoryIconPickerState extends State<CategoryIconPicker> {
  String _selectedCategory = 'All';
  
  static const List<CategoryIcon> _categoryIcons = [
    // Food & Dining
    CategoryIcon(icon: Icons.restaurant, name: 'Restaurant', category: 'Food'),
    CategoryIcon(icon: Icons.fastfood, name: 'Fast Food', category: 'Food'),
    CategoryIcon(icon: Icons.local_pizza, name: 'Pizza', category: 'Food'),
    CategoryIcon(icon: Icons.local_cafe, name: 'Cafe', category: 'Food'),
    CategoryIcon(icon: Icons.local_bar, name: 'Bar', category: 'Food'),
    CategoryIcon(icon: Icons.cake, name: 'Dessert', category: 'Food'),
    CategoryIcon(icon: Icons.kitchen, name: 'Cooking', category: 'Food'),
    CategoryIcon(icon: Icons.lunch_dining, name: 'Lunch', category: 'Food'),
    
    // Transportation
    CategoryIcon(icon: Icons.directions_car, name: 'Car', category: 'Transport'),
    CategoryIcon(icon: Icons.local_gas_station, name: 'Gas', category: 'Transport'),
    CategoryIcon(icon: Icons.train, name: 'Train', category: 'Transport'),
    CategoryIcon(icon: Icons.directions_bus, name: 'Bus', category: 'Transport'),
    CategoryIcon(icon: Icons.flight, name: 'Flight', category: 'Transport'),
    CategoryIcon(icon: Icons.local_taxi, name: 'Taxi', category: 'Transport'),
    CategoryIcon(icon: Icons.two_wheeler, name: 'Motorcycle', category: 'Transport'),
    CategoryIcon(icon: Icons.directions_bike, name: 'Bike', category: 'Transport'),
    
    // Shopping
    CategoryIcon(icon: Icons.shopping_bag, name: 'Shopping', category: 'Shopping'),
    CategoryIcon(icon: Icons.shopping_cart, name: 'Groceries', category: 'Shopping'),
    CategoryIcon(icon: Icons.store, name: 'Store', category: 'Shopping'),
    CategoryIcon(icon: Icons.local_mall, name: 'Mall', category: 'Shopping'),
    CategoryIcon(icon: Icons.checkroom, name: 'Clothing', category: 'Shopping'),
    CategoryIcon(icon: Icons.devices, name: 'Electronics', category: 'Shopping'),
    CategoryIcon(icon: Icons.book, name: 'Books', category: 'Shopping'),
    CategoryIcon(icon: Icons.toys, name: 'Toys', category: 'Shopping'),
    
    // Entertainment
    CategoryIcon(icon: Icons.movie, name: 'Movies', category: 'Entertainment'),
    CategoryIcon(icon: Icons.music_note, name: 'Music', category: 'Entertainment'),
    CategoryIcon(icon: Icons.sports_esports, name: 'Gaming', category: 'Entertainment'),
    CategoryIcon(icon: Icons.sports_soccer, name: 'Sports', category: 'Entertainment'),
    CategoryIcon(icon: Icons.theater_comedy, name: 'Theater', category: 'Entertainment'),
    CategoryIcon(icon: Icons.celebration, name: 'Party', category: 'Entertainment'),
    CategoryIcon(icon: Icons.casino, name: 'Casino', category: 'Entertainment'),
    CategoryIcon(icon: Icons.park, name: 'Recreation', category: 'Entertainment'),
    
    // Bills & Utilities
    CategoryIcon(icon: Icons.receipt_long, name: 'Bills', category: 'Bills'),
    CategoryIcon(icon: Icons.electrical_services, name: 'Electricity', category: 'Bills'),
    CategoryIcon(icon: Icons.water_drop, name: 'Water', category: 'Bills'),
    CategoryIcon(icon: Icons.wifi, name: 'Internet', category: 'Bills'),
    CategoryIcon(icon: Icons.phone, name: 'Phone', category: 'Bills'),
    CategoryIcon(icon: Icons.tv, name: 'Cable TV', category: 'Bills'),
    CategoryIcon(icon: Icons.local_fire_department, name: 'Gas', category: 'Bills'),
    CategoryIcon(icon: Icons.delete, name: 'Trash', category: 'Bills'),
    
    // Healthcare
    CategoryIcon(icon: Icons.local_hospital, name: 'Hospital', category: 'Health'),
    CategoryIcon(icon: Icons.medical_services, name: 'Medical', category: 'Health'),
    CategoryIcon(icon: Icons.local_pharmacy, name: 'Pharmacy', category: 'Health'),
    CategoryIcon(icon: Icons.fitness_center, name: 'Fitness', category: 'Health'),
    CategoryIcon(icon: Icons.spa, name: 'Spa', category: 'Health'),
    CategoryIcon(icon: Icons.psychology, name: 'Mental Health', category: 'Health'),
    CategoryIcon(icon: Icons.healing, name: 'Treatment', category: 'Health'),
    CategoryIcon(icon: Icons.vaccines, name: 'Vaccination', category: 'Health'),
    
    // Education
    CategoryIcon(icon: Icons.school, name: 'School', category: 'Education'),
    CategoryIcon(icon: Icons.library_books, name: 'Books', category: 'Education'),
    CategoryIcon(icon: Icons.computer, name: 'Online Course', category: 'Education'),
    CategoryIcon(icon: Icons.science, name: 'Science', category: 'Education'),
    CategoryIcon(icon: Icons.language, name: 'Language', category: 'Education'),
    CategoryIcon(icon: Icons.calculate, name: 'Math', category: 'Education'),
    CategoryIcon(icon: Icons.brush, name: 'Art', category: 'Education'),
    CategoryIcon(icon: Icons.piano, name: 'Music Lessons', category: 'Education'),
    
    // Home & Garden
    CategoryIcon(icon: Icons.home, name: 'Home', category: 'Home'),
    CategoryIcon(icon: Icons.construction, name: 'Renovation', category: 'Home'),
    CategoryIcon(icon: Icons.cleaning_services, name: 'Cleaning', category: 'Home'),
    CategoryIcon(icon: Icons.yard, name: 'Garden', category: 'Home'),
    CategoryIcon(icon: Icons.plumbing, name: 'Plumbing', category: 'Home'),
    CategoryIcon(icon: Icons.handyman, name: 'Maintenance', category: 'Home'),
    CategoryIcon(icon: Icons.chair, name: 'Furniture', category: 'Home'),
    CategoryIcon(icon: Icons.lightbulb, name: 'Lighting', category: 'Home'),
    
    // Work & Income
    CategoryIcon(icon: Icons.work, name: 'Work', category: 'Income'),
    CategoryIcon(icon: Icons.business, name: 'Business', category: 'Income'),
    CategoryIcon(icon: Icons.laptop, name: 'Freelance', category: 'Income'),
    CategoryIcon(icon: Icons.trending_up, name: 'Investment', category: 'Income'),
    CategoryIcon(icon: Icons.attach_money, name: 'Bonus', category: 'Income'),
    CategoryIcon(icon: Icons.card_giftcard, name: 'Gift', category: 'Income'),
    CategoryIcon(icon: Icons.sell, name: 'Sale', category: 'Income'),
    CategoryIcon(icon: Icons.account_balance, name: 'Interest', category: 'Income'),
    
    // Financial
    CategoryIcon(icon: Icons.savings, name: 'Savings', category: 'Financial'),
    CategoryIcon(icon: Icons.credit_card, name: 'Credit Card', category: 'Financial'),
    CategoryIcon(icon: Icons.account_balance_wallet, name: 'Wallet', category: 'Financial'),
    CategoryIcon(icon: Icons.payments, name: 'Payment', category: 'Financial'),
    CategoryIcon(icon: Icons.currency_exchange, name: 'Exchange', category: 'Financial'),
    CategoryIcon(icon: Icons.security, name: 'Insurance', category: 'Financial'),
    CategoryIcon(icon: Icons.gavel, name: 'Legal', category: 'Financial'),
    CategoryIcon(icon: Icons.receipt, name: 'Tax', category: 'Financial'),
    
    // General
    CategoryIcon(icon: Icons.category, name: 'Category', category: 'General'),
    CategoryIcon(icon: Icons.star, name: 'Star', category: 'General'),
    CategoryIcon(icon: Icons.favorite, name: 'Favorite', category: 'General'),
    CategoryIcon(icon: Icons.bookmark, name: 'Bookmark', category: 'General'),
    CategoryIcon(icon: Icons.flag, name: 'Flag', category: 'General'),
    CategoryIcon(icon: Icons.label, name: 'Label', category: 'General'),
    CategoryIcon(icon: Icons.folder, name: 'Folder', category: 'General'),
    CategoryIcon(icon: Icons.more_horiz, name: 'Other', category: 'General'),
  ];

  List<String> get _categories {
    final categories = _categoryIcons.map((icon) => icon.category).toSet().toList();
    categories.sort();
    return ['All', ...categories];
  }

  List<CategoryIcon> get _filteredIcons {
    if (_selectedCategory == 'All') {
      return _categoryIcons;
    }
    return _categoryIcons.where((icon) => icon.category == _selectedCategory).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        height: 600,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.palette,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Choose Category Icon',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Category filter
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = category == _selectedCategory;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                      selectedColor: Theme.of(context).colorScheme.primaryContainer,
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Current selection
            if (widget.selectedIcon != null) ...[
              Text(
                'Current Selection:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (widget.selectedColor ?? Theme.of(context).colorScheme.primary)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.selectedIcon,
                      color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _getIconName(widget.selectedIcon),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Icon grid
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 1,
                ),
                itemCount: _filteredIcons.length,
                itemBuilder: (context, index) {
                  final iconData = _filteredIcons[index];
                  final isSelected = iconData.icon == widget.selectedIcon;
                  
                  return Tooltip(
                    message: iconData.name,
                    child: InkWell(
                      onTap: () {
                        widget.onIconSelected(iconData.icon);
                        Navigator.of(context).pop();
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (widget.selectedColor ?? Theme.of(context).colorScheme.primary)
                                  .withValues(alpha: 0.2)
                              : Theme.of(context).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(12),
                          border: isSelected
                              ? Border.all(
                                  color: widget.selectedColor ?? Theme.of(context).colorScheme.primary,
                                  width: 2,
                                )
                              : null,
                        ),
                        child: Icon(
                          iconData.icon,
                          color: isSelected
                              ? (widget.selectedColor ?? Theme.of(context).colorScheme.primary)
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          size: 24,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Done'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getIconName(IconData icon) {
    final iconData = _categoryIcons.firstWhere(
      (item) => item.icon == icon,
      orElse: () => const CategoryIcon(icon: Icons.help, name: 'Unknown', category: 'General'),
    );
    return iconData.name;
  }
}
