import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/account_provider.dart';
import '../providers/transaction_provider.dart';
import '../models/account.dart';
import '../models/transaction.dart';

/// Comprehensive accounts management tab for Money Flow
class AccountsManagementTab extends ConsumerStatefulWidget {
  const AccountsManagementTab({super.key});

  @override
  ConsumerState<AccountsManagementTab> createState() => _AccountsManagementTabState();
}

class _AccountsManagementTabState extends ConsumerState<AccountsManagementTab> {
  String _searchQuery = '';
  String _selectedFilter = 'All';
  String _sortBy = 'Name';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final accounts = ref.watch(accountsProvider);
    final transactions = ref.watch(transactionsProvider);
    final filteredAccounts = _filterAccounts(accounts);

    return Scaffold(
      body: Column(
        children: [
          // Accounts toolbar
          _buildAccountsToolbar(),
          
          // Accounts summary
          _buildAccountsSummary(accounts, transactions),
          
          // Accounts content
          Expanded(
            child: _buildAccountsContent(filteredAccounts, transactions),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addAccount,
        icon: const Icon(Icons.add),
        label: const Text('Add Account'),
      ),
    );
  }

  Widget _buildAccountsToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Search and filter row
          Row(
            children: [
              // Search field
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search accounts...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Filter dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedFilter,
                  decoration: const InputDecoration(
                    labelText: 'Filter',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'All',
                    'Active',
                    'Inactive',
                    'Checking',
                    'Savings',
                    'Credit Card',
                    'Investment',
                    'Cash',
                  ].map((filter) {
                    return DropdownMenuItem(
                      value: filter,
                      child: Text(filter),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Sort dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: const InputDecoration(
                    labelText: 'Sort By',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'Name',
                    'Balance',
                    'Type',
                    'Created Date',
                    'Last Activity',
                  ].map((sort) {
                    return DropdownMenuItem(
                      value: sort,
                      child: Text(sort),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsSummary(List<Account> accounts, List<Transaction> transactions) {
    final activeAccounts = accounts.where((a) => a.isActive).length;
    final totalBalance = accounts
        .where((a) => a.isActive && a.includeInTotal)
        .fold(0.0, (sum, account) => sum + account.balance);
    
    final thisMonthTransactions = transactions.where((t) {
      final now = DateTime.now();
      return t.date.year == now.year && t.date.month == now.month;
    }).length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(child: _buildSummaryCard('Active Accounts', activeAccounts.toString(), Icons.account_balance_wallet, Colors.blue)),
          const SizedBox(width: 16),
          Expanded(child: _buildSummaryCard('Total Balance', '\$${totalBalance.toStringAsFixed(2)}', Icons.account_balance, Colors.green)),
          const SizedBox(width: 16),
          Expanded(child: _buildSummaryCard('This Month', '$thisMonthTransactions transactions', Icons.trending_up, Colors.orange)),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsContent(List<Account> accounts, List<Transaction> transactions) {
    if (accounts.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: accounts.length,
      itemBuilder: (context, index) {
        final account = accounts[index];
        final accountTransactions = transactions.where((t) => t.accountId == account.id.toString()).toList();
        return _buildAccountCard(account, accountTransactions);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No Accounts Found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty 
              ? 'Try adjusting your search or filter criteria'
              : 'Create your first account to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addAccount,
            icon: const Icon(Icons.add),
            label: const Text('Add Account'),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountCard(Account account, List<Transaction> transactions) {
    final recentTransactions = transactions.take(3).toList();
    final thisMonthTransactions = transactions.where((t) {
      final now = DateTime.now();
      return t.date.year == now.year && t.date.month == now.month;
    }).length;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getAccountTypeColor(account.type).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getAccountTypeIcon(account.type),
            color: _getAccountTypeColor(account.type),
          ),
        ),
        title: Text(
          account.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${account.type.name.toUpperCase()} • ${account.currency}'),
            const SizedBox(height: 4),
            Text(
              '\$${account.balance.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: account.balance >= 0 ? Colors.green : Colors.red,
                fontSize: 16,
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!account.isActive)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Inactive',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ),
            PopupMenuButton<String>(
              onSelected: (action) => _handleAccountAction(action, account),
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'view', child: Text('View Details')),
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'transfer', child: Text('Transfer Money')),
                const PopupMenuItem(value: 'export', child: Text('Export Transactions')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Account details
                _buildAccountDetails(account),
                
                const SizedBox(height: 16),
                
                // Quick stats
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickStat(
                        'Transactions',
                        transactions.length.toString(),
                        Icons.list,
                      ),
                    ),
                    Expanded(
                      child: _buildQuickStat(
                        'This Month',
                        thisMonthTransactions.toString(),
                        Icons.calendar_today,
                      ),
                    ),
                    Expanded(
                      child: _buildQuickStat(
                        'Last Activity',
                        transactions.isNotEmpty 
                          ? _formatDate(transactions.first.date)
                          : 'None',
                        Icons.access_time,
                      ),
                    ),
                  ],
                ),
                
                if (recentTransactions.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  
                  Text(
                    'Recent Transactions',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  ...recentTransactions.map((transaction) => 
                    _buildTransactionItem(transaction)
                  ),
                  
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () => _viewAllTransactions(account),
                    child: const Text('View All Transactions'),
                  ),
                ],
                
                const SizedBox(height: 16),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _addTransaction(account),
                        icon: const Icon(Icons.add),
                        label: const Text('Add Transaction'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _viewAccountDetails(account),
                        icon: const Icon(Icons.visibility),
                        label: const Text('View Details'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountDetails(Account account) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (account.description.isNotEmpty) ...[
          Text(
            'Description',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(account.description),
          const SizedBox(height: 8),
        ],
        
        Row(
          children: [
            Expanded(
              child: _buildDetailItem('Type', account.type.name.toUpperCase()),
            ),
            Expanded(
              child: _buildDetailItem('Currency', account.currency),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        Row(
          children: [
            Expanded(
              child: _buildDetailItem('Include in Total', account.includeInTotal ? 'Yes' : 'No'),
            ),
            Expanded(
              child: _buildDetailItem('Created', _formatDate(account.createdAt)),
            ),
          ],
        ),
        
        if (account.bankName != null && account.bankName!.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildDetailItem('Bank', account.bankName!),
        ],
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTransactionItem(Transaction transaction) {
    return ListTile(
      dense: true,
      leading: CircleAvatar(
        radius: 16,
        backgroundColor: transaction.amount >= 0 
          ? Colors.green.withValues(alpha: 0.2)
          : Colors.red.withValues(alpha: 0.2),
        child: Icon(
          transaction.amount >= 0 ? Icons.add : Icons.remove,
          size: 16,
          color: transaction.amount >= 0 ? Colors.green : Colors.red,
        ),
      ),
      title: Text(
        transaction.description,
        style: const TextStyle(fontSize: 14),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        _formatDate(transaction.date),
        style: const TextStyle(fontSize: 12),
      ),
      trailing: Text(
        '\$${transaction.amount.abs().toStringAsFixed(2)}',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: transaction.amount >= 0 ? Colors.green : Colors.red,
          fontSize: 14,
        ),
      ),
    );
  }

  List<Account> _filterAccounts(List<Account> accounts) {
    var filtered = accounts;
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((account) {
        return account.name.toLowerCase().contains(_searchQuery) ||
               account.description.toLowerCase().contains(_searchQuery) ||
               (account.bankName?.toLowerCase().contains(_searchQuery) ?? false);
      }).toList();
    }
    
    // Apply type filter
    switch (_selectedFilter) {
      case 'Active':
        filtered = filtered.where((a) => a.isActive).toList();
        break;
      case 'Inactive':
        filtered = filtered.where((a) => !a.isActive).toList();
        break;
      case 'Checking':
        filtered = filtered.where((a) => a.type == AccountType.checking).toList();
        break;
      case 'Savings':
        filtered = filtered.where((a) => a.type == AccountType.savings).toList();
        break;
      case 'Credit Card':
        filtered = filtered.where((a) => a.type == AccountType.credit).toList();
        break;
      case 'Investment':
        filtered = filtered.where((a) => a.type == AccountType.investment).toList();
        break;
      case 'Cash':
        filtered = filtered.where((a) => a.type == AccountType.cash).toList();
        break;
    }
    
    // Apply sorting
    switch (_sortBy) {
      case 'Name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'Balance':
        filtered.sort((a, b) => b.balance.compareTo(a.balance));
        break;
      case 'Type':
        filtered.sort((a, b) => a.type.name.compareTo(b.type.name));
        break;
      case 'Created Date':
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Last Activity':
        filtered.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        break;
    }
    
    return filtered;
  }

  void _handleAccountAction(String action, Account account) {
    switch (action) {
      case 'view':
        _viewAccountDetails(account);
        break;
      case 'edit':
        _editAccount(account);
        break;
      case 'transfer':
        _transferMoney(account);
        break;
      case 'export':
        _exportTransactions(account);
        break;
      case 'delete':
        _deleteAccount(account);
        break;
    }
  }

  void _addAccount() {
    context.push('/money-flow/account/new');
  }

  void _editAccount(Account account) {
    context.push('/money-flow/account/${account.id}/edit');
  }

  void _viewAccountDetails(Account account) {
    context.push('/money-flow/account/${account.id}');
  }

  void _addTransaction(Account account) {
    context.push('/money-flow/transaction/new?accountId=${account.id}');
  }

  void _viewAllTransactions(Account account) {
    context.push('/money-flow/account/${account.id}/transactions');
  }

  void _transferMoney(Account account) {
    // TODO: Implement money transfer functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Transfer money from ${account.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _exportTransactions(Account account) {
    // TODO: Implement transaction export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Export transactions for ${account.name}'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteAccount(Account account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text('Are you sure you want to delete "${account.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(accountsProvider.notifier).deleteAccount(account.id);
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Account "${account.name}" deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Colors.blue;
      case AccountType.savings:
        return Colors.green;
      case AccountType.credit:
        return Colors.orange;
      case AccountType.investment:
        return Colors.purple;
      case AccountType.cash:
        return Colors.teal;
      case AccountType.loan:
        return Colors.red;
      case AccountType.other:
        return Colors.grey;
    }
  }

  IconData _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.credit:
        return Icons.credit_card;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.cash:
        return Icons.money;
      case AccountType.loan:
        return Icons.account_balance_wallet;
      case AccountType.other:
        return Icons.account_balance;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
