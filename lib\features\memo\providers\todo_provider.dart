import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/todo.dart';
import '../models/todo_analytics.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Todos List Provider
final todosProvider = StateNotifierProvider<TodosNotifier, List<Todo>>((ref) {
  final userId = ref.watch(userProfileProvider.select((profile) => profile?.id.toString()));
  return TodosNotifier(userId);
});

class TodosNotifier extends StateNotifier<List<Todo>> {
  final String? userId;

  TodosNotifier(this.userId) : super([]) {
    loadTodos();
  }

  Future<void> loadTodos() async {
    final todos = await DatabaseService.instance.getAllTodos(userId: userId);
    state = todos;
  }

  Future<Todo> createTodo({
    required String title,
    String description = '',
    DateTime? dueDate,
    DateTime? startDate,
    TodoPriority priority = TodoPriority.medium,
    RecurrenceType recurrenceType = RecurrenceType.none,
    int recurrenceInterval = 1,
    String category = 'general',
    DateTime? reminderTime,
    List<String>? tags,
    int? parentTodoId,
    int estimatedMinutes = 0,
    String? location,
    String? url,
  }) async {
    final todo = Todo.create(
      title: title,
      userId: userId ?? '',
      description: description,
      dueDate: dueDate,
      startDate: startDate,
      priority: priority,
      recurrenceType: recurrenceType,
      recurrenceInterval: recurrenceInterval,
      category: category,
      reminderTime: reminderTime,
      tags: tags,
      parentTodoId: parentTodoId,
      estimatedMinutes: estimatedMinutes,
      location: location,
      url: url,
    );

    final savedTodo = await DatabaseService.instance.saveTodo(todo);
    await loadTodos(); // Refresh the list
    return savedTodo;
  }

  // Subtask management
  Future<Todo> createSubtask({
    required int parentTodoId,
    required String title,
    String description = '',
    DateTime? dueDate,
    TodoPriority priority = TodoPriority.medium,
  }) async {
    final subtask = await createTodo(
      title: title,
      description: description,
      dueDate: dueDate,
      priority: priority,
      parentTodoId: parentTodoId,
    );

    // Add subtask to parent
    final parent = state.firstWhere((t) => t.id == parentTodoId);
    parent.addSubtask(subtask.id);
    await updateTodo(parent);

    return subtask;
  }

  Future<void> removeSubtask(int parentTodoId, int subtaskId) async {
    final parent = state.firstWhere((t) => t.id == parentTodoId);
    parent.removeSubtask(subtaskId);
    await updateTodo(parent);
    await deleteTodo(subtaskId);
  }

  // Dependency management
  Future<void> addDependency(int todoId, int dependsOnId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    final dependsOn = state.firstWhere((t) => t.id == dependsOnId);

    todo.addDependency(dependsOnId);
    dependsOn.addBlocks(todoId);

    await updateTodo(todo);
    await updateTodo(dependsOn);
  }

  Future<void> removeDependency(int todoId, int dependsOnId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    final dependsOn = state.firstWhere((t) => t.id == dependsOnId);

    todo.removeDependency(dependsOnId);
    dependsOn.removeBlocks(todoId);

    await updateTodo(todo);
    await updateTodo(dependsOn);
  }

  // Status management
  Future<void> startTodo(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.start();
    await updateTodo(todo);
  }

  Future<void> pauseTodo(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.pause();
    await updateTodo(todo);
  }

  Future<void> resumeTodo(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.resume();
    await updateTodo(todo);
  }

  Future<void> cancelTodo(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.cancel();
    await updateTodo(todo);
  }

  // Progress tracking
  Future<void> updateProgress(int todoId, double progress) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.updateProgress(progress);
    await updateTodo(todo);
  }

  Future<void> addTimeSpent(int todoId, int minutes) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.addTimeSpent(minutes);
    await updateTodo(todo);
  }

  Future<void> updateTodo(Todo todo) async {
    await DatabaseService.instance.saveTodo(todo);
    await loadTodos(); // Refresh the list
  }

  Future<void> deleteTodo(int todoId) async {
    await DatabaseService.instance.deleteTodo(todoId);
    await loadTodos(); // Refresh the list
  }

  Future<void> completeTodo(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.complete();
    await updateTodo(todo);
  }

  Future<void> uncompleteTodo(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.uncomplete();
    await updateTodo(todo);
  }

  Future<void> toggleCompletion(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    if (todo.isCompleted) {
      todo.uncomplete();
    } else {
      todo.complete();
    }
    await updateTodo(todo);
  }

  // Completed Todo Management Methods
  Future<Todo> duplicateCompletedTodo(int todoId) async {
    final originalTodo = state.firstWhere((t) => t.id == todoId);

    final duplicatedTodo = Todo.create(
      title: originalTodo.title,
      userId: originalTodo.userId,
      description: originalTodo.description,
      dueDate: originalTodo.dueDate,
      startDate: originalTodo.startDate,
      priority: originalTodo.priority,
      recurrenceType: originalTodo.recurrenceType,
      recurrenceInterval: originalTodo.recurrenceInterval,
      category: originalTodo.category,
      reminderTime: originalTodo.reminderTime,
      tags: List.from(originalTodo.tags),
      parentTodoId: originalTodo.parentTodoId,
      estimatedMinutes: originalTodo.estimatedMinutes,
      location: originalTodo.location,
      url: originalTodo.url,
    );

    // Reset completion status for the duplicate
    duplicatedTodo.isCompleted = false;
    duplicatedTodo.status = TodoStatus.pending;
    duplicatedTodo.progress = 0.0;
    duplicatedTodo.completedAt = null;
    duplicatedTodo.actualMinutes = 0;

    final savedTodo = await DatabaseService.instance.saveTodo(duplicatedTodo);
    await loadTodos();
    return savedTodo;
  }

  Future<void> restoreCompletedTodo(int todoId) async {
    final todo = state.firstWhere((t) => t.id == todoId);
    todo.uncomplete();
    await updateTodo(todo);
  }

  Future<void> editCompletedTodo(Todo updatedTodo) async {
    await updateTodo(updatedTodo);
  }

  Future<void> deleteCompletedTodo(int todoId) async {
    await deleteTodo(todoId);
  }

  Future<void> clearAllCompletedTodos() async {
    final completedTodos = state.where((todo) => todo.isCompleted).toList();
    for (final todo in completedTodos) {
      await DatabaseService.instance.deleteTodo(todo.id);
    }
    await loadTodos();
  }

  List<Todo> get activeTodos => state.where((todo) => !todo.isCompleted).toList();
  List<Todo> get completedTodos => state.where((todo) => todo.isCompleted).toList();
  List<Todo> get overdueTodos => state.where((todo) => todo.isOverdue).toList();
  List<Todo> get todayTodos => state.where((todo) => todo.isDueToday).toList();
  List<Todo> get inProgressTodos => state.where((todo) => todo.status == TodoStatus.inProgress).toList();
  List<Todo> get pendingTodos => state.where((todo) => todo.status == TodoStatus.pending).toList();
  List<Todo> get onHoldTodos => state.where((todo) => todo.status == TodoStatus.onHold).toList();
  List<Todo> get parentTodos => state.where((todo) => todo.parentTodoId == null).toList();

  List<Todo> getTodosByPriority(TodoPriority priority) {
    return state.where((todo) => todo.priority == priority && !todo.isCompleted).toList();
  }

  List<Todo> getTodosByCategory(String category) {
    return state.where((todo) => todo.category == category).toList();
  }

  List<Todo> getTodosByStatus(TodoStatus status) {
    return state.where((todo) => todo.status == status).toList();
  }

  List<Todo> getSubtasks(int parentTodoId) {
    return state.where((todo) => todo.parentTodoId == parentTodoId).toList();
  }

  List<Todo> getDependencies(int todoId) {
    final todo = state.firstWhere((t) => t.id == todoId);
    return state.where((t) => todo.dependsOnIds.contains(t.id)).toList();
  }

  List<Todo> getBlockedTodos(int todoId) {
    final todo = state.firstWhere((t) => t.id == todoId);
    return state.where((t) => todo.blocksIds.contains(t.id)).toList();
  }

  bool canStartTodo(int todoId) {
    final todo = state.firstWhere((t) => t.id == todoId);
    if (todo.hasDependencies) {
      final dependencies = getDependencies(todoId);
      return dependencies.every((dep) => dep.isCompleted);
    }
    return true;
  }
}

// Todo Filter Provider
enum TodoFilter { all, active, completed, overdue, today, inProgress, pending, onHold, subtasks }

final todoFilterProvider = StateProvider<TodoFilter>((ref) => TodoFilter.active);

// Active Todos Provider (non-completed)
final activeTodosProvider = Provider<List<Todo>>((ref) {
  final todos = ref.watch(todosProvider);
  return todos.where((todo) => !todo.isCompleted && !todo.isDeleted).toList();
});

// Completed Todos Provider
final completedTodosProvider = Provider<List<Todo>>((ref) {
  final todos = ref.watch(todosProvider);
  return todos.where((todo) => todo.isCompleted && !todo.isDeleted).toList()
    ..sort((a, b) => (b.completedAt ?? DateTime.now()).compareTo(a.completedAt ?? DateTime.now()));
});

// Filtered Todos Provider
final filteredTodosProvider = Provider<List<Todo>>((ref) {
  final todos = ref.watch(todosProvider);
  final filter = ref.watch(todoFilterProvider);
  final searchTerm = ref.watch(todoSearchProvider);
  
  List<Todo> filteredList;
  
  switch (filter) {
    case TodoFilter.all:
      filteredList = todos;
      break;
    case TodoFilter.active:
      filteredList = todos.where((todo) => !todo.isCompleted).toList();
      break;
    case TodoFilter.completed:
      filteredList = todos.where((todo) => todo.isCompleted).toList();
      break;
    case TodoFilter.overdue:
      filteredList = todos.where((todo) => todo.isOverdue).toList();
      break;
    case TodoFilter.today:
      filteredList = todos.where((todo) => todo.isDueToday).toList();
      break;
    case TodoFilter.inProgress:
      filteredList = todos.where((todo) => todo.status == TodoStatus.inProgress).toList();
      break;
    case TodoFilter.pending:
      filteredList = todos.where((todo) => todo.status == TodoStatus.pending).toList();
      break;
    case TodoFilter.onHold:
      filteredList = todos.where((todo) => todo.status == TodoStatus.onHold).toList();
      break;
    case TodoFilter.subtasks:
      filteredList = todos.where((todo) => todo.isSubtask).toList();
      break;
  }
  
  if (searchTerm.isNotEmpty) {
    filteredList = filteredList.where((todo) => todo.containsSearchTerm(searchTerm)).toList();
  }
  
  return filteredList;
});

// Search Provider
final todoSearchProvider = StateProvider<String>((ref) => '');

// Selected Todo Provider (for editing)
final selectedTodoProvider = StateProvider<Todo?>((ref) => null);

// Todo Categories Provider
final todoCategoriesProvider = Provider<List<String>>((ref) => [
  'general',
  'work',
  'personal',
  'shopping',
  'health',
  'finance',
  'education',
  'travel',
  'home',
  'family',
]);

// Priority Filter Provider
final priorityFilterProvider = StateProvider<TodoPriority?>((ref) => null);

// Category Filter Provider
final categoryFilterProvider = StateProvider<String?>((ref) => null);

// Todo Statistics Provider
final todoStatsProvider = Provider<Map<String, int>>((ref) {
  final todos = ref.watch(todosProvider);

  return {
    'total': todos.length,
    'active': todos.where((todo) => !todo.isCompleted).length,
    'completed': todos.where((todo) => todo.isCompleted).length,
    'overdue': todos.where((todo) => todo.isOverdue).length,
    'today': todos.where((todo) => todo.isDueToday).length,
    'inProgress': todos.where((todo) => todo.status == TodoStatus.inProgress).length,
    'pending': todos.where((todo) => todo.status == TodoStatus.pending).length,
    'onHold': todos.where((todo) => todo.status == TodoStatus.onHold).length,
    'cancelled': todos.where((todo) => todo.status == TodoStatus.cancelled).length,
    'high_priority': todos.where((todo) => todo.priority == TodoPriority.high && !todo.isCompleted).length,
    'urgent': todos.where((todo) => todo.priority == TodoPriority.urgent && !todo.isCompleted).length,
    'subtasks': todos.where((todo) => todo.isSubtask).length,
    'parent_todos': todos.where((todo) => todo.hasSubtasks).length,
  };
});

// Todo Analytics Provider
final todoAnalyticsProvider = Provider<TodoAnalytics>((ref) {
  final todos = ref.watch(todosProvider);
  final now = DateTime.now();
  final startOfMonth = DateTime(now.year, now.month, 1);
  final endOfMonth = DateTime(now.year, now.month + 1, 0);

  return TodoAnalytics(
    todos: todos,
    startDate: startOfMonth,
    endDate: endOfMonth,
  );
});

// Weekly Analytics Provider
final weeklyAnalyticsProvider = Provider<TodoAnalytics>((ref) {
  final todos = ref.watch(todosProvider);
  final now = DateTime.now();
  final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
  final endOfWeek = startOfWeek.add(const Duration(days: 6));

  return TodoAnalytics(
    todos: todos,
    startDate: startOfWeek,
    endDate: endOfWeek,
  );
});

// Productivity Score Provider
final productivityScoreProvider = Provider<double>((ref) {
  final analytics = ref.watch(todoAnalyticsProvider);
  return analytics.productivityScore;
});

// Insights Provider
final todoInsightsProvider = Provider<List<String>>((ref) {
  final analytics = ref.watch(todoAnalyticsProvider);
  return analytics.insights;
});

// Recommendations Provider
final todoRecommendationsProvider = Provider<List<String>>((ref) {
  final analytics = ref.watch(todoAnalyticsProvider);
  return analytics.recommendations;
});

// Due Soon Provider (next 7 days)
final dueSoonTodosProvider = Provider<List<Todo>>((ref) {
  final todos = ref.watch(todosProvider);
  final now = DateTime.now();
  final weekFromNow = now.add(const Duration(days: 7));
  
  return todos.where((todo) {
    if (todo.isCompleted || todo.dueDate == null) return false;
    return todo.dueDate!.isAfter(now) && todo.dueDate!.isBefore(weekFromNow);
  }).toList();
});
