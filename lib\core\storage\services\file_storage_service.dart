import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/file_info.dart';

class FileStorageService {
  static final FileStorageService _instance = FileStorageService._internal();
  factory FileStorageService() => _instance;
  FileStorageService._internal();

  static const String _filesKey = 'managed_files';
  static const String _settingsKey = 'storage_settings';

  final List<FileInfo> _files = [];
  final StreamController<List<FileInfo>> _filesController = 
      StreamController<List<FileInfo>>.broadcast();
  final StreamController<FileInfo> _fileUpdatedController = 
      StreamController<FileInfo>.broadcast();

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  
  // Storage directories
  late Directory _appDocumentsDir;
  late Directory _voiceMemosDir;
  late Directory _noteExportsDir;
  late Directory _backupsDir;
  late Directory _tempDir;

  // Storage settings
  int _maxFileSize = 100 * 1024 * 1024; // 100MB
  int _maxTotalStorage = 1024 * 1024 * 1024; // 1GB
  bool _autoCleanup = true;
  int _cleanupAfterDays = 30;
  bool _compressBackups = true;

  // Getters
  Stream<List<FileInfo>> get filesStream => _filesController.stream;
  Stream<FileInfo> get fileUpdatedStream => _fileUpdatedController.stream;
  List<FileInfo> get files => List.unmodifiable(_files);
  List<FileInfo> get voiceMemos => _files.where((f) => f.type == FileType.voiceMemo).toList();
  List<FileInfo> get noteExports => _files.where((f) => f.type == FileType.noteExport).toList();
  List<FileInfo> get backups => _files.where((f) => f.type == FileType.backup).toList();
  
  int get totalFiles => _files.length;
  int get totalSizeBytes => _files.fold(0, (sum, file) => sum + file.sizeBytes);
  String get formattedTotalSize => _formatBytes(totalSizeBytes);
  double get storageUsagePercentage => totalSizeBytes / _maxTotalStorage;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _initializeDirectories();
      await _loadFiles();
      await _loadSettings();
      _isInitialized = true;
      debugPrint('FileStorageService initialized');
    } catch (e) {
      debugPrint('Error initializing FileStorageService: $e');
    }
  }

  // File management
  Future<FileInfo> saveFile({
    required String name,
    required FileType type,
    required List<int> data,
    String? associatedId,
    String? associatedType,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final directory = _getDirectoryForType(type);
      final filePath = '${directory.path}/$name';
      final file = File(filePath);
      
      // Check file size
      if (data.length > _maxFileSize) {
        throw Exception('File size exceeds maximum allowed size');
      }
      
      // Check total storage
      if (totalSizeBytes + data.length > _maxTotalStorage) {
        if (_autoCleanup) {
          await _performCleanup();
        } else {
          throw Exception('Storage limit exceeded');
        }
      }

      await file.writeAsBytes(data);
      
      final fileInfo = FileInfo(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        path: filePath,
        type: type,
        sizeBytes: data.length,
        associatedId: associatedId,
        associatedType: associatedType,
        metadata: metadata,
      );

      await fileInfo.calculateChecksum();
      
      _files.add(fileInfo);
      await _saveFiles();
      _filesController.add(_files);
      _fileUpdatedController.add(fileInfo);
      
      debugPrint('File saved: $name (${fileInfo.formattedSize})');
      return fileInfo;
    } catch (e) {
      debugPrint('Error saving file: $e');
      rethrow;
    }
  }

  Future<List<int>?> readFile(String fileId) async {
    try {
      final fileInfo = _files.firstWhere((f) => f.id == fileId);
      final file = File(fileInfo.path);
      
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      debugPrint('Error reading file: $e');
      return null;
    }
  }

  Future<bool> deleteFile(String fileId) async {
    try {
      final fileInfo = _files.firstWhere((f) => f.id == fileId);
      final deleted = await fileInfo.delete();
      
      if (deleted) {
        _files.removeWhere((f) => f.id == fileId);
        await _saveFiles();
        _filesController.add(_files);
        debugPrint('File deleted: ${fileInfo.name}');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  Future<bool> moveFile(String fileId, String newName) async {
    try {
      final fileInfo = _files.firstWhere((f) => f.id == fileId);
      final directory = _getDirectoryForType(fileInfo.type);
      final newPath = '${directory.path}/$newName';
      
      final moved = await fileInfo.move(newPath);
      if (moved) {
        fileInfo.name = newName;
        await _saveFiles();
        _filesController.add(_files);
        _fileUpdatedController.add(fileInfo);
        debugPrint('File moved: ${fileInfo.name}');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error moving file: $e');
      return false;
    }
  }

  Future<FileInfo?> copyFile(String fileId, String newName) async {
    try {
      final originalFile = _files.firstWhere((f) => f.id == fileId);
      final directory = _getDirectoryForType(originalFile.type);
      final newPath = '${directory.path}/$newName';
      
      final copied = await originalFile.copy(newPath);
      if (copied) {
        final newFileInfo = originalFile.copyWith(
          name: newName,
          path: newPath,
        );
        newFileInfo.id = DateTime.now().millisecondsSinceEpoch.toString();
        
        _files.add(newFileInfo);
        await _saveFiles();
        _filesController.add(_files);
        _fileUpdatedController.add(newFileInfo);
        
        debugPrint('File copied: $newName');
        return newFileInfo;
      }
      return null;
    } catch (e) {
      debugPrint('Error copying file: $e');
      return null;
    }
  }

  // Export and import
  Future<FileInfo> exportNote({
    required String noteId,
    required String noteTitle,
    required String content,
    required String format, // 'md', 'txt', 'pdf'
  }) async {
    try {
      final fileName = '${noteTitle.replaceAll(RegExp(r'[^\w\s-]'), '')}_${DateTime.now().millisecondsSinceEpoch}.$format';
      final data = utf8.encode(content);
      
      return await saveFile(
        name: fileName,
        type: FileType.noteExport,
        data: data,
        associatedId: noteId,
        associatedType: 'note',
        metadata: {
          'format': format,
          'exportedAt': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('Error exporting note: $e');
      rethrow;
    }
  }

  Future<FileInfo> createBackup({
    required Map<String, dynamic> data,
    String? version,
  }) async {
    try {
      final backupVersion = version ?? DateTime.now().toIso8601String();
      final fileName = 'backup_${DateTime.now().millisecondsSinceEpoch}.json';
      final jsonData = utf8.encode(json.encode(data));
      
      return await saveFile(
        name: fileName,
        type: FileType.backup,
        data: jsonData,
        metadata: {
          'version': backupVersion,
          'createdAt': DateTime.now().toIso8601String(),
          'compressed': _compressBackups,
        },
      );
    } catch (e) {
      debugPrint('Error creating backup: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> restoreBackup(String fileId) async {
    try {
      final data = await readFile(fileId);
      if (data != null) {
        final jsonString = utf8.decode(data);
        return json.decode(jsonString);
      }
      return null;
    } catch (e) {
      debugPrint('Error restoring backup: $e');
      return null;
    }
  }

  // Cleanup and maintenance
  Future<void> performCleanup() async {
    await _performCleanup();
  }

  Future<void> _performCleanup() async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: _cleanupAfterDays));
      final filesToDelete = _files.where((f) => 
          f.createdAt.isBefore(cutoffDate) && 
          !f.isShared && 
          f.type != FileType.backup).toList();

      for (final file in filesToDelete) {
        await deleteFile(file.id);
      }

      debugPrint('Cleanup completed: ${filesToDelete.length} files removed');
    } catch (e) {
      debugPrint('Error during cleanup: $e');
    }
  }

  Future<void> optimizeStorage() async {
    try {
      // Remove duplicate files
      final duplicates = <String, List<FileInfo>>{};
      
      for (final file in _files) {
        if (file.checksum != null) {
          duplicates.putIfAbsent(file.checksum!, () => []).add(file);
        }
      }

      int removedCount = 0;
      for (final group in duplicates.values) {
        if (group.length > 1) {
          // Keep the newest file, remove others
          group.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          for (int i = 1; i < group.length; i++) {
            await deleteFile(group[i].id);
            removedCount++;
          }
        }
      }

      debugPrint('Storage optimized: $removedCount duplicate files removed');
    } catch (e) {
      debugPrint('Error optimizing storage: $e');
    }
  }

  // Settings
  Future<void> updateSettings({
    int? maxFileSize,
    int? maxTotalStorage,
    bool? autoCleanup,
    int? cleanupAfterDays,
    bool? compressBackups,
  }) async {
    try {
      if (maxFileSize != null) _maxFileSize = maxFileSize;
      if (maxTotalStorage != null) _maxTotalStorage = maxTotalStorage;
      if (autoCleanup != null) _autoCleanup = autoCleanup;
      if (cleanupAfterDays != null) _cleanupAfterDays = cleanupAfterDays;
      if (compressBackups != null) _compressBackups = compressBackups;

      await _saveSettings();
    } catch (e) {
      debugPrint('Error updating storage settings: $e');
    }
  }

  // File search and filtering
  List<FileInfo> searchFiles(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _files.where((file) =>
        file.name.toLowerCase().contains(lowercaseQuery) ||
        (file.metadata?.toString().toLowerCase().contains(lowercaseQuery) ?? false)).toList();
  }

  List<FileInfo> getFilesByType(FileType type) {
    return _files.where((file) => file.type == type).toList();
  }

  List<FileInfo> getFilesByAssociation(String associatedId, String associatedType) {
    return _files.where((file) =>
        file.associatedId == associatedId && file.associatedType == associatedType).toList();
  }

  // Private methods
  Future<void> _initializeDirectories() async {
    _appDocumentsDir = await getApplicationDocumentsDirectory();
    
    _voiceMemosDir = Directory('${_appDocumentsDir.path}/voice_memos');
    _noteExportsDir = Directory('${_appDocumentsDir.path}/note_exports');
    _backupsDir = Directory('${_appDocumentsDir.path}/backups');
    _tempDir = Directory('${_appDocumentsDir.path}/temp');

    // Create directories if they don't exist
    for (final dir in [_voiceMemosDir, _noteExportsDir, _backupsDir, _tempDir]) {
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
    }
  }

  Directory _getDirectoryForType(FileType type) {
    switch (type) {
      case FileType.voiceMemo:
        return _voiceMemosDir;
      case FileType.noteExport:
        return _noteExportsDir;
      case FileType.backup:
        return _backupsDir;
      default:
        return _appDocumentsDir;
    }
  }

  Future<void> _loadFiles() async {
    try {
      final filesJson = _prefs?.getString(_filesKey);
      if (filesJson != null) {
        final List<dynamic> filesList = json.decode(filesJson);
        _files.clear();
        _files.addAll(
          filesList.map((json) => FileInfo.fromJson(json))
        );
        _filesController.add(_files);
      }
    } catch (e) {
      debugPrint('Error loading files: $e');
    }
  }

  Future<void> _saveFiles() async {
    try {
      final filesJson = json.encode(
        _files.map((f) => f.toJson()).toList()
      );
      await _prefs?.setString(_filesKey, filesJson);
    } catch (e) {
      debugPrint('Error saving files: $e');
    }
  }

  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs?.getString(_settingsKey);
      if (settingsJson != null) {
        final Map<String, dynamic> settings = json.decode(settingsJson);
        _maxFileSize = settings['maxFileSize'] ?? _maxFileSize;
        _maxTotalStorage = settings['maxTotalStorage'] ?? _maxTotalStorage;
        _autoCleanup = settings['autoCleanup'] ?? _autoCleanup;
        _cleanupAfterDays = settings['cleanupAfterDays'] ?? _cleanupAfterDays;
        _compressBackups = settings['compressBackups'] ?? _compressBackups;
      }
    } catch (e) {
      debugPrint('Error loading storage settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final settings = {
        'maxFileSize': _maxFileSize,
        'maxTotalStorage': _maxTotalStorage,
        'autoCleanup': _autoCleanup,
        'cleanupAfterDays': _cleanupAfterDays,
        'compressBackups': _compressBackups,
      };
      
      final settingsJson = json.encode(settings);
      await _prefs?.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving storage settings: $e');
    }
  }

  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Statistics
  Map<String, dynamic> getStorageStats() {
    final typeStats = <String, int>{};
    final typeSizes = <String, int>{};

    for (final file in _files) {
      final typeName = file.type.name;
      typeStats[typeName] = (typeStats[typeName] ?? 0) + 1;
      typeSizes[typeName] = (typeSizes[typeName] ?? 0) + file.sizeBytes;
    }

    return {
      'totalFiles': totalFiles,
      'totalSize': totalSizeBytes,
      'formattedTotalSize': formattedTotalSize,
      'usagePercentage': storageUsagePercentage,
      'typeStats': typeStats,
      'typeSizes': typeSizes,
      'maxFileSize': _maxFileSize,
      'maxTotalStorage': _maxTotalStorage,
      'autoCleanup': _autoCleanup,
      'cleanupAfterDays': _cleanupAfterDays,
    };
  }

  // Dispose resources
  void dispose() {
    _filesController.close();
    _fileUpdatedController.close();
  }
}
