import 'package:flutter/material.dart';
import '../models/mobile_app_theme.dart';

/// Service for managing mobile app themes and templates
class MobileAppThemeService {
  static final MobileAppThemeService _instance = MobileAppThemeService._internal();
  factory MobileAppThemeService() => _instance;
  MobileAppThemeService._internal();

  final List<MobileAppTheme> _themes = [];
  final List<MobileAppTemplate> _templates = [];

  /// Initialize with default themes and templates
  void initialize() {
    _initializeDefaultThemes();
    _initializeDefaultTemplates();
  }

  /// Get all available themes
  List<MobileAppTheme> getThemes() => List.unmodifiable(_themes);

  /// Get themes by type
  List<MobileAppTheme> getThemesByType(MobileAppThemeType type) {
    return _themes.where((theme) => theme.type == type).toList();
  }

  /// Get theme by ID
  MobileAppTheme? getThemeById(String id) {
    try {
      return _themes.firstWhere((theme) => theme.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get all available templates
  List<MobileAppTemplate> getTemplates() => List.unmodifiable(_templates);

  /// Get templates by layout type
  List<MobileAppTemplate> getTemplatesByLayout(MobileAppLayoutType layoutType) {
    return _templates.where((template) => template.layoutType == layoutType).toList();
  }

  /// Get template by ID
  MobileAppTemplate? getTemplateById(String id) {
    try {
      return _templates.firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Add custom theme
  void addCustomTheme(MobileAppTheme theme) {
    _themes.add(theme);
  }

  /// Add custom template
  void addCustomTemplate(MobileAppTemplate template) {
    _templates.add(template);
  }

  /// Initialize default themes
  void _initializeDefaultThemes() {
    // Material Design Android Theme
    _themes.add(MobileAppTheme(
      id: 'material_android',
      name: 'Material Android',
      description: 'Modern Material Design 3 theme for Android apps',
      type: MobileAppThemeType.android,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w400),
        displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
        displaySmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
        headlineLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500),
        headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        headlineSmall: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
        titleLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
      ),
      bottomNavTheme: const BottomNavigationBarThemeData(),
      drawerTheme: const NavigationDrawerThemeData(),
      cardTheme: const CardThemeData(
        elevation: 1,
        margin: EdgeInsets.all(8),
      ),
      buttonTheme: const ButtonThemeData(),
      inputTheme: const InputDecorationTheme(
        border: OutlineInputBorder(),
        filled: true,
      ),
    ));

    // iOS Cupertino Theme
    _themes.add(MobileAppTheme(
      id: 'cupertino_ios',
      name: 'Cupertino iOS',
      description: 'Native iOS design theme with Cupertino styling',
      type: MobileAppThemeType.ios,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 34, fontWeight: FontWeight.w700),
        displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w700),
        displaySmall: TextStyle(fontSize: 22, fontWeight: FontWeight.w700),
        headlineLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
        headlineMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        headlineSmall: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        titleLarge: TextStyle(fontSize: 17, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(fontSize: 17, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 15, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 13, fontWeight: FontWeight.w400),
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(fontSize: 17, fontWeight: FontWeight.w600),
      ),
      bottomNavTheme: const BottomNavigationBarThemeData(),
      drawerTheme: const NavigationDrawerThemeData(),
      cardTheme: const CardThemeData(
        elevation: 0,
        margin: EdgeInsets.all(8),
      ),
      buttonTheme: const ButtonThemeData(),
      inputTheme: const InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        filled: true,
      ),
    ));

    // Modern Web Theme
    _themes.add(MobileAppTheme(
      id: 'modern_web',
      name: 'Modern Web',
      description: 'Clean, modern theme optimized for web applications',
      type: MobileAppThemeType.web,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.indigo,
        brightness: Brightness.light,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 48, fontWeight: FontWeight.w300),
        displayMedium: TextStyle(fontSize: 36, fontWeight: FontWeight.w300),
        displaySmall: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
        headlineLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.w500),
        headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        headlineSmall: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
        titleLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      ),
      appBarTheme: const AppBarTheme(
        elevation: 1,
        centerTitle: false,
        titleTextStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
      ),
      bottomNavTheme: const BottomNavigationBarThemeData(),
      drawerTheme: const NavigationDrawerThemeData(),
      cardTheme: const CardThemeData(
        elevation: 2,
        margin: EdgeInsets.all(12),
      ),
      buttonTheme: const ButtonThemeData(),
      inputTheme: const InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        filled: true,
      ),
    ));

    // Dark Theme
    _themes.add(MobileAppTheme(
      id: 'dark_modern',
      name: 'Dark Modern',
      description: 'Sleek dark theme for modern applications',
      type: MobileAppThemeType.custom,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.purple,
        brightness: Brightness.dark,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w400),
        displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w400),
        displaySmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400),
        headlineLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500),
        headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        headlineSmall: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
        titleLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
      ),
      bottomNavTheme: const BottomNavigationBarThemeData(),
      drawerTheme: const NavigationDrawerThemeData(),
      cardTheme: const CardThemeData(
        elevation: 4,
        margin: EdgeInsets.all(8),
      ),
      buttonTheme: const ButtonThemeData(),
      inputTheme: const InputDecorationTheme(
        border: OutlineInputBorder(),
        filled: true,
      ),
    ));
  }

  /// Initialize default templates
  void _initializeDefaultTemplates() {
    // Calculator App Template
    _templates.add(MobileAppTemplate(
      id: 'calculator_app',
      name: 'Calculator App',
      description: 'Professional calculator with formula support',
      layoutType: MobileAppLayoutType.singlePage,
      themeType: MobileAppThemeType.android,
      components: [
        MobileComponentData(
          id: 'main_scaffold',
          type: MobileComponentType.scaffold,
          label: 'Main Screen',
          children: [
            MobileComponentData(
              id: 'app_bar',
              type: MobileComponentType.appBar,
              label: 'Calculator',
              properties: {'title': 'Calculator', 'centerTitle': true},
            ),
            MobileComponentData(
              id: 'display',
              type: MobileComponentType.formulaOutput,
              label: 'Display',
              cellBinding: 'A1',
              properties: {'fontSize': 24, 'alignment': 'right'},
            ),
            MobileComponentData(
              id: 'button_grid',
              type: MobileComponentType.column,
              label: 'Button Grid',
              children: [
                // Number buttons would be added here
              ],
            ),
          ],
        ),
      ],
      configuration: {
        'allowFormulas': true,
        'showHistory': true,
        'scientificMode': false,
      },
    ));

    // Dashboard App Template
    _templates.add(MobileAppTemplate(
      id: 'dashboard_app',
      name: 'Dashboard App',
      description: 'Business dashboard with charts and KPIs',
      layoutType: MobileAppLayoutType.dashboard,
      themeType: MobileAppThemeType.web,
      components: [
        MobileComponentData(
          id: 'main_scaffold',
          type: MobileComponentType.scaffold,
          label: 'Dashboard',
          children: [
            MobileComponentData(
              id: 'app_bar',
              type: MobileComponentType.appBar,
              label: 'Business Dashboard',
              properties: {'title': 'Dashboard', 'actions': ['settings', 'refresh']},
            ),
            MobileComponentData(
              id: 'kpi_row',
              type: MobileComponentType.row,
              label: 'KPI Cards',
              children: [
                MobileComponentData(
                  id: 'revenue_card',
                  type: MobileComponentType.card,
                  label: 'Revenue',
                  cellBinding: 'B1',
                  properties: {'title': 'Total Revenue', 'icon': 'attach_money'},
                ),
                MobileComponentData(
                  id: 'users_card',
                  type: MobileComponentType.card,
                  label: 'Users',
                  cellBinding: 'B2',
                  properties: {'title': 'Active Users', 'icon': 'people'},
                ),
              ],
            ),
            MobileComponentData(
              id: 'chart_section',
              type: MobileComponentType.chart,
              label: 'Sales Chart',
              cellBinding: 'C1:C12',
              properties: {'chartType': 'line', 'title': 'Monthly Sales'},
            ),
          ],
        ),
      ],
      configuration: {
        'refreshInterval': 30,
        'showNotifications': true,
        'allowExport': true,
      },
    ));

    // Form App Template
    _templates.add(MobileAppTemplate(
      id: 'form_app',
      name: 'Form App',
      description: 'Data collection form with validation',
      layoutType: MobileAppLayoutType.form,
      themeType: MobileAppThemeType.android,
      components: [
        MobileComponentData(
          id: 'main_scaffold',
          type: MobileComponentType.scaffold,
          label: 'Data Form',
          children: [
            MobileComponentData(
              id: 'app_bar',
              type: MobileComponentType.appBar,
              label: 'Data Entry',
              properties: {'title': 'New Entry', 'showBack': true},
            ),
            MobileComponentData(
              id: 'form_column',
              type: MobileComponentType.column,
              label: 'Form Fields',
              children: [
                MobileComponentData(
                  id: 'name_field',
                  type: MobileComponentType.textField,
                  label: 'Name',
                  cellBinding: 'A1',
                  properties: {'hint': 'Enter your name', 'required': true},
                ),
                MobileComponentData(
                  id: 'email_field',
                  type: MobileComponentType.textField,
                  label: 'Email',
                  cellBinding: 'A2',
                  properties: {'hint': 'Enter email address', 'keyboardType': 'email'},
                ),
                MobileComponentData(
                  id: 'submit_button',
                  type: MobileComponentType.button,
                  label: 'Submit',
                  properties: {'text': 'Submit Form', 'style': 'elevated'},
                ),
              ],
            ),
          ],
        ),
      ],
      configuration: {
        'validateOnSubmit': true,
        'saveLocally': true,
        'showProgress': true,
      },
    ));

    // List App Template
    _templates.add(MobileAppTemplate(
      id: 'list_app',
      name: 'List App',
      description: 'Scrollable list with search and filter',
      layoutType: MobileAppLayoutType.list,
      themeType: MobileAppThemeType.ios,
      components: [
        MobileComponentData(
          id: 'main_scaffold',
          type: MobileComponentType.scaffold,
          label: 'Item List',
          children: [
            MobileComponentData(
              id: 'app_bar',
              type: MobileComponentType.appBar,
              label: 'Items',
              properties: {'title': 'My Items', 'searchable': true},
            ),
            MobileComponentData(
              id: 'list_view',
              type: MobileComponentType.column,
              label: 'List Items',
              children: [
                MobileComponentData(
                  id: 'list_tile_template',
                  type: MobileComponentType.listTile,
                  label: 'Item Template',
                  cellBinding: 'A1:C100',
                  properties: {'showSubtitle': true, 'showTrailing': true},
                ),
              ],
            ),
            MobileComponentData(
              id: 'fab',
              type: MobileComponentType.floatingActionButton,
              label: 'Add Item',
              properties: {'icon': 'add', 'tooltip': 'Add new item'},
            ),
          ],
        ),
      ],
      configuration: {
        'allowSearch': true,
        'allowFilter': true,
        'allowSort': true,
      },
    ));

    // Tabbed App Template
    _templates.add(MobileAppTemplate(
      id: 'tabbed_app',
      name: 'Tabbed App',
      description: 'Multi-tab application with navigation',
      layoutType: MobileAppLayoutType.tabbed,
      themeType: MobileAppThemeType.android,
      components: [
        MobileComponentData(
          id: 'main_scaffold',
          type: MobileComponentType.scaffold,
          label: 'Tabbed App',
          children: [
            MobileComponentData(
              id: 'app_bar',
              type: MobileComponentType.appBar,
              label: 'Multi-Tab App',
              properties: {'title': 'My App', 'showTabs': true},
            ),
            MobileComponentData(
              id: 'tab_bar',
              type: MobileComponentType.tabBar,
              label: 'Tab Navigation',
              properties: {'tabs': ['Home', 'Data', 'Settings']},
            ),
          ],
        ),
      ],
      configuration: {
        'tabCount': 3,
        'persistTabs': true,
        'showTabIcons': true,
      },
    ));
  }
}
