import 'package:flutter/material.dart';

class RichTextEditor extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode? focusNode;
  final VoidCallback? onChanged;
  final String? hintText;
  final bool enabled;

  const RichTextEditor({
    super.key,
    required this.controller,
    this.focusNode,
    this.onChanged,
    this.hintText,
    this.enabled = true,
  });

  @override
  State<RichTextEditor> createState() => _RichTextEditorState();
}

class _RichTextEditorState extends State<RichTextEditor> {
  bool _showToolbar = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _toolbarOverlay;

  @override
  void initState() {
    super.initState();
    widget.focusNode?.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    widget.focusNode?.removeListener(_onFocusChanged);
    _removeToolbar();
    super.dispose();
  }

  void _onFocusChanged() {
    if (widget.focusNode?.hasFocus == true) {
      _showFormattingToolbar();
    } else {
      _hideFormattingToolbar();
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Column(
        children: [
          if (_showToolbar) _buildQuickToolbar(),
          Expanded(
            child: TextField(
              controller: widget.controller,
              focusNode: widget.focusNode,
              enabled: widget.enabled,
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Start writing...',
                border: InputBorder.none,
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 16,
                ),
              ),
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
              ),
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              textCapitalization: TextCapitalization.sentences,
              keyboardType: TextInputType.multiline,
              onChanged: (value) => widget.onChanged?.call(),
              onTap: _showFormattingToolbar,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickToolbar() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          _buildToolbarButton(
            icon: Icons.format_bold,
            tooltip: 'Bold',
            onPressed: () => _insertFormatting('**', '**'),
          ),
          _buildToolbarButton(
            icon: Icons.format_italic,
            tooltip: 'Italic',
            onPressed: () => _insertFormatting('*', '*'),
          ),
          _buildToolbarButton(
            icon: Icons.format_underlined,
            tooltip: 'Underline',
            onPressed: () => _insertFormatting('<u>', '</u>'),
          ),
          const VerticalDivider(width: 16),
          _buildToolbarButton(
            icon: Icons.format_list_bulleted,
            tooltip: 'Bullet List',
            onPressed: () => _insertListItem('• '),
          ),
          _buildToolbarButton(
            icon: Icons.format_list_numbered,
            tooltip: 'Numbered List',
            onPressed: () => _insertListItem('1. '),
          ),
          const VerticalDivider(width: 16),
          _buildToolbarButton(
            icon: Icons.link,
            tooltip: 'Insert Link',
            onPressed: _insertLink,
          ),
          _buildToolbarButton(
            icon: Icons.code,
            tooltip: 'Code',
            onPressed: () => _insertFormatting('`', '`'),
          ),
          const Spacer(),
          _buildToolbarButton(
            icon: Icons.keyboard_hide,
            tooltip: 'Hide Toolbar',
            onPressed: _hideFormattingToolbar,
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return IconButton(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      tooltip: tooltip,
      padding: const EdgeInsets.all(4),
      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
    );
  }

  void _showFormattingToolbar() {
    if (!_showToolbar) {
      setState(() {
        _showToolbar = true;
      });
    }
  }

  void _hideFormattingToolbar() {
    if (_showToolbar) {
      setState(() {
        _showToolbar = false;
      });
    }
  }

  void _removeToolbar() {
    _toolbarOverlay?.remove();
    _toolbarOverlay = null;
  }

  void _insertFormatting(String startTag, String endTag) {
    final text = widget.controller.text;
    final selection = widget.controller.selection;
    
    if (selection.isValid) {
      final selectedText = selection.textInside(text);
      final newText = text.replaceRange(
        selection.start,
        selection.end,
        '$startTag$selectedText$endTag',
      );
      
      widget.controller.text = newText;
      widget.controller.selection = TextSelection.collapsed(
        offset: selection.start + startTag.length + selectedText.length + endTag.length,
      );
    } else {
      final cursorPos = selection.baseOffset;
      final newText = text.replaceRange(
        cursorPos,
        cursorPos,
        '$startTag$endTag',
      );
      
      widget.controller.text = newText;
      widget.controller.selection = TextSelection.collapsed(
        offset: cursorPos + startTag.length,
      );
    }
    
    widget.onChanged?.call();
  }

  void _insertListItem(String prefix) {
    final text = widget.controller.text;
    final selection = widget.controller.selection;
    final cursorPos = selection.baseOffset;
    
    // Find the start of the current line
    int lineStart = cursorPos;
    while (lineStart > 0 && text[lineStart - 1] != '\n') {
      lineStart--;
    }
    
    // Insert the list prefix at the beginning of the line
    final newText = text.replaceRange(lineStart, lineStart, prefix);
    widget.controller.text = newText;
    widget.controller.selection = TextSelection.collapsed(
      offset: cursorPos + prefix.length,
    );
    
    widget.onChanged?.call();
  }

  void _insertLink() {
    showDialog(
      context: context,
      builder: (context) => _LinkDialog(
        onInsert: (linkText, url) {
          final linkMarkdown = '[$linkText]($url)';
          final currentText = widget.controller.text;
          final selection = widget.controller.selection;
          final cursorPos = selection.baseOffset;

          final newText = currentText.replaceRange(cursorPos, cursorPos, linkMarkdown);
          widget.controller.text = newText;
          widget.controller.selection = TextSelection.collapsed(
            offset: cursorPos + linkMarkdown.length,
          );
          
          widget.onChanged?.call();
        },
      ),
    );
  }
}

class _LinkDialog extends StatefulWidget {
  final Function(String text, String url) onInsert;

  const _LinkDialog({required this.onInsert});

  @override
  State<_LinkDialog> createState() => _LinkDialogState();
}

class _LinkDialogState extends State<_LinkDialog> {
  final TextEditingController _textController = TextEditingController();
  final TextEditingController _urlController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Insert Link'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _textController,
            decoration: const InputDecoration(
              labelText: 'Link Text',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _urlController,
            decoration: const InputDecoration(
              labelText: 'URL',
              border: OutlineInputBorder(),
              hintText: 'https://example.com',
            ),
            keyboardType: TextInputType.url,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _canInsert() ? _insertLink : null,
          child: const Text('Insert'),
        ),
      ],
    );
  }

  bool _canInsert() {
    return _textController.text.isNotEmpty && _urlController.text.isNotEmpty;
  }

  void _insertLink() {
    widget.onInsert(_textController.text, _urlController.text);
    Navigator.of(context).pop();
  }
}
