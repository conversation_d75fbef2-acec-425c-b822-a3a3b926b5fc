import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Visual formula builder with drag-and-drop functionality
class FormulaBuilderWidget extends ConsumerStatefulWidget {
  final String? initialFormula;
  final Function(String) onFormulaChanged;
  final VoidCallback? onClose;

  const FormulaBuilderWidget({
    super.key,
    this.initialFormula,
    required this.onFormulaChanged,
    this.onClose,
  });

  @override
  ConsumerState<FormulaBuilderWidget> createState() => _FormulaBuilderWidgetState();
}

class _FormulaBuilderWidgetState extends ConsumerState<FormulaBuilderWidget> {
  final List<FormulaComponent> _components = [];
  String _currentFormula = '';

  @override
  void initState() {
    super.initState();
    _currentFormula = widget.initialFormula ?? '';
    if (_currentFormula.isNotEmpty) {
      _parseExistingFormula();
    }
  }

  void _parseExistingFormula() {
    // Simple parsing - in a real implementation, this would be more sophisticated
    if (_currentFormula.startsWith('=')) {
      _components.add(FormulaComponent(
        type: ComponentType.formula,
        value: _currentFormula,
        displayText: _currentFormula,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),
            const SizedBox(height: 16),
            
            // Main content
            Expanded(
              child: Row(
                children: [
                  // Function palette
                  Expanded(flex: 1, child: _buildFunctionPalette()),
                  const SizedBox(width: 16),
                  
                  // Formula builder area
                  Expanded(flex: 2, child: _buildFormulaBuilder()),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Formula preview and actions
            _buildFormulaPreview(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.functions,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          'Formula Builder',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const Spacer(),
        IconButton(
          onPressed: widget.onClose ?? () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildFunctionPalette() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Palette header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Text(
              'Functions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // Function categories
          Expanded(
            child: DefaultTabController(
              length: 4,
              child: Column(
                children: [
                  const TabBar(
                    isScrollable: true,
                    tabs: [
                      Tab(text: 'Math'),
                      Tab(text: 'Text'),
                      Tab(text: 'Date'),
                      Tab(text: 'Logical'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildFunctionList(_mathFunctions),
                        _buildFunctionList(_textFunctions),
                        _buildFunctionList(_dateFunctions),
                        _buildFunctionList(_logicalFunctions),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionList(List<FunctionInfo> functions) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: functions.length,
      itemBuilder: (context, index) {
        final function = functions[index];
        return Draggable<FunctionInfo>(
          data: function,
          feedback: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                function.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          child: Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  function.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  function.description,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormulaBuilder() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Builder header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Formula Builder',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _clearFormula,
                  icon: const Icon(Icons.clear),
                  tooltip: 'Clear formula',
                ),
              ],
            ),
          ),
          
          // Drop area
          Expanded(
            child: DragTarget<FunctionInfo>(
              onAcceptWithDetails: (details) => _addFunction(details.data),
              builder: (context, candidateData, rejectedData) {
                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: candidateData.isNotEmpty
                        ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
                        : null,
                    border: candidateData.isNotEmpty
                        ? Border.all(
                            color: Theme.of(context).colorScheme.primary,
                            width: 2,
                            style: BorderStyle.solid,
                          )
                        : null,
                  ),
                  child: _components.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.drag_indicator,
                                size: 48,
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Drag functions here to build your formula',
                                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )
                      : _buildComponentsList(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentsList() {
    return ListView.builder(
      itemCount: _components.length,
      itemBuilder: (context, index) {
        final component = _components[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.secondaryContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Theme.of(context).colorScheme.secondary),
          ),
          child: Row(
            children: [
              Icon(
                _getComponentIcon(component.type),
                color: Theme.of(context).colorScheme.secondary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  component.displayText,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
              IconButton(
                onPressed: () => _removeComponent(index),
                icon: const Icon(Icons.close, size: 16),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFormulaPreview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Formula Preview:',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Theme.of(context).colorScheme.outline),
            ),
            child: Text(
              _buildFormulaString(),
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: widget.onClose ?? () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _applyFormula,
                child: const Text('Apply Formula'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _addFunction(FunctionInfo function) {
    setState(() {
      _components.add(FormulaComponent(
        type: ComponentType.function,
        value: function.name,
        displayText: '${function.name}()',
        functionInfo: function,
      ));
    });
    _updateFormula();
  }

  void _removeComponent(int index) {
    setState(() {
      _components.removeAt(index);
    });
    _updateFormula();
  }

  void _clearFormula() {
    setState(() {
      _components.clear();
    });
    _updateFormula();
  }

  String _buildFormulaString() {
    if (_components.isEmpty) return '';
    
    final formula = StringBuffer('=');
    for (final component in _components) {
      formula.write(component.displayText);
    }
    return formula.toString();
  }

  void _updateFormula() {
    _currentFormula = _buildFormulaString();
    widget.onFormulaChanged(_currentFormula);
  }

  void _applyFormula() {
    widget.onFormulaChanged(_currentFormula);
    if (widget.onClose != null) {
      widget.onClose!();
    } else {
      Navigator.of(context).pop();
    }
  }

  IconData _getComponentIcon(ComponentType type) {
    switch (type) {
      case ComponentType.function:
        return Icons.functions;
      case ComponentType.operator:
        return Icons.calculate;
      case ComponentType.cellReference:
        return Icons.grid_on;
      case ComponentType.value:
        return Icons.numbers;
      case ComponentType.formula:
        return Icons.code;
    }
  }
}

/// Formula component model
class FormulaComponent {
  final ComponentType type;
  final String value;
  final String displayText;
  final FunctionInfo? functionInfo;

  FormulaComponent({
    required this.type,
    required this.value,
    required this.displayText,
    this.functionInfo,
  });
}

/// Component types
enum ComponentType {
  function,
  operator,
  cellReference,
  value,
  formula,
}

/// Function information model
class FunctionInfo {
  final String name;
  final String description;
  final String syntax;
  final String category;
  final List<String> parameters;

  const FunctionInfo({
    required this.name,
    required this.description,
    required this.syntax,
    required this.category,
    this.parameters = const [],
  });
}

/// Predefined function lists
final List<FunctionInfo> _mathFunctions = [
  FunctionInfo(name: 'SUM', description: 'Adds all numbers in a range', syntax: 'SUM(range)', category: 'Math'),
  FunctionInfo(name: 'AVERAGE', description: 'Calculates the average', syntax: 'AVERAGE(range)', category: 'Math'),
  FunctionInfo(name: 'COUNT', description: 'Counts numbers in a range', syntax: 'COUNT(range)', category: 'Math'),
  FunctionInfo(name: 'MAX', description: 'Returns the largest value', syntax: 'MAX(range)', category: 'Math'),
  FunctionInfo(name: 'MIN', description: 'Returns the smallest value', syntax: 'MIN(range)', category: 'Math'),
  FunctionInfo(name: 'ROUND', description: 'Rounds a number', syntax: 'ROUND(number, digits)', category: 'Math'),
];

final List<FunctionInfo> _textFunctions = [
  FunctionInfo(name: 'CONCATENATE', description: 'Joins text strings', syntax: 'CONCATENATE(text1, text2)', category: 'Text'),
  FunctionInfo(name: 'LEFT', description: 'Returns leftmost characters', syntax: 'LEFT(text, num_chars)', category: 'Text'),
  FunctionInfo(name: 'RIGHT', description: 'Returns rightmost characters', syntax: 'RIGHT(text, num_chars)', category: 'Text'),
  FunctionInfo(name: 'LEN', description: 'Returns length of text', syntax: 'LEN(text)', category: 'Text'),
];

final List<FunctionInfo> _dateFunctions = [
  FunctionInfo(name: 'TODAY', description: 'Returns current date', syntax: 'TODAY()', category: 'Date'),
  FunctionInfo(name: 'NOW', description: 'Returns current date and time', syntax: 'NOW()', category: 'Date'),
  FunctionInfo(name: 'YEAR', description: 'Returns year from date', syntax: 'YEAR(date)', category: 'Date'),
  FunctionInfo(name: 'MONTH', description: 'Returns month from date', syntax: 'MONTH(date)', category: 'Date'),
];

final List<FunctionInfo> _logicalFunctions = [
  FunctionInfo(name: 'IF', description: 'Returns value based on condition', syntax: 'IF(condition, true_value, false_value)', category: 'Logical'),
  FunctionInfo(name: 'AND', description: 'Returns TRUE if all conditions are TRUE', syntax: 'AND(condition1, condition2)', category: 'Logical'),
  FunctionInfo(name: 'OR', description: 'Returns TRUE if any condition is TRUE', syntax: 'OR(condition1, condition2)', category: 'Logical'),
  FunctionInfo(name: 'NOT', description: 'Returns opposite of logical value', syntax: 'NOT(condition)', category: 'Logical'),
];
