import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/file_info.dart';
import '../services/file_storage_service.dart';

// File storage service provider
final fileStorageServiceProvider = Provider<FileStorageService>((ref) {
  return FileStorageService();
});

// Files stream provider
final filesProvider = StreamProvider<List<FileInfo>>((ref) {
  final service = ref.watch(fileStorageServiceProvider);
  return service.filesStream;
});

// File updated stream provider
final fileUpdatedProvider = StreamProvider<FileInfo>((ref) {
  final service = ref.watch(fileStorageServiceProvider);
  return service.fileUpdatedStream;
});

// Storage statistics provider
final storageStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final filesAsync = ref.watch(filesProvider);
  return filesAsync.when(
    data: (files) {
      final service = ref.read(fileStorageServiceProvider);
      return service.getStorageStats();
    },
    loading: () => {},
    error: (_, __) => {},
  );
});

// Files by type providers
final voiceMemosProvider = Provider<List<FileInfo>>((ref) {
  final filesAsync = ref.watch(filesProvider);
  return filesAsync.when(
    data: (files) => files.where((f) => f.type == FileType.voiceMemo).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

final noteExportsProvider = Provider<List<FileInfo>>((ref) {
  final filesAsync = ref.watch(filesProvider);
  return filesAsync.when(
    data: (files) => files.where((f) => f.type == FileType.noteExport).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

final backupsProvider = Provider<List<FileInfo>>((ref) {
  final filesAsync = ref.watch(filesProvider);
  return filesAsync.when(
    data: (files) => files.where((f) => f.type == FileType.backup).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Storage actions provider
final storageActionsProvider = Provider<StorageActions>((ref) {
  final service = ref.watch(fileStorageServiceProvider);
  return StorageActions(service);
});

// Storage helper class
class StorageActions {
  final FileStorageService _service;

  StorageActions(this._service);

  // File operations
  Future<FileInfo> saveFile({
    required String name,
    required FileType type,
    required List<int> data,
    String? associatedId,
    String? associatedType,
    Map<String, dynamic>? metadata,
  }) async {
    return await _service.saveFile(
      name: name,
      type: type,
      data: data,
      associatedId: associatedId,
      associatedType: associatedType,
      metadata: metadata,
    );
  }

  Future<List<int>?> readFile(String fileId) async {
    return await _service.readFile(fileId);
  }

  Future<bool> deleteFile(String fileId) async {
    return await _service.deleteFile(fileId);
  }

  Future<bool> moveFile(String fileId, String newName) async {
    return await _service.moveFile(fileId, newName);
  }

  Future<FileInfo?> copyFile(String fileId, String newName) async {
    return await _service.copyFile(fileId, newName);
  }

  // Voice memo operations
  Future<FileInfo> saveVoiceMemo({
    required String memoId,
    required String fileName,
    required List<int> audioData,
    String? mimeType,
  }) async {
    return await saveFile(
      name: fileName,
      type: FileType.voiceMemo,
      data: audioData,
      associatedId: memoId,
      associatedType: 'voice_memo',
      metadata: {
        'mimeType': mimeType ?? 'audio/aac',
        'recordedAt': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<List<int>?> getVoiceMemoData(String memoId) async {
    final files = await _service.filesStream.first;
    final voiceMemoFile = files.firstWhere(
      (f) => f.type == FileType.voiceMemo && f.associatedId == memoId,
      orElse: () => throw Exception('Voice memo file not found'),
    );
    return await readFile(voiceMemoFile.id);
  }

  Future<bool> deleteVoiceMemo(String memoId) async {
    final files = await _service.filesStream.first;
    final voiceMemoFiles = files.where(
      (f) => f.type == FileType.voiceMemo && f.associatedId == memoId,
    ).toList();
    
    bool allDeleted = true;
    for (final file in voiceMemoFiles) {
      final deleted = await deleteFile(file.id);
      if (!deleted) allDeleted = false;
    }
    return allDeleted;
  }

  // Note export operations
  Future<FileInfo> exportNote({
    required String noteId,
    required String noteTitle,
    required String content,
    required String format,
  }) async {
    return await _service.exportNote(
      noteId: noteId,
      noteTitle: noteTitle,
      content: content,
      format: format,
    );
  }

  Future<String?> getExportedNoteContent(String fileId) async {
    final data = await readFile(fileId);
    if (data != null) {
      return String.fromCharCodes(data);
    }
    return null;
  }

  Future<List<FileInfo>> getNoteExports(String noteId) async {
    return _service.getFilesByAssociation(noteId, 'note');
  }

  // Backup operations
  Future<FileInfo> createBackup({
    required Map<String, dynamic> data,
    String? version,
  }) async {
    return await _service.createBackup(data: data, version: version);
  }

  Future<Map<String, dynamic>?> restoreBackup(String fileId) async {
    return await _service.restoreBackup(fileId);
  }

  Future<List<FileInfo>> getAllBackups() async {
    final files = await _service.filesStream.first;
    return files.where((f) => f.type == FileType.backup).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<bool> deleteOldBackups({int keepCount = 5}) async {
    final backups = await getAllBackups();
    if (backups.length <= keepCount) return true;

    final toDelete = backups.skip(keepCount).toList();
    bool allDeleted = true;
    
    for (final backup in toDelete) {
      final deleted = await deleteFile(backup.id);
      if (!deleted) allDeleted = false;
    }
    
    return allDeleted;
  }

  // Search and filtering
  List<FileInfo> searchFiles(String query) {
    return _service.searchFiles(query);
  }

  List<FileInfo> getFilesByType(FileType type) {
    return _service.getFilesByType(type);
  }

  List<FileInfo> getFilesByAssociation(String associatedId, String associatedType) {
    return _service.getFilesByAssociation(associatedId, associatedType);
  }

  // Maintenance operations
  Future<void> performCleanup() async {
    await _service.performCleanup();
  }

  Future<void> optimizeStorage() async {
    await _service.optimizeStorage();
  }

  // Settings
  Future<void> updateSettings({
    int? maxFileSize,
    int? maxTotalStorage,
    bool? autoCleanup,
    int? cleanupAfterDays,
    bool? compressBackups,
  }) async {
    await _service.updateSettings(
      maxFileSize: maxFileSize,
      maxTotalStorage: maxTotalStorage,
      autoCleanup: autoCleanup,
      cleanupAfterDays: cleanupAfterDays,
      compressBackups: compressBackups,
    );
  }

  // Bulk operations
  Future<List<FileInfo>> exportMultipleNotes(List<Map<String, dynamic>> notes) async {
    final exports = <FileInfo>[];
    
    for (final note in notes) {
      try {
        final export = await exportNote(
          noteId: note['id'],
          noteTitle: note['title'],
          content: note['content'],
          format: note['format'] ?? 'md',
        );
        exports.add(export);
      } catch (e) {
        // Continue with other notes if one fails
        continue;
      }
    }
    
    return exports;
  }

  Future<bool> deleteMultipleFiles(List<String> fileIds) async {
    bool allDeleted = true;
    
    for (final fileId in fileIds) {
      final deleted = await deleteFile(fileId);
      if (!deleted) allDeleted = false;
    }
    
    return allDeleted;
  }

  Future<Map<String, dynamic>> createFullBackup() async {
    // This would integrate with other services to create a complete backup
    final backupData = {
      'version': '1.0.0',
      'timestamp': DateTime.now().toIso8601String(),
      'files': await _service.filesStream.first,
      // Add other app data here
      'notes': [], // Would come from notes service
      'todos': [], // Would come from todos service
      'voiceMemos': [], // Would come from voice memos service
      'dhikrRoutines': [], // Would come from dhikr service
      'settings': {}, // Would come from settings service
    };
    
    final backupFile = await createBackup(data: backupData);
    return {
      'success': true,
      'backupFile': backupFile,
      'size': backupFile.formattedSize,
    };
  }

  // Statistics and analytics
  Future<Map<String, dynamic>> getDetailedStats() async {
    final stats = _service.getStorageStats();
    final files = await _service.filesStream.first;
    
    // Calculate additional statistics
    final recentFiles = files.where((f) => 
        f.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7)))).length;
    
    final largestFile = files.isNotEmpty 
        ? files.reduce((a, b) => a.sizeBytes > b.sizeBytes ? a : b)
        : null;
    
    final oldestFile = files.isNotEmpty
        ? files.reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b)
        : null;

    return {
      ...stats,
      'recentFiles': recentFiles,
      'largestFile': largestFile?.toJson(),
      'oldestFile': oldestFile?.toJson(),
      'averageFileSize': files.isNotEmpty 
          ? (stats['totalSize'] as int) / files.length 
          : 0,
    };
  }
}

// Initialize storage service provider
final initializeStorageServiceProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(fileStorageServiceProvider);
  await service.initialize();
});
