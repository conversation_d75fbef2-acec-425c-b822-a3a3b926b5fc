

enum SyncStatus {
  pending,
  syncing,
  synced,
  failed,
  conflict,
}

enum SyncOperation {
  create,
  update,
  delete,
}

enum SyncPriority {
  low,
  normal,
  high,
  critical,
}

class SyncItem {
  final String id;
  final String entityType;
  final String entityId;
  final SyncOperation operation;
  final SyncStatus status;
  final SyncPriority priority;
  final Map<String, dynamic> data;
  final Map<String, dynamic>? previousData;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final DateTime? syncedAt;
  final int retryCount;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  SyncItem({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.operation,
    required this.data,
    this.previousData,
    this.status = SyncStatus.pending,
    this.priority = SyncPriority.normal,
    DateTime? createdAt,
    DateTime? modifiedAt,
    this.syncedAt,
    this.retryCount = 0,
    this.errorMessage,
    this.metadata,
  }) : createdAt = createdAt ?? DateTime.now(),
       modifiedAt = modifiedAt ?? DateTime.now();

  SyncItem copyWith({
    SyncStatus? status,
    DateTime? syncedAt,
    int? retryCount,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return SyncItem(
      id: id,
      entityType: entityType,
      entityId: entityId,
      operation: operation,
      data: data,
      previousData: previousData,
      status: status ?? this.status,
      priority: priority,
      createdAt: createdAt,
      modifiedAt: DateTime.now(),
      syncedAt: syncedAt ?? this.syncedAt,
      retryCount: retryCount ?? this.retryCount,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'entityType': entityType,
      'entityId': entityId,
      'operation': operation.name,
      'status': status.name,
      'priority': priority.name,
      'data': data,
      'previousData': previousData,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'retryCount': retryCount,
      'errorMessage': errorMessage,
      'metadata': metadata,
    };
  }

  factory SyncItem.fromJson(Map<String, dynamic> json) {
    return SyncItem(
      id: json['id'],
      entityType: json['entityType'],
      entityId: json['entityId'],
      operation: SyncOperation.values.firstWhere(
        (op) => op.name == json['operation'],
      ),
      status: SyncStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => SyncStatus.pending,
      ),
      priority: SyncPriority.values.firstWhere(
        (p) => p.name == json['priority'],
        orElse: () => SyncPriority.normal,
      ),
      data: Map<String, dynamic>.from(json['data']),
      previousData: json['previousData'] != null 
          ? Map<String, dynamic>.from(json['previousData']) 
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      modifiedAt: DateTime.parse(json['modifiedAt']),
      syncedAt: json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null,
      retryCount: json['retryCount'] ?? 0,
      errorMessage: json['errorMessage'],
      metadata: json['metadata'] != null 
          ? Map<String, dynamic>.from(json['metadata']) 
          : null,
    );
  }
}

class SyncConflict {
  final String entityType;
  final String entityId;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> remoteData;
  final DateTime localModifiedAt;
  final DateTime remoteModifiedAt;
  final ConflictResolution? resolution;

  SyncConflict({
    required this.entityType,
    required this.entityId,
    required this.localData,
    required this.remoteData,
    required this.localModifiedAt,
    required this.remoteModifiedAt,
    this.resolution,
  });

  SyncConflict copyWith({ConflictResolution? resolution}) {
    return SyncConflict(
      entityType: entityType,
      entityId: entityId,
      localData: localData,
      remoteData: remoteData,
      localModifiedAt: localModifiedAt,
      remoteModifiedAt: remoteModifiedAt,
      resolution: resolution ?? this.resolution,
    );
  }
}

enum ConflictResolution {
  useLocal,
  useRemote,
  merge,
  skip,
}

class SyncStats {
  final int totalItems;
  final int pendingItems;
  final int syncedItems;
  final int failedItems;
  final int conflictItems;
  final DateTime? lastSyncAt;
  final bool isOnline;
  final bool isSyncing;

  SyncStats({
    required this.totalItems,
    required this.pendingItems,
    required this.syncedItems,
    required this.failedItems,
    required this.conflictItems,
    this.lastSyncAt,
    required this.isOnline,
    required this.isSyncing,
  });

  double get syncProgress {
    if (totalItems == 0) return 1.0;
    return syncedItems / totalItems;
  }

  bool get hasConflicts => conflictItems > 0;
  bool get hasPendingItems => pendingItems > 0;
  bool get hasFailedItems => failedItems > 0;
}

abstract class SyncableEntity {
  String get id;
  String get entityType;
  DateTime get modifiedAt;
  Map<String, dynamic> toSyncJson();
  
  static T fromSyncJson<T extends SyncableEntity>(Map<String, dynamic> json) {
    throw UnimplementedError('fromSyncJson must be implemented by subclasses');
  }
}

class OfflineQueueItem {
  final String id;
  final String action;
  final String entityType;
  final String entityId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final int priority;

  OfflineQueueItem({
    required this.id,
    required this.action,
    required this.entityType,
    required this.entityId,
    required this.data,
    DateTime? timestamp,
    this.priority = 0,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'action': action,
      'entityType': entityType,
      'entityId': entityId,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'priority': priority,
    };
  }

  factory OfflineQueueItem.fromJson(Map<String, dynamic> json) {
    return OfflineQueueItem(
      id: json['id'],
      action: json['action'],
      entityType: json['entityType'],
      entityId: json['entityId'],
      data: Map<String, dynamic>.from(json['data']),
      timestamp: DateTime.parse(json['timestamp']),
      priority: json['priority'] ?? 0,
    );
  }
}

class SyncConfiguration {
  final bool autoSync;
  final Duration syncInterval;
  final bool syncOnlyOnWifi;
  final bool syncInBackground;
  final int maxRetryAttempts;
  final Duration retryDelay;
  final List<String> enabledEntityTypes;
  final ConflictResolution defaultConflictResolution;

  const SyncConfiguration({
    this.autoSync = true,
    this.syncInterval = const Duration(minutes: 5),
    this.syncOnlyOnWifi = false,
    this.syncInBackground = true,
    this.maxRetryAttempts = 3,
    this.retryDelay = const Duration(seconds: 30),
    this.enabledEntityTypes = const [],
    this.defaultConflictResolution = ConflictResolution.useRemote,
  });

  SyncConfiguration copyWith({
    bool? autoSync,
    Duration? syncInterval,
    bool? syncOnlyOnWifi,
    bool? syncInBackground,
    int? maxRetryAttempts,
    Duration? retryDelay,
    List<String>? enabledEntityTypes,
    ConflictResolution? defaultConflictResolution,
  }) {
    return SyncConfiguration(
      autoSync: autoSync ?? this.autoSync,
      syncInterval: syncInterval ?? this.syncInterval,
      syncOnlyOnWifi: syncOnlyOnWifi ?? this.syncOnlyOnWifi,
      syncInBackground: syncInBackground ?? this.syncInBackground,
      maxRetryAttempts: maxRetryAttempts ?? this.maxRetryAttempts,
      retryDelay: retryDelay ?? this.retryDelay,
      enabledEntityTypes: enabledEntityTypes ?? this.enabledEntityTypes,
      defaultConflictResolution: defaultConflictResolution ?? this.defaultConflictResolution,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoSync': autoSync,
      'syncInterval': syncInterval.inMilliseconds,
      'syncOnlyOnWifi': syncOnlyOnWifi,
      'syncInBackground': syncInBackground,
      'maxRetryAttempts': maxRetryAttempts,
      'retryDelay': retryDelay.inMilliseconds,
      'enabledEntityTypes': enabledEntityTypes,
      'defaultConflictResolution': defaultConflictResolution.name,
    };
  }

  factory SyncConfiguration.fromJson(Map<String, dynamic> json) {
    return SyncConfiguration(
      autoSync: json['autoSync'] ?? true,
      syncInterval: Duration(milliseconds: json['syncInterval'] ?? 300000),
      syncOnlyOnWifi: json['syncOnlyOnWifi'] ?? false,
      syncInBackground: json['syncInBackground'] ?? true,
      maxRetryAttempts: json['maxRetryAttempts'] ?? 3,
      retryDelay: Duration(milliseconds: json['retryDelay'] ?? 30000),
      enabledEntityTypes: List<String>.from(json['enabledEntityTypes'] ?? []),
      defaultConflictResolution: ConflictResolution.values.firstWhere(
        (r) => r.name == json['defaultConflictResolution'],
        orElse: () => ConflictResolution.useRemote,
      ),
    );
  }
}
