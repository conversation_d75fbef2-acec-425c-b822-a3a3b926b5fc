import 'package:flutter/material.dart';
import '../models/ui_component.dart';
import '../models/mobile_app_theme.dart';
import '../services/formula_engine_service.dart';
import '../models/spreadsheet_cell.dart';

// Import the UIComponentData class from the UI builder screen
class UIComponentData {
  final String id;
  ComponentType type;
  String label;
  double x;
  double y;
  double width;
  double height;
  String? cellBinding;
  Color? customColor;

  UIComponentData({
    required this.id,
    required this.type,
    required this.label,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.cellBinding,
    this.customColor,
  });
}

/// Service for managing app preview functionality
class AppPreviewService {
  static final AppPreviewService _instance = AppPreviewService._internal();
  factory AppPreviewService() => _instance;
  AppPreviewService._internal();

  final Map<String, dynamic> _appState = {};
  final Map<String, Function> _eventHandlers = {};
  final List<String> _validationErrors = [];

  /// Initialize app preview with components and theme
  void initializePreview({
    required List<UIComponentData> components,
    required MobileAppTheme theme,
    required Map<String, dynamic> cellValues,
    required FormulaEngineService formulaEngine,
  }) {
    _appState.clear();
    _eventHandlers.clear();
    _validationErrors.clear();

    // Initialize component states
    for (final component in components) {
      _initializeComponentState(component, cellValues, formulaEngine);
    }

    // Setup event handlers
    _setupEventHandlers(components, formulaEngine);

    // Validate app configuration
    _validateApp(components);
  }

  /// Get current app state
  Map<String, dynamic> getAppState() => Map.unmodifiable(_appState);

  /// Get validation errors
  List<String> getValidationErrors() => List.unmodifiable(_validationErrors);

  /// Handle component interaction
  void handleComponentInteraction(String componentId, String eventType, dynamic value) {
    final handler = _eventHandlers['${componentId}_$eventType'];
    if (handler != null) {
      handler(value);
    }

    // Update app state
    _appState[componentId] = value;

    // Trigger dependent calculations
    _updateDependentComponents(componentId);
  }

  /// Simulate form submission
  Map<String, dynamic> simulateFormSubmission(List<UIComponentData> components) {
    final formData = <String, dynamic>{};
    final errors = <String>[];

    for (final component in components) {
      if (component.type == ComponentType.textField) {
        final value = _appState[component.id];
        if (value == null || value.toString().isEmpty) {
          errors.add('${component.label} is required');
        } else {
          formData[component.label] = value;
        }
      }
    }

    return {
      'data': formData,
      'errors': errors,
      'isValid': errors.isEmpty,
    };
  }

  /// Simulate data persistence
  Future<bool> simulateDataPersistence(Map<String, dynamic> data) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Simulate success/failure (90% success rate)
    return DateTime.now().millisecond % 10 != 0;
  }

  /// Generate app analytics
  Map<String, dynamic> generateAppAnalytics(List<UIComponentData> components) {
    final analytics = <String, dynamic>{
      'totalComponents': components.length,
      'componentTypes': <String, int>{},
      'interactiveComponents': 0,
      'dataBindings': 0,
      'formFields': 0,
      'outputFields': 0,
    };

    for (final component in components) {
      final typeName = component.type.name;
      analytics['componentTypes'][typeName] = 
          (analytics['componentTypes'][typeName] as int? ?? 0) + 1;

      if (_isInteractiveComponent(component.type)) {
        analytics['interactiveComponents']++;
      }

      if (component.type == ComponentType.textField) {
        analytics['formFields']++;
      }

      if (component.type == ComponentType.outputField) {
        analytics['outputFields']++;
      }
    }

    return analytics;
  }

  /// Export app configuration
  Map<String, dynamic> exportAppConfiguration(
    List<UIComponentData> components,
    MobileAppTheme theme,
    Map<String, String> cellBindings,
  ) {
    return {
      'version': '1.0.0',
      'timestamp': DateTime.now().toIso8601String(),
      'theme': {
        'id': theme.id,
        'name': theme.name,
        'type': theme.type.name,
      },
      'components': components.map((component) => {
        'id': component.id,
        'type': component.type.name,
        'label': component.label,
        'position': {'x': component.x, 'y': component.y},
        'size': {'width': component.width, 'height': component.height},
        'cellBinding': cellBindings[component.id],
      }).toList(),
      'cellBindings': cellBindings,
      'analytics': generateAppAnalytics(components),
    };
  }

  /// Generate Flutter code for the app
  String generateFlutterCode(
    List<UIComponentData> components,
    MobileAppTheme theme,
    Map<String, String> cellBindings,
  ) {
    final buffer = StringBuffer();
    
    // Add imports
    buffer.writeln('import \'package:flutter/material.dart\';');
    buffer.writeln('');
    
    // Add main app class
    buffer.writeln('class GeneratedApp extends StatefulWidget {');
    buffer.writeln('  @override');
    buffer.writeln('  _GeneratedAppState createState() => _GeneratedAppState();');
    buffer.writeln('}');
    buffer.writeln('');
    
    // Add state class
    buffer.writeln('class _GeneratedAppState extends State<GeneratedApp> {');
    
    // Add state variables
    for (final component in components) {
      if (_isStatefulComponent(component.type)) {
        buffer.writeln('  ${_getStateVariableType(component.type)} _${component.id}Value;');
      }
    }
    buffer.writeln('');
    
    // Add build method
    buffer.writeln('  @override');
    buffer.writeln('  Widget build(BuildContext context) {');
    buffer.writeln('    return Scaffold(');
    buffer.writeln('      appBar: AppBar(title: Text(\'Generated App\')),');
    buffer.writeln('      body: Padding(');
    buffer.writeln('        padding: EdgeInsets.all(16.0),');
    buffer.writeln('        child: Column(');
    buffer.writeln('          children: [');
    
    // Add components
    for (final component in components) {
      buffer.writeln('            ${_generateComponentCode(component, cellBindings)},');
      buffer.writeln('            SizedBox(height: 16),');
    }
    
    buffer.writeln('          ],');
    buffer.writeln('        ),');
    buffer.writeln('      ),');
    buffer.writeln('    );');
    buffer.writeln('  }');
    buffer.writeln('}');
    
    return buffer.toString();
  }

  /// Initialize component state
  void _initializeComponentState(
    UIComponentData component,
    Map<String, dynamic> cellValues,
    FormulaEngineService formulaEngine,
  ) {
    switch (component.type) {
      case ComponentType.textField:
        _appState[component.id] = '';
        break;
      case ComponentType.toggle:
        _appState[component.id] = false;
        break;
      case ComponentType.slider:
        _appState[component.id] = 0.0;
        break;
      case ComponentType.dropdown:
        _appState[component.id] = null;
        break;
      case ComponentType.outputField:
        // Initialize with cell value or formula result
        final cellBinding = cellValues[component.id];
        if (cellBinding != null) {
          _appState[component.id] = cellBinding;
        } else {
          _appState[component.id] = component.label;
        }
        break;
      default:
        _appState[component.id] = component.label;
    }
  }

  /// Setup event handlers for components
  void _setupEventHandlers(List<UIComponentData> components, FormulaEngineService formulaEngine) {
    for (final component in components) {
      switch (component.type) {
        case ComponentType.textField:
          _eventHandlers['${component.id}_onChanged'] = (String value) {
            _appState[component.id] = value;
          };
          break;
        case ComponentType.button:
          _eventHandlers['${component.id}_onPressed'] = () {
            // Handle button press
            _handleButtonPress(component, formulaEngine);
          };
          break;
        case ComponentType.toggle:
          _eventHandlers['${component.id}_onChanged'] = (bool value) {
            _appState[component.id] = value;
          };
          break;
        case ComponentType.slider:
          _eventHandlers['${component.id}_onChanged'] = (double value) {
            _appState[component.id] = value;
          };
          break;
        case ComponentType.dropdown:
          _eventHandlers['${component.id}_onChanged'] = (String? value) {
            _appState[component.id] = value;
          };
          break;
        case ComponentType.checkbox:
          _eventHandlers['${component.id}_onChanged'] = (bool value) {
            _appState[component.id] = value;
          };
          break;
        default:
          // No event handlers for other component types
          break;
      }
    }
  }

  /// Handle button press events
  void _handleButtonPress(UIComponentData component, FormulaEngineService formulaEngine) {
    // Simulate button action based on label
    final label = component.label.toLowerCase();
    
    if (label.contains('calculate') || label.contains('compute')) {
      _triggerCalculation(formulaEngine);
    } else if (label.contains('submit') || label.contains('save')) {
      _triggerFormSubmission();
    } else if (label.contains('clear') || label.contains('reset')) {
      _clearForm();
    }
  }

  /// Trigger calculation for all formula components
  void _triggerCalculation(FormulaEngineService formulaEngine) {
    // Update all output fields with new calculations
    for (final entry in _appState.entries) {
      if (entry.key.startsWith('output_')) {
        // Simulate formula calculation
        _appState[entry.key] = 'Calculated: ${DateTime.now().millisecond}';
      }
    }
  }

  /// Trigger form submission
  void _triggerFormSubmission() {
    // Validate and submit form data
    final hasErrors = _appState.values.any((value) => 
        value == null || value.toString().isEmpty);
    
    if (hasErrors) {
      _validationErrors.add('Please fill in all required fields');
    } else {
      _validationErrors.clear();
      // Simulate successful submission
    }
  }

  /// Clear form data
  void _clearForm() {
    for (final key in _appState.keys) {
      if (key.startsWith('textField_') || key.startsWith('dropdown_')) {
        _appState[key] = '';
      } else if (key.startsWith('toggle_')) {
        _appState[key] = false;
      } else if (key.startsWith('slider_')) {
        _appState[key] = 0.0;
      }
    }
  }

  /// Update components that depend on the changed component
  void _updateDependentComponents(String componentId) {
    // Find components that reference this component in their formulas
    // This would be implemented with proper dependency tracking
  }

  /// Validate app configuration
  void _validateApp(List<UIComponentData> components) {
    _validationErrors.clear();

    if (components.isEmpty) {
      _validationErrors.add('App has no components');
      return;
    }

    // Check for required components
    final hasInput = components.any((c) => _isInputComponent(c.type));
    final hasOutput = components.any((c) => c.type == ComponentType.outputField);

    if (!hasInput && !hasOutput) {
      _validationErrors.add('App should have at least one input or output component');
    }

    // Check for duplicate IDs
    final ids = components.map((c) => c.id).toList();
    final uniqueIds = ids.toSet();
    if (ids.length != uniqueIds.length) {
      _validationErrors.add('Duplicate component IDs found');
    }
  }

  /// Check if component type is interactive
  bool _isInteractiveComponent(ComponentType type) {
    return [
      ComponentType.textField,
      ComponentType.button,
      ComponentType.toggle,
      ComponentType.slider,
      ComponentType.dropdown,
      ComponentType.checkbox,
    ].contains(type);
  }

  /// Check if component type is input
  bool _isInputComponent(ComponentType type) {
    return [
      ComponentType.textField,
      ComponentType.toggle,
      ComponentType.slider,
      ComponentType.dropdown,
      ComponentType.checkbox,
    ].contains(type);
  }

  /// Check if component type is stateful
  bool _isStatefulComponent(ComponentType type) {
    return _isInputComponent(type);
  }

  /// Get state variable type for code generation
  String _getStateVariableType(ComponentType type) {
    switch (type) {
      case ComponentType.textField:
        return 'String';
      case ComponentType.toggle:
      case ComponentType.checkbox:
        return 'bool';
      case ComponentType.slider:
        return 'double';
      case ComponentType.dropdown:
        return 'String?';
      default:
        return 'dynamic';
    }
  }

  /// Generate Flutter code for a component
  String _generateComponentCode(UIComponentData component, Map<String, String> cellBindings) {
    switch (component.type) {
      case ComponentType.textField:
        return 'TextField(decoration: InputDecoration(labelText: \'${component.label}\'))';
      case ComponentType.button:
        return 'ElevatedButton(onPressed: () {}, child: Text(\'${component.label}\'))';
      case ComponentType.label:
        return 'Text(\'${component.label}\')';
      case ComponentType.outputField:
        return 'Container(padding: EdgeInsets.all(8), child: Text(\'${component.label}\'))';
      default:
        return 'Placeholder()';
    }
  }
}
