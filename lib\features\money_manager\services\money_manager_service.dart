import 'dart:convert';
import 'package:flutter/material.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/category.dart';
import '../models/budget.dart';

/// Comprehensive Money Manager service
class MoneyManagerService {
  static final MoneyManagerService _instance = MoneyManagerService._internal();
  factory MoneyManagerService() => _instance;
  MoneyManagerService._internal();

  // Data storage
  List<Account> _accounts = [];
  List<Transaction> _transactions = [];
  List<Category> _categories = [];
  List<Budget> _budgets = [];
  
  bool _isInitialized = false;

  // Getters
  List<Account> get accounts => List.unmodifiable(_accounts.where((a) => a.isActive));
  List<Transaction> get transactions => List.unmodifiable(_transactions);
  List<Category> get categories => List.unmodifiable(_categories.where((c) => c.isActive));
  List<Budget> get budgets => List.unmodifiable(_budgets.where((b) => b.isActive));
  bool get isInitialized => _isInitialized;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadData();
      _isInitialized = true;
    } catch (e) {
      print('Error initializing Money Manager: $e');
      await _initializeDefaults();
      _isInitialized = true;
    }
  }

  /// Load data from storage
  Future<void> _loadData() async {
    // Load from local storage or database
    // For now, initialize with defaults
    await _initializeDefaults();
  }

  /// Initialize with default data
  Future<void> _initializeDefaults() async {
    // Initialize default categories
    _categories = DefaultCategories.allDefaultCategories;

    // Initialize default accounts
    final now = DateTime.now();
    _accounts = [
      Account(
        id: 'account_checking',
        name: 'Checking Account',
        description: 'Primary checking account',
        type: AccountType.checking,
        currency: 'USD',
        initialBalance: 1000.0,
        currentBalance: 1000.0,
        iconName: 'account_balance_wallet',
        color: AccountType.checking.defaultColor,
        createdAt: now,
      ),
      Account(
        id: 'account_savings',
        name: 'Savings Account',
        description: 'Emergency savings',
        type: AccountType.savings,
        currency: 'USD',
        initialBalance: 5000.0,
        currentBalance: 5000.0,
        iconName: 'savings',
        color: AccountType.savings.defaultColor,
        createdAt: now,
      ),
      Account(
        id: 'account_credit',
        name: 'Credit Card',
        description: 'Main credit card',
        type: AccountType.credit,
        currency: 'USD',
        initialBalance: 0.0,
        currentBalance: -250.0,
        iconName: 'credit_card',
        color: AccountType.credit.defaultColor,
        allowNegativeBalance: true,
        createdAt: now,
      ),
    ];

    // Initialize sample transactions
    _transactions = [
      Transaction(
        id: 'trans_1',
        type: TransactionType.expense,
        amount: 45.50,
        fromAccountId: 'account_checking',
        categoryId: 'expense_food',
        title: 'Lunch at Restaurant',
        description: 'Team lunch meeting',
        date: now.subtract(const Duration(days: 1)),
        tags: ['work', 'food'],
        createdAt: now.subtract(const Duration(days: 1)),
      ),
      Transaction(
        id: 'trans_2',
        type: TransactionType.income,
        amount: 3000.0,
        fromAccountId: 'account_checking',
        categoryId: 'income_salary',
        title: 'Monthly Salary',
        description: 'Regular monthly salary payment',
        date: now.subtract(const Duration(days: 5)),
        createdAt: now.subtract(const Duration(days: 5)),
      ),
      Transaction(
        id: 'trans_3',
        type: TransactionType.expense,
        amount: 120.0,
        fromAccountId: 'account_checking',
        categoryId: 'expense_groceries',
        title: 'Weekly Groceries',
        description: 'Supermarket shopping',
        date: now.subtract(const Duration(days: 3)),
        tags: ['groceries', 'weekly'],
        createdAt: now.subtract(const Duration(days: 3)),
      ),
      Transaction(
        id: 'trans_4',
        type: TransactionType.transfer,
        amount: 500.0,
        fromAccountId: 'account_checking',
        toAccountId: 'account_savings',
        categoryId: 'income_other',
        title: 'Transfer to Savings',
        description: 'Monthly savings transfer',
        date: now.subtract(const Duration(days: 2)),
        createdAt: now.subtract(const Duration(days: 2)),
      ),
    ];

    // Initialize sample budgets
    _budgets = [
      Budget(
        id: 'budget_food',
        name: 'Food & Dining',
        description: 'Monthly food budget',
        type: BudgetType.category,
        amount: 500.0,
        spent: 165.50,
        categoryIds: ['expense_food', 'expense_groceries'],
        period: BudgetPeriod.monthly,
        startDate: DateTime(now.year, now.month, 1),
        endDate: DateTime(now.year, now.month + 1, 0),
        color: Colors.red,
        createdAt: now,
      ),
      Budget(
        id: 'budget_total',
        name: 'Monthly Spending',
        description: 'Total monthly spending limit',
        type: BudgetType.total,
        amount: 2000.0,
        spent: 285.50,
        period: BudgetPeriod.monthly,
        startDate: DateTime(now.year, now.month, 1),
        endDate: DateTime(now.year, now.month + 1, 0),
        color: Colors.blue,
        createdAt: now,
      ),
    ];

    await _saveData();
  }

  /// Save data to storage
  Future<void> _saveData() async {
    // Implement saving to local storage or database
    // For now, just keep in memory
  }

  // Account operations
  Future<Account> createAccount({
    required String name,
    String description = '',
    required AccountType type,
    String currency = 'USD',
    double initialBalance = 0.0,
    String? iconName,
    Color? color,
    bool allowNegativeBalance = false,
  }) async {
    final account = Account(
      id: 'account_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      type: type,
      currency: currency,
      initialBalance: initialBalance,
      currentBalance: initialBalance,
      iconName: iconName ?? type.defaultIcon.toString(),
      color: color ?? type.defaultColor,
      allowNegativeBalance: allowNegativeBalance,
      createdAt: DateTime.now(),
    );

    _accounts.add(account);
    await _saveData();
    return account;
  }

  Future<void> updateAccount(Account account) async {
    final index = _accounts.indexWhere((a) => a.id == account.id);
    if (index != -1) {
      _accounts[index] = account;
      await _saveData();
    }
  }

  Future<void> deleteAccount(String accountId) async {
    // Check if account has transactions
    final hasTransactions = _transactions.any((t) => 
        t.fromAccountId == accountId || t.toAccountId == accountId);
    
    if (hasTransactions) {
      // Soft delete - mark as inactive
      final index = _accounts.indexWhere((a) => a.id == accountId);
      if (index != -1) {
        _accounts[index] = _accounts[index].copyWith(isActive: false);
      }
    } else {
      // Hard delete
      _accounts.removeWhere((a) => a.id == accountId);
    }
    
    await _saveData();
  }

  Account? getAccount(String accountId) {
    try {
      return _accounts.firstWhere((a) => a.id == accountId);
    } catch (e) {
      return null;
    }
  }

  // Transaction operations
  Future<Transaction> createTransaction({
    required TransactionType type,
    required double amount,
    required String fromAccountId,
    String? toAccountId,
    required String categoryId,
    required String title,
    String description = '',
    DateTime? date,
    List<String> tags = const [],
    bool isRecurring = false,
    RecurrencePattern? recurrencePattern,
  }) async {
    final transaction = Transaction(
      id: 'trans_${DateTime.now().millisecondsSinceEpoch}',
      type: type,
      amount: amount,
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      categoryId: categoryId,
      title: title,
      description: description,
      date: date ?? DateTime.now(),
      tags: tags,
      isRecurring: isRecurring,
      recurrencePattern: recurrencePattern,
      createdAt: DateTime.now(),
    );

    _transactions.add(transaction);
    await _updateAccountBalances(transaction);
    await _updateBudgetSpending();
    await _saveData();
    
    return transaction;
  }

  Future<void> updateTransaction(Transaction transaction) async {
    final index = _transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      final oldTransaction = _transactions[index];
      _transactions[index] = transaction;
      
      // Reverse old transaction effects
      await _reverseAccountBalances(oldTransaction);
      // Apply new transaction effects
      await _updateAccountBalances(transaction);
      await _updateBudgetSpending();
      await _saveData();
    }
  }

  Future<void> deleteTransaction(String transactionId) async {
    final transaction = _transactions.where((t) => t.id == transactionId).firstOrNull;
    if (transaction != null) {
      _transactions.removeWhere((t) => t.id == transactionId);
      await _reverseAccountBalances(transaction);
      await _updateBudgetSpending();
      await _saveData();
    }
  }

  Transaction? getTransaction(String transactionId) {
    try {
      return _transactions.firstWhere((t) => t.id == transactionId);
    } catch (e) {
      return null;
    }
  }

  List<Transaction> getTransactionsByAccount(String accountId) {
    return _transactions.where((t) => 
        t.fromAccountId == accountId || t.toAccountId == accountId).toList();
  }

  List<Transaction> getTransactionsByCategory(String categoryId) {
    return _transactions.where((t) => t.categoryId == categoryId).toList();
  }

  List<Transaction> getTransactionsByDateRange(DateTime start, DateTime end) {
    return _transactions.where((t) => 
        t.date.isAfter(start.subtract(const Duration(days: 1))) && 
        t.date.isBefore(end.add(const Duration(days: 1)))).toList();
  }

  // Category operations
  Future<Category> createCategory({
    required String name,
    String description = '',
    required CategoryType type,
    String? iconName,
    Color? color,
    String? parentCategoryId,
    double? budgetLimit,
  }) async {
    final category = Category(
      id: 'category_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      type: type,
      iconName: iconName ?? 'category',
      color: color ?? type.defaultColor,
      parentCategoryId: parentCategoryId,
      budgetLimit: budgetLimit,
      createdAt: DateTime.now(),
    );

    _categories.add(category);
    await _saveData();
    return category;
  }

  Future<void> updateCategory(Category category) async {
    final index = _categories.indexWhere((c) => c.id == category.id);
    if (index != -1) {
      _categories[index] = category;
      await _saveData();
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    // Check if category has transactions
    final hasTransactions = _transactions.any((t) => t.categoryId == categoryId);
    
    if (hasTransactions) {
      // Soft delete - mark as inactive
      final index = _categories.indexWhere((c) => c.id == categoryId);
      if (index != -1) {
        _categories[index] = _categories[index].copyWith(isActive: false);
      }
    } else {
      // Hard delete
      _categories.removeWhere((c) => c.id == categoryId);
    }
    
    await _saveData();
  }

  Category? getCategory(String categoryId) {
    try {
      return _categories.firstWhere((c) => c.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  List<Category> getCategoriesByType(CategoryType type) {
    return _categories.where((c) => c.type == type && c.isActive).toList();
  }

  // Budget operations
  Future<Budget> createBudget({
    required String name,
    String description = '',
    required BudgetType type,
    required double amount,
    List<String> categoryIds = const [],
    List<String> accountIds = const [],
    required BudgetPeriod period,
    DateTime? startDate,
    DateTime? endDate,
    bool enableAlerts = true,
    double alertThreshold = 0.8,
    Color? color,
  }) async {
    final now = DateTime.now();
    final start = startDate ?? DateTime(now.year, now.month, 1);
    final end = endDate ?? period.getEndDate(start);

    final budget = Budget(
      id: 'budget_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      type: type,
      amount: amount,
      categoryIds: categoryIds,
      accountIds: accountIds,
      period: period,
      startDate: start,
      endDate: end,
      enableAlerts: enableAlerts,
      alertThreshold: alertThreshold,
      color: color ?? Colors.blue,
      createdAt: DateTime.now(),
    );

    _budgets.add(budget);
    await _updateBudgetSpending();
    await _saveData();
    return budget;
  }

  Future<void> updateBudget(Budget budget) async {
    final index = _budgets.indexWhere((b) => b.id == budget.id);
    if (index != -1) {
      _budgets[index] = budget;
      await _updateBudgetSpending();
      await _saveData();
    }
  }

  Future<void> deleteBudget(String budgetId) async {
    _budgets.removeWhere((b) => b.id == budgetId);
    await _saveData();
  }

  Budget? getBudget(String budgetId) {
    try {
      return _budgets.firstWhere((b) => b.id == budgetId);
    } catch (e) {
      return null;
    }
  }

  // Helper methods
  Future<void> _updateAccountBalances(Transaction transaction) async {
    final fromAccount = getAccount(transaction.fromAccountId);
    if (fromAccount != null) {
      double newBalance = fromAccount.currentBalance;
      
      switch (transaction.type) {
        case TransactionType.income:
          newBalance += transaction.amount;
          break;
        case TransactionType.expense:
          newBalance -= transaction.amount;
          break;
        case TransactionType.transfer:
          newBalance -= transaction.amount;
          break;
      }
      
      await updateAccount(fromAccount.copyWith(currentBalance: newBalance));
    }

    // Handle transfer to account
    if (transaction.toAccountId != null) {
      final toAccount = getAccount(transaction.toAccountId!);
      if (toAccount != null) {
        final newBalance = toAccount.currentBalance + transaction.amount;
        await updateAccount(toAccount.copyWith(currentBalance: newBalance));
      }
    }
  }

  Future<void> _reverseAccountBalances(Transaction transaction) async {
    final fromAccount = getAccount(transaction.fromAccountId);
    if (fromAccount != null) {
      double newBalance = fromAccount.currentBalance;
      
      switch (transaction.type) {
        case TransactionType.income:
          newBalance -= transaction.amount;
          break;
        case TransactionType.expense:
          newBalance += transaction.amount;
          break;
        case TransactionType.transfer:
          newBalance += transaction.amount;
          break;
      }
      
      await updateAccount(fromAccount.copyWith(currentBalance: newBalance));
    }

    // Handle transfer reversal
    if (transaction.toAccountId != null) {
      final toAccount = getAccount(transaction.toAccountId!);
      if (toAccount != null) {
        final newBalance = toAccount.currentBalance - transaction.amount;
        await updateAccount(toAccount.copyWith(currentBalance: newBalance));
      }
    }
  }

  Future<void> _updateBudgetSpending() async {
    for (int i = 0; i < _budgets.length; i++) {
      final budget = _budgets[i];
      double spent = 0.0;

      // Calculate spent amount based on budget type
      switch (budget.type) {
        case BudgetType.category:
          spent = _calculateCategorySpending(budget);
          break;
        case BudgetType.account:
          spent = _calculateAccountSpending(budget);
          break;
        case BudgetType.total:
          spent = _calculateTotalSpending(budget);
          break;
      }

      _budgets[i] = budget.copyWith(spent: spent);
    }
  }

  double _calculateCategorySpending(Budget budget) {
    return _transactions
        .where((t) => 
            t.type == TransactionType.expense &&
            budget.categoryIds.contains(t.categoryId) &&
            t.date.isAfter(budget.startDate.subtract(const Duration(days: 1))) &&
            t.date.isBefore(budget.endDate.add(const Duration(days: 1))))
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double _calculateAccountSpending(Budget budget) {
    return _transactions
        .where((t) => 
            t.type == TransactionType.expense &&
            budget.accountIds.contains(t.fromAccountId) &&
            t.date.isAfter(budget.startDate.subtract(const Duration(days: 1))) &&
            t.date.isBefore(budget.endDate.add(const Duration(days: 1))))
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double _calculateTotalSpending(Budget budget) {
    return _transactions
        .where((t) => 
            t.type == TransactionType.expense &&
            t.date.isAfter(budget.startDate.subtract(const Duration(days: 1))) &&
            t.date.isBefore(budget.endDate.add(const Duration(days: 1))))
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  // Analytics methods
  double getTotalBalance() {
    return _accounts.fold(0.0, (sum, account) => sum + account.currentBalance);
  }

  double getIncomeForPeriod(DateTime start, DateTime end) {
    return _transactions
        .where((t) => 
            t.type == TransactionType.income &&
            t.date.isAfter(start.subtract(const Duration(days: 1))) &&
            t.date.isBefore(end.add(const Duration(days: 1))))
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double getExpensesForPeriod(DateTime start, DateTime end) {
    return _transactions
        .where((t) => 
            t.type == TransactionType.expense &&
            t.date.isAfter(start.subtract(const Duration(days: 1))) &&
            t.date.isBefore(end.add(const Duration(days: 1))))
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  Map<String, double> getCategorySpending(DateTime start, DateTime end) {
    final Map<String, double> spending = {};
    
    for (final transaction in _transactions) {
      if (transaction.type == TransactionType.expense &&
          transaction.date.isAfter(start.subtract(const Duration(days: 1))) &&
          transaction.date.isBefore(end.add(const Duration(days: 1)))) {
        
        final category = getCategory(transaction.categoryId);
        final categoryName = category?.name ?? 'Unknown';
        spending[categoryName] = (spending[categoryName] ?? 0.0) + transaction.amount;
      }
    }
    
    return spending;
  }

  List<Transaction> searchTransactions(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _transactions.where((t) =>
        t.title.toLowerCase().contains(lowercaseQuery) ||
        t.description.toLowerCase().contains(lowercaseQuery) ||
        t.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery))).toList();
  }

  // Export/Import functionality
  Future<String> exportData() async {
    final data = {
      'accounts': _accounts.map((a) => a.toJson()).toList(),
      'transactions': _transactions.map((t) => t.toJson()).toList(),
      'categories': _categories.map((c) => c.toJson()).toList(),
      'budgets': _budgets.map((b) => b.toJson()).toList(),
      'exported_at': DateTime.now().toIso8601String(),
      'version': '1.0',
    };
    
    return json.encode(data);
  }

  Future<void> importData(String jsonData) async {
    try {
      final data = json.decode(jsonData) as Map<String, dynamic>;
      
      // Import accounts
      if (data['accounts'] != null) {
        final importedAccounts = (data['accounts'] as List<dynamic>)
            .map((a) => Account.fromJson(a as Map<String, dynamic>))
            .toList();
        _accounts.addAll(importedAccounts);
      }
      
      // Import transactions
      if (data['transactions'] != null) {
        final importedTransactions = (data['transactions'] as List<dynamic>)
            .map((t) => Transaction.fromJson(t as Map<String, dynamic>))
            .toList();
        _transactions.addAll(importedTransactions);
      }
      
      // Import categories
      if (data['categories'] != null) {
        final importedCategories = (data['categories'] as List<dynamic>)
            .map((c) => Category.fromJson(c as Map<String, dynamic>))
            .toList();
        _categories.addAll(importedCategories);
      }
      
      // Import budgets
      if (data['budgets'] != null) {
        final importedBudgets = (data['budgets'] as List<dynamic>)
            .map((b) => Budget.fromJson(b as Map<String, dynamic>))
            .toList();
        _budgets.addAll(importedBudgets);
      }
      
      await _saveData();
    } catch (e) {
      throw Exception('Failed to import data: $e');
    }
  }

  // Reset functionality
  Future<void> resetAllData() async {
    _accounts.clear();
    _transactions.clear();
    _categories.clear();
    _budgets.clear();
    
    await _initializeDefaults();
  }
}
