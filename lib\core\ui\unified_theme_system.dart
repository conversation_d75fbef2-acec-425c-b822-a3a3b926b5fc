import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';
import '../../features/settings/providers/settings_provider.dart';

/// Unified theme system for ShadowSuite
/// Provides consistent theming across all mini-apps and screens
class UnifiedThemeSystem {
  /// Get the current theme data based on settings
  static ThemeData getThemeData(WidgetRef ref) {
    final settings = ref.watch(settingsProvider);
    final context = NavigationService.navigatorKey.currentContext;
    final platformBrightness = context != null
        ? MediaQuery.platformBrightnessOf(context)
        : Brightness.light;

    return AppTheme.getTheme(settings, platformBrightness);
  }

  /// Get the current color scheme
  static ColorScheme getColorScheme(WidgetRef ref) {
    return getThemeData(ref).colorScheme;
  }

  /// Get the current text theme
  static TextTheme getTextTheme(WidgetRef ref) {
    return getThemeData(ref).textTheme;
  }

  /// Check if current theme is dark mode
  static bool isDarkMode(WidgetRef ref) {
    return getColorScheme(ref).brightness == Brightness.dark;
  }

  /// Get standardized spacing values
  static EdgeInsets getStandardPadding(BuildContext context) {
    return EdgeInsets.all(
      MediaQuery.of(context).size.width >= 1200 ? 24 : 16,
    );
  }

  /// Get standardized card elevation
  static double getCardElevation(WidgetRef ref) {
    final settings = ref.watch(settingsProvider);
    return settings.useMaterial3 ? 1 : 2;
  }

  /// Get standardized border radius
  static BorderRadius getStandardBorderRadius() {
    return BorderRadius.circular(12);
  }

  /// Get standardized button size
  static Size getButtonSize(WidgetRef ref) {
    final settings = ref.watch(settingsProvider);
    return Size(88 * settings.buttonSize, 36 * settings.buttonSize);
  }

  /// Get app bar theme for mini-apps
  static AppBarTheme getAppBarTheme(ColorScheme colorScheme, bool useMaterial3) {
    return AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: useMaterial3 ? 0 : 1,
      centerTitle: false,
      titleTextStyle: TextStyle(
        color: colorScheme.onSurface,
        fontSize: 20,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// Get navigation theme
  static NavigationBarThemeData getNavigationBarTheme(
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return NavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      indicatorColor: colorScheme.secondaryContainer,
      labelTextStyle: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return textTheme.labelMedium?.copyWith(
            color: colorScheme.onSecondaryContainer,
            fontWeight: FontWeight.w600,
          );
        }
        return textTheme.labelMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        );
      }),
    );
  }

  /// Get card theme for standardized cards
  static CardTheme getCardTheme(ColorScheme colorScheme, bool useMaterial3) {
    return CardTheme(
      elevation: useMaterial3 ? 1 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: getStandardBorderRadius(),
      ),
      color: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
    );
  }

  /// Get input decoration theme
  static InputDecorationTheme getInputDecorationTheme(ColorScheme colorScheme) {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      filled: true,
      fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  /// Get floating action button theme
  static FloatingActionButtonThemeData getFabTheme(ColorScheme colorScheme) {
    return FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 6,
      focusElevation: 8,
      hoverElevation: 8,
      highlightElevation: 12,
    );
  }

  /// Get dialog theme
  static DialogTheme getDialogTheme(ColorScheme colorScheme, TextTheme textTheme) {
    return DialogTheme(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      titleTextStyle: textTheme.headlineSmall?.copyWith(
        color: colorScheme.onSurface,
      ),
      contentTextStyle: textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// Get bottom sheet theme
  static BottomSheetThemeData getBottomSheetTheme(ColorScheme colorScheme) {
    return BottomSheetThemeData(
      backgroundColor: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      elevation: 8,
    );
  }

  /// Get snackbar theme
  static SnackBarThemeData getSnackBarTheme(
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return SnackBarThemeData(
      backgroundColor: colorScheme.inverseSurface,
      contentTextStyle: textTheme.bodyMedium?.copyWith(
        color: colorScheme.onInverseSurface,
      ),
      actionTextColor: colorScheme.inversePrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      behavior: SnackBarBehavior.floating,
      elevation: 6,
    );
  }

  /// Get chip theme
  static ChipThemeData getChipTheme(ColorScheme colorScheme, TextTheme textTheme) {
    return ChipThemeData(
      backgroundColor: colorScheme.surfaceContainerHighest,
      selectedColor: colorScheme.secondaryContainer,
      disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
      labelStyle: textTheme.labelLarge?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      secondaryLabelStyle: textTheme.labelLarge?.copyWith(
        color: colorScheme.onSecondaryContainer,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      pressElevation: 1,
    );
  }

  /// Get list tile theme
  static ListTileThemeData getListTileTheme(
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return ListTileThemeData(
      tileColor: colorScheme.surface,
      selectedTileColor: colorScheme.secondaryContainer,
      iconColor: colorScheme.onSurfaceVariant,
      selectedColor: colorScheme.onSecondaryContainer,
      textColor: colorScheme.onSurface,
      titleTextStyle: textTheme.bodyLarge?.copyWith(
        color: colorScheme.onSurface,
      ),
      subtitleTextStyle: textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  /// Get switch theme
  static SwitchThemeData getSwitchTheme(ColorScheme colorScheme) {
    return SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.onPrimary;
        }
        return colorScheme.outline;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.surfaceContainerHighest;
      }),
    );
  }

  /// Get checkbox theme
  static CheckboxThemeData getCheckboxTheme(ColorScheme colorScheme) {
    return CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return Colors.transparent;
      }),
      checkColor: WidgetStateProperty.all(colorScheme.onPrimary),
      side: BorderSide(color: colorScheme.outline),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  /// Get radio theme
  static RadioThemeData getRadioTheme(ColorScheme colorScheme) {
    return RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.outline;
      }),
    );
  }
}

/// Navigation service for accessing context
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  static BuildContext? get context => navigatorKey.currentContext;
}
