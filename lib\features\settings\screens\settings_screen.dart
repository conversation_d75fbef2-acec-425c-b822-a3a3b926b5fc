import 'package:flutter/material.dart' hide ThemeMode;
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:convert';

import '../../../shared/widgets/base_screen.dart';
import '../../../shared/providers/user_provider.dart';
import '../../../core/ui/unified_theme_system.dart';
import '../../../core/theme/theme_provider.dart' as theme;
import '../../../core/ui/master_layout.dart';
import '../../../core/navigation/app_router.dart';
import '../models/app_settings.dart';
import '../providers/settings_provider.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_tile.dart';
import '../widgets/theme_picker.dart';
import 'customization_screen.dart';
import '../services/backup_service.dart';

import 'permissions_screen.dart';
import 'user_profile_screen.dart';
import 'app_info_screen.dart';
import '../../search/screens/global_search_screen.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(settingsProvider);
    final colorScheme = UnifiedThemeSystem.getColorScheme(ref);
    final textTheme = UnifiedThemeSystem.getTextTheme(ref);

    return MasterLayout(
      title: 'Settings',
      currentRoute: AppRoutes.settings,
      actions: [
        IconButton(
          onPressed: _showSearchDialog,
          icon: const Icon(Icons.search),
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('Export Settings'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.upload),
                  SizedBox(width: 8),
                  Text('Import Settings'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'reset',
              child: Row(
                children: [
                  Icon(Icons.restore, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Reset to Defaults', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
      body: Column(
        children: [
          // Tab bar with unified theme
          Container(
            color: colorScheme.surface,
            child: TabBar(
              controller: _tabController,
              labelColor: colorScheme.primary,
              unselectedLabelColor: colorScheme.onSurfaceVariant,
              indicatorColor: colorScheme.primary,
              isScrollable: true,
              tabs: const [
                Tab(text: 'General', icon: Icon(Icons.settings, size: 16)),
                Tab(text: 'Appearance', icon: Icon(Icons.palette, size: 16)),
                Tab(text: 'Privacy', icon: Icon(Icons.security, size: 16)),
                Tab(text: 'Features', icon: Icon(Icons.extension, size: 16)),
                Tab(text: 'Accessibility', icon: Icon(Icons.accessibility, size: 16)),
                Tab(text: 'Advanced', icon: Icon(Icons.code, size: 16)),
              ],
            ),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildGeneralTab(),
                _buildAppearanceTab(),
                _buildPrivacyTab(),
                _buildFeaturesTab(),
                _buildAccessibilityTab(),
                _buildAdvancedTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralTab() {
    final settings = ref.watch(settingsProvider);
    final notifier = ref.read(settingsProvider.notifier);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        SettingsSection(
          title: 'Language & Region',
          children: [
            SettingsTile.dropdown<Language>(
              title: 'Language',
              subtitle: settings.languageDisplayName,
              value: settings.language,
              items: Language.values,
              itemBuilder: (language) => Text(settings.copyWith(language: language).languageDisplayName),
              onChanged: (language) => notifier.updateLanguage(language),
            ),
            SettingsTile.dropdown<DateFormat>(
              title: 'Date Format',
              subtitle: settings.dateFormatDisplayName,
              value: settings.dateFormat,
              items: DateFormat.values,
              itemBuilder: (format) => Text(settings.copyWith(dateFormat: format).dateFormatDisplayName),
              onChanged: (format) => notifier.updateDateFormat(format),
            ),
            SettingsTile.dropdown<TimeFormat>(
              title: 'Time Format',
              subtitle: settings.timeFormatDisplayName,
              value: settings.timeFormat,
              items: TimeFormat.values,
              itemBuilder: (format) => Text(settings.copyWith(timeFormat: format).timeFormatDisplayName),
              onChanged: (format) => notifier.updateTimeFormat(format),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Notifications',
          children: [
            SettingsTile.switch_(
              title: 'Enable Notifications',
              subtitle: 'Receive app notifications',
              value: settings.notificationsEnabled,
              onChanged: notifier.updateNotificationsEnabled,
            ),
            if (settings.notificationsEnabled) ...[
              ListTile(
                title: const Text('Notification Sound'),
                subtitle: Text(settings.notificationSoundDisplayName),
                trailing: DropdownButton<NotificationSound>(
                  value: settings.notificationSound,
                  items: NotificationSound.values.map((sound) {
                    return DropdownMenuItem(
                      value: sound,
                      child: Text(settings.copyWith(notificationSound: sound).notificationSoundDisplayName),
                    );
                  }).toList(),
                  onChanged: (sound) => sound != null ? notifier.updateNotificationSound(sound) : null,
                  underline: const SizedBox(),
                ),
              ),
              SettingsTile.switch_(
                title: 'Vibration',
                subtitle: 'Vibrate for notifications',
                value: settings.vibrationEnabled,
                onChanged: notifier.updateVibrationEnabled,
              ),
              SettingsTile.switch_(
                title: 'Show Previews',
                subtitle: 'Show notification content',
                value: settings.showNotificationPreviews,
                onChanged: notifier.updateShowNotificationPreviews,
              ),
              SettingsTile.navigation(
                title: 'Quiet Hours',
                subtitle: _getQuietHoursText(settings),
                onTap: () => _showQuietHoursDialog(),
              ),
            ],
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Performance',
          children: [
            SettingsTile.switch_(
              title: 'Animations',
              subtitle: 'Enable UI animations',
              value: settings.animationsEnabled,
              onChanged: notifier.updateAnimationsEnabled,
            ),
            SettingsTile.switch_(
              title: 'Haptic Feedback',
              subtitle: 'Vibrate on interactions',
              value: settings.hapticFeedbackEnabled,
              onChanged: notifier.updateHapticFeedbackEnabled,
            ),
            SettingsTile.switch_(
              title: 'Auto Save',
              subtitle: 'Automatically save changes',
              value: settings.autoSaveEnabled,
              onChanged: notifier.updateAutoSaveEnabled,
            ),
            if (settings.autoSaveEnabled)
              SettingsTile.slider(
                title: 'Auto Save Interval',
                subtitle: '${settings.autoSaveIntervalSeconds} seconds',
                value: settings.autoSaveIntervalSeconds.toDouble(),
                min: 10,
                max: 300,
                divisions: 29,
                onChanged: (value) => notifier.updateAutoSaveIntervalSeconds(value.round()),
              ),
            SettingsTile.switch_(
              title: 'Low Power Mode',
              subtitle: 'Reduce background activity',
              value: settings.lowPowerMode,
              onChanged: notifier.updateLowPowerMode,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAppearanceTab() {
    final settings = ref.watch(settingsProvider);
    final notifier = ref.read(settingsProvider.notifier);
    final colorScheme = UnifiedThemeSystem.getColorScheme(ref);
    final textTheme = UnifiedThemeSystem.getTextTheme(ref);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        SettingsSection(
          title: 'Unified Theme System',
          children: [
            SettingsTile.dropdown<ThemeMode>(
              title: 'Theme Mode',
              subtitle: settings.themeModeDisplayName,
              value: settings.themeMode,
              items: ThemeMode.values,
              itemBuilder: (mode) => Text(settings.copyWith(themeMode: mode).themeModeDisplayName),
              onChanged: (mode) {
                notifier.updateThemeMode(mode);
                // Theme will be automatically updated through the provider
              },
            ),
            SettingsTile.navigation(
              title: 'Primary Color',
              subtitle: 'Main app color theme',
              trailing: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: colorScheme.primary,
                  shape: BoxShape.circle,
                  border: Border.all(color: colorScheme.outline),
                ),
              ),
              onTap: () => _showColorPicker('primary'),
            ),
            SettingsTile.navigation(
              title: 'Accent Color',
              subtitle: 'Secondary accent color',
              trailing: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: settings.accentColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: colorScheme.outline),
                ),
              ),
              onTap: () => _showColorPicker('accent'),
            ),
            SettingsTile.switch_(
              title: 'Material 3',
              subtitle: 'Use Material Design 3',
              value: settings.useMaterial3,
              onChanged: (value) {
                notifier.updateUseMaterial3(value);
                // Theme will be automatically updated through the provider
              },
            ),
            SettingsTile.navigation(
              title: 'Advanced Theme Customization',
              subtitle: 'Colors, typography, spacing & animations',
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _openAppearanceCustomization(),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Typography',
          children: [
            SettingsTile.slider(
              title: 'Font Size',
              subtitle: '${settings.fontSize.round()}pt',
              value: settings.fontSize,
              min: 10,
              max: 24,
              divisions: 14,
              onChanged: notifier.updateFontSize,
            ),
            SettingsTile.switch_(
              title: 'Use System Font',
              subtitle: 'Use device default font',
              value: settings.useSystemFont,
              onChanged: notifier.updateUseSystemFont,
            ),
            if (!settings.useSystemFont)
              SettingsTile.navigation(
                title: 'Custom Font',
                subtitle: settings.customFontFamily.isEmpty ? 'None selected' : settings.customFontFamily,
                onTap: () => _showFontPicker(),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildPrivacyTab() {
    final settings = ref.watch(settingsProvider);
    final notifier = ref.read(settingsProvider.notifier);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        SettingsSection(
          title: 'Permissions',
          children: [
            SettingsTile.navigation(
              title: 'App Permissions',
              subtitle: 'Manage app permissions',
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PermissionsScreen(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        SettingsSection(
          title: 'Authentication',
          children: [
            SettingsTile.switch_(
              title: 'Require Authentication',
              subtitle: 'Lock app when inactive',
              value: settings.requireAuthentication,
              onChanged: notifier.updateRequireAuthentication,
            ),
            if (settings.requireAuthentication) ...[
              SettingsTile.switch_(
                title: 'Biometric Authentication',
                subtitle: 'Use fingerprint/face unlock',
                value: settings.biometricAuthentication,
                onChanged: notifier.updateBiometricAuthentication,
              ),
              SettingsTile.dropdown<int>(
                title: 'Auto Lock',
                subtitle: '${settings.autoLockMinutes} minutes',
                value: settings.autoLockMinutes,
                items: const [1, 2, 5, 10, 15, 30],
                itemBuilder: (minutes) => Text('$minutes minutes'),
                onChanged: notifier.updateAutoLockMinutes,
              ),
            ],
            SettingsTile.switch_(
              title: 'Hide in Recent Apps',
              subtitle: 'Hide content in app switcher',
              value: settings.hideContentInRecents,
              onChanged: notifier.updateHideContentInRecents,
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Data & Analytics',
          children: [
            SettingsTile.switch_(
              title: 'Analytics',
              subtitle: 'Help improve the app',
              value: settings.analyticsEnabled,
              onChanged: notifier.updateAnalyticsEnabled,
            ),
            SettingsTile.switch_(
              title: 'Crash Reporting',
              subtitle: 'Send crash reports',
              value: settings.crashReportingEnabled,
              onChanged: notifier.updateCrashReportingEnabled,
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Backup & Sync',
          children: [
            SettingsTile.switch_(
              title: 'Auto Backup',
              subtitle: 'Automatically backup data',
              value: settings.autoBackupEnabled,
              onChanged: notifier.updateAutoBackupEnabled,
            ),
            if (settings.autoBackupEnabled) ...[
              SettingsTile.dropdown<String>(
                title: 'Backup Frequency',
                subtitle: settings.backupFrequency,
                value: settings.backupFrequency,
                items: const ['daily', 'weekly', 'monthly'],
                itemBuilder: (frequency) => Text(frequency),
                onChanged: notifier.updateBackupFrequency,
              ),
              SettingsTile.slider(
                title: 'Max Backup Files',
                subtitle: '${settings.maxBackupFiles} files',
                value: settings.maxBackupFiles.toDouble(),
                min: 1,
                max: 50,
                divisions: 49,
                onChanged: (value) => notifier.updateMaxBackupFiles(value.round()),
              ),
            ],
            SettingsTile.switch_(
              title: 'Cloud Sync',
              subtitle: 'Sync across devices',
              value: settings.cloudSyncEnabled,
              onChanged: notifier.updateCloudSyncEnabled,
            ),
            if (settings.cloudSyncEnabled)
              SettingsTile.switch_(
                title: 'WiFi Only',
                subtitle: 'Sync only on WiFi',
                value: settings.syncOnWifiOnly,
                onChanged: notifier.updateSyncOnWifiOnly,
              ),

            // User Profile Management
            SettingsTile.navigation(
              title: 'User Profile',
              subtitle: 'Manage your profile and account',
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const UserProfileScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeaturesTab() {
    final settings = ref.watch(settingsProvider);
    final notifier = ref.read(settingsProvider.notifier);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        SettingsSection(
          title: 'Voice Memos',
          children: [
            SettingsTile.dropdown<String>(
              title: 'Audio Quality',
              subtitle: settings.audioQuality,
              value: settings.audioQuality,
              items: const ['low', 'medium', 'high'],
              itemBuilder: (quality) => Text(quality),
              onChanged: notifier.updateAudioQuality,
            ),
            SettingsTile.dropdown<String>(
              title: 'Audio Format',
              subtitle: settings.audioFormat.toUpperCase(),
              value: settings.audioFormat,
              items: const ['aac', 'mp3', 'wav'],
              itemBuilder: (format) => Text(format.toUpperCase()),
              onChanged: notifier.updateAudioFormat,
            ),
            SettingsTile.switch_(
              title: 'Auto Transcription',
              subtitle: 'Convert speech to text',
              value: settings.autoTranscription,
              onChanged: notifier.updateAutoTranscription,
            ),
            SettingsTile.slider(
              title: 'Max Recording Time',
              subtitle: '${settings.maxRecordingMinutes} minutes',
              value: settings.maxRecordingMinutes.toDouble(),
              min: 1,
              max: 180,
              divisions: 179,
              onChanged: (value) => notifier.updateMaxRecordingMinutes(value.round()),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Notes',
          children: [
            SettingsTile.switch_(
              title: 'Auto Save Notes',
              subtitle: 'Save notes automatically',
              value: settings.autoSaveNotes,
              onChanged: notifier.updateAutoSaveNotes,
            ),
            SettingsTile.navigation(
              title: 'Default Category',
              subtitle: settings.defaultNoteCategory,
              onTap: () => _showCategoryPicker('note'),
            ),
            SettingsTile.switch_(
              title: 'Show Word Count',
              subtitle: 'Display word count',
              value: settings.showWordCount,
              onChanged: notifier.updateShowWordCount,
            ),
            SettingsTile.switch_(
              title: 'Spell Check',
              subtitle: 'Check spelling while typing',
              value: settings.spellCheckEnabled,
              onChanged: notifier.updateSpellCheckEnabled,
            ),
            SettingsTile.dropdown<String>(
              title: 'Export Format',
              subtitle: settings.exportFormat.toUpperCase(),
              value: settings.exportFormat,
              items: const ['txt', 'md', 'pdf'],
              itemBuilder: (format) => Text(format.toUpperCase()),
              onChanged: notifier.updateExportFormat,
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Todos',
          children: [
            SettingsTile.switch_(
              title: 'Show Completed',
              subtitle: 'Show completed todos',
              value: settings.showCompletedTodos,
              onChanged: notifier.updateShowCompletedTodos,
            ),
            SettingsTile.slider(
              title: 'Default Reminder',
              subtitle: '${settings.defaultReminderMinutes} minutes before',
              value: settings.defaultReminderMinutes.toDouble(),
              min: 5,
              max: 1440,
              divisions: 287,
              onChanged: (value) => notifier.updateDefaultReminderMinutes(value.round()),
            ),
            SettingsTile.switch_(
              title: 'Auto Archive',
              subtitle: 'Archive completed todos',
              value: settings.autoArchiveCompleted,
              onChanged: notifier.updateAutoArchiveCompleted,
            ),
            if (settings.autoArchiveCompleted)
              SettingsTile.slider(
                title: 'Archive After',
                subtitle: '${settings.autoArchiveDays} days',
                value: settings.autoArchiveDays.toDouble(),
                min: 1,
                max: 365,
                divisions: 364,
                onChanged: (value) => notifier.updateAutoArchiveDays(value.round()),
              ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Dhikr',
          children: [
            SettingsTile.switch_(
              title: 'Vibration',
              subtitle: 'Vibrate on count',
              value: settings.dhikrVibration,
              onChanged: notifier.updateDhikrVibration,
            ),
            SettingsTile.switch_(
              title: 'Sound',
              subtitle: 'Play sound on count',
              value: settings.dhikrSound,
              onChanged: notifier.updateDhikrSound,
            ),
            SettingsTile.slider(
              title: 'Default Target',
              subtitle: '${settings.defaultDhikrTarget} repetitions',
              value: settings.defaultDhikrTarget.toDouble(),
              min: 1,
              max: 1000,
              divisions: 999,
              onChanged: (value) => notifier.updateDefaultDhikrTarget(value.round()),
            ),
            SettingsTile.switch_(
              title: 'Show Arabic Text',
              subtitle: 'Display Arabic text',
              value: settings.showArabicText,
              onChanged: notifier.updateShowArabicText,
            ),
            SettingsTile.switch_(
              title: 'Show Transliteration',
              subtitle: 'Display transliteration',
              value: settings.showTransliteration,
              onChanged: notifier.updateShowTransliteration,
            ),
            SettingsTile.switch_(
              title: 'Show Translation',
              subtitle: 'Display translation',
              value: settings.showTranslation,
              onChanged: notifier.updateShowTranslation,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccessibilityTab() {
    final settings = ref.watch(settingsProvider);
    final notifier = ref.read(settingsProvider.notifier);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        SettingsSection(
          title: 'Visual',
          children: [
            SettingsTile.switch_(
              title: 'High Contrast',
              subtitle: 'Increase contrast for better visibility',
              value: settings.highContrastMode,
              onChanged: notifier.updateHighContrastMode,
            ),
            SettingsTile.switch_(
              title: 'Large Text',
              subtitle: 'Use larger text sizes',
              value: settings.largeTextMode,
              onChanged: notifier.updateLargeTextMode,
            ),
            SettingsTile.switch_(
              title: 'Reduce Motion',
              subtitle: 'Minimize animations',
              value: settings.reduceMotion,
              onChanged: notifier.updateReduceMotion,
            ),
            SettingsTile.slider(
              title: 'Button Size',
              subtitle: '${(settings.buttonSize * 100).round()}%',
              value: settings.buttonSize,
              min: 0.8,
              max: 2.0,
              divisions: 12,
              onChanged: notifier.updateButtonSize,
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        SettingsSection(
          title: 'Interaction',
          children: [
            SettingsTile.switch_(
              title: 'Screen Reader Support',
              subtitle: 'Optimize for screen readers',
              value: settings.screenReaderSupport,
              onChanged: notifier.updateScreenReaderSupport,
            ),
            SettingsTile.switch_(
              title: 'Show Tooltips',
              subtitle: 'Display helpful tooltips',
              value: settings.showTooltips,
              onChanged: notifier.updateShowTooltips,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdvancedTab() {
    final settings = ref.watch(settingsProvider);
    final notifier = ref.read(settingsProvider.notifier);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        SettingsSection(
          title: 'Developer Options',
          children: [
            SettingsTile.switch_(
              title: 'Developer Mode',
              subtitle: 'Enable developer features',
              value: settings.developerMode,
              onChanged: notifier.updateDeveloperMode,
            ),
            if (settings.developerMode) ...[
              SettingsTile.switch_(
                title: 'Debug Logging',
                subtitle: 'Enable debug logs',
                value: settings.debugLogging,
                onChanged: notifier.updateDebugLogging,
              ),
              SettingsTile.dropdown<String>(
                title: 'Log Level',
                subtitle: settings.logLevel,
                value: settings.logLevel,
                items: const ['error', 'warning', 'info', 'debug'],
                itemBuilder: (level) => Text(level),
                onChanged: notifier.updateLogLevel,
              ),
            ],
            SettingsTile.switch_(
              title: 'Beta Features',
              subtitle: 'Enable experimental features',
              value: settings.betaFeatures,
              onChanged: notifier.updateBetaFeatures,
            ),
          ],
        ),
        const SizedBox(height: 16),

        SettingsSection(
          title: 'Data Management',
          children: [
            SettingsTile.navigation(
              title: 'Export Data',
              subtitle: 'Create backup of all data',
              trailing: const Icon(Icons.download),
              onTap: _exportData,
            ),
            SettingsTile.navigation(
              title: 'Import Data',
              subtitle: 'Restore from backup',
              trailing: const Icon(Icons.upload),
              onTap: _importData,
            ),
          ],
        ),
        const SizedBox(height: 16),

        SettingsSection(
          title: 'App Information',
          children: [
            SettingsTile.navigation(
              title: 'Version',
              subtitle: '1.0.0+1',
              onTap: () {},
            ),
            SettingsTile.navigation(
              title: 'Build Number',
              subtitle: '1',
              onTap: () {},
            ),
            SettingsTile.navigation(
              title: 'About',
              subtitle: 'App information and credits',
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AppInfoScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getQuietHoursText(AppSettings settings) {
    if (settings.quietHoursStart == null || settings.quietHoursEnd == null) {
      return 'Not set';
    }
    return '${settings.quietHoursStart!.format(context)} - ${settings.quietHoursEnd!.format(context)}';
  }

  void _showSearchDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const GlobalSearchScreen(),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportSettings();
        break;
      case 'import':
        _importSettings();
        break;
      case 'reset':
        _resetSettings();
        break;
    }
  }

  void _exportSettings() async {
    try {
      final settings = ref.read(settingsProvider);
      final settingsJson = settings.toJson();
      final jsonString = const JsonEncoder.withIndent('  ').convert(settingsJson);

      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: jsonString));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Settings exported to clipboard'),
            action: SnackBarAction(
              label: 'Share',
              onPressed: () => _shareSettings(jsonString),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Export failed: $e')),
        );
      }
    }
  }

  void _importSettings() {
    _showImportDialog();
  }

  void _shareSettings(String jsonString) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings Export'),
        content: SingleChildScrollView(
          child: SelectableText(jsonString),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showImportDialog() {
    final textController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Paste your settings JSON below:'),
            const SizedBox(height: 16),
            TextField(
              controller: textController,
              maxLines: 10,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: '{\n  "themeMode": "dark",\n  "primaryColor": "#2196F3"\n}',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _processImportedSettings(textController.text);
              Navigator.of(context).pop();
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _processImportedSettings(String jsonText) async {
    try {
      final jsonData = json.decode(jsonText) as Map<String, dynamic>;

      // Update settings
      final notifier = ref.read(settingsProvider.notifier);
      await notifier.importSettings(jsonData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings imported successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Import failed: $e')),
        );
      }
    }
  }

  void _resetSettings() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(settingsProvider.notifier).resetToDefaults();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings reset to defaults')),
        );
      }
    }
  }

  void _showQuietHoursDialog() async {
    final settings = ref.read(settingsProvider);
    TimeOfDay? startTime = settings.quietHoursStart;
    TimeOfDay? endTime = settings.quietHoursEnd;

    final result = await showDialog<Map<String, TimeOfDay?>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quiet Hours'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Start Time'),
              subtitle: Text(startTime?.format(context) ?? 'Not set'),
              onTap: () async {
                final time = await showTimePicker(
                  context: context,
                  initialTime: startTime ?? const TimeOfDay(hour: 22, minute: 0),
                );
                if (time != null) {
                  startTime = time;
                }
              },
            ),
            ListTile(
              title: const Text('End Time'),
              subtitle: Text(endTime?.format(context) ?? 'Not set'),
              onTap: () async {
                final time = await showTimePicker(
                  context: context,
                  initialTime: endTime ?? const TimeOfDay(hour: 7, minute: 0),
                );
                if (time != null) {
                  endTime = time;
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop({
              'start': startTime,
              'end': endTime,
            }),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null) {
      await ref.read(settingsProvider.notifier).updateQuietHours(
        result['start'],
        result['end'],
      );
    }
  }

  void _showColorPicker(String type) {
    final colors = [
      Colors.blue, Colors.green, Colors.red, Colors.orange, Colors.purple,
      Colors.teal, Colors.pink, Colors.indigo, Colors.amber, Colors.cyan,
      Colors.lime, Colors.deepOrange, Colors.deepPurple, Colors.lightBlue,
      Colors.lightGreen, Colors.brown, Colors.grey, Colors.blueGrey,
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select $type Color'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 6,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: colors.length,
            itemBuilder: (context, index) {
              final color = colors[index];
              return GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  _applyColorSelection(type, color);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _applyColorSelection(String type, Color color) async {
    final notifier = ref.read(settingsProvider.notifier);
    switch (type.toLowerCase()) {
      case 'primary':
        await notifier.updatePrimaryColor(color);
        break;
      case 'accent':
        await notifier.updateAccentColor(color);
        break;
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('$type color updated')),
      );
    }
  }

  void _showFontPicker() {
    final fonts = [
      'System Default',
      'Roboto',
      'Open Sans',
      'Lato',
      'Montserrat',
      'Source Sans Pro',
      'Raleway',
      'PT Sans',
      'Lora',
      'Merriweather',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Font'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: fonts.length,
            itemBuilder: (context, index) {
              final font = fonts[index];
              return ListTile(
                title: Text(
                  font,
                  style: TextStyle(
                    fontFamily: font == 'System Default' ? null : font,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _applyFontSelection(font);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _applyFontSelection(String font) async {
    final notifier = ref.read(settingsProvider.notifier);
    if (font == 'System Default') {
      await notifier.updateUseSystemFont(true);
    } else {
      await notifier.updateUseSystemFont(false);
      await notifier.updateCustomFontFamily(font);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Font updated to $font')),
      );
    }
  }

  void _showCategoryPicker(String type) {
    final categories = [
      'Work', 'Personal', 'Shopping', 'Health', 'Finance',
      'Travel', 'Education', 'Entertainment', 'Family', 'Other'
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select Default $type Category'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return ListTile(
                title: Text(category),
                onTap: () {
                  Navigator.of(context).pop();
                  _applyCategorySelection(type, category);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _applyCategorySelection(String type, String category) async {
    final notifier = ref.read(settingsProvider.notifier);
    if (type.toLowerCase() == 'note') {
      await notifier.updateDefaultNoteCategory(category);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Default $type category updated to $category')),
      );
    }
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'ShadowSuite',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.apps, size: 64),
      children: const [
        Text('A comprehensive productivity suite with notes, todos, voice memos, and more.'),
      ],
    );
  }

  void _openThemePicker() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ThemePicker(),
      ),
    );
  }

  void _openAppearanceCustomization() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CustomizationScreen(),
      ),
    );
  }

  void _exportData() async {
    try {
      final userProfile = ref.read(userProfileProvider);
      if (userProfile == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No user profile found')),
          );
        }
        return;
      }

      // Show loading
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Creating backup...'),
              ],
            ),
          ),
        );
      }

      final backup = await BackupService.createBackup(userProfile);
      final filePath = await BackupService.exportBackup(backup);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show success and offer to share
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Backup Created'),
            content: Text('Backup saved to: ${filePath.split('/').last}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await BackupService.shareBackup(filePath);
                },
                child: const Text('Share'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog if open
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to create backup: $e')),
        );
      }
    }
  }

  void _importData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Data'),
        content: const Text('Import functionality will be available in future updates.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
