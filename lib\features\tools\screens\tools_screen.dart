import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/widgets/base_screen.dart';
import '../models/conversion.dart';
import 'basic_calculator_screen.dart';
import 'scientific_calculator_screen.dart';
import 'unit_converter_screen.dart';
import 'enhanced_tool_builder_screen.dart';

/// Main Tools screen with Calculator, Converter, and Tool Builder sections
class ToolsScreen extends ConsumerWidget {
  const ToolsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tools'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Productivity Tools',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Calculators, converters, and custom tool builder',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),

            // Calculator Section
            _buildSection(
              context,
              title: 'Calculators',
              icon: Icons.calculate,
              children: [
                _buildToolCard(
                  context,
                  title: 'Basic Calculator',
                  subtitle: 'Simple arithmetic operations',
                  icon: Icons.calculate,
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const BasicCalculatorScreen(),
                    ),
                  ),
                ),
                _buildToolCard(
                  context,
                  title: 'Scientific Calculator',
                  subtitle: 'Advanced mathematical functions',
                  icon: Icons.science,
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ScientificCalculatorScreen(),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Converter Section
            _buildSection(
              context,
              title: 'Converters',
              icon: Icons.swap_horiz,
              children: [
                _buildToolCard(
                  context,
                  title: 'Unit Converter',
                  subtitle: 'Convert between different units',
                  icon: Icons.swap_horiz,
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const UnitConverterScreen(category: ConversionCategory.length),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Tool Builder Section
            _buildSection(
              context,
              title: 'Tool Builder',
              icon: Icons.build_circle,
              children: [
                _buildToolCard(
                  context,
                  title: 'Enhanced Tool Builder',
                  subtitle: 'Professional app builder with mobile themes & Excel',
                  icon: Icons.build_circle,
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const EnhancedToolBuilderScreen(),
                    ),
                  ),
                  isNew: true,
                ),
                _buildToolCard(
                  context,
                  title: 'Create New Tool',
                  subtitle: 'Build custom tools with Excel-like formulas',
                  icon: Icons.add_circle,
                  onTap: () => context.go('/tools/create'),
                ),
                _buildToolCard(
                  context,
                  title: 'My Tools',
                  subtitle: 'View and manage your saved tools',
                  icon: Icons.folder,
                  onTap: () => context.go('/tools/list'),
                ),
                _buildToolCard(
                  context,
                  title: 'Tool Builder Home',
                  subtitle: 'Advanced tool creation features',
                  icon: Icons.build_circle,
                  onTap: () => context.go('/tools/builder-home'),
                ),
                _buildToolCard(
                  context,
                  title: 'Excel Live Viewer',
                  subtitle: 'Open and edit Excel files directly',
                  icon: Icons.table_chart,
                  onTap: () => context.go('/tools/excel-viewer'),
                  isNew: true,
                ),
                _buildToolCard(
                  context,
                  title: 'Template Gallery',
                  subtitle: 'Start with pre-built tool templates',
                  icon: Icons.widgets,
                  onTap: () => context.go('/tools/templates'),
                  isNew: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildToolCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isNew = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (isNew) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.secondary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'NEW',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSecondary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
