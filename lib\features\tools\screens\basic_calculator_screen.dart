import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/calculation_history_provider.dart';
import '../models/calculation_history.dart';
import 'dart:math' as math;

class BasicCalculatorScreen extends ConsumerStatefulWidget {
  const BasicCalculatorScreen({super.key});

  @override
  ConsumerState<BasicCalculatorScreen> createState() => _BasicCalculatorScreenState();
}

class _BasicCalculatorScreenState extends ConsumerState<BasicCalculatorScreen> {
  String _display = '0';
  String _expression = '';
  String _currentInput = '';
  double _result = 0;
  String _operation = '';
  double _operand = 0;
  bool _waitingForOperand = false;
  double _memory = 0;
  List<String> _history = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Basic Calculator'),
        actions: [
          IconButton(
            onPressed: _showHistory,
            icon: const Icon(Icons.history),
            tooltip: 'History',
          ),
          IconButton(
            onPressed: _clearAll,
            icon: const Icon(Icons.clear_all),
            tooltip: 'Clear All',
          ),
        ],
      ),
      body: Column(
        children: [
          // Display
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Expression/History
                  if (_expression.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Text(
                        _expression,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontFamily: 'monospace',
                        ),
                        textAlign: TextAlign.end,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],

                  // Main display
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      reverse: true,
                      child: Text(
                        _display,
                        style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                          fontSize: 42,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'monospace',
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Memory indicator
          if (_memory != 0) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              color: Theme.of(context).colorScheme.primaryContainer,
              child: Text(
                'M: ${_formatNumber(_memory)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          
          // Buttons
          Expanded(
            flex: 4,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  // Row 1: Memory and Clear
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('MC', _memoryClear, isMemory: true),
                        _buildButton('MR', _memoryRecall, isMemory: true),
                        _buildButton('M+', _memoryAdd, isMemory: true),
                        _buildButton('M-', _memorySubtract, isMemory: true),
                      ],
                    ),
                  ),
                  
                  // Row 2: Clear and operations
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('C', _clear, isFunction: true),
                        _buildButton('CE', _clearEntry, isFunction: true),
                        _buildButton('⌫', _backspace, isFunction: true),
                        _buildButton('÷', () => _inputOperation('/'), isOperation: true),
                      ],
                    ),
                  ),
                  
                  // Row 3: Numbers and multiply
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('7', () => _inputNumber('7')),
                        _buildButton('8', () => _inputNumber('8')),
                        _buildButton('9', () => _inputNumber('9')),
                        _buildButton('×', () => _inputOperation('*'), isOperation: true),
                      ],
                    ),
                  ),
                  
                  // Row 4: Numbers and subtract
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('4', () => _inputNumber('4')),
                        _buildButton('5', () => _inputNumber('5')),
                        _buildButton('6', () => _inputNumber('6')),
                        _buildButton('-', () => _inputOperation('-'), isOperation: true),
                      ],
                    ),
                  ),
                  
                  // Row 5: Numbers and add
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('1', () => _inputNumber('1')),
                        _buildButton('2', () => _inputNumber('2')),
                        _buildButton('3', () => _inputNumber('3')),
                        _buildButton('+', () => _inputOperation('+'), isOperation: true),
                      ],
                    ),
                  ),
                  
                  // Row 6: Zero, decimal, and equals
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('±', _toggleSign, isFunction: true),
                        _buildButton('0', () => _inputNumber('0')),
                        _buildButton('.', _inputDecimal),
                        _buildButton('=', _calculate, isEquals: true),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton(
    String text,
    VoidCallback onPressed, {
    bool isOperation = false,
    bool isEquals = false,
    bool isFunction = false,
    bool isMemory = false,
  }) {
    Color? backgroundColor;
    Color? foregroundColor;
    
    if (isEquals) {
      backgroundColor = Theme.of(context).colorScheme.primary;
      foregroundColor = Theme.of(context).colorScheme.onPrimary;
    } else if (isOperation) {
      backgroundColor = Theme.of(context).colorScheme.secondary;
      foregroundColor = Theme.of(context).colorScheme.onSecondary;
    } else if (isFunction || isMemory) {
      backgroundColor = Theme.of(context).colorScheme.surfaceContainerHighest;
      foregroundColor = Theme.of(context).colorScheme.onSurfaceVariant;
    }

    return Expanded(
      child: Container(
        margin: const EdgeInsets.all(2),
        child: ElevatedButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            onPressed();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.zero,
          ),
          child: Text(
            text,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: foregroundColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  void _inputNumber(String number) {
    setState(() {
      if (_waitingForOperand) {
        _display = number;
        _waitingForOperand = false;
      } else {
        _display = _display == '0' ? number : _display + number;
      }
      _updateRealTimeCalculation();
    });
  }

  void _updateRealTimeCalculation() {
    if (_operation.isNotEmpty && !_waitingForOperand) {
      try {
        final currentValue = double.parse(_display);
        double previewResult = _operand;

        switch (_operation) {
          case '+':
            previewResult = _operand + currentValue;
            break;
          case '-':
            previewResult = _operand - currentValue;
            break;
          case '*':
            previewResult = _operand * currentValue;
            break;
          case '/':
            if (currentValue != 0) {
              previewResult = _operand / currentValue;
            }
            break;
        }

        // Update expression with preview
        _expression = '${_formatNumber(_operand)} $_operation ${_formatNumber(currentValue)} = ${_formatNumber(previewResult)}';
      } catch (e) {
        // Ignore errors during real-time calculation
      }
    }
  }

  void _inputDecimal() {
    setState(() {
      if (_waitingForOperand) {
        _display = '0.';
        _waitingForOperand = false;
      } else if (!_display.contains('.')) {
        _display += '.';
      }
    });
  }

  void _inputOperation(String operation) {
    setState(() {
      if (!_waitingForOperand) {
        if (_operation.isNotEmpty) {
          _calculate();
        } else {
          _result = double.parse(_display);
        }
      }
      
      _operation = operation;
      _operand = _result;
      _waitingForOperand = true;
      _expression = '${_formatNumber(_result)} $operation';
    });
  }

  void _calculate() {
    if (_operation.isEmpty || _waitingForOperand) return;

    final currentValue = double.parse(_display);
    double newResult = _result;

    switch (_operation) {
      case '+':
        newResult = _operand + currentValue;
        break;
      case '-':
        newResult = _operand - currentValue;
        break;
      case '*':
        newResult = _operand * currentValue;
        break;
      case '/':
        if (currentValue != 0) {
          newResult = _operand / currentValue;
        } else {
          _showError('Cannot divide by zero');
          return;
        }
        break;
    }

    final fullExpression = '$_expression ${_formatNumber(currentValue)} =';
    final resultString = _formatNumber(newResult);
    
    // Save to history
    _saveCalculation(fullExpression, resultString);
    
    setState(() {
      _result = newResult;
      _display = resultString;
      _expression = '';
      _operation = '';
      _waitingForOperand = true;
    });
  }

  void _clear() {
    setState(() {
      _display = '0';
      _expression = '';
      _result = 0;
      _operation = '';
      _operand = 0;
      _waitingForOperand = false;
    });
  }

  void _clearEntry() {
    setState(() {
      _display = '0';
      _waitingForOperand = false;
    });
  }

  void _backspace() {
    setState(() {
      if (_display.length > 1) {
        _display = _display.substring(0, _display.length - 1);
      } else {
        _display = '0';
      }
    });
  }

  void _toggleSign() {
    setState(() {
      if (_display != '0') {
        if (_display.startsWith('-')) {
          _display = _display.substring(1);
        } else {
          _display = '-$_display';
        }
      }
    });
  }

  void _memoryClear() {
    setState(() {
      _memory = 0;
    });
  }

  void _memoryRecall() {
    setState(() {
      _display = _formatNumber(_memory);
      _waitingForOperand = false;
    });
  }

  void _memoryAdd() {
    setState(() {
      _memory += double.parse(_display);
    });
  }

  void _memorySubtract() {
    setState(() {
      _memory -= double.parse(_display);
    });
  }

  void _clearAll() {
    setState(() {
      _clear();
      _memory = 0;
      _history.clear();
    });
  }

  String _formatNumber(double number) {
    if (number == number.toInt()) {
      return number.toInt().toString();
    } else {
      return number.toString();
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _saveCalculation(String expression, String result) {
    _history.add('$expression $result');
    
    // Save to database
    final calculation = CalculationHistory.create(
      type: CalculationType.basic,
      expression: expression,
      result: result,
      userId: '', // Will be set by provider
      calculatorName: 'Basic Calculator',
    );
    
    ref.read(calculationHistoryProvider.notifier).addCalculation(calculation);
  }

  void _showHistory() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.3,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      'Calculation History',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _history.clear();
                        });
                        Navigator.of(context).pop();
                      },
                      icon: const Icon(Icons.clear_all),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: _history.isEmpty
                    ? const Center(
                        child: Text('No calculations yet'),
                      )
                    : ListView.builder(
                        controller: scrollController,
                        itemCount: _history.length,
                        reverse: true,
                        itemBuilder: (context, index) {
                          final calculation = _history[_history.length - 1 - index];
                          return ListTile(
                            title: Text(calculation),
                            onTap: () {
                              // Extract result and use it
                              final parts = calculation.split(' = ');
                              if (parts.length == 2) {
                                setState(() {
                                  _display = parts[1];
                                  _waitingForOperand = false;
                                });
                                Navigator.of(context).pop();
                              }
                            },
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
