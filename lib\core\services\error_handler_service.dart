import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class ErrorHandlerService {
  static final ErrorHandlerService _instance = ErrorHandlerService._internal();
  factory ErrorHandlerService() => _instance;
  ErrorHandlerService._internal();

  static ErrorHandlerService get instance => _instance;

  /// Initialize global error handling
  static void initialize() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      _logError('Flutter Error', details.exception, details.stack);
    };

    // Handle async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _logError('Platform Error', error, stack);
      return true;
    };

    // Handle zone errors
    runZonedGuarded(() {
      // App initialization will happen here
    }, (error, stack) {
      _logError('Zone Error', error, stack);
    });
  }

  /// Log error with context
  static void _logError(String type, Object error, StackTrace? stack) {
    debugPrint('=== $type ===');
    debugPrint('Error: $error');
    if (stack != null) {
      debugPrint('Stack trace: $stack');
    }
    debugPrint('================');
  }

  /// Handle database errors
  static String handleDatabaseError(Object error) {
    if (error.toString().contains('database')) {
      return 'Database error occurred. Please try again.';
    } else if (error.toString().contains('permission')) {
      return 'Permission denied. Please check app permissions.';
    } else if (error.toString().contains('network')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Handle network errors
  static String handleNetworkError(Object error) {
    if (error.toString().contains('timeout')) {
      return 'Request timed out. Please check your connection and try again.';
    } else if (error.toString().contains('connection')) {
      return 'Connection failed. Please check your internet connection.';
    } else if (error.toString().contains('404')) {
      return 'Resource not found. Please try again later.';
    } else if (error.toString().contains('500')) {
      return 'Server error. Please try again later.';
    } else {
      return 'Network error occurred. Please check your connection.';
    }
  }

  /// Handle file system errors
  static String handleFileSystemError(Object error) {
    if (error.toString().contains('permission')) {
      return 'File permission denied. Please check app permissions.';
    } else if (error.toString().contains('space')) {
      return 'Insufficient storage space. Please free up some space.';
    } else if (error.toString().contains('not found')) {
      return 'File not found. It may have been moved or deleted.';
    } else {
      return 'File system error occurred. Please try again.';
    }
  }

  /// Handle audio/recording errors
  static String handleAudioError(Object error) {
    if (error.toString().contains('permission')) {
      return 'Microphone permission required. Please grant permission in settings.';
    } else if (error.toString().contains('device')) {
      return 'Audio device not available. Please check your microphone.';
    } else if (error.toString().contains('format')) {
      return 'Audio format not supported. Please try again.';
    } else {
      return 'Audio error occurred. Please try again.';
    }
  }

  /// Handle sync errors
  static String handleSyncError(Object error) {
    if (error.toString().contains('auth')) {
      return 'Authentication failed. Please sign in again.';
    } else if (error.toString().contains('network')) {
      return 'Sync failed due to network issues. Will retry automatically.';
    } else if (error.toString().contains('conflict')) {
      return 'Data conflict detected. Please resolve conflicts manually.';
    } else {
      return 'Sync error occurred. Will retry automatically.';
    }
  }

  /// Show error dialog with appropriate message
  static void showErrorDialog(BuildContext context, Object error, {String? title}) {
    String message = _getErrorMessage(error);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? 'Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar with appropriate message
  static void showErrorSnackBar(BuildContext context, Object error) {
    String message = _getErrorMessage(error);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Get user-friendly error message
  static String _getErrorMessage(Object error) {
    String errorString = error.toString().toLowerCase();
    
    // Database errors
    if (errorString.contains('database') || errorString.contains('sql')) {
      return handleDatabaseError(error);
    }
    
    // Network errors
    if (errorString.contains('network') || errorString.contains('http') || 
        errorString.contains('connection') || errorString.contains('timeout')) {
      return handleNetworkError(error);
    }
    
    // File system errors
    if (errorString.contains('file') || errorString.contains('directory') || 
        errorString.contains('storage') || errorString.contains('path')) {
      return handleFileSystemError(error);
    }
    
    // Audio errors
    if (errorString.contains('audio') || errorString.contains('microphone') || 
        errorString.contains('recording') || errorString.contains('playback')) {
      return handleAudioError(error);
    }
    
    // Sync errors
    if (errorString.contains('sync') || errorString.contains('backup') || 
        errorString.contains('cloud') || errorString.contains('supabase')) {
      return handleSyncError(error);
    }
    
    // Permission errors
    if (errorString.contains('permission')) {
      return 'Permission required. Please grant the necessary permissions in settings.';
    }
    
    // Generic error
    return 'An unexpected error occurred. Please try again.';
  }

  /// Wrap async operations with error handling
  static Future<T?> safeExecute<T>(
    Future<T> Function() operation, {
    BuildContext? context,
    String? errorTitle,
    bool showSnackBar = true,
    bool showDialog = false,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      _logError('Safe Execute Error', error, stackTrace);
      
      if (context != null) {
        if (showDialog) {
          showErrorDialog(context, error, title: errorTitle);
        } else if (showSnackBar) {
          showErrorSnackBar(context, error);
        }
      }
      
      return null;
    }
  }

  /// Wrap sync operations with error handling
  static T? safeExecuteSync<T>(
    T Function() operation, {
    BuildContext? context,
    String? errorTitle,
    bool showSnackBar = true,
    bool showDialog = false,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      _logError('Safe Execute Sync Error', error, stackTrace);
      
      if (context != null) {
        if (showDialog) {
          showErrorDialog(context, error, title: errorTitle);
        } else if (showSnackBar) {
          showErrorSnackBar(context, error);
        }
      }
      
      return null;
    }
  }

  /// Report error to analytics/crash reporting service
  static void reportError(Object error, StackTrace? stackTrace, {
    Map<String, dynamic>? additionalData,
  }) {
    // In a production app, you would send this to a crash reporting service
    // like Firebase Crashlytics, Sentry, etc.
    debugPrint('=== Error Report ===');
    debugPrint('Error: $error');
    if (stackTrace != null) {
      debugPrint('Stack trace: $stackTrace');
    }
    if (additionalData != null) {
      debugPrint('Additional data: $additionalData');
    }
    debugPrint('==================');
  }

  /// Check if error is recoverable
  static bool isRecoverableError(Object error) {
    String errorString = error.toString().toLowerCase();
    
    // Network errors are usually recoverable
    if (errorString.contains('network') || errorString.contains('timeout') || 
        errorString.contains('connection')) {
      return true;
    }
    
    // Permission errors are recoverable if user grants permission
    if (errorString.contains('permission')) {
      return true;
    }
    
    // Storage errors might be recoverable
    if (errorString.contains('space') || errorString.contains('storage')) {
      return true;
    }
    
    return false;
  }

  /// Get retry suggestion for recoverable errors
  static String getRetrySuggestion(Object error) {
    String errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Check your internet connection and try again.';
    }
    
    if (errorString.contains('permission')) {
      return 'Grant the required permissions and try again.';
    }
    
    if (errorString.contains('space') || errorString.contains('storage')) {
      return 'Free up some storage space and try again.';
    }
    
    return 'Please try again in a moment.';
  }
}
