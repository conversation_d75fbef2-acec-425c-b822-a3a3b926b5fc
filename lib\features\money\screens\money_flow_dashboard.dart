import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/ui/master_layout.dart';
import '../../../core/navigation/app_router.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../providers/account_provider.dart';
import '../providers/transaction_provider.dart';
import '../providers/money_settings_provider.dart';
import 'account_form_screen.dart';
import 'transaction_form_screen.dart';

/// Main Money Flow dashboard screen
class MoneyFlowDashboard extends ConsumerStatefulWidget {
  const MoneyFlowDashboard({super.key});

  @override
  ConsumerState<MoneyFlowDashboard> createState() => _MoneyFlowDashboardState();
}

class _MoneyFlowDashboardState extends ConsumerState<MoneyFlowDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final accounts = ref.watch(accountsProvider);
    final transactions = ref.watch(transactionsProvider);
    final formatCurrency = ref.watch(currencyFormatterProvider);
    final shouldHideAmounts = ref.watch(shouldHideAmountsProvider);

    return MasterLayout(
      title: 'Money Flow',
      currentRoute: AppRoutes.money,
      actions: [
        IconButton(
          onPressed: () => _showQuickActions(context),
          icon: const Icon(Icons.add),
          tooltip: 'Quick Actions',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'reports',
              child: ListTile(
                leading: Icon(Icons.analytics),
                title: Text('Reports'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'charts',
              child: ListTile(
                leading: Icon(Icons.pie_chart),
                title: Text('Charts'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'categories',
              child: ListTile(
                leading: Icon(Icons.category),
                title: Text('Categories'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('Settings'),
                dense: true,
              ),
            ),
          ],
        ),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addTransaction(context),
        child: const Icon(Icons.add),
      ),
      body: Scaffold(
        appBar: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.account_balance_wallet), text: 'Accounts'),
            Tab(icon: Icon(Icons.receipt), text: 'Transactions'),
            Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
          ],
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(accounts, transactions, formatCurrency, shouldHideAmounts),
            _buildAccountsTab(accounts, formatCurrency, shouldHideAmounts),
            _buildTransactionsTab(transactions, formatCurrency),
            _buildAnalyticsTab(transactions, formatCurrency),
          ],
        ),
      ),
    );
  }

  /// Build overview tab
  Widget _buildOverviewTab(
    List<Account> accounts,
    List<Transaction> transactions,
    String Function(double) formatCurrency,
    bool shouldHideAmounts,
  ) {
    final totalBalance = accounts.fold<double>(0, (sum, account) => sum + account.balance);
    final recentTransactions = transactions.take(5).toList();
    
    // Calculate monthly stats
    final now = DateTime.now();
    final thisMonth = transactions.where((t) => 
        t.date.year == now.year && t.date.month == now.month).toList();
    final monthlyIncome = thisMonth
        .where((t) => t.type == TransactionType.income)
        .fold<double>(0, (sum, t) => sum + t.amount);
    final monthlyExpenses = thisMonth
        .where((t) => t.type == TransactionType.expense)
        .fold<double>(0, (sum, t) => sum + t.amount);

    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Financial Summary
          SectionHeader(
            title: 'Financial Summary',
            subtitle: 'Your money at a glance',
          ),
          
          ResponsiveGrid(
            mobileColumns: 1,
            tabletColumns: 2,
            desktopColumns: 4,
            spacing: ResponsiveSpacing.md(context),
            runSpacing: ResponsiveSpacing.md(context),
            children: [
              InfoCard(
                icon: Icons.account_balance_wallet,
                title: 'Total Balance',
                value: shouldHideAmounts ? '***' : formatCurrency(totalBalance),
                iconColor: totalBalance >= 0 ? Colors.green : Colors.red,
              ),
              InfoCard(
                icon: Icons.trending_up,
                title: 'This Month Income',
                value: shouldHideAmounts ? '***' : formatCurrency(monthlyIncome),
                iconColor: Colors.green,
              ),
              InfoCard(
                icon: Icons.trending_down,
                title: 'This Month Expenses',
                value: shouldHideAmounts ? '***' : formatCurrency(monthlyExpenses),
                iconColor: Colors.red,
              ),
              InfoCard(
                icon: Icons.account_balance,
                title: 'Net Income',
                value: shouldHideAmounts ? '***' : formatCurrency(monthlyIncome - monthlyExpenses),
                iconColor: (monthlyIncome - monthlyExpenses) >= 0 ? Colors.green : Colors.red,
              ),
            ],
          ),
          
          SizedBox(height: ResponsiveSpacing.lg(context)),
          
          // Quick Actions
          SectionHeader(
            title: 'Quick Actions',
            subtitle: 'Common tasks',
          ),
          
          ResponsiveGrid(
            mobileColumns: 2,
            tabletColumns: 3,
            desktopColumns: 6,
            spacing: ResponsiveSpacing.sm(context),
            runSpacing: ResponsiveSpacing.sm(context),
            children: [
              _buildQuickActionCard(
                'Add Income',
                Icons.add_circle,
                Colors.green,
                () => _addTransaction(context, TransactionType.income),
              ),
              _buildQuickActionCard(
                'Add Expense',
                Icons.remove_circle,
                Colors.red,
                () => _addTransaction(context, TransactionType.expense),
              ),
              _buildQuickActionCard(
                'Transfer',
                Icons.swap_horiz,
                Colors.blue,
                () => _addTransaction(context, TransactionType.transfer),
              ),
              _buildQuickActionCard(
                'New Account',
                Icons.account_balance_wallet,
                Colors.purple,
                () => _addAccount(context),
              ),
              _buildQuickActionCard(
                'Reports',
                Icons.analytics,
                Colors.orange,
                () => context.push('/money-flow/reports'),
              ),
              _buildQuickActionCard(
                'Charts',
                Icons.pie_chart,
                Colors.teal,
                () => context.push('/money-flow/charts'),
              ),
            ],
          ),
          
          SizedBox(height: ResponsiveSpacing.lg(context)),
          
          // Recent Transactions
          SectionHeader(
            title: 'Recent Transactions',
            subtitle: 'Latest activity',
            action: TextButton(
              onPressed: () => _tabController.animateTo(2),
              child: const Text('View All'),
            ),
          ),
          
          if (recentTransactions.isEmpty)
            EmptyState(
              icon: Icons.receipt,
              title: 'No Transactions',
              message: 'Add your first transaction to get started',
              action: StandardActionButton(
                label: 'Add Transaction',
                icon: Icons.add,
                onPressed: () => _addTransaction(context),
              ),
            )
          else
            ...recentTransactions.map((transaction) => _buildTransactionCard(
              transaction,
              formatCurrency,
              shouldHideAmounts,
            )),
        ],
      ),
    );
  }

  /// Build accounts tab
  Widget _buildAccountsTab(
    List<Account> accounts,
    String Function(double) formatCurrency,
    bool shouldHideAmounts,
  ) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Your Accounts',
            subtitle: '${accounts.length} account${accounts.length == 1 ? '' : 's'}',
            action: StandardActionButton(
              label: 'Add Account',
              icon: Icons.add,
              onPressed: () => _addAccount(context),
            ),
          ),
          
          if (accounts.isEmpty)
            EmptyState(
              icon: Icons.account_balance_wallet,
              title: 'No Accounts',
              message: 'Create your first account to start tracking your money',
              action: StandardActionButton(
                label: 'Add Account',
                icon: Icons.add,
                onPressed: () => _addAccount(context),
              ),
            )
          else
            ...accounts.map((account) => _buildAccountCard(
              account,
              formatCurrency,
              shouldHideAmounts,
            )),
        ],
      ),
    );
  }

  /// Build transactions tab
  Widget _buildTransactionsTab(
    List<Transaction> transactions,
    String Function(double) formatCurrency,
  ) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'All Transactions',
            subtitle: '${transactions.length} transaction${transactions.length == 1 ? '' : 's'}',
            action: StandardActionButton(
              label: 'Add Transaction',
              icon: Icons.add,
              onPressed: () => _addTransaction(context),
            ),
          ),
          
          if (transactions.isEmpty)
            EmptyState(
              icon: Icons.receipt,
              title: 'No Transactions',
              message: 'Add your first transaction to start tracking your money flow',
              action: StandardActionButton(
                label: 'Add Transaction',
                icon: Icons.add,
                onPressed: () => _addTransaction(context),
              ),
            )
          else
            ...transactions.map((transaction) => _buildTransactionCard(
              transaction,
              formatCurrency,
              false, // Don't hide amounts in transactions tab
            )),
        ],
      ),
    );
  }

  /// Build analytics tab
  Widget _buildAnalyticsTab(
    List<Transaction> transactions,
    String Function(double) formatCurrency,
  ) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Analytics Overview',
            subtitle: 'Insights into your spending patterns',
          ),
          
          StandardCard(
            child: Container(
              height: 200,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.analytics,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Advanced Analytics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Detailed charts and reports are available in the dedicated sections',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        StandardActionButton(
                          label: 'View Reports',
                          icon: Icons.analytics,
                          onPressed: () => context.push('/money-flow/reports'),
                          isPrimary: false,
                        ),
                        const SizedBox(width: 16),
                        StandardActionButton(
                          label: 'View Charts',
                          icon: Icons.pie_chart,
                          onPressed: () => context.push('/money-flow/charts'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build quick action card
  Widget _buildQuickActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return StandardCard(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build account card
  Widget _buildAccountCard(
    Account account,
    String Function(double) formatCurrency,
    bool shouldHideAmounts,
  ) {
    return StandardCard(
      onTap: () => context.push('/money-flow/account/${account.id}'),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: account.color,
            child: Icon(account.icon, color: Colors.white),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  account.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  account.typeDisplayName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            shouldHideAmounts ? '***' : formatCurrency(account.balance),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: account.balance >= 0 ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// Build transaction card
  Widget _buildTransactionCard(
    Transaction transaction,
    String Function(double) formatCurrency,
    bool shouldHideAmounts,
  ) {
    final isIncome = transaction.type == TransactionType.income;
    final isExpense = transaction.type == TransactionType.expense;
    
    return StandardCard(
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isIncome 
                  ? Colors.green.withValues(alpha: 0.1)
                  : isExpense 
                      ? Colors.red.withValues(alpha: 0.1)
                      : Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isIncome 
                  ? Icons.trending_up
                  : isExpense 
                      ? Icons.trending_down
                      : Icons.swap_horiz,
              color: isIncome 
                  ? Colors.green
                  : isExpense 
                      ? Colors.red
                      : Colors.blue,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            shouldHideAmounts 
                ? '***'
                : '${isExpense ? '-' : isIncome ? '+' : ''}${formatCurrency(transaction.amount)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isIncome 
                  ? Colors.green
                  : isExpense 
                      ? Colors.red
                      : Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  /// Show quick actions dialog
  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildQuickActionButton(
                  'Add Income',
                  Icons.add_circle,
                  Colors.green,
                  () => _addTransaction(context, TransactionType.income),
                ),
                _buildQuickActionButton(
                  'Add Expense',
                  Icons.remove_circle,
                  Colors.red,
                  () => _addTransaction(context, TransactionType.expense),
                ),
                _buildQuickActionButton(
                  'Transfer',
                  Icons.swap_horiz,
                  Colors.blue,
                  () => _addTransaction(context, TransactionType.transfer),
                ),
                _buildQuickActionButton(
                  'New Account',
                  Icons.account_balance_wallet,
                  Colors.purple,
                  () => _addAccount(context),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build quick action button
  Widget _buildQuickActionButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        onTap();
      },
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: color, size: 32),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'reports':
        context.push('/money-flow/reports');
        break;
      case 'charts':
        context.push('/money-flow/charts');
        break;
      case 'categories':
        context.push('/money-flow/categories');
        break;
      case 'settings':
        context.push('/money-flow/settings');
        break;
    }
  }

  /// Add transaction
  void _addTransaction(BuildContext context, [TransactionType? type]) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TransactionFormScreen(),
      ),
    );
  }

  /// Add account
  void _addAccount(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AccountFormScreen(),
      ),
    );
  }
}
