import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../shared/widgets/base_screen.dart';

/// Comprehensive app information screen
class AppInfoScreen extends StatelessWidget {
  const AppInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SettingsBaseScreen(
      title: 'About ShadowSuite',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // App Logo and Name
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.apps,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'ShadowSuite',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Complete Productivity Suite',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.green),
                      ),
                      child: const Text(
                        'Version 1.0.0',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Features Overview
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Features',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    _buildFeatureItem(
                      context,
                      'Memo Suite',
                      'Complete productivity suite with notes, todos, and voice memos',
                      Icons.note_alt_outlined,
                      Colors.blue,
                    ),
                    _buildFeatureItem(
                      context,
                      'Athkar Pro',
                      'Islamic remembrance with Quran, prayer times, and dhikr routines',
                      Icons.mosque_outlined,
                      Colors.green,
                    ),
                    _buildFeatureItem(
                      context,
                      'Tools Builder',
                      'Custom calculators, converters, and Excel-like spreadsheet tools',
                      Icons.build_outlined,
                      Colors.orange,
                    ),
                    _buildFeatureItem(
                      context,
                      'Money Flow',
                      'Financial tracking, budgeting, and expense management',
                      Icons.account_balance_wallet_outlined,
                      Colors.purple,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Technical Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Technical Information',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    _buildInfoRow('Framework', 'Flutter 3.24+'),
                    _buildInfoRow('State Management', 'Riverpod'),
                    _buildInfoRow('Navigation', 'Go Router'),
                    _buildInfoRow('Database', 'Isar (Local) + Supabase (Cloud)'),
                    _buildInfoRow('Design System', 'Material Design 3'),
                    _buildInfoRow('Platforms', 'Windows, Android, iOS, Web'),
                    _buildInfoRow('Languages', 'Arabic, English'),
                    _buildInfoRow('Architecture', 'Clean Architecture + Feature-First'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Credits and Acknowledgments
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Credits & Acknowledgments',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    const Text(
                      'ShadowSuite is built with love using open-source technologies and follows modern development practices.',
                    ),
                    const SizedBox(height: 16),
                    
                    _buildCreditItem('Flutter Team', 'Amazing cross-platform framework'),
                    _buildCreditItem('Riverpod', 'Reactive state management'),
                    _buildCreditItem('Isar Database', 'Fast local database'),
                    _buildCreditItem('Supabase', 'Backend-as-a-Service'),
                    _buildCreditItem('Material Design', 'Beautiful design system'),
                    _buildCreditItem('Open Source Community', 'Countless packages and tools'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _copySystemInfo(context),
                        icon: const Icon(Icons.copy),
                        label: const Text('Copy System Information'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () => _showLicenses(context),
                        icon: const Icon(Icons.description),
                        label: const Text('View Licenses'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Footer
            Text(
              '© 2024 ShadowSuite. Built with Flutter.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildCreditItem(String name, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.favorite, size: 16, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: const TextStyle(color: Colors.black87),
                children: [
                  TextSpan(
                    text: '$name: ',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  TextSpan(text: description),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _copySystemInfo(BuildContext context) {
    final systemInfo = '''
ShadowSuite v1.0.0
Framework: Flutter 3.24+
State Management: Riverpod
Navigation: Go Router
Database: Isar + Supabase
Design: Material Design 3
Platform: ${Theme.of(context).platform.name}
''';

    Clipboard.setData(ClipboardData(text: systemInfo));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('System information copied to clipboard'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showLicenses(BuildContext context) {
    showLicensePage(
      context: context,
      applicationName: 'ShadowSuite',
      applicationVersion: '1.0.0',
      applicationLegalese: '© 2024 ShadowSuite. Built with Flutter.',
    );
  }
}
