import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/custom_dhikr_routine.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Custom dhikr routines provider
final customDhikrRoutinesProvider = StateNotifierProvider<CustomDhikrNotifier, List<CustomDhikrRoutine>>((ref) {
  return CustomDhikrNotifier(ref);
});

class CustomDhikrNotifier extends StateNotifier<List<CustomDhikrRoutine>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  CustomDhikrNotifier(this.ref) : super([]) {
    loadRoutines();
  }

  Future<void> loadRoutines() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final routines = await _db.getAllCustomDhikrRoutines(userId: userProfile.id.toString());
      state = routines;
    }
  }

  Future<void> addRoutine(CustomDhikrRoutine routine) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      routine.userId = userProfile.id.toString();
      final savedRoutine = await _db.saveCustomDhikrRoutine(routine);
      state = [...state, savedRoutine];
    }
  }

  Future<void> updateRoutine(CustomDhikrRoutine routine) async {
    final savedRoutine = await _db.saveCustomDhikrRoutine(routine);
    state = state.map((r) => r.id == savedRoutine.id ? savedRoutine : r).toList();
  }

  Future<void> deleteRoutine(int routineId) async {
    await _db.deleteCustomDhikrRoutine(routineId);
    state = state.where((r) => r.id != routineId).toList();
  }

  Future<void> duplicateRoutine(CustomDhikrRoutine routine) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final duplicatedRoutine = CustomDhikrRoutine.create(
        name: '${routine.name} (Copy)',
        userId: userProfile.id.toString(),
        description: routine.description,
        themeColor: routine.themeColor,
        enableReminders: routine.enableReminders,
        repeatRoutine: routine.repeatRoutine,
        steps: routine.steps.map((step) => DhikrStep.create(
          type: step.type,
          arabicText: step.arabicText,
          transliteration: step.transliteration,
          translation: step.translation,
          targetCount: step.targetCount,
          duration: step.duration,
          reminderText: step.reminderText,
          color: step.color,
          playSound: step.playSound,
          volume: step.volume,
        )).toList(),
      );
      
      await addRoutine(duplicatedRoutine);
    }
  }

  List<CustomDhikrRoutine> getActiveRoutines() {
    return state.where((routine) => routine.isActive).toList();
  }

  List<CustomDhikrRoutine> getRoutinesByCompletion(bool completed) {
    return state.where((routine) => routine.isCompleted == completed).toList();
  }

  CustomDhikrRoutine? getRoutineById(int id) {
    try {
      return state.firstWhere((routine) => routine.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> startRoutine(int routineId) async {
    final routine = getRoutineById(routineId);
    if (routine != null) {
      routine.resetRoutine();
      await updateRoutine(routine);
    }
  }

  Future<void> completeStep(int routineId, int stepId) async {
    final routine = getRoutineById(routineId);
    if (routine != null) {
      final step = routine.steps.firstWhere((s) => s.id == stepId);
      step.incrementCount();
      
      if (step.isCompleted) {
        routine.moveToNextStep();
      }
      
      if (routine.isCompleted) {
        routine.completeRoutine();
      }
      
      await updateRoutine(routine);
    }
  }

  Future<void> resetRoutine(int routineId) async {
    final routine = getRoutineById(routineId);
    if (routine != null) {
      routine.resetRoutine();
      await updateRoutine(routine);
    }
  }

  Future<void> toggleRoutineActive(int routineId) async {
    final routine = getRoutineById(routineId);
    if (routine != null) {
      routine.isActive = !routine.isActive;
      routine.updateTimestamp();
      await updateRoutine(routine);
    }
  }
}

// Active routines provider
final activeCustomDhikrRoutinesProvider = Provider<List<CustomDhikrRoutine>>((ref) {
  final routines = ref.watch(customDhikrRoutinesProvider);
  return routines.where((routine) => routine.isActive).toList();
});

// Completed routines provider
final completedCustomDhikrRoutinesProvider = Provider<List<CustomDhikrRoutine>>((ref) {
  final routines = ref.watch(customDhikrRoutinesProvider);
  return routines.where((routine) => routine.isCompleted).toList();
});

// In-progress routines provider
final inProgressCustomDhikrRoutinesProvider = Provider<List<CustomDhikrRoutine>>((ref) {
  final routines = ref.watch(customDhikrRoutinesProvider);
  return routines.where((routine) => 
    routine.isActive && 
    !routine.isCompleted && 
    routine.completedDhikrCount > 0
  ).toList();
});

// Custom dhikr statistics provider
final customDhikrStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final routines = ref.watch(customDhikrRoutinesProvider);
  
  final totalRoutines = routines.length;
  final activeRoutines = routines.where((r) => r.isActive).length;
  final completedRoutines = routines.where((r) => r.isCompleted).length;
  final totalCompletions = routines.fold(0, (sum, r) => sum + r.totalCompletions);
  final totalTimeSpent = routines.fold(Duration.zero, (sum, r) => sum + r.totalTimeSpent);
  
  final totalDhikrCount = routines.fold(0, (sum, r) => sum + r.totalDhikrCount);
  final completedDhikrCount = routines.fold(0, (sum, r) => sum + r.completedDhikrCount);
  
  return {
    'totalRoutines': totalRoutines,
    'activeRoutines': activeRoutines,
    'completedRoutines': completedRoutines,
    'totalCompletions': totalCompletions,
    'totalTimeSpent': totalTimeSpent,
    'totalDhikrCount': totalDhikrCount,
    'completedDhikrCount': completedDhikrCount,
    'averageProgress': totalRoutines > 0 ? completedRoutines / totalRoutines : 0.0,
  };
});

// Current routine provider for active session
final currentRoutineProvider = StateProvider<CustomDhikrRoutine?>((ref) => null);

// Routine session provider
final routineSessionProvider = StateNotifierProvider<RoutineSessionNotifier, RoutineSession?>((ref) {
  return RoutineSessionNotifier();
});

class RoutineSession {
  final CustomDhikrRoutine routine;
  final DateTime startTime;
  DateTime? endTime;
  Duration elapsedTime = Duration.zero;
  bool isPaused = false;
  
  RoutineSession({
    required this.routine,
    required this.startTime,
  });
  
  bool get isActive => endTime == null;
  Duration get totalDuration => endTime?.difference(startTime) ?? DateTime.now().difference(startTime);
}

class RoutineSessionNotifier extends StateNotifier<RoutineSession?> {
  RoutineSessionNotifier() : super(null);
  
  void startSession(CustomDhikrRoutine routine) {
    state = RoutineSession(
      routine: routine,
      startTime: DateTime.now(),
    );
  }
  
  void endSession() {
    if (state != null) {
      state!.endTime = DateTime.now();
      state = null;
    }
  }
  
  void pauseSession() {
    if (state != null) {
      state!.isPaused = true;
    }
  }
  
  void resumeSession() {
    if (state != null) {
      state!.isPaused = false;
    }
  }
}
