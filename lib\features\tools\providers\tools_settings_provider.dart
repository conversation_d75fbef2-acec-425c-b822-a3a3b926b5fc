import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/tools_settings.dart';

/// Tools settings notifier
class ToolsSettingsNotifier extends StateNotifier<ToolsSettings> {
  ToolsSettingsNotifier() : super(const ToolsSettings()) {
    _loadSettings();
  }

  static const String _settingsKey = 'tools_settings';

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);

      if (settingsJson != null) {
        final Map<String, dynamic> data = json.decode(settingsJson);
        state = ToolsSettings.fromJson(data);
      }
    } catch (e) {
      // If loading fails, keep default settings
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(state.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      // If saving fails, continue silently
    }
  }

  /// Update settings
  Future<void> updateSettings(ToolsSettings newSettings) async {
    state = newSettings;
    await _saveSettings();
  }

  /// Update UI builder settings
  Future<void> updateUIBuilderSettings({
    bool? enableDragDrop,
    bool? enableSnapping,
    GridSnapMode? gridSnapMode,
    int? gridSize,
    bool? showGrid,
    bool? showRulers,
    bool? showGuidelines,
    double? zoomLevel,
    bool? enableAutoLayout,
    bool? enableResponsivePreview,
  }) async {
    state = state.copyWith(
      enableDragDrop: enableDragDrop,
      enableSnapping: enableSnapping,
      gridSnapMode: gridSnapMode,
      gridSize: gridSize,
      showGrid: showGrid,
      showRulers: showRulers,
      showGuidelines: showGuidelines,
      zoomLevel: zoomLevel,
      enableAutoLayout: enableAutoLayout,
      enableResponsivePreview: enableResponsivePreview,
    );
    await _saveSettings();
  }

  /// Update template settings
  Future<void> updateTemplateSettings({
    String? defaultTemplate,
    List<TemplateCategory>? enabledCategories,
    bool? enableCustomTemplates,
    bool? autoSaveTemplates,
    String? templatesDirectory,
    bool? enableTemplateSharing,
    bool? enableTemplateVersioning,
  }) async {
    state = state.copyWith(
      defaultTemplate: defaultTemplate,
      enabledCategories: enabledCategories,
      enableCustomTemplates: enableCustomTemplates,
      autoSaveTemplates: autoSaveTemplates,
      templatesDirectory: templatesDirectory,
      enableTemplateSharing: enableTemplateSharing,
      enableTemplateVersioning: enableTemplateVersioning,
    );
    await _saveSettings();
  }

  /// Update code generation settings
  Future<void> updateCodeGenerationSettings({
    CodeGenerationStyle? codeStyle,
    bool? enableAutoGeneration,
    bool? generateComments,
    bool? generateDocumentation,
    bool? enableCodeFormatting,
    String? indentationStyle,
    int? indentationSize,
    bool? enableLinting,
  }) async {
    state = state.copyWith(
      codeStyle: codeStyle,
      enableAutoGeneration: enableAutoGeneration,
      generateComments: generateComments,
      generateDocumentation: generateDocumentation,
      enableCodeFormatting: enableCodeFormatting,
      indentationStyle: indentationStyle,
      indentationSize: indentationSize,
      enableLinting: enableLinting,
    );
    await _saveSettings();
  }

  /// Update component library settings
  Future<void> updateComponentLibrarySettings({
    List<String>? enabledLibraries,
    bool? enableCustomComponents,
    String? customComponentsPath,
    bool? enableComponentPreview,
    bool? enableComponentSearch,
    bool? enableComponentCategories,
  }) async {
    state = state.copyWith(
      enabledLibraries: enabledLibraries,
      enableCustomComponents: enableCustomComponents,
      customComponentsPath: customComponentsPath,
      enableComponentPreview: enableComponentPreview,
      enableComponentSearch: enableComponentSearch,
      enableComponentCategories: enableComponentCategories,
    );
    await _saveSettings();
  }

  /// Update export settings
  Future<void> updateExportSettings({
    List<ExportFormat>? enabledExportFormats,
    ExportFormat? defaultExportFormat,
    String? exportDirectory,
    bool? enableBatchExport,
    bool? enableExportPreview,
    bool? compressExports,
  }) async {
    state = state.copyWith(
      enabledExportFormats: enabledExportFormats,
      defaultExportFormat: defaultExportFormat,
      exportDirectory: exportDirectory,
      enableBatchExport: enableBatchExport,
      enableExportPreview: enableExportPreview,
      compressExports: compressExports,
    );
    await _saveSettings();
  }

  /// Update auto-save settings
  Future<void> updateAutoSaveSettings({
    bool? enableAutoSave,
    int? autoSaveIntervalSeconds,
    bool? saveOnPreview,
    bool? saveOnExport,
    int? maxAutoSaveFiles,
    String? autoSaveDirectory,
  }) async {
    state = state.copyWith(
      enableAutoSave: enableAutoSave,
      autoSaveIntervalSeconds: autoSaveIntervalSeconds,
      saveOnPreview: saveOnPreview,
      saveOnExport: saveOnExport,
      maxAutoSaveFiles: maxAutoSaveFiles,
      autoSaveDirectory: autoSaveDirectory,
    );
    await _saveSettings();
  }

  /// Update collaboration settings
  Future<void> updateCollaborationSettings({
    bool? enableCollaboration,
    bool? enableRealTimeEditing,
    bool? enableComments,
    bool? enableVersionHistory,
    bool? enableUserPresence,
    String? collaborationServer,
    int? maxCollaborators,
  }) async {
    state = state.copyWith(
      enableCollaboration: enableCollaboration,
      enableRealTimeEditing: enableRealTimeEditing,
      enableComments: enableComments,
      enableVersionHistory: enableVersionHistory,
      enableUserPresence: enableUserPresence,
      collaborationServer: collaborationServer,
      maxCollaborators: maxCollaborators,
    );
    await _saveSettings();
  }

  /// Update version control settings
  Future<void> updateVersionControlSettings({
    VersionControlSystem? versionControlSystem,
    bool? enableAutoCommit,
    String? defaultCommitMessage,
    bool? enableBranchManagement,
    String? defaultBranch,
    bool? enableMergeConflictResolution,
  }) async {
    state = state.copyWith(
      versionControlSystem: versionControlSystem,
      enableAutoCommit: enableAutoCommit,
      defaultCommitMessage: defaultCommitMessage,
      enableBranchManagement: enableBranchManagement,
      defaultBranch: defaultBranch,
      enableMergeConflictResolution: enableMergeConflictResolution,
    );
    await _saveSettings();
  }

  /// Update deployment settings
  Future<void> updateDeploymentSettings({
    List<DeploymentTarget>? enabledTargets,
    DeploymentTarget? defaultTarget,
    bool? enableAutoDeploy,
    bool? enablePreviewDeploy,
    bool? enableProductionDeploy,
  }) async {
    state = state.copyWith(
      enabledTargets: enabledTargets,
      defaultTarget: defaultTarget,
      enableAutoDeploy: enableAutoDeploy,
      enablePreviewDeploy: enablePreviewDeploy,
      enableProductionDeploy: enableProductionDeploy,
    );
    await _saveSettings();
  }

  /// Update performance settings
  Future<void> updatePerformanceSettings({
    bool? enablePerformanceMonitoring,
    bool? enableCodeOptimization,
    bool? enableAssetOptimization,
    bool? enableLazyLoading,
    int? maxUndoSteps,
    bool? enableMemoryOptimization,
    int? maxCacheSize,
  }) async {
    state = state.copyWith(
      enablePerformanceMonitoring: enablePerformanceMonitoring,
      enableCodeOptimization: enableCodeOptimization,
      enableAssetOptimization: enableAssetOptimization,
      enableLazyLoading: enableLazyLoading,
      maxUndoSteps: maxUndoSteps,
      enableMemoryOptimization: enableMemoryOptimization,
      maxCacheSize: maxCacheSize,
    );
    await _saveSettings();
  }

  /// Update editor settings
  Future<void> updateEditorSettings({
    String? editorTheme,
    double? editorFontSize,
    String? editorFontFamily,
    bool? enableSyntaxHighlighting,
    bool? enableCodeCompletion,
    bool? enableErrorHighlighting,
    bool? enableBracketMatching,
    bool? enableLineNumbers,
    bool? enableWordWrap,
  }) async {
    state = state.copyWith(
      editorTheme: editorTheme,
      editorFontSize: editorFontSize,
      editorFontFamily: editorFontFamily,
      enableSyntaxHighlighting: enableSyntaxHighlighting,
      enableCodeCompletion: enableCodeCompletion,
      enableErrorHighlighting: enableErrorHighlighting,
      enableBracketMatching: enableBracketMatching,
      enableLineNumbers: enableLineNumbers,
      enableWordWrap: enableWordWrap,
    );
    await _saveSettings();
  }

  /// Update preview settings
  Future<void> updatePreviewSettings({
    bool? enableLivePreview,
    bool? enableHotReload,
    bool? enableDeviceSimulation,
    bool? enableNetworkSimulation,
    List<String>? previewDevices,
    bool? enableAccessibilityPreview,
    bool? enablePerformancePreview,
  }) async {
    state = state.copyWith(
      enableLivePreview: enableLivePreview,
      enableHotReload: enableHotReload,
      enableDeviceSimulation: enableDeviceSimulation,
      enableNetworkSimulation: enableNetworkSimulation,
      previewDevices: previewDevices,
      enableAccessibilityPreview: enableAccessibilityPreview,
      enablePerformancePreview: enablePerformancePreview,
    );
    await _saveSettings();
  }

  /// Update integration settings
  Future<void> updateIntegrationSettings({
    bool? enableFigmaIntegration,
    bool? enableSketchIntegration,
    bool? enableAdobeXDIntegration,
    bool? enableAPIIntegration,
    bool? enableDatabaseIntegration,
  }) async {
    state = state.copyWith(
      enableFigmaIntegration: enableFigmaIntegration,
      enableSketchIntegration: enableSketchIntegration,
      enableAdobeXDIntegration: enableAdobeXDIntegration,
      enableAPIIntegration: enableAPIIntegration,
      enableDatabaseIntegration: enableDatabaseIntegration,
    );
    await _saveSettings();
  }

  /// Update backup settings
  Future<void> updateBackupSettings({
    bool? enableAutoBackup,
    bool? backupToCloud,
    bool? syncAcrossDevices,
    int? backupFrequencyHours,
    int? maxBackupFiles,
    bool? enableIncrementalBackup,
  }) async {
    state = state.copyWith(
      enableAutoBackup: enableAutoBackup,
      backupToCloud: backupToCloud,
      syncAcrossDevices: syncAcrossDevices,
      backupFrequencyHours: backupFrequencyHours,
      maxBackupFiles: maxBackupFiles,
      enableIncrementalBackup: enableIncrementalBackup,
    );
    await _saveSettings();
  }

  /// Update security settings
  Future<void> updateSecuritySettings({
    bool? enableEncryption,
    bool? requirePinForAccess,
    String? pinHash,
    bool? enableBiometricAuth,
    int? autoLockMinutes,
    bool? enableAuditLog,
    bool? enableSecureMode,
  }) async {
    state = state.copyWith(
      enableEncryption: enableEncryption,
      requirePinForAccess: requirePinForAccess,
      pinHash: pinHash,
      enableBiometricAuth: enableBiometricAuth,
      autoLockMinutes: autoLockMinutes,
      enableAuditLog: enableAuditLog,
      enableSecureMode: enableSecureMode,
    );
    await _saveSettings();
  }

  /// Update advanced settings
  Future<void> updateAdvancedSettings({
    bool? enableDebugMode,
    bool? enableBetaFeatures,
    bool? enableExperimentalFeatures,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    bool? enablePerformanceReporting,
  }) async {
    state = state.copyWith(
      enableDebugMode: enableDebugMode,
      enableBetaFeatures: enableBetaFeatures,
      enableExperimentalFeatures: enableExperimentalFeatures,
      enableAnalytics: enableAnalytics,
      enableCrashReporting: enableCrashReporting,
      enablePerformanceReporting: enablePerformanceReporting,
    );
    await _saveSettings();
  }

  /// Reset to default settings
  Future<void> resetToDefaults() async {
    state = const ToolsSettings();
    await _saveSettings();
  }

  /// Export settings as JSON string
  String exportSettings() {
    return json.encode(state.toJson());
  }

  /// Import settings from JSON string
  Future<bool> importSettings(String settingsJson) async {
    try {
      final Map<String, dynamic> data = json.decode(settingsJson);
      final importedSettings = ToolsSettings.fromJson(data);
      state = importedSettings;
      await _saveSettings();
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Provider for tools settings
final toolsSettingsProvider = StateNotifierProvider<ToolsSettingsNotifier, ToolsSettings>((ref) {
  return ToolsSettingsNotifier();
});

/// Provider for UI builder settings
final uiBuilderSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(toolsSettingsProvider);
  return {
    'dragDrop': settings.enableDragDrop,
    'snapping': settings.enableSnapping,
    'gridSnapMode': settings.gridSnapMode,
    'gridSize': settings.gridSize,
    'showGrid': settings.showGrid,
    'showRulers': settings.showRulers,
    'showGuidelines': settings.showGuidelines,
    'zoomLevel': settings.zoomLevel,
    'autoLayout': settings.enableAutoLayout,
    'responsivePreview': settings.enableResponsivePreview,
  };
});

/// Provider for code generation settings
final codeGenerationSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(toolsSettingsProvider);
  return {
    'style': settings.codeStyle,
    'autoGeneration': settings.enableAutoGeneration,
    'comments': settings.generateComments,
    'documentation': settings.generateDocumentation,
    'formatting': settings.enableCodeFormatting,
    'indentationStyle': settings.indentationStyle,
    'indentationSize': settings.indentationSize,
    'linting': settings.enableLinting,
  };
});

/// Provider for export settings
final exportSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(toolsSettingsProvider);
  return {
    'enabledFormats': settings.enabledExportFormats,
    'defaultFormat': settings.defaultExportFormat,
    'directory': settings.exportDirectory,
    'batchExport': settings.enableBatchExport,
    'preview': settings.enableExportPreview,
    'compress': settings.compressExports,
  };
});
