import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/spreadsheet_cell.dart';
import '../models/tool_definition.dart';
import '../services/tool_storage_service.dart';

/// Screen for saving a custom tool with backend data
class ToolSaveScreen extends ConsumerStatefulWidget {
  final Map<String, SpreadsheetCell> backendData;
  final Function(int toolId)? onSaved;
  final ToolDefinition? existingTool;

  const ToolSaveScreen({
    super.key,
    required this.backendData,
    this.onSaved,
    this.existingTool,
  });

  @override
  ConsumerState<ToolSaveScreen> createState() => _ToolSaveScreenState();
}

class _ToolSaveScreenState extends ConsumerState<ToolSaveScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _storageService = ToolStorageService();
  
  bool _isPasswordProtected = false;
  bool _isSaving = false;
  String _selectedCategory = 'Custom';

  final List<String> _categories = [
    'Custom',
    'Calculator',
    'Converter',
    'Finance',
    'Utility',
    'Education',
    'Health',
    'Productivity',
  ];

  @override
  void initState() {
    super.initState();
    
    // Pre-fill if editing existing tool
    if (widget.existingTool != null) {
      _nameController.text = widget.existingTool!.name;
      _descriptionController.text = widget.existingTool!.description;
      _isPasswordProtected = widget.existingTool!.isPasswordProtected;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingTool != null ? 'Update Tool' : 'Save Tool'),
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        actions: [
          if (widget.existingTool != null)
            IconButton(
              onPressed: _showDeleteConfirmation,
              icon: const Icon(Icons.delete),
              tooltip: 'Delete Tool',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Tool Preview Card
              _buildPreviewCard(),
              
              const SizedBox(height: 24),
              
              // Tool Information
              _buildToolInformation(),
              
              const SizedBox(height: 24),
              
              // Backend Data Summary
              _buildBackendSummary(),
              
              const SizedBox(height: 32),
              
              // Action Buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.build_circle,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tool Preview',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'This tool will be saved with ${widget.backendData.length} data cells',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            if (widget.backendData.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Sample Data:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ...widget.backendData.entries.take(3).map((entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        entry.key,
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        entry.value.value?.toString() ?? '',
                        style: Theme.of(context).textTheme.bodySmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              )),
              if (widget.backendData.length > 3)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    '... and ${widget.backendData.length - 3} more cells',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildToolInformation() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tool Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Tool Name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Tool Name *',
                hintText: 'Enter a descriptive name for your tool',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a tool name';
                }
                if (value.trim().length < 3) {
                  return 'Tool name must be at least 3 characters';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Tool Description
            TextFormField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Description *',
                hintText: 'Describe what this tool does and how to use it',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a description';
                }
                if (value.trim().length < 10) {
                  return 'Description must be at least 10 characters';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Category Selection
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: _categories.map((category) => DropdownMenuItem(
                value: category,
                child: Text(category),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value ?? 'Custom';
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Password Protection
            CheckboxListTile(
              title: const Text('Password Protected'),
              subtitle: const Text('Require password to access this tool'),
              value: _isPasswordProtected,
              onChanged: (value) {
                setState(() {
                  _isPasswordProtected = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackendSummary() {
    final formulaCells = widget.backendData.values.where((cell) => cell.hasFormula).length;
    final dataCells = widget.backendData.length - formulaCells;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Backend Data Summary',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Cells',
                    widget.backendData.length.toString(),
                    Icons.grid_on,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Data Cells',
                    dataCells.toString(),
                    Icons.data_object,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Formulas',
                    formulaCells.toString(),
                    Icons.functions,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,

          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: _isSaving ? null : _saveTool,
          icon: _isSaving 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.save),
          label: Text(widget.existingTool != null ? 'Update Tool' : 'Save Tool'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        
        const SizedBox(height: 12),
        
        OutlinedButton.icon(
          onPressed: _isSaving ? null : () => context.pop(),
          icon: const Icon(Icons.cancel),
          label: const Text('Cancel'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        
        if (widget.existingTool != null) ...[
          const SizedBox(height: 12),
          
          OutlinedButton.icon(
            onPressed: _isSaving ? null : _duplicateTool,
            icon: const Icon(Icons.copy),
            label: const Text('Save as Copy'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _saveTool() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final tool = widget.existingTool?.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        isPasswordProtected: _isPasswordProtected,
        backendData: _encodeBackendData(widget.backendData),
        updatedAt: DateTime.now(),
      ) ?? ToolDefinition.create(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        userId: 'user_001', // TODO: Get actual user ID
        backendData: _encodeBackendData(widget.backendData),
        isPasswordProtected: _isPasswordProtected,
      );

      await _storageService.saveTool(tool);

      if (mounted) {
        widget.onSaved?.call(tool.id!);
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save tool: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _duplicateTool() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final newTool = ToolDefinition.create(
        name: '${_nameController.text.trim()} (Copy)',
        description: _descriptionController.text.trim(),
        userId: 'user_001', // TODO: Get actual user ID
        backendData: _encodeBackendData(widget.backendData),
        isPasswordProtected: false, // Remove password protection on copy
      );

      await _storageService.saveTool(newTool);

      if (mounted) {
        widget.onSaved?.call(newTool.id!);
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to duplicate tool: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tool'),
        content: Text('Are you sure you want to delete "${widget.existingTool?.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteTool();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteTool() async {
    if (widget.existingTool?.id == null) return;

    try {
      await _storageService.deleteTool(widget.existingTool!.id!);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tool deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete tool: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _encodeBackendData(Map<String, SpreadsheetCell> backendData) {
    final jsonData = <String, dynamic>{};
    for (final entry in backendData.entries) {
      jsonData[entry.key] = entry.value.toJson();
    }
    return jsonEncode(jsonData);
  }
}
