enum AthkarDisplayMode {
  arabicOnly,
  arabicWithTranslation,
  arabicWithTransliteration,
  arabicWithBoth,
  translationOnly,
}

class AthkarDisplayPreferences {
  int id = 0;
  AthkarDisplayMode displayMode = AthkarDisplayMode.arabicWithBoth;
  String preferredLanguage = 'en'; // ISO language code
  bool showReference = true;
  bool showRecommendedCount = true;
  double arabicFontSize = 24.0;
  double transliterationFontSize = 18.0;
  double translationFontSize = 16.0;
  String arabicFontFamily = 'default';
  bool enableRightToLeft = true;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  AthkarDisplayPreferences();

  AthkarDisplayPreferences.create({
    required this.userId,
    this.displayMode = AthkarDisplayMode.arabicWithBoth,
    this.preferredLanguage = 'en',
    this.showReference = true,
    this.showRecommendedCount = true,
    this.arabicFontSize = 24.0,
    this.transliterationFontSize = 18.0,
    this.translationFontSize = 16.0,
    this.arabicFontFamily = 'default',
    this.enableRightToLeft = true,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void updatePreferences({
    AthkarDisplayMode? displayMode,
    String? preferredLanguage,
    bool? showReference,
    bool? showRecommendedCount,
    double? arabicFontSize,
    double? transliterationFontSize,
    double? translationFontSize,
    String? arabicFontFamily,
    bool? enableRightToLeft,
  }) {
    if (displayMode != null) this.displayMode = displayMode;
    if (preferredLanguage != null) this.preferredLanguage = preferredLanguage;
    if (showReference != null) this.showReference = showReference;
    if (showRecommendedCount != null) this.showRecommendedCount = showRecommendedCount;
    if (arabicFontSize != null) this.arabicFontSize = arabicFontSize;
    if (transliterationFontSize != null) this.transliterationFontSize = transliterationFontSize;
    if (translationFontSize != null) this.translationFontSize = translationFontSize;
    if (arabicFontFamily != null) this.arabicFontFamily = arabicFontFamily;
    if (enableRightToLeft != null) this.enableRightToLeft = enableRightToLeft;
    
    updateTimestamp();
  }

  String get displayModeDescription {
    switch (displayMode) {
      case AthkarDisplayMode.arabicOnly:
        return 'Arabic Only';
      case AthkarDisplayMode.arabicWithTranslation:
        return 'Arabic + Translation';
      case AthkarDisplayMode.arabicWithTransliteration:
        return 'Arabic + Transliteration';
      case AthkarDisplayMode.arabicWithBoth:
        return 'Arabic + Translation + Transliteration';
      case AthkarDisplayMode.translationOnly:
        return 'Translation Only';
    }
  }

  bool get shouldShowArabic {
    return displayMode != AthkarDisplayMode.translationOnly;
  }

  bool get shouldShowTransliteration {
    return displayMode == AthkarDisplayMode.arabicWithTransliteration ||
           displayMode == AthkarDisplayMode.arabicWithBoth;
  }

  bool get shouldShowTranslation {
    return displayMode == AthkarDisplayMode.arabicWithTranslation ||
           displayMode == AthkarDisplayMode.arabicWithBoth ||
           displayMode == AthkarDisplayMode.translationOnly;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'displayMode': displayMode.name,
      'preferredLanguage': preferredLanguage,
      'showReference': showReference,
      'showRecommendedCount': showRecommendedCount,
      'arabicFontSize': arabicFontSize,
      'transliterationFontSize': transliterationFontSize,
      'translationFontSize': translationFontSize,
      'arabicFontFamily': arabicFontFamily,
      'enableRightToLeft': enableRightToLeft,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory AthkarDisplayPreferences.fromJson(Map<String, dynamic> json) {
    final preferences = AthkarDisplayPreferences();
    preferences.id = json['id'] ?? 0;
    preferences.displayMode = AthkarDisplayMode.values.firstWhere(
      (mode) => mode.name == json['displayMode'],
      orElse: () => AthkarDisplayMode.arabicWithBoth,
    );
    preferences.preferredLanguage = json['preferredLanguage'] ?? 'en';
    preferences.showReference = json['showReference'] ?? true;
    preferences.showRecommendedCount = json['showRecommendedCount'] ?? true;
    preferences.arabicFontSize = json['arabicFontSize']?.toDouble() ?? 24.0;
    preferences.transliterationFontSize = json['transliterationFontSize']?.toDouble() ?? 18.0;
    preferences.translationFontSize = json['translationFontSize']?.toDouble() ?? 16.0;
    preferences.arabicFontFamily = json['arabicFontFamily'] ?? 'default';
    preferences.enableRightToLeft = json['enableRightToLeft'] ?? true;
    preferences.userId = json['userId'] ?? '';
    preferences.createdAt = DateTime.parse(json['createdAt']);
    preferences.updatedAt = DateTime.parse(json['updatedAt']);
    preferences.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    preferences.isDeleted = json['isDeleted'] ?? false;
    preferences.syncId = json['syncId'];
    return preferences;
  }

  // Create default preferences for a user
  static AthkarDisplayPreferences createDefault(String userId) {
    return AthkarDisplayPreferences.create(
      userId: userId,
      displayMode: AthkarDisplayMode.arabicWithBoth,
      preferredLanguage: 'en',
      showReference: true,
      showRecommendedCount: true,
      arabicFontSize: 24.0,
      transliterationFontSize: 18.0,
      translationFontSize: 16.0,
      arabicFontFamily: 'default',
      enableRightToLeft: true,
    );
  }

  // Get available languages
  static Map<String, String> get availableLanguages => {
    'en': 'English',
    'ar': 'العربية',
    'ur': 'اردو',
    'tr': 'Türkçe',
    'id': 'Bahasa Indonesia',
    'ms': 'Bahasa Melayu',
    'fr': 'Français',
    'de': 'Deutsch',
    'es': 'Español',
    'ru': 'Русский',
  };

  // Get available Arabic fonts
  static List<String> get availableArabicFonts => [
    'default',
    'Amiri',
    'Scheherazade',
    'Lateef',
    'Noto Sans Arabic',
    'Cairo',
  ];
}
