import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculation_history.dart';
import '../providers/calculation_history_provider.dart';

class TipCalculatorScreen extends ConsumerStatefulWidget {
  const TipCalculatorScreen({super.key});

  @override
  ConsumerState<TipCalculatorScreen> createState() => _TipCalculatorScreenState();
}

class _TipCalculatorScreenState extends ConsumerState<TipCalculatorScreen> {
  final _billAmountController = TextEditingController();
  final _customTipController = TextEditingController();
  final _peopleController = TextEditingController(text: '1');
  
  double _billAmount = 0;
  double _tipPercentage = 18;
  int _numberOfPeople = 1;
  bool _useCustomTip = false;
  
  double get _tipAmount => _billAmount * (_tipPercentage / 100);
  double get _totalAmount => _billAmount + _tipAmount;
  double get _amountPerPerson => _totalAmount / _numberOfPeople;
  double get _tipPerPerson => _tipAmount / _numberOfPeople;

  final List<double> _quickTipPercentages = [10, 15, 18, 20, 25];

  @override
  void initState() {
    super.initState();
    _billAmountController.addListener(_updateCalculation);
    _customTipController.addListener(_updateCustomTip);
    _peopleController.addListener(_updatePeople);
  }

  @override
  void dispose() {
    _billAmountController.dispose();
    _customTipController.dispose();
    _peopleController.dispose();
    super.dispose();
  }

  void _updateCalculation() {
    setState(() {
      _billAmount = double.tryParse(_billAmountController.text) ?? 0;
    });
  }

  void _updateCustomTip() {
    if (_useCustomTip) {
      setState(() {
        _tipPercentage = double.tryParse(_customTipController.text) ?? 0;
      });
    }
  }

  void _updatePeople() {
    setState(() {
      _numberOfPeople = int.tryParse(_peopleController.text) ?? 1;
      if (_numberOfPeople < 1) _numberOfPeople = 1;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tip Calculator'),
        actions: [
          IconButton(
            onPressed: _saveCalculation,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bill Amount Input
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bill Amount',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _billAmountController,
                      decoration: const InputDecoration(
                        labelText: 'Enter bill amount',
                        prefixText: '\$',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Tip Percentage Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tip Percentage',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    // Quick tip buttons
                    Wrap(
                      spacing: 8,
                      children: _quickTipPercentages.map((percentage) {
                        final isSelected = !_useCustomTip && _tipPercentage == percentage;
                        return FilterChip(
                          label: Text('${percentage.toInt()}%'),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _useCustomTip = false;
                              _tipPercentage = percentage;
                            });
                          },
                        );
                      }).toList(),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Custom tip input
                    Row(
                      children: [
                        Checkbox(
                          value: _useCustomTip,
                          onChanged: (value) {
                            setState(() {
                              _useCustomTip = value ?? false;
                              if (_useCustomTip) {
                                _tipPercentage = double.tryParse(_customTipController.text) ?? 0;
                              }
                            });
                          },
                        ),
                        const Text('Custom tip: '),
                        Expanded(
                          child: TextFormField(
                            controller: _customTipController,
                            enabled: _useCustomTip,
                            decoration: const InputDecoration(
                              hintText: 'Enter custom %',
                              suffixText: '%',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Tip slider
                    Text('Tip: ${_tipPercentage.toStringAsFixed(1)}%'),
                    Slider(
                      value: _tipPercentage,
                      min: 0,
                      max: 50,
                      divisions: 100,
                      onChanged: (value) {
                        setState(() {
                          _tipPercentage = value;
                          _useCustomTip = true;
                          _customTipController.text = value.toStringAsFixed(1);
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Number of People
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Split Between',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        IconButton(
                          onPressed: _numberOfPeople > 1 ? () {
                            setState(() {
                              _numberOfPeople--;
                              _peopleController.text = _numberOfPeople.toString();
                            });
                          } : null,
                          icon: const Icon(Icons.remove),
                        ),
                        Expanded(
                          child: TextFormField(
                            controller: _peopleController,
                            decoration: const InputDecoration(
                              labelText: 'Number of people',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            textAlign: TextAlign.center,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _numberOfPeople++;
                              _peopleController.text = _numberOfPeople.toString();
                            });
                          },
                          icon: const Icon(Icons.add),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Results
            Card(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Calculation Results',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    _buildResultRow('Bill Amount', _billAmount),
                    _buildResultRow('Tip Amount', _tipAmount),
                    _buildResultRow('Total Amount', _totalAmount, isTotal: true),
                    
                    if (_numberOfPeople > 1) ...[
                      const Divider(),
                      Text(
                        'Per Person',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      _buildResultRow('Amount Per Person', _amountPerPerson),
                      _buildResultRow('Tip Per Person', _tipPerPerson),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Service Quality Guide
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tipping Guide',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('• Excellent service: 20-25%'),
                    const Text('• Good service: 18-20%'),
                    const Text('• Average service: 15-18%'),
                    const Text('• Poor service: 10-15%'),
                    const Text('• Takeout/Counter service: 10-15%'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quick Actions
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _clearAll,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _shareResults,
                    icon: const Icon(Icons.share),
                    label: const Text('Share'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  void _clearAll() {
    setState(() {
      _billAmountController.clear();
      _customTipController.clear();
      _peopleController.text = '1';
      _billAmount = 0;
      _tipPercentage = 18;
      _numberOfPeople = 1;
      _useCustomTip = false;
    });
  }

  void _saveCalculation() {
    if (_billAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a bill amount first')),
      );
      return;
    }

    final expression = 'Tip: \$${_billAmount.toStringAsFixed(2)} bill, '
        '${_tipPercentage.toStringAsFixed(1)}% tip, '
        '$_numberOfPeople ${_numberOfPeople == 1 ? 'person' : 'people'}';
    
    final result = 'Total: \$${_totalAmount.toStringAsFixed(2)}, '
        'Tip: \$${_tipAmount.toStringAsFixed(2)}';
    
    final calculation = CalculationHistory.create(
      type: CalculationType.tip,
      expression: expression,
      result: result,
      userId: '',
    );
    
    ref.read(calculationHistoryProvider.notifier).addCalculation(calculation);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Calculation saved to history')),
    );
  }

  void _shareResults() {
    if (_billAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a bill amount first')),
      );
      return;
    }

    final shareText = '''
Tip Calculator Results:
Bill Amount: \$${_billAmount.toStringAsFixed(2)}
Tip (${_tipPercentage.toStringAsFixed(1)}%): \$${_tipAmount.toStringAsFixed(2)}
Total: \$${_totalAmount.toStringAsFixed(2)}
${_numberOfPeople > 1 ? 'Per Person: \$${_amountPerPerson.toStringAsFixed(2)}' : ''}

Calculated with ShadowSuite
''';

    // In a real app, you would use the share package
    // For now, copy to clipboard
    Clipboard.setData(ClipboardData(text: shareText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Results copied to clipboard')),
    );
  }
}
