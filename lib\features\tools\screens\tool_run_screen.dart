import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/custom_tool.dart';
import '../models/spreadsheet_cell.dart';
import '../services/formula_engine_service.dart';
import '../services/tool_persistence_service.dart';

/// Screen for running/executing a saved custom tool through its UI interface
class ToolRunScreen extends ConsumerStatefulWidget {
  final dynamic tool; // Accept both CustomTool and ToolDefinition for now

  const ToolRunScreen({
    super.key,
    required this.tool,
  });

  @override
  ConsumerState<ToolRunScreen> createState() => _ToolRunScreenState();
}

class _ToolRunScreenState extends ConsumerState<ToolRunScreen> {
  final FormulaEngineService _formulaEngine = FormulaEngineService();
  final ToolPersistenceService _persistenceService = ToolPersistenceService();

  Map<String, SpreadsheetCell> _backendData = {};
  List<ToolComponent> _uiComponents = [];
  final Map<String, TextEditingController> _inputControllers = {};
  final Map<String, dynamic> _componentValues = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadToolData();
  }

  @override
  void dispose() {
    for (final controller in _inputControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  /// Convert ToolDefinition to CustomTool for compatibility
  CustomTool _convertToCustomTool(dynamic tool) {
    if (tool is CustomTool) {
      return tool;
    }

    // Convert ToolDefinition to CustomTool
    return CustomTool(
      id: tool.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      name: tool.name ?? 'Unnamed Tool',
      description: tool.description ?? 'No description',
      category: ToolCategory.custom.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: false,
      isFromExcel: false,
      createdAt: tool.createdAt ?? DateTime.now(),
      updatedAt: tool.updatedAt ?? DateTime.now(),
    );
  }

  /// Load tool data including backend and UI components
  Future<void> _loadToolData() async {
    try {
      final customTool = _convertToCustomTool(widget.tool);

      // Load backend data
      _backendData = await _persistenceService.loadBackendData(customTool.id);

      // Load UI components
      _uiComponents = await _persistenceService.loadUIData(customTool.id);

      // If no UI components exist, create a simple default interface
      if (_uiComponents.isEmpty) {
        _createDefaultUIComponents();
      }

      // Initialize component controllers and values
      _initializeComponents();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading tool: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Create default UI components if none exist
  void _createDefaultUIComponents() {
    // Create simple input and output components based on backend data
    final inputCells = _backendData.entries
        .where((entry) => !entry.value.hasFormula)
        .take(5); // Limit to 5 inputs

    final outputCells = _backendData.entries
        .where((entry) => entry.value.hasFormula)
        .take(5); // Limit to 5 outputs

    int componentIndex = 0;

    // Create input components
    for (final entry in inputCells) {
      _uiComponents.add(ToolComponent(
        id: 'input_${componentIndex++}',
        type: ComponentType.input,
        label: 'Input ${entry.key}',
        properties: {
          'placeholder': 'Enter value for ${entry.key}',
          'cellBinding': entry.key,
        },
      ));
    }

    // Create output components
    for (final entry in outputCells) {
      _uiComponents.add(ToolComponent(
        id: 'output_${componentIndex++}',
        type: ComponentType.output,
        label: 'Result ${entry.key}',
        properties: {
          'cellBinding': entry.key,
        },
      ));
    }

    // Add a calculate button
    _uiComponents.add(ToolComponent(
      id: 'button_calculate',
      type: ComponentType.button,
      label: 'Calculate',
      properties: {
        'action': 'calculate',
      },
    ));
  }

  /// Initialize UI components with controllers and default values
  void _initializeComponents() {
    for (final component in _uiComponents) {
      if (component.type == ComponentType.input) {
        final controller = TextEditingController();
        _inputControllers[component.id] = controller;

        // Set initial value from backend if available
        final cellBinding = component.properties['cellBinding'] as String?;
        if (cellBinding != null && _backendData.containsKey(cellBinding)) {
          final cellValue = _backendData[cellBinding]?.value?.toString() ?? '';
          controller.text = cellValue;
          _componentValues[component.id] = cellValue;
        }

        // Listen for changes
        controller.addListener(() {
          _onComponentValueChanged(component.id, controller.text);
        });
      } else if (component.type == ComponentType.dropdown) {
        // Initialize dropdown with default value
        final defaultValue = component.properties['defaultValue'] as String?;
        _componentValues[component.id] = defaultValue ?? '';
      } else if (component.type == ComponentType.slider) {
        // Initialize slider with default value
        final defaultValue = component.properties['defaultValue'] as double?;
        _componentValues[component.id] = defaultValue ?? 0.0;
      }
    }
  }

  /// Handle component value changes
  void _onComponentValueChanged(String componentId, dynamic value) {
    setState(() {
      _componentValues[componentId] = value;
    });

    // Update backend data if component has cell binding
    final component = _uiComponents.firstWhere((c) => c.id == componentId);
    final cellBinding = component.properties['cellBinding'] as String?;

    if (cellBinding != null) {
      _backendData[cellBinding] = SpreadsheetCell(
        address: cellBinding,
        value: value,
        type: _getCellType(value),
      );

      // Recalculate formulas
      _recalculateBackend();
    }
  }

  /// Recalculate backend formulas and update output components
  void _recalculateBackend() {
    final recalculated = _formulaEngine.recalculateSheet(_backendData);

    setState(() {
      _backendData = recalculated;

      // Update output component values
      for (final component in _uiComponents) {
        if (component.type == ComponentType.output) {
          final cellBinding = component.properties['cellBinding'] as String?;
          if (cellBinding != null && _backendData.containsKey(cellBinding)) {
            _componentValues[component.id] = _backendData[cellBinding]?.value;
          }
        }
      }
    });
  }

  /// Determine cell type from value
  CellType _getCellType(dynamic value) {
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    if (value.toString().startsWith('=')) return CellType.formula;
    return CellType.text;
  }

  /// Build UI components based on the UI Builder design
  List<Widget> _buildUIComponents() {
    final widgets = <Widget>[];

    for (final component in _uiComponents) {
      widgets.add(_buildUIComponent(component));
      widgets.add(const SizedBox(height: 12));
    }

    return widgets;
  }

  /// Build individual UI component
  Widget _buildUIComponent(ToolComponent component) {
    switch (component.type) {
      case ComponentType.input:
        return _buildInputComponent(component);
      case ComponentType.output:
        return _buildOutputComponent(component);
      case ComponentType.button:
        return _buildButtonComponent(component);
      case ComponentType.dropdown:
        return _buildDropdownComponent(component);
      case ComponentType.slider:
        return _buildSliderComponent(component);
      default:
        return _buildLabelComponent(component);
    }
  }

  /// Build input text field component
  Widget _buildInputComponent(ToolComponent component) {
    final controller = _inputControllers[component.id];
    if (controller == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              component.properties['label'] as String? ?? 'Input',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: controller,
              decoration: InputDecoration(
                hintText: component.properties['placeholder'] as String? ?? 'Enter value',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  onPressed: () => controller.clear(),
                  icon: const Icon(Icons.clear),
                ),
              ),
              keyboardType: _getKeyboardType(component),
            ),
          ],
        ),
      ),
    );
  }

  /// Build output display component
  Widget _buildOutputComponent(ToolComponent component) {
    final value = _componentValues[component.id]?.toString() ?? '';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              component.properties['label'] as String? ?? 'Output',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              child: Text(
                value,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build button component
  Widget _buildButtonComponent(ToolComponent component) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _onButtonPressed(component),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              component.properties['label'] as String? ?? 'Button',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
        ),
      ),
    );
  }

  /// Build dropdown component
  Widget _buildDropdownComponent(ToolComponent component) {
    final options = component.properties['options'] as List<String>? ?? ['Option 1', 'Option 2'];
    final currentValue = _componentValues[component.id] as String? ?? options.first;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              component.properties['label'] as String? ?? 'Dropdown',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: currentValue,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
              items: options.map((option) {
                return DropdownMenuItem(
                  value: option,
                  child: Text(option),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  _onComponentValueChanged(component.id, value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build slider component
  Widget _buildSliderComponent(ToolComponent component) {
    final min = component.properties['min'] as double? ?? 0.0;
    final max = component.properties['max'] as double? ?? 100.0;
    final currentValue = _componentValues[component.id] as double? ?? min;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  component.properties['label'] as String? ?? 'Slider',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Text(
                  currentValue.toStringAsFixed(1),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Slider(
              value: currentValue,
              min: min,
              max: max,
              divisions: ((max - min) / (component.properties['step'] as double? ?? 1.0)).round(),
              onChanged: (value) {
                _onComponentValueChanged(component.id, value);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build label component
  Widget _buildLabelComponent(ToolComponent component) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          component.properties['text'] as String? ?? 'Label',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text(widget.tool.name),
          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.tool.name),
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        actions: [
          IconButton(
            onPressed: _resetTool,
            icon: const Icon(Icons.refresh),
            tooltip: 'Reset Tool',
          ),
          IconButton(
            onPressed: _showToolInfo,
            icon: const Icon(Icons.info_outline),
            tooltip: 'Tool Info',
          ),
        ],
      ),
      body: _buildUIInterface(),
    );
  }

  /// Build the custom UI interface based on UI Builder components
  Widget _buildUIInterface() {
    if (_uiComponents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.build_circle_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No UI Interface Designed',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'This tool needs a UI interface to be created in the UI Builder.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to UI Builder
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.design_services),
              label: const Text('Design UI Interface'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Tool description card
          _buildToolDescriptionCard(),
          const SizedBox(height: 16),

          // Custom UI components
          ..._buildUIComponents(),
        ],
      ),
    );
  }

  /// Build tool description card
  Widget _buildToolDescriptionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.build_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.tool.name,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              widget.tool.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildInfoChip(
                  Icons.widgets,
                  '${_uiComponents.length} components',
                  Colors.blue,
                ),
                const SizedBox(width: 8),
                _buildInfoChip(
                  Icons.data_object,
                  '${_backendData.length} cells',
                  Colors.green,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Helper methods for UI components
  TextInputType _getKeyboardType(ToolComponent component) {
    final inputType = component.properties['inputType'] as String? ?? 'text';
    switch (inputType) {
      case 'number':
        return TextInputType.number;
      case 'email':
        return TextInputType.emailAddress;
      case 'phone':
        return TextInputType.phone;
      default:
        return TextInputType.text;
    }
  }

  /// Handle button press
  void _onButtonPressed(ToolComponent component) {
    final action = component.properties['action'] as String? ?? 'calculate';

    switch (action) {
      case 'calculate':
        _recalculateBackend();
        break;
      case 'reset':
        _resetTool();
        break;
      case 'clear':
        _clearInputs();
        break;
      default:
        // Custom action - could trigger specific calculations
        _recalculateBackend();
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${component.properties['label'] ?? 'Button'} pressed'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// Reset tool to initial state
  void _resetTool() {
    setState(() {
      // Reset component values to defaults
      for (final component in _uiComponents) {
        if (component.type == ComponentType.input) {
          final controller = _inputControllers[component.id];
          controller?.clear();
          _componentValues[component.id] = '';
        } else if (component.type == ComponentType.dropdown) {
          final options = component.properties['options'] as List<String>? ?? [];
          _componentValues[component.id] = options.isNotEmpty ? options.first : '';
        } else if (component.type == ComponentType.slider) {
          final min = component.properties['min'] as double? ?? 0.0;
          _componentValues[component.id] = min;
        }
      }

      // Reset backend data
      _backendData.clear();
    });

    // Reload tool data
    _loadToolData();
  }

  /// Clear all input values
  void _clearInputs() {
    setState(() {
      for (final component in _uiComponents) {
        if (component.type == ComponentType.input) {
          final controller = _inputControllers[component.id];
          controller?.clear();
          _componentValues[component.id] = '';
        }
      }
    });

    _recalculateBackend();
  }

  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }





  void _showToolInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(widget.tool.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Description:', style: Theme.of(context).textTheme.titleSmall),
              Text(widget.tool.description),
              const SizedBox(height: 12),
              Text('Statistics:', style: Theme.of(context).textTheme.titleSmall),
              Text('UI Components: ${_uiComponents.length}'),
              Text('Backend Cells: ${_backendData.length}'),
              Text('Created: ${widget.tool.createdAt.toString().split('.')[0]}'),
              Text('Updated: ${widget.tool.updatedAt.toString().split('.')[0]}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
