import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Loading state provider
final loadingProvider = StateNotifierProvider<LoadingNotifier, LoadingState>((ref) {
  return LoadingNotifier();
});

class LoadingState {
  final bool isLoading;
  final String? message;
  final double? progress;
  final LoadingType type;

  const LoadingState({
    this.isLoading = false,
    this.message,
    this.progress,
    this.type = LoadingType.spinner,
  });

  LoadingState copyWith({
    bool? isLoading,
    String? message,
    double? progress,
    LoadingType? type,
  }) {
    return LoadingState(
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
      progress: progress ?? this.progress,
      type: type ?? this.type,
    );
  }
}

enum LoadingType {
  spinner,
  linear,
  circular,
  dots,
  pulse,
}

class LoadingNotifier extends StateNotifier<LoadingState> {
  LoadingNotifier() : super(const LoadingState());

  void startLoading({
    String? message,
    LoadingType type = LoadingType.spinner,
  }) {
    state = state.copyWith(
      isLoading: true,
      message: message,
      type: type,
      progress: null,
    );
  }

  void updateProgress(double progress, {String? message}) {
    state = state.copyWith(
      progress: progress.clamp(0.0, 1.0),
      message: message,
    );
  }

  void updateMessage(String message) {
    state = state.copyWith(message: message);
  }

  void stopLoading() {
    state = const LoadingState();
  }
}

class LoadingService {
  static OverlayEntry? _overlayEntry;
  static bool _isShowing = false;

  // Show loading overlay
  static void show(
    BuildContext context, {
    String? message,
    LoadingType type = LoadingType.spinner,
    bool barrierDismissible = false,
  }) {
    if (_isShowing) return;

    _isShowing = true;
    _overlayEntry = OverlayEntry(
      builder: (context) => LoadingOverlay(
        message: message,
        type: type,
        barrierDismissible: barrierDismissible,
        onDismiss: hide,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  // Hide loading overlay
  static void hide() {
    if (!_isShowing) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
    _isShowing = false;
  }

  // Show loading dialog
  static void showDialog(
    BuildContext context, {
    String? message,
    LoadingType type = LoadingType.spinner,
  }) {
    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Center(
          child: LoadingWidget(
            message: message,
            type: type,
            showBackground: true,
          ),
        );
      },
    );
  }

  // Execute with loading
  static Future<T?> executeWithLoading<T>(
    BuildContext context,
    Future<T> Function() operation, {
    String? message,
    LoadingType type = LoadingType.spinner,
    bool showOverlay = true,
  }) async {
    try {
      if (showOverlay) {
        show(context, message: message, type: type);
      }

      final result = await operation();
      return result;
    } catch (e) {
      rethrow;
    } finally {
      if (showOverlay) {
        hide();
      }
    }
  }
}

class LoadingOverlay extends StatelessWidget {
  final String? message;
  final LoadingType type;
  final bool barrierDismissible;
  final VoidCallback? onDismiss;

  const LoadingOverlay({
    super.key,
    this.message,
    this.type = LoadingType.spinner,
    this.barrierDismissible = false,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: GestureDetector(
        onTap: barrierDismissible ? onDismiss : null,
        child: Container(
          width: double.infinity,
          height: double.infinity,
          child: Center(
            child: LoadingWidget(
              message: message,
              type: type,
              showBackground: true,
            ),
          ),
        ),
      ),
    );
  }
}

class LoadingWidget extends StatefulWidget {
  final String? message;
  final LoadingType type;
  final double? progress;
  final bool showBackground;
  final Color? color;
  final double size;

  const LoadingWidget({
    super.key,
    this.message,
    this.type = LoadingType.spinner,
    this.progress,
    this.showBackground = false,
    this.color,
    this.size = 50.0,
  });

  @override
  State<LoadingWidget> createState() => _LoadingWidgetState();
}

class _LoadingWidgetState extends State<LoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    if (widget.type == LoadingType.pulse || widget.type == LoadingType.dots) {
      _controller.repeat(reverse: true);
    } else {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = widget.color ?? theme.primaryColor;

    Widget loadingIndicator;

    switch (widget.type) {
      case LoadingType.spinner:
        loadingIndicator = SizedBox(
          width: widget.size,
          height: widget.size,
          child: CircularProgressIndicator(
            color: color,
            strokeWidth: 3,
          ),
        );
        break;

      case LoadingType.linear:
        loadingIndicator = SizedBox(
          width: widget.size * 2,
          child: LinearProgressIndicator(
            color: color,
            value: widget.progress,
          ),
        );
        break;

      case LoadingType.circular:
        loadingIndicator = SizedBox(
          width: widget.size,
          height: widget.size,
          child: CircularProgressIndicator(
            color: color,
            value: widget.progress,
            strokeWidth: 4,
          ),
        );
        break;

      case LoadingType.dots:
        loadingIndicator = AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(3, (index) {
                final delay = index * 0.2;
                final animationValue = (_animation.value + delay) % 1.0;
                final opacity = (animationValue < 0.5) 
                    ? animationValue * 2 
                    : (1.0 - animationValue) * 2;

                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: color.withOpacity(opacity.clamp(0.3, 1.0)),
                    shape: BoxShape.circle,
                  ),
                );
              }),
            );
          },
        );
        break;

      case LoadingType.pulse:
        loadingIndicator = AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.scale(
              scale: 0.8 + (_animation.value * 0.4),
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.8 - (_animation.value * 0.3)),
                  shape: BoxShape.circle,
                ),
              ),
            );
          },
        );
        break;
    }

    Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        loadingIndicator,
        if (widget.message != null) ...[
          const SizedBox(height: 16),
          Text(
            widget.message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: widget.showBackground ? Colors.white : null,
            ),
            textAlign: TextAlign.center,
          ),
        ],
        if (widget.progress != null && widget.type != LoadingType.linear) ...[
          const SizedBox(height: 8),
          Text(
            '${(widget.progress! * 100).toInt()}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: widget.showBackground ? Colors.white70 : null,
            ),
          ),
        ],
      ],
    );

    if (widget.showBackground) {
      content = Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(12),
        ),
        child: content,
      );
    }

    return content;
  }
}

// Consumer widget for loading state
class LoadingConsumer extends ConsumerWidget {
  final Widget Function(BuildContext context, LoadingState loadingState) builder;

  const LoadingConsumer({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loadingState = ref.watch(loadingProvider);
    return builder(context, loadingState);
  }
}

// Loading button widget
class LoadingButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final bool isLoading;
  final LoadingType loadingType;

  const LoadingButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.isLoading = false,
    this.loadingType = LoadingType.spinner,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      child: isLoading
          ? SizedBox(
              height: 20,
              width: 20,
              child: LoadingWidget(
                type: loadingType,
                size: 20,
                color: Colors.white,
              ),
            )
          : child,
    );
  }
}
