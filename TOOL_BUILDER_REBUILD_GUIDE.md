# 🛠️ ShadowSuite Tool Builder Complete Rebuild Guide

## 🎯 Overview
This guide provides step-by-step instructions to completely rebuild the Tool Builder mini-app within ShadowSuite, transforming it into a powerful Excel-like backend with visual UI builder.

## 🚫 Phase 1: Remove Existing Tool Builder

### 1.1 Delete Legacy Files
```bash
# Remove existing tool builder files (KEEP calculator and converter)
rm lib/features/tools/screens/custom_tool_builder_screen.dart
rm lib/features/tools/screens/tool_creation_screen.dart
rm lib/features/tools/models/custom_tool.dart
rm lib/features/tools/providers/custom_tool_provider.dart
rm lib/features/tools/widgets/tool_builder_widgets.dart
```

### 1.2 Clean Tool Builder Routes
Edit `lib/core/router/app_router.dart`:
```dart
// REMOVE these routes (keep calculator and converter routes):
// - '/tools/builder'
// - '/tools/create'
// - '/tools/edit/:id'
```

### 1.3 Update Tools Main Screen
Edit `lib/features/tools/screens/tools_screen.dart`:
```dart
// Remove custom tool builder card, keep only:
// - Calculator card
// - Converter card
// Add new "Tool Builder" card that navigates to '/tools/builder-home'
```

## 🆕 Phase 2: Implement New Architecture

### 2.1 Create New Data Models

#### File: `lib/features/tools/models/tool_definition.dart`
```dart
@collection
class ToolDefinition {
  Id id = Isar.autoIncrement;
  late String name;
  late String description;
  late DateTime createdAt;
  late DateTime updatedAt;
  late String backendData; // JSON string of spreadsheet data
  late String uiLayout; // JSON string of UI components
  late String userId;
  bool isPasswordProtected = false;
  String? passwordHash;
  bool isDeleted = false;
}
```

#### File: `lib/features/tools/models/spreadsheet_cell.dart`
```dart
class SpreadsheetCell {
  final String address; // A1, B2, etc.
  final dynamic value;
  final String? formula;
  final CellType type;
  
  const SpreadsheetCell({
    required this.address,
    this.value,
    this.formula,
    required this.type,
  });
}

enum CellType { text, number, boolean, formula, error }
```

#### File: `lib/features/tools/models/ui_component.dart`
```dart
class UIComponent {
  final String id;
  final ComponentType type;
  final String label;
  final String? bindToCell;
  final Map<String, dynamic> properties;
  final ComponentStyle style;
  
  const UIComponent({
    required this.id,
    required this.type,
    required this.label,
    this.bindToCell,
    required this.properties,
    required this.style,
  });
}

enum ComponentType {
  textField, outputField, button, toggle, dropdown, label
}
```

### 2.2 Create Core Services

#### File: `lib/features/tools/services/formula_engine_service.dart`
```dart
class FormulaEngineService {
  // Excel-compatible formula evaluation
  dynamic evaluateFormula(String formula, Map<String, dynamic> cellData);
  List<String> getDependencies(String formula);
  Map<String, dynamic> recalculateSheet(Map<String, dynamic> sheetData);
  bool validateFormula(String formula);
  String getFormulaError(String formula);
}
```

#### File: `lib/features/tools/services/excel_parser_service.dart`
```dart
class ExcelParserService {
  Future<Map<String, dynamic>> parseExcelFile(String filePath);
  List<UIComponent> suggestUIFromHeaders(List<String> headers);
  Map<String, SpreadsheetCell> convertToSpreadsheetData(dynamic excelData);
}
```

#### File: `lib/features/tools/services/tool_storage_service.dart`
```dart
class ToolStorageService {
  Future<void> saveTool(ToolDefinition tool);
  Future<List<ToolDefinition>> getUserTools(String userId);
  Future<ToolDefinition?> getTool(int toolId);
  Future<void> deleteTool(int toolId);
  Future<String> exportTool(int toolId);
  Future<ToolDefinition> importTool(String jsonData);
}
```

### 2.3 Create New Screens

#### File: `lib/features/tools/screens/tool_builder_home_screen.dart`
```dart
class ToolBuilderHomeScreen extends ConsumerWidget {
  // Navigation dashboard with:
  // - Create New Tool button
  // - Import Excel button  
  // - My Tools list
  // - Search and filter options
}
```

#### File: `lib/features/tools/screens/create_tool_screen.dart`
```dart
class CreateToolScreen extends ConsumerWidget {
  // Tool name input
  // Two navigation buttons:
  // - Backend Formula Engine
  // - UI Builder
}
```

#### File: `lib/features/tools/screens/backend_formula_screen.dart`
```dart
class BackendFormulaScreen extends ConsumerWidget {
  // Excel-like spreadsheet interface:
  // - Grid with A-Z columns, 1-100+ rows
  // - Formula bar
  // - Cell editing
  // - Formula autocomplete
  // - Multiple sheets support
}
```

#### File: `lib/features/tools/screens/ui_builder_screen.dart`
```dart
class UIBuilderScreen extends ConsumerWidget {
  // Visual UI builder:
  // - Component palette
  // - Drag-and-drop canvas
  // - Property editor
  // - Cell binding interface
  // - Preview mode
}
```

#### File: `lib/features/tools/screens/tool_preview_screen.dart`
```dart
class ToolPreviewScreen extends ConsumerWidget {
  // Read-only tool execution:
  // - Rendered UI from builder
  // - Live formula calculations
  // - Interactive components
}
```

#### File: `lib/features/tools/screens/my_tools_screen.dart`
```dart
class MyToolsScreen extends ConsumerWidget {
  // Tool management:
  // - List/grid view of tools
  // - Search and filter
  // - Edit/Delete/Duplicate actions
  // - Export options
}
```

#### File: `lib/features/tools/screens/import_excel_screen.dart`
```dart
class ImportExcelScreen extends ConsumerWidget {
  // Excel import wizard:
  // - File picker
  // - Data preview
  // - UI mapping suggestions
  // - Import confirmation
}
```

## 🔧 Phase 3: Implementation Steps

### 3.1 Dependencies
Add to `pubspec.yaml`:
```yaml
dependencies:
  syncfusion_flutter_xlsio: ^26.2.14
  file_picker: ^8.1.2
  excel: ^4.0.6
  expressions: ^0.2.6
```

### 3.2 Database Schema
Update `lib/core/database/database_service.dart`:
```dart
// Add ToolDefinition to Isar schemas
final isar = await Isar.open([
  // ... existing schemas
  ToolDefinitionSchema,
]);
```

### 3.3 Providers
Create `lib/features/tools/providers/tool_builder_provider.dart`:
```dart
// Riverpod providers for:
// - Current tool state
// - Spreadsheet data
// - UI components
// - Formula evaluation
// - Tool list management
```

### 3.4 Routing
Update `lib/core/router/app_router.dart`:
```dart
GoRoute(
  path: '/tools/builder-home',
  builder: (context, state) => const ToolBuilderHomeScreen(),
),
GoRoute(
  path: '/tools/create',
  builder: (context, state) => const CreateToolScreen(),
),
GoRoute(
  path: '/tools/backend/:id',
  builder: (context, state) => BackendFormulaScreen(
    toolId: int.parse(state.pathParameters['id']!),
  ),
),
GoRoute(
  path: '/tools/ui-builder/:id',
  builder: (context, state) => UIBuilderScreen(
    toolId: int.parse(state.pathParameters['id']!),
  ),
),
GoRoute(
  path: '/tools/preview/:id',
  builder: (context, state) => ToolPreviewScreen(
    toolId: int.parse(state.pathParameters['id']!),
  ),
),
GoRoute(
  path: '/tools/my-tools',
  builder: (context, state) => const MyToolsScreen(),
),
GoRoute(
  path: '/tools/import-excel',
  builder: (context, state) => const ImportExcelScreen(),
),
```

## 🚀 Phase 4: Implementation Priority

### Priority 1 (Core Foundation)
1. Create data models and database schema
2. Implement basic tool storage service
3. Create tool builder home screen
4. Create tool creation screen

### Priority 2 (Backend Engine)
1. Implement formula engine service
2. Create backend formula screen with basic grid
3. Add formula evaluation and cell references
4. Implement multiple sheets support

### Priority 3 (UI Builder)
1. Create UI component models
2. Implement UI builder screen with drag-and-drop
3. Add component property editor
4. Implement cell binding system

### Priority 4 (Advanced Features)
1. Excel import functionality
2. Tool preview and execution
3. My Tools management screen
4. Export and sharing features

### Priority 5 (Polish)
1. Error handling and validation
2. Undo/redo functionality
3. Performance optimization
4. UI/UX enhancements

## ✅ Verification Checklist

- [ ] Legacy tool builder completely removed
- [ ] New architecture implemented
- [ ] Excel-like spreadsheet functional
- [ ] Visual UI builder working
- [ ] Tool storage and management complete
- [ ] Excel import working
- [ ] All screens responsive and accessible
- [ ] Material Design 3 compliance
- [ ] Full CRUD operations
- [ ] Error handling implemented

## 🔄 Testing Strategy

1. **Unit Tests**: Formula engine, data models
2. **Integration Tests**: Tool creation workflow
3. **UI Tests**: Screen navigation and interactions
4. **Performance Tests**: Large spreadsheet handling
5. **User Tests**: Complete tool creation process

This rebuild will transform the Tool Builder into a powerful, Excel-alternative platform while maintaining ShadowSuite's architecture and design principles.

## 📋 Implementation Script

To execute this rebuild, run the following script:

```bash
# Navigate to project directory
cd shadowsuite

# Run the automated rebuild script
dart run tool_builder_rebuild_script.dart
```

The script will:
1. Backup existing tool builder files
2. Remove legacy components safely
3. Create new architecture step by step
4. Update routing and navigation
5. Implement core services and providers
6. Create all new screens and widgets
7. Update database schema
8. Run tests to verify functionality

## 🎯 Expected Outcome

After running this rebuild:
- Tool Builder will have Excel-like spreadsheet functionality
- Visual drag-and-drop UI builder will be fully functional
- Users can import Excel files and convert them to interactive tools
- All tools will be saved persistently with full CRUD operations
- The interface will be responsive and follow Material Design 3
- Calculator and Converter mini-apps will remain unchanged
