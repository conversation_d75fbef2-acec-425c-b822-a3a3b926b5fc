import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/custom_tool.dart';
import '../../../core/database/database_service.dart';

/// Tool creation state for the 4-page workflow
class ToolCreationState {
  final String toolName;
  final String toolDescription;
  final String toolCategory;
  final String creationMethod;
  final Map<String, dynamic> backendData;
  final List<Map<String, dynamic>> uiComponents;
  final bool isLoading;
  final String? error;

  const ToolCreationState({
    this.toolName = '',
    this.toolDescription = '',
    this.toolCategory = '',
    this.creationMethod = '',
    this.backendData = const {},
    this.uiComponents = const [],
    this.isLoading = false,
    this.error,
  });

  ToolCreationState copyWith({
    String? toolName,
    String? toolDescription,
    String? toolCategory,
    String? creationMethod,
    Map<String, dynamic>? backendData,
    List<Map<String, dynamic>>? uiComponents,
    bool? isLoading,
    String? error,
  }) {
    return ToolCreationState(
      toolName: toolName ?? this.toolName,
      toolDescription: toolDescription ?? this.toolDescription,
      toolCategory: toolCategory ?? this.toolCategory,
      creationMethod: creationMethod ?? this.creationMethod,
      backendData: backendData ?? this.backendData,
      uiComponents: uiComponents ?? this.uiComponents,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Tool creation notifier for managing the 4-page workflow
class ToolCreationNotifier extends StateNotifier<ToolCreationState> {
  ToolCreationNotifier() : super(const ToolCreationState());

  /// Update tool name
  void updateToolName(String name) {
    state = state.copyWith(toolName: name);
  }

  /// Update tool description
  void updateToolDescription(String description) {
    state = state.copyWith(toolDescription: description);
  }

  /// Update tool category
  void updateToolCategory(String category) {
    state = state.copyWith(toolCategory: category);
  }

  /// Set creation method
  void setCreationMethod(String method) {
    state = state.copyWith(creationMethod: method);
  }

  /// Import Excel file
  Future<void> importExcelFile(String filePath) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Process Excel file and extract data
      final backendData = await _processExcelFile(filePath);
      
      state = state.copyWith(
        isLoading: false,
        creationMethod: 'excel_import',
        backendData: backendData,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to import Excel file: $e',
      );
    }
  }

  /// Load template
  Future<void> loadTemplate(String templateId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final templateData = await _loadTemplateData(templateId);
      
      state = state.copyWith(
        isLoading: false,
        creationMethod: 'template',
        toolName: templateData['name'] ?? '',
        toolDescription: templateData['description'] ?? '',
        toolCategory: templateData['category'] ?? '',
        backendData: templateData['backend'] ?? {},
        uiComponents: List<Map<String, dynamic>>.from(templateData['ui'] ?? []),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load template: $e',
      );
    }
  }

  /// Update backend data
  void updateBackendData(Map<String, dynamic> data) {
    state = state.copyWith(backendData: data);
  }

  /// Update UI components
  void updateUIComponents(List<Map<String, dynamic>> components) {
    state = state.copyWith(uiComponents: components);
  }

  /// Add UI component
  void addUIComponent(Map<String, dynamic> component) {
    final updatedComponents = [...state.uiComponents, component];
    state = state.copyWith(uiComponents: updatedComponents);
  }

  /// Remove UI component
  void removeUIComponent(int index) {
    final updatedComponents = [...state.uiComponents];
    if (index >= 0 && index < updatedComponents.length) {
      updatedComponents.removeAt(index);
      state = state.copyWith(uiComponents: updatedComponents);
    }
  }

  /// Save tool
  Future<void> saveTool() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final tool = CustomTool(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: state.toolName,
        description: state.toolDescription,
        category: state.toolCategory,
        backendData: state.backendData,
        uiLayout: {'components': state.uiComponents},
        isActive: true,
        isFromTemplate: state.creationMethod == 'template',
        isFromExcel: state.creationMethod == 'excel_import',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save to database (mock implementation)
      await _saveTool(tool);

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to save tool: $e',
      );
    }
  }

  /// Reset creation state
  void reset() {
    state = const ToolCreationState();
  }

  /// Process Excel file (mock implementation)
  Future<Map<String, dynamic>> _processExcelFile(String filePath) async {
    // Simulate processing delay
    await Future.delayed(const Duration(seconds: 2));

    // Mock Excel data extraction
    return {
      'cells': {
        'A1': {'value': 'Income', 'type': 'text'},
        'B1': {'value': 5000, 'type': 'number'},
        'A2': {'value': 'Expenses', 'type': 'text'},
        'B2': {'value': 3000, 'type': 'number'},
        'A3': {'value': 'Net', 'type': 'text'},
        'B3': {'value': '=B1-B2', 'type': 'formula'},
      },
      'formulas': ['=B1-B2'],
      'metadata': {
        'fileName': filePath.split('/').last,
        'importedAt': DateTime.now().toIso8601String(),
      },
    };
  }

  /// Load template data (mock implementation)
  Future<Map<String, dynamic>> _loadTemplateData(String templateId) async {
    // Simulate loading delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock template data
    final templates = {
      'basic_calculator': {
        'name': 'Basic Calculator',
        'description': 'Simple arithmetic calculator',
        'category': 'Calculator',
        'backend': {
          'cells': {
            'A1': {'value': 0, 'type': 'number'},
            'A2': {'value': 0, 'type': 'number'},
            'A3': {'value': '=A1+A2', 'type': 'formula'},
          },
        },
        'ui': [
          {
            'type': 'input',
            'id': 'input1',
            'label': 'First Number',
            'cellRef': 'A1',
          },
          {
            'type': 'input',
            'id': 'input2',
            'label': 'Second Number',
            'cellRef': 'A2',
          },
          {
            'type': 'output',
            'id': 'result',
            'label': 'Result',
            'cellRef': 'A3',
          },
        ],
      },
      'budget_tracker': {
        'name': 'Budget Tracker',
        'description': 'Track income and expenses',
        'category': 'Financial',
        'backend': {
          'cells': {
            'A1': {'value': 'Income', 'type': 'text'},
            'B1': {'value': 0, 'type': 'number'},
            'A2': {'value': 'Expenses', 'type': 'text'},
            'B2': {'value': 0, 'type': 'number'},
            'A3': {'value': 'Balance', 'type': 'text'},
            'B3': {'value': '=B1-B2', 'type': 'formula'},
          },
        },
        'ui': [
          {
            'type': 'input',
            'id': 'income',
            'label': 'Monthly Income',
            'cellRef': 'B1',
          },
          {
            'type': 'input',
            'id': 'expenses',
            'label': 'Monthly Expenses',
            'cellRef': 'B2',
          },
          {
            'type': 'output',
            'id': 'balance',
            'label': 'Remaining Balance',
            'cellRef': 'B3',
          },
        ],
      },
    };

    return templates[templateId] ?? {};
  }

  /// Save tool to database
  Future<void> _saveTool(CustomTool tool) async {
    await DatabaseService.instance.saveCustomTool(tool);
  }
}

/// Tool creation provider
final toolCreationProvider = StateNotifierProvider<ToolCreationNotifier, ToolCreationState>(
  (ref) => ToolCreationNotifier(),
);
