import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/database/database_service.dart';
import 'core/permissions/permission_service.dart';
import 'core/navigation/app_router.dart';
import 'core/theme/theme_provider.dart' as theme;
import 'core/localization/localization_provider.dart';
import 'core/services/error_handler_service.dart';
import 'core/ui/unified_theme_system.dart';
import 'l10n/generated/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize error handling
  ErrorHandlerService.initialize();

  // Initialize services
  await DatabaseService.instance.initialize();
  await PermissionService().initialize();

  runApp(const ProviderScope(child: ShadowSuiteApp()));
}

class ShadowSuiteApp extends ConsumerWidget {
  const ShadowSuiteApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lightTheme = ref.watch(theme.themeProvider);
    final darkTheme = ref.watch(theme.darkThemeProvider);
    final themeMode = ref.watch(theme.themeModeProvider);
    final locale = ref.watch(localeProvider);

    return MaterialApp.router(
      title: 'ShadowSuite',
      debugShowCheckedModeBanner: false,

      // Navigation key for global access
      key: NavigationService.navigatorKey,

      // Localization configuration
      locale: locale,
      supportedLocales: supportedLocales,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      localeResolutionCallback: (locale, supportedLocales) {
        return LocaleResolution.resolve(locale, supportedLocales);
      },

      // Theme configuration using new theming system
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: themeMode,

      // Router configuration
      routerConfig: appRouter,
    );
  }


}


