import 'package:flutter/material.dart';
import '../models/app_layout_config.dart';

/// Widget for editing and creating custom layouts
class LayoutEditorWidget extends StatefulWidget {
  final AppLayoutConfig? layout;
  final Function(AppLayoutConfig) onSave;

  const LayoutEditorWidget({
    super.key,
    this.layout,
    required this.onSave,
  });

  @override
  State<LayoutEditorWidget> createState() => _LayoutEditorWidgetState();
}

class _LayoutEditorWidgetState extends State<LayoutEditorWidget> {
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  
  late LayoutType _layoutType;
  late NavigationType _navigationType;
  late SidebarPosition _sidebarPosition;
  late double _sidebarWidth;
  late bool _showSidebarLabels;
  late bool _compactMode;
  late bool _showQuickActions;
  late bool _showBreadcrumbs;
  late bool _showStatusBar;
  late HeaderStyle _headerStyle;
  late FooterStyle _footerStyle;
  late double _contentPadding;
  late double _cardSpacing;
  late double _elevation;
  late bool _enableAnimations;
  late Duration _animationDuration;
  late bool _enableHapticFeedback;
  late bool _enableSounds;
  late double _iconSize;
  late double _buttonHeight;
  late double _inputHeight;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.layout?.name ?? '');
    _descriptionController = TextEditingController(text: widget.layout?.description ?? '');
    
    if (widget.layout != null) {
      _layoutType = widget.layout!.layoutType;
      _navigationType = widget.layout!.navigationType;
      _sidebarPosition = widget.layout!.sidebarPosition;
      _sidebarWidth = widget.layout!.sidebarWidth;
      _showSidebarLabels = widget.layout!.showSidebarLabels;
      _compactMode = widget.layout!.compactMode;
      _showQuickActions = widget.layout!.showQuickActions;
      _showBreadcrumbs = widget.layout!.showBreadcrumbs;
      _showStatusBar = widget.layout!.showStatusBar;
      _headerStyle = widget.layout!.headerStyle;
      _footerStyle = widget.layout!.footerStyle;
      _contentPadding = widget.layout!.contentPadding;
      _cardSpacing = widget.layout!.cardSpacing;
      _elevation = widget.layout!.elevation;
      _enableAnimations = widget.layout!.enableAnimations;
      _animationDuration = widget.layout!.animationDuration;
      _enableHapticFeedback = widget.layout!.enableHapticFeedback;
      _enableSounds = widget.layout!.enableSounds;
      _iconSize = widget.layout!.iconSize;
      _buttonHeight = widget.layout!.buttonHeight;
      _inputHeight = widget.layout!.inputHeight;
    } else {
      // Default values
      _layoutType = LayoutType.adaptive;
      _navigationType = NavigationType.rail;
      _sidebarPosition = SidebarPosition.left;
      _sidebarWidth = 280.0;
      _showSidebarLabels = true;
      _compactMode = false;
      _showQuickActions = true;
      _showBreadcrumbs = true;
      _showStatusBar = true;
      _headerStyle = HeaderStyle.elevated;
      _footerStyle = FooterStyle.minimal;
      _contentPadding = 16.0;
      _cardSpacing = 12.0;
      _elevation = 2.0;
      _enableAnimations = true;
      _animationDuration = const Duration(milliseconds: 300);
      _enableHapticFeedback = true;
      _enableSounds = false;
      _iconSize = 24.0;
      _buttonHeight = 48.0;
      _inputHeight = 56.0;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.layout == null ? 'Create Layout' : 'Edit Layout'),
        actions: [
          TextButton(
            onPressed: _saveLayout,
            child: const Text('Save'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic info
            _buildSection(
              'Basic Information',
              [
                TextField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Layout Name',
                    hintText: 'Enter layout name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Enter layout description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),

            // Layout settings
            _buildSection(
              'Layout Settings',
              [
                _buildDropdown<LayoutType>(
                  'Layout Type',
                  _layoutType,
                  LayoutType.values,
                  (value) => setState(() => _layoutType = value!),
                  (type) => type.name,
                ),
                const SizedBox(height: 16),
                _buildDropdown<NavigationType>(
                  'Navigation Type',
                  _navigationType,
                  NavigationType.values,
                  (value) => setState(() => _navigationType = value!),
                  (type) => type.name,
                ),
                const SizedBox(height: 16),
                _buildDropdown<SidebarPosition>(
                  'Sidebar Position',
                  _sidebarPosition,
                  SidebarPosition.values,
                  (value) => setState(() => _sidebarPosition = value!),
                  (position) => position.name,
                ),
              ],
            ),

            // Sidebar settings
            _buildSection(
              'Sidebar Settings',
              [
                _buildSlider(
                  'Sidebar Width',
                  _sidebarWidth,
                  200.0,
                  400.0,
                  (value) => setState(() => _sidebarWidth = value),
                ),
                const SizedBox(height: 16),
                _buildSwitch(
                  'Show Sidebar Labels',
                  _showSidebarLabels,
                  (value) => setState(() => _showSidebarLabels = value),
                ),
              ],
            ),

            // Display settings
            _buildSection(
              'Display Settings',
              [
                _buildSwitch(
                  'Compact Mode',
                  _compactMode,
                  (value) => setState(() => _compactMode = value),
                ),
                const SizedBox(height: 16),
                _buildSwitch(
                  'Show Quick Actions',
                  _showQuickActions,
                  (value) => setState(() => _showQuickActions = value),
                ),
                const SizedBox(height: 16),
                _buildSwitch(
                  'Show Breadcrumbs',
                  _showBreadcrumbs,
                  (value) => setState(() => _showBreadcrumbs = value),
                ),
                const SizedBox(height: 16),
                _buildSwitch(
                  'Show Status Bar',
                  _showStatusBar,
                  (value) => setState(() => _showStatusBar = value),
                ),
              ],
            ),

            // Spacing settings
            _buildSection(
              'Spacing & Sizing',
              [
                _buildSlider(
                  'Content Padding',
                  _contentPadding,
                  8.0,
                  32.0,
                  (value) => setState(() => _contentPadding = value),
                ),
                const SizedBox(height: 16),
                _buildSlider(
                  'Card Spacing',
                  _cardSpacing,
                  4.0,
                  24.0,
                  (value) => setState(() => _cardSpacing = value),
                ),
                const SizedBox(height: 16),
                _buildSlider(
                  'Elevation',
                  _elevation,
                  0.0,
                  8.0,
                  (value) => setState(() => _elevation = value),
                ),
                const SizedBox(height: 16),
                _buildSlider(
                  'Icon Size',
                  _iconSize,
                  16.0,
                  32.0,
                  (value) => setState(() => _iconSize = value),
                ),
                const SizedBox(height: 16),
                _buildSlider(
                  'Button Height',
                  _buttonHeight,
                  32.0,
                  64.0,
                  (value) => setState(() => _buttonHeight = value),
                ),
              ],
            ),

            // Animation settings
            _buildSection(
              'Animation & Feedback',
              [
                _buildSwitch(
                  'Enable Animations',
                  _enableAnimations,
                  (value) => setState(() => _enableAnimations = value),
                ),
                const SizedBox(height: 16),
                _buildSwitch(
                  'Enable Haptic Feedback',
                  _enableHapticFeedback,
                  (value) => setState(() => _enableHapticFeedback = value),
                ),
                const SizedBox(height: 16),
                _buildSwitch(
                  'Enable Sounds',
                  _enableSounds,
                  (value) => setState(() => _enableSounds = value),
                ),
              ],
            ),

            // Preview
            _buildSection(
              'Preview',
              [
                _buildPreview(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildDropdown<T>(
    String label,
    T value,
    List<T> items,
    Function(T?) onChanged,
    String Function(T) getLabel,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: items.map((item) => DropdownMenuItem<T>(
            value: item,
            child: Text(getLabel(item)),
          )).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              value.toStringAsFixed(1),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: ((max - min) * 2).round(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSwitch(
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return SwitchListTile(
      title: Text(label),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildPreview() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
        ),
      ),
      child: Row(
        children: [
          // Sidebar preview
          Container(
            width: _sidebarWidth / 4,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
            ),
            child: Column(
              children: [
                Container(
                  height: 40,
                  color: Theme.of(context).colorScheme.primary,
                  child: Center(
                    child: Text(
                      _showSidebarLabels ? 'Menu' : '',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(_contentPadding / 4),
                    child: Column(
                      children: List.generate(4, (index) => Container(
                        height: 20,
                        margin: EdgeInsets.only(bottom: _cardSpacing / 2),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      )),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Content area preview
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(_contentPadding / 2),
              child: Column(
                children: [
                  // Header
                  if (_showStatusBar)
                    Container(
                      height: 20,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  
                  if (_showStatusBar) SizedBox(height: _cardSpacing / 2),
                  
                  // Content cards
                  Expanded(
                    child: GridView.count(
                      crossAxisCount: 2,
                      crossAxisSpacing: _cardSpacing / 2,
                      mainAxisSpacing: _cardSpacing / 2,
                      children: List.generate(4, (index) => Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: _elevation,
                              offset: Offset(0, _elevation / 2),
                            ),
                          ],
                        ),
                      )),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveLayout() {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a layout name')),
      );
      return;
    }

    final layout = AppLayoutConfig(
      id: widget.layout?.id ?? 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      layoutType: _layoutType,
      navigationType: _navigationType,
      sidebarPosition: _sidebarPosition,
      sidebarWidth: _sidebarWidth,
      showSidebarLabels: _showSidebarLabels,
      compactMode: _compactMode,
      showQuickActions: _showQuickActions,
      showBreadcrumbs: _showBreadcrumbs,
      showStatusBar: _showStatusBar,
      headerStyle: _headerStyle,
      footerStyle: _footerStyle,
      contentPadding: _contentPadding,
      cardSpacing: _cardSpacing,
      elevation: _elevation,
      enableAnimations: _enableAnimations,
      animationDuration: _animationDuration,
      enableHapticFeedback: _enableHapticFeedback,
      enableSounds: _enableSounds,
      iconSize: _iconSize,
      buttonHeight: _buttonHeight,
      inputHeight: _inputHeight,
      isDefault: false,
      createdAt: widget.layout?.createdAt ?? DateTime.now(),
      modifiedAt: DateTime.now(),
    );

    widget.onSave(layout);
    Navigator.of(context).pop();
  }
}
