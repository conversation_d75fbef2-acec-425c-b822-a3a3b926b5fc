import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/base_screen.dart';
import '../models/athkar_display_preferences.dart';
import '../providers/athkar_display_preferences_provider.dart';

class AthkarTranslationSettingsScreen extends ConsumerWidget {
  const AthkarTranslationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final preferences = ref.watch(athkarDisplayPreferencesProvider);
    
    if (preferences == null) {
      return const BaseScreen(
        title: 'Translation Settings',
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return BaseScreen(
      title: 'Translation Settings',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Display Mode Section
            _buildSectionHeader(context, 'Display Mode'),
            _buildDisplayModeCard(context, ref, preferences),
            const SizedBox(height: 24),

            // Language Section
            _buildSectionHeader(context, 'Language'),
            _buildLanguageCard(context, ref, preferences),
            const SizedBox(height: 24),

            // Font Settings Section
            _buildSectionHeader(context, 'Font Settings'),
            _buildFontSettingsCard(context, ref, preferences),
            const SizedBox(height: 24),

            // Display Options Section
            _buildSectionHeader(context, 'Display Options'),
            _buildDisplayOptionsCard(context, ref, preferences),
            const SizedBox(height: 24),

            // Reset Button
            _buildResetButton(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildDisplayModeCard(BuildContext context, WidgetRef ref, AthkarDisplayPreferences preferences) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Choose how dhikr text is displayed',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ...AthkarDisplayMode.values.map((mode) {
              return RadioListTile<AthkarDisplayMode>(
                title: Text(mode.name.replaceAll(RegExp(r'([A-Z])'), ' \$1').trim()),
                subtitle: Text(_getDisplayModeDescription(mode)),
                value: mode,
                groupValue: preferences.displayMode,
                onChanged: (value) {
                  if (value != null) {
                    ref.read(athkarDisplayPreferencesProvider.notifier).updateDisplayMode(value);
                  }
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageCard(BuildContext context, WidgetRef ref, AthkarDisplayPreferences preferences) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preferred translation language',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: preferences.preferredLanguage,
              decoration: const InputDecoration(
                labelText: 'Language',
                border: OutlineInputBorder(),
              ),
              items: AthkarDisplayPreferences.availableLanguages.entries.map((entry) {
                return DropdownMenuItem(
                  value: entry.key,
                  child: Text(entry.value),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  ref.read(athkarDisplayPreferencesProvider.notifier).updateLanguage(value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSettingsCard(BuildContext context, WidgetRef ref, AthkarDisplayPreferences preferences) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Font sizes and family',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            
            // Arabic Font Size
            Text('Arabic Font Size: ${preferences.arabicFontSize.toInt()}'),
            Slider(
              value: preferences.arabicFontSize,
              min: 16.0,
              max: 36.0,
              divisions: 20,
              onChanged: (value) {
                ref.read(athkarDisplayPreferencesProvider.notifier).updateFontSizes(
                  arabicFontSize: value,
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Transliteration Font Size
            Text('Transliteration Font Size: ${preferences.transliterationFontSize.toInt()}'),
            Slider(
              value: preferences.transliterationFontSize,
              min: 12.0,
              max: 24.0,
              divisions: 12,
              onChanged: (value) {
                ref.read(athkarDisplayPreferencesProvider.notifier).updateFontSizes(
                  transliterationFontSize: value,
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Translation Font Size
            Text('Translation Font Size: ${preferences.translationFontSize.toInt()}'),
            Slider(
              value: preferences.translationFontSize,
              min: 12.0,
              max: 20.0,
              divisions: 8,
              onChanged: (value) {
                ref.read(athkarDisplayPreferencesProvider.notifier).updateFontSizes(
                  translationFontSize: value,
                );
              },
            ),
            const SizedBox(height: 16),
            
            // Arabic Font Family
            DropdownButtonFormField<String>(
              value: preferences.arabicFontFamily,
              decoration: const InputDecoration(
                labelText: 'Arabic Font',
                border: OutlineInputBorder(),
              ),
              items: AthkarDisplayPreferences.availableArabicFonts.map((font) {
                return DropdownMenuItem(
                  value: font,
                  child: Text(font == 'default' ? 'Default' : font),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  ref.read(athkarDisplayPreferencesProvider.notifier).updateArabicFont(value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplayOptionsCard(BuildContext context, WidgetRef ref, AthkarDisplayPreferences preferences) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional display options',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('Show Reference'),
              subtitle: const Text('Display Quran/Hadith references'),
              value: preferences.showReference,
              onChanged: (value) {
                ref.read(athkarDisplayPreferencesProvider.notifier).updateDisplayOptions(
                  showReference: value,
                );
              },
            ),
            
            SwitchListTile(
              title: const Text('Show Recommended Count'),
              subtitle: const Text('Display suggested repetition count'),
              value: preferences.showRecommendedCount,
              onChanged: (value) {
                ref.read(athkarDisplayPreferencesProvider.notifier).updateDisplayOptions(
                  showRecommendedCount: value,
                );
              },
            ),
            
            SwitchListTile(
              title: const Text('Right-to-Left Text'),
              subtitle: const Text('Enable RTL layout for Arabic text'),
              value: preferences.enableRightToLeft,
              onChanged: (value) {
                ref.read(athkarDisplayPreferencesProvider.notifier).updateDisplayOptions(
                  enableRightToLeft: value,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResetButton(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: () => _showResetDialog(context, ref),
        icon: const Icon(Icons.restore),
        label: const Text('Reset to Defaults'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.orange,
          side: const BorderSide(color: Colors.orange),
        ),
      ),
    );
  }

  void _showResetDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all translation settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(athkarDisplayPreferencesProvider.notifier).resetToDefaults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Settings reset to defaults'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  String _getDisplayModeDescription(AthkarDisplayMode mode) {
    switch (mode) {
      case AthkarDisplayMode.arabicOnly:
        return 'Show only Arabic text';
      case AthkarDisplayMode.arabicWithTranslation:
        return 'Arabic text with translation';
      case AthkarDisplayMode.arabicWithTransliteration:
        return 'Arabic text with pronunciation guide';
      case AthkarDisplayMode.arabicWithBoth:
        return 'Arabic with translation and transliteration';
      case AthkarDisplayMode.translationOnly:
        return 'Show only translation text';
    }
  }
}
