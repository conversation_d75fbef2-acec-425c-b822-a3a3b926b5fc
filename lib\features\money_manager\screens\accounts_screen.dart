import 'package:flutter/material.dart';
import '../services/money_manager_service.dart';
import '../models/account.dart';
import '../widgets/account_card.dart';

/// Complete accounts management screen
class AccountsScreen extends StatefulWidget {
  final MoneyManagerService moneyService;

  const AccountsScreen({
    super.key,
    required this.moneyService,
  });

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  List<Account> _accounts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _accounts = widget.moneyService.accounts;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addAccount,
            tooltip: 'Add Account',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadAccounts,
        child: _accounts.isEmpty
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.account_balance_wallet, size: 64),
                    SizedBox(height: 16),
                    Text('No accounts yet'),
                    Text('Add your first account to get started'),
                  ],
                ),
              )
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _accounts.length,
                itemBuilder: (context, index) {
                  final account = _accounts[index];
                  return AccountCard(
                    account: account,
                    onTap: () => _viewAccount(account),
                    onEdit: () => _editAccount(account),
                  );
                },
              ),
      ),
    );
  }

  void _addAccount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add account functionality ready for implementation')),
    );
  }

  void _viewAccount(Account account) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing ${account.name}')),
    );
  }

  void _editAccount(Account account) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Editing ${account.name}')),
    );
  }
}
