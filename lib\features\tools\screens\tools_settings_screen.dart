import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/tools_settings.dart';
import '../providers/tools_settings_provider.dart';

/// Tools Builder settings screen
class ToolsSettingsScreen extends ConsumerStatefulWidget {
  const ToolsSettingsScreen({super.key});

  @override
  ConsumerState<ToolsSettingsScreen> createState() => _ToolsSettingsScreenState();
}

class _ToolsSettingsScreenState extends ConsumerState<ToolsSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(toolsSettingsProvider);
    
    return StandardizedScaffold(
      title: 'Tools Builder Settings',
      currentRoute: '/tools/settings',
      showBackButton: true,
      actions: [
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.upload),
                title: Text('Import Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'reset',
              child: ListTile(
                leading: Icon(Icons.restore, color: Colors.red),
                title: Text('Reset to Defaults', style: TextStyle(color: Colors.red)),
                dense: true,
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.design_services), text: 'UI Builder'),
          Tab(icon: Icon(Icons.code), text: 'Code Gen'),
          Tab(icon: Icon(Icons.save), text: 'Auto-Save'),
          Tab(icon: Icon(Icons.people), text: 'Collaboration'),
          Tab(icon: Icon(Icons.cloud_upload), text: 'Deployment'),
          Tab(icon: Icon(Icons.tune), text: 'Performance'),
          Tab(icon: Icon(Icons.security), text: 'Security'),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildUIBuilderSettings(settings),
          _buildCodeGenerationSettings(settings),
          _buildAutoSaveSettings(settings),
          _buildCollaborationSettings(settings),
          _buildDeploymentSettings(settings),
          _buildPerformanceSettings(settings),
          _buildSecuritySettings(settings),
        ],
      ),
    );
  }

  /// Build UI Builder settings tab
  Widget _buildUIBuilderSettings(ToolsSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Design Canvas',
            subtitle: 'Visual design environment settings',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Drag & Drop
                SwitchListTile(
                  secondary: const Icon(Icons.drag_indicator),
                  title: const Text('Drag & Drop'),
                  subtitle: const Text('Enable drag and drop components'),
                  value: settings.enableDragDrop,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateUIBuilderSettings(
                      enableDragDrop: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Enable Snapping
                SwitchListTile(
                  secondary: const Icon(Icons.grid_on),
                  title: const Text('Grid Snapping'),
                  subtitle: const Text('Snap components to grid'),
                  value: settings.enableSnapping,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateUIBuilderSettings(
                      enableSnapping: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Grid Size
                ListTile(
                  leading: const Icon(Icons.grid_4x4),
                  title: const Text('Grid Size'),
                  subtitle: Text('${settings.gridSize}px'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.gridSize.toDouble(),
                  min: 4.0,
                  max: 32.0,
                  divisions: 7,
                  label: '${settings.gridSize}px',
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateUIBuilderSettings(
                      gridSize: value.toInt(),
                    );
                  },
                ),
                
                const Divider(),
                
                // Show Grid
                SwitchListTile(
                  secondary: const Icon(Icons.grid_view),
                  title: const Text('Show Grid'),
                  subtitle: const Text('Display grid lines on canvas'),
                  value: settings.showGrid,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateUIBuilderSettings(
                      showGrid: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Show Rulers
                SwitchListTile(
                  secondary: const Icon(Icons.straighten),
                  title: const Text('Show Rulers'),
                  subtitle: const Text('Display measurement rulers'),
                  value: settings.showRulers,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateUIBuilderSettings(
                      showRulers: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build code generation settings tab
  Widget _buildCodeGenerationSettings(ToolsSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Code Generation',
            subtitle: 'How code is generated from designs',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Code Style
                ListTile(
                  leading: const Icon(Icons.code),
                  title: const Text('Code Style'),
                  subtitle: Text(_getCodeStyleText(settings.codeStyle)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showCodeStylePicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Auto Generation
                SwitchListTile(
                  secondary: const Icon(Icons.auto_awesome),
                  title: const Text('Auto Generation'),
                  subtitle: const Text('Generate code automatically on changes'),
                  value: settings.enableAutoGeneration,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateCodeGenerationSettings(
                      enableAutoGeneration: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Generate Comments
                SwitchListTile(
                  secondary: const Icon(Icons.comment),
                  title: const Text('Generate Comments'),
                  subtitle: const Text('Include comments in generated code'),
                  value: settings.generateComments,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateCodeGenerationSettings(
                      generateComments: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Code Formatting
                SwitchListTile(
                  secondary: const Icon(Icons.format_align_left),
                  title: const Text('Code Formatting'),
                  subtitle: const Text('Format generated code automatically'),
                  value: settings.enableCodeFormatting,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateCodeGenerationSettings(
                      enableCodeFormatting: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Indentation Size
                ListTile(
                  leading: const Icon(Icons.format_indent_increase),
                  title: const Text('Indentation Size'),
                  subtitle: Text('${settings.indentationSize} spaces'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.indentationSize.toDouble(),
                  min: 2.0,
                  max: 8.0,
                  divisions: 3,
                  label: '${settings.indentationSize} spaces',
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateCodeGenerationSettings(
                      indentationSize: value.toInt(),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build auto-save settings tab
  Widget _buildAutoSaveSettings(ToolsSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Auto-Save Settings',
            subtitle: 'Automatic saving configuration',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Auto Save
                SwitchListTile(
                  secondary: const Icon(Icons.save),
                  title: const Text('Enable Auto Save'),
                  subtitle: const Text('Automatically save your work'),
                  value: settings.enableAutoSave,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateAutoSaveSettings(
                      enableAutoSave: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                if (settings.enableAutoSave) ...[
                  const Divider(),
                  
                  // Auto Save Interval
                  ListTile(
                    leading: const Icon(Icons.timer),
                    title: const Text('Auto Save Interval'),
                    subtitle: Text('${settings.autoSaveIntervalSeconds} seconds'),
                    contentPadding: EdgeInsets.zero,
                  ),
                  Slider(
                    value: settings.autoSaveIntervalSeconds.toDouble(),
                    min: 10.0,
                    max: 300.0,
                    divisions: 29,
                    label: '${settings.autoSaveIntervalSeconds}s',
                    onChanged: (value) {
                      ref.read(toolsSettingsProvider.notifier).updateAutoSaveSettings(
                        autoSaveIntervalSeconds: value.toInt(),
                      );
                    },
                  ),
                  
                  const Divider(),
                  
                  // Save on Preview
                  SwitchListTile(
                    secondary: const Icon(Icons.preview),
                    title: const Text('Save on Preview'),
                    subtitle: const Text('Save when previewing tools'),
                    value: settings.saveOnPreview,
                    onChanged: (value) {
                      ref.read(toolsSettingsProvider.notifier).updateAutoSaveSettings(
                        saveOnPreview: value,
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Max Auto Save Files
                  ListTile(
                    leading: const Icon(Icons.folder),
                    title: const Text('Max Auto Save Files'),
                    subtitle: Text('${settings.maxAutoSaveFiles} files'),
                    contentPadding: EdgeInsets.zero,
                  ),
                  Slider(
                    value: settings.maxAutoSaveFiles.toDouble(),
                    min: 5.0,
                    max: 50.0,
                    divisions: 9,
                    label: '${settings.maxAutoSaveFiles} files',
                    onChanged: (value) {
                      ref.read(toolsSettingsProvider.notifier).updateAutoSaveSettings(
                        maxAutoSaveFiles: value.toInt(),
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build collaboration settings tab
  Widget _buildCollaborationSettings(ToolsSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Collaboration Features',
            subtitle: 'Work together with others',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Collaboration
                SwitchListTile(
                  secondary: const Icon(Icons.people),
                  title: const Text('Enable Collaboration'),
                  subtitle: const Text('Allow others to work on your tools'),
                  value: settings.enableCollaboration,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateCollaborationSettings(
                      enableCollaboration: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                if (settings.enableCollaboration) ...[
                  const Divider(),
                  
                  // Real-time Editing
                  SwitchListTile(
                    secondary: const Icon(Icons.edit),
                    title: const Text('Real-time Editing'),
                    subtitle: const Text('See changes from others instantly'),
                    value: settings.enableRealTimeEditing,
                    onChanged: (value) {
                      ref.read(toolsSettingsProvider.notifier).updateCollaborationSettings(
                        enableRealTimeEditing: value,
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Comments
                  SwitchListTile(
                    secondary: const Icon(Icons.comment),
                    title: const Text('Comments'),
                    subtitle: const Text('Allow comments on tools'),
                    value: settings.enableComments,
                    onChanged: (value) {
                      ref.read(toolsSettingsProvider.notifier).updateCollaborationSettings(
                        enableComments: value,
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Version History
                  SwitchListTile(
                    secondary: const Icon(Icons.history),
                    title: const Text('Version History'),
                    subtitle: const Text('Track changes over time'),
                    value: settings.enableVersionHistory,
                    onChanged: (value) {
                      ref.read(toolsSettingsProvider.notifier).updateCollaborationSettings(
                        enableVersionHistory: value,
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build deployment settings tab
  Widget _buildDeploymentSettings(ToolsSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Deployment Options',
            subtitle: 'Deploy your tools to the web',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Default Target
                ListTile(
                  leading: const Icon(Icons.cloud_upload),
                  title: const Text('Default Deployment Target'),
                  subtitle: Text(_getDeploymentTargetText(settings.defaultTarget)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showDeploymentTargetPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Auto Deploy
                SwitchListTile(
                  secondary: const Icon(Icons.auto_awesome),
                  title: const Text('Auto Deploy'),
                  subtitle: const Text('Deploy automatically on save'),
                  value: settings.enableAutoDeploy,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateDeploymentSettings(
                      enableAutoDeploy: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Preview Deploy
                SwitchListTile(
                  secondary: const Icon(Icons.preview),
                  title: const Text('Preview Deployments'),
                  subtitle: const Text('Create preview versions for testing'),
                  value: settings.enablePreviewDeploy,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateDeploymentSettings(
                      enablePreviewDeploy: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build performance settings tab
  Widget _buildPerformanceSettings(ToolsSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Performance Optimization',
            subtitle: 'Optimize app performance',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Code Optimization
                SwitchListTile(
                  secondary: const Icon(Icons.speed),
                  title: const Text('Code Optimization'),
                  subtitle: const Text('Optimize generated code for performance'),
                  value: settings.enableCodeOptimization,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updatePerformanceSettings(
                      enableCodeOptimization: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Asset Optimization
                SwitchListTile(
                  secondary: const Icon(Icons.image),
                  title: const Text('Asset Optimization'),
                  subtitle: const Text('Compress images and assets'),
                  value: settings.enableAssetOptimization,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updatePerformanceSettings(
                      enableAssetOptimization: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Lazy Loading
                SwitchListTile(
                  secondary: const Icon(Icons.hourglass_empty),
                  title: const Text('Lazy Loading'),
                  subtitle: const Text('Load components only when needed'),
                  value: settings.enableLazyLoading,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updatePerformanceSettings(
                      enableLazyLoading: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Max Undo Steps
                ListTile(
                  leading: const Icon(Icons.undo),
                  title: const Text('Max Undo Steps'),
                  subtitle: Text('${settings.maxUndoSteps} steps'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.maxUndoSteps.toDouble(),
                  min: 10.0,
                  max: 100.0,
                  divisions: 9,
                  label: '${settings.maxUndoSteps} steps',
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updatePerformanceSettings(
                      maxUndoSteps: value.toInt(),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build security settings tab
  Widget _buildSecuritySettings(ToolsSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Security & Privacy',
            subtitle: 'Protect your tools and data',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Encryption
                SwitchListTile(
                  secondary: const Icon(Icons.lock),
                  title: const Text('Enable Encryption'),
                  subtitle: const Text('Encrypt saved tools and data'),
                  value: settings.enableEncryption,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateSecuritySettings(
                      enableEncryption: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Require PIN
                SwitchListTile(
                  secondary: const Icon(Icons.pin),
                  title: const Text('Require PIN'),
                  subtitle: const Text('PIN required to access tools'),
                  value: settings.requirePinForAccess,
                  onChanged: (value) {
                    if (value) {
                      _setupPin(settings);
                    } else {
                      ref.read(toolsSettingsProvider.notifier).updateSecuritySettings(
                        requirePinForAccess: false,
                      );
                    }
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Biometric Auth
                SwitchListTile(
                  secondary: const Icon(Icons.fingerprint),
                  title: const Text('Biometric Authentication'),
                  subtitle: const Text('Use fingerprint/face unlock'),
                  value: settings.enableBiometricAuth,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateSecuritySettings(
                      enableBiometricAuth: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Audit Log
                SwitchListTile(
                  secondary: const Icon(Icons.history_edu),
                  title: const Text('Audit Log'),
                  subtitle: const Text('Track all actions and changes'),
                  value: settings.enableAuditLog,
                  onChanged: (value) {
                    ref.read(toolsSettingsProvider.notifier).updateSecuritySettings(
                      enableAuditLog: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportSettings();
        break;
      case 'import':
        _importSettings();
        break;
      case 'reset':
        _resetSettings();
        break;
    }
  }

  /// Export settings
  void _exportSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings exported successfully')),
    );
  }

  /// Import settings
  void _importSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality will be implemented')),
    );
  }

  /// Reset settings
  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(toolsSettingsProvider.notifier).resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to defaults')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  /// Get code style text
  String _getCodeStyleText(CodeGenerationStyle style) {
    switch (style) {
      case CodeGenerationStyle.clean:
        return 'Clean';
      case CodeGenerationStyle.verbose:
        return 'Verbose';
      case CodeGenerationStyle.minimal:
        return 'Minimal';
      case CodeGenerationStyle.documented:
        return 'Documented';
    }
  }

  /// Get deployment target text
  String _getDeploymentTargetText(DeploymentTarget target) {
    switch (target) {
      case DeploymentTarget.none:
        return 'None';
      case DeploymentTarget.firebase:
        return 'Firebase';
      case DeploymentTarget.github:
        return 'GitHub Pages';
      case DeploymentTarget.netlify:
        return 'Netlify';
      case DeploymentTarget.vercel:
        return 'Vercel';
      case DeploymentTarget.aws:
        return 'AWS';
      case DeploymentTarget.azure:
        return 'Azure';
      case DeploymentTarget.custom:
        return 'Custom';
    }
  }

  /// Show code style picker
  void _showCodeStylePicker(ToolsSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Code Style'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: CodeGenerationStyle.values.map((style) {
            return ListTile(
              title: Text(_getCodeStyleText(style)),
              trailing: settings.codeStyle == style
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(toolsSettingsProvider.notifier).updateCodeGenerationSettings(
                  codeStyle: style,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show deployment target picker
  void _showDeploymentTargetPicker(ToolsSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deployment Target'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: DeploymentTarget.values.length,
            itemBuilder: (context, index) {
              final target = DeploymentTarget.values[index];
              return ListTile(
                title: Text(_getDeploymentTargetText(target)),
                trailing: settings.defaultTarget == target
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  ref.read(toolsSettingsProvider.notifier).updateDeploymentSettings(
                    defaultTarget: target,
                  );
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// Setup PIN
  void _setupPin(ToolsSettings settings) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('PIN setup will be implemented')),
    );
  }
}
