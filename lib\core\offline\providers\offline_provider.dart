import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/sync_models.dart';
import '../services/offline_sync_service.dart';

// Offline sync service provider
final offlineSyncServiceProvider = Provider<OfflineSyncService>((ref) {
  return OfflineSyncService();
});

// Sync stats stream provider
final syncStatsProvider = StreamProvider<SyncStats>((ref) {
  final service = ref.watch(offlineSyncServiceProvider);
  return service.statsStream;
});

// Sync conflicts stream provider
final syncConflictsProvider = StreamProvider<List<SyncConflict>>((ref) {
  final service = ref.watch(offlineSyncServiceProvider);
  return service.conflictsStream;
});

// Current sync stats provider
final currentSyncStatsProvider = Provider<SyncStats?>((ref) {
  final statsAsync = ref.watch(syncStatsProvider);
  return statsAsync.when(
    data: (stats) => stats,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Online status provider
final isOnlineProvider = Provider<bool>((ref) {
  final stats = ref.watch(currentSyncStatsProvider);
  return stats?.isOnline ?? false;
});

// Sync status provider
final isSyncingProvider = Provider<bool>((ref) {
  final stats = ref.watch(currentSyncStatsProvider);
  return stats?.isSyncing ?? false;
});

// Has conflicts provider
final hasConflictsProvider = Provider<bool>((ref) {
  final stats = ref.watch(currentSyncStatsProvider);
  return stats?.hasConflicts ?? false;
});

// Pending items count provider
final pendingItemsCountProvider = Provider<int>((ref) {
  final stats = ref.watch(currentSyncStatsProvider);
  return stats?.pendingItems ?? 0;
});

// Offline sync actions provider
final offlineSyncActionsProvider = Provider<OfflineSyncActions>((ref) {
  final service = ref.watch(offlineSyncServiceProvider);
  return OfflineSyncActions(service);
});

// Offline sync helper class
class OfflineSyncActions {
  final OfflineSyncService _service;

  OfflineSyncActions(this._service);

  // Configuration
  Future<void> updateConfiguration(SyncConfiguration config) async {
    await _service.updateConfiguration(config);
  }

  SyncConfiguration get currentConfiguration => _service.config;

  // Queue operations
  Future<void> queueCreate({
    required String entityType,
    required String entityId,
    required Map<String, dynamic> data,
    SyncPriority priority = SyncPriority.normal,
  }) async {
    await _service.queueForSync(
      entityType: entityType,
      entityId: entityId,
      operation: SyncOperation.create,
      data: data,
      priority: priority,
    );
  }

  Future<void> queueUpdate({
    required String entityType,
    required String entityId,
    required Map<String, dynamic> data,
    Map<String, dynamic>? previousData,
    SyncPriority priority = SyncPriority.normal,
  }) async {
    await _service.queueForSync(
      entityType: entityType,
      entityId: entityId,
      operation: SyncOperation.update,
      data: data,
      previousData: previousData,
      priority: priority,
    );
  }

  Future<void> queueDelete({
    required String entityType,
    required String entityId,
    required Map<String, dynamic> data,
    SyncPriority priority = SyncPriority.normal,
  }) async {
    await _service.queueForSync(
      entityType: entityType,
      entityId: entityId,
      operation: SyncOperation.delete,
      data: data,
      priority: priority,
    );
  }

  // Sync operations
  Future<void> syncNow() async {
    await _service.syncNow();
  }

  Future<void> syncEntityType(String entityType) async {
    await _service.syncEntityType(entityType);
  }

  // Conflict resolution
  Future<void> resolveConflict(
    String entityType,
    String entityId,
    ConflictResolution resolution,
  ) async {
    await _service.resolveConflict(entityType, entityId, resolution);
  }

  Future<void> resolveAllConflicts([ConflictResolution? resolution]) async {
    await _service.resolveAllConflicts(resolution);
  }

  // Queue management
  Future<void> clearSyncQueue() async {
    await _service.clearSyncQueue();
  }

  Future<void> retryFailedItems() async {
    await _service.retryFailedItems();
  }

  // Entity-specific sync helpers
  Future<void> syncNote({
    required String noteId,
    required String title,
    required String content,
    required DateTime modifiedAt,
    SyncOperation operation = SyncOperation.update,
  }) async {
    final data = {
      'id': noteId,
      'title': title,
      'content': content,
      'modifiedAt': modifiedAt.toIso8601String(),
    };

    await _service.queueForSync(
      entityType: 'note',
      entityId: noteId,
      operation: operation,
      data: data,
      priority: SyncPriority.normal,
    );
  }

  Future<void> syncTodo({
    required String todoId,
    required String title,
    required bool isCompleted,
    required DateTime modifiedAt,
    String? description,
    DateTime? dueDate,
    SyncOperation operation = SyncOperation.update,
  }) async {
    final data = {
      'id': todoId,
      'title': title,
      'description': description,
      'isCompleted': isCompleted,
      'dueDate': dueDate?.toIso8601String(),
      'modifiedAt': modifiedAt.toIso8601String(),
    };

    await _service.queueForSync(
      entityType: 'todo',
      entityId: todoId,
      operation: operation,
      data: data,
      priority: SyncPriority.normal,
    );
  }

  Future<void> syncVoiceMemo({
    required String memoId,
    required String title,
    required String filePath,
    required DateTime modifiedAt,
    String? transcription,
    SyncOperation operation = SyncOperation.update,
  }) async {
    final data = {
      'id': memoId,
      'title': title,
      'filePath': filePath,
      'transcription': transcription,
      'modifiedAt': modifiedAt.toIso8601String(),
    };

    await _service.queueForSync(
      entityType: 'voice_memo',
      entityId: memoId,
      operation: operation,
      data: data,
      priority: SyncPriority.high, // Voice memos are high priority
    );
  }

  Future<void> syncDhikrRoutine({
    required String routineId,
    required String name,
    required List<Map<String, dynamic>> dhikrs,
    required DateTime modifiedAt,
    SyncOperation operation = SyncOperation.update,
  }) async {
    final data = {
      'id': routineId,
      'name': name,
      'dhikrs': dhikrs,
      'modifiedAt': modifiedAt.toIso8601String(),
    };

    await _service.queueForSync(
      entityType: 'dhikr_routine',
      entityId: routineId,
      operation: operation,
      data: data,
      priority: SyncPriority.normal,
    );
  }

  Future<void> syncSettings({
    required Map<String, dynamic> settings,
    required DateTime modifiedAt,
  }) async {
    final data = {
      'settings': settings,
      'modifiedAt': modifiedAt.toIso8601String(),
    };

    await _service.queueForSync(
      entityType: 'settings',
      entityId: 'user_settings',
      operation: SyncOperation.update,
      data: data,
      priority: SyncPriority.low,
    );
  }

  // Batch operations
  Future<void> syncMultipleNotes(List<Map<String, dynamic>> notes) async {
    for (final note in notes) {
      await syncNote(
        noteId: note['id'],
        title: note['title'],
        content: note['content'],
        modifiedAt: DateTime.parse(note['modifiedAt']),
        operation: SyncOperation.update,
      );
    }
  }

  Future<void> syncMultipleTodos(List<Map<String, dynamic>> todos) async {
    for (final todo in todos) {
      await syncTodo(
        todoId: todo['id'],
        title: todo['title'],
        isCompleted: todo['isCompleted'],
        modifiedAt: DateTime.parse(todo['modifiedAt']),
        description: todo['description'],
        dueDate: todo['dueDate'] != null ? DateTime.parse(todo['dueDate']) : null,
        operation: SyncOperation.update,
      );
    }
  }

  // Sync status helpers
  bool get isOnline => _service.isOnline;
  bool get isSyncing => _service.isSyncing;
  SyncStats get currentStats => _service.currentStats;
  List<SyncConflict> get conflicts => _service.conflicts;

  // Configuration presets
  Future<void> enableAutoSync() async {
    final config = _service.config.copyWith(autoSync: true);
    await updateConfiguration(config);
  }

  Future<void> disableAutoSync() async {
    final config = _service.config.copyWith(autoSync: false);
    await updateConfiguration(config);
  }

  Future<void> setSyncInterval(Duration interval) async {
    final config = _service.config.copyWith(syncInterval: interval);
    await updateConfiguration(config);
  }

  Future<void> enableWifiOnlySync() async {
    final config = _service.config.copyWith(syncOnlyOnWifi: true);
    await updateConfiguration(config);
  }

  Future<void> disableWifiOnlySync() async {
    final config = _service.config.copyWith(syncOnlyOnWifi: false);
    await updateConfiguration(config);
  }

  Future<void> setDefaultConflictResolution(ConflictResolution resolution) async {
    final config = _service.config.copyWith(defaultConflictResolution: resolution);
    await updateConfiguration(config);
  }

  Future<void> enableEntityTypeSync(String entityType) async {
    final enabledTypes = List<String>.from(_service.config.enabledEntityTypes);
    if (!enabledTypes.contains(entityType)) {
      enabledTypes.add(entityType);
      final config = _service.config.copyWith(enabledEntityTypes: enabledTypes);
      await updateConfiguration(config);
    }
  }

  Future<void> disableEntityTypeSync(String entityType) async {
    final enabledTypes = List<String>.from(_service.config.enabledEntityTypes);
    enabledTypes.remove(entityType);
    final config = _service.config.copyWith(enabledEntityTypes: enabledTypes);
    await updateConfiguration(config);
  }

  // Enable all entity types for sync
  Future<void> enableAllEntityTypes() async {
    const allEntityTypes = [
      'note',
      'todo',
      'voice_memo',
      'dhikr_routine',
      'settings',
    ];
    
    final config = _service.config.copyWith(enabledEntityTypes: allEntityTypes);
    await updateConfiguration(config);
  }
}

// Initialize offline sync service provider
final initializeOfflineSyncServiceProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(offlineSyncServiceProvider);
  await service.initialize();
});
