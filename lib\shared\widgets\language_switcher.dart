import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/localization/localization_provider.dart';
import '../../l10n/generated/app_localizations.dart';

class LanguageSwitcher extends ConsumerWidget {
  final bool showLabel;
  final bool showFlag;
  final bool compact;

  const LanguageSwitcher({
    super.key,
    this.showLabel = true,
    this.showFlag = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocale = ref.watch(localeProvider);
    final l10n = AppLocalizations.of(context);

    if (compact) {
      return IconButton(
        onPressed: () => _showLanguageDialog(context, ref),
        icon: const Icon(Icons.language),
        tooltip: l10n.language,
      );
    }

    return PopupMenuButton<Locale>(
      onSelected: (locale) => _changeLanguage(ref, locale),
      itemBuilder: (context) => _buildMenuItems(context),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showFlag) ...[
              Text(
                _getFlagForLocale(currentLocale),
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
            ],
            if (showLabel) ...[
              Text(
                _getLanguageName(currentLocale),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 4),
            ],
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ),
    );
  }

  List<PopupMenuEntry<Locale>> _buildMenuItems(BuildContext context) {
    final languages = LanguageService.getAvailableLanguages();
    
    return languages.map((language) {
      return PopupMenuItem<Locale>(
        value: language.locale,
        child: Row(
          children: [
            Text(
              language.flag,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  language.nativeName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  language.name,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }).toList();
  }

  void _showLanguageDialog(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final languages = LanguageService.getAvailableLanguages();
    final currentLocale = ref.read(localeProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.language),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languages.map((language) {
            final isSelected = language.locale == currentLocale;
            
            return RadioListTile<Locale>(
              value: language.locale,
              groupValue: currentLocale,
              onChanged: (locale) {
                if (locale != null) {
                  _changeLanguage(ref, locale);
                  Navigator.of(context).pop();
                }
              },
              title: Row(
                children: [
                  Text(
                    language.flag,
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        language.nativeName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                      Text(
                        language.name,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _changeLanguage(WidgetRef ref, Locale locale) {
    LanguageService.switchLanguage(ref, locale);
  }

  String _getFlagForLocale(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return '🇸🇦';
      case 'en':
        return '🇺🇸';
      default:
        return '🌐';
    }
  }

  String _getLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return 'English';
    }
  }
}

class LanguageToggleButton extends ConsumerWidget {
  final bool showText;

  const LanguageToggleButton({
    super.key,
    this.showText = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocale = ref.watch(localeProvider);
    final l10n = AppLocalizations.of(context);

    return ElevatedButton.icon(
      onPressed: () => LanguageService.toggleLanguage(ref),
      icon: Text(
        _getFlagForLocale(currentLocale),
        style: const TextStyle(fontSize: 16),
      ),
      label: showText 
          ? Text(_getOtherLanguageName(currentLocale))
          : const SizedBox.shrink(),
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.symmetric(
          horizontal: showText ? 16 : 12,
          vertical: 8,
        ),
      ),
    );
  }

  String _getFlagForLocale(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return '🇺🇸';
      case 'en':
        return '🇸🇦';
      default:
        return '🌐';
    }
  }

  String _getOtherLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return 'English';
      case 'en':
        return 'العربية';
      default:
        return 'العربية';
    }
  }
}

class RTLDirectionalityWrapper extends ConsumerWidget {
  final Widget child;

  const RTLDirectionalityWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textDirection = ref.watch(textDirectionProvider);
    
    return Directionality(
      textDirection: textDirection,
      child: child,
    );
  }
}

class LocalizedText extends StatelessWidget {
  final String Function(AppLocalizations) textBuilder;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const LocalizedText(
    this.textBuilder, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Text(
      textBuilder(l10n),
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

// Helper extension for easy localization access
extension LocalizationExtension on BuildContext {
  AppLocalizations get l10n => AppLocalizations.of(this);
}

// Helper widget for RTL-aware icons
class RTLAwareIcon extends ConsumerWidget {
  final IconData ltrIcon;
  final IconData rtlIcon;
  final double? size;
  final Color? color;

  const RTLAwareIcon({
    super.key,
    required this.ltrIcon,
    required this.rtlIcon,
    this.size,
    this.color,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isRTL = ref.watch(isRTLProvider);
    
    return Icon(
      isRTL ? rtlIcon : ltrIcon,
      size: size,
      color: color,
    );
  }
}

// Helper widget for RTL-aware padding
class RTLAwarePadding extends ConsumerWidget {
  final Widget child;
  final double start;
  final double end;
  final double top;
  final double bottom;

  const RTLAwarePadding({
    super.key,
    required this.child,
    this.start = 0.0,
    this.end = 0.0,
    this.top = 0.0,
    this.bottom = 0.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isRTL = ref.watch(isRTLProvider);
    
    return Padding(
      padding: EdgeInsets.only(
        left: isRTL ? end : start,
        right: isRTL ? start : end,
        top: top,
        bottom: bottom,
      ),
      child: child,
    );
  }
}
