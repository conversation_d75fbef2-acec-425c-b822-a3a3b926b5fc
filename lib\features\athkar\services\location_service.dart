import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/location_settings.dart';

/// Service for handling location detection and search
class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  /// Get current location automatically
  Future<LocationSettings?> getCurrentLocation(String userId) async {
    try {
      // Try to get location from IP geolocation as fallback
      final response = await http.get(
        Uri.parse('http://ip-api.com/json/'),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'success') {
          return LocationSettings.create(
            name: '${data['city']}, ${data['country']}',
            latitude: data['lat'].toDouble(),
            longitude: data['lon'].toDouble(),
            userId: userId,
            timezone: data['timezone'] ?? 'UTC',
            country: data['country'] ?? '',
            city: data['city'] ?? '',
            calculationMethod: CalculationMethod.muslimWorldLeague,
          );
        }
      }
    } catch (e) {
      print('Error getting current location: $e');
    }
    
    // Return default Jordan location as fallback
    return LocationSettings.createDefaultJordan(userId);
  }

  /// Search for locations by name
  Future<List<LocationSearchResult>> searchLocations(String query) async {
    if (query.trim().isEmpty) return [];
    
    try {
      // Use OpenStreetMap Nominatim API for location search
      final encodedQuery = Uri.encodeComponent(query);
      final response = await http.get(
        Uri.parse('https://nominatim.openstreetmap.org/search?q=$encodedQuery&format=json&limit=10&addressdetails=1'),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'ShadowSuite/1.0',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        
        return data.map((item) => LocationSearchResult(
          displayName: item['display_name'] ?? 'Unknown Location',
          latitude: double.parse(item['lat']),
          longitude: double.parse(item['lon']),
          country: item['address']?['country'] ?? '',
          city: item['address']?['city'] ?? item['address']?['town'] ?? item['address']?['village'] ?? '',
          state: item['address']?['state'] ?? '',
        )).toList();
      }
    } catch (e) {
      print('Error searching locations: $e');
    }
    
    // Return popular Islamic cities as fallback
    return _getPopularIslamicCities(query);
  }

  /// Get popular Islamic cities for fallback
  List<LocationSearchResult> _getPopularIslamicCities(String query) {
    final cities = [
      LocationSearchResult(
        displayName: 'Mecca, Saudi Arabia',
        latitude: 21.4225,
        longitude: 39.8262,
        country: 'Saudi Arabia',
        city: 'Mecca',
        state: 'Makkah Province',
      ),
      LocationSearchResult(
        displayName: 'Medina, Saudi Arabia',
        latitude: 24.4686,
        longitude: 39.6142,
        country: 'Saudi Arabia',
        city: 'Medina',
        state: 'Al Madinah Province',
      ),
      LocationSearchResult(
        displayName: 'Riyadh, Saudi Arabia',
        latitude: 24.7136,
        longitude: 46.6753,
        country: 'Saudi Arabia',
        city: 'Riyadh',
        state: 'Riyadh Province',
      ),
      LocationSearchResult(
        displayName: 'Cairo, Egypt',
        latitude: 30.0444,
        longitude: 31.2357,
        country: 'Egypt',
        city: 'Cairo',
        state: 'Cairo Governorate',
      ),
      LocationSearchResult(
        displayName: 'Istanbul, Turkey',
        latitude: 41.0082,
        longitude: 28.9784,
        country: 'Turkey',
        city: 'Istanbul',
        state: 'Istanbul Province',
      ),
      LocationSearchResult(
        displayName: 'Amman, Jordan',
        latitude: 31.9454,
        longitude: 35.9284,
        country: 'Jordan',
        city: 'Amman',
        state: 'Amman Governorate',
      ),
      LocationSearchResult(
        displayName: 'Dubai, UAE',
        latitude: 25.2048,
        longitude: 55.2708,
        country: 'United Arab Emirates',
        city: 'Dubai',
        state: 'Dubai Emirate',
      ),
      LocationSearchResult(
        displayName: 'Kuala Lumpur, Malaysia',
        latitude: 3.1390,
        longitude: 101.6869,
        country: 'Malaysia',
        city: 'Kuala Lumpur',
        state: 'Federal Territory of Kuala Lumpur',
      ),
      LocationSearchResult(
        displayName: 'Jakarta, Indonesia',
        latitude: -6.2088,
        longitude: 106.8456,
        country: 'Indonesia',
        city: 'Jakarta',
        state: 'Jakarta Special Capital Region',
      ),
      LocationSearchResult(
        displayName: 'London, United Kingdom',
        latitude: 51.5074,
        longitude: -0.1278,
        country: 'United Kingdom',
        city: 'London',
        state: 'England',
      ),
    ];

    if (query.trim().isEmpty) return cities;
    
    return cities.where((city) => 
      city.displayName.toLowerCase().contains(query.toLowerCase()) ||
      city.city.toLowerCase().contains(query.toLowerCase()) ||
      city.country.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  /// Convert search result to LocationSettings
  LocationSettings searchResultToLocationSettings(
    LocationSearchResult result,
    String userId, {
    CalculationMethod? method,
  }) {
    return LocationSettings.create(
      name: result.displayName,
      latitude: result.latitude,
      longitude: result.longitude,
      userId: userId,
      timezone: _getTimezoneForLocation(result.latitude, result.longitude),
      country: result.country,
      city: result.city,
      calculationMethod: method ?? _getDefaultCalculationMethod(result.country),
    );
  }

  /// Get default calculation method based on country
  CalculationMethod _getDefaultCalculationMethod(String country) {
    switch (country.toLowerCase()) {
      case 'saudi arabia':
      case 'united arab emirates':
      case 'qatar':
      case 'kuwait':
      case 'bahrain':
      case 'oman':
        return CalculationMethod.ummAlQura;
      case 'egypt':
      case 'libya':
      case 'sudan':
        return CalculationMethod.egyptian;
      case 'pakistan':
      case 'india':
      case 'bangladesh':
        return CalculationMethod.karachi;
      case 'jordan':
      case 'palestine':
      case 'syria':
      case 'lebanon':
        return CalculationMethod.jordan;
      default:
        return CalculationMethod.muslimWorldLeague;
    }
  }

  /// Get timezone for location (simplified)
  String _getTimezoneForLocation(double latitude, double longitude) {
    // Simplified timezone detection based on longitude
    final offsetHours = (longitude / 15).round();
    if (offsetHours >= 0) {
      return 'UTC+$offsetHours';
    } else {
      return 'UTC$offsetHours';
    }
  }

  /// Validate coordinates
  bool isValidCoordinates(double latitude, double longitude) {
    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
  }
}

/// Location search result model
class LocationSearchResult {
  final String displayName;
  final double latitude;
  final double longitude;
  final String country;
  final String city;
  final String state;

  const LocationSearchResult({
    required this.displayName,
    required this.latitude,
    required this.longitude,
    required this.country,
    required this.city,
    required this.state,
  });

  @override
  String toString() => displayName;
}
