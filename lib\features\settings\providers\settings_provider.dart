import 'package:flutter/material.dart' hide ThemeMode;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/app_settings.dart';

// Settings provider
final settingsProvider = StateNotifierProvider<SettingsNotifier, AppSettings>((ref) {
  return SettingsNotifier();
});

// Theme provider (derived from settings)
final themeProvider = Provider<AppSettings>((ref) {
  return ref.watch(settingsProvider);
});

// Accessibility provider (derived from settings)
final accessibilityProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(settingsProvider);
  return {
    'highContrast': settings.highContrastMode,
    'largeText': settings.largeTextMode,
    'screenReader': settings.screenReaderSupport,
    'reduceMotion': settings.reduceMotion,
    'buttonSize': settings.buttonSize,
    'showTooltips': settings.showTooltips,
  };
});

// Performance provider (derived from settings)
final performanceProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(settingsProvider);
  return {
    'animations': settings.animationsEnabled,
    'hapticFeedback': settings.hapticFeedbackEnabled,
    'autoSave': settings.autoSaveEnabled,
    'autoSaveInterval': settings.autoSaveIntervalSeconds,
    'lowPowerMode': settings.lowPowerMode,
  };
});

class SettingsNotifier extends StateNotifier<AppSettings> {
  static const String _settingsKey = 'app_settings';
  SharedPreferences? _prefs;

  SettingsNotifier() : super(AppSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      final settingsJson = _prefs?.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        state = AppSettings.fromJson(settingsMap);
      }
    } catch (e) {
      debugPrint('Error loading settings: $e');
      // Use default settings if loading fails
      state = AppSettings();
    }
  }

  Future<void> _saveSettings() async {
    try {
      _prefs ??= await SharedPreferences.getInstance();
      final settingsJson = json.encode(state.toJson());
      await _prefs!.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving settings: $e');
    }
  }

  // Theme settings
  Future<void> updateThemeMode(ThemeMode themeMode) async {
    state = state.copyWith(themeMode: themeMode);
    await _saveSettings();
  }

  Future<void> updatePrimaryColor(Color color) async {
    state = state.copyWith(primaryColor: color);
    await _saveSettings();
  }

  Future<void> updateAccentColor(Color color) async {
    state = state.copyWith(accentColor: color);
    await _saveSettings();
  }

  Future<void> updateUseMaterial3(bool useMaterial3) async {
    state = state.copyWith(useMaterial3: useMaterial3);
    await _saveSettings();
  }

  Future<void> updateFontSize(double fontSize) async {
    state = state.copyWith(fontSize: fontSize);
    await _saveSettings();
  }

  Future<void> updateUseSystemFont(bool useSystemFont) async {
    state = state.copyWith(useSystemFont: useSystemFont);
    await _saveSettings();
  }

  Future<void> updateCustomFontFamily(String fontFamily) async {
    state = state.copyWith(customFontFamily: fontFamily);
    await _saveSettings();
  }

  // Language and localization
  Future<void> updateLanguage(Language language) async {
    state = state.copyWith(language: language);
    await _saveSettings();
  }

  Future<void> updateDateFormat(DateFormat dateFormat) async {
    state = state.copyWith(dateFormat: dateFormat);
    await _saveSettings();
  }

  Future<void> updateTimeFormat(TimeFormat timeFormat) async {
    state = state.copyWith(timeFormat: timeFormat);
    await _saveSettings();
  }

  Future<void> updateTimezone(String timezone) async {
    state = state.copyWith(timezone: timezone);
    await _saveSettings();
  }

  // Notifications
  Future<void> updateNotificationsEnabled(bool enabled) async {
    state = state.copyWith(notificationsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateNotificationSound(NotificationSound sound) async {
    state = state.copyWith(notificationSound: sound);
    await _saveSettings();
  }

  Future<void> updateVibrationEnabled(bool enabled) async {
    state = state.copyWith(vibrationEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateShowNotificationPreviews(bool show) async {
    state = state.copyWith(showNotificationPreviews: show);
    await _saveSettings();
  }

  Future<void> updateQuietHours(TimeOfDay? start, TimeOfDay? end) async {
    state = state.copyWith(quietHoursStart: start, quietHoursEnd: end);
    await _saveSettings();
  }

  // Privacy and security
  Future<void> updateRequireAuthentication(bool require) async {
    state = state.copyWith(requireAuthentication: require);
    await _saveSettings();
  }

  Future<void> updateBiometricAuthentication(bool enabled) async {
    state = state.copyWith(biometricAuthentication: enabled);
    await _saveSettings();
  }

  Future<void> updateAutoLockMinutes(int minutes) async {
    state = state.copyWith(autoLockMinutes: minutes);
    await _saveSettings();
  }

  Future<void> updateHideContentInRecents(bool hide) async {
    state = state.copyWith(hideContentInRecents: hide);
    await _saveSettings();
  }

  Future<void> updateAnalyticsEnabled(bool enabled) async {
    state = state.copyWith(analyticsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateCrashReportingEnabled(bool enabled) async {
    state = state.copyWith(crashReportingEnabled: enabled);
    await _saveSettings();
  }

  // Backup and sync
  Future<void> updateAutoBackupEnabled(bool enabled) async {
    state = state.copyWith(autoBackupEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateCloudSyncEnabled(bool enabled) async {
    state = state.copyWith(cloudSyncEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateBackupFrequency(String frequency) async {
    state = state.copyWith(backupFrequency: frequency);
    await _saveSettings();
  }

  Future<void> updateSyncOnWifiOnly(bool wifiOnly) async {
    state = state.copyWith(syncOnWifiOnly: wifiOnly);
    await _saveSettings();
  }

  Future<void> updateMaxBackupFiles(int maxFiles) async {
    state = state.copyWith(maxBackupFiles: maxFiles);
    await _saveSettings();
  }

  // Performance
  Future<void> updateAnimationsEnabled(bool enabled) async {
    state = state.copyWith(animationsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateHapticFeedbackEnabled(bool enabled) async {
    state = state.copyWith(hapticFeedbackEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateAutoSaveEnabled(bool enabled) async {
    state = state.copyWith(autoSaveEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateAutoSaveIntervalSeconds(int seconds) async {
    state = state.copyWith(autoSaveIntervalSeconds: seconds);
    await _saveSettings();
  }

  Future<void> updateLowPowerMode(bool enabled) async {
    state = state.copyWith(lowPowerMode: enabled);
    await _saveSettings();
  }

  // Accessibility
  Future<void> updateHighContrastMode(bool enabled) async {
    state = state.copyWith(highContrastMode: enabled);
    await _saveSettings();
  }

  Future<void> updateLargeTextMode(bool enabled) async {
    state = state.copyWith(largeTextMode: enabled);
    await _saveSettings();
  }

  Future<void> updateScreenReaderSupport(bool enabled) async {
    state = state.copyWith(screenReaderSupport: enabled);
    await _saveSettings();
  }

  Future<void> updateReduceMotion(bool enabled) async {
    state = state.copyWith(reduceMotion: enabled);
    await _saveSettings();
  }

  Future<void> updateButtonSize(double size) async {
    state = state.copyWith(buttonSize: size);
    await _saveSettings();
  }

  Future<void> updateShowTooltips(bool show) async {
    state = state.copyWith(showTooltips: show);
    await _saveSettings();
  }

  // Voice memos
  Future<void> updateAudioQuality(String quality) async {
    state = state.copyWith(audioQuality: quality);
    await _saveSettings();
  }

  Future<void> updateAudioFormat(String format) async {
    state = state.copyWith(audioFormat: format);
    await _saveSettings();
  }

  Future<void> updateAutoTranscription(bool enabled) async {
    state = state.copyWith(autoTranscription: enabled);
    await _saveSettings();
  }

  Future<void> updateMaxRecordingMinutes(int minutes) async {
    state = state.copyWith(maxRecordingMinutes: minutes);
    await _saveSettings();
  }

  // Notes
  Future<void> updateAutoSaveNotes(bool enabled) async {
    state = state.copyWith(autoSaveNotes: enabled);
    await _saveSettings();
  }

  Future<void> updateDefaultNoteCategory(String category) async {
    state = state.copyWith(defaultNoteCategory: category);
    await _saveSettings();
  }

  Future<void> updateShowWordCount(bool show) async {
    state = state.copyWith(showWordCount: show);
    await _saveSettings();
  }

  Future<void> updateSpellCheckEnabled(bool enabled) async {
    state = state.copyWith(spellCheckEnabled: enabled);
    await _saveSettings();
  }

  Future<void> updateExportFormat(String format) async {
    state = state.copyWith(exportFormat: format);
    await _saveSettings();
  }

  // Todos
  Future<void> updateShowCompletedTodos(bool show) async {
    state = state.copyWith(showCompletedTodos: show);
    await _saveSettings();
  }

  Future<void> updateDefaultReminderMinutes(int minutes) async {
    state = state.copyWith(defaultReminderMinutes: minutes);
    await _saveSettings();
  }

  Future<void> updateAutoArchiveCompleted(bool enabled) async {
    state = state.copyWith(autoArchiveCompleted: enabled);
    await _saveSettings();
  }

  Future<void> updateAutoArchiveDays(int days) async {
    state = state.copyWith(autoArchiveDays: days);
    await _saveSettings();
  }

  // Dhikr
  Future<void> updateDhikrVibration(bool enabled) async {
    state = state.copyWith(dhikrVibration: enabled);
    await _saveSettings();
  }

  Future<void> updateDhikrSound(bool enabled) async {
    state = state.copyWith(dhikrSound: enabled);
    await _saveSettings();
  }

  Future<void> updateDefaultDhikrTarget(int target) async {
    state = state.copyWith(defaultDhikrTarget: target);
    await _saveSettings();
  }

  Future<void> updateShowArabicText(bool show) async {
    state = state.copyWith(showArabicText: show);
    await _saveSettings();
  }

  Future<void> updateShowTransliteration(bool show) async {
    state = state.copyWith(showTransliteration: show);
    await _saveSettings();
  }

  Future<void> updateShowTranslation(bool show) async {
    state = state.copyWith(showTranslation: show);
    await _saveSettings();
  }

  // Advanced
  Future<void> updateDeveloperMode(bool enabled) async {
    state = state.copyWith(developerMode: enabled);
    await _saveSettings();
  }

  Future<void> updateDebugLogging(bool enabled) async {
    state = state.copyWith(debugLogging: enabled);
    await _saveSettings();
  }

  Future<void> updateLogLevel(String level) async {
    state = state.copyWith(logLevel: level);
    await _saveSettings();
  }

  Future<void> updateBetaFeatures(bool enabled) async {
    state = state.copyWith(betaFeatures: enabled);
    await _saveSettings();
  }

  // Bulk operations
  Future<void> resetToDefaults() async {
    state = AppSettings();
    await _saveSettings();
  }

  Future<void> importSettings(Map<String, dynamic> settingsMap) async {
    try {
      state = AppSettings.fromJson(settingsMap);
      await _saveSettings();
    } catch (e) {
      debugPrint('Error importing settings: $e');
      rethrow;
    }
  }

  Map<String, dynamic> exportSettings() {
    return state.toJson();
  }
}
