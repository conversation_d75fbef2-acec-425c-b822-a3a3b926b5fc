import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/locale_config.dart';

class TranslationService {
  static final TranslationService _instance = TranslationService._internal();
  factory TranslationService() => _instance;
  TranslationService._internal();

  static const String _localeKey = 'app_locale';
  static const String _customTranslationsKey = 'custom_translations';

  LocaleConfig _currentConfig = LocaleConfig.english();
  Map<String, Map<String, String>> _translations = {};
  final StreamController<LocaleConfig> _localeController = 
      StreamController<LocaleConfig>.broadcast();

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Getters
  Stream<LocaleConfig> get localeStream => _localeController.stream;
  LocaleConfig get currentConfig => _currentConfig;
  Locale get currentLocale => _currentConfig.locale;
  TextDirection get textDirection => _currentConfig.textDirection;
  bool get isRTL => _currentConfig.isRTL;
  bool get isLTR => _currentConfig.isLTR;
  SupportedLanguage get currentLanguage => _currentConfig.language;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSavedLocale();
      await _loadTranslations();
      _isInitialized = true;
      debugPrint('TranslationService initialized with locale: ${_currentConfig.locale}');
    } catch (e) {
      debugPrint('Error initializing TranslationService: $e');
    }
  }

  // Change locale
  Future<void> setLocale(SupportedLanguage language, {String? countryCode}) async {
    final newConfig = _currentConfig.copyWith(
      language: language,
      countryCode: countryCode,
      useSystemLocale: false,
    );

    await _updateLocaleConfig(newConfig);
  }

  // Use system locale
  Future<void> useSystemLocale() async {
    final systemLocale = _getSystemLocale();
    final language = SupportedLanguage.fromLocale(systemLocale);
    
    final newConfig = LocaleConfig(
      language: language,
      countryCode: systemLocale.countryCode,
      useSystemLocale: true,
      fallbackToEnglish: _currentConfig.fallbackToEnglish,
      customTranslations: _currentConfig.customTranslations,
    );

    await _updateLocaleConfig(newConfig);
  }

  // Get translation
  String translate(String key, {Map<String, dynamic>? params}) {
    final languageCode = _currentConfig.language.code;
    
    // Check custom translations first
    if (_currentConfig.customTranslations.containsKey(key)) {
      return _interpolate(_currentConfig.customTranslations[key]!, params);
    }

    // Check loaded translations
    final languageTranslations = _translations[languageCode];
    if (languageTranslations != null && languageTranslations.containsKey(key)) {
      return _interpolate(languageTranslations[key]!, params);
    }

    // Fallback to English if enabled
    if (_currentConfig.fallbackToEnglish && languageCode != 'en') {
      final englishTranslations = _translations['en'];
      if (englishTranslations != null && englishTranslations.containsKey(key)) {
        return _interpolate(englishTranslations[key]!, params);
      }
    }

    // Return key if no translation found
    debugPrint('Translation not found for key: $key');
    return key;
  }

  // Get plural translation
  String translatePlural(String key, int count, {Map<String, dynamic>? params}) {
    final pluralKey = _getPluralKey(key, count);
    final translatedParams = {...?params, 'count': count};
    return translate(pluralKey, params: translatedParams);
  }

  // Add custom translation
  Future<void> addCustomTranslation(String key, String value) async {
    final customTranslations = Map<String, String>.from(_currentConfig.customTranslations);
    customTranslations[key] = value;
    
    final newConfig = _currentConfig.copyWith(customTranslations: customTranslations);
    await _updateLocaleConfig(newConfig);
  }

  // Remove custom translation
  Future<void> removeCustomTranslation(String key) async {
    final customTranslations = Map<String, String>.from(_currentConfig.customTranslations);
    customTranslations.remove(key);
    
    final newConfig = _currentConfig.copyWith(customTranslations: customTranslations);
    await _updateLocaleConfig(newConfig);
  }

  // Clear all custom translations
  Future<void> clearCustomTranslations() async {
    final newConfig = _currentConfig.copyWith(customTranslations: {});
    await _updateLocaleConfig(newConfig);
  }

  // Get available translations for a key
  Map<String, String> getAvailableTranslations(String key) {
    final result = <String, String>{};
    
    for (final entry in _translations.entries) {
      final languageCode = entry.key;
      final translations = entry.value;
      
      if (translations.containsKey(key)) {
        result[languageCode] = translations[key]!;
      }
    }
    
    return result;
  }

  // Check if translation exists
  bool hasTranslation(String key, [String? languageCode]) {
    languageCode ??= _currentConfig.language.code;
    
    // Check custom translations
    if (_currentConfig.customTranslations.containsKey(key)) {
      return true;
    }
    
    // Check loaded translations
    final languageTranslations = _translations[languageCode];
    return languageTranslations?.containsKey(key) ?? false;
  }

  // Get all translation keys
  Set<String> getAllTranslationKeys() {
    final keys = <String>{};
    
    for (final translations in _translations.values) {
      keys.addAll(translations.keys);
    }
    
    keys.addAll(_currentConfig.customTranslations.keys);
    return keys;
  }

  // Export translations
  Map<String, dynamic> exportTranslations() {
    return {
      'locale': _currentConfig.toJson(),
      'translations': _translations,
      'customTranslations': _currentConfig.customTranslations,
    };
  }

  // Import translations
  Future<void> importTranslations(Map<String, dynamic> data) async {
    try {
      if (data.containsKey('translations')) {
        _translations = Map<String, Map<String, String>>.from(
          data['translations'].map((key, value) => 
            MapEntry(key, Map<String, String>.from(value))
          )
        );
      }
      
      if (data.containsKey('locale')) {
        final localeConfig = LocaleConfig.fromJson(data['locale']);
        await _updateLocaleConfig(localeConfig);
      }
    } catch (e) {
      debugPrint('Error importing translations: $e');
    }
  }

  // Private methods
  Future<void> _updateLocaleConfig(LocaleConfig newConfig) async {
    _currentConfig = newConfig;
    await _saveLocaleConfig();
    await _loadTranslationsForLanguage(_currentConfig.language.code);
    _localeController.add(_currentConfig);
  }

  Future<void> _loadSavedLocale() async {
    try {
      final localeJson = _prefs?.getString(_localeKey);
      if (localeJson != null) {
        final localeMap = json.decode(localeJson) as Map<String, dynamic>;
        _currentConfig = LocaleConfig.fromJson(localeMap);
      } else if (_shouldUseSystemLocale()) {
        await useSystemLocale();
      }
    } catch (e) {
      debugPrint('Error loading saved locale: $e');
    }
  }

  Future<void> _saveLocaleConfig() async {
    try {
      final localeJson = json.encode(_currentConfig.toJson());
      await _prefs?.setString(_localeKey, localeJson);
    } catch (e) {
      debugPrint('Error saving locale config: $e');
    }
  }

  Future<void> _loadTranslations() async {
    // Load translations for current language
    await _loadTranslationsForLanguage(_currentConfig.language.code);
    
    // Load English as fallback if needed
    if (_currentConfig.fallbackToEnglish && _currentConfig.language.code != 'en') {
      await _loadTranslationsForLanguage('en');
    }
  }

  Future<void> _loadTranslationsForLanguage(String languageCode) async {
    try {
      final translationsPath = 'assets/translations/$languageCode.json';
      final translationsString = await rootBundle.loadString(translationsPath);
      final translationsMap = json.decode(translationsString) as Map<String, dynamic>;
      
      _translations[languageCode] = Map<String, String>.from(
        _flattenTranslations(translationsMap)
      );
      
      debugPrint('Loaded translations for $languageCode: ${_translations[languageCode]?.length} keys');
    } catch (e) {
      debugPrint('Error loading translations for $languageCode: $e');
      
      // Create empty translations map if file doesn't exist
      _translations[languageCode] = {};
    }
  }

  Map<String, String> _flattenTranslations(Map<String, dynamic> nested, [String prefix = '']) {
    final flattened = <String, String>{};
    
    nested.forEach((key, value) {
      final fullKey = prefix.isEmpty ? key : '$prefix.$key';
      
      if (value is Map<String, dynamic>) {
        flattened.addAll(_flattenTranslations(value, fullKey));
      } else if (value is String) {
        flattened[fullKey] = value;
      }
    });
    
    return flattened;
  }

  String _interpolate(String template, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) return template;
    
    String result = template;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value.toString());
    });
    
    return result;
  }

  String _getPluralKey(String key, int count) {
    final language = _currentConfig.language.code;
    
    // Simple plural rules - can be extended for more complex languages
    switch (language) {
      case 'ar':
        // Arabic has complex plural rules
        if (count == 0) return '${key}_zero';
        if (count == 1) return '${key}_one';
        if (count == 2) return '${key}_two';
        if (count >= 3 && count <= 10) return '${key}_few';
        if (count >= 11 && count <= 99) return '${key}_many';
        return '${key}_other';
      
      case 'en':
      default:
        // English plural rules
        if (count == 1) return '${key}_one';
        return '${key}_other';
    }
  }

  Locale _getSystemLocale() {
    return PlatformDispatcher.instance.locale;
  }

  bool _shouldUseSystemLocale() {
    return _currentConfig.useSystemLocale;
  }

  // Dispose resources
  void dispose() {
    _localeController.close();
  }
}

// Extension for easy translation access
extension TranslationExtension on String {
  String tr({Map<String, dynamic>? params}) {
    return TranslationService().translate(this, params: params);
  }
  
  String trPlural(int count, {Map<String, dynamic>? params}) {
    return TranslationService().translatePlural(this, count, params: params);
  }
  
  bool get hasTranslation {
    return TranslationService().hasTranslation(this);
  }
}
