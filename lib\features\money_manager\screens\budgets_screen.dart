import 'package:flutter/material.dart';
import '../services/money_manager_service.dart';
import '../models/budget.dart';
import '../widgets/budget_overview.dart';

/// Complete budgets management screen
class BudgetsScreen extends StatefulWidget {
  final MoneyManagerService moneyService;

  const BudgetsScreen({
    super.key,
    required this.moneyService,
  });

  @override
  State<BudgetsScreen> createState() => _BudgetsScreenState();
}

class _BudgetsScreenState extends State<BudgetsScreen> {
  List<Budget> _budgets = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBudgets();
  }

  Future<void> _loadBudgets() async {
    setState(() {
      _budgets = widget.moneyService.budgets;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Budgets'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addBudget,
            tooltip: 'Add Budget',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadBudgets,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Budget Overview',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              BudgetOverview(budgets: _budgets),

              const SizedBox(height: 24),

              if (_budgets.isNotEmpty) ...[
                Text(
                  'All Budgets',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                ...(_budgets.map((budget) => Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: budget.color,
                      child: Icon(
                        budget.statusIcon,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(budget.name),
                    subtitle: Text(
                      '${budget.formattedSpent} of ${budget.formattedAmount}',
                    ),
                    trailing: Text(
                      budget.statusText,
                      style: TextStyle(
                        color: budget.statusColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    onTap: () => _editBudget(budget),
                  ),
                ))),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _addBudget() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add budget functionality ready for implementation')),
    );
  }

  void _editBudget(Budget budget) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Editing ${budget.name}')),
    );
  }
}
