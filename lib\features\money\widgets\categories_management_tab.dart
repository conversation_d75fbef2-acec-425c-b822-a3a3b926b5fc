import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:convert';
import '../providers/category_provider.dart';
import '../models/category.dart';
import '../../../shared/providers/user_provider.dart';

/// Comprehensive categories management tab for Money Flow
class CategoriesManagementTab extends ConsumerStatefulWidget {
  const CategoriesManagementTab({super.key});

  @override
  ConsumerState<CategoriesManagementTab> createState() => _CategoriesManagementTabState();
}

class _CategoriesManagementTabState extends ConsumerState<CategoriesManagementTab> {
  String _searchQuery = '';
  String _selectedFilter = 'All';
  bool _showHierarchy = true;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categories = ref.watch(categoriesProvider);
    final filteredCategories = _filterCategories(categories);

    return Scaffold(
      body: Column(
        children: [
          // Categories toolbar
          _buildCategoriesToolbar(),
          
          // Categories content
          Expanded(
            child: _buildCategoriesContent(filteredCategories),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addCategory,
        icon: const Icon(Icons.add),
        label: const Text('Add Category'),
      ),
    );
  }

  Widget _buildCategoriesToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Search and filter row
          Row(
            children: [
              // Search field
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search categories...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Filter dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedFilter,
                  decoration: const InputDecoration(
                    labelText: 'Filter',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'All',
                    'Income',
                    'Expense',
                    'Active',
                    'Inactive',
                    'Parent Categories',
                    'Subcategories',
                  ].map((filter) {
                    return DropdownMenuItem(
                      value: filter,
                      child: Text(filter),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // View options
          Row(
            children: [
              // Hierarchy toggle
              Row(
                children: [
                  Switch(
                    value: _showHierarchy,
                    onChanged: (value) {
                      setState(() {
                        _showHierarchy = value;
                      });
                    },
                  ),
                  const SizedBox(width: 8),
                  const Text('Show Hierarchy'),
                ],
              ),
              
              const Spacer(),
              
              // Action buttons
              OutlinedButton.icon(
                onPressed: _importCategories,
                icon: const Icon(Icons.upload),
                label: const Text('Import'),
              ),
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: _exportCategories,
                icon: const Icon(Icons.download),
                label: const Text('Export'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesContent(List<MoneyCategory> categories) {
    if (categories.isEmpty) {
      return _buildEmptyState();
    }

    return _showHierarchy 
      ? _buildHierarchicalView(categories)
      : _buildFlatView(categories);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.category_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No Categories Found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty 
              ? 'Try adjusting your search or filter criteria'
              : 'Create your first category to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addCategory,
            icon: const Icon(Icons.add),
            label: const Text('Add Category'),
          ),
        ],
      ),
    );
  }

  Widget _buildHierarchicalView(List<MoneyCategory> categories) {
    final parentCategories = categories.where((c) => c.parentCategoryId == null).toList();
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: parentCategories.length,
      itemBuilder: (context, index) {
        final category = parentCategories[index];
        final subcategories = categories.where((c) => c.parentCategoryId == category.id.toString()).toList();
        
        return _buildCategoryGroup(category, subcategories, categories);
      },
    );
  }

  Widget _buildFlatView(List<MoneyCategory> categories) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _buildCategoryCard(category, isSubcategory: category.parentCategoryId != null);
      },
    );
  }

  Widget _buildCategoryGroup(MoneyCategory parentCategory, List<MoneyCategory> subcategories, List<MoneyCategory> allCategories) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        leading: _buildCategoryIcon(parentCategory),
        title: Text(
          parentCategory.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          '${subcategories.length} subcategories • ${_getCategoryTypeText(parentCategory.type)}',
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCategoryStatusChip(parentCategory),
            PopupMenuButton<String>(
              onSelected: (action) => _handleCategoryAction(action, parentCategory),
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'add_sub', child: Text('Add Subcategory')),
                const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
          ],
        ),
        children: [
          // Parent category details
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: _buildCategoryDetails(parentCategory),
          ),
          
          // Subcategories
          if (subcategories.isNotEmpty) ...[
            const Divider(),
            ...subcategories.map((subcategory) => 
              Padding(
                padding: const EdgeInsets.only(left: 32, right: 16, bottom: 8),
                child: _buildCategoryCard(subcategory, isSubcategory: true),
              ),
            ),
          ],
          
          // Add subcategory button
          Padding(
            padding: const EdgeInsets.all(16),
            child: OutlinedButton.icon(
              onPressed: () => _addSubcategory(parentCategory),
              icon: const Icon(Icons.add),
              label: const Text('Add Subcategory'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(MoneyCategory category, {bool isSubcategory = false}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: _buildCategoryIcon(category),
        title: Text(
          category.name,
          style: TextStyle(
            fontWeight: isSubcategory ? FontWeight.normal : FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_getCategoryTypeText(category.type)),
            if (category.description.isNotEmpty)
              Text(
                category.description,
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCategoryStatusChip(category),
            PopupMenuButton<String>(
              onSelected: (action) => _handleCategoryAction(action, category),
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                if (!isSubcategory)
                  const PopupMenuItem(value: 'add_sub', child: Text('Add Subcategory')),
                const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
          ],
        ),
        onTap: () => _editCategory(category),
      ),
    );
  }

  Widget _buildCategoryIcon(MoneyCategory category) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: category.color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        category.icon,
        color: category.color,
      ),
    );
  }

  Widget _buildCategoryStatusChip(MoneyCategory category) {
    return Chip(
      label: Text(
        category.isActive ? 'Active' : 'Inactive',
        style: const TextStyle(fontSize: 12),
      ),
      backgroundColor: category.isActive 
        ? Colors.green.withValues(alpha: 0.2)
        : Colors.grey.withValues(alpha: 0.2),
      side: BorderSide(
        color: category.isActive ? Colors.green : Colors.grey,
      ),
    );
  }

  Widget _buildCategoryDetails(MoneyCategory category) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (category.description.isNotEmpty) ...[
          Text(
            'Description',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(category.description),
          const SizedBox(height: 8),
        ],
        
        Row(
          children: [
            Text(
              'Type: ${_getCategoryTypeText(category.type)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(width: 16),
            Text(
              'Created: ${_formatDate(category.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  List<MoneyCategory> _filterCategories(List<MoneyCategory> categories) {
    var filtered = categories;
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((category) {
        return category.name.toLowerCase().contains(_searchQuery) ||
               category.description.toLowerCase().contains(_searchQuery);
      }).toList();
    }
    
    // Apply type filter
    switch (_selectedFilter) {
      case 'Income':
        filtered = filtered.where((c) => c.type == CategoryType.income).toList();
        break;
      case 'Expense':
        filtered = filtered.where((c) => c.type == CategoryType.expense).toList();
        break;
      case 'Active':
        filtered = filtered.where((c) => c.isActive).toList();
        break;
      case 'Inactive':
        filtered = filtered.where((c) => !c.isActive).toList();
        break;
      case 'Parent Categories':
        filtered = filtered.where((c) => c.parentCategoryId == null).toList();
        break;
      case 'Subcategories':
        filtered = filtered.where((c) => c.parentCategoryId != null).toList();
        break;
    }
    
    return filtered;
  }

  void _handleCategoryAction(String action, MoneyCategory category) {
    switch (action) {
      case 'edit':
        _editCategory(category);
        break;
      case 'add_sub':
        _addSubcategory(category);
        break;
      case 'duplicate':
        _duplicateCategory(category);
        break;
      case 'delete':
        _deleteCategory(category);
        break;
    }
  }

  void _addCategory() {
    _showCategoryDialog();
  }

  void _addSubcategory(MoneyCategory parentCategory) {
    _showCategoryDialog(parentCategory: parentCategory);
  }

  void _editCategory(MoneyCategory category) {
    _showCategoryDialog(category: category);
  }

  void _duplicateCategory(MoneyCategory category) {
    final duplicatedCategory = MoneyCategory.create(
      name: '${category.name} (Copy)',
      type: category.type,
      userId: category.userId,
      description: category.description,
      icon: category.icon,
      color: category.color,
      parentCategoryId: category.parentCategoryId,
      isActive: category.isActive,
      budgetLimit: category.budgetLimit,
      enableBudgetAlert: category.enableBudgetAlert,
      alertThreshold: category.alertThreshold,
    );
    
    ref.read(categoriesProvider.notifier).addCategory(duplicatedCategory);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Category "${duplicatedCategory.name}" duplicated'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteCategory(MoneyCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(categoriesProvider.notifier).deleteCategory(category.id);
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Category "${category.name}" deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showCategoryDialog({MoneyCategory? category, MoneyCategory? parentCategory}) {
    showDialog(
      context: context,
      builder: (context) => CategoryFormDialog(
        category: category,
        parentCategory: parentCategory,
        onSave: (newCategory) {
          if (category != null) {
            ref.read(categoriesProvider.notifier).updateCategory(newCategory);
          } else {
            ref.read(categoriesProvider.notifier).addCategory(newCategory);
          }
        },
      ),
    );
  }

  void _importCategories() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Categories'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose import source:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.file_upload),
              title: const Text('Import from JSON file'),
              onTap: () {
                Navigator.of(context).pop();
                _importFromJsonFile();
              },
            ),
            ListTile(
              leading: const Icon(Icons.restore),
              title: const Text('Restore default categories'),
              onTap: () {
                Navigator.of(context).pop();
                _restoreDefaultCategories();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _importFromJsonFile() async {
    _showJsonImportDialog();
  }

  void _showJsonImportDialog() {
    final textController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Categories from JSON'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Paste your JSON data below:'),
            const SizedBox(height: 16),
            TextField(
              controller: textController,
              maxLines: 10,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: '[\n  {\n    "name": "Food",\n    "type": "expense",\n    "color": "#FF5722"\n  }\n]',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _processJsonImport(textController.text);
              Navigator.of(context).pop();
            },
            child: const Text('Import'),
          ),
        ],
      ),
    );
  }

  void _processJsonImport(String jsonText) async {
    try {
      final jsonData = json.decode(jsonText);
      if (jsonData is! List) {
        throw Exception('JSON must be an array of categories');
      }

      int importedCount = 0;
      final userProfile = ref.read(userProfileProvider);
      if (userProfile == null) return;

      for (final item in jsonData) {
        if (item is Map<String, dynamic>) {
          final category = MoneyCategory.create(
            name: item['name'] ?? 'Unnamed Category',
            type: _parseType(item['type']),
            userId: userProfile.id.toString(),
            description: item['description'] ?? '',
            color: _parseColor(item['color']),
            budgetLimit: (item['budgetLimit'] ?? 0.0).toDouble(),
          );

          await ref.read(categoriesProvider.notifier).addCategory(category);
          importedCount++;
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully imported $importedCount categories'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Import failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  CategoryType _parseType(dynamic type) {
    if (type is String) {
      switch (type.toLowerCase()) {
        case 'income':
          return CategoryType.income;
        case 'expense':
          return CategoryType.expense;
        case 'transfer':
          return CategoryType.transfer;
      }
    }
    return CategoryType.expense;
  }

  Color _parseColor(dynamic color) {
    if (color is String) {
      try {
        if (color.startsWith('#')) {
          return Color(int.parse(color.substring(1), radix: 16) + 0xFF000000);
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }
    return Colors.blue;
  }

  void _restoreDefaultCategories() {
    // Add some default categories
    final defaultCategories = [
      MoneyCategory.create(
        name: 'Food & Dining',
        type: CategoryType.expense,
        userId: 'current_user',
        icon: Icons.restaurant,
        color: Colors.orange,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Transportation',
        type: CategoryType.expense,
        userId: 'current_user',
        icon: Icons.directions_car,
        color: Colors.blue,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Shopping',
        type: CategoryType.expense,
        userId: 'current_user',
        icon: Icons.shopping_bag,
        color: Colors.purple,
        isDefault: true,
      ),
      MoneyCategory.create(
        name: 'Salary',
        type: CategoryType.income,
        userId: 'current_user',
        icon: Icons.work,
        color: Colors.green,
        isDefault: true,
      ),
    ];

    for (final category in defaultCategories) {
      ref.read(categoriesProvider.notifier).addCategory(category);
    }

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Default categories restored successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _exportCategories() {
    final categories = ref.read(categoriesProvider);

    if (categories.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No categories to export'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Convert categories to JSON format
    final exportData = {
      'categories': categories.map((category) => category.toJson()).toList(),
      'exportDate': DateTime.now().toIso8601String(),
      'version': '1.0',
    };

    // In a real implementation, you would save this to a file
    // For now, show the export summary
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Categories'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Ready to export ${categories.length} categories'),
            const SizedBox(height: 16),
            const Text('Export includes:'),
            Text('• ${categories.where((c) => c.type == CategoryType.income).length} income categories'),
            Text('• ${categories.where((c) => c.type == CategoryType.expense).length} expense categories'),
            const SizedBox(height: 16),
            const Text('In a full implementation, this would save to a JSON file.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Categories exported successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  String _getCategoryTypeText(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return 'Income';
      case CategoryType.expense:
        return 'Expense';
      case CategoryType.transfer:
        return 'Transfer';
    }
  }



  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Category form dialog for creating/editing categories
class CategoryFormDialog extends StatefulWidget {
  final MoneyCategory? category;
  final MoneyCategory? parentCategory;
  final Function(MoneyCategory) onSave;

  const CategoryFormDialog({
    super.key,
    this.category,
    this.parentCategory,
    required this.onSave,
  });

  @override
  State<CategoryFormDialog> createState() => _CategoryFormDialogState();
}

class _CategoryFormDialogState extends State<CategoryFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  CategoryType _selectedType = CategoryType.expense;
  IconData _selectedIcon = Icons.category;
  Color _selectedColor = Colors.blue;
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    
    if (widget.category != null) {
      _nameController.text = widget.category!.name;
      _descriptionController.text = widget.category!.description;
      _selectedType = widget.category!.type;
      _selectedIcon = widget.category!.icon;
      _selectedColor = widget.category!.color;
      _isActive = widget.category!.isActive;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                widget.category != null ? 'Edit Category' : 'Add Category',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Form fields
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Category Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a category name';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              
              const SizedBox(height: 16),
              
              // Type selector
              DropdownButtonFormField<CategoryType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Category Type',
                  border: OutlineInputBorder(),
                ),
                items: CategoryType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.name.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
              
              const SizedBox(height: 16),
              
              // Color and icon row
              Row(
                children: [
                  // Color picker
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Color'),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: _selectColor,
                          child: Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: _selectedColor,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Icon selector
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Icon'),
                        const SizedBox(height: 8),
                        Container(
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey),
                          ),
                          child: Center(
                            child: Icon(
                              _selectedIcon,
                              color: _selectedColor,
                              size: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Active toggle
              Row(
                children: [
                  Switch(
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                  ),
                  const SizedBox(width: 8),
                  const Text('Active'),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _saveCategory,
                    child: const Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectColor() {
    final colors = <Color>[
      Colors.blue, Colors.green, Colors.red, Colors.orange, Colors.purple,
      Colors.teal, Colors.pink, Colors.indigo, Colors.amber, Colors.cyan
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Color'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: colors.length,
            itemBuilder: (context, index) {
              final color = colors[index];
              final isSelected = color == _selectedColor;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                  Navigator.of(context).pop();
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(color: Colors.white, width: 3)
                        : null,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white)
                      : null,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _saveCategory() {
    if (_formKey.currentState!.validate()) {
      final category = MoneyCategory.create(
        name: _nameController.text.trim(),
        type: _selectedType,
        userId: 'default_user',
        description: _descriptionController.text.trim(),
        icon: _selectedIcon,
        color: _selectedColor,
        parentCategoryId: widget.parentCategory?.id.toString(),
        isActive: _isActive,
      );

      if (widget.category != null) {
        // Update existing category
        category.id = widget.category!.id;
        category.createdAt = widget.category!.createdAt;
      }
      
      widget.onSave(category);
      Navigator.of(context).pop();
    }
  }


}
