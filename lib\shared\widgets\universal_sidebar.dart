import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/user_provider.dart';
import '../../core/navigation/app_router.dart';

class UniversalSidebar extends ConsumerWidget {
  final String? currentRoute;
  final bool isCompact;

  const UniversalSidebar({
    super.key,
    this.currentRoute,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfile = ref.watch(userProfileProvider);
    final currentPath = GoRouter.of(context).routerDelegate.currentConfiguration.uri.path;

    if (isCompact) {
      return _buildNavigationRail(context, currentPath);
    } else {
      return _buildDrawer(context, userProfile, currentPath);
    }
  }

  Widget _buildNavigationRail(BuildContext context, String currentPath) {
    return NavigationRail(
      selectedIndex: _getSelectedIndex(currentPath),
      onDestinationSelected: (index) => _navigateToIndex(context, index),
      labelType: NavigationRailLabelType.all,
      backgroundColor: Theme.of(context).colorScheme.surface,
      destinations: [
        const NavigationRailDestination(
          icon: Icon(Icons.dashboard),
          label: Text('Dashboard'),
        ),
        const NavigationRailDestination(
          icon: Icon(Icons.note_alt_outlined),
          label: Text('Memo'),
        ),
        const NavigationRailDestination(
          icon: Icon(Icons.mosque_outlined),
          label: Text('Islami'),
        ),
        const NavigationRailDestination(
          icon: Icon(Icons.build_outlined),
          label: Text('Tools'),
        ),
        const NavigationRailDestination(
          icon: Icon(Icons.account_balance_wallet_outlined),
          label: Text('Money'),
        ),
        const NavigationRailDestination(
          icon: Icon(Icons.settings_outlined),
          label: Text('Settings'),
        ),
      ],
    );
  }

  Widget _buildDrawer(BuildContext context, userProfile, String currentPath) {
    return Drawer(
      child: Column(
        children: [
          // User Profile Header
          UserAccountsDrawerHeader(
            accountName: Text(userProfile?.name ?? 'Guest User'),
            accountEmail: Text(userProfile?.email ?? 'No email'),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: userProfile?.avatarPath != null
                  ? ClipOval(
                      child: Image.asset(
                        userProfile!.avatarPath!,
                        fit: BoxFit.cover,
                        width: 72,
                        height: 72,
                      ),
                    )
                  : Text(
                      userProfile?.name.isNotEmpty == true
                          ? userProfile!.name[0].toUpperCase()
                          : 'G',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
            ),
          ),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  route: AppRoutes.dashboard,
                  currentPath: currentPath,
                ),
                const Divider(),
                
                // Mini Apps Section
                _buildSectionHeader(context, 'Mini Apps'),
                _buildDrawerItem(
                  context,
                  icon: Icons.note_alt_outlined,
                  title: 'Memo Suite',
                  subtitle: 'Notes, todos & voice memos',
                  route: AppRoutes.memo,
                  currentPath: currentPath,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.mosque_outlined,
                  title: 'Islami',
                  subtitle: 'Islamic remembrance',
                  route: AppRoutes.islami,
                  currentPath: currentPath,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.build_outlined,
                  title: 'Tools Builder',
                  subtitle: 'Custom calculators',
                  route: AppRoutes.tools,
                  currentPath: currentPath,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.account_balance_wallet_outlined,
                  title: 'Money Flow',
                  subtitle: 'Financial management',
                  route: AppRoutes.money,
                  currentPath: currentPath,
                ),
                const Divider(),
                
                // System Section
                _buildSectionHeader(context, 'System'),
                _buildDrawerItem(
                  context,
                  icon: Icons.settings_outlined,
                  title: 'Settings',
                  subtitle: 'App preferences',
                  route: AppRoutes.settings,
                  currentPath: currentPath,
                ),
              ],
            ),
          ),

          // Footer with app version
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Divider(),
                Text(
                  'ShadowSuite v1.0.0',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Production Ready',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    required String route,
    required String currentPath,
    bool isComingSoon = false,
  }) {
    final isSelected = _isRouteSelected(currentPath, route);

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected 
            ? Theme.of(context).colorScheme.primary 
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected 
              ? Theme.of(context).colorScheme.primary 
              : Theme.of(context).colorScheme.onSurface,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: TextStyle(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.7)
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      selected: isSelected,
      selectedTileColor: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
      onTap: () {
        if (isComingSoon) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$title is coming soon!'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else {
          context.go(route);
          Navigator.of(context).pop(); // Close drawer
        }
      },
    );
  }

  bool _isRouteSelected(String currentPath, String route) {
    if (route == AppRoutes.dashboard) {
      return currentPath == route;
    }
    return currentPath.startsWith(route);
  }

  int _getSelectedIndex(String currentPath) {
    if (currentPath == AppRoutes.dashboard) return 0;
    if (currentPath.startsWith(AppRoutes.memo)) return 1;
    if (currentPath.startsWith(AppRoutes.islami)) return 2;
    if (currentPath.startsWith(AppRoutes.tools)) return 3;
    if (currentPath.startsWith(AppRoutes.money)) return 4;
    if (currentPath.startsWith(AppRoutes.settings)) return 5;
    return 0;
  }

  void _navigateToIndex(BuildContext context, int index) {
    final routes = [
      AppRoutes.dashboard,
      AppRoutes.memo,
      AppRoutes.islami,
      AppRoutes.tools,
      AppRoutes.money,
      AppRoutes.settings,
    ];
    
    if (index < routes.length) {
      context.go(routes[index]);
    }
  }
}

// Helper widget for responsive sidebar
class ResponsiveSidebar extends ConsumerWidget {
  final Widget child;
  final String? currentRoute;

  const ResponsiveSidebar({
    super.key,
    required this.child,
    this.currentRoute,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;
    final isDesktop = screenWidth >= 1024;

    if (isDesktop) {
      return Row(
        children: [
          UniversalSidebar(
            currentRoute: currentRoute,
            isCompact: false,
          ),
          Expanded(child: child),
        ],
      );
    } else if (isTablet) {
      return Row(
        children: [
          UniversalSidebar(
            currentRoute: currentRoute,
            isCompact: true,
          ),
          Expanded(child: child),
        ],
      );
    } else {
      return Scaffold(
        drawer: UniversalSidebar(
          currentRoute: currentRoute,
          isCompact: false,
        ),
        body: child,
      );
    }
  }
}
