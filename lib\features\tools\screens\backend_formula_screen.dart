import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/formula_engine_service.dart';
import '../services/excel_import_export_service.dart';
import '../models/spreadsheet_cell.dart';
import 'tool_save_screen.dart';

/// Excel-like spreadsheet interface for building backend logic
class BackendFormulaScreen extends ConsumerStatefulWidget {
  final int toolId;

  const BackendFormulaScreen({
    super.key,
    required this.toolId,
  });

  @override
  ConsumerState<BackendFormulaScreen> createState() => _BackendFormulaScreenState();
}

class _BackendFormulaScreenState extends ConsumerState<BackendFormulaScreen> {
  final _formulaController = TextEditingController();
  String? _selectedCell;
  final Map<String, SpreadsheetCell> _cellData = {};
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final ScrollController _headerHorizontalController = ScrollController();
  final ScrollController _headerVerticalController = ScrollController();

  // Enhanced cell selection features
  Set<String> _selectedCells = {};
  bool _isSelecting = false;
  String? _selectionStart;
  bool _isEditingFormula = false;
  final FocusNode _formulaFocusNode = FocusNode();

  // Formula engine and highlighting
  late final FormulaEngineService _formulaEngine;
  Set<String> _highlightedCells = {};
  List<FormulaAutoComplete> _suggestions = [];
  bool _showSuggestions = false;

  // Freeze panes
  bool _freezePanes = false;
  int _frozenRows = 1;
  int _frozenCols = 1;

  // Undo/Redo functionality
  final List<Map<String, SpreadsheetCell>> _undoStack = [];
  final List<Map<String, SpreadsheetCell>> _redoStack = [];
  bool _hasUnsavedChanges = false;

  // Dynamic matrix size configuration
  int _matrixRows = 50;
  int _matrixCols = 26;
  bool _directDataEntry = true;

  // Keyboard navigation
  bool _isEditingCell = false;
  String? _editingCell;

  @override
  void initState() {
    super.initState();
    _formulaEngine = FormulaEngineService();

    // Synchronize scroll controllers
    _horizontalController.addListener(_syncHorizontalScroll);
    _verticalController.addListener(_syncVerticalScroll);
  }

  @override
  void dispose() {
    _formulaController.dispose();
    _horizontalController.dispose();
    _verticalController.dispose();
    _headerHorizontalController.dispose();
    _headerVerticalController.dispose();
    super.dispose();
  }

  void _syncHorizontalScroll() {
    if (_headerHorizontalController.hasClients) {
      _headerHorizontalController.jumpTo(_horizontalController.offset);
    }
  }

  void _syncVerticalScroll() {
    if (_headerVerticalController.hasClients) {
      _headerVerticalController.jumpTo(_verticalController.offset);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backend Formula Engine'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        leading: IconButton(
          onPressed: _handleBackNavigation,
          icon: const Icon(Icons.arrow_back),
          tooltip: 'Back',
        ),
        actions: [
          IconButton(
            onPressed: _configureMatrixSize,
            icon: const Icon(Icons.grid_3x3),
            tooltip: 'Configure Matrix Size',
          ),
          IconButton(
            onPressed: _toggleFreezePanes,
            icon: Icon(_freezePanes ? Icons.lock : Icons.lock_open),
            tooltip: _freezePanes ? 'Unfreeze Panes' : 'Freeze Panes',
          ),
          IconButton(
            onPressed: _showCellFormatting,
            icon: const Icon(Icons.format_paint),
            tooltip: 'Cell Formatting',
          ),
          IconButton(
            onPressed: _undoLastAction,
            icon: const Icon(Icons.undo),
            tooltip: 'Undo',
          ),
          IconButton(
            onPressed: _redoLastAction,
            icon: const Icon(Icons.redo),
            tooltip: 'Redo',
          ),
          IconButton(
            onPressed: _loadSampleData,
            icon: const Icon(Icons.data_array),
            tooltip: 'Load Sample Data',
          ),
          IconButton(
            onPressed: _saveBackend,
            icon: const Icon(Icons.save),
            tooltip: 'Save Backend',
          ),
          IconButton(
            onPressed: () => context.go('/tools/ui-builder/${widget.toolId}'),
            icon: const Icon(Icons.design_services),
            tooltip: 'Go to UI Builder',
          ),
        ],
      ),
      body: Column(
        children: [
          // Formula Bar
          _buildFormulaBar(),
          
          // Spreadsheet Grid
          Expanded(
            child: _buildSpreadsheetGrid(),
          ),
          
          // Bottom Actions
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildFormulaBar() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
      ),
      child: Row(
        children: [
          // Cell Address
          Container(
            width: 80,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                _selectedCell ?? 'A1',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Enhanced Formula Input with Auto-Complete
          Expanded(
            child: Column(
              children: [
                TextField(
                  controller: _formulaController,
                  focusNode: _formulaFocusNode,
                  decoration: InputDecoration(
                    hintText: _isEditingFormula
                        ? 'Click cells to add to formula...'
                        : 'Enter formula (e.g., =A1+B1)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(4),
                      borderSide: BorderSide(
                        color: _isEditingFormula
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: _isEditingFormula ? 2 : 1,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    prefixIcon: Icon(
                      Icons.functions,
                      color: _isEditingFormula
                          ? Theme.of(context).colorScheme.primary
                          : null,
                    ),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_isEditingFormula) ...[
                          IconButton(
                            onPressed: _confirmFormula,
                            icon: const Icon(Icons.check, color: Colors.green),
                            tooltip: 'Confirm Formula',
                            iconSize: 20,
                          ),
                          IconButton(
                            onPressed: _cancelFormula,
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'Cancel Formula',
                            iconSize: 20,
                          ),
                        ] else ...[
                          IconButton(
                            onPressed: _showFormulaSyntaxHighlighting,
                            icon: const Icon(Icons.highlight),
                            tooltip: 'Syntax Highlighting',
                            iconSize: 20,
                          ),
                          IconButton(
                            onPressed: _validateCurrentFormula,
                            icon: const Icon(Icons.check_circle_outline),
                            tooltip: 'Validate Formula',
                            iconSize: 20,
                          ),
                        ],
                      ],
                    ),
                  ),
                  onSubmitted: _onFormulaSubmitted,
                  onChanged: _onFormulaChanged,
                ),

                // Auto-complete suggestions
                if (_showSuggestions && _suggestions.isNotEmpty)
                  Container(
                    height: 120,
                    margin: const EdgeInsets.only(top: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      border: Border.all(color: Theme.of(context).colorScheme.outline),
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListView.builder(
                      itemCount: _suggestions.length,
                      itemBuilder: (context, index) {
                        final suggestion = _suggestions[index];
                        return ListTile(
                          dense: true,
                          leading: Icon(
                            Icons.functions,
                            size: 16,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          title: Text(
                            suggestion.name,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          subtitle: Text(
                            suggestion.description,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          trailing: Text(
                            suggestion.category,
                            style: Theme.of(context).textTheme.labelSmall,
                          ),
                          onTap: () => _insertSuggestion(suggestion),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Function Helper
          IconButton(
            onPressed: _showFunctionHelper,
            icon: const Icon(Icons.help_outline),
            tooltip: 'Function Helper',
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetGrid() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Column(
        children: [
          // Column Headers
          _buildColumnHeaders(),
          
          // Grid Content
          Expanded(
            child: Row(
              children: [
                // Row Headers
                _buildRowHeaders(),
                
                // Cell Grid
                Expanded(
                  child: _buildCellGrid(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeaders() {
    return Container(
      height: 40,
      child: Row(
        children: [
          // Corner cell
          Container(
            width: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant,
              border: Border.all(color: Theme.of(context).colorScheme.outline),
            ),
          ),
          
          // Column letters
          Expanded(
            child: Scrollbar(
              controller: _horizontalController,
              scrollbarOrientation: ScrollbarOrientation.bottom,
              child: SingleChildScrollView(
                controller: _horizontalController,
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: List.generate(_matrixCols, (index) {
                    final letter = _getColumnLabel(index);
                    return Container(
                      width: 100,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceVariant,
                        border: Border.all(color: Theme.of(context).colorScheme.outline),
                      ),
                      child: Center(
                        child: Text(
                          letter,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRowHeaders() {
    return Container(
      width: 60,
      child: Scrollbar(
        controller: _verticalController,
        child: SingleChildScrollView(
          controller: _verticalController,
          child: Column(
            children: List.generate(_matrixRows, (index) {
              final rowNumber = index + 1;
              return Container(
                width: 60,
                height: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                ),
                child: Center(
                  child: Text(
                    rowNumber.toString(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildCellGrid() {
    return Scrollbar(
      controller: _horizontalController,
      scrollbarOrientation: ScrollbarOrientation.bottom,
      child: Scrollbar(
        controller: _verticalController,
        child: SingleChildScrollView(
          controller: _horizontalController,
          scrollDirection: Axis.horizontal,
          child: SingleChildScrollView(
            controller: _verticalController,
            child: Column(
              children: List.generate(_matrixRows, (rowIndex) {
                return Row(
                  children: List.generate(_matrixCols, (colIndex) {
                    final cellAddress = '${_getColumnLabel(colIndex)}${rowIndex + 1}';
                    final isSelected = _selectedCell == cellAddress;
                    
                    final isHighlighted = _highlightedCells.contains(cellAddress);

                    return GestureDetector(
                      onTap: () => _isEditingFormula ? _addCellToFormula(cellAddress) : _selectCell(cellAddress),
                      onPanStart: (details) => _startCellSelection(cellAddress),
                      onPanUpdate: (details) => _updateCellSelection(details, cellAddress),
                      onPanEnd: (details) => _endCellSelection(),
                      child: Container(
                        width: 100,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primaryContainer
                              : isHighlighted
                                  ? Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.5)
                                  : _selectedCells.contains(cellAddress)
                                      ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
                                      : Theme.of(context).colorScheme.surface,
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : isHighlighted
                                    ? Theme.of(context).colorScheme.secondary
                                    : _selectedCells.contains(cellAddress)
                                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.7)
                                        : Theme.of(context).colorScheme.outline,
                            width: isSelected ? 2 : isHighlighted ? 1.5 : 1,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Text(
                            _getCellDisplayValue(cellAddress),
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    );
                  }),
                );
              }),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          top: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Wrap(
        spacing: 12,
        runSpacing: 8,
        children: [
          ElevatedButton.icon(
            onPressed: _importExcelFile,
            icon: const Icon(Icons.file_upload),
            label: const Text('Import Excel'),
          ),
          OutlinedButton.icon(
            onPressed: _exportToCsv,
            icon: const Icon(Icons.file_download),
            label: const Text('Export CSV'),
          ),
          ElevatedButton.icon(
            onPressed: _loadSampleData,
            icon: const Icon(Icons.data_object),
            label: const Text('Load Sample Data'),
          ),
          ElevatedButton.icon(
            onPressed: _addSampleData,
            icon: const Icon(Icons.add_box),
            label: const Text('Add Sample Data'),
          ),
          OutlinedButton.icon(
            onPressed: _clearAll,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear All'),
          ),
          const SizedBox(width: 20), // Spacer
          ElevatedButton.icon(
            onPressed: () => context.go('/tools/ui-builder/${widget.toolId}'),
            icon: const Icon(Icons.arrow_forward),
            label: const Text('Next: UI Builder'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to create a SpreadsheetCell
  SpreadsheetCell _createCell(String value, {String? formula}) {
    return SpreadsheetCell(
      address: '',
      value: value,
      formula: formula,
      type: formula != null ? CellType.formula : _getCellType(value),
      style: const CellStyle(),
    );
  }

  /// Helper method to get cell display value
  String _getCellDisplayValue(String cellAddress) {
    final cell = _cellData[cellAddress];
    if (cell == null) return '';

    // If it has a formula, show the calculated value, otherwise show the raw value
    if (cell.hasFormula) {
      // Evaluate formula and return result
      final result = _formulaEngine.evaluateFormula(cell.formula!, _cellData, cellAddress);
      return result.toString();
    }

    return cell.value?.toString() ?? '';
  }

  /// Helper method to get cell input value (formula or raw value)
  String _getCellInputValue(String cellAddress) {
    final cell = _cellData[cellAddress];
    if (cell == null) return '';

    // Return formula if it exists, otherwise return the value
    return cell.formula ?? cell.value?.toString() ?? '';
  }

  /// Helper method to determine cell type
  CellType _getCellType(dynamic value) {
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    if (value.toString().startsWith('=')) return CellType.formula;
    return CellType.text;
  }

  void _selectCell(String cellAddress) {
    setState(() {
      _selectedCell = cellAddress;
      _formulaController.text = _getCellInputValue(cellAddress);

      // Clear multi-selection if not holding Ctrl
      if (!_isSelecting) {
        _selectedCells.clear();
      }

      // Update highlighted cells based on formula dependencies
      _updateHighlightedCells(cellAddress);
    });
  }

  void _startCellSelection(String cellAddress) {
    setState(() {
      _isSelecting = true;
      _selectionStart = cellAddress;
      _selectedCells.clear();
      _selectedCells.add(cellAddress);
    });
  }

  void _updateCellSelection(DragUpdateDetails details, String currentCell) {
    if (_isSelecting && _selectionStart != null) {
      setState(() {
        _selectedCells.clear();
        _selectedCells.addAll(_getCellRange(_selectionStart!, currentCell));
      });
    }
  }

  void _endCellSelection() {
    setState(() {
      _isSelecting = false;
      _selectionStart = null;
    });
  }

  List<String> _getCellRange(String start, String end) {
    final startCoords = _parseCellAddress(start);
    final endCoords = _parseCellAddress(end);

    final minRow = math.min(startCoords['row']!, endCoords['row']!);
    final maxRow = math.max(startCoords['row']!, endCoords['row']!);
    final minCol = math.min(startCoords['col']!, endCoords['col']!);
    final maxCol = math.max(startCoords['col']!, endCoords['col']!);

    final cells = <String>[];
    for (int row = minRow; row <= maxRow; row++) {
      for (int col = minCol; col <= maxCol; col++) {
        cells.add(_getCellAddressFromCoords(row, col));
      }
    }

    return cells;
  }

  Map<String, int> _parseCellAddress(String address) {
    final regex = RegExp(r'^([A-Z]+)(\d+)$');
    final match = regex.firstMatch(address);

    if (match != null) {
      final colStr = match.group(1)!;
      final rowStr = match.group(2)!;

      int col = 0;
      for (int i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.codeUnitAt(i) - 65 + 1);
      }

      return {
        'row': int.parse(rowStr),
        'col': col,
      };
    }

    return {'row': 1, 'col': 1};
  }

  String _getCellAddressFromCoords(int row, int col) {
    String colStr = '';
    int tempCol = col;

    while (tempCol > 0) {
      tempCol--;
      colStr = String.fromCharCode(65 + (tempCol % 26)) + colStr;
      tempCol ~/= 26;
    }

    return '$colStr$row';
  }

  void _addCellToFormula(String cellAddress) {
    final currentText = _formulaController.text;
    final cursorPosition = _formulaController.selection.baseOffset;

    // Add cell reference at cursor position
    final newText = currentText.substring(0, cursorPosition) +
                   cellAddress +
                   currentText.substring(cursorPosition);

    _formulaController.text = newText;
    _formulaController.selection = TextSelection.fromPosition(
      TextPosition(offset: cursorPosition + cellAddress.length),
    );

    // Keep focus on formula input
    _formulaFocusNode.requestFocus();
  }

  void _addRangeToFormula(String startCell, String endCell) {
    final currentText = _formulaController.text;
    final cursorPosition = _formulaController.selection.baseOffset;

    final rangeText = '$startCell:$endCell';

    // Add range reference at cursor position
    final newText = currentText.substring(0, cursorPosition) +
                   rangeText +
                   currentText.substring(cursorPosition);

    _formulaController.text = newText;
    _formulaController.selection = TextSelection.fromPosition(
      TextPosition(offset: cursorPosition + rangeText.length),
    );

    // Keep focus on formula input
    _formulaFocusNode.requestFocus();
  }

  void _confirmFormula() {
    if (_selectedCell != null) {
      _updateCellValue(_selectedCell!, _formulaController.text);
      setState(() {
        _isEditingFormula = false;
      });
    }
  }

  void _cancelFormula() {
    setState(() {
      _isEditingFormula = false;
      _formulaController.text = _getCellInputValue(_selectedCell ?? 'A1');
    });
  }

  void _updateCellValue(String cellAddress, String value) {
    setState(() {
      if (value.isEmpty) {
        _cellData.remove(cellAddress);
      } else {
        final isFormula = value.startsWith('=');
        _cellData[cellAddress] = SpreadsheetCell(
          address: cellAddress,
          value: isFormula ? _formulaEngine.evaluateFormula(value, _cellData, cellAddress) : value,
          formula: isFormula ? value : null,
          type: _getCellType(value),
          style: const CellStyle(),
        );
      }

      // Update highlighted cells for the new selection
      _updateHighlightedCells(cellAddress);

      // Mark as having unsaved changes
      _hasUnsavedChanges = true;
    });
  }

  void _onFormulaChanged(String value) {
    setState(() {
      _isEditingFormula = value.startsWith('=');

      // Update suggestions based on current input
      if (_isEditingFormula) {
        _updateFormulaSuggestions(value);
      } else {
        _suggestions.clear();
        _showSuggestions = false;
      }
    });
  }



  void _updateFormulaSuggestions(String input) {
    if (!input.startsWith('=') || input.length < 2) {
      setState(() {
        _suggestions.clear();
        _showSuggestions = false;
      });
      return;
    }

    final query = input.substring(1).toUpperCase();
    final allSuggestions = _getBuiltInFunctions();

    setState(() {
      _suggestions = allSuggestions
          .where((suggestion) => suggestion.name.toUpperCase().contains(query))
          .take(10) // Show more suggestions
          .toList();
      _showSuggestions = _suggestions.isNotEmpty;
    });
  }

  void _insertSuggestion(FormulaAutoComplete suggestion) {
    final currentText = _formulaController.text;
    final cursorPosition = _formulaController.selection.baseOffset;

    // Find the start of the current function name
    int startPos = cursorPosition - 1;
    while (startPos > 0 && currentText[startPos] != '=' && currentText[startPos] != '(' && currentText[startPos] != ',') {
      startPos--;
    }
    if (currentText[startPos] == '=' || currentText[startPos] == '(' || currentText[startPos] == ',') {
      startPos++;
    }

    // Replace the partial function name with the suggestion
    final newText = currentText.substring(0, startPos) +
                   suggestion.name + '(' +
                   currentText.substring(cursorPosition);

    _formulaController.text = newText;
    _formulaController.selection = TextSelection.fromPosition(
      TextPosition(offset: startPos + suggestion.name.length + 1),
    );

    setState(() {
      _suggestions.clear();
      _showSuggestions = false;
    });

    _formulaFocusNode.requestFocus();
  }

  void _updateHighlightedCells(String cellAddress) {
    _highlightedCells.clear();

    final cell = _cellData[cellAddress];
    if (cell?.hasFormula == true) {
      // Get dependencies and highlight them
      final dependencies = _formulaEngine.getDependencies(cell!.formula!);
      _highlightedCells.addAll(dependencies);
    }
  }



  /// Show syntax highlighting dialog
  void _showFormulaSyntaxHighlighting() {
    final formula = _formulaController.text;
    if (formula.isEmpty) return;

    final tokens = _formulaEngine.getSyntaxTokens(formula);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Formula Syntax Highlighting'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Formula: $formula'),
              const SizedBox(height: 16),
              const Text('Syntax Elements:'),
              const SizedBox(height: 8),
              ...tokens.map((token) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _getTokenColor(token.type),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text('${token.text} (${token.type.name})'),
                  ],
                ),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Get color for syntax token type
  Color _getTokenColor(TokenType type) {
    switch (type) {
      case TokenType.function:
        return Colors.blue;
      case TokenType.cellReference:
        return Colors.green;
      case TokenType.operator:
        return Colors.orange;
      case TokenType.number:
        return Colors.purple;
      case TokenType.string:
        return Colors.red;
      case TokenType.error:
        return Colors.red;
    }
  }

  /// Validate current formula
  void _validateCurrentFormula() {
    final formula = _formulaController.text;
    if (formula.isEmpty) return;

    final validation = _formulaEngine.validateFormulaDetailed(formula);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          validation.isValid ? 'Formula Valid ✓' : 'Formula Errors ✗',
          style: TextStyle(
            color: validation.isValid ? Colors.green : Colors.red,
          ),
        ),
        content: validation.isValid
            ? const Text('The formula syntax is correct.')
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Errors found:'),
                  const SizedBox(height: 8),
                  ...validation.errors.map((error) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text('• $error'),
                  )),
                ],
              ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _onFormulaSubmitted(String input) {
    if (_selectedCell != null) {
      // Save state for undo
      _saveStateForUndo();

      setState(() {
        if (input.startsWith('=')) {
          // It's a formula
          _cellData[_selectedCell!] = _createCell('', formula: input);
        } else {
          // It's a value
          _cellData[_selectedCell!] = _createCell(input);
        }

        // Recalculate dependent cells
        _recalculateSheet();

        // Update highlighting
        _updateHighlightedCells(_selectedCell!);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Updated cell $_selectedCell'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  void _recalculateSheet() {
    // Simple recalculation - in production, implement proper dependency graph
    final recalculated = _formulaEngine.recalculateSheet(_cellData);
    _cellData.clear();
    _cellData.addAll(recalculated);
  }

  void _showFunctionHelper() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Excel Functions'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildFunctionItem('SUM(A1:A5)', 'Sum of range'),
              _buildFunctionItem('AVG(A1:A5)', 'Average of range'),
              _buildFunctionItem('MAX(A1:A5)', 'Maximum value'),
              _buildFunctionItem('MIN(A1:A5)', 'Minimum value'),
              _buildFunctionItem('COUNT(A1:A5)', 'Count of numbers'),
              _buildFunctionItem('IF(A1>10,"High","Low")', 'Conditional logic'),
              _buildFunctionItem('A1+B1', 'Simple addition'),
              _buildFunctionItem('A1*B1', 'Multiplication'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionItem(String formula, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              formula,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(description),
          ),
        ],
      ),
    );
  }

  void _addSampleData() {
    setState(() {
      _cellData['A1'] = _createCell('Item');
      _cellData['B1'] = _createCell('Price');
      _cellData['C1'] = _createCell('Quantity');
      _cellData['D1'] = _createCell('Total');
      _cellData['A2'] = _createCell('Apple');
      _cellData['B2'] = _createCell('1.50');
      _cellData['C2'] = _createCell('10');
      _cellData['D2'] = _createCell('', formula: '=B2*C2');
      _cellData['A3'] = _createCell('Banana');
      _cellData['B3'] = _createCell('0.75');
      _cellData['C3'] = _createCell('20');
      _cellData['D3'] = _createCell('', formula: '=B3*C3');
      _cellData['D4'] = _createCell('', formula: '=SUM(D2:D3)');

      // Recalculate formulas
      _recalculateSheet();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sample data added')),
    );
  }

  void _clearAll() {
    setState(() {
      _cellData.clear();
      _selectedCell = null;
      _formulaController.clear();
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All data cleared')),
    );
  }

  /// Import Excel file
  Future<void> _importExcelFile() async {
    try {
      final importService = ExcelImportExportService();
      final importedData = await importService.importExcelFile();

      if (importedData != null) {
        setState(() {
          _cellData.clear();
          if (importedData.cells != null) {
          _cellData.addAll(importedData.cells!);
        }
          _recalculateSheet();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Excel file imported successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to import file'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error importing file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Export to CSV
  Future<void> _exportToCsv() async {
    try {
      final exportService = ExcelImportExportService();
      final fileName = 'spreadsheet_${DateTime.now().millisecondsSinceEpoch}';
      final success = await exportService.exportToCsv(_cellData, fileName);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Data exported to CSV successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to export data'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  /// Handle back navigation with confirmation
  void _handleBackNavigation() {
    if (_hasUnsavedChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Unsaved Changes'),
          content: const Text('You have unsaved changes. What would you like to do?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.go('/tools');
              },
              child: const Text('Exit Without Saving'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _saveBackend();
                context.go('/tools');
              },
              child: const Text('Save & Exit'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    } else {
      context.go('/tools');
    }
  }

  /// Toggle freeze panes functionality
  void _toggleFreezePanes() {
    setState(() {
      _freezePanes = !_freezePanes;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_freezePanes ? 'Panes frozen' : 'Panes unfrozen'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// Show cell formatting dialog
  void _showCellFormatting() {
    if (_selectedCell == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a cell first')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Format Cell $_selectedCell'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.format_bold),
              title: const Text('Bold'),
              onTap: () => _applyCellFormat('bold'),
            ),
            ListTile(
              leading: const Icon(Icons.format_italic),
              title: const Text('Italic'),
              onTap: () => _applyCellFormat('italic'),
            ),
            ListTile(
              leading: const Icon(Icons.format_color_text),
              title: const Text('Text Color'),
              onTap: () => _applyCellFormat('textColor'),
            ),
            ListTile(
              leading: const Icon(Icons.format_color_fill),
              title: const Text('Background Color'),
              onTap: () => _applyCellFormat('backgroundColor'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Apply cell formatting
  void _applyCellFormat(String formatType) {
    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Applied $formatType formatting to $_selectedCell'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// Undo last action
  void _undoLastAction() {
    if (_undoStack.isNotEmpty) {
      _redoStack.add(Map<String, SpreadsheetCell>.from(_cellData));

      setState(() {
        final previousState = _undoStack.removeLast();
        _cellData.clear();
        _cellData.addAll(previousState);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Action undone'),
          duration: Duration(seconds: 1),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Nothing to undo')),
      );
    }
  }

  /// Redo last action
  void _redoLastAction() {
    if (_redoStack.isNotEmpty) {
      _undoStack.add(Map<String, SpreadsheetCell>.from(_cellData));

      setState(() {
        final nextState = _redoStack.removeLast();
        _cellData.clear();
        _cellData.addAll(nextState);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Action redone'),
          duration: Duration(seconds: 1),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Nothing to redo')),
      );
    }
  }

  /// Save current state for undo functionality
  void _saveStateForUndo() {
    _undoStack.add(Map<String, SpreadsheetCell>.from(_cellData));
    _redoStack.clear(); // Clear redo stack when new action is performed

    // Limit undo stack size
    if (_undoStack.length > 50) {
      _undoStack.removeAt(0);
    }

    _hasUnsavedChanges = true;
  }

  void _saveBackend() {
    // Navigate to save screen with current data
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ToolSaveScreen(
          backendData: _cellData,
          onSaved: (toolId) {
            setState(() {
              _hasUnsavedChanges = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Tool saved successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          },
        ),
      ),
    );
  }

  List<FormulaAutoComplete> _getBuiltInFunctions() {
    // Get all available functions from the enhanced formula engine
    final functions = _formulaEngine.getAvailableFunctions();

    return functions.map((func) => FormulaAutoComplete(
      name: func.name,
      description: func.description,
      parameters: func.parameters,
      category: func.category,
      insertText: '${func.name}(${func.parameters.map((p) => '').join(',')})',
    )).toList();
  }

  // Helper methods for dynamic matrix size and enhanced functionality

  String _getColumnLabel(int index) {
    if (index < 26) {
      return String.fromCharCode(65 + index); // A-Z
    } else {
      // For columns beyond Z, use AA, AB, AC, etc.
      final firstLetter = String.fromCharCode(65 + (index ~/ 26) - 1);
      final secondLetter = String.fromCharCode(65 + (index % 26));
      return firstLetter + secondLetter;
    }
  }

  void _configureMatrixSize() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Configure Matrix Size'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Current size: ${_matrixCols} columns × ${_matrixRows} rows',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Columns: '),
                  Expanded(
                    child: Slider(
                      value: _matrixCols.toDouble(),
                      min: 10,
                      max: 100,
                      divisions: 90,
                      label: _matrixCols.toString(),
                      onChanged: (value) {
                        setState(() {
                          _matrixCols = value.toInt();
                        });
                      },
                    ),
                  ),
                  Text(_matrixCols.toString()),
                ],
              ),
              Row(
                children: [
                  const Text('Rows: '),
                  Expanded(
                    child: Slider(
                      value: _matrixRows.toDouble(),
                      min: 10,
                      max: 200,
                      divisions: 190,
                      label: _matrixRows.toString(),
                      onChanged: (value) {
                        setState(() {
                          _matrixRows = value.toInt();
                        });
                      },
                    ),
                  ),
                  Text(_matrixRows.toString()),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Checkbox(
                    value: _directDataEntry,
                    onChanged: (value) {
                      setState(() {
                        _directDataEntry = value ?? true;
                      });
                    },
                  ),
                  const Expanded(
                    child: Text('Enable direct data entry mode'),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                // Matrix size updated
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Matrix size updated to ${_matrixCols}×${_matrixRows}'),
                ),
              );
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _loadSampleData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Load Sample Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose a sample dataset to populate the spreadsheet:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.attach_money),
              title: const Text('Financial Data'),
              subtitle: const Text('Budget, expenses, and income data'),
              onTap: () => _loadFinancialSampleData(),
            ),
            ListTile(
              leading: const Icon(Icons.inventory),
              title: const Text('Inventory Data'),
              subtitle: const Text('Product inventory and stock levels'),
              onTap: () => _loadInventorySampleData(),
            ),
            ListTile(
              leading: const Icon(Icons.trending_up),
              title: const Text('Sales Data'),
              subtitle: const Text('Sales performance and metrics'),
              onTap: () => _loadSalesSampleData(),
            ),
            ListTile(
              leading: const Icon(Icons.school),
              title: const Text('Student Grades'),
              subtitle: const Text('Academic performance tracking'),
              onTap: () => _loadGradesSampleData(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _loadFinancialSampleData() {
    Navigator.of(context).pop();
    _saveStateForUndo();

    // Sample financial data
    final sampleData = {
      'A1': SpreadsheetCell(address: 'A1', value: 'Category', type: CellType.text),
      'B1': SpreadsheetCell(address: 'B1', value: 'Budget', type: CellType.text),
      'C1': SpreadsheetCell(address: 'C1', value: 'Actual', type: CellType.text),
      'D1': SpreadsheetCell(address: 'D1', value: 'Difference', type: CellType.text),
      'A2': SpreadsheetCell(address: 'A2', value: 'Housing', type: CellType.text),
      'B2': SpreadsheetCell(address: 'B2', value: 1500, type: CellType.number),
      'C2': SpreadsheetCell(address: 'C2', value: 1650, type: CellType.number),
      'D2': SpreadsheetCell(address: 'D2', value: 150, formula: '=C2-B2', type: CellType.formula),
      'A3': SpreadsheetCell(address: 'A3', value: 'Food', type: CellType.text),
      'B3': SpreadsheetCell(address: 'B3', value: 600, type: CellType.number),
      'C3': SpreadsheetCell(address: 'C3', value: 580, type: CellType.number),
      'D3': SpreadsheetCell(address: 'D3', value: -20, formula: '=C3-B3', type: CellType.formula),
      'A4': SpreadsheetCell(address: 'A4', value: 'Transportation', type: CellType.text),
      'B4': SpreadsheetCell(address: 'B4', value: 400, type: CellType.number),
      'C4': SpreadsheetCell(address: 'C4', value: 420, type: CellType.number),
      'D4': SpreadsheetCell(address: 'D4', value: 20, formula: '=C4-B4', type: CellType.formula),
      'A5': SpreadsheetCell(address: 'A5', value: 'Total', type: CellType.text),
      'B5': SpreadsheetCell(address: 'B5', value: 2500, formula: '=SUM(B2:B4)', type: CellType.formula),
      'C5': SpreadsheetCell(address: 'C5', value: 2650, formula: '=SUM(C2:C4)', type: CellType.formula),
      'D5': SpreadsheetCell(address: 'D5', value: 150, formula: '=SUM(D2:D4)', type: CellType.formula),
    };

    setState(() {
      _cellData.addAll(sampleData);
      _hasUnsavedChanges = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Financial sample data loaded')),
    );
  }

  void _loadInventorySampleData() {
    Navigator.of(context).pop();
    _saveStateForUndo();

    final sampleData = {
      'A1': SpreadsheetCell(address: 'A1', value: 'Product', type: CellType.text),
      'B1': SpreadsheetCell(address: 'B1', value: 'Stock', type: CellType.text),
      'C1': SpreadsheetCell(address: 'C1', value: 'Min Level', type: CellType.text),
      'D1': SpreadsheetCell(address: 'D1', value: 'Status', type: CellType.text),
      'A2': SpreadsheetCell(address: 'A2', value: 'Widget A', type: CellType.text),
      'B2': SpreadsheetCell(address: 'B2', value: 150, type: CellType.number),
      'C2': SpreadsheetCell(address: 'C2', value: 100, type: CellType.number),
      'D2': SpreadsheetCell(address: 'D2', value: 'OK', formula: '=IF(B2>=C2,"OK","LOW")', type: CellType.formula),
      'A3': SpreadsheetCell(address: 'A3', value: 'Widget B', type: CellType.text),
      'B3': SpreadsheetCell(address: 'B3', value: 75, type: CellType.number),
      'C3': SpreadsheetCell(address: 'C3', value: 80, type: CellType.number),
      'D3': SpreadsheetCell(address: 'D3', value: 'LOW', formula: '=IF(B3>=C3,"OK","LOW")', type: CellType.formula),
    };

    setState(() {
      _cellData.addAll(sampleData);
      _hasUnsavedChanges = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Inventory sample data loaded')),
    );
  }

  void _loadSalesSampleData() {
    Navigator.of(context).pop();
    _saveStateForUndo();

    final sampleData = {
      'A1': SpreadsheetCell(address: 'A1', value: 'Month', type: CellType.text),
      'B1': SpreadsheetCell(address: 'B1', value: 'Sales', type: CellType.text),
      'C1': SpreadsheetCell(address: 'C1', value: 'Target', type: CellType.text),
      'D1': SpreadsheetCell(address: 'D1', value: 'Performance', type: CellType.text),
      'A2': SpreadsheetCell(address: 'A2', value: 'January', type: CellType.text),
      'B2': SpreadsheetCell(address: 'B2', value: 12500, type: CellType.number),
      'C2': SpreadsheetCell(address: 'C2', value: 10000, type: CellType.number),
      'D2': SpreadsheetCell(address: 'D2', value: 125, formula: '=B2/C2*100', type: CellType.formula),
      'A3': SpreadsheetCell(address: 'A3', value: 'February', type: CellType.text),
      'B3': SpreadsheetCell(address: 'B3', value: 9800, type: CellType.number),
      'C3': SpreadsheetCell(address: 'C3', value: 10000, type: CellType.number),
      'D3': SpreadsheetCell(address: 'D3', value: 98, formula: '=B3/C3*100', type: CellType.formula),
    };

    setState(() {
      _cellData.addAll(sampleData);
      _hasUnsavedChanges = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sales sample data loaded')),
    );
  }

  void _loadGradesSampleData() {
    Navigator.of(context).pop();
    _saveStateForUndo();

    final sampleData = {
      'A1': SpreadsheetCell(address: 'A1', value: 'Student', type: CellType.text),
      'B1': SpreadsheetCell(address: 'B1', value: 'Math', type: CellType.text),
      'C1': SpreadsheetCell(address: 'C1', value: 'Science', type: CellType.text),
      'D1': SpreadsheetCell(address: 'D1', value: 'Average', type: CellType.text),
      'A2': SpreadsheetCell(address: 'A2', value: 'Alice', type: CellType.text),
      'B2': SpreadsheetCell(address: 'B2', value: 95, type: CellType.number),
      'C2': SpreadsheetCell(address: 'C2', value: 88, type: CellType.number),
      'D2': SpreadsheetCell(address: 'D2', value: 91.5, formula: '=(B2+C2)/2', type: CellType.formula),
      'A3': SpreadsheetCell(address: 'A3', value: 'Bob', type: CellType.text),
      'B3': SpreadsheetCell(address: 'B3', value: 82, type: CellType.number),
      'C3': SpreadsheetCell(address: 'C3', value: 90, type: CellType.number),
      'D3': SpreadsheetCell(address: 'D3', value: 86, formula: '=(B3+C3)/2', type: CellType.formula),
    };

    setState(() {
      _cellData.addAll(sampleData);
      _hasUnsavedChanges = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Student grades sample data loaded')),
    );
  }
}
