import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sync_models.dart';

class OfflineSyncService {
  static final OfflineSyncService _instance = OfflineSyncService._internal();
  factory OfflineSyncService() => _instance;
  OfflineSyncService._internal();

  static const String _syncQueueKey = 'sync_queue';
  static const String _conflictsKey = 'sync_conflicts';
  static const String _configKey = 'sync_config';
  static const String _lastSyncKey = 'last_sync_time';

  final List<SyncItem> _syncQueue = [];
  final List<SyncConflict> _conflicts = [];
  final StreamController<SyncStats> _statsController = 
      StreamController<SyncStats>.broadcast();
  final StreamController<List<SyncConflict>> _conflictsController = 
      StreamController<List<SyncConflict>>.broadcast();

  SharedPreferences? _prefs;
  Timer? _syncTimer;
  bool _isInitialized = false;
  bool _isSyncing = false;
  bool _isOnline = false;
  SyncConfiguration _config = const SyncConfiguration();

  // Connectivity
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  // Getters
  Stream<SyncStats> get statsStream => _statsController.stream;
  Stream<List<SyncConflict>> get conflictsStream => _conflictsController.stream;
  SyncStats get currentStats => _generateStats();
  List<SyncConflict> get conflicts => List.unmodifiable(_conflicts);
  bool get isOnline => _isOnline;
  bool get isSyncing => _isSyncing;
  SyncConfiguration get config => _config;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadConfiguration();
      await _loadSyncQueue();
      await _loadConflicts();
      await _checkConnectivity();
      _startConnectivityMonitoring();
      _startAutoSync();
      _isInitialized = true;
      _emitStats();
      debugPrint('OfflineSyncService initialized');
    } catch (e) {
      debugPrint('Error initializing OfflineSyncService: $e');
    }
  }

  // Configuration management
  Future<void> updateConfiguration(SyncConfiguration newConfig) async {
    _config = newConfig;
    await _saveConfiguration();
    _restartAutoSync();
  }

  // Queue management
  Future<void> queueForSync({
    required String entityType,
    required String entityId,
    required SyncOperation operation,
    required Map<String, dynamic> data,
    Map<String, dynamic>? previousData,
    SyncPriority priority = SyncPriority.normal,
  }) async {
    if (!_config.enabledEntityTypes.contains(entityType)) {
      return; // Entity type not enabled for sync
    }

    final syncItem = SyncItem(
      id: '${entityType}_${entityId}_${DateTime.now().millisecondsSinceEpoch}',
      entityType: entityType,
      entityId: entityId,
      operation: operation,
      data: data,
      previousData: previousData,
      priority: priority,
    );

    // Remove any existing sync items for the same entity
    _syncQueue.removeWhere((item) => 
        item.entityType == entityType && 
        item.entityId == entityId &&
        item.status == SyncStatus.pending);

    _syncQueue.add(syncItem);
    await _saveSyncQueue();
    _emitStats();

    // Trigger immediate sync if online and auto-sync is enabled
    if (_isOnline && _config.autoSync && !_isSyncing) {
      _performSync();
    }
  }

  // Manual sync trigger
  Future<void> syncNow() async {
    if (_isSyncing) return;
    await _performSync();
  }

  // Sync specific entity type
  Future<void> syncEntityType(String entityType) async {
    if (_isSyncing) return;

    final itemsToSync = _syncQueue.where((item) => 
        item.entityType == entityType && 
        item.status == SyncStatus.pending).toList();

    if (itemsToSync.isEmpty) return;

    await _performSyncForItems(itemsToSync);
  }

  // Conflict resolution
  Future<void> resolveConflict(
    String entityType,
    String entityId,
    ConflictResolution resolution,
  ) async {
    final conflictIndex = _conflicts.indexWhere((conflict) =>
        conflict.entityType == entityType && conflict.entityId == entityId);

    if (conflictIndex == -1) return;

    final conflict = _conflicts[conflictIndex];
    final resolvedConflict = conflict.copyWith(resolution: resolution);

    switch (resolution) {
      case ConflictResolution.useLocal:
        await _applyLocalData(resolvedConflict);
        break;
      case ConflictResolution.useRemote:
        await _applyRemoteData(resolvedConflict);
        break;
      case ConflictResolution.merge:
        await _mergeData(resolvedConflict);
        break;
      case ConflictResolution.skip:
        // Just remove the conflict
        break;
    }

    _conflicts.removeAt(conflictIndex);
    await _saveConflicts();
    _conflictsController.add(_conflicts);
    _emitStats();
  }

  // Resolve all conflicts with default resolution
  Future<void> resolveAllConflicts([ConflictResolution? resolution]) async {
    final resolutionToUse = resolution ?? _config.defaultConflictResolution;
    
    for (final conflict in List.from(_conflicts)) {
      await resolveConflict(
        conflict.entityType,
        conflict.entityId,
        resolutionToUse,
      );
    }
  }

  // Clear sync queue
  Future<void> clearSyncQueue() async {
    _syncQueue.clear();
    await _saveSyncQueue();
    _emitStats();
  }

  // Retry failed items
  Future<void> retryFailedItems() async {
    final failedItems = _syncQueue.where((item) => 
        item.status == SyncStatus.failed).toList();

    for (final item in failedItems) {
      final updatedItem = item.copyWith(
        status: SyncStatus.pending,
        errorMessage: null,
      );
      
      final index = _syncQueue.indexOf(item);
      _syncQueue[index] = updatedItem;
    }

    await _saveSyncQueue();
    _emitStats();

    if (_isOnline && !_isSyncing) {
      await _performSync();
    }
  }

  // Private methods
  Future<void> _performSync() async {
    if (_isSyncing || !_isOnline) return;

    _isSyncing = true;
    _emitStats();

    try {
      final pendingItems = _syncQueue.where((item) => 
          item.status == SyncStatus.pending).toList();

      // Sort by priority and creation time
      pendingItems.sort((a, b) {
        final priorityComparison = b.priority.index.compareTo(a.priority.index);
        if (priorityComparison != 0) return priorityComparison;
        return a.createdAt.compareTo(b.createdAt);
      });

      await _performSyncForItems(pendingItems);
      
      // Update last sync time
      await _prefs?.setString(_lastSyncKey, DateTime.now().toIso8601String());
      
    } catch (e) {
      debugPrint('Sync error: $e');
    } finally {
      _isSyncing = false;
      _emitStats();
    }
  }

  Future<void> _performSyncForItems(List<SyncItem> items) async {
    for (final item in items) {
      if (!_isOnline) break; // Stop if we go offline

      try {
        // Mark as syncing
        final syncingItem = item.copyWith(status: SyncStatus.syncing);
        final index = _syncQueue.indexOf(item);
        _syncQueue[index] = syncingItem;
        _emitStats();

        // Perform the actual sync operation
        final success = await _syncItem(syncingItem);

        if (success) {
          final syncedItem = syncingItem.copyWith(
            status: SyncStatus.synced,
            syncedAt: DateTime.now(),
          );
          _syncQueue[index] = syncedItem;
        } else {
          final failedItem = syncingItem.copyWith(
            status: SyncStatus.failed,
            retryCount: syncingItem.retryCount + 1,
            errorMessage: 'Sync operation failed',
          );
          _syncQueue[index] = failedItem;
        }

      } catch (e) {
        final failedItem = item.copyWith(
          status: SyncStatus.failed,
          retryCount: item.retryCount + 1,
          errorMessage: e.toString(),
        );
        final index = _syncQueue.indexOf(item);
        _syncQueue[index] = failedItem;
      }
    }

    await _saveSyncQueue();
    _emitStats();
  }

  Future<bool> _syncItem(SyncItem item) async {
    // This would integrate with your actual sync backend (Supabase, etc.)
    // For now, simulate sync operation
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Simulate occasional conflicts
    if (item.operation == SyncOperation.update && 
        DateTime.now().millisecond % 10 == 0) {
      await _handleConflict(item);
      return false;
    }

    // Simulate occasional failures
    if (DateTime.now().millisecond % 20 == 0) {
      return false;
    }

    return true;
  }

  Future<void> _handleConflict(SyncItem item) async {
    // Simulate remote data that conflicts with local data
    final remoteData = Map<String, dynamic>.from(item.data);
    remoteData['title'] = '${remoteData['title']} (Remote)';
    remoteData['modifiedAt'] = DateTime.now().subtract(const Duration(minutes: 5)).toIso8601String();

    final conflict = SyncConflict(
      entityType: item.entityType,
      entityId: item.entityId,
      localData: item.data,
      remoteData: remoteData,
      localModifiedAt: DateTime.parse(item.data['modifiedAt'] ?? DateTime.now().toIso8601String()),
      remoteModifiedAt: DateTime.parse(remoteData['modifiedAt']),
    );

    _conflicts.add(conflict);
    await _saveConflicts();
    _conflictsController.add(_conflicts);

    // Mark sync item as conflict
    final conflictItem = item.copyWith(status: SyncStatus.conflict);
    final index = _syncQueue.indexOf(item);
    _syncQueue[index] = conflictItem;
  }

  Future<void> _applyLocalData(SyncConflict conflict) async {
    // Apply local data to remote
    debugPrint('Applying local data for ${conflict.entityType}:${conflict.entityId}');
  }

  Future<void> _applyRemoteData(SyncConflict conflict) async {
    // Apply remote data to local
    debugPrint('Applying remote data for ${conflict.entityType}:${conflict.entityId}');
  }

  Future<void> _mergeData(SyncConflict conflict) async {
    // Merge local and remote data
    debugPrint('Merging data for ${conflict.entityType}:${conflict.entityId}');
  }

  Future<void> _checkConnectivity() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    _isOnline = connectivityResult != ConnectivityResult.none;
  }

  void _startConnectivityMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;
      
      if (!wasOnline && _isOnline) {
        // Just came online, trigger sync
        if (_config.autoSync && !_isSyncing) {
          _performSync();
        }
      }
      
      _emitStats();
    });
  }

  void _startAutoSync() {
    if (!_config.autoSync) return;
    
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(_config.syncInterval, (_) {
      if (_isOnline && !_isSyncing) {
        _performSync();
      }
    });
  }

  void _restartAutoSync() {
    _syncTimer?.cancel();
    _startAutoSync();
  }

  SyncStats _generateStats() {
    final totalItems = _syncQueue.length;
    final pendingItems = _syncQueue.where((item) => item.status == SyncStatus.pending).length;
    final syncedItems = _syncQueue.where((item) => item.status == SyncStatus.synced).length;
    final failedItems = _syncQueue.where((item) => item.status == SyncStatus.failed).length;
    final conflictItems = _conflicts.length;

    final lastSyncString = _prefs?.getString(_lastSyncKey);
    final lastSyncAt = lastSyncString != null ? DateTime.parse(lastSyncString) : null;

    return SyncStats(
      totalItems: totalItems,
      pendingItems: pendingItems,
      syncedItems: syncedItems,
      failedItems: failedItems,
      conflictItems: conflictItems,
      lastSyncAt: lastSyncAt,
      isOnline: _isOnline,
      isSyncing: _isSyncing,
    );
  }

  void _emitStats() {
    _statsController.add(_generateStats());
  }

  Future<void> _loadConfiguration() async {
    try {
      final configJson = _prefs?.getString(_configKey);
      if (configJson != null) {
        final configMap = json.decode(configJson) as Map<String, dynamic>;
        _config = SyncConfiguration.fromJson(configMap);
      }
    } catch (e) {
      debugPrint('Error loading sync configuration: $e');
    }
  }

  Future<void> _saveConfiguration() async {
    try {
      final configJson = json.encode(_config.toJson());
      await _prefs?.setString(_configKey, configJson);
    } catch (e) {
      debugPrint('Error saving sync configuration: $e');
    }
  }

  Future<void> _loadSyncQueue() async {
    try {
      final queueJson = _prefs?.getString(_syncQueueKey);
      if (queueJson != null) {
        final queueList = json.decode(queueJson) as List<dynamic>;
        _syncQueue.clear();
        _syncQueue.addAll(
          queueList.map((item) => SyncItem.fromJson(item as Map<String, dynamic>))
        );
      }
    } catch (e) {
      debugPrint('Error loading sync queue: $e');
    }
  }

  Future<void> _saveSyncQueue() async {
    try {
      final queueJson = json.encode(
        _syncQueue.map((item) => item.toJson()).toList()
      );
      await _prefs?.setString(_syncQueueKey, queueJson);
    } catch (e) {
      debugPrint('Error saving sync queue: $e');
    }
  }

  Future<void> _loadConflicts() async {
    try {
      final conflictsJson = _prefs?.getString(_conflictsKey);
      if (conflictsJson != null) {
        final conflictsList = json.decode(conflictsJson) as List<dynamic>;
        _conflicts.clear();
        // Note: Would need to implement SyncConflict.fromJson if persisting conflicts
      }
    } catch (e) {
      debugPrint('Error loading conflicts: $e');
    }
  }

  Future<void> _saveConflicts() async {
    try {
      // Note: Would need to implement SyncConflict.toJson if persisting conflicts
      await _prefs?.setString(_conflictsKey, '[]');
    } catch (e) {
      debugPrint('Error saving conflicts: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _syncTimer?.cancel();
    _connectivitySubscription?.cancel();
    _statsController.close();
    _conflictsController.close();
  }
}
