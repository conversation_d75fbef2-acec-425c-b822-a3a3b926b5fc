import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/spreadsheet_selection_provider.dart';
import '../models/spreadsheet_cell.dart' as models;

/// Toolbar with formatting and action buttons for the spreadsheet
class SpreadsheetToolbar extends ConsumerWidget {
  final VoidCallback? onAddRow;
  final VoidCallback? onAddColumn;
  final VoidCallback? onDeleteRow;
  final VoidCallback? onDeleteColumn;
  final VoidCallback? onImportExcel;
  final VoidCallback? onExportExcel;
  final VoidCallback? onLoadSample;
  final VoidCallback? onClearAll;
  final Function(models.CellStyle style)? onFormatCells;
  final Map<String, dynamic> cells;

  const SpreadsheetToolbar({
    super.key,
    this.onAddRow,
    this.onAddColumn,
    this.onDeleteRow,
    this.onDeleteColumn,
    this.onImportExcel,
    this.onExportExcel,
    this.onLoadSample,
    this.onClearAll,
    this.onFormatCells,
    this.cells = const {},
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectionState = ref.watch(cellSelectionProvider);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Main toolbar
          Row(
            children: [
              // File operations
              _buildToolbarSection(
                context,
                'File',
                [
                  _buildToolbarButton(
                    context,
                    icon: Icons.upload_file,
                    label: 'Import',
                    onPressed: onImportExcel,
                    tooltip: 'Import Excel file',
                  ),
                  _buildToolbarButton(
                    context,
                    icon: Icons.download,
                    label: 'Export',
                    onPressed: onExportExcel,
                    tooltip: 'Export to Excel',
                  ),
                  _buildToolbarButton(
                    context,
                    icon: Icons.data_object,
                    label: 'Sample',
                    onPressed: onLoadSample,
                    tooltip: 'Load sample data',
                  ),
                ],
              ),
              
              const VerticalDivider(),
              
              // Edit operations
              _buildToolbarSection(
                context,
                'Edit',
                [
                  _buildToolbarButton(
                    context,
                    icon: Icons.content_copy,
                    label: 'Copy',
                    onPressed: selectionState.selectedRange.isNotEmpty
                        ? () => _copySelection(ref)
                        : null,
                    tooltip: 'Copy selected cells (Ctrl+C)',
                  ),
                  _buildToolbarButton(
                    context,
                    icon: Icons.content_paste,
                    label: 'Paste',
                    onPressed: selectionState.clipboard.isNotEmpty
                        ? () => _pasteSelection(ref)
                        : null,
                    tooltip: 'Paste from clipboard (Ctrl+V)',
                  ),
                  _buildToolbarButton(
                    context,
                    icon: Icons.clear,
                    label: 'Clear',
                    onPressed: selectionState.selectedRange.isNotEmpty
                        ? () => _clearSelection(ref)
                        : null,
                    tooltip: 'Clear selected cells (Delete)',
                  ),
                ],
              ),
              
              const VerticalDivider(),
              
              // Row/Column operations
              _buildToolbarSection(
                context,
                'Insert',
                [
                  _buildToolbarButton(
                    context,
                    icon: Icons.table_rows,
                    label: 'Row',
                    onPressed: onAddRow,
                    tooltip: 'Add new row',
                  ),
                  _buildToolbarButton(
                    context,
                    icon: Icons.view_column,
                    label: 'Column',
                    onPressed: onAddColumn,
                    tooltip: 'Add new column',
                  ),
                ],
              ),
              
              const VerticalDivider(),
              
              // Formatting
              _buildFormattingSection(context, ref, selectionState),
              
              const Spacer(),
              
              // Status and actions
              _buildStatusSection(context, selectionState),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Secondary toolbar with additional options
          _buildSecondaryToolbar(context, ref, selectionState),
        ],
      ),
    );
  }

  Widget _buildToolbarSection(
    BuildContext context,
    String title,
    List<Widget> buttons,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Row(children: buttons),
      ],
    );
  }

  Widget _buildToolbarButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    VoidCallback? onPressed,
    String? tooltip,
    bool isSelected = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 4),
      child: Column(
        children: [
          IconButton(
            onPressed: onPressed,
            icon: Icon(icon),
            tooltip: tooltip,
            style: IconButton.styleFrom(
              backgroundColor: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : null,
              foregroundColor: onPressed == null
                  ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.38)
                  : null,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.labelSmall,
          ),
        ],
      ),
    );
  }

  Widget _buildFormattingSection(
    BuildContext context,
    WidgetRef ref,
    CellSelectionState selectionState,
  ) {
    return _buildToolbarSection(
      context,
      'Format',
      [
        _buildToolbarButton(
          context,
          icon: Icons.format_bold,
          label: 'Bold',
          onPressed: selectionState.selectedRange.isNotEmpty
              ? () => _toggleBold(ref)
              : null,
          tooltip: 'Bold (Ctrl+B)',
        ),
        _buildToolbarButton(
          context,
          icon: Icons.format_italic,
          label: 'Italic',
          onPressed: selectionState.selectedRange.isNotEmpty
              ? () => _toggleItalic(ref)
              : null,
          tooltip: 'Italic (Ctrl+I)',
        ),
        _buildToolbarButton(
          context,
          icon: Icons.format_color_text,
          label: 'Color',
          onPressed: selectionState.selectedRange.isNotEmpty
              ? () => _showColorPicker(context, ref)
              : null,
          tooltip: 'Text color',
        ),
      ],
    );
  }

  Widget _buildStatusSection(
    BuildContext context,
    CellSelectionState selectionState,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          'Selected: ${selectionState.selectedRange.length} cells',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          'Total: ${cells.length} cells with data',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        if (selectionState.selectedCell != null)
          Text(
            'Active: ${selectionState.selectedCell}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
      ],
    );
  }

  Widget _buildSecondaryToolbar(
    BuildContext context,
    WidgetRef ref,
    CellSelectionState selectionState,
  ) {
    return Row(
      children: [
        // Alignment options
        _buildToolbarSection(
          context,
          'Align',
          [
            _buildSmallButton(
              context,
              icon: Icons.format_align_left,
              onPressed: () => _setAlignment(ref, models.TextAlign.left),
              tooltip: 'Align left',
            ),
            _buildSmallButton(
              context,
              icon: Icons.format_align_center,
              onPressed: () => _setAlignment(ref, models.TextAlign.center),
              tooltip: 'Align center',
            ),
            _buildSmallButton(
              context,
              icon: Icons.format_align_right,
              onPressed: () => _setAlignment(ref, models.TextAlign.right),
              tooltip: 'Align right',
            ),
          ],
        ),
        
        const VerticalDivider(),
        
        // Number formatting
        _buildToolbarSection(
          context,
          'Number',
          [
            _buildSmallButton(
              context,
              icon: Icons.percent,
              onPressed: () => _formatAsPercentage(ref),
              tooltip: 'Format as percentage',
            ),
            _buildSmallButton(
              context,
              icon: Icons.attach_money,
              onPressed: () => _formatAsCurrency(ref),
              tooltip: 'Format as currency',
            ),
            _buildSmallButton(
              context,
              icon: Icons.calendar_today,
              onPressed: () => _formatAsDate(ref),
              tooltip: 'Format as date',
            ),
          ],
        ),
        
        const Spacer(),
        
        // Danger zone
        OutlinedButton.icon(
          onPressed: onClearAll,
          icon: const Icon(Icons.clear_all),
          label: const Text('Clear All'),
          style: OutlinedButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildSmallButton(
    BuildContext context, {
    required IconData icon,
    VoidCallback? onPressed,
    String? tooltip,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 2),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: 16),
        tooltip: tooltip,
        style: IconButton.styleFrom(
          minimumSize: const Size(24, 24),
          padding: const EdgeInsets.all(4),
        ),
      ),
    );
  }

  // Action methods
  void _copySelection(WidgetRef ref) {
    // Implementation would use the existing copy functionality
    ref.read(cellSelectionProvider.notifier).copyToClipboard({});
  }

  void _pasteSelection(WidgetRef ref) {
    // Implementation would use the existing paste functionality
    ref.read(cellSelectionProvider.notifier).pasteFromClipboard({}, (address, value) {
      // Handle cell update
    });
  }

  void _clearSelection(WidgetRef ref) {
    final selectionState = ref.read(cellSelectionProvider);
    for (final address in selectionState.selectedRange) {
      // Clear cell content - would be handled by parent
    }
  }

  void _toggleBold(WidgetRef ref) {
    // Toggle bold formatting for selected cells
    onFormatCells?.call(const models.CellStyle(bold: true));
  }

  void _toggleItalic(WidgetRef ref) {
    // Toggle italic formatting for selected cells
    onFormatCells?.call(const models.CellStyle(italic: true));
  }

  void _showColorPicker(BuildContext context, WidgetRef ref) {
    // Show color picker dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Text Color'),
        content: Container(
          width: 200,
          height: 200,
          child: GridView.count(
            crossAxisCount: 4,
            children: [
              Colors.black,
              Colors.red,
              Colors.green,
              Colors.blue,
              Colors.orange,
              Colors.purple,
              Colors.brown,
              Colors.grey,
            ].map((color) {
              return GestureDetector(
                onTap: () {
                  onFormatCells?.call(models.CellStyle(
                    textColor: '#${color.value.toRadixString(16).substring(2)}',
                  ));
                  Navigator.of(context).pop();
                },
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  void _setAlignment(WidgetRef ref, models.TextAlign alignment) {
    onFormatCells?.call(models.CellStyle(textAlign: alignment));
  }

  void _formatAsPercentage(WidgetRef ref) {
    // Format selected cells as percentage
  }

  void _formatAsCurrency(WidgetRef ref) {
    // Format selected cells as currency
  }

  void _formatAsDate(WidgetRef ref) {
    // Format selected cells as date
  }
}
