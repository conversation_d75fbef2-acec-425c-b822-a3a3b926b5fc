import 'package:flutter/material.dart';
import '../services/formula_dependency_service.dart';
import '../models/spreadsheet_cell.dart';

/// Widget for visualizing formula dependencies and relationships
class DependencyVisualizationWidget extends StatefulWidget {
  final Map<String, SpreadsheetCell> cells;
  final String? selectedCell;
  final Function(String cellAddress)? onCellSelected;

  const DependencyVisualizationWidget({
    super.key,
    required this.cells,
    this.selectedCell,
    this.onCellSelected,
  });

  @override
  State<DependencyVisualizationWidget> createState() => _DependencyVisualizationWidgetState();
}

class _DependencyVisualizationWidgetState extends State<DependencyVisualizationWidget> {
  final FormulaDependencyService _dependencyService = FormulaDependencyService();
  DependencyTrace? _currentTrace;
  bool _showPrecedents = true;
  bool _showDependents = true;
  int _maxDepth = 3;

  @override
  void initState() {
    super.initState();
    _updateTrace();
  }

  @override
  void didUpdateWidget(DependencyVisualizationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedCell != widget.selectedCell) {
      _updateTrace();
    }
  }

  void _updateTrace() {
    if (widget.selectedCell != null) {
      _currentTrace = _dependencyService.traceDependencies(
        widget.selectedCell!,
        widget.cells,
        maxDepth: _maxDepth,
      );
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_tree,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Formula Dependencies',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (widget.selectedCell != null)
                  Chip(
                    label: Text(widget.selectedCell!),
                    backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                  ),
              ],
            ),
          ),

          // Controls
          Padding(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                FilterChip(
                  label: const Text('Precedents'),
                  selected: _showPrecedents,
                  onSelected: (selected) {
                    setState(() {
                      _showPrecedents = selected;
                    });
                  },
                ),
                FilterChip(
                  label: const Text('Dependents'),
                  selected: _showDependents,
                  onSelected: (selected) {
                    setState(() {
                      _showDependents = selected;
                    });
                  },
                ),
                DropdownButton<int>(
                  value: _maxDepth,
                  items: [1, 2, 3, 4, 5].map((depth) {
                    return DropdownMenuItem(
                      value: depth,
                      child: Text('Depth: $depth'),
                    );
                  }).toList(),
                  onChanged: (depth) {
                    if (depth != null) {
                      setState(() {
                        _maxDepth = depth;
                      });
                      _updateTrace();
                    }
                  },
                ),
              ],
            ),
          ),

          // Visualization
          Expanded(
            child: widget.selectedCell == null
                ? _buildEmptyState()
                : _buildDependencyVisualization(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.touch_app,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'Select a cell to view dependencies',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDependencyVisualization() {
    if (_currentTrace == null) return const SizedBox.shrink();

    final links = _currentTrace!.getAllLinks();
    final precedentLinks = links.where((link) => 
      link.type == DependencyType.precedent && _showPrecedents
    ).toList();
    final dependentLinks = links.where((link) => 
      link.type == DependencyType.dependent && _showDependents
    ).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Formula complexity analysis
          if (widget.selectedCell != null) _buildComplexityAnalysis(),
          
          const SizedBox(height: 16),
          
          // Precedents section
          if (_showPrecedents && precedentLinks.isNotEmpty) ...[
            Text(
              'Precedents (cells this formula depends on)',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            _buildDependencyTree(precedentLinks, DependencyType.precedent),
            const SizedBox(height: 16),
          ],
          
          // Dependents section
          if (_showDependents && dependentLinks.isNotEmpty) ...[
            Text(
              'Dependents (cells that depend on this formula)',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 8),
            _buildDependencyTree(dependentLinks, DependencyType.dependent),
          ],
          
          // Circular reference warning
          _buildCircularReferenceWarning(),
        ],
      ),
    );
  }

  Widget _buildComplexityAnalysis() {
    final cell = widget.cells[widget.selectedCell!];
    if (cell == null || !cell.hasFormula) return const SizedBox.shrink();

    final complexity = _dependencyService.analyzeComplexity(cell.formula!);

    return Card(
      color: Theme.of(context).colorScheme.surfaceContainerHigh,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Formula Complexity: ${complexity.complexityLevel}',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Chip(
                  label: Text(complexity.totalComplexity.toStringAsFixed(1)),
                  backgroundColor: _getComplexityColor(complexity.totalComplexity),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildComplexityChip('Functions', complexity.functions.length.toString(), Colors.blue),
                _buildComplexityChip('Cell Refs', complexity.cellReferences.length.toString(), Colors.green),
                _buildComplexityChip('Operators', complexity.operators.length.toString(), Colors.orange),
                _buildComplexityChip('Nesting', complexity.nestingLevel.toString(), Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComplexityChip(String label, String value, Color color) {
    return Chip(
      label: Text('$label: $value'),
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color.withValues(alpha: 0.3)),
    );
  }

  Color _getComplexityColor(double complexity) {
    if (complexity < 5) return Colors.green.withValues(alpha: 0.2);
    if (complexity < 15) return Colors.yellow.withValues(alpha: 0.2);
    if (complexity < 30) return Colors.orange.withValues(alpha: 0.2);
    return Colors.red.withValues(alpha: 0.2);
  }

  Widget _buildDependencyTree(List<DependencyLink> links, DependencyType type) {
    final groupedByDepth = <int, List<DependencyLink>>{};
    
    for (final link in links) {
      groupedByDepth[link.depth] ??= [];
      groupedByDepth[link.depth]!.add(link);
    }

    return Column(
      children: groupedByDepth.entries.map((entry) {
        final depth = entry.key;
        final depthLinks = entry.value;
        
        return Padding(
          padding: EdgeInsets.only(left: depth * 20.0),
          child: Column(
            children: depthLinks.map((link) {
              return _buildDependencyItem(link, type);
            }).toList(),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDependencyItem(DependencyLink link, DependencyType type) {
    final targetCell = type == DependencyType.precedent ? link.to : link.to;
    final cell = widget.cells[targetCell];
    final hasFormula = cell?.hasFormula ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: InkWell(
        onTap: () => widget.onCellSelected?.call(targetCell),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: type == DependencyType.precedent 
                ? Colors.blue.withValues(alpha: 0.1)
                : Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: type == DependencyType.precedent 
                  ? Colors.blue.withValues(alpha: 0.3)
                  : Colors.green.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                hasFormula ? Icons.functions : Icons.data_object,
                size: 16,
                color: type == DependencyType.precedent ? Colors.blue : Colors.green,
              ),
              const SizedBox(width: 8),
              Text(
                targetCell,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: type == DependencyType.precedent ? Colors.blue : Colors.green,
                ),
              ),
              if (cell != null) ...[
                const SizedBox(width: 8),
                Text(
                  cell.displayValue,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
              const SizedBox(width: 8),
              Icon(
                type == DependencyType.precedent ? Icons.arrow_upward : Icons.arrow_downward,
                size: 12,
                color: type == DependencyType.precedent ? Colors.blue : Colors.green,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCircularReferenceWarning() {
    final circularRefs = _dependencyService.detectCircularReferences(widget.cells);
    
    if (circularRefs.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.warning, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Circular Reference Detected',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                Text(
                  'Cells involved: ${circularRefs.join(', ')}',
                  style: const TextStyle(color: Colors.red),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
