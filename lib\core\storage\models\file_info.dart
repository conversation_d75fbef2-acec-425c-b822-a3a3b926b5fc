import 'dart:io';

enum FileType {
  voiceMemo,
  noteExport,
  backup,
  image,
  document,
  other,
}

enum FileStatus {
  local,
  uploading,
  uploaded,
  downloading,
  synced,
  error,
}

class FileInfo {
  String id;
  String name;
  String path;
  String? remotePath;
  FileType type;
  FileStatus status;
  int sizeBytes;
  DateTime createdAt;
  DateTime modifiedAt;
  DateTime? uploadedAt;
  String? mimeType;
  String? checksum;
  Map<String, dynamic>? metadata;
  
  // Associated data
  String? associatedId; // ID of related note, memo, etc.
  String? associatedType; // Type of associated entity
  
  // Sync information
  bool isShared;
  List<String> sharedWith;
  bool isPublic;
  String? publicUrl;
  
  // Backup information
  bool isBackup;
  String? backupVersion;
  DateTime? lastBackupAt;

  FileInfo({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.sizeBytes,
    this.remotePath,
    this.status = FileStatus.local,
    DateTime? createdAt,
    DateTime? modifiedAt,
    this.uploadedAt,
    this.mimeType,
    this.checksum,
    this.metadata,
    this.associatedId,
    this.associatedType,
    this.isShared = false,
    List<String>? sharedWith,
    this.isPublic = false,
    this.publicUrl,
    this.isBackup = false,
    this.backupVersion,
    this.lastBackupAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       modifiedAt = modifiedAt ?? DateTime.now(),
       sharedWith = sharedWith ?? [];

  bool get isLocal => status == FileStatus.local;
  bool get isUploaded => status == FileStatus.uploaded || status == FileStatus.synced;
  bool get isUploading => status == FileStatus.uploading;
  bool get isDownloading => status == FileStatus.downloading;
  bool get hasError => status == FileStatus.error;
  bool get exists => File(path).existsSync();
  
  String get formattedSize => _formatBytes(sizeBytes);
  String get extension => path.split('.').last.toLowerCase();
  
  void updateStatus(FileStatus newStatus) {
    status = newStatus;
    modifiedAt = DateTime.now();
    
    if (newStatus == FileStatus.uploaded) {
      uploadedAt = DateTime.now();
    }
  }

  void updatePath(String newPath) {
    path = newPath;
    modifiedAt = DateTime.now();
  }

  void updateSize(int newSize) {
    sizeBytes = newSize;
    modifiedAt = DateTime.now();
  }

  void markAsShared(List<String> userIds) {
    isShared = true;
    sharedWith = userIds;
    modifiedAt = DateTime.now();
  }

  void unshare() {
    isShared = false;
    sharedWith.clear();
    modifiedAt = DateTime.now();
  }

  void makePublic(String url) {
    isPublic = true;
    publicUrl = url;
    modifiedAt = DateTime.now();
  }

  void makePrivate() {
    isPublic = false;
    publicUrl = null;
    modifiedAt = DateTime.now();
  }

  FileInfo copyWith({
    String? name,
    String? path,
    String? remotePath,
    FileType? type,
    FileStatus? status,
    int? sizeBytes,
    String? mimeType,
    String? checksum,
    Map<String, dynamic>? metadata,
    String? associatedId,
    String? associatedType,
    bool? isShared,
    List<String>? sharedWith,
    bool? isPublic,
    String? publicUrl,
    bool? isBackup,
    String? backupVersion,
  }) {
    return FileInfo(
      id: id,
      name: name ?? this.name,
      path: path ?? this.path,
      remotePath: remotePath ?? this.remotePath,
      type: type ?? this.type,
      status: status ?? this.status,
      sizeBytes: sizeBytes ?? this.sizeBytes,
      createdAt: createdAt,
      modifiedAt: DateTime.now(),
      uploadedAt: uploadedAt,
      mimeType: mimeType ?? this.mimeType,
      checksum: checksum ?? this.checksum,
      metadata: metadata ?? this.metadata,
      associatedId: associatedId ?? this.associatedId,
      associatedType: associatedType ?? this.associatedType,
      isShared: isShared ?? this.isShared,
      sharedWith: sharedWith ?? this.sharedWith,
      isPublic: isPublic ?? this.isPublic,
      publicUrl: publicUrl ?? this.publicUrl,
      isBackup: isBackup ?? this.isBackup,
      backupVersion: backupVersion ?? this.backupVersion,
      lastBackupAt: lastBackupAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'remotePath': remotePath,
      'type': type.name,
      'status': status.name,
      'sizeBytes': sizeBytes,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt.toIso8601String(),
      'uploadedAt': uploadedAt?.toIso8601String(),
      'mimeType': mimeType,
      'checksum': checksum,
      'metadata': metadata,
      'associatedId': associatedId,
      'associatedType': associatedType,
      'isShared': isShared,
      'sharedWith': sharedWith,
      'isPublic': isPublic,
      'publicUrl': publicUrl,
      'isBackup': isBackup,
      'backupVersion': backupVersion,
      'lastBackupAt': lastBackupAt?.toIso8601String(),
    };
  }

  factory FileInfo.fromJson(Map<String, dynamic> json) {
    return FileInfo(
      id: json['id'],
      name: json['name'],
      path: json['path'],
      remotePath: json['remotePath'],
      type: FileType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => FileType.other,
      ),
      status: FileStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => FileStatus.local,
      ),
      sizeBytes: json['sizeBytes'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      modifiedAt: DateTime.parse(json['modifiedAt']),
      uploadedAt: json['uploadedAt'] != null ? DateTime.parse(json['uploadedAt']) : null,
      mimeType: json['mimeType'],
      checksum: json['checksum'],
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
      associatedId: json['associatedId'],
      associatedType: json['associatedType'],
      isShared: json['isShared'] ?? false,
      sharedWith: List<String>.from(json['sharedWith'] ?? []),
      isPublic: json['isPublic'] ?? false,
      publicUrl: json['publicUrl'],
      isBackup: json['isBackup'] ?? false,
      backupVersion: json['backupVersion'],
      lastBackupAt: json['lastBackupAt'] != null ? DateTime.parse(json['lastBackupAt']) : null,
    );
  }

  // Factory methods for common file types
  factory FileInfo.voiceMemo({
    required String id,
    required String name,
    required String path,
    required int sizeBytes,
    required String associatedMemoId,
    String? mimeType,
  }) {
    return FileInfo(
      id: id,
      name: name,
      path: path,
      type: FileType.voiceMemo,
      sizeBytes: sizeBytes,
      associatedId: associatedMemoId,
      associatedType: 'voice_memo',
      mimeType: mimeType ?? 'audio/aac',
    );
  }

  factory FileInfo.noteExport({
    required String id,
    required String name,
    required String path,
    required int sizeBytes,
    required String associatedNoteId,
    String? mimeType,
  }) {
    return FileInfo(
      id: id,
      name: name,
      path: path,
      type: FileType.noteExport,
      sizeBytes: sizeBytes,
      associatedId: associatedNoteId,
      associatedType: 'note',
      mimeType: mimeType ?? 'text/markdown',
    );
  }

  factory FileInfo.backup({
    required String id,
    required String name,
    required String path,
    required int sizeBytes,
    required String backupVersion,
  }) {
    return FileInfo(
      id: id,
      name: name,
      path: path,
      type: FileType.backup,
      sizeBytes: sizeBytes,
      isBackup: true,
      backupVersion: backupVersion,
      mimeType: 'application/json',
    );
  }

  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Helper methods for file operations
  Future<bool> delete() async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> move(String newPath) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.rename(newPath);
        updatePath(newPath);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> copy(String newPath) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.copy(newPath);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<String?> calculateChecksum() async {
    try {
      final file = File(path);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        // Simple checksum - in production, use crypto package for proper hashing
        final sum = bytes.fold(0, (prev, element) => prev + element);
        checksum = sum.toString();
        return checksum;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
