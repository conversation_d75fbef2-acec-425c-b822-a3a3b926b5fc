import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../navigation/app_router.dart';
import '../../shared/widgets/responsive_layout.dart';

/// Master layout system for ShadowSuite
/// Provides unified navigation and responsive design across all screens
class MasterLayout extends ConsumerStatefulWidget {
  final Widget body;
  final String title;
  final String currentRoute;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showBackButton;
  final bool showAppBar;
  final bool showNavigation;
  final Widget? bottomSheet;
  final Color? backgroundColor;

  const MasterLayout({
    super.key,
    required this.body,
    required this.title,
    required this.currentRoute,
    this.actions,
    this.floatingActionButton,
    this.showBackButton = false,
    this.showAppBar = true,
    this.showNavigation = true,
    this.bottomSheet,
    this.backgroundColor,
  });

  @override
  ConsumerState<MasterLayout> createState() => _MasterLayoutState();
}

class _MasterLayoutState extends ConsumerState<MasterLayout> {
  /// Navigation items for the master layout
  static const List<NavigationItem> _navigationItems = [
    NavigationItem(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      route: AppRoutes.dashboard,
    ),
    NavigationItem(
      label: 'Memo Suite',
      icon: Icons.note_alt_outlined,
      selectedIcon: Icons.note_alt,
      route: AppRoutes.memo,
    ),
    NavigationItem(
      label: 'Islami',
      icon: Icons.mosque_outlined,
      selectedIcon: Icons.mosque,
      route: AppRoutes.islami,
    ),
    NavigationItem(
      label: 'Tools Builder',
      icon: Icons.build_outlined,
      selectedIcon: Icons.build,
      route: AppRoutes.tools,
    ),
    NavigationItem(
      label: 'Money Flow',
      icon: Icons.account_balance_wallet_outlined,
      selectedIcon: Icons.account_balance_wallet,
      route: AppRoutes.money,
    ),
    NavigationItem(
      label: 'Settings',
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings,
      route: AppRoutes.settings,
    ),
  ];

  int get _selectedIndex {
    for (int i = 0; i < _navigationItems.length; i++) {
      if (widget.currentRoute.startsWith(_navigationItems[i].route)) {
        return i;
      }
    }
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showNavigation) {
      // Simple scaffold without navigation for special screens
      return Scaffold(
        appBar: widget.showAppBar ? _buildAppBar() : null,
        body: widget.body,
        floatingActionButton: widget.floatingActionButton,
        bottomSheet: widget.bottomSheet,
        backgroundColor: widget.backgroundColor,
      );
    }

    return ResponsiveBuilder(
      mobile: (context, constraints) => _buildMobileLayout(),
      tablet: (context, constraints) => _buildTabletLayout(),
      desktop: (context, constraints) => _buildDesktopLayout(),
    );
  }

  /// Mobile layout with bottom navigation
  Widget _buildMobileLayout() {
    return Scaffold(
      appBar: widget.showAppBar ? _buildAppBar() : null,
      body: widget.body,
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: widget.floatingActionButton,
      bottomSheet: widget.bottomSheet,
      backgroundColor: widget.backgroundColor,
    );
  }

  /// Tablet layout with drawer
  Widget _buildTabletLayout() {
    return Scaffold(
      appBar: widget.showAppBar ? _buildAppBar() : null,
      drawer: _buildDrawer(),
      body: widget.body,
      floatingActionButton: widget.floatingActionButton,
      bottomSheet: widget.bottomSheet,
      backgroundColor: widget.backgroundColor,
    );
  }

  /// Desktop layout with sidebar
  Widget _buildDesktopLayout() {
    return Scaffold(
      body: Row(
        children: [
          _buildSidebar(),
          Expanded(
            child: Column(
              children: [
                if (widget.showAppBar) _buildDesktopAppBar(),
                Expanded(child: widget.body),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: widget.floatingActionButton,
      bottomSheet: widget.bottomSheet,
      backgroundColor: widget.backgroundColor,
    );
  }

  /// Build app bar for mobile and tablet
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.title),
      leading: widget.showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => context.pop(),
            )
          : null,
      actions: widget.actions,
      elevation: 0,
    );
  }

  /// Build desktop app bar (without leading icon)
  Widget _buildDesktopAppBar() {
    return Container(
      height: kToolbarHeight,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            if (widget.showBackButton)
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => context.pop(),
              ),
            Expanded(
              child: Text(
                widget.title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            if (widget.actions != null) ...widget.actions!,
          ],
        ),
      ),
    );
  }

  /// Build bottom navigation bar for mobile
  Widget _buildBottomNavigationBar() {
    return NavigationBar(
      selectedIndex: _selectedIndex,
      onDestinationSelected: (index) {
        if (index != _selectedIndex) {
          context.go(_navigationItems[index].route);
        }
      },
      destinations: _navigationItems.map((item) {
        return NavigationDestination(
          icon: Icon(item.icon),
          selectedIcon: Icon(item.selectedIcon ?? item.icon),
          label: item.label,
        );
      }).toList(),
    );
  }

  /// Build drawer for tablet
  Widget _buildDrawer() {
    return NavigationDrawer(
      selectedIndex: _selectedIndex,
      onDestinationSelected: (index) {
        if (index != _selectedIndex) {
          context.go(_navigationItems[index].route);
        }
        Navigator.of(context).pop(); // Close drawer
      },
      children: [
        const DrawerHeader(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.apps, size: 48),
              SizedBox(height: 8),
              Text(
                'ShadowSuite',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        ..._navigationItems.map((item) {
          return NavigationDrawerDestination(
            icon: Icon(item.icon),
            selectedIcon: Icon(item.selectedIcon ?? item.icon),
            label: Text(item.label),
          );
        }),
      ],
    );
  }

  /// Build sidebar for desktop
  Widget _buildSidebar() {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // App header
          Container(
            height: kToolbarHeight,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(
                  Icons.apps,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Text(
                  'ShadowSuite',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          // Navigation items
          Expanded(
            child: NavigationRail(
              extended: true,
              selectedIndex: _selectedIndex,
              onDestinationSelected: (index) {
                if (index != _selectedIndex) {
                  context.go(_navigationItems[index].route);
                }
              },
              destinations: _navigationItems.map((item) {
                return NavigationRailDestination(
                  icon: Icon(item.icon),
                  selectedIcon: Icon(item.selectedIcon ?? item.icon),
                  label: Text(item.label),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

/// Navigation item model
class NavigationItem {
  final String label;
  final IconData icon;
  final IconData? selectedIcon;
  final String route;
  final List<NavigationItem>? children;

  const NavigationItem({
    required this.label,
    required this.icon,
    this.selectedIcon,
    required this.route,
    this.children,
  });
}
