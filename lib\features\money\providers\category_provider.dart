import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category.dart';
import '../../../shared/providers/user_provider.dart';
import '../../../core/database/database_service.dart';

// Categories provider
final categoriesProvider = StateNotifierProvider<CategoriesNotifier, List<MoneyCategory>>((ref) {
  return CategoriesNotifier(ref);
});

class CategoriesNotifier extends StateNotifier<List<MoneyCategory>> {
  final Ref ref;

  CategoriesNotifier(this.ref) : super([]) {
    loadCategories();
  }

  Future<void> loadCategories() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile == null) return;

    try {
      final db = DatabaseService.instance;
      final categories = await db.getAllCategories(userId: userProfile.id.toString());
      state = categories;
    } catch (e) {
      // Handle error - in production, use proper logging
      // print('Error loading categories: $e');
    }
  }

  Future<void> addCategory(MoneyCategory category) async {
    try {
      final db = DatabaseService.instance;
      final savedCategory = await db.saveCategory(category);
      state = [...state, savedCategory];
    } catch (e) {
      throw Exception('Failed to add category: $e');
    }
  }

  Future<void> updateCategory(MoneyCategory category) async {
    try {
      final db = DatabaseService.instance;
      final updatedCategory = await db.saveCategory(category);
      state = [
        for (final c in state)
          if (c.id == category.id) updatedCategory else c,
      ];
    } catch (e) {
      throw Exception('Failed to update category: $e');
    }
  }

  Future<void> deleteCategory(int categoryId) async {
    try {
      final category = state.firstWhere((c) => c.id == categoryId);
      if (category.isDefault) {
        throw Exception('Cannot delete default categories');
      }

      final db = DatabaseService.instance;
      await db.deleteCategory(categoryId);

      // Reload categories to reflect the change
      await loadCategories();
    } catch (e) {
      throw Exception('Failed to delete category: $e');
    }
  }

  Future<void> restoreCategory(int categoryId) async {
    try {
      final category = state.firstWhere((c) => c.id == categoryId);
      category.restore();
      state = [
        for (final c in state)
          if (c.id == categoryId) category else c,
      ];
    } catch (e) {
      throw Exception('Failed to restore category: $e');
    }
  }

  Future<void> toggleCategoryActive(int categoryId) async {
    try {
      final category = state.firstWhere((c) => c.id == categoryId);
      category.isActive = !category.isActive;
      category.updateTimestamp();
      state = [
        for (final c in state)
          if (c.id == categoryId) category else c,
      ];
    } catch (e) {
      throw Exception('Failed to toggle category: $e');
    }
  }

  Future<void> updateBudgetLimit(int categoryId, double budgetLimit) async {
    try {
      final category = state.firstWhere((c) => c.id == categoryId);
      category.budgetLimit = budgetLimit;
      category.updateTimestamp();
      state = [
        for (final c in state)
          if (c.id == categoryId) category else c,
      ];
    } catch (e) {
      throw Exception('Failed to update budget limit: $e');
    }
  }

  List<MoneyCategory> getCategoriesByType(CategoryType type) {
    return state.where((c) => c.type == type && c.isActive && !c.isDeleted).toList();
  }

  List<MoneyCategory> getActiveCategories() {
    return state.where((c) => c.isActive && !c.isDeleted).toList();
  }

  List<MoneyCategory> getDeletedCategories() {
    return state.where((c) => c.isDeleted).toList();
  }

  MoneyCategory? getCategoryById(int id) {
    try {
      return state.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  MoneyCategory? getCategoryByName(String name) {
    try {
      return state.firstWhere((c) => c.name.toLowerCase() == name.toLowerCase());
    } catch (e) {
      return null;
    }
  }
}

// Categories by type providers
final incomeCategoriesProvider = Provider<List<MoneyCategory>>((ref) {
  final categories = ref.watch(categoriesProvider);
  return categories.where((c) => c.type == CategoryType.income && c.isActive && !c.isDeleted).toList();
});

final expenseCategoriesProvider = Provider<List<MoneyCategory>>((ref) {
  final categories = ref.watch(categoriesProvider);
  return categories.where((c) => c.type == CategoryType.expense && c.isActive && !c.isDeleted).toList();
});

final transferCategoriesProvider = Provider<List<MoneyCategory>>((ref) {
  final categories = ref.watch(categoriesProvider);
  return categories.where((c) => c.type == CategoryType.transfer && c.isActive && !c.isDeleted).toList();
});

// Categories by type provider (family)
final categoriesByTypeProvider = Provider.family<List<MoneyCategory>, CategoryType>((ref, type) {
  final categories = ref.watch(categoriesProvider);
  return categories.where((c) => c.type == type && c.isActive && !c.isDeleted).toList();
});

// Category statistics provider
final categoryStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final categories = ref.watch(categoriesProvider);
  final activeCategories = categories.where((c) => c.isActive && !c.isDeleted).toList();
  
  final incomeCategories = activeCategories.where((c) => c.type == CategoryType.income).length;
  final expenseCategories = activeCategories.where((c) => c.type == CategoryType.expense).length;
  final transferCategories = activeCategories.where((c) => c.type == CategoryType.transfer).length;
  
  final totalBudgetLimit = activeCategories
      .where((c) => c.type == CategoryType.expense)
      .fold(0.0, (sum, c) => sum + c.budgetLimit);
  
  final categoriesWithBudget = activeCategories
      .where((c) => c.type == CategoryType.expense && c.budgetLimit > 0)
      .length;
  
  return {
    'totalCategories': activeCategories.length,
    'incomeCategories': incomeCategories,
    'expenseCategories': expenseCategories,
    'transferCategories': transferCategories,
    'totalBudgetLimit': totalBudgetLimit,
    'categoriesWithBudget': categoriesWithBudget,
    'deletedCategories': categories.where((c) => c.isDeleted).length,
  };
});

// Budget alerts provider (simplified for now)
final budgetAlertsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  final categories = ref.watch(categoriesProvider);

  final alerts = <Map<String, dynamic>>[];

  for (final category in categories.where((c) =>
      c.type == CategoryType.expense &&
      c.enableBudgetAlert &&
      c.budgetLimit > 0 &&
      c.isActive &&
      !c.isDeleted)) {

    // For now, just return categories that have budget alerts enabled
    // In a real app, this would calculate actual spending vs budget
    alerts.add({
      'category': category,
      'budgetLimit': category.budgetLimit,
      'totalSpent': 0.0,
      'percentage': 0.0,
      'isOverBudget': false,
      'remainingBudget': category.budgetLimit,
    });
  }

  return alerts;
});

// Category transaction statistics provider
final categoryTransactionStatsProvider = Provider<Map<int, Map<String, dynamic>>>((ref) {
  final categories = ref.watch(categoriesProvider);
  final stats = <int, Map<String, dynamic>>{};

  // For now, return empty stats since we don't have transaction data here
  // In a real implementation, this would calculate actual transaction statistics
  for (final category in categories) {
    stats[category.id] = {
      'totalAmount': 0.0,
      'transactionCount': 0,
    };
  }

  return stats;
});
