import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/ui/unified_theme_system.dart';
import '../../../core/theme/theme_provider.dart';
import 'custom_dhikr_builder_screen.dart';
import 'authentic_quran_reader_screen.dart';
import 'dhikr_routine_runner_screen.dart';
import '../providers/quran_provider.dart';
import '../providers/custom_dhikr_provider.dart';
import '../../../core/ui/master_layout.dart';
import '../providers/dhikr_provider.dart';
import '../providers/dhikr_session_provider.dart';
import '../providers/prayer_time_provider.dart';
import '../providers/athkar_display_preferences_provider.dart';
import '../models/athkar_display_preferences.dart';
import '../../../core/navigation/app_router.dart';
import 'package:go_router/go_router.dart';

import '../data/authentic_athkar.dart';
import 'dhikr_counter_screen.dart';

class AthkarScreen extends ConsumerStatefulWidget {
  final String? initialTab; // Support for tab parameter from URL

  const AthkarScreen({super.key, this.initialTab});

  @override
  ConsumerState<AthkarScreen> createState() => _AthkarScreenState();
}

class _AthkarScreenState extends ConsumerState<AthkarScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();

    // Determine initial tab based on parameter
    int initialIndex = 0;
    if (widget.initialTab != null) {
      switch (widget.initialTab) {
        case 'dhikr':
          initialIndex = 0;
          break;
        case 'quran':
          initialIndex = 1;
          break;
        case 'prayer':
          initialIndex = 2;
          break;
        case 'progress':
          initialIndex = 3;
          break;
      }
    }

    _tabController = TabController(length: 4, vsync: this, initialIndex: initialIndex);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MasterLayout(
      title: 'Athkar Pro-Quran',
      currentRoute: AppRoutes.islami,
      actions: [
        IconButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Translation settings moved to new Islami section!')),
            );
          },
          icon: const Icon(Icons.translate),
          tooltip: 'Translation Settings',
        ),
        IconButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Settings feature coming soon!')),
            );
          },
          icon: const Icon(Icons.settings),
          tooltip: 'Settings',
        ),
      ],
      floatingActionButton: _buildFloatingActionButton(),
      body: Scaffold(
        appBar: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.favorite), text: 'Dhikr'),
            Tab(icon: Icon(Icons.menu_book), text: 'Quran'),
            Tab(icon: Icon(Icons.access_time), text: 'Prayer Times'),
            Tab(icon: Icon(Icons.analytics), text: 'Progress'),
          ],
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildDhikrTab(),
            _buildQuranTab(),
            _buildPrayerTimesTab(),
            _buildProgressTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 0: // Dhikr
        return FloatingActionButton(
          onPressed: () => Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const CustomDhikrBuilderScreen(),
            ),
          ),
          tooltip: 'Create Custom Dhikr',
          child: const Icon(Icons.add),
        );
      case 1: // Quran
        return FloatingActionButton(
          onPressed: () => Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AuthenticQuranReaderScreen(),
            ),
          ),
          tooltip: 'Open Quran Reader',
          child: const Icon(Icons.menu_book),
        );
      case 2: // Prayer Times
        return FloatingActionButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Prayer reminder feature coming soon!')),
            );
          },
          tooltip: 'Add Prayer Reminder',
          child: const Icon(Icons.notification_add),
        );
      case 3: // Progress
        return FloatingActionButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Export progress feature coming soon!')),
            );
          },
          tooltip: 'Share Progress',
          child: const Icon(Icons.share),
        );
      default:
        return FloatingActionButton(
          onPressed: () => Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const CustomDhikrBuilderScreen(),
            ),
          ),
          child: const Icon(Icons.add),
        );
    }
  }

  Widget _buildPrayerTimesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final todayPrayers = ref.watch(todayPrayerTimesProvider);
        final nextPrayer = ref.watch(nextPrayerProvider);
        final currentPrayer = ref.watch(currentPrayerProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Next Prayer Card
              if (nextPrayer != null) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.schedule, color: Theme.of(context).primaryColor),
                            const SizedBox(width: 8),
                            Text(
                              'Next Prayer',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${nextPrayer.prayerName} (${nextPrayer.prayerNameArabic})',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        Text(
                          nextPrayer.formattedTime,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        Text(
                          'in ${nextPrayer.timeUntilPrayerFormatted}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Current Prayer Card
              if (currentPrayer != null) ...[
                Card(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.mosque, color: Theme.of(context).primaryColor),
                            const SizedBox(width: 8),
                            Text(
                              'Current Prayer Time',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${currentPrayer.prayerName} (${currentPrayer.prayerNameArabic})',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        Text(
                          currentPrayer.formattedTime,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Today's Prayer Times
              Text(
                'Today\'s Prayer Times',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              
              if (todayPrayers.isEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.schedule_outlined,
                          size: 48,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No prayer times set for today',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Prayer times will be automatically calculated based on your location',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            ElevatedButton.icon(
                              onPressed: _fetchRealPrayerTimes,
                              icon: const Icon(Icons.cloud_download),
                              label: const Text('Fetch from API'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).primaryColor,
                                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                              ),
                            ),
                            OutlinedButton.icon(
                              onPressed: _generateSamplePrayerTimes,
                              icon: const Icon(Icons.schedule),
                              label: const Text('Use Sample'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                ...todayPrayers.map((prayer) => Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: prayer.isPassed 
                          ? Theme.of(context).colorScheme.outline
                          : Theme.of(context).primaryColor,
                      child: Icon(
                        prayer.isPassed ? Icons.check : Icons.schedule,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                    title: Text('${prayer.prayerName} (${prayer.prayerNameArabic})'),
                    subtitle: Text(prayer.formattedTime),
                    trailing: prayer.isUpcoming 
                        ? Text(
                            prayer.timeUntilPrayerFormatted,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).primaryColor,
                            ),
                          )
                        : null,
                  ),
                )),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildDhikrTab() {
    return Consumer(
      builder: (context, ref, child) {
        final dhikrs = ref.watch(dhikrsProvider);
        final categories = ref.watch(dhikrCategoriesProvider);
        final customRoutines = ref.watch(customDhikrRoutinesProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Quick Actions
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _addCustomDhikr,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Custom Dhikr'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: _loadDefaultDhikrs,
                    icon: const Icon(Icons.download),
                    label: const Text('Load Defaults'),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Custom Dhikr Routines Section
              if (customRoutines.isNotEmpty) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'My Custom Dhikr',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    TextButton.icon(
                      onPressed: _addCustomDhikr,
                      icon: const Icon(Icons.add),
                      label: const Text('Create New'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...customRoutines.map((routine) => Card(
                  color: routine.themeColor.withValues(alpha: 0.1),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: routine.themeColor,
                      child: Text(
                        routine.name.isNotEmpty ? routine.name[0].toUpperCase() : 'D',
                        style: TextStyle(color: Theme.of(context).colorScheme.onPrimary, fontWeight: FontWeight.bold),
                      ),
                    ),
                    title: Text(
                      routine.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (routine.description.isNotEmpty)
                          Text(routine.description),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.list, size: 16, color: Colors.grey[600]),
                            const SizedBox(width: 4),
                            Text('${routine.steps.length} steps'),
                            const SizedBox(width: 16),
                            Icon(Icons.check_circle, size: 16, color: Colors.grey[600]),
                            const SizedBox(width: 4),
                            Text('${routine.totalCompletions} completed'),
                          ],
                        ),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) => _handleCustomDhikrAction(value, routine),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'start',
                          child: Row(
                            children: [
                              Icon(Icons.play_arrow, color: Colors.green),
                              SizedBox(width: 8),
                              Text('Start Session'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, color: Colors.blue),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Row(
                            children: [
                              Icon(Icons.copy, color: Colors.orange),
                              SizedBox(width: 8),
                              Text('Duplicate'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete'),
                            ],
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                    onTap: () => _startCustomDhikrSession(routine),
                  ),
                )),
                const SizedBox(height: 24),
              ],

              if (dhikrs.isEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.favorite_outline,
                          size: 48,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No dhikr available',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Add custom dhikr or load default Islamic remembrances',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                // Categories
                ...categories.map((category) {
                  final categoryDhikrs = ref.watch(dhikrsByCategoryProvider(category));
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                          category.toUpperCase(),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      ...categoryDhikrs.map((dhikr) => Card(
                        child: Consumer(
                          builder: (context, ref, child) {
                            return _buildDhikrCard(context, ref, dhikr);
                          },
                        ),
                      )),
                      const SizedBox(height: 8),
                    ],
                  );
                }),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuranTab() {
    return Consumer(
      builder: (context, ref, child) {
        final bookmarks = ref.watch(quranBookmarksProvider);
        final stats = ref.watch(quranStatsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Quick Stats
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      stats.when(
                        data: (statsData) => _buildStatItem('Bookmarks', statsData['totalBookmarks'].toString(), Icons.bookmark),
                        loading: () => _buildStatItem('Bookmarks', '...', Icons.bookmark),
                        error: (_, __) => _buildStatItem('Bookmarks', '0', Icons.bookmark),
                      ),
                      stats.when(
                        data: (statsData) => _buildStatItem('Sessions', statsData['todaySessions'].toString(), Icons.today),
                        loading: () => _buildStatItem('Sessions', '...', Icons.today),
                        error: (_, __) => _buildStatItem('Sessions', '0', Icons.today),
                      ),
                      stats.when(
                        data: (statsData) => _buildStatItem('Time', _formatDuration(statsData['todayReadingTime']), Icons.timer),
                        loading: () => _buildStatItem('Time', '...', Icons.timer),
                        error: (_, __) => _buildStatItem('Time', '0:00', Icons.timer),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Quick Actions
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AuthenticQuranReaderScreen(),
                        ),
                      ),
                      icon: const Icon(Icons.menu_book),
                      label: const Text('Read Quran'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AuthenticQuranReaderScreen(),
                      ),
                    ),
                    icon: const Icon(Icons.search),
                    label: const Text('Search'),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Recent Bookmarks
              if (bookmarks.isNotEmpty) ...[
                Row(
                  children: [
                    Text(
                      'Recent Bookmarks',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AuthenticQuranReaderScreen(),
                        ),
                      ),
                      child: const Text('View All'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                ...bookmarks.take(3).map((bookmark) => Card(
                  child: ListTile(
                    leading: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${bookmark.surahNumber}:${bookmark.verseNumber}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(bookmark.surahName),
                    subtitle: bookmark.note?.isNotEmpty == true ? Text(bookmark.note!) : null,
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AuthenticQuranReaderScreen(),
                      ),
                    ),
                  ),
                )),
              ] else ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.menu_book_outlined,
                          size: 48,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Start reading the Quran',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Begin your spiritual journey with the Holy Quran',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const AuthenticQuranReaderScreen(),
                            ),
                          ),
                          icon: const Icon(Icons.menu_book),
                          label: const Text('Open Quran'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressTab() {
    return Consumer(
      builder: (context, ref, child) {
        final sessionStats = ref.watch(sessionStatsProvider);
        final activeSessions = ref.watch(activeSessionsProvider);
        final completedSessions = ref.watch(completedSessionsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistics Cards
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1.5,
                children: [
                  _buildStatCard(
                    'Total Count',
                    sessionStats['totalCount'].toString(),
                    Icons.favorite,
                    Colors.red,
                  ),
                  _buildStatCard(
                    'Sessions',
                    sessionStats['completedSessions'].toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'Today',
                    sessionStats['todayCount'].toString(),
                    Icons.today,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'Time',
                    '${sessionStats['totalDurationMinutes']}m',
                    Icons.timer,
                    Colors.orange,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Active Sessions
              if (activeSessions.isNotEmpty) ...[
                Text(
                  'Active Sessions',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                ...activeSessions.map((session) => Card(
                  child: ListTile(
                    title: Text('Session ${session.id}'),
                    subtitle: Text('${session.currentCount}/${session.targetCount}'),
                    trailing: Text('${(session.progressPercentage * 100).toInt()}%'),
                    onTap: () => _resumeSession(session),
                  ),
                )),
                const SizedBox(height: 16),
              ],

              // Recent Completed Sessions
              Text(
                'Recent Sessions',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              
              if (completedSessions.isEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.analytics_outlined,
                          size: 48,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No completed sessions yet',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Start a dhikr session to see your progress here',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                ...completedSessions.take(10).map((session) => Card(
                  child: ListTile(
                    leading: const CircleAvatar(
                      child: Icon(Icons.check),
                    ),
                    title: Text('Session ${session.id}'),
                    subtitle: Text(
                      'Completed ${session.currentCount} dhikr in ${session.formattedDuration}',
                    ),
                    trailing: Text(
                      session.endTime != null 
                          ? '${session.endTime!.day}/${session.endTime!.month}'
                          : '',
                    ),
                  ),
                )),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _generateSamplePrayerTimes() {
    // Generate prayer times for Jordan (default location)
    ref.read(locationSettingsProvider.notifier).generatePrayerTimesForToday();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Prayer times generated for Amman, Jordan')),
    );
  }

  Future<void> _fetchRealPrayerTimes() async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 16),
              Text('Fetching prayer times...'),
            ],
          ),
          duration: Duration(seconds: 2),
        ),
      );

      // Fetch from API using the location settings
      final locationSettings = ref.read(locationSettingsProvider);
      final country = locationSettings?.country ?? 'Jordan';
      final city = locationSettings?.city ?? 'amman';

      // Get country code
      final countryCode = _getCountryCode(country);

      // Fetch prayer times from API using the provider method
      await ref.read(locationSettingsProvider.notifier).fetchRealPrayerTimes(
        country: countryCode,
        city: city.toLowerCase().replaceAll(' ', '_'),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Prayer times updated for $city, $country'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to fetch prayer times: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );

        // Fallback to sample data
        _generateSamplePrayerTimes();
      }
    }
  }





  String _getCountryCode(String countryName) {
    final countryCodeMap = {
      'Iraq': 'iq',
      'Jordan': 'jo',
      'Saudi Arabia': 'sa',
      'UAE': 'ae',
      'Egypt': 'eg',
      'Turkey': 'tr',
      'Pakistan': 'pk',
      'India': 'in',
      'Indonesia': 'id',
      'Malaysia': 'my',
      'UK': 'gb',
      'USA': 'us',
      'Canada': 'ca',
    };
    return countryCodeMap[countryName] ?? 'jo';
  }



  void _addCustomDhikr() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CustomDhikrBuilderScreen(),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  void _loadDefaultDhikrs() async {
    try {
      final authenticRoutines = AuthenticAthkar.getPreBuiltRoutines();
      final customDhikrNotifier = ref.read(customDhikrRoutinesProvider.notifier);

      for (final routine in authenticRoutines) {
        await customDhikrNotifier.addRoutine(routine);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Loaded ${authenticRoutines.length} authentic Islamic athkar routines'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading default athkar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildDhikrCard(BuildContext context, WidgetRef ref, dhikr) {
    final preferences = ref.watch(athkarDisplayPreferencesProvider);

    if (preferences == null) {
      return ListTile(
        title: Text(dhikr.arabicText),
        subtitle: Text(dhikr.translation),
        trailing: IconButton(
          onPressed: () => _startDhikrSession(dhikr),
          icon: const Icon(Icons.play_circle_fill),
        ),
      );
    }

    return ListTile(
      title: _buildDhikrText(context, ref, dhikr, preferences),
      trailing: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => _startDhikrSession(dhikr),
            icon: const Icon(Icons.play_circle_fill),
            tooltip: 'Start Dhikr',
          ),
          if (preferences.showRecommendedCount)
            Text('${dhikr.recommendedCount}x'),
        ],
      ),
      isThreeLine: preferences.shouldShowTranslation && preferences.shouldShowTransliteration,
    );
  }

  Widget _buildDhikrText(BuildContext context, WidgetRef ref, dhikr, AthkarDisplayPreferences preferences) {
    final List<Widget> textWidgets = [];

    // Arabic Text
    if (preferences.shouldShowArabic) {
      textWidgets.add(
        Text(
          dhikr.arabicText,
          style: TextStyle(
            fontSize: preferences.arabicFontSize,
            fontFamily: preferences.arabicFontFamily == 'default' ? null : preferences.arabicFontFamily,
            fontWeight: FontWeight.w600,
          ),
          textDirection: preferences.enableRightToLeft ? TextDirection.rtl : TextDirection.ltr,
        ),
      );
    }

    // Transliteration
    if (preferences.shouldShowTransliteration && dhikr.transliteration.isNotEmpty) {
      textWidgets.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            dhikr.transliteration,
            style: TextStyle(
              fontSize: preferences.transliterationFontSize,
              fontStyle: FontStyle.italic,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }

    // Translation
    if (preferences.shouldShowTranslation && dhikr.translation.isNotEmpty) {
      textWidgets.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            dhikr.translation,
            style: TextStyle(
              fontSize: preferences.translationFontSize,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }

    // Reference
    if (preferences.showReference && dhikr.reference != null) {
      textWidgets.add(
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            dhikr.reference!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontStyle: FontStyle.italic,
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: textWidgets,
    );
  }

  void _startDhikrSession(dhikr) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DhikrCounterScreen(dhikr: dhikr),
      ),
    );
  }

  void _resumeSession(session) {
    // Navigate to counter with existing session
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DhikrCounterScreen(
          dhikr: session.dhikr,
          existingSession: session,
        ),
      ),
    );
  }

  void _handleCustomDhikrAction(String action, routine) {
    switch (action) {
      case 'start':
        _startCustomDhikrSession(routine);
        break;
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CustomDhikrBuilderScreen(routine: routine),
          ),
        );
        break;
      case 'duplicate':
        ref.read(customDhikrRoutinesProvider.notifier).duplicateRoutine(routine);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Dhikr routine duplicated')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(routine);
        break;
    }
  }

  void _startCustomDhikrSession(routine) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DhikrRoutineRunnerScreen(
          routine: routine,
          isPreview: false,
        ),
      ),
    );
  }

  void _showDeleteConfirmation(routine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Dhikr Routine'),
        content: Text('Are you sure you want to delete "${routine.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(customDhikrRoutinesProvider.notifier).deleteRoutine(routine.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Dhikr routine deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
