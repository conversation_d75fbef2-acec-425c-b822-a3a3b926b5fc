import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/athkar_settings.dart';
import '../providers/athkar_settings_provider.dart';

/// Athkar settings screen
class AthkarSettingsScreen extends ConsumerStatefulWidget {
  const AthkarSettingsScreen({super.key});

  @override
  ConsumerState<AthkarSettingsScreen> createState() => _AthkarSettingsScreenState();
}

class _AthkarSettingsScreenState extends ConsumerState<AthkarSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(athkarSettingsProvider);
    
    return StandardizedScaffold(
      title: 'Athkar Settings',
      currentRoute: '/athkar/settings',
      showBackButton: true,
      actions: [
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.upload),
                title: Text('Import Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'reset',
              child: ListTile(
                leading: Icon(Icons.restore, color: Colors.red),
                title: Text('Reset to Defaults', style: TextStyle(color: Colors.red)),
                dense: true,
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.display_settings), text: 'Display'),
          Tab(icon: Icon(Icons.touch_app), text: 'Counter'),
          Tab(icon: Icon(Icons.book), text: 'Reading'),
          Tab(icon: Icon(Icons.notifications), text: 'Reminders'),
          Tab(icon: Icon(Icons.security), text: 'Privacy'),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDisplaySettings(settings),
          _buildCounterSettings(settings),
          _buildReadingSettings(settings),
          _buildReminderSettings(settings),
          _buildPrivacySettings(settings),
        ],
      ),
    );
  }

  /// Build display settings tab
  Widget _buildDisplaySettings(AthkarSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Display Mode',
            subtitle: 'How athkar are displayed',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Display Mode
                ListTile(
                  leading: const Icon(Icons.view_list),
                  title: const Text('Display Mode'),
                  subtitle: Text(_getDisplayModeText(settings.displayMode)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showDisplayModePicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Show Translation
                SwitchListTile(
                  secondary: const Icon(Icons.translate),
                  title: const Text('Show Translation'),
                  subtitle: const Text('Display English translation'),
                  value: settings.showTranslation,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateLanguageSettings(
                      showTranslation: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Show Transliteration
                SwitchListTile(
                  secondary: const Icon(Icons.text_fields),
                  title: const Text('Show Transliteration'),
                  subtitle: const Text('Display phonetic pronunciation'),
                  value: settings.showTransliteration,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateLanguageSettings(
                      showTransliteration: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
          
          SizedBox(height: ResponsiveSpacing.lg(context)),
          
          SectionHeader(
            title: 'Text Settings',
            subtitle: 'Font and text appearance',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Arabic Text Size
                ListTile(
                  leading: const Icon(Icons.format_size),
                  title: const Text('Arabic Text Size'),
                  subtitle: Text(_getTextSizeText(settings.arabicTextSize)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showArabicTextSizePicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Translation Text Size
                ListTile(
                  leading: const Icon(Icons.format_size),
                  title: const Text('Translation Text Size'),
                  subtitle: Text(_getTextSizeText(settings.translationTextSize)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showTranslationTextSizePicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Dark Mode
                SwitchListTile(
                  secondary: const Icon(Icons.dark_mode),
                  title: const Text('Dark Mode'),
                  subtitle: const Text('Use dark theme'),
                  value: settings.enableDarkMode,
                  onChanged: (value) {
                    final currentSettings = ref.read(athkarSettingsProvider);
                    ref.read(athkarSettingsProvider.notifier).updateSettings(
                      currentSettings.copyWith(enableDarkMode: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build counter settings tab
  Widget _buildCounterSettings(AthkarSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Counter Style',
            subtitle: 'How the counter is displayed',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Counter Style
                ListTile(
                  leading: const Icon(Icons.touch_app),
                  title: const Text('Counter Style'),
                  subtitle: Text(_getCounterStyleText(settings.counterStyle)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showCounterStylePicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Enable Vibration
                SwitchListTile(
                  secondary: const Icon(Icons.vibration),
                  title: const Text('Vibration'),
                  subtitle: const Text('Vibrate on counter tap'),
                  value: settings.enableVibration,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateCounterSettings(
                      enableVibration: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Enable Sound
                SwitchListTile(
                  secondary: const Icon(Icons.volume_up),
                  title: const Text('Sound'),
                  subtitle: const Text('Play sound on counter tap'),
                  value: settings.enableSound,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateCounterSettings(
                      enableSound: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Show Progress
                SwitchListTile(
                  secondary: const Icon(Icons.show_chart),
                  title: const Text('Show Progress'),
                  subtitle: const Text('Display progress indicator'),
                  value: settings.showProgress,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateCounterSettings(
                      showProgress: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build reading settings tab
  Widget _buildReadingSettings(AthkarSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Reading Features',
            subtitle: 'Reading assistance options',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Auto Scroll
                SwitchListTile(
                  secondary: const Icon(Icons.auto_stories),
                  title: const Text('Auto Scroll'),
                  subtitle: const Text('Automatically scroll text'),
                  value: settings.enableAutoScroll,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateReadingSettings(
                      autoScroll: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Highlight Current Verse
                SwitchListTile(
                  secondary: const Icon(Icons.highlight),
                  title: const Text('Highlight Current'),
                  subtitle: const Text('Highlight current verse'),
                  value: settings.highlightCurrentVerse,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateReadingSettings(
                      highlightVerse: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Night Mode
                SwitchListTile(
                  secondary: const Icon(Icons.nights_stay),
                  title: const Text('Night Mode'),
                  subtitle: const Text('Reduce eye strain in dark'),
                  value: settings.enableNightMode,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateReadingSettings(
                      nightMode: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Keep Screen On
                SwitchListTile(
                  secondary: const Icon(Icons.screen_lock_portrait),
                  title: const Text('Keep Screen On'),
                  subtitle: const Text('Prevent screen from turning off'),
                  value: settings.keepScreenOn,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateReadingSettings(
                      keepScreenOn: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build reminder settings tab
  Widget _buildReminderSettings(AthkarSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Reminder Settings',
            subtitle: 'Configure athkar reminders',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Reminders
                SwitchListTile(
                  secondary: const Icon(Icons.notifications),
                  title: const Text('Enable Reminders'),
                  subtitle: const Text('Get reminded to recite athkar'),
                  value: settings.enableReminders,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updateReminderSettings(
                      enableReminders: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                if (settings.enableReminders) ...[
                  const Divider(),
                  
                  // Reminder Frequency
                  ListTile(
                    leading: const Icon(Icons.schedule),
                    title: const Text('Reminder Frequency'),
                    subtitle: Text(_getReminderFrequencyText(settings.reminderFrequency)),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _showReminderFrequencyPicker(settings),
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Quiet Hours
                  SwitchListTile(
                    secondary: const Icon(Icons.do_not_disturb),
                    title: const Text('Quiet Hours'),
                    subtitle: const Text('Disable reminders during specific hours'),
                    value: settings.enableQuietHours,
                    onChanged: (value) {
                      ref.read(athkarSettingsProvider.notifier).updateReminderSettings(
                        enableQuietHours: value,
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build privacy settings tab
  Widget _buildPrivacySettings(AthkarSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Privacy & Security',
            subtitle: 'Protect your spiritual practice',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Privacy Mode
                SwitchListTile(
                  secondary: const Icon(Icons.privacy_tip),
                  title: const Text('Privacy Mode'),
                  subtitle: const Text('Hide content from screenshots'),
                  value: settings.enablePrivacyMode,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updatePrivacySettings(
                      privacyMode: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Require PIN
                SwitchListTile(
                  secondary: const Icon(Icons.lock),
                  title: const Text('Require PIN'),
                  subtitle: const Text('PIN required to access athkar'),
                  value: settings.requirePinForAccess,
                  onChanged: (value) {
                    if (value) {
                      _setupPin(settings);
                    } else {
                      ref.read(athkarSettingsProvider.notifier).updatePrivacySettings(
                        requirePin: false,
                      );
                    }
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Biometric Auth
                SwitchListTile(
                  secondary: const Icon(Icons.fingerprint),
                  title: const Text('Biometric Authentication'),
                  subtitle: const Text('Use fingerprint/face unlock'),
                  value: settings.enableBiometricAuth,
                  onChanged: (value) {
                    ref.read(athkarSettingsProvider.notifier).updatePrivacySettings(
                      biometric: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportSettings();
        break;
      case 'import':
        _importSettings();
        break;
      case 'reset':
        _resetSettings();
        break;
    }
  }

  /// Export settings
  void _exportSettings() {
    ref.read(athkarSettingsProvider.notifier).exportSettings();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings exported successfully')),
    );
  }

  /// Import settings
  void _importSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality will be implemented')),
    );
  }

  /// Reset settings
  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(athkarSettingsProvider.notifier).resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to defaults')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  /// Get display mode text
  String _getDisplayModeText(AthkarDisplayMode mode) {
    switch (mode) {
      case AthkarDisplayMode.card:
        return 'Card View';
      case AthkarDisplayMode.list:
        return 'List View';
      case AthkarDisplayMode.fullscreen:
        return 'Fullscreen';
    }
  }

  /// Get text size text
  String _getTextSizeText(TextSize size) {
    switch (size) {
      case TextSize.small:
        return 'Small';
      case TextSize.medium:
        return 'Medium';
      case TextSize.large:
        return 'Large';
      case TextSize.extraLarge:
        return 'Extra Large';
    }
  }

  /// Get counter style text
  String _getCounterStyleText(CounterStyle style) {
    switch (style) {
      case CounterStyle.numeric:
        return 'Numeric';
      case CounterStyle.progress:
        return 'Progress Bar';
      case CounterStyle.beads:
        return 'Prayer Beads';
      case CounterStyle.circular:
        return 'Circular';
    }
  }

  /// Get reminder frequency text
  String _getReminderFrequencyText(ReminderFrequency frequency) {
    switch (frequency) {
      case ReminderFrequency.never:
        return 'Never';
      case ReminderFrequency.daily:
        return 'Daily';
      case ReminderFrequency.weekly:
        return 'Weekly';
      case ReminderFrequency.custom:
        return 'Custom';
    }
  }

  /// Show display mode picker
  void _showDisplayModePicker(AthkarSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Display Mode'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AthkarDisplayMode.values.map((mode) {
            return ListTile(
              title: Text(_getDisplayModeText(mode)),
              trailing: settings.displayMode == mode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(athkarSettingsProvider.notifier).updateDisplayMode(mode);
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show Arabic text size picker
  void _showArabicTextSizePicker(AthkarSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Arabic Text Size'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TextSize.values.map((size) {
            return ListTile(
              title: Text(_getTextSizeText(size)),
              trailing: settings.arabicTextSize == size
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(athkarSettingsProvider.notifier).updateTextSizes(
                  arabicSize: size,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show translation text size picker
  void _showTranslationTextSizePicker(AthkarSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Translation Text Size'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TextSize.values.map((size) {
            return ListTile(
              title: Text(_getTextSizeText(size)),
              trailing: settings.translationTextSize == size
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(athkarSettingsProvider.notifier).updateTextSizes(
                  translationSize: size,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show counter style picker
  void _showCounterStylePicker(AthkarSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Counter Style'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: CounterStyle.values.map((style) {
            return ListTile(
              title: Text(_getCounterStyleText(style)),
              trailing: settings.counterStyle == style
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(athkarSettingsProvider.notifier).updateCounterSettings(
                  style: style,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show reminder frequency picker
  void _showReminderFrequencyPicker(AthkarSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reminder Frequency'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ReminderFrequency.values.map((frequency) {
            return ListTile(
              title: Text(_getReminderFrequencyText(frequency)),
              trailing: settings.reminderFrequency == frequency
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(athkarSettingsProvider.notifier).updateReminderSettings(
                  frequency: frequency,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Setup PIN
  void _setupPin(AthkarSettings settings) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('PIN setup will be implemented')),
    );
  }
}
