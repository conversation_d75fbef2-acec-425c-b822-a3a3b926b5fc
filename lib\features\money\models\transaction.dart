enum TransactionType { income, expense, transfer }

class Transaction {
  int id = 0;
  TransactionType type = TransactionType.expense;
  double amount = 0.0;
  String description = '';
  String category = '';
  int accountId = 0;
  int? toAccountId; // For transfers
  DateTime transactionDate = DateTime.now();
  String? notes;
  String? receipt; // Path to receipt image
  List<String> tags = [];
  bool isRecurring = false;
  String? recurringPattern; // daily, weekly, monthly, yearly
  DateTime? nextRecurrence;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Transaction();

  Transaction.create({
    required this.type,
    required this.amount,
    required this.description,
    required this.category,
    required this.accountId,
    required this.userId,
    this.toAccountId,
    DateTime? transactionDate,
    this.notes,
    this.receipt,
    List<String>? tags,
    this.isRecurring = false,
    this.recurringPattern,
    this.nextRecurrence,
  }) {
    this.transactionDate = transactionDate ?? DateTime.now();
    this.tags = tags ?? [];
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  // Convenience getter for date (alias for transactionDate)
  DateTime get date => transactionDate;
  bool get isTransfer => type == TransactionType.transfer;
  bool get isIncome => type == TransactionType.income;
  bool get isExpense => type == TransactionType.expense;

  String get typeDisplayName {
    switch (type) {
      case TransactionType.income:
        return 'Income';
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.transfer:
        return 'Transfer';
    }
  }

  String get formattedAmount {
    final sign = type == TransactionType.income ? '+' : '-';
    return '$sign\$${amount.toStringAsFixed(2)}';
  }

  String get formattedDate {
    return '${transactionDate.day}/${transactionDate.month}/${transactionDate.year}';
  }

  String get formattedDateTime {
    final hour = transactionDate.hour.toString().padLeft(2, '0');
    final minute = transactionDate.minute.toString().padLeft(2, '0');
    return '$formattedDate $hour:$minute';
  }

  bool get isToday {
    final now = DateTime.now();
    return transactionDate.year == now.year &&
           transactionDate.month == now.month &&
           transactionDate.day == now.day;
  }

  bool get isThisWeek {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    return transactionDate.isAfter(weekStart);
  }

  bool get isThisMonth {
    final now = DateTime.now();
    return transactionDate.year == now.year &&
           transactionDate.month == now.month;
  }

  bool get isThisYear {
    final now = DateTime.now();
    return transactionDate.year == now.year;
  }

  bool get hasReceipt {
    return receipt != null && receipt!.isNotEmpty;
  }

  double get signedAmount {
    switch (type) {
      case TransactionType.income:
        return amount;
      case TransactionType.expense:
        return -amount;
      case TransactionType.transfer:
        return 0; // Transfers don't affect net worth
    }
  }

  void updateTransaction({
    TransactionType? type,
    double? amount,
    String? description,
    String? category,
    int? accountId,
    int? toAccountId,
    DateTime? transactionDate,
    String? notes,
    String? receipt,
    List<String>? tags,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? nextRecurrence,
  }) {
    if (type != null) this.type = type;
    if (amount != null) this.amount = amount;
    if (description != null) this.description = description;
    if (category != null) this.category = category;
    if (accountId != null) this.accountId = accountId;
    if (toAccountId != null) this.toAccountId = toAccountId;
    if (transactionDate != null) this.transactionDate = transactionDate;
    if (notes != null) this.notes = notes;
    if (receipt != null) this.receipt = receipt;
    if (tags != null) this.tags = tags;
    if (isRecurring != null) this.isRecurring = isRecurring;
    if (recurringPattern != null) this.recurringPattern = recurringPattern;
    if (nextRecurrence != null) this.nextRecurrence = nextRecurrence;
    
    updateTimestamp();
  }

  bool containsSearchTerm(String searchTerm) {
    final term = searchTerm.toLowerCase();
    return description.toLowerCase().contains(term) ||
           category.toLowerCase().contains(term) ||
           (notes?.toLowerCase().contains(term) ?? false) ||
           tags.any((tag) => tag.toLowerCase().contains(term));
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'amount': amount,
      'description': description,
      'category': category,
      'accountId': accountId,
      'toAccountId': toAccountId,
      'transactionDate': transactionDate.toIso8601String(),
      'notes': notes,
      'receipt': receipt,
      'tags': tags,
      'isRecurring': isRecurring,
      'recurringPattern': recurringPattern,
      'nextRecurrence': nextRecurrence?.toIso8601String(),
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Transaction.fromJson(Map<String, dynamic> json) {
    final transaction = Transaction();
    transaction.id = json['id'] ?? 0;
    transaction.type = TransactionType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => TransactionType.expense,
    );
    transaction.amount = (json['amount'] ?? 0.0).toDouble();
    transaction.description = json['description'] ?? '';
    transaction.category = json['category'] ?? '';
    transaction.accountId = json['accountId'] ?? 0;
    transaction.toAccountId = json['toAccountId'];
    transaction.transactionDate = DateTime.parse(json['transactionDate']);
    transaction.notes = json['notes'];
    transaction.receipt = json['receipt'];
    transaction.tags = List<String>.from(json['tags'] ?? []);
    transaction.isRecurring = json['isRecurring'] ?? false;
    transaction.recurringPattern = json['recurringPattern'];
    transaction.nextRecurrence = json['nextRecurrence'] != null 
        ? DateTime.parse(json['nextRecurrence']) 
        : null;
    transaction.userId = json['userId'] ?? '';
    transaction.createdAt = DateTime.parse(json['createdAt']);
    transaction.updatedAt = DateTime.parse(json['updatedAt']);
    transaction.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    transaction.isDeleted = json['isDeleted'] ?? false;
    transaction.syncId = json['syncId'];
    return transaction;
  }
}
