import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../models/calculation_history.dart';
import '../providers/calculation_history_provider.dart';

class ScientificCalculatorScreen extends ConsumerStatefulWidget {
  const ScientificCalculatorScreen({super.key});

  @override
  ConsumerState<ScientificCalculatorScreen> createState() => _ScientificCalculatorScreenState();
}

class _ScientificCalculatorScreenState extends ConsumerState<ScientificCalculatorScreen> {
  String _display = '0';
  String _expression = '';
  String _currentInput = '';
  double _result = 0;
  bool _isRadians = true;
  bool _showSecondaryFunctions = false;
  final List<String> _history = [];

  // Mathematical constants
  static const double _e = 2.718281828459045;
  static const double _pi = 3.141592653589793;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scientific Calculator'),
        actions: [
          IconButton(
            onPressed: _toggleAngleMode,
            icon: Text(
              _isRadians ? 'RAD' : 'DEG',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          IconButton(
            onPressed: _showHistory,
            icon: const Icon(Icons.history),
          ),
        ],
      ),
      body: Column(
        children: [
          // Display
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Expression/History
                  if (_expression.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Text(
                        _expression,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontFamily: 'monospace',
                        ),
                        textAlign: TextAlign.end,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],

                  // Main display
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      reverse: true,
                      child: Text(
                        _display,
                        style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                          fontSize: 36,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'monospace',
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Function toggle
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _showSecondaryFunctions = !_showSecondaryFunctions;
                      });
                    },
                    child: Text(_showSecondaryFunctions ? '2nd' : '1st'),
                  ),
                ),
              ],
            ),
          ),
          
          // Button grid
          Expanded(
            flex: 5,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  // Row 1: Functions
                  Expanded(
                    child: Row(
                      children: [
                        _buildFunctionButton('C', _clear, Colors.red),
                        _buildFunctionButton('±', _toggleSign, Colors.orange),
                        _buildFunctionButton('%', () => _inputOperator('%'), Colors.orange),
                        _buildFunctionButton('÷', () => _inputOperator('/'), Colors.orange),
                        _buildFunctionButton('⌫', _backspace, Colors.orange),
                      ],
                    ),
                  ),
                  
                  // Row 2: Scientific functions
                  Expanded(
                    child: Row(
                      children: [
                        _buildFunctionButton(
                          _showSecondaryFunctions ? 'sin⁻¹' : 'sin',
                          _showSecondaryFunctions ? _asin : _sin,
                          Colors.blue,
                        ),
                        _buildFunctionButton(
                          _showSecondaryFunctions ? 'cos⁻¹' : 'cos',
                          _showSecondaryFunctions ? _acos : _cos,
                          Colors.blue,
                        ),
                        _buildFunctionButton(
                          _showSecondaryFunctions ? 'tan⁻¹' : 'tan',
                          _showSecondaryFunctions ? _atan : _tan,
                          Colors.blue,
                        ),
                        _buildFunctionButton(
                          _showSecondaryFunctions ? 'log' : 'ln',
                          _showSecondaryFunctions ? _log10 : _ln,
                          Colors.blue,
                        ),
                        _buildFunctionButton('×', () => _inputOperator('*'), Colors.orange),
                      ],
                    ),
                  ),
                  
                  // Row 3: More functions and numbers
                  Expanded(
                    child: Row(
                      children: [
                        _buildFunctionButton(
                          _showSecondaryFunctions ? 'eˣ' : 'x²',
                          _showSecondaryFunctions ? _exp : _square,
                          Colors.blue,
                        ),
                        _buildFunctionButton(
                          _showSecondaryFunctions ? '10ˣ' : 'x³',
                          _showSecondaryFunctions ? _pow10 : _cube,
                          Colors.blue,
                        ),
                        _buildFunctionButton(
                          _showSecondaryFunctions ? 'xʸ' : '√',
                          _showSecondaryFunctions ? _power : _sqrt,
                          Colors.blue,
                        ),
                        _buildNumberButton('7'),
                        _buildFunctionButton('-', () => _inputOperator('-'), Colors.orange),
                      ],
                    ),
                  ),
                  
                  // Row 4: Constants and numbers
                  Expanded(
                    child: Row(
                      children: [
                        _buildFunctionButton('π', () => _inputConstant(_pi), Colors.purple),
                        _buildFunctionButton('e', () => _inputConstant(_e), Colors.purple),
                        _buildFunctionButton('!', _factorial, Colors.blue),
                        _buildNumberButton('8'),
                        _buildNumberButton('9'),
                      ],
                    ),
                  ),
                  
                  // Row 5: Numbers
                  Expanded(
                    child: Row(
                      children: [
                        _buildFunctionButton('(', () => _inputOperator('('), Colors.grey),
                        _buildFunctionButton(')', () => _inputOperator(')'), Colors.grey),
                        _buildNumberButton('4'),
                        _buildNumberButton('5'),
                        _buildNumberButton('6'),
                      ],
                    ),
                  ),
                  
                  // Row 6: Numbers and operations
                  Expanded(
                    child: Row(
                      children: [
                        _buildFunctionButton('+', () => _inputOperator('+'), Colors.orange),
                        _buildNumberButton('1'),
                        _buildNumberButton('2'),
                        _buildNumberButton('3'),
                        _buildFunctionButton('=', _calculate, Colors.green),
                      ],
                    ),
                  ),
                  
                  // Row 7: Zero and decimal
                  Expanded(
                    child: Row(
                      children: [
                        _buildNumberButton('0'),
                        _buildFunctionButton('.', () => _inputDecimal(), Colors.grey),
                        _buildFunctionButton('π', () => _inputConstant(_pi), Colors.purple),
                        _buildFunctionButton('e', () => _inputConstant(_e), Colors.purple),
                        _buildFunctionButton('=', _calculate, Colors.green),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNumberButton(String text) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.all(2),
        child: ElevatedButton(
          onPressed: () => _inputNumber(text),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.onSurface,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            text,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  Widget _buildFunctionButton(String text, VoidCallback onPressed, Color color) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.all(2),
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            text,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  void _inputNumber(String number) {
    setState(() {
      if (_display == '0' || _display == 'Error') {
        _display = number;
      } else {
        _display += number;
      }
    });
  }

  void _inputOperator(String operator) {
    setState(() {
      if (_expression.isEmpty && _display != '0') {
        _expression = _display + ' ' + operator + ' ';
      } else if (_expression.isNotEmpty) {
        _expression += _display + ' ' + operator + ' ';
      }
      _display = '0';
    });
  }

  void _inputDecimal() {
    setState(() {
      if (!_display.contains('.')) {
        _display += '.';
      }
    });
  }

  void _inputConstant(double value) {
    setState(() {
      _display = value.toString();
    });
  }

  void _clear() {
    setState(() {
      _display = '0';
      _expression = '';
      _result = 0;
    });
  }

  void _backspace() {
    setState(() {
      if (_display.length > 1) {
        _display = _display.substring(0, _display.length - 1);
      } else {
        _display = '0';
      }
    });
  }

  void _toggleSign() {
    setState(() {
      if (_display != '0') {
        if (_display.startsWith('-')) {
          _display = _display.substring(1);
        } else {
          _display = '-' + _display;
        }
      }
    });
  }

  void _toggleAngleMode() {
    setState(() {
      _isRadians = !_isRadians;
    });
  }

  // Mathematical functions
  void _sin() => _applyFunction((x) => math.sin(_isRadians ? x : x * math.pi / 180));
  void _cos() => _applyFunction((x) => math.cos(_isRadians ? x : x * math.pi / 180));
  void _tan() => _applyFunction((x) => math.tan(_isRadians ? x : x * math.pi / 180));
  void _asin() => _applyFunction((x) => _isRadians ? math.asin(x) : math.asin(x) * 180 / math.pi);
  void _acos() => _applyFunction((x) => _isRadians ? math.acos(x) : math.acos(x) * 180 / math.pi);
  void _atan() => _applyFunction((x) => _isRadians ? math.atan(x) : math.atan(x) * 180 / math.pi);
  void _ln() => _applyFunction((x) => math.log(x));
  void _log10() => _applyFunction((x) => math.log(x) / math.ln10);
  void _exp() => _applyFunction((x) => math.exp(x));
  void _pow10() => _applyFunction((x) => math.pow(10, x).toDouble());
  void _square() => _applyFunction((x) => x * x);
  void _cube() => _applyFunction((x) => x * x * x);
  void _sqrt() => _applyFunction((x) => math.sqrt(x));
  void _factorial() => _applyFunction((x) => _calculateFactorial(x.toInt()));

  void _applyFunction(double Function(double) function) {
    try {
      final value = double.parse(_display);
      final result = function(value);
      setState(() {
        _display = _formatResult(result);
      });
    } catch (e) {
      setState(() {
        _display = 'Error';
      });
    }
  }

  double _calculateFactorial(int n) {
    if (n < 0) throw ArgumentError('Factorial of negative number');
    if (n > 170) throw ArgumentError('Number too large');
    double result = 1;
    for (int i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  }

  void _power() {
    // This would need to be implemented with a two-operand system
    _inputOperator('^');
  }

  void _calculate() {
    try {
      final fullExpression = _expression + _display;
      final result = _evaluateExpression(fullExpression);
      
      // Save to history
      _saveCalculation(fullExpression, result);
      
      setState(() {
        _display = _formatResult(result);
        _expression = '';
        _result = result;
      });
    } catch (e) {
      setState(() {
        _display = 'Error';
        _expression = '';
      });
    }
  }

  double _evaluateExpression(String expression) {
    try {
      // Replace operators and evaluate
      expression = expression.replaceAll('×', '*');
      expression = expression.replaceAll('÷', '/');

      // Simple expression evaluator for basic operations
      final parts = expression.split(' ');
      if (parts.length >= 3) {
        final operand1 = double.parse(parts[0]);
        final operator = parts[1];
        final operand2 = double.parse(parts[2]);

        switch (operator) {
          case '+':
            return operand1 + operand2;
          case '-':
            return operand1 - operand2;
          case '*':
            return operand1 * operand2;
          case '/':
            if (operand2 != 0) {
              return operand1 / operand2;
            } else {
              throw Exception('Division by zero');
            }
          case '^':
            return math.pow(operand1, operand2).toDouble();
          default:
            throw Exception('Unknown operator');
        }
      }

      // If no operation, return the current result
      return _result;
    } catch (e) {
      throw Exception('Invalid expression');
    }
  }

  String _formatResult(double result) {
    if (result == result.toInt()) {
      return result.toInt().toString();
    } else {
      return result.toStringAsFixed(8).replaceAll(RegExp(r'0*$'), '').replaceAll(RegExp(r'\.$'), '');
    }
  }

  void _saveCalculation(String expression, double result) {
    final calculation = CalculationHistory.create(
      type: CalculationType.scientific,
      expression: expression,
      result: result.toString(),
      userId: '', // Will be set by provider
    );
    
    ref.read(calculationHistoryProvider.notifier).addCalculation(calculation);
    
    setState(() {
      _history.add('$expression = ${_formatResult(result)}');
    });
  }

  void _showHistory() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 300,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calculation History',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _history.isEmpty
                  ? const Center(child: Text('No calculations yet'))
                  : ListView.builder(
                      itemCount: _history.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          title: Text(_history[_history.length - 1 - index]),
                          onTap: () {
                            Navigator.pop(context);
                            // Could implement loading previous calculation
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
