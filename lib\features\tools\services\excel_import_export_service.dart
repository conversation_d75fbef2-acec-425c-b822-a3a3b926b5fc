import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:excel/excel.dart' as excel_lib;
import '../models/spreadsheet_cell.dart' as models;

/// Service for importing and exporting Excel files
class ExcelImportExportService {
  static final ExcelImportExportService _instance = ExcelImportExportService._internal();
  factory ExcelImportExportService() => _instance;
  ExcelImportExportService._internal();

  /// Import Excel file and return cell data
  Future<ExcelImportResult> importExcelFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.extension?.toLowerCase() == 'csv') {
          return await _importCsvFileNew(file);
        } else {
          return await _importExcelFile(file);
        }
      }

      return ExcelImportResult.error('No file selected');
    } catch (e) {
      return ExcelImportResult.error('Error importing file: $e');
    }
  }

  /// Import CSV file with new result format
  Future<ExcelImportResult> _importCsvFileNew(PlatformFile file) async {
    try {
      if (file.bytes == null) {
        throw Exception('File bytes are null');
      }

      final content = utf8.decode(file.bytes!);
      final lines = content.split('\n');
      final cellData = <String, models.SpreadsheetCell>{};
      int totalCells = 0;

      for (int rowIndex = 0; rowIndex < lines.length; rowIndex++) {
        final line = lines[rowIndex].trim();
        if (line.isEmpty) continue;

        final values = parseCsvLine(line);

        for (int colIndex = 0; colIndex < values.length; colIndex++) {
          final address = _getCellAddress(rowIndex, colIndex);
          final value = values[colIndex];
          totalCells++;

          cellData[address] = models.SpreadsheetCell(
            address: address,
            value: parseValue(value),
            type: _determineCellType(value),
            style: const models.CellStyle(),
          );
        }
      }

      return ExcelImportResult.success(
        cells: cellData,
        sheets: ['Sheet1'],
        namedRanges: {},
        metadata: ExcelImportMetadata(
          fileName: file.name,
          fileSize: file.bytes!.length,
          sheetCount: 1,
          cellCount: totalCells,
          formulaCount: 0,
          hasNamedRanges: false,
        ),
      );
    } catch (e) {
      return ExcelImportResult.error('Failed to parse CSV: $e');
    }
  }

  /// Import CSV file
  Future<Map<String, models.SpreadsheetCell>> _importCsvFile(PlatformFile file) async {
    final cellData = <String, models.SpreadsheetCell>{};

    if (file.bytes != null) {
      final content = String.fromCharCodes(file.bytes!);
      final lines = content.split('\n');

      for (int rowIndex = 0; rowIndex < lines.length; rowIndex++) {
        final line = lines[rowIndex].trim();
        if (line.isEmpty) continue;

        final cells = _parseCsvLine(line);
        for (int colIndex = 0; colIndex < cells.length; colIndex++) {
          final cellAddress = '${String.fromCharCode(65 + colIndex)}${rowIndex + 1}';
          final value = cells[colIndex].trim();

          if (value.isNotEmpty) {
            cellData[cellAddress] = models.SpreadsheetCell(
              address: cellAddress,
              value: value,
              type: _determineCellType(value),
              style: const models.CellStyle(),
            );
          }
        }
      }
    }

    return cellData;
  }

  /// Import Excel file with real data extraction
  Future<ExcelImportResult> _importExcelFile(PlatformFile file) async {
    try {
      if (file.bytes == null) {
        throw Exception('File bytes are null');
      }

      final excel = excel_lib.Excel.decodeBytes(file.bytes!);

      // Process the first sheet (or default sheet)
      String? sheetName = excel.tables.keys.isNotEmpty ? excel.tables.keys.first : null;
      if (sheetName == null) {
        throw Exception('No sheets found in Excel file');
      }

      final sheet = excel.tables[sheetName];
      if (sheet == null) {
        throw Exception('Sheet not found: $sheetName');
      }

      final cellData = <String, models.SpreadsheetCell>{};
      final namedRanges = <String, String>{};
      int formulaCount = 0;
      int totalCells = 0;

      // Process each row and cell
      for (int rowIndex = 0; rowIndex < sheet.rows.length; rowIndex++) {
        final row = sheet.rows[rowIndex];

        for (int colIndex = 0; colIndex < row.length; colIndex++) {
          final cell = row[colIndex];

          if (cell != null && cell.value != null) {
            totalCells++;
            final cellAddress = _getCellAddress(rowIndex, colIndex);
            final cellValue = cell.value;

            // Determine cell type and value
            dynamic processedValue;
            models.CellType cellType;
            String? formula;
            models.CellStyle cellStyle = const models.CellStyle();

            // Process cell value based on type
            final cellValueStr = cellValue?.toString() ?? '';

            if (cellValueStr.startsWith('=')) {
              formula = cellValueStr;
              processedValue = cellValueStr;
              cellType = models.CellType.formula;
              formulaCount++;
            } else if (cellValue is String) {
              processedValue = cellValue;
              cellType = models.CellType.text;
            } else if (cellValue is int || cellValue is double) {
              processedValue = cellValue;
              cellType = models.CellType.number;
            } else if (cellValue is bool) {
              processedValue = cellValue;
              cellType = models.CellType.boolean;
            } else if (cellValue is DateTime) {
              processedValue = (cellValue as DateTime).toIso8601String();
              cellType = models.CellType.text;
            } else {
              processedValue = cellValueStr;
              cellType = _determineCellType(cellValueStr);
            }

            // Extract basic formatting (simplified for now)
            cellStyle = const models.CellStyle();

            cellData[cellAddress] = models.SpreadsheetCell(
              address: cellAddress,
              value: processedValue,
              formula: formula,
              type: cellType,
              style: cellStyle,
            );
          }
        }
      }

      // Extract named ranges if available
      // Note: The excel package has limited support for named ranges
      // This is a placeholder for future enhancement

      return ExcelImportResult.success(
        cells: cellData,
        sheets: excel.tables.keys.toList(),
        namedRanges: namedRanges,
        metadata: ExcelImportMetadata(
          fileName: file.name,
          fileSize: file.bytes!.length,
          sheetCount: excel.tables.length,
          cellCount: totalCells,
          formulaCount: formulaCount,
          hasNamedRanges: namedRanges.isNotEmpty,
        ),
      );

    } catch (e) {
      return ExcelImportResult.error('Failed to import Excel file: $e');
    }
  }

  /// Get cell address from row and column indices
  String _getCellAddress(int rowIndex, int colIndex) {
    String columnName = '';
    int col = colIndex;

    while (col >= 0) {
      columnName = String.fromCharCode(65 + (col % 26)) + columnName;
      col = (col ~/ 26) - 1;
      if (col < 0) break;
    }

    return '$columnName${rowIndex + 1}';
  }

  /// Parse CSV line handling quoted values
  List<String> _parseCsvLine(String line) {
    final cells = <String>[];
    final buffer = StringBuffer();
    bool inQuotes = false;
    
    for (int i = 0; i < line.length; i++) {
      final char = line[i];
      
      if (char == '"') {
        inQuotes = !inQuotes;
      } else if (char == ',' && !inQuotes) {
        cells.add(buffer.toString());
        buffer.clear();
      } else {
        buffer.write(char);
      }
    }
    
    cells.add(buffer.toString());
    return cells;
  }

  /// Determine cell type from value
  models.CellType _determineCellType(String value) {
    if (value.startsWith('=')) return models.CellType.formula;
    if (double.tryParse(value) != null) return models.CellType.number;
    if (value.toLowerCase() == 'true' || value.toLowerCase() == 'false') {
      return models.CellType.boolean;
    }
    return models.CellType.text;
  }

  /// Export cell data to CSV
  Future<bool> exportToCsv(Map<String, models.SpreadsheetCell> cellData, String fileName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.csv');
      
      final csvContent = _generateCsvContent(cellData);
      await file.writeAsString(csvContent);
      
      return true;
    } catch (e) {
      print('Error exporting to CSV: $e');
      return false;
    }
  }

  /// Generate CSV content from cell data
  String _generateCsvContent(Map<String, models.SpreadsheetCell> cellData) {
    final buffer = StringBuffer();
    
    // Find the maximum row and column
    int maxRow = 0;
    int maxCol = 0;
    
    for (final address in cellData.keys) {
      final coords = _parseAddress(address);
      if (coords != null) {
        maxRow = maxRow > coords.row ? maxRow : coords.row;
        maxCol = maxCol > coords.col ? maxCol : coords.col;
      }
    }
    
    // Generate CSV rows
    for (int row = 1; row <= maxRow; row++) {
      final rowCells = <String>[];
      
      for (int col = 0; col < maxCol; col++) {
        final address = '${String.fromCharCode(65 + col)}$row';
        final cell = cellData[address];
        
        if (cell != null) {
          String value = cell.value?.toString() ?? '';
          
          // Escape quotes and wrap in quotes if contains comma
          if (value.contains(',') || value.contains('"') || value.contains('\n')) {
            value = '"${value.replaceAll('"', '""')}"';
          }
          
          rowCells.add(value);
        } else {
          rowCells.add('');
        }
      }
      
      buffer.writeln(rowCells.join(','));
    }
    
    return buffer.toString();
  }

  /// Parse cell address to row/column coordinates
  CellCoordinates? _parseAddress(String address) {
    final match = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(address);
    if (match == null) return null;
    
    final colStr = match.group(1)!;
    final rowStr = match.group(2)!;
    
    int col = 0;
    for (int i = 0; i < colStr.length; i++) {
      col = col * 26 + (colStr.codeUnitAt(i) - 64);
    }
    
    return CellCoordinates(
      row: int.parse(rowStr),
      col: col - 1,
    );
  }

  /// Generate sample data for testing
  Map<String, models.SpreadsheetCell> _generateSampleData() {
    return {
      'A1': models.SpreadsheetCell(
        address: 'A1',
        value: 'Product',
        type: models.CellType.text,
        style: const models.CellStyle(),
      ),
      'B1': models.SpreadsheetCell(
        address: 'B1',
        value: 'Price',
        type: models.CellType.text,
        style: const models.CellStyle(),
      ),
      'C1': models.SpreadsheetCell(
        address: 'C1',
        value: 'Quantity',
        type: models.CellType.text,
        style: const models.CellStyle(),
      ),
      'D1': models.SpreadsheetCell(
        address: 'D1',
        value: 'Total',
        type: models.CellType.text,
        style: const models.CellStyle(),
      ),
      'A2': models.SpreadsheetCell(
        address: 'A2',
        value: 'Laptop',
        type: models.CellType.text,
        style: const models.CellStyle(),
      ),
      'B2': models.SpreadsheetCell(
        address: 'B2',
        value: 999.99,
        type: models.CellType.number,
        style: const models.CellStyle(),
      ),
      'C2': models.SpreadsheetCell(
        address: 'C2',
        value: 5,
        type: models.CellType.number,
        style: const models.CellStyle(),
      ),
      'D2': models.SpreadsheetCell(
        address: 'D2',
        value: 4999.95,
        formula: '=B2*C2',
        type: models.CellType.formula,
        style: const models.CellStyle(),
      ),
      'A3': models.SpreadsheetCell(
        address: 'A3',
        value: 'Mouse',
        type: models.CellType.text,
        style: const models.CellStyle(),
      ),
      'B3': models.SpreadsheetCell(
        address: 'B3',
        value: 29.99,
        type: models.CellType.number,
        style: const models.CellStyle(),
      ),
      'C3': models.SpreadsheetCell(
        address: 'C3',
        value: 10,
        type: models.CellType.number,
        style: const models.CellStyle(),
      ),
      'D3': models.SpreadsheetCell(
        address: 'D3',
        value: 299.90,
        formula: '=B3*C3',
        type: models.CellType.formula,
        style: const models.CellStyle(),
      ),
      'D4': models.SpreadsheetCell(
        address: 'D4',
        value: 5299.85,
        formula: '=SUM(D2:D3)',
        type: models.CellType.formula,
        style: const models.CellStyle(),
      ),
    };
  }

  /// Export cell data to Excel (.xlsx) format
  Future<bool> exportToExcel(Map<String, models.SpreadsheetCell> cellData, String fileName) async {
    try {
      final excel = excel_lib.Excel.createExcel();
      final sheetName = 'Sheet1';

      // Remove default sheet and create new one
      excel.delete('Sheet1');
      excel.copy(sheetName, sheetName);
      final sheet = excel[sheetName];

      // Find the maximum row and column
      int maxRow = 0;
      int maxCol = 0;

      for (final address in cellData.keys) {
        final coords = _parseAddress(address);
        if (coords != null) {
          maxRow = maxRow > coords.row ? maxRow : coords.row;
          maxCol = maxCol > coords.col ? maxCol : coords.col;
        }
      }

      // Populate Excel sheet with data
      for (final entry in cellData.entries) {
        final coords = _parseAddress(entry.key);
        if (coords != null) {
          final cell = entry.value;

          // Set cell value based on type
          dynamic cellValue;
          if (cell.type == models.CellType.formula && cell.formula != null) {
            // For formulas, set the calculated value and try to preserve formula
            cellValue = cell.value;
          } else if (cell.type == models.CellType.number) {
            cellValue = cell.value is String ? double.tryParse(cell.value) ?? cell.value : cell.value;
          } else if (cell.type == models.CellType.boolean) {
            cellValue = cell.value is String ? cell.value.toLowerCase() == 'true' : cell.value;
          } else {
            cellValue = cell.value?.toString() ?? '';
          }

          // Set the cell value
          sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: coords.col,
            rowIndex: coords.row - 1,
          )).value = cellValue;

          // Apply basic styling if available
          if (cell.style.bold || cell.style.italic) {
            // Note: The excel package has limited styling support
            // This is a basic implementation - styling would be applied here
          }
        }
      }

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.xlsx');
      final excelBytes = excel.encode();

      if (excelBytes != null) {
        await file.writeAsBytes(excelBytes);
        return true;
      }

      return false;
    } catch (e) {
      print('Error exporting to Excel: $e');
      return false;
    }
  }

  /// Export with advanced Excel features
  Future<bool> exportToExcelAdvanced(
    Map<String, models.SpreadsheetCell> cellData,
    String fileName, {
    bool preserveFormulas = true,
    bool includeFormatting = true,
    String sheetName = 'Sheet1',
  }) async {
    try {
      final excel = excel_lib.Excel.createExcel();

      // Create or get sheet
      if (excel.tables.containsKey(sheetName)) {
        excel.delete(sheetName);
      }
      excel.copy(sheetName, sheetName);
      final sheet = excel[sheetName];

      // Process each cell with advanced features
      for (final entry in cellData.entries) {
        final coords = _parseAddress(entry.key);
        if (coords != null) {
          final cell = entry.value;
          final excelCell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: coords.col,
            rowIndex: coords.row - 1,
          ));

          // Set value based on type and preserve formulas
          if (cell.type == models.CellType.formula && cell.formula != null && preserveFormulas) {
            // For formulas, set the calculated value (formula preservation has limited support)
            excelCell.value = cell.value;
          } else {
            // Set calculated value
            dynamic cellValue = cell.value;

            if (cell.type == models.CellType.number && cellValue is String) {
              cellValue = double.tryParse(cellValue) ?? cellValue;
            } else if (cell.type == models.CellType.boolean && cellValue is String) {
              cellValue = cellValue.toLowerCase() == 'true';
            }

            excelCell.value = cellValue;
          }

          // Apply formatting if requested
          if (includeFormatting) {
            _applyExcelCellFormatting(excelCell, cell.style);
          }
        }
      }

      // Save file
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName.xlsx');
      final excelBytes = excel.encode();

      if (excelBytes != null) {
        await file.writeAsBytes(excelBytes);
        return true;
      }

      return false;
    } catch (e) {
      print('Error exporting advanced Excel: $e');
      return false;
    }
  }

  /// Apply cell formatting to Excel cell
  void _applyExcelCellFormatting(dynamic excelCell, models.CellStyle style) {
    // Note: The excel package has limited formatting support
    // This is a placeholder for when more advanced formatting is needed

    // Basic text alignment
    // if (style.textAlign != models.TextAlign.left) {
    //   // Apply alignment when supported
    // }

    // Background color
    // if (style.backgroundColor != null) {
    //   // Apply background color when supported
    // }

    // Text color
    // if (style.textColor != null) {
    //   // Apply text color when supported
    // }
  }

  /// Get file export path
  Future<String> getExportPath(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/$fileName';
  }

  /// Load sample data for user experimentation
  Map<String, models.SpreadsheetCell> loadSampleData() {
    return _generateSampleData();
  }
}

/// Cell coordinates helper class
class CellCoordinates {
  final int row;
  final int col;

  const CellCoordinates({
    required this.row,
    required this.col,
  });
}

/// Excel import result
class ExcelImportResult {
  final bool success;
  final String? error;
  final Map<String, models.SpreadsheetCell>? cells;
  final List<String>? sheets;
  final Map<String, String>? namedRanges;
  final ExcelImportMetadata? metadata;

  const ExcelImportResult._({
    required this.success,
    this.error,
    this.cells,
    this.sheets,
    this.namedRanges,
    this.metadata,
  });

  factory ExcelImportResult.success({
    required Map<String, models.SpreadsheetCell> cells,
    required List<String> sheets,
    required Map<String, String> namedRanges,
    required ExcelImportMetadata metadata,
  }) {
    return ExcelImportResult._(
      success: true,
      cells: cells,
      sheets: sheets,
      namedRanges: namedRanges,
      metadata: metadata,
    );
  }

  factory ExcelImportResult.error(String error) {
    return ExcelImportResult._(
      success: false,
      error: error,
    );
  }
}

/// Excel import metadata
class ExcelImportMetadata {
  final String fileName;
  final int fileSize;
  final int sheetCount;
  final int cellCount;
  final int formulaCount;
  final bool hasNamedRanges;

  const ExcelImportMetadata({
    required this.fileName,
    required this.fileSize,
    required this.sheetCount,
    required this.cellCount,
    required this.formulaCount,
    required this.hasNamedRanges,
  });
}

/// Helper methods for ExcelImportExportService
extension ExcelImportExportServiceHelpers on ExcelImportExportService {
  /// Parse value from string
  dynamic parseValue(String value) {
    if (value.isEmpty) return '';

    // Try to parse as number
    final number = double.tryParse(value);
    if (number != null) return number;

    // Try to parse as boolean
    if (value.toLowerCase() == 'true') return true;
    if (value.toLowerCase() == 'false') return false;

    // Try to parse as date
    final date = DateTime.tryParse(value);
    if (date != null) return date;

    // Default to string
    return value;
  }

  /// Parse CSV line handling quotes and commas
  List<String> parseCsvLine(String line) {
    final values = <String>[];
    final buffer = StringBuffer();
    bool inQuotes = false;

    for (int i = 0; i < line.length; i++) {
      final char = line[i];

      if (char == '"') {
        if (inQuotes && i + 1 < line.length && line[i + 1] == '"') {
          // Escaped quote
          buffer.write('"');
          i++; // Skip next quote
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
        }
      } else if (char == ',' && !inQuotes) {
        // End of value
        values.add(buffer.toString());
        buffer.clear();
      } else {
        buffer.write(char);
      }
    }

    // Add last value
    values.add(buffer.toString());

    return values;
  }
}
