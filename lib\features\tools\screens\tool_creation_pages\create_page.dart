import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../providers/tool_creation_provider.dart';

/// Page A - Create: Start from scratch, Import Excel, Use template, Clone existing tool
class CreatePage extends ConsumerStatefulWidget {
  const CreatePage({super.key});

  @override
  ConsumerState<CreatePage> createState() => _CreatePageState();
}

class _CreatePageState extends ConsumerState<CreatePage> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedCategory = 'Calculator';

  final List<String> _categories = [
    'Calculator',
    'Converter',
    'Financial',
    'Health',
    'Engineering',
    'Business',
    'Personal',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    final creationState = ref.read(toolCreationProvider);
    _nameController.text = creationState.toolName;
    _descriptionController.text = creationState.toolDescription;
    _selectedCategory = creationState.toolCategory.isNotEmpty 
        ? creationState.toolCategory 
        : _categories.first;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Creation Options
          Text(
            'How would you like to create your tool?',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          
          // Creation Method Cards
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: _getCrossAxisCount(context),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              _buildCreationMethodCard(
                context,
                'Start from Scratch',
                'Build a completely new tool',
                Icons.add_circle_outline,
                Colors.blue,
                () => _selectCreationMethod('scratch'),
              ),
              _buildCreationMethodCard(
                context,
                'Import Excel File',
                'Convert Excel spreadsheet to tool',
                Icons.upload_file,
                Colors.green,
                () => _importExcelFile(),
              ),
              _buildCreationMethodCard(
                context,
                'Use Template',
                'Start with a pre-built template',
                Icons.description_outlined,
                Colors.orange,
                () => _selectTemplate(),
              ),
              _buildCreationMethodCard(
                context,
                'Clone Existing Tool',
                'Copy and modify existing tool',
                Icons.copy_outlined,
                Colors.purple,
                () => _cloneExistingTool(),
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Tool Information Form
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tool Information',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Tool Name
                  TextField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Tool Name *',
                      hintText: 'Enter a descriptive name for your tool',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      ref.read(toolCreationProvider.notifier).updateToolName(value);
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Tool Description
                  TextField(
                    controller: _descriptionController,
                    maxLines: 3,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      hintText: 'Describe what your tool does',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      ref.read(toolCreationProvider.notifier).updateToolDescription(value);
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Category Selection
                  DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                    ),
                    items: _categories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                      ref.read(toolCreationProvider.notifier).updateToolCategory(value!);
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Recent Templates Section
          Text(
            'Popular Templates',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: _getCrossAxisCount(context),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.8,
            children: [
              _buildTemplateCard(
                context,
                'Basic Calculator',
                'Simple arithmetic operations',
                Icons.calculate,
                Colors.blue,
                () => _useTemplate('basic_calculator'),
              ),
              _buildTemplateCard(
                context,
                'Budget Tracker',
                'Track income and expenses',
                Icons.account_balance_wallet,
                Colors.green,
                () => _useTemplate('budget_tracker'),
              ),
              _buildTemplateCard(
                context,
                'Unit Converter',
                'Convert between units',
                Icons.swap_horiz,
                Colors.orange,
                () => _useTemplate('unit_converter'),
              ),
              _buildTemplateCard(
                context,
                'Loan Calculator',
                'Calculate loan payments',
                Icons.home,
                Colors.purple,
                () => _useTemplate('loan_calculator'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCreationMethodCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTemplateCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 24,
                  color: color,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectCreationMethod(String method) {
    ref.read(toolCreationProvider.notifier).setCreationMethod(method);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected: ${method == 'scratch' ? 'Start from Scratch' : method}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  Future<void> _importExcelFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        
        // Process Excel file
        ref.read(toolCreationProvider.notifier).importExcelFile(file.path!);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Excel file "${file.name}" imported successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error importing Excel file: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _selectTemplate() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Template'),
        content: const Text('Browse available templates to start your tool creation.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to template gallery
            },
            child: const Text('Browse Templates'),
          ),
        ],
      ),
    );
  }

  void _cloneExistingTool() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clone Existing Tool'),
        content: const Text('Select an existing tool to clone and modify.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to saved tools for cloning
            },
            child: const Text('Select Tool'),
          ),
        ],
      ),
    );
  }

  void _useTemplate(String templateId) {
    ref.read(toolCreationProvider.notifier).loadTemplate(templateId);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Template "$templateId" loaded'),
        backgroundColor: Colors.green,
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }
}
