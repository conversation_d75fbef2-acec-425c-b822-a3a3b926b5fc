class Note {
  int id = 0;
  String title = '';
  String content = '';
  String color = 'default';
  bool isPinned = false;
  bool isFavorite = false;
  List<String> tags = [];
  String? imagePath;
  String userId = '';

  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Note();

  Note.create({
    required this.title,
    required this.content,
    required this.userId,
    this.color = 'default',
    this.isPinned = false,
    this.isFavorite = false,
    List<String>? tags,
  }) {
    this.tags = tags ?? [];
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void updateContent({
    String? title,
    String? content,
    String? color,
    bool? isPinned,
    bool? isFavorite,
    List<String>? tags,
    String? imagePath,
  }) {
    if (title != null) this.title = title;
    if (content != null) this.content = content;
    if (color != null) this.color = color;
    if (isPinned != null) this.isPinned = isPinned;
    if (isFavorite != null) this.isFavorite = isFavorite;
    if (tags != null) this.tags = tags;
    if (imagePath != null) this.imagePath = imagePath;
    
    updateTimestamp();
  }

  void addTag(String tag) {
    if (!tags.contains(tag)) {
      tags.add(tag);
      updateTimestamp();
    }
  }

  void removeTag(String tag) {
    if (tags.remove(tag)) {
      updateTimestamp();
    }
  }

  void togglePin() {
    isPinned = !isPinned;
    updateTimestamp();
  }

  void toggleFavorite() {
    isFavorite = !isFavorite;
    updateTimestamp();
  }

  bool containsSearchTerm(String searchTerm) {
    final term = searchTerm.toLowerCase();
    return title.toLowerCase().contains(term) ||
           content.toLowerCase().contains(term) ||
           tags.any((tag) => tag.toLowerCase().contains(term));
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'color': color,
      'isPinned': isPinned,
      'isFavorite': isFavorite,
      'tags': tags,
      'imagePath': imagePath,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Note.fromJson(Map<String, dynamic> json) {
    final note = Note();
    note.id = json['id'] ?? 0;
    note.title = json['title'] ?? '';
    note.content = json['content'] ?? '';
    note.color = json['color'] ?? 'default';
    note.isPinned = json['isPinned'] ?? false;
    note.isFavorite = json['isFavorite'] ?? false;
    note.tags = List<String>.from(json['tags'] ?? []);
    note.imagePath = json['imagePath'];
    note.userId = json['userId'] ?? '';
    note.createdAt = DateTime.parse(json['createdAt']);
    note.updatedAt = DateTime.parse(json['updatedAt']);
    note.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    note.isDeleted = json['isDeleted'] ?? false;
    note.syncId = json['syncId'];
    return note;
  }
}
