import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../shared/widgets/base_screen.dart';
import '../providers/transaction_provider.dart';
import '../providers/account_provider.dart';
import '../providers/category_provider.dart';
import '../models/category.dart';

class FinancialReportsScreen extends ConsumerStatefulWidget {
  const FinancialReportsScreen({super.key});

  @override
  ConsumerState<FinancialReportsScreen> createState() => _FinancialReportsScreenState();
}

class _FinancialReportsScreenState extends ConsumerState<FinancialReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTimeRange _selectedDateRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 30)),
    end: DateTime.now(),
  );

  String _selectedPeriod = 'Last 30 Days';
  List<int> _selectedAccountIds = [];
  String _sortBy = 'amount';
  bool _sortDescending = true;

  final List<String> _periodOptions = [
    'Last 7 Days',
    'Last 30 Days',
    'Last 90 Days',
    'This Month',
    'Last Month',
    'This Year',
    'Last Year',
    'Custom Range',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MoneyBaseScreen(
      title: 'Financial Reports',
      actions: [
        IconButton(
          onPressed: _showFilterOptions,
          icon: const Icon(Icons.filter_list),
          tooltip: 'Filter Options',
        ),
        IconButton(
          onPressed: _selectDateRange,
          icon: const Icon(Icons.date_range),
          tooltip: 'Select Date Range',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.pie_chart), text: 'Overview'),
          Tab(icon: Icon(Icons.category), text: 'Categories'),
          Tab(icon: Icon(Icons.trending_up), text: 'Trends'),
          Tab(icon: Icon(Icons.account_balance), text: 'Accounts'),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildCategoriesTab(),
          _buildTrendsTab(),
          _buildAccountsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return Consumer(
      builder: (context, ref, child) {
        final transactionStats = ref.watch(transactionStatsProvider);
        final accountStats = ref.watch(accountStatsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date Range Display
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.date_range),
                      const SizedBox(width: 8),
                      Text(
                        'Period: ${_formatDate(_selectedDateRange.start)} - ${_formatDate(_selectedDateRange.end)}',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Summary Cards
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'Total Income',
                      '\$${transactionStats['totalIncome']?.toStringAsFixed(2) ?? '0.00'}',
                      Icons.trending_up,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildSummaryCard(
                      'Total Expenses',
                      '\$${transactionStats['totalExpenses']?.toStringAsFixed(2) ?? '0.00'}',
                      Icons.trending_down,
                      Colors.red,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'Net Worth',
                      '\$${accountStats['netWorth']?.toStringAsFixed(2) ?? '0.00'}',
                      Icons.account_balance_wallet,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildSummaryCard(
                      'Cash Flow',
                      '\$${((transactionStats['totalIncome'] ?? 0.0) - (transactionStats['totalExpenses'] ?? 0.0)).toStringAsFixed(2)}',
                      Icons.compare_arrows,
                      ((transactionStats['totalIncome'] ?? 0.0) - (transactionStats['totalExpenses'] ?? 0.0)) >= 0 
                          ? Colors.green 
                          : Colors.red,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Income vs Expenses Chart
              Text(
                'Income vs Expenses',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SizedBox(
                    height: 200,
                    child: _buildIncomeExpenseChart(transactionStats),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Account Balances
              Text(
                'Account Balances',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              Consumer(
                builder: (context, ref, child) {
                  final accounts = ref.watch(accountsProvider);
                  
                  return Column(
                    children: accounts.map((account) {
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: account.color,
                            child: Icon(
                              account.icon,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          title: Text(account.name),
                          subtitle: Text(account.type.name.toUpperCase()),
                          trailing: Text(
                            '\$${account.balance.toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: account.balance >= 0 ? Colors.green : Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final categories = ref.watch(categoriesProvider);
        final categoryStats = ref.watch(categoryTransactionStatsProvider);

        final expenseCategories = categories
            .where((c) => c.type == CategoryType.expense)
            .where((c) => categoryStats[c.id]?['totalAmount'] != null && categoryStats[c.id]!['totalAmount'] > 0)
            .toList();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Expense Categories Pie Chart
              Text(
                'Expenses by Category',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              if (expenseCategories.isNotEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      height: 300,
                      child: _buildCategoryPieChart(expenseCategories, categoryStats),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Category Details
                Text(
                  'Category Breakdown',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                
                ...expenseCategories.map((category) {
                  final stats = categoryStats[category.id];
                  final amount = stats?['totalAmount'] ?? 0.0;
                  final transactionCount = stats?['transactionCount'] ?? 0;
                  
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: category.color,
                        child: Icon(category.icon, color: Colors.white, size: 20),
                      ),
                      title: Text(category.name),
                      subtitle: Text('$transactionCount transactions'),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '\$${amount.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (category.budgetLimit > 0) ...[
                            Text(
                              '${((amount / category.budgetLimit) * 100).toStringAsFixed(0)}% of budget',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: amount > category.budgetLimit ? Colors.red : Colors.grey,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                }),
              ] else ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      children: [
                        Icon(
                          Icons.pie_chart_outline,
                          size: 64,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No expense data available',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Add some transactions to see category breakdown',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildTrendsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final transactions = ref.watch(transactionsProvider);
        final filteredTransactions = transactions.where((t) =>
          t.transactionDate.isAfter(_selectedDateRange.start) &&
          t.transactionDate.isBefore(_selectedDateRange.end.add(const Duration(days: 1)))
        ).toList();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Spending Trends',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              if (filteredTransactions.isEmpty)
                const Center(
                  child: Text('No transaction data available for trends analysis'),
                )
              else
                _buildTrendsChart(filteredTransactions),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final accounts = ref.watch(accountsProvider);
        final transactions = ref.watch(transactionsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Account Analysis',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              if (accounts.isEmpty)
                const Center(
                  child: Text('No accounts available for analysis'),
                )
              else
                ...accounts.map((account) => Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: account.color,
                      child: Icon(
                        account.icon,
                        color: Colors.white,
                      ),
                    ),
                    title: Text(account.name),
                    subtitle: Text('Balance: ${account.formattedBalance}'),
                    trailing: Text(
                      '${transactions.where((t) => t.accountId == account.id).length} transactions',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                )),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTrendsChart(List<dynamic> transactions) {
    // Simple trend visualization
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Monthly Spending Trend',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('Trend visualization would be displayed here'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeExpenseChart(Map<String, dynamic> stats) {
    final income = stats['totalIncome'] ?? 0.0;
    final expenses = stats['totalExpenses'] ?? 0.0;
    
    if (income == 0 && expenses == 0) {
      return const Center(
        child: Text('No transaction data available'),
      );
    }

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: [income, expenses].reduce((a, b) => a > b ? a : b) * 1.2,
        barTouchData: BarTouchData(enabled: false),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                switch (value.toInt()) {
                  case 0:
                    return const Text('Income');
                  case 1:
                    return const Text('Expenses');
                  default:
                    return const Text('');
                }
              },
            ),
          ),
          leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: [
          BarChartGroupData(
            x: 0,
            barRods: [
              BarChartRodData(
                toY: income,
                color: Colors.green,
                width: 40,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 1,
            barRods: [
              BarChartRodData(
                toY: expenses,
                color: Colors.red,
                width: 40,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryPieChart(List<MoneyCategory> categories, Map<int, Map<String, dynamic>> categoryStats) {
    final sections = categories.map((category) {
      final amount = categoryStats[category.id]?['totalAmount'] ?? 0.0;
      final total = categories.fold(0.0, (sum, c) => sum + (categoryStats[c.id]?['totalAmount'] ?? 0.0));
      final percentage = total > 0 ? (amount / total) * 100 : 0.0;
      
      return PieChartSectionData(
        color: category.color,
        value: amount,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: 40,
        sectionsSpace: 2,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _showFilterOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.date_range),
              title: const Text('Date Range'),
              subtitle: Text('${_selectedDateRange.start.day}/${_selectedDateRange.start.month}/${_selectedDateRange.start.year} - ${_selectedDateRange.end.day}/${_selectedDateRange.end.month}/${_selectedDateRange.end.year}'),
              onTap: () {
                Navigator.of(context).pop();
                _selectDateRange();
              },
            ),
            ListTile(
              leading: const Icon(Icons.category),
              title: const Text('Categories'),
              subtitle: const Text('Filter by specific categories'),
              onTap: () {
                Navigator.of(context).pop();
                _showCategoryFilter();
              },
            ),
            ListTile(
              leading: const Icon(Icons.account_balance),
              title: const Text('Accounts'),
              subtitle: const Text('Filter by specific accounts'),
              onTap: () {
                Navigator.of(context).pop();
                _showAccountFilter();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showCategoryFilter() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Category filter applied')),
    );
  }

  void _showAccountFilter() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Account filter applied')),
    );
  }
}
