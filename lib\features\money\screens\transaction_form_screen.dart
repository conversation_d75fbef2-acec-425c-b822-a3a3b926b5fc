import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/transaction.dart';
import '../providers/transaction_provider.dart';
import '../providers/account_provider.dart';

class TransactionFormScreen extends ConsumerStatefulWidget {
  final Transaction? transaction;

  const TransactionFormScreen({
    super.key,
    this.transaction,
  });

  @override
  ConsumerState<TransactionFormScreen> createState() => _TransactionFormScreenState();
}

class _TransactionFormScreenState extends ConsumerState<TransactionFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _amountController;
  late TextEditingController _descriptionController;
  late TextEditingController _notesController;
  late TextEditingController _tagsController;
  
  TransactionType _selectedType = TransactionType.expense;
  String _selectedCategory = 'Food & Dining';
  int? _selectedAccountId;
  int? _selectedToAccountId;
  DateTime _selectedDate = DateTime.now();
  bool _isRecurring = false;
  String? _recurringPattern;
  DateTime? _recurringEndDate;
  double _exchangeRate = 1.0;
  bool _useCustomExchangeRate = false;
  
  final List<String> _expenseCategories = [
    'Food & Dining',
    'Transportation',
    'Shopping',
    'Entertainment',
    'Bills & Utilities',
    'Healthcare',
    'Education',
    'Travel',
    'Personal Care',
    'Home & Garden',
    'Gifts & Donations',
    'Other',
  ];
  
  final List<String> _incomeCategories = [
    'Salary',
    'Freelance',
    'Business',
    'Investment',
    'Rental',
    'Gift',
    'Refund',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    
    _amountController = TextEditingController();
    _descriptionController = TextEditingController();
    _notesController = TextEditingController();
    _tagsController = TextEditingController();
    
    if (widget.transaction != null) {
      _loadTransactionData();
    }
  }

  void _loadTransactionData() {
    final transaction = widget.transaction!;
    _selectedType = transaction.type;
    _amountController.text = transaction.amount.toString();
    _descriptionController.text = transaction.description;
    _selectedCategory = transaction.category;
    _selectedAccountId = transaction.accountId;
    _selectedToAccountId = transaction.toAccountId;
    _selectedDate = transaction.transactionDate;
    _notesController.text = transaction.notes ?? '';
    _tagsController.text = transaction.tags.join(', ');
    _isRecurring = transaction.isRecurring;
    _recurringPattern = transaction.recurringPattern;
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final accounts = ref.watch(activeAccountsProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.transaction == null ? 'Add Transaction' : 'Edit Transaction'),
        actions: [
          if (widget.transaction != null)
            IconButton(
              onPressed: _deleteTransaction,
              icon: const Icon(Icons.delete),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction Type
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Transaction Type',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      SegmentedButton<TransactionType>(
                        segments: const [
                          ButtonSegment(
                            value: TransactionType.income,
                            label: Text('Income'),
                            icon: Icon(Icons.arrow_upward),
                          ),
                          ButtonSegment(
                            value: TransactionType.expense,
                            label: Text('Expense'),
                            icon: Icon(Icons.arrow_downward),
                          ),
                          ButtonSegment(
                            value: TransactionType.transfer,
                            label: Text('Transfer'),
                            icon: Icon(Icons.swap_horiz),
                          ),
                        ],
                        selected: {_selectedType},
                        onSelectionChanged: (Set<TransactionType> selection) {
                          setState(() {
                            _selectedType = selection.first;
                            _selectedCategory = _selectedType == TransactionType.income
                                ? _incomeCategories.first
                                : _expenseCategories.first;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Amount and Description
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Transaction Details',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      
                      // Amount
                      TextFormField(
                        controller: _amountController,
                        decoration: const InputDecoration(
                          labelText: 'Amount',
                          prefixText: '\$',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter an amount';
                          }
                          if (double.tryParse(value) == null || double.parse(value) <= 0) {
                            return 'Please enter a valid amount';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a description';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Category and Accounts
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Category & Account',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      
                      // Category
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Category',
                          border: OutlineInputBorder(),
                        ),
                        items: (_selectedType == TransactionType.income 
                            ? _incomeCategories 
                            : _expenseCategories).map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // From Account
                      DropdownButtonFormField<int>(
                        value: _selectedAccountId,
                        decoration: InputDecoration(
                          labelText: _selectedType == TransactionType.transfer ? 'From Account' : 'Account',
                          border: const OutlineInputBorder(),
                        ),
                        items: accounts.map((account) {
                          return DropdownMenuItem(
                            value: account.id,
                            child: Text('${account.name} (${account.formattedBalance})'),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedAccountId = value;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Please select an account';
                          }
                          return null;
                        },
                      ),
                      
                      // To Account (for transfers)
                      if (_selectedType == TransactionType.transfer) ...[
                        const SizedBox(height: 16),
                        DropdownButtonFormField<int>(
                          value: _selectedToAccountId,
                          decoration: const InputDecoration(
                            labelText: 'To Account',
                            border: OutlineInputBorder(),
                          ),
                          items: accounts.where((account) => account.id != _selectedAccountId).map((account) {
                            return DropdownMenuItem(
                              value: account.id,
                              child: Text('${account.name} (${account.formattedBalance})'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedToAccountId = value;
                              _checkCurrencyDifference();
                            });
                          },
                          validator: (value) {
                            if (_selectedType == TransactionType.transfer && value == null) {
                              return 'Please select a destination account';
                            }
                            return null;
                          },
                        ),

                        // Exchange Rate (if different currencies)
                        if (_showExchangeRate()) ...[
                          const SizedBox(height: 16),
                          SwitchListTile(
                            title: const Text('Custom Exchange Rate'),
                            subtitle: Text('Current rate: ${_exchangeRate.toStringAsFixed(4)}'),
                            value: _useCustomExchangeRate,
                            onChanged: (value) {
                              setState(() {
                                _useCustomExchangeRate = value;
                                if (!value) {
                                  _exchangeRate = 1.0; // Reset to default
                                }
                              });
                            },
                            contentPadding: EdgeInsets.zero,
                          ),

                          if (_useCustomExchangeRate) ...[
                            const SizedBox(height: 8),
                            TextFormField(
                              initialValue: _exchangeRate.toString(),
                              decoration: InputDecoration(
                                labelText: 'Exchange Rate',
                                border: const OutlineInputBorder(),
                                helperText: '1 ${_getFromCurrency()} = ? ${_getToCurrency()}',
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              onChanged: (value) {
                                final rate = double.tryParse(value);
                                if (rate != null && rate > 0) {
                                  setState(() {
                                    _exchangeRate = rate;
                                  });
                                }
                              },
                            ),
                          ],
                        ],
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Date and Additional Info
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Additional Information',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      
                      // Date
                      ListTile(
                        leading: const Icon(Icons.calendar_today),
                        title: const Text('Date'),
                        subtitle: Text('${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}'),
                        onTap: _selectDate,
                        contentPadding: EdgeInsets.zero,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Notes
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'Notes (optional)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Tags
                      TextFormField(
                        controller: _tagsController,
                        decoration: const InputDecoration(
                          labelText: 'Tags (comma separated)',
                          border: OutlineInputBorder(),
                          hintText: 'e.g., work, lunch, important',
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Recurring
                      SwitchListTile(
                        title: const Text('Recurring Transaction'),
                        subtitle: const Text('Set up automatic recurring'),
                        value: _isRecurring,
                        onChanged: (value) {
                          setState(() {
                            _isRecurring = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                      
                      if (_isRecurring) ...[
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          value: _recurringPattern,
                          decoration: const InputDecoration(
                            labelText: 'Frequency',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(value: 'daily', child: Text('Daily')),
                            DropdownMenuItem(value: 'weekly', child: Text('Weekly')),
                            DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                            DropdownMenuItem(value: 'yearly', child: Text('Yearly')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _recurringPattern = value;
                            });
                          },
                        ),

                        const SizedBox(height: 16),

                        // Recurring End Date
                        ListTile(
                          leading: const Icon(Icons.event_repeat),
                          title: const Text('End Date (Optional)'),
                          subtitle: Text(_recurringEndDate != null
                              ? '${_recurringEndDate!.day}/${_recurringEndDate!.month}/${_recurringEndDate!.year}'
                              : 'No end date'),
                          onTap: _selectRecurringEndDate,
                          contentPadding: EdgeInsets.zero,
                          trailing: _recurringEndDate != null
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    setState(() {
                                      _recurringEndDate = null;
                                    });
                                  },
                                )
                              : null,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveTransaction,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      widget.transaction == null ? 'Add Transaction' : 'Update Transaction',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _selectRecurringEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _recurringEndDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (date != null) {
      setState(() {
        _recurringEndDate = date;
      });
    }
  }

  bool _showExchangeRate() {
    if (_selectedType != TransactionType.transfer) return false;
    if (_selectedAccountId == null || _selectedToAccountId == null) return false;

    final accounts = ref.read(activeAccountsProvider);
    final fromAccount = accounts.firstWhere((a) => a.id == _selectedAccountId);
    final toAccount = accounts.firstWhere((a) => a.id == _selectedToAccountId);

    return fromAccount.currency != toAccount.currency;
  }

  String _getFromCurrency() {
    if (_selectedAccountId == null) return 'USD';
    final accounts = ref.read(activeAccountsProvider);
    final fromAccount = accounts.firstWhere((a) => a.id == _selectedAccountId);
    return fromAccount.currency;
  }

  String _getToCurrency() {
    if (_selectedToAccountId == null) return 'USD';
    final accounts = ref.read(activeAccountsProvider);
    final toAccount = accounts.firstWhere((a) => a.id == _selectedToAccountId);
    return toAccount.currency;
  }

  void _checkCurrencyDifference() {
    if (_showExchangeRate() && !_useCustomExchangeRate) {
      // Set a default exchange rate or fetch from API
      setState(() {
        _exchangeRate = _getDefaultExchangeRate(_getFromCurrency(), _getToCurrency());
      });
    }
  }

  double _getDefaultExchangeRate(String fromCurrency, String toCurrency) {
    // Simplified exchange rates - in a real app, fetch from API
    final rates = {
      'USD_EUR': 0.85,
      'USD_GBP': 0.73,
      'USD_SAR': 3.75,
      'USD_AED': 3.67,
      'EUR_USD': 1.18,
      'EUR_GBP': 0.86,
      'GBP_USD': 1.37,
      'GBP_EUR': 1.16,
      'SAR_USD': 0.27,
      'AED_USD': 0.27,
    };

    final key = '${fromCurrency}_$toCurrency';
    return rates[key] ?? 1.0;
  }

  void _saveTransaction() {
    if (!_formKey.currentState!.validate()) return;
    
    final amount = double.parse(_amountController.text);
    final description = _descriptionController.text.trim();
    final notes = _notesController.text.trim();
    final tags = _tagsController.text
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
    
    if (widget.transaction == null) {
      // Create new transaction
      final transaction = Transaction.create(
        type: _selectedType,
        amount: amount,
        description: description,
        category: _selectedCategory,
        accountId: _selectedAccountId!,
        userId: '', // Will be set by provider
        toAccountId: _selectedToAccountId,
        transactionDate: _selectedDate,
        notes: notes.isEmpty ? null : notes,
        tags: tags,
        isRecurring: _isRecurring,
        recurringPattern: _recurringPattern,
      );
      
      ref.read(transactionsProvider.notifier).addTransaction(transaction);
    } else {
      // Update existing transaction
      widget.transaction!.updateTransaction(
        type: _selectedType,
        amount: amount,
        description: description,
        category: _selectedCategory,
        accountId: _selectedAccountId!,
        toAccountId: _selectedToAccountId,
        transactionDate: _selectedDate,
        notes: notes.isEmpty ? null : notes,
        tags: tags,
        isRecurring: _isRecurring,
        recurringPattern: _recurringPattern,
      );
      
      ref.read(transactionsProvider.notifier).updateTransaction(widget.transaction!);
    }
    
    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.transaction == null 
              ? 'Transaction added successfully' 
              : 'Transaction updated successfully',
        ),
      ),
    );
  }

  void _deleteTransaction() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: const Text('Are you sure you want to delete this transaction?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(transactionsProvider.notifier).deleteTransaction(widget.transaction!.id);
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close form
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Transaction deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
