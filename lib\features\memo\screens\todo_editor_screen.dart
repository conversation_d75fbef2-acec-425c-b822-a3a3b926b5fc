import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/todo.dart';
import '../providers/todo_provider.dart';
import '../../../shared/providers/user_provider.dart';

class TodoEditorScreen extends ConsumerStatefulWidget {
  final int? todoId;

  const TodoEditorScreen({super.key, this.todoId});

  @override
  ConsumerState<TodoEditorScreen> createState() => _TodoEditorScreenState();
}

class _TodoEditorScreenState extends ConsumerState<TodoEditorScreen> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagController = TextEditingController();
  
  Todo? _currentTodo;
  DateTime? _dueDate;
  DateTime? _reminderTime;
  TodoPriority _priority = TodoPriority.medium;
  RecurrenceType _recurrenceType = RecurrenceType.none;
  int _recurrenceInterval = 1;
  String _category = 'general';
  List<String> _tags = [];
  bool _isLoading = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadTodo();
    _titleController.addListener(_onTextChanged);
    _descriptionController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  void _loadTodo() {
    if (widget.todoId != null) {
      final todos = ref.read(todosProvider);
      _currentTodo = todos.firstWhere(
        (todo) => todo.id == widget.todoId,
        orElse: () => Todo(),
      );
      
      if (_currentTodo != null && _currentTodo!.id != 0) {
        _titleController.text = _currentTodo!.title;
        _descriptionController.text = _currentTodo!.description;
        _dueDate = _currentTodo!.dueDate;
        _reminderTime = _currentTodo!.reminderTime;
        _priority = _currentTodo!.priority;
        _recurrenceType = _currentTodo!.recurrenceType;
        _recurrenceInterval = _currentTodo!.recurrenceInterval;
        _category = _currentTodo!.category;
        _tags = List.from(_currentTodo!.tags);
      }
    }
  }

  Future<void> _saveTodo() async {
    if (_isLoading || _titleController.text.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = ref.read(userProfileProvider)?.id.toString() ?? '';
      
      if (_currentTodo != null && _currentTodo!.id != 0) {
        // Update existing todo
        _currentTodo!.updateTodo(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          dueDate: _dueDate,
          priority: _priority,
          recurrenceType: _recurrenceType,
          recurrenceInterval: _recurrenceInterval,
          category: _category,
          reminderTime: _reminderTime,
          tags: _tags,
        );
        await ref.read(todosProvider.notifier).updateTodo(_currentTodo!);
      } else {
        // Create new todo
        await ref.read(todosProvider.notifier).createTodo(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          dueDate: _dueDate,
          priority: _priority,
          recurrenceType: _recurrenceType,
          recurrenceInterval: _recurrenceInterval,
          category: _category,
          reminderTime: _reminderTime,
          tags: _tags,
        );
      }

      setState(() {
        _hasChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Todo saved successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving todo: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDueDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_dueDate ?? DateTime.now()),
      );
      
      if (time != null) {
        setState(() {
          _dueDate = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
          _hasChanges = true;
        });
      }
    }
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
        _hasChanges = true;
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
      _hasChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final categories = ref.watch(todoCategoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.todoId != null ? 'Edit Todo' : 'New Todo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_hasChanges)
            IconButton(
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              onPressed: _isLoading ? null : _saveTodo,
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title Field
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title *',
                border: OutlineInputBorder(),
              ),
              textInputAction: TextInputAction.next,
            ),
            const SizedBox(height: 16),

            // Description Field
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              textInputAction: TextInputAction.newline,
            ),
            const SizedBox(height: 16),

            // Due Date
            Card(
              child: ListTile(
                leading: const Icon(Icons.calendar_today),
                title: const Text('Due Date'),
                subtitle: Text(
                  _dueDate != null
                      ? '${_dueDate!.day}/${_dueDate!.month}/${_dueDate!.year} at ${_dueDate!.hour.toString().padLeft(2, '0')}:${_dueDate!.minute.toString().padLeft(2, '0')}'
                      : 'No due date set',
                ),
                trailing: _dueDate != null
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _dueDate = null;
                            _hasChanges = true;
                          });
                        },
                      )
                    : null,
                onTap: _selectDueDate,
              ),
            ),
            const SizedBox(height: 16),

            // Priority
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Priority',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SegmentedButton<TodoPriority>(
                      segments: const [
                        ButtonSegment(
                          value: TodoPriority.low,
                          label: Text('Low'),
                          icon: Icon(Icons.keyboard_arrow_down),
                        ),
                        ButtonSegment(
                          value: TodoPriority.medium,
                          label: Text('Medium'),
                          icon: Icon(Icons.remove),
                        ),
                        ButtonSegment(
                          value: TodoPriority.high,
                          label: Text('High'),
                          icon: Icon(Icons.keyboard_arrow_up),
                        ),
                        ButtonSegment(
                          value: TodoPriority.urgent,
                          label: Text('Urgent'),
                          icon: Icon(Icons.priority_high),
                        ),
                      ],
                      selected: {_priority},
                      onSelectionChanged: (Set<TodoPriority> selection) {
                        setState(() {
                          _priority = selection.first;
                          _hasChanges = true;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Category
            DropdownButtonFormField<String>(
              value: _category,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: categories.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category.toUpperCase()),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _category = value;
                    _hasChanges = true;
                  });
                }
              },
            ),
            const SizedBox(height: 16),

            // Tags Section
            if (_tags.isNotEmpty) ...[
              Text(
                'Tags',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _tags.map((tag) {
                  return Chip(
                    label: Text(tag),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeTag(tag),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],

            // Add Tag Field
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _tagController,
                    decoration: const InputDecoration(
                      labelText: 'Add tag',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (_) => _addTag(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: _addTag,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
