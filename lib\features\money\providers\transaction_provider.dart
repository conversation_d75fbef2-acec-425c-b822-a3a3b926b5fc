import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/transaction.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Transaction list provider
final transactionsProvider = StateNotifierProvider<TransactionNotifier, List<Transaction>>((ref) {
  return TransactionNotifier(ref);
});

class TransactionNotifier extends StateNotifier<List<Transaction>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  TransactionNotifier(this.ref) : super([]) {
    loadTransactions();
  }

  Future<void> loadTransactions() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final transactions = await _db.getAllTransactions(userId: userProfile.id.toString());
      state = transactions;
    }
  }

  Future<void> addTransaction(Transaction transaction) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      transaction.userId = userProfile.id.toString();
      final savedTransaction = await _db.saveTransaction(transaction);
      state = [savedTransaction, ...state];
    }
  }

  Future<void> updateTransaction(Transaction transaction) async {
    final savedTransaction = await _db.saveTransaction(transaction);
    state = state.map((t) => t.id == savedTransaction.id ? savedTransaction : t).toList();
  }

  Future<void> deleteTransaction(int transactionId) async {
    await _db.deleteTransaction(transactionId);
    state = state.where((t) => t.id != transactionId).toList();
  }

  List<Transaction> getTransactionsByAccount(int accountId) {
    return state.where((t) => t.accountId == accountId || t.toAccountId == accountId).toList();
  }

  List<Transaction> getTransactionsByCategory(String category) {
    return state.where((t) => t.category == category).toList();
  }

  List<Transaction> getTransactionsByType(TransactionType type) {
    return state.where((t) => t.type == type).toList();
  }

  List<Transaction> getTransactionsByDateRange(DateTime start, DateTime end) {
    return state.where((t) => 
      t.transactionDate.isAfter(start) && t.transactionDate.isBefore(end)
    ).toList();
  }

  List<Transaction> getTodayTransactions() {
    return state.where((t) => t.isToday).toList();
  }

  List<Transaction> getThisWeekTransactions() {
    return state.where((t) => t.isThisWeek).toList();
  }

  List<Transaction> getThisMonthTransactions() {
    return state.where((t) => t.isThisMonth).toList();
  }

  double getTotalIncome({DateTime? start, DateTime? end}) {
    var transactions = state.where((t) => t.type == TransactionType.income);
    
    if (start != null && end != null) {
      transactions = transactions.where((t) => 
        t.transactionDate.isAfter(start) && t.transactionDate.isBefore(end)
      );
    }
    
    return transactions.fold(0.0, (sum, t) => sum + t.amount);
  }

  double getTotalExpenses({DateTime? start, DateTime? end}) {
    var transactions = state.where((t) => t.type == TransactionType.expense);
    
    if (start != null && end != null) {
      transactions = transactions.where((t) => 
        t.transactionDate.isAfter(start) && t.transactionDate.isBefore(end)
      );
    }
    
    return transactions.fold(0.0, (sum, t) => sum + t.amount);
  }

  double getNetIncome({DateTime? start, DateTime? end}) {
    return getTotalIncome(start: start, end: end) - getTotalExpenses(start: start, end: end);
  }

  Map<String, double> getExpensesByCategory({DateTime? start, DateTime? end}) {
    var expenses = state.where((t) => t.type == TransactionType.expense);
    
    if (start != null && end != null) {
      expenses = expenses.where((t) => 
        t.transactionDate.isAfter(start) && t.transactionDate.isBefore(end)
      );
    }
    
    final categoryTotals = <String, double>{};
    for (final expense in expenses) {
      categoryTotals[expense.category] = (categoryTotals[expense.category] ?? 0) + expense.amount;
    }
    
    return categoryTotals;
  }

  List<String> getAllCategories() {
    final categories = state.map((t) => t.category).toSet().toList();
    categories.sort();
    return categories;
  }

  List<String> getAllTags() {
    final allTags = <String>{};
    for (final transaction in state) {
      allTags.addAll(transaction.tags);
    }
    return allTags.toList()..sort();
  }
}

// Transactions by type provider
final transactionsByTypeProvider = Provider.family<List<Transaction>, TransactionType>((ref, type) {
  final transactions = ref.watch(transactionsProvider);
  return transactions.where((t) => t.type == type).toList();
});

// Transactions by account provider
final transactionsByAccountProvider = Provider.family<List<Transaction>, int>((ref, accountId) {
  final transactions = ref.watch(transactionsProvider);
  return transactions.where((t) => t.accountId == accountId && !t.isDeleted).toList();
});

// Recent transactions provider
final recentTransactionsProvider = Provider<List<Transaction>>((ref) {
  final transactions = ref.watch(transactionsProvider);
  return transactions.take(10).toList();
});

// Today's transactions provider
final todayTransactionsProvider = Provider<List<Transaction>>((ref) {
  final transactions = ref.watch(transactionsProvider);
  return transactions.where((t) => t.isToday).toList();
});

// This month's transactions provider
final thisMonthTransactionsProvider = Provider<List<Transaction>>((ref) {
  final transactions = ref.watch(transactionsProvider);
  return transactions.where((t) => t.isThisMonth).toList();
});

// Transaction statistics provider
final transactionStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final transactions = ref.watch(transactionsProvider);
  final todayTransactions = transactions.where((t) => t.isToday).toList();
  final thisMonthTransactions = transactions.where((t) => t.isThisMonth).toList();
  
  final totalIncome = transactions
      .where((t) => t.type == TransactionType.income)
      .fold(0.0, (sum, t) => sum + t.amount);
      
  final totalExpenses = transactions
      .where((t) => t.type == TransactionType.expense)
      .fold(0.0, (sum, t) => sum + t.amount);
      
  final monthlyIncome = thisMonthTransactions
      .where((t) => t.type == TransactionType.income)
      .fold(0.0, (sum, t) => sum + t.amount);
      
  final monthlyExpenses = thisMonthTransactions
      .where((t) => t.type == TransactionType.expense)
      .fold(0.0, (sum, t) => sum + t.amount);
  
  final categoryStats = <String, double>{};
  for (final transaction in thisMonthTransactions.where((t) => t.type == TransactionType.expense)) {
    categoryStats[transaction.category] = (categoryStats[transaction.category] ?? 0) + transaction.amount;
  }
  
  return {
    'totalTransactions': transactions.length,
    'todayTransactions': todayTransactions.length,
    'monthlyTransactions': thisMonthTransactions.length,
    'totalIncome': totalIncome,
    'totalExpenses': totalExpenses,
    'netIncome': totalIncome - totalExpenses,
    'monthlyIncome': monthlyIncome,
    'monthlyExpenses': monthlyExpenses,
    'monthlyNet': monthlyIncome - monthlyExpenses,
    'categoryStats': categoryStats,
    'topCategory': categoryStats.isNotEmpty 
        ? categoryStats.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : null,
  };
});

// Search transactions provider
final searchTransactionsProvider = StateProvider<String>((ref) => '');

final filteredTransactionsProvider = Provider<List<Transaction>>((ref) {
  final transactions = ref.watch(transactionsProvider);
  final searchQuery = ref.watch(searchTransactionsProvider);
  
  if (searchQuery.isEmpty) {
    return transactions;
  }
  
  return transactions.where((t) => t.containsSearchTerm(searchQuery)).toList();
});
