{"app": {"name": "ShadowSuite"}, "common": {"ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "loading": "Loading...", "error": "Error", "success": "Success", "retry": "Retry", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "done": "Done", "yes": "Yes", "no": "No", "create": "Create", "update": "Update", "refresh": "Refresh", "clear": "Clear", "reset": "Reset", "import": "Import", "export": "Export", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo"}, "navigation": {"dashboard": "Dashboard", "notes": "Notes", "todos": "Todos", "voice_memos": "Voice Memos", "dhikr": "Dhikr", "tools": "Tools", "money": "Money", "settings": "Settings"}, "notes": {"title": "Notes", "create": "Create Note", "edit": "Edit Note", "note_title": "Note Title", "content": "Content", "no_notes": "No notes yet", "search": "Search notes...", "search_placeholder": "Type to search your notes", "created": "Created", "modified": "Modified", "word_count": "{count} words", "character_count": "{count} characters", "save_note": "Save Note", "delete_note": "Delete Note", "note_saved": "Note saved successfully", "note_deleted": "Note deleted successfully", "untitled_note": "Untitled Note", "markdown_preview": "Markdown Preview", "rich_text": "Rich Text", "plain_text": "Plain Text"}, "todos": {"title": "Todos", "create": "Create Todo", "edit": "<PERSON>", "todo_title": "Todo Title", "description": "Description", "due_date": "Due Date", "completed": "Completed", "pending": "Pending", "no_todos": "No todos yet", "mark_complete": "Mark as Complete", "mark_incomplete": "<PERSON> as Incomplete", "overdue": "Overdue", "today": "Due Today", "tomorrow": "Due Tomorrow", "this_week": "Due This Week", "priority": "Priority", "high_priority": "High Priority", "medium_priority": "Medium Priority", "low_priority": "Low Priority", "no_priority": "No Priority", "add_reminder": "<PERSON><PERSON>", "remove_reminder": "<PERSON><PERSON><PERSON>", "todo_completed": "Todo completed!", "todo_created": "<PERSON><PERSON> created successfully", "todo_updated": "Todo updated successfully", "todo_deleted": "<PERSON><PERSON> deleted successfully"}, "voice_memos": {"title": "Voice Memos", "record": "Record", "stop_recording": "Stop Recording", "playback": "Playback", "transcription": "Transcription", "no_memos": "No voice memos yet", "recording": "Recording...", "duration": "Duration: {duration}", "file_size": "File size: {size}", "transcribe": "Transcribe", "transcribing": "Transcribing...", "transcription_complete": "Transcription complete", "transcription_failed": "Transcription failed", "play": "Play", "pause": "Pause", "stop": "Stop", "delete_memo": "Delete Memo", "memo_deleted": "Voice memo deleted successfully", "permission_required": "Microphone permission required", "grant_permission": "Grant Permission"}, "dhikr": {"title": "Dhikr", "counter": "Counter", "routines": "Routines", "create_routine": "Create Routine", "start": "Start Dhikr", "reset": "Reset Counter", "count": "Count: {count}", "target": "Target: {target}", "progress": "Progress: {progress}%", "completed": "Completed!", "routine_name": "Routine Name", "dhikr_text": "Dhikr Text", "target_count": "Target Count", "add_dhikr": "Add Dhi<PERSON>", "remove_dhikr": "Remove <PERSON>", "routine_created": "Routine created successfully", "routine_updated": "Routine updated successfully", "routine_deleted": "Routine deleted successfully", "daily_goal": "Daily Goal", "weekly_goal": "Weekly Goal", "monthly_goal": "Monthly Goal", "streak": "Streak: {days} days"}, "tools": {"title": "Tools", "calculator": "Calculator", "unit_converter": "Unit Converter", "qr_generator": "QR Generator", "color_picker": "Color Picker", "text_tools": "Text Tools", "custom_tools": "Custom Tools", "create_tool": "Create Tool", "tool_builder": "Tool Builder", "formula_editor": "Formula Editor", "drag_drop": "Drag & Drop", "save_tool": "Save Tool", "load_tool": "<PERSON><PERSON>", "share_tool": "Share Tool", "tool_created": "Tool created successfully", "tool_saved": "<PERSON><PERSON> saved successfully", "tool_loaded": "Tool loaded successfully"}, "money": {"title": "Money Manager", "income": "Income", "expenses": "Expenses", "budget": "Budget", "categories": "Categories", "transactions": "Transactions", "add_transaction": "Add Transaction", "edit_transaction": "Edit Transaction", "amount": "Amount", "category": "Category", "date": "Date", "description": "Description", "balance": "Balance", "total_income": "Total Income", "total_expenses": "Total Expenses", "net_income": "Net Income", "monthly_budget": "Monthly Budget", "budget_remaining": "Budget Remaining", "over_budget": "Over Budget", "transaction_added": "Transaction added successfully", "transaction_updated": "Transaction updated successfully", "transaction_deleted": "Transaction deleted successfully"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "privacy": "Privacy", "about": "About", "general": "General", "appearance": "Appearance", "data": "Data & Storage", "security": "Security", "accessibility": "Accessibility", "sync": "Sync & Backup", "advanced": "Advanced", "light_theme": "Light Theme", "dark_theme": "Dark Theme", "system_theme": "System Theme", "auto_theme": "Auto Theme", "high_contrast": "High Contrast", "large_text": "Large Text", "reduce_motion": "Reduce Motion", "screen_reader": "Screen Reader Support", "enable_notifications": "Enable Notifications", "notification_sound": "Notification Sound", "vibration": "Vibration", "auto_backup": "Auto Backup", "backup_frequency": "Backup Frequency", "cloud_sync": "Cloud Sync", "offline_mode": "Offline Mode", "data_usage": "Data Usage", "storage_usage": "Storage Usage", "clear_cache": "<PERSON>ache", "reset_settings": "Reset Settings", "export_data": "Export Data", "import_data": "Import Data"}, "errors": {"network": "Network connection error. Please check your internet connection.", "server": "Server error. Please try again later.", "validation": "Please check your input and try again.", "permission": "Permission denied. Please grant the required permissions.", "not_found": "The requested resource was not found.", "timeout": "Request timeout. Please try again.", "unknown": "An unexpected error occurred. Please try again.", "file_not_found": "File not found.", "file_too_large": "File is too large.", "invalid_format": "Invalid file format.", "storage_full": "Storage is full. Please free up some space.", "sync_failed": "Sync failed. Please try again.", "backup_failed": "Backup failed. Please try again.", "restore_failed": "<PERSON><PERSON> failed. Please try again."}, "sync": {"title": "Sync & Backup", "syncing": "Syncing...", "complete": "Sync complete", "failed": "Sync failed", "offline": "Offline", "online": "Online", "conflicts": "Conflicts", "resolve_conflicts": "Resolve Conflicts", "last_sync": "Last sync: {time}", "never_synced": "Never synced", "sync_now": "Sync Now", "auto_sync": "Auto Sync", "sync_interval": "Sync Interval", "wifi_only": "WiFi Only", "background_sync": "Background Sync", "conflict_resolution": "Conflict Resolution", "use_local": "Use Local", "use_remote": "Use Remote", "merge": "<PERSON><PERSON>", "skip": "<PERSON><PERSON>", "pending_items": "{count} items pending sync", "sync_queue": "Sync Queue", "clear_queue": "Clear Queue", "retry_failed": "Retry Failed Items"}, "accessibility": {"settings": "Accessibility Settings", "high_contrast": "High Contrast Mode", "large_text": "Large Text", "reduce_motion": "Reduce Motion", "screen_reader": "Screen Reader Support", "keyboard_navigation": "Keyboard Navigation", "focus_indicators": "Focus Indicators", "tooltips": "Show Tooltips", "help_text": "Show Help Text", "confirm_actions": "Confirm Actions", "extended_timeouts": "Extended Timeouts", "simplified_interface": "Simplified Interface", "voice_control": "Voice Control", "switch_control": "Switch Control", "button_size": "<PERSON><PERSON>", "touch_target_size": "Touch Target Size", "color_blindness": "Color Blindness Support", "sound_feedback": "Sound Feedback", "haptic_feedback": "Haptic <PERSON>", "visual_alerts": "Visual Alerts"}, "time": {"today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "this_year": "This Year", "last_year": "Last Year", "just_now": "Just now", "minutes_ago": "{count} minutes ago", "hours_ago": "{count} hours ago", "days_ago": "{count} days ago", "weeks_ago": "{count} weeks ago", "months_ago": "{count} months ago", "years_ago": "{count} years ago"}, "validation": {"required": "This field is required", "invalid_email": "Please enter a valid email address", "password_too_short": "Password must be at least 8 characters", "passwords_do_not_match": "Passwords do not match", "invalid_url": "Please enter a valid URL", "invalid_phone": "Please enter a valid phone number", "min_length": "Must be at least {min} characters", "max_length": "Must be no more than {max} characters", "min_value": "Must be at least {min}", "max_value": "Must be no more than {max}", "invalid_number": "Please enter a valid number", "invalid_date": "Please enter a valid date"}, "files": {"export_data": "Export Data", "import_data": "Import Data", "backup": "Backup", "restore": "Rest<PERSON>", "not_found": "File not found", "too_large": "File is too large", "invalid_format": "Invalid file format", "export_success": "Data exported successfully", "import_success": "Data imported successfully", "backup_success": "Backup created successfully", "restore_success": "Data restored successfully", "select_file": "Select File", "choose_location": "Choose Location", "file_name": "File Name", "file_size": "File Size", "file_type": "File Type", "created_date": "Created Date", "modified_date": "Modified Date"}}