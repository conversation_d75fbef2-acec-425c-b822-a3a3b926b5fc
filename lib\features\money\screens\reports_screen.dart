import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../providers/account_provider.dart';
import '../providers/transaction_provider.dart';

/// Reports and analytics screen
class ReportsScreen extends ConsumerStatefulWidget {
  const ReportsScreen({super.key});

  @override
  ConsumerState<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends ConsumerState<ReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  // Filter state
  DateTimeRange? _selectedDateRange;
  List<String> _selectedAccountIds = [];
  List<int> _selectedCategoryIds = [];
  TransactionType? _selectedTransactionType;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Default to last 30 days
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: now.subtract(const Duration(days: 30)),
      end: now,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StandardizedScaffold(
      title: 'Reports & Analytics',
      currentRoute: '/money-flow/reports',
      showBackButton: true,
      actions: [
        IconButton(
          onPressed: _showFilterDialog,
          icon: const Icon(Icons.filter_list),
          tooltip: 'Filter Reports',
        ),
        IconButton(
          onPressed: _exportReport,
          icon: const Icon(Icons.download),
          tooltip: 'Export Report',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.analytics), text: 'Overview'),
          Tab(icon: Icon(Icons.pie_chart), text: 'Categories'),
          Tab(icon: Icon(Icons.account_balance), text: 'Accounts'),
          Tab(icon: Icon(Icons.trending_up), text: 'Trends'),
        ],
      ),
      body: Column(
        children: [
          // Filter summary
          _buildFilterSummary(),
          
          // Report content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewReport(),
                _buildCategoryReport(),
                _buildAccountReport(),
                _buildTrendsReport(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build filter summary bar
  Widget _buildFilterSummary() {
    return Container(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.date_range,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            _getDateRangeText(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          
          if (_selectedAccountIds.isNotEmpty) ...[
            const SizedBox(width: 16),
            Icon(
              Icons.account_balance_wallet,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 4),
            Text(
              '${_selectedAccountIds.length} account${_selectedAccountIds.length == 1 ? '' : 's'}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
          
          if (_selectedCategoryIds.isNotEmpty) ...[
            const SizedBox(width: 16),
            Icon(
              Icons.category,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 4),
            Text(
              '${_selectedCategoryIds.length} categor${_selectedCategoryIds.length == 1 ? 'y' : 'ies'}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
          
          const Spacer(),
          
          TextButton.icon(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.tune, size: 16),
            label: const Text('Filter'),
          ),
        ],
      ),
    );
  }

  /// Build overview report
  Widget _buildOverviewReport() {
    final transactions = _getFilteredTransactions();
    final stats = _calculateOverviewStats(transactions);
    
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          ResponsiveGrid(
            mobileColumns: 1,
            tabletColumns: 2,
            desktopColumns: 4,
            spacing: ResponsiveSpacing.md(context),
            runSpacing: ResponsiveSpacing.md(context),
            children: [
              InfoCard(
                icon: Icons.trending_up,
                title: 'Total Income',
                value: '\$${stats['totalIncome']!.toStringAsFixed(2)}',
                iconColor: Colors.green,
              ),
              InfoCard(
                icon: Icons.trending_down,
                title: 'Total Expenses',
                value: '\$${stats['totalExpenses']!.toStringAsFixed(2)}',
                iconColor: Colors.red,
              ),
              InfoCard(
                icon: Icons.account_balance,
                title: 'Net Income',
                value: '\$${stats['netIncome']!.toStringAsFixed(2)}',
                iconColor: stats['netIncome']! >= 0 ? Colors.green : Colors.red,
              ),
              InfoCard(
                icon: Icons.receipt,
                title: 'Transactions',
                value: '${stats['transactionCount']}',
                iconColor: Colors.blue,
              ),
            ],
          ),
          
          SizedBox(height: ResponsiveSpacing.lg(context)),
          
          // Income vs Expenses chart placeholder
          SectionHeader(
            title: 'Income vs Expenses',
            subtitle: 'Monthly comparison',
          ),
          
          StandardCard(
            child: Container(
              height: 200,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Chart Coming Soon',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Interactive charts will be implemented in Phase 5',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build category report
  Widget _buildCategoryReport() {
    final transactions = _getFilteredTransactions();
    final categoryStats = _calculateCategoryStats(transactions);
    
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Category Breakdown',
            subtitle: 'Spending by category',
          ),
          
          if (categoryStats.isEmpty)
            EmptyState(
              icon: Icons.pie_chart,
              title: 'No Category Data',
              message: 'No transactions found for the selected period',
            )
          else
            ...categoryStats.entries.map((entry) {
              final categoryName = entry.key;
              final amount = entry.value;
              final percentage = (amount / categoryStats.values.reduce((a, b) => a + b)) * 100;
              
              return StandardCard(
                child: Row(
                  children: [
                    Container(
                      width: 4,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(categoryName),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            categoryName,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${percentage.toStringAsFixed(1)}% of total',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '\$${amount.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getCategoryColor(categoryName),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
        ],
      ),
    );
  }

  /// Build account report
  Widget _buildAccountReport() {
    final accounts = ref.watch(accountsProvider);
    final transactions = _getFilteredTransactions();
    final accountStats = _calculateAccountStats(transactions, accounts);
    
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Account Performance',
            subtitle: 'Activity by account',
          ),
          
          if (accountStats.isEmpty)
            EmptyState(
              icon: Icons.account_balance,
              title: 'No Account Data',
              message: 'No transactions found for the selected accounts',
            )
          else
            ...accountStats.entries.map((entry) {
              final accountName = entry.key;
              final stats = entry.value;
              
              return StandardCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      accountName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatItem(
                            'Income',
                            '\$${stats['income']!.toStringAsFixed(2)}',
                            Colors.green,
                          ),
                        ),
                        Expanded(
                          child: _buildStatItem(
                            'Expenses',
                            '\$${stats['expenses']!.toStringAsFixed(2)}',
                            Colors.red,
                          ),
                        ),
                        Expanded(
                          child: _buildStatItem(
                            'Net',
                            '\$${stats['net']!.toStringAsFixed(2)}',
                            stats['net']! >= 0 ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }).toList(),
        ],
      ),
    );
  }

  /// Build trends report
  Widget _buildTrendsReport() {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Spending Trends',
            subtitle: 'Track your financial patterns',
          ),
          
          StandardCard(
            child: Container(
              height: 300,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.show_chart,
                      size: 64,
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Trend Analysis Coming Soon',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Advanced trend analysis and forecasting will be available in Phase 5',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build stat item widget
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// Get filtered transactions based on current filters
  List<Transaction> _getFilteredTransactions() {
    final allTransactions = ref.watch(transactionsProvider);
    
    return allTransactions.where((transaction) {
      // Date filter
      if (_selectedDateRange != null) {
        final transactionDate = transaction.date;
        if (transactionDate.isBefore(_selectedDateRange!.start) ||
            transactionDate.isAfter(_selectedDateRange!.end)) {
          return false;
        }
      }
      
      // Account filter
      if (_selectedAccountIds.isNotEmpty) {
        if (!_selectedAccountIds.contains(transaction.accountId)) {
          return false;
        }
      }
      
      // Category filter
      if (_selectedCategoryIds.isNotEmpty) {
        // Note: This needs to be updated when category system is implemented
        // For now, skip category filtering
      }
      
      // Transaction type filter
      if (_selectedTransactionType != null) {
        if (transaction.type != _selectedTransactionType) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  /// Calculate overview statistics
  Map<String, double> _calculateOverviewStats(List<Transaction> transactions) {
    double totalIncome = 0;
    double totalExpenses = 0;
    
    for (final transaction in transactions) {
      if (transaction.type == TransactionType.income) {
        totalIncome += transaction.amount;
      } else if (transaction.type == TransactionType.expense) {
        totalExpenses += transaction.amount;
      }
    }
    
    return {
      'totalIncome': totalIncome,
      'totalExpenses': totalExpenses,
      'netIncome': totalIncome - totalExpenses,
      'transactionCount': transactions.length.toDouble(),
    };
  }

  /// Calculate category statistics
  Map<String, double> _calculateCategoryStats(List<Transaction> transactions) {
    final Map<String, double> categoryTotals = {};
    
    for (final transaction in transactions) {
      if (transaction.type == TransactionType.expense) {
        final categoryName = transaction.category.isNotEmpty ? transaction.category : 'Unknown';
        categoryTotals[categoryName] = (categoryTotals[categoryName] ?? 0) + transaction.amount;
      }
    }
    
    // Sort by amount descending
    final sortedEntries = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Map.fromEntries(sortedEntries);
  }

  /// Calculate account statistics
  Map<String, Map<String, double>> _calculateAccountStats(
    List<Transaction> transactions,
    List<Account> accounts,
  ) {
    final Map<String, Map<String, double>> accountStats = {};
    
    for (final transaction in transactions) {
      final account = accounts.firstWhere(
        (acc) => acc.id == transaction.accountId,
        orElse: () => Account()..name = 'Unknown',
      );
      
      if (!accountStats.containsKey(account.name)) {
        accountStats[account.name] = {
          'income': 0.0,
          'expenses': 0.0,
          'net': 0.0,
        };
      }
      
      if (transaction.type == TransactionType.income) {
        accountStats[account.name]!['income'] = 
            accountStats[account.name]!['income']! + transaction.amount;
      } else if (transaction.type == TransactionType.expense) {
        accountStats[account.name]!['expenses'] = 
            accountStats[account.name]!['expenses']! + transaction.amount;
      }
      
      accountStats[account.name]!['net'] = 
          accountStats[account.name]!['income']! - accountStats[account.name]!['expenses']!;
    }
    
    return accountStats;
  }

  /// Get category color (placeholder)
  Color _getCategoryColor(String categoryName) {
    final colors = [
      Colors.blue, Colors.green, Colors.orange, Colors.purple,
      Colors.red, Colors.teal, Colors.amber, Colors.indigo,
    ];
    return colors[categoryName.hashCode % colors.length];
  }

  /// Get date range text
  String _getDateRangeText() {
    if (_selectedDateRange == null) return 'All time';
    
    final start = _selectedDateRange!.start;
    final end = _selectedDateRange!.end;
    
    return '${start.day}/${start.month}/${start.year} - ${end.day}/${end.month}/${end.year}';
  }

  /// Show filter dialog
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        selectedDateRange: _selectedDateRange,
        selectedAccountIds: _selectedAccountIds,
        selectedCategoryIds: _selectedCategoryIds,
        selectedTransactionType: _selectedTransactionType,
        onFiltersChanged: (dateRange, accountIds, categoryIds, transactionType) {
          setState(() {
            _selectedDateRange = dateRange;
            _selectedAccountIds = accountIds;
            _selectedCategoryIds = categoryIds;
            _selectedTransactionType = transactionType;
          });
        },
      ),
    );
  }

  /// Export report
  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality will be implemented in Phase 4'),
      ),
    );
  }
}

/// Filter dialog for reports
class _FilterDialog extends ConsumerStatefulWidget {
  final DateTimeRange? selectedDateRange;
  final List<String> selectedAccountIds;
  final List<int> selectedCategoryIds;
  final TransactionType? selectedTransactionType;
  final Function(DateTimeRange?, List<String>, List<int>, TransactionType?) onFiltersChanged;

  const _FilterDialog({
    required this.selectedDateRange,
    required this.selectedAccountIds,
    required this.selectedCategoryIds,
    required this.selectedTransactionType,
    required this.onFiltersChanged,
  });

  @override
  ConsumerState<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends ConsumerState<_FilterDialog> {
  late DateTimeRange? _dateRange;
  late List<String> _accountIds;
  late List<int> _categoryIds;
  late TransactionType? _transactionType;

  @override
  void initState() {
    super.initState();
    _dateRange = widget.selectedDateRange;
    _accountIds = List.from(widget.selectedAccountIds);
    _categoryIds = List.from(widget.selectedCategoryIds);
    _transactionType = widget.selectedTransactionType;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter Reports',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Date range
            Text(
              'Date Range',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            OutlinedButton(
              onPressed: _selectDateRange,
              child: Text(
                _dateRange != null
                    ? '${_dateRange!.start.day}/${_dateRange!.start.month}/${_dateRange!.start.year} - ${_dateRange!.end.day}/${_dateRange!.end.month}/${_dateRange!.end.year}'
                    : 'Select Date Range',
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quick date filters
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('Last 7 days'),
                  selected: _isDateRangeSelected(7),
                  onSelected: (selected) => _setQuickDateRange(7),
                ),
                FilterChip(
                  label: const Text('Last 30 days'),
                  selected: _isDateRangeSelected(30),
                  onSelected: (selected) => _setQuickDateRange(30),
                ),
                FilterChip(
                  label: const Text('Last 3 months'),
                  selected: _isDateRangeSelected(90),
                  onSelected: (selected) => _setQuickDateRange(90),
                ),
                FilterChip(
                  label: const Text('Last year'),
                  selected: _isDateRangeSelected(365),
                  onSelected: (selected) => _setQuickDateRange(365),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    widget.onFiltersChanged(_dateRange, _accountIds, _categoryIds, _transactionType);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
    );
    
    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
    }
  }

  bool _isDateRangeSelected(int days) {
    if (_dateRange == null) return false;
    
    final now = DateTime.now();
    final expectedStart = now.subtract(Duration(days: days));
    final expectedEnd = now;
    
    return _dateRange!.start.difference(expectedStart).inDays.abs() <= 1 &&
           _dateRange!.end.difference(expectedEnd).inDays.abs() <= 1;
  }

  void _setQuickDateRange(int days) {
    final now = DateTime.now();
    setState(() {
      _dateRange = DateTimeRange(
        start: now.subtract(Duration(days: days)),
        end: now,
      );
    });
  }
}
