import 'dart:convert';

/// Tool Definition model for the new Excel-like Tool Builder
/// Stores both spreadsheet backend data and UI layout configuration
class ToolDefinition {
  final int? id;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String backendData; // JSON string of spreadsheet data
  final String uiLayout; // JSON string of UI components
  final String userId;
  final bool isPasswordProtected;
  final String? passwordHash;
  final bool isDeleted;

  const ToolDefinition({
    this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.backendData,
    required this.uiLayout,
    required this.userId,
    this.isPasswordProtected = false,
    this.passwordHash,
    this.isDeleted = false,
  });

  /// Factory constructor for creating new tools
  factory ToolDefinition.create({
    required String name,
    required String description,
    required String userId,
    String backendData = '{}',
    String uiLayout = '{}',
    bool isPasswordProtected = false,
    String? passwordHash,
  }) {
    final now = DateTime.now();
    return ToolDefinition(
      name: name,
      description: description,
      userId: userId,
      backendData: backendData,
      uiLayout: uiLayout,
      isPasswordProtected: isPasswordProtected,
      passwordHash: passwordHash,
      createdAt: now,
      updatedAt: now,
      isDeleted: false,
    );
  }

  /// Convert to JSON for export
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'backendData': backendData,
      'uiLayout': uiLayout,
      'userId': userId,
      'isPasswordProtected': isPasswordProtected,
      'isDeleted': isDeleted,
    };
  }

  /// Create from JSON for import
  factory ToolDefinition.fromJson(Map<String, dynamic> json, String userId) {
    final now = DateTime.now();
    return ToolDefinition(
      id: json['id'],
      name: json['name'] ?? 'Imported Tool',
      description: json['description'] ?? '',
      userId: userId,
      backendData: json['backendData'] ?? '{}',
      uiLayout: json['uiLayout'] ?? '{}',
      isPasswordProtected: json['isPasswordProtected'] ?? false,
      passwordHash: json['passwordHash'],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : now,
      updatedAt: now,
      isDeleted: json['isDeleted'] ?? false,
    );
  }

  /// Create a copy with updated values
  ToolDefinition copyWith({
    int? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? backendData,
    String? uiLayout,
    String? userId,
    bool? isPasswordProtected,
    String? passwordHash,
    bool? isDeleted,
  }) {
    return ToolDefinition(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      backendData: backendData ?? this.backendData,
      uiLayout: uiLayout ?? this.uiLayout,
      userId: userId ?? this.userId,
      isPasswordProtected: isPasswordProtected ?? this.isPasswordProtected,
      passwordHash: passwordHash ?? this.passwordHash,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// Get parsed backend data
  Map<String, dynamic> getParsedBackendData() {
    try {
      return jsonDecode(backendData);
    } catch (e) {
      return {};
    }
  }

  /// Get parsed UI layout
  Map<String, dynamic> getParsedUILayout() {
    try {
      return jsonDecode(uiLayout);
    } catch (e) {
      return {};
    }
  }

  /// Update backend data
  ToolDefinition updateBackendData(Map<String, dynamic> data) {
    return copyWith(
      backendData: jsonEncode(data),
      updatedAt: DateTime.now(),
    );
  }

  /// Update UI layout
  ToolDefinition updateUILayout(Map<String, dynamic> layout) {
    return copyWith(
      uiLayout: jsonEncode(layout),
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ToolDefinition &&
        other.id == id &&
        other.name == name &&
        other.userId == userId;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, userId);
  }

  @override
  String toString() {
    return 'ToolDefinition(id: $id, name: $name, userId: $userId)';
  }
}
