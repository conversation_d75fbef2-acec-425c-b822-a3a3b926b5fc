import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/database/database_service.dart';
import '../models/tool_definition.dart';
import '../models/spreadsheet_cell.dart';
import '../models/ui_component.dart';
import '../services/tool_storage_service.dart';
import '../services/formula_engine_service.dart';

/// Tool Builder dashboard state
class ToolBuilderState {
  final bool isLoading;
  final String? error;
  final Map<String, dynamic> stats;

  const ToolBuilderState({
    this.isLoading = false,
    this.error,
    this.stats = const {},
  });

  ToolBuilderState copyWith({
    bool? isLoading,
    String? error,
    Map<String, dynamic>? stats,
  }) {
    return ToolBuilderState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      stats: stats ?? this.stats,
    );
  }
}

/// Provider for tool storage service
final toolStorageServiceProvider = Provider<ToolStorageService>((ref) {
  return ToolStorageService();
});

/// Provider for formula engine service
final formulaEngineServiceProvider = Provider<FormulaEngineService>((ref) {
  return FormulaEngineService();
});

/// Provider for current user ID (placeholder)
final currentUserIdProvider = Provider<String>((ref) {
  return 'default_user'; // TODO: Get from authentication service
});

/// Provider for user's tools
final userToolsProvider = FutureProvider<List<ToolDefinition>>((ref) async {
  final storageService = ref.read(toolStorageServiceProvider);
  final userId = ref.read(currentUserIdProvider);
  return await storageService.getUserTools(userId);
});

/// Provider for a specific tool
final toolProvider = FutureProvider.family<ToolDefinition?, int>((ref, toolId) async {
  final storageService = ref.read(toolStorageServiceProvider);
  return await storageService.getTool(toolId);
});

/// Provider for tool statistics
final toolStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final storageService = ref.read(toolStorageServiceProvider);
  final userId = ref.read(currentUserIdProvider);
  return await storageService.getToolStats(userId);
});

/// State notifier for managing current tool being edited
class CurrentToolNotifier extends StateNotifier<ToolDefinition?> {
  CurrentToolNotifier(this._storageService, this._userId) : super(null);

  final ToolStorageService _storageService;
  final String _userId;

  /// Create a new tool
  Future<void> createTool(String name, String description) async {
    final tool = ToolDefinition.create(
      name: name,
      description: description,
      userId: _userId,
    );
    
    await _storageService.saveTool(tool);
    state = tool;
  }

  /// Load an existing tool
  Future<void> loadTool(int toolId) async {
    final tool = await _storageService.getTool(toolId);
    state = tool;
  }

  /// Update tool backend data
  Future<void> updateBackendData(Map<String, dynamic> backendData) async {
    if (state == null) return;
    
    final updatedTool = state!.updateBackendData(backendData);
    await _storageService.saveTool(updatedTool);
    state = updatedTool;
  }

  /// Update tool UI layout
  Future<void> updateUILayout(Map<String, dynamic> uiLayout) async {
    if (state == null) return;
    
    final updatedTool = state!.updateUILayout(uiLayout);
    await _storageService.saveTool(updatedTool);
    state = updatedTool;
  }

  /// Save current tool
  Future<void> saveTool() async {
    if (state == null) return;
    await _storageService.saveTool(state!);
  }

  /// Clear current tool
  void clearTool() {
    state = null;
  }
}

/// Provider for current tool being edited
final currentToolProvider = StateNotifierProvider<CurrentToolNotifier, ToolDefinition?>((ref) {
  final storageService = ref.read(toolStorageServiceProvider);
  final userId = ref.read(currentUserIdProvider);
  return CurrentToolNotifier(storageService, userId);
});

/// State notifier for managing spreadsheet data
class SpreadsheetDataNotifier extends StateNotifier<Map<String, SpreadsheetCell>> {
  SpreadsheetDataNotifier(this._formulaEngine) : super({});

  final FormulaEngineService _formulaEngine;

  /// Set cell value
  void setCell(String address, dynamic value, {String? formula}) {
    final cellType = _determineCellType(value, formula);
    final cell = SpreadsheetCell(
      address: address,
      value: value,
      formula: formula,
      type: cellType,
    );
    
    state = {
      ...state,
      address: cell,
    };
    
    // Recalculate if there are formulas
    _recalculateSheet();
  }

  /// Remove cell
  void removeCell(String address) {
    final newState = Map<String, SpreadsheetCell>.from(state);
    newState.remove(address);
    state = newState;
  }

  /// Clear all cells
  void clearAll() {
    state = {};
  }

  /// Load spreadsheet data from JSON
  void loadFromJson(Map<String, dynamic> data) {
    final cells = <String, SpreadsheetCell>{};
    
    if (data.containsKey('cells')) {
      final cellsData = data['cells'] as Map<String, dynamic>;
      for (final entry in cellsData.entries) {
        cells[entry.key] = SpreadsheetCell.fromJson(entry.value);
      }
    }
    
    state = cells;
  }

  /// Export spreadsheet data to JSON
  Map<String, dynamic> toJson() {
    return {
      'cells': state.map((key, value) => MapEntry(key, value.toJson())),
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// Recalculate all formulas
  void _recalculateSheet() {
    state = _formulaEngine.recalculateSheet(state);
  }

  /// Determine cell type based on value and formula
  CellType _determineCellType(dynamic value, String? formula) {
    if (formula != null && formula.startsWith('=')) {
      return CellType.formula;
    }
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    return CellType.text;
  }
}

/// Provider for spreadsheet data
final spreadsheetDataProvider = StateNotifierProvider<SpreadsheetDataNotifier, Map<String, SpreadsheetCell>>((ref) {
  final formulaEngine = ref.read(formulaEngineServiceProvider);
  return SpreadsheetDataNotifier(formulaEngine);
});

/// State notifier for managing UI components
class UIComponentsNotifier extends StateNotifier<List<UIComponent>> {
  UIComponentsNotifier() : super([]);

  /// Add a new component
  void addComponent(UIComponent component) {
    state = [...state, component];
  }

  /// Update a component
  void updateComponent(String componentId, UIComponent updatedComponent) {
    state = state.map((component) {
      return component.id == componentId ? updatedComponent : component;
    }).toList();
  }

  /// Remove a component
  void removeComponent(String componentId) {
    state = state.where((component) => component.id != componentId).toList();
  }

  /// Clear all components
  void clearAll() {
    state = [];
  }

  /// Load components from JSON
  void loadFromJson(Map<String, dynamic> data) {
    final components = <UIComponent>[];
    
    if (data.containsKey('components')) {
      final componentsData = data['components'] as List;
      for (final componentData in componentsData) {
        components.add(UIComponent.fromJson(componentData));
      }
    }
    
    state = components;
  }

  /// Export components to JSON
  Map<String, dynamic> toJson() {
    return {
      'components': state.map((component) => component.toJson()).toList(),
      'layout': 'custom',
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }
}

/// Provider for UI components
final uiComponentsProvider = StateNotifierProvider<UIComponentsNotifier, List<UIComponent>>((ref) {
  return UIComponentsNotifier();
});

/// Provider for selected cell in spreadsheet
final selectedCellProvider = StateProvider<String?>((ref) => null);

/// Provider for selected component in UI builder
final selectedComponentProvider = StateProvider<String?>((ref) => null);

/// Provider for formula suggestions
final formulaSuggestionsProvider = Provider.family<List<String>, String>((ref, input) {
  final formulaEngine = ref.read(formulaEngineServiceProvider);
  return formulaEngine.getFormulaSuggestions(input);
});

/// Provider for tool search query
final toolSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider for filtered tools based on search
final filteredToolsProvider = FutureProvider<List<ToolDefinition>>((ref) async {
  final tools = await ref.watch(userToolsProvider.future);
  final searchQuery = ref.watch(toolSearchQueryProvider);
  
  if (searchQuery.isEmpty) {
    return tools;
  }
  
  final lowercaseQuery = searchQuery.toLowerCase();
  return tools.where((tool) {
    return tool.name.toLowerCase().contains(lowercaseQuery) ||
           tool.description.toLowerCase().contains(lowercaseQuery);
  }).toList();
});

/// Tool Builder dashboard notifier
class ToolBuilderNotifier extends StateNotifier<ToolBuilderState> {
  ToolBuilderNotifier() : super(const ToolBuilderState());

  final DatabaseService _db = DatabaseService.instance;

  /// Load dashboard statistics
  Future<void> loadDashboardStats() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final stats = <String, dynamic>{};

      // Load tool statistics (mock data for now)
      stats['totalTools'] = 8;
      stats['activeTools'] = 6;
      stats['templatesUsed'] = 3;
      stats['excelImports'] = 2;
      stats['totalUsage'] = 45;

      // Recent activity (mock data)
      stats['recentActivity'] = [
        {
          'action': 'Created "Budget Calculator"',
          'time': DateTime.now().subtract(const Duration(hours: 2)),
          'type': 'create',
        },
        {
          'action': 'Imported Excel file "Financial Model"',
          'time': DateTime.now().subtract(const Duration(days: 1)),
          'type': 'import',
        },
        {
          'action': 'Modified "Loan Calculator"',
          'time': DateTime.now().subtract(const Duration(days: 3)),
          'type': 'edit',
        },
      ];

      state = state.copyWith(isLoading: false, stats: stats);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load dashboard stats: $e',
      );
    }
  }

  /// Refresh dashboard data
  Future<void> refresh() async {
    await loadDashboardStats();
  }
}

/// Tool Builder dashboard provider
final toolBuilderProvider = StateNotifierProvider<ToolBuilderNotifier, ToolBuilderState>(
  (ref) => ToolBuilderNotifier(),
);
