enum CalculationType {
  basic,
  scientific,
  mortgage,
  tip,
  bmi,
  conversion,
  custom,
}

class CalculationHistory {
  int id = 0;
  CalculationType type = CalculationType.basic;
  String expression = '';
  String result = '';
  Map<String, dynamic> inputs = {};
  Map<String, dynamic> outputs = {};
  String calculatorName = '';
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  CalculationHistory();

  CalculationHistory.create({
    required this.type,
    required this.expression,
    required this.result,
    required this.userId,
    this.inputs = const {},
    this.outputs = const {},
    this.calculatorName = '',
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  String get typeDisplayName {
    switch (type) {
      case CalculationType.basic:
        return 'Basic';
      case CalculationType.scientific:
        return 'Scientific';
      case CalculationType.mortgage:
        return 'Mortgage';
      case CalculationType.tip:
        return 'Tip';
      case CalculationType.bmi:
        return 'BMI';
      case CalculationType.conversion:
        return 'Conversion';
      case CalculationType.custom:
        return 'Custom';
    }
  }

  String get formattedDateTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get shortExpression {
    if (expression.length > 30) {
      return '${expression.substring(0, 27)}...';
    }
    return expression;
  }

  String get shortResult {
    if (result.length > 20) {
      return '${result.substring(0, 17)}...';
    }
    return result;
  }

  bool containsSearchTerm(String searchTerm) {
    final term = searchTerm.toLowerCase();
    return expression.toLowerCase().contains(term) ||
           result.toLowerCase().contains(term) ||
           calculatorName.toLowerCase().contains(term) ||
           typeDisplayName.toLowerCase().contains(term);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'expression': expression,
      'result': result,
      'inputs': inputs,
      'outputs': outputs,
      'calculatorName': calculatorName,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory CalculationHistory.fromJson(Map<String, dynamic> json) {
    final history = CalculationHistory();
    history.id = json['id'] ?? 0;
    history.type = CalculationType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => CalculationType.basic,
    );
    history.expression = json['expression'] ?? '';
    history.result = json['result'] ?? '';
    history.inputs = Map<String, dynamic>.from(json['inputs'] ?? {});
    history.outputs = Map<String, dynamic>.from(json['outputs'] ?? {});
    history.calculatorName = json['calculatorName'] ?? '';
    history.userId = json['userId'] ?? '';
    history.createdAt = DateTime.parse(json['createdAt']);
    history.updatedAt = DateTime.parse(json['updatedAt']);
    history.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    history.isDeleted = json['isDeleted'] ?? false;
    history.syncId = json['syncId'];
    return history;
  }
}
