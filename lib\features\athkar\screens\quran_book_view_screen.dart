import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/base_screen.dart';
import '../models/quran_models.dart';
import '../providers/quran_provider.dart';

/// Book-like Quran reader that mimics traditional Mushaf layout
class QuranBookViewScreen extends ConsumerStatefulWidget {
  final int initialSurah;
  final int initialVerse;

  const QuranBookViewScreen({
    super.key,
    this.initialSurah = 1,
    this.initialVerse = 1,
  });

  @override
  ConsumerState<QuranBookViewScreen> createState() => _QuranBookViewScreenState();
}

class _QuranBookViewScreenState extends ConsumerState<QuranBookViewScreen> {
  late PageController _pageController;
  int _currentPage = 0;
  int _currentSurah = 1;
  bool _showTranslation = false;
  double _fontSize = 24.0;
  bool _showTafsir = false;

  @override
  void initState() {
    super.initState();
    _currentSurah = widget.initialSurah;
    _pageController = PageController(initialPage: _currentSurah - 1);
    _currentPage = _currentSurah - 1;
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScreen(
      title: 'القرآن الكريم',
      backgroundColor: const Color(0xFFF5F5DC), // Beige background like traditional Mushaf
      actions: [
        IconButton(
          icon: Icon(_showTranslation ? Icons.translate : Icons.translate_outlined),
          onPressed: () => setState(() => _showTranslation = !_showTranslation),
          tooltip: 'Toggle Translation',
        ),
        IconButton(
          icon: const Icon(Icons.text_fields),
          onPressed: _showFontSizeDialog,
          tooltip: 'Font Size',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'bookmark', child: Text('Add Bookmark')),
            const PopupMenuItem(value: 'search', child: Text('Search')),
            const PopupMenuItem(value: 'goto', child: Text('Go to Surah')),
            const PopupMenuItem(value: 'settings', child: Text('Reading Settings')),
          ],
        ),
      ],
      body: Column(
        children: [
          // Surah header with navigation
          _buildSurahHeader(),
          
          // Main Quran content
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (page) {
                setState(() {
                  _currentPage = page;
                  _currentSurah = page + 1;
                });
              },
              itemCount: 114, // Total surahs
              itemBuilder: (context, index) => _buildSurahPage(index + 1),
            ),
          ),
          
          // Bottom navigation
          _buildBottomNavigation(),
        ],
      ),
    );
  }

  Widget _buildSurahHeader() {
    final surahs = ref.watch(allSurahsProvider);
    final surah = surahs.isNotEmpty && _currentSurah <= surahs.length
        ? surahs[_currentSurah - 1]
        : null;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
      ),
      child: Column(
        children: [
          // Surah name in Arabic
          Text(
            surah?.info.name ?? 'Unknown Surah',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
              fontFamily: 'Amiri', // Arabic font
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),

          // Surah name in English and info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                surah?.info.englishName ?? 'Unknown',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${surah?.verses.length ?? 0} آيات',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSurahPage(int surahNumber) {
    final surahs = ref.watch(allSurahsProvider);
    final surah = surahs.isNotEmpty && surahNumber <= surahs.length
        ? surahs[surahNumber - 1]
        : null;

    return Container(
      padding: const EdgeInsets.all(20),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Bismillah (except for Surah 9)
            if (surahNumber != 9 && surahNumber != 1) ...[
              Container(
                margin: const EdgeInsets.symmetric(vertical: 20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                  style: TextStyle(
                    fontSize: _fontSize,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor,
                    fontFamily: 'Amiri',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
            
            // Verses
            if (surah?.verses != null)
              ...surah!.verses.asMap().entries.map((entry) {
                final index = entry.key;
                final verse = entry.value;
                return _buildVerseWidget(verse, index + 1);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildVerseWidget(Verse verse, int verseNumber) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Arabic text
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Arabic verse text
                RichText(
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.rtl,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: verse.arabicText,
                        style: TextStyle(
                          fontSize: _fontSize,
                          height: 1.8,
                          color: Colors.black87,
                          fontFamily: 'Amiri',
                        ),
                      ),
                      TextSpan(
                        text: ' ﴿$verseNumber﴾',
                        style: TextStyle(
                          fontSize: _fontSize * 0.8,
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Translation (if enabled)
                if (_showTranslation) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      verse.translation ?? 'Translation not available',
                      style: TextStyle(
                        fontSize: _fontSize * 0.7,
                        color: Colors.black87,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous surah
          IconButton(
            onPressed: _currentSurah > 1 ? _goToPreviousSurah : null,
            icon: const Icon(Icons.arrow_back_ios),
            tooltip: 'Previous Surah',
          ),
          
          // Surah indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'سورة $_currentSurah من 114',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          
          // Next surah
          IconButton(
            onPressed: _currentSurah < 114 ? _goToNextSurah : null,
            icon: const Icon(Icons.arrow_forward_ios),
            tooltip: 'Next Surah',
          ),
        ],
      ),
    );
  }

  void _goToPreviousSurah() {
    if (_currentSurah > 1) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextSurah() {
    if (_currentSurah < 114) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Font Size'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Size: ${_fontSize.round()}'),
              Slider(
                value: _fontSize,
                min: 16.0,
                max: 36.0,
                divisions: 20,
                onChanged: (value) {
                  setDialogState(() => _fontSize = value);
                  setState(() => _fontSize = value);
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'bookmark':
        // TODO: Implement bookmark functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Bookmark added!')),
        );
        break;
      case 'search':
        // TODO: Implement search functionality
        break;
      case 'goto':
        _showGoToSurahDialog();
        break;
      case 'settings':
        // TODO: Implement reading settings
        break;
    }
  }

  void _showGoToSurahDialog() {
    final surahs = ref.read(allSurahsProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Go to Surah'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: surahs.length,
            itemBuilder: (context, index) {
              final surah = surahs[index];
              return ListTile(
                title: Text(surah.info.englishName),
                subtitle: Text(surah.info.name),
                onTap: () {
                  Navigator.pop(context);
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
