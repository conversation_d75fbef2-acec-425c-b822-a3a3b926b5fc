import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  static const String supabaseUrl = 'https://gqliovwpnoyydbmugmnw.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdxbGlvdndwbm95eWRibXVnbW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI5NzQsImV4cCI6MjA1MDU0ODk3NH0.Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8';

  SupabaseClient? _client;
  User? _currentUser;
  bool _isInitialized = false;

  final StreamController<AuthState> _authStateController = 
      StreamController<AuthState>.broadcast();
  final StreamController<SyncEvent> _syncEventController = 
      StreamController<SyncEvent>.broadcast();

  // Getters
  SupabaseClient get client => _client!;
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;
  bool get isInitialized => _isInitialized;
  Stream<AuthState> get authStateStream => _authStateController.stream;
  Stream<SyncEvent> get syncEventStream => _syncEventController.stream;

  // Initialize Supabase
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: kDebugMode,
      );

      _client = Supabase.instance.client;
      _currentUser = _client!.auth.currentUser;

      // Listen to auth changes
      _client!.auth.onAuthStateChange.listen((authChange) {
        _currentUser = authChange.session?.user;
        _authStateController.add(AuthState(
          user: authChange.session?.user,
          event: authChange.event,
        ));
      });

      _isInitialized = true;
      debugPrint('SupabaseService initialized');
    } catch (e) {
      debugPrint('Error initializing SupabaseService: $e');
      rethrow;
    }
  }

  // Authentication
  Future<AuthResponse> signInAnonymously() async {
    try {
      final response = await _client!.auth.signInAnonymously();
      return response;
    } catch (e) {
      debugPrint('Error signing in anonymously: $e');
      rethrow;
    }
  }

  Future<AuthResponse> signInWithEmail(String email, String password) async {
    try {
      final response = await _client!.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      debugPrint('Error signing in with email: $e');
      rethrow;
    }
  }

  Future<AuthResponse> signUpWithEmail(String email, String password) async {
    try {
      final response = await _client!.auth.signUp(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      debugPrint('Error signing up with email: $e');
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      await _client!.auth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  // Data sync operations
  Future<List<Map<String, dynamic>>> syncNotes() async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final response = await _client!
          .from('notes')
          .select()
          .eq('user_id', _currentUser!.id)
          .order('modified_at', ascending: false);

      _emitSyncEvent(SyncEvent.notesSync(success: true));
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _emitSyncEvent(SyncEvent.notesSync(success: false, error: e.toString()));
      debugPrint('Error syncing notes: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> upsertNote(Map<String, dynamic> note) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final noteData = {
        ...note,
        'user_id': _currentUser!.id,
        'modified_at': DateTime.now().toIso8601String(),
      };

      final response = await _client!
          .from('notes')
          .upsert(noteData)
          .select()
          .single();

      return response;
    } catch (e) {
      debugPrint('Error upserting note: $e');
      rethrow;
    }
  }

  Future<void> deleteNote(String noteId) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      await _client!
          .from('notes')
          .delete()
          .eq('id', noteId)
          .eq('user_id', _currentUser!.id);
    } catch (e) {
      debugPrint('Error deleting note: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> syncTodos() async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final response = await _client!
          .from('todos')
          .select()
          .eq('user_id', _currentUser!.id)
          .order('modified_at', ascending: false);

      _emitSyncEvent(SyncEvent.todosSync(success: true));
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _emitSyncEvent(SyncEvent.todosSync(success: false, error: e.toString()));
      debugPrint('Error syncing todos: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> upsertTodo(Map<String, dynamic> todo) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final todoData = {
        ...todo,
        'user_id': _currentUser!.id,
        'modified_at': DateTime.now().toIso8601String(),
      };

      final response = await _client!
          .from('todos')
          .upsert(todoData)
          .select()
          .single();

      return response;
    } catch (e) {
      debugPrint('Error upserting todo: $e');
      rethrow;
    }
  }

  Future<void> deleteTodo(String todoId) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      await _client!
          .from('todos')
          .delete()
          .eq('id', todoId)
          .eq('user_id', _currentUser!.id);
    } catch (e) {
      debugPrint('Error deleting todo: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> syncVoiceMemos() async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final response = await _client!
          .from('voice_memos')
          .select()
          .eq('user_id', _currentUser!.id)
          .order('modified_at', ascending: false);

      _emitSyncEvent(SyncEvent.voiceMemosSync(success: true));
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _emitSyncEvent(SyncEvent.voiceMemosSync(success: false, error: e.toString()));
      debugPrint('Error syncing voice memos: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> upsertVoiceMemo(Map<String, dynamic> memo) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final memoData = {
        ...memo,
        'user_id': _currentUser!.id,
        'modified_at': DateTime.now().toIso8601String(),
      };

      final response = await _client!
          .from('voice_memos')
          .upsert(memoData)
          .select()
          .single();

      return response;
    } catch (e) {
      debugPrint('Error upserting voice memo: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> syncDhikrRoutines() async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final response = await _client!
          .from('dhikr_routines')
          .select()
          .eq('user_id', _currentUser!.id)
          .order('modified_at', ascending: false);

      _emitSyncEvent(SyncEvent.dhikrSync(success: true));
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _emitSyncEvent(SyncEvent.dhikrSync(success: false, error: e.toString()));
      debugPrint('Error syncing dhikr routines: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> upsertDhikrRoutine(Map<String, dynamic> routine) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final routineData = {
        ...routine,
        'user_id': _currentUser!.id,
        'modified_at': DateTime.now().toIso8601String(),
      };

      final response = await _client!
          .from('dhikr_routines')
          .upsert(routineData)
          .select()
          .single();

      return response;
    } catch (e) {
      debugPrint('Error upserting dhikr routine: $e');
      rethrow;
    }
  }

  // Backup operations
  Future<Map<String, dynamic>> createFullBackup() async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final backupData = {
        'user_id': _currentUser!.id,
        'backup_type': 'full',
        'created_at': DateTime.now().toIso8601String(),
        'data': {
          'notes': await syncNotes(),
          'todos': await syncTodos(),
          'voice_memos': await syncVoiceMemos(),
          'dhikr_routines': await syncDhikrRoutines(),
        },
      };

      final response = await _client!
          .from('backups')
          .insert(backupData)
          .select()
          .single();

      _emitSyncEvent(SyncEvent.backupCreated(success: true));
      return response;
    } catch (e) {
      _emitSyncEvent(SyncEvent.backupCreated(success: false, error: e.toString()));
      debugPrint('Error creating backup: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getBackups() async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final response = await _client!
          .from('backups')
          .select()
          .eq('user_id', _currentUser!.id)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting backups: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> restoreBackup(String backupId) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final response = await _client!
          .from('backups')
          .select()
          .eq('id', backupId)
          .eq('user_id', _currentUser!.id)
          .single();

      _emitSyncEvent(SyncEvent.backupRestored(success: true));
      return response;
    } catch (e) {
      _emitSyncEvent(SyncEvent.backupRestored(success: false, error: e.toString()));
      debugPrint('Error restoring backup: $e');
      rethrow;
    }
  }

  // Settings sync
  Future<Map<String, dynamic>?> syncSettings() async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final response = await _client!
          .from('user_settings')
          .select()
          .eq('user_id', _currentUser!.id)
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('Error syncing settings: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> upsertSettings(Map<String, dynamic> settings) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final settingsData = {
        'user_id': _currentUser!.id,
        'settings': settings,
        'modified_at': DateTime.now().toIso8601String(),
      };

      final response = await _client!
          .from('user_settings')
          .upsert(settingsData)
          .select()
          .single();

      return response;
    } catch (e) {
      debugPrint('Error upserting settings: $e');
      rethrow;
    }
  }

  // File storage
  Future<String> uploadFile(String bucket, String path, List<int> data) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final filePath = '${_currentUser!.id}/$path';
      await _client!.storage.from(bucket).uploadBinary(filePath, Uint8List.fromList(data));
      
      final publicUrl = _client!.storage.from(bucket).getPublicUrl(filePath);
      return publicUrl;
    } catch (e) {
      debugPrint('Error uploading file: $e');
      rethrow;
    }
  }

  Future<List<int>> downloadFile(String bucket, String path) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final filePath = '${_currentUser!.id}/$path';
      final data = await _client!.storage.from(bucket).download(filePath);
      return data;
    } catch (e) {
      debugPrint('Error downloading file: $e');
      rethrow;
    }
  }

  Future<void> deleteFile(String bucket, String path) async {
    if (!isAuthenticated) throw Exception('User not authenticated');

    try {
      final filePath = '${_currentUser!.id}/$path';
      await _client!.storage.from(bucket).remove([filePath]);
    } catch (e) {
      debugPrint('Error deleting file: $e');
      rethrow;
    }
  }

  // Private methods
  void _emitSyncEvent(SyncEvent event) {
    _syncEventController.add(event);
  }

  // Dispose resources
  void dispose() {
    _authStateController.close();
    _syncEventController.close();
  }
}

// Auth state model
class AuthState {
  final User? user;
  final AuthChangeEvent event;

  AuthState({
    required this.user,
    required this.event,
  });

  bool get isAuthenticated => user != null;
}

// Sync event model
class SyncEvent {
  final String type;
  final bool success;
  final String? error;
  final DateTime timestamp;

  SyncEvent({
    required this.type,
    required this.success,
    this.error,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory SyncEvent.notesSync({required bool success, String? error}) {
    return SyncEvent(type: 'notes_sync', success: success, error: error);
  }

  factory SyncEvent.todosSync({required bool success, String? error}) {
    return SyncEvent(type: 'todos_sync', success: success, error: error);
  }

  factory SyncEvent.voiceMemosSync({required bool success, String? error}) {
    return SyncEvent(type: 'voice_memos_sync', success: success, error: error);
  }

  factory SyncEvent.dhikrSync({required bool success, String? error}) {
    return SyncEvent(type: 'dhikr_sync', success: success, error: error);
  }

  factory SyncEvent.backupCreated({required bool success, String? error}) {
    return SyncEvent(type: 'backup_created', success: success, error: error);
  }

  factory SyncEvent.backupRestored({required bool success, String? error}) {
    return SyncEvent(type: 'backup_restored', success: success, error: error);
  }
}
