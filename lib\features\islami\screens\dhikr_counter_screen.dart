import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr_models.dart';
import '../services/quran_service.dart';

/// Dhikr counter screen for recitation sessions
class DhikrCounterScreen extends ConsumerStatefulWidget {
  final Dhikr dhikr;
  final DhikrSession? existingSession;

  const DhikrCounterScreen({
    super.key,
    required this.dhikr,
    this.existingSession,
  });

  @override
  ConsumerState<DhikrCounterScreen> createState() => _DhikrCounterScreenState();
}

class _DhikrCounterScreenState extends ConsumerState<DhikrCounterScreen>
    with TickerProviderStateMixin {
  final DhikrService _dhikrService = DhikrService();
  late DhikrSession _session;
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;

  int _targetCount = 33;
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeSession();
    _setupAnimations();
  }

  void _initializeSession() {
    if (widget.existingSession != null) {
      _session = widget.existingSession!;
      _targetCount = _session.targetCount;
    } else {
      _targetCount = widget.dhikr.recommendedCount;
      _session = DhikrSession(
        id: 'session_${DateTime.now().millisecondsSinceEpoch}',
        dhikrId: widget.dhikr.id,
        targetCount: _targetCount,
        startTime: DateTime.now(),
      );
    }
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: _session.progressPercentage / 100,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // Start with current progress
    _progressController.animateTo(_session.progressPercentage / 100);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.dhikr.textTransliteration),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'reset',
                child: ListTile(
                  leading: Icon(Icons.refresh),
                  title: Text('Reset Counter'),
                ),
              ),
              const PopupMenuItem(
                value: 'change_target',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Change Target'),
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Session History'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return LinearProgressIndicator(
                      value: _progressAnimation.value,
                      backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _isCompleted 
                            ? Colors.green
                            : Theme.of(context).colorScheme.primary,
                      ),
                      minHeight: 8,
                    );
                  },
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${_session.currentCount} / ${_session.targetCount}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${(_session.progressPercentage).toInt()}%',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Dhikr text
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Arabic text
                  Text(
                    widget.dhikr.textArabic,
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      fontFamily: 'Amiri',
                      height: 1.8,
                      fontSize: 32,
                    ),
                    textAlign: TextAlign.center,
                    textDirection: TextDirection.rtl,
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Transliteration
                  Text(
                    widget.dhikr.textTransliteration,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontStyle: FontStyle.italic,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Translation
                  Text(
                    widget.dhikr.textTranslation,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  if (widget.dhikr.benefits.isNotEmpty) ...[
                    const SizedBox(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.lightbulb_outline,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.dhikr.benefits,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontStyle: FontStyle.italic,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Counter button
          Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Main counter button
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: GestureDetector(
                        onTap: _incrementCounter,
                        child: Container(
                          width: 150,
                          height: 150,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _isCompleted 
                                ? Colors.green
                                : Theme.of(context).colorScheme.primary,
                            boxShadow: [
                              BoxShadow(
                                color: (_isCompleted ? Colors.green : Theme.of(context).colorScheme.primary)
                                    .withOpacity(0.3),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _isCompleted ? Icons.check : Icons.touch_app,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  size: 40,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _isCompleted ? 'Completed!' : 'Tap',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(context).colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 24),
                
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: _session.currentCount > 0 ? _decrementCounter : null,
                      icon: const Icon(Icons.remove),
                      label: const Text('Undo'),
                    ),
                    ElevatedButton.icon(
                      onPressed: _resetCounter,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Reset'),
                    ),
                    ElevatedButton.icon(
                      onPressed: _isCompleted ? _completeSession : null,
                      icon: const Icon(Icons.check_circle),
                      label: const Text('Finish'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _incrementCounter() {
    if (_isCompleted) return;

    HapticFeedback.lightImpact();
    
    setState(() {
      _session = _session.copyWith(
        currentCount: _session.currentCount + 1,
        recitationTimes: [..._session.recitationTimes, DateTime.now()],
      );
      
      if (_session.currentCount >= _session.targetCount) {
        _isCompleted = true;
        _session = _session.copyWith(
          isCompleted: true,
          endTime: DateTime.now(),
        );
        _showCompletionDialog();
      }
    });

    // Animate progress
    _progressController.animateTo(_session.progressPercentage / 100);
    
    // Pulse animation
    _pulseController.forward().then((_) {
      _pulseController.reverse();
    });

    // Update service
    _dhikrService.updateSessionCount(_session.id);
  }

  void _decrementCounter() {
    if (_session.currentCount <= 0) return;

    HapticFeedback.lightImpact();
    
    setState(() {
      _session = _session.copyWith(
        currentCount: _session.currentCount - 1,
        recitationTimes: _session.recitationTimes.take(_session.recitationTimes.length - 1).toList(),
      );
      
      if (_isCompleted && _session.currentCount < _session.targetCount) {
        _isCompleted = false;
        _session = _session.copyWith(
          isCompleted: false,
          endTime: null,
        );
      }
    });

    _progressController.animateTo(_session.progressPercentage / 100);
  }

  void _resetCounter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Counter'),
        content: const Text('Are you sure you want to reset the counter? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _session = _session.copyWith(
                  currentCount: 0,
                  recitationTimes: [],
                  isCompleted: false,
                  endTime: null,
                );
                _isCompleted = false;
              });
              _progressController.reset();
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _completeSession() {
    _dhikrService.completeSession(_session.id);
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Session completed! May Allah accept your dhikr.'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showCompletionDialog() {
    HapticFeedback.heavyImpact();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.celebration,
              color: Colors.green,
            ),
            const SizedBox(width: 8),
            const Text('Alhamdulillah!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('You have completed your dhikr session.'),
            const SizedBox(height: 16),
            Text(
              'Duration: ${_formatDuration(_session.sessionDuration)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetCounter();
            },
            child: const Text('Continue'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _completeSession();
            },
            child: const Text('Finish'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Session Settings'),
        content: const Text('Session settings will be available in the next update.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'reset':
        _resetCounter();
        break;
      case 'change_target':
        _showChangeTargetDialog();
        break;
      case 'history':
        _showSessionHistory();
        break;
    }
  }

  void _showChangeTargetDialog() {
    final controller = TextEditingController(text: _targetCount.toString());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Target Count'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Target Count',
            hintText: 'Enter target count',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newTarget = int.tryParse(controller.text);
              if (newTarget != null && newTarget > 0) {
                setState(() {
                  _targetCount = newTarget;
                  _session = _session.copyWith(targetCount: newTarget);
                });
                Navigator.of(context).pop();
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showSessionHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Session history coming soon!')),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }
}
