import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/quran_models.dart';
import '../services/quran_service.dart';

/// Quran state
class QuranState {
  final List<QuranSurah> surahs;
  final List<QuranVerse> allVerses;
  final List<QuranBookmark> bookmarks;
  final QuranPreferences preferences;
  final bool isLoading;
  final String? error;
  final List<QuranSearchResult> searchResults;
  final String searchQuery;
  final int? currentSurahNumber;
  final int? currentVerseNumber;

  const QuranState({
    this.surahs = const [],
    this.allVerses = const [],
    this.bookmarks = const [],
    this.preferences = const QuranPreferences(),
    this.isLoading = false,
    this.error,
    this.searchResults = const [],
    this.searchQuery = '',
    this.currentSurahNumber,
    this.currentVerseNumber,
  });

  /// Copy with modifications
  QuranState copyWith({
    List<QuranSurah>? surahs,
    List<QuranVerse>? allVerses,
    List<QuranBookmark>? bookmarks,
    QuranPreferences? preferences,
    bool? isLoading,
    String? error,
    List<QuranSearchResult>? searchResults,
    String? searchQuery,
    int? currentSurahNumber,
    int? currentVerseNumber,
  }) {
    return QuranState(
      surahs: surahs ?? this.surahs,
      allVerses: allVerses ?? this.allVerses,
      bookmarks: bookmarks ?? this.bookmarks,
      preferences: preferences ?? this.preferences,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      searchResults: searchResults ?? this.searchResults,
      searchQuery: searchQuery ?? this.searchQuery,
      currentSurahNumber: currentSurahNumber ?? this.currentSurahNumber,
      currentVerseNumber: currentVerseNumber ?? this.currentVerseNumber,
    );
  }

  /// Get current Surah
  QuranSurah? get currentSurah {
    if (currentSurahNumber == null) return null;
    try {
      return surahs.firstWhere((surah) => surah.number == currentSurahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get current verse
  QuranVerse? get currentVerse {
    if (currentSurahNumber == null || currentVerseNumber == null) return null;
    final surah = currentSurah;
    if (surah == null) return null;
    
    try {
      return surah.verses.firstWhere((verse) => verse.verseNumber == currentVerseNumber);
    } catch (e) {
      return null;
    }
  }

  /// Check if verse is bookmarked
  bool isVerseBookmarked(QuranVerse verse) {
    return bookmarks.any((bookmark) => 
        bookmark.verse.surahNumber == verse.surahNumber &&
        bookmark.verse.verseNumber == verse.verseNumber);
  }

  /// Get bookmark for verse
  QuranBookmark? getBookmarkForVerse(QuranVerse verse) {
    try {
      return bookmarks.firstWhere((bookmark) => 
          bookmark.verse.surahNumber == verse.surahNumber &&
          bookmark.verse.verseNumber == verse.verseNumber);
    } catch (e) {
      return null;
    }
  }
}

/// Quran notifier
class QuranNotifier extends StateNotifier<QuranState> {
  QuranNotifier() : super(const QuranState()) {
    _loadInitialData();
  }

  /// Load initial data
  Future<void> _loadInitialData() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Load Quran data
      final surahs = await QuranService.loadSurahs();
      final allVerses = await QuranService.loadQuranData();
      
      // Load saved data
      final preferences = await _loadPreferences();
      final bookmarks = await _loadBookmarks();
      
      state = state.copyWith(
        surahs: surahs,
        allVerses: allVerses,
        preferences: preferences,
        bookmarks: bookmarks,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load Quran data: $e',
      );
    }
  }

  /// Navigate to specific Surah
  Future<void> navigateToSurah(int surahNumber) async {
    state = state.copyWith(
      currentSurahNumber: surahNumber,
      currentVerseNumber: 1,
    );
  }

  /// Navigate to specific verse
  Future<void> navigateToVerse(int surahNumber, int verseNumber) async {
    state = state.copyWith(
      currentSurahNumber: surahNumber,
      currentVerseNumber: verseNumber,
    );
  }

  /// Search verses
  Future<void> searchVerses(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(
        searchResults: [],
        searchQuery: '',
      );
      return;
    }

    state = state.copyWith(isLoading: true);
    
    try {
      final results = await QuranService.searchVerses(query);
      state = state.copyWith(
        searchResults: results,
        searchQuery: query,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Search failed: $e',
      );
    }
  }

  /// Clear search results
  void clearSearch() {
    state = state.copyWith(
      searchResults: [],
      searchQuery: '',
    );
  }

  /// Add bookmark
  Future<void> addBookmark(QuranVerse verse, {String? note, List<String>? tags}) async {
    if (state.isVerseBookmarked(verse)) return;
    
    final bookmark = QuranBookmark.create(verse, note: note, tags: tags);
    final updatedBookmarks = [...state.bookmarks, bookmark];
    
    state = state.copyWith(bookmarks: updatedBookmarks);
    await _saveBookmarks();
  }

  /// Remove bookmark
  Future<void> removeBookmark(QuranVerse verse) async {
    final updatedBookmarks = state.bookmarks.where((bookmark) =>
        !(bookmark.verse.surahNumber == verse.surahNumber &&
          bookmark.verse.verseNumber == verse.verseNumber)).toList();
    
    state = state.copyWith(bookmarks: updatedBookmarks);
    await _saveBookmarks();
  }

  /// Toggle bookmark
  Future<void> toggleBookmark(QuranVerse verse, {String? note, List<String>? tags}) async {
    if (state.isVerseBookmarked(verse)) {
      await removeBookmark(verse);
    } else {
      await addBookmark(verse, note: note, tags: tags);
    }
  }

  /// Update bookmark
  Future<void> updateBookmark(String bookmarkId, {String? note, List<String>? tags}) async {
    final bookmarkIndex = state.bookmarks.indexWhere((b) => b.id == bookmarkId);
    if (bookmarkIndex == -1) return;
    
    final updatedBookmarks = [...state.bookmarks];
    updatedBookmarks[bookmarkIndex] = updatedBookmarks[bookmarkIndex].copyWith(
      note: note,
      tags: tags,
    );
    
    state = state.copyWith(bookmarks: updatedBookmarks);
    await _saveBookmarks();
  }

  /// Update preferences
  Future<void> updatePreferences(QuranPreferences preferences) async {
    state = state.copyWith(preferences: preferences);
    await _savePreferences();
  }

  /// Load preferences from storage
  Future<QuranPreferences> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final prefsJson = prefs.getString('quran_preferences');
      if (prefsJson != null) {
        final Map<String, dynamic> data = json.decode(prefsJson);
        return QuranPreferences.fromJson(data);
      }
    } catch (e) {
      // Ignore errors and return default preferences
    }
    return const QuranPreferences();
  }

  /// Save preferences to storage
  Future<void> _savePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final prefsJson = json.encode(state.preferences.toJson());
      await prefs.setString('quran_preferences', prefsJson);
    } catch (e) {
      // Ignore save errors
    }
  }

  /// Load bookmarks from storage
  Future<List<QuranBookmark>> _loadBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookmarksJson = prefs.getString('quran_bookmarks');
      if (bookmarksJson != null) {
        final List<dynamic> data = json.decode(bookmarksJson);
        return data.map((item) => QuranBookmark.fromJson(item)).toList();
      }
    } catch (e) {
      // Ignore errors and return empty list
    }
    return [];
  }

  /// Save bookmarks to storage
  Future<void> _saveBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookmarksJson = json.encode(
        state.bookmarks.map((bookmark) => bookmark.toJson()).toList(),
      );
      await prefs.setString('quran_bookmarks', bookmarksJson);
    } catch (e) {
      // Ignore save errors
    }
  }

  /// Refresh data
  Future<void> refresh() async {
    await _loadInitialData();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for Quran
final quranProvider = StateNotifierProvider<QuranNotifier, QuranState>((ref) {
  return QuranNotifier();
});

/// Provider for current Surah
final currentSurahProvider = Provider<QuranSurah?>((ref) {
  final quranState = ref.watch(quranProvider);
  return quranState.currentSurah;
});

/// Provider for current verse
final currentVerseProvider = Provider<QuranVerse?>((ref) {
  final quranState = ref.watch(quranProvider);
  return quranState.currentVerse;
});

/// Provider for bookmarked verses
final bookmarkedVersesProvider = Provider<List<QuranVerse>>((ref) {
  final quranState = ref.watch(quranProvider);
  return quranState.bookmarks.map((bookmark) => bookmark.verse).toList();
});

/// Provider for search results
final searchResultsProvider = Provider<List<QuranSearchResult>>((ref) {
  final quranState = ref.watch(quranProvider);
  return quranState.searchResults;
});
