import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  static const String _microphonePermissionKey = 'microphone_permission_granted';
  static const String _storagePermissionKey = 'storage_permission_granted';
  static const String _notificationPermissionKey = 'notification_permission_granted';

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Permission status cache
  bool _microphoneGranted = false;
  bool _storageGranted = false;
  bool _notificationGranted = false;

  // Stream controllers for permission status changes
  final _microphoneStatusController = StreamController<bool>.broadcast();
  final _storageStatusController = StreamController<bool>.broadcast();
  final _notificationStatusController = StreamController<bool>.broadcast();

  // Getters for permission status
  bool get microphoneGranted => _microphoneGranted;
  bool get storageGranted => _storageGranted;
  bool get notificationGranted => _notificationGranted;

  // Streams for permission status changes
  Stream<bool> get microphoneStatusStream => _microphoneStatusController.stream;
  Stream<bool> get storageStatusStream => _storageStatusController.stream;
  Stream<bool> get notificationStatusStream => _notificationStatusController.stream;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadPermissionStates();
      await _checkAllPermissions();
      _isInitialized = true;
      debugPrint('PermissionService initialized');
    } catch (e) {
      debugPrint('Error initializing PermissionService: $e');
      rethrow;
    }
  }

  Future<void> _loadPermissionStates() async {
    if (_prefs == null) return;

    _microphoneGranted = _prefs!.getBool(_microphonePermissionKey) ?? false;
    _storageGranted = _prefs!.getBool(_storagePermissionKey) ?? false;
    _notificationGranted = _prefs!.getBool(_notificationPermissionKey) ?? false;
  }

  Future<void> _savePermissionState(String key, bool granted) async {
    if (_prefs == null) return;
    await _prefs!.setBool(key, granted);
  }

  Future<void> _checkAllPermissions() async {
    await _checkMicrophonePermission();
    await _checkStoragePermission();
    await _checkNotificationPermission();
  }

  Future<void> _checkMicrophonePermission() async {
    try {
      final status = await Permission.microphone.status;
      final granted = status.isGranted;
      
      if (_microphoneGranted != granted) {
        _microphoneGranted = granted;
        await _savePermissionState(_microphonePermissionKey, granted);
        _microphoneStatusController.add(granted);
        debugPrint('Microphone permission status changed: $granted');
      }
    } catch (e) {
      debugPrint('Error checking microphone permission: $e');
    }
  }

  Future<void> _checkStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      final granted = status.isGranted || status.isLimited;
      
      if (_storageGranted != granted) {
        _storageGranted = granted;
        await _savePermissionState(_storagePermissionKey, granted);
        _storageStatusController.add(granted);
        debugPrint('Storage permission status changed: $granted');
      }
    } catch (e) {
      debugPrint('Error checking storage permission: $e');
    }
  }

  Future<void> _checkNotificationPermission() async {
    try {
      final status = await Permission.notification.status;
      final granted = status.isGranted;
      
      if (_notificationGranted != granted) {
        _notificationGranted = granted;
        await _savePermissionState(_notificationPermissionKey, granted);
        _notificationStatusController.add(granted);
        debugPrint('Notification permission status changed: $granted');
      }
    } catch (e) {
      debugPrint('Error checking notification permission: $e');
    }
  }

  // Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    try {
      debugPrint('Requesting microphone permission...');
      
      // First check current status
      final currentStatus = await Permission.microphone.status;
      debugPrint('Current microphone permission status: $currentStatus');
      
      if (currentStatus.isGranted) {
        _microphoneGranted = true;
        await _savePermissionState(_microphonePermissionKey, true);
        _microphoneStatusController.add(true);
        return true;
      }

      if (currentStatus.isPermanentlyDenied) {
        debugPrint('Microphone permission permanently denied');
        return false;
      }

      // Request permission
      final status = await Permission.microphone.request();
      debugPrint('Microphone permission request result: $status');
      
      final granted = status.isGranted;
      _microphoneGranted = granted;
      await _savePermissionState(_microphonePermissionKey, granted);
      _microphoneStatusController.add(granted);
      
      return granted;
    } catch (e) {
      debugPrint('Error requesting microphone permission: $e');
      return false;
    }
  }

  // Request storage permission
  Future<bool> requestStoragePermission() async {
    try {
      debugPrint('Requesting storage permission...');
      
      final currentStatus = await Permission.storage.status;
      debugPrint('Current storage permission status: $currentStatus');
      
      if (currentStatus.isGranted || currentStatus.isLimited) {
        _storageGranted = true;
        await _savePermissionState(_storagePermissionKey, true);
        _storageStatusController.add(true);
        return true;
      }

      if (currentStatus.isPermanentlyDenied) {
        debugPrint('Storage permission permanently denied');
        return false;
      }

      final status = await Permission.storage.request();
      debugPrint('Storage permission request result: $status');
      
      final granted = status.isGranted || status.isLimited;
      _storageGranted = granted;
      await _savePermissionState(_storagePermissionKey, granted);
      _storageStatusController.add(granted);
      
      return granted;
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      return false;
    }
  }

  // Request notification permission
  Future<bool> requestNotificationPermission() async {
    try {
      debugPrint('Requesting notification permission...');
      
      final currentStatus = await Permission.notification.status;
      debugPrint('Current notification permission status: $currentStatus');
      
      if (currentStatus.isGranted) {
        _notificationGranted = true;
        await _savePermissionState(_notificationPermissionKey, true);
        _notificationStatusController.add(true);
        return true;
      }

      if (currentStatus.isPermanentlyDenied) {
        debugPrint('Notification permission permanently denied');
        return false;
      }

      final status = await Permission.notification.request();
      debugPrint('Notification permission request result: $status');
      
      final granted = status.isGranted;
      _notificationGranted = granted;
      await _savePermissionState(_notificationPermissionKey, granted);
      _notificationStatusController.add(granted);
      
      return granted;
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      return false;
    }
  }

  // Request all required permissions for voice recording
  Future<bool> requestVoiceRecordingPermissions() async {
    debugPrint('Requesting voice recording permissions...');
    
    final microphoneGranted = await requestMicrophonePermission();
    final storageGranted = await requestStoragePermission();
    
    final allGranted = microphoneGranted && storageGranted;
    debugPrint('Voice recording permissions result - Microphone: $microphoneGranted, Storage: $storageGranted, All granted: $allGranted');
    
    return allGranted;
  }

  // Check if voice recording permissions are granted
  Future<bool> hasVoiceRecordingPermissions() async {
    await _checkMicrophonePermission();
    await _checkStoragePermission();
    
    final hasPermissions = _microphoneGranted && _storageGranted;
    debugPrint('Voice recording permissions check: $hasPermissions (Microphone: $_microphoneGranted, Storage: $_storageGranted)');
    
    return hasPermissions;
  }

  // Open app settings
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
      debugPrint('Opened app settings');
    } catch (e) {
      debugPrint('Error opening app settings: $e');
    }
  }

  // Refresh all permission statuses
  Future<void> refreshPermissions() async {
    debugPrint('Refreshing all permissions...');
    await _checkAllPermissions();
  }

  // Get detailed permission status
  Future<Map<String, dynamic>> getPermissionStatus() async {
    await _checkAllPermissions();
    
    return {
      'microphone': {
        'granted': _microphoneGranted,
        'status': (await Permission.microphone.status).toString(),
      },
      'storage': {
        'granted': _storageGranted,
        'status': (await Permission.storage.status).toString(),
      },
      'notification': {
        'granted': _notificationGranted,
        'status': (await Permission.notification.status).toString(),
      },
    };
  }

  // Reset permission cache (useful for testing)
  Future<void> resetPermissionCache() async {
    if (_prefs == null) return;
    
    await _prefs!.remove(_microphonePermissionKey);
    await _prefs!.remove(_storagePermissionKey);
    await _prefs!.remove(_notificationPermissionKey);
    
    _microphoneGranted = false;
    _storageGranted = false;
    _notificationGranted = false;
    
    _microphoneStatusController.add(false);
    _storageStatusController.add(false);
    _notificationStatusController.add(false);
    
    debugPrint('Permission cache reset');
  }

  void dispose() {
    _microphoneStatusController.close();
    _storageStatusController.close();
    _notificationStatusController.close();
  }
}
