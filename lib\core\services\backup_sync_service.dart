import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../database/database_service.dart';

enum SyncStatus {
  idle,
  syncing,
  success,
  error,
  conflict,
}

class BackupSyncService {
  static final BackupSyncService _instance = BackupSyncService._internal();
  factory BackupSyncService() => _instance;
  BackupSyncService._internal();

  static BackupSyncService get instance => _instance;

  final DatabaseService _db = DatabaseService.instance;
  SupabaseClient? _supabase;
  
  // Keys for SharedPreferences
  static const String _keyLastSyncTime = 'last_sync_time';
  static const String _keyAutoSyncEnabled = 'auto_sync_enabled';
  static const String _keySyncOnWifiOnly = 'sync_on_wifi_only';
  static const String _keyBackupFrequency = 'backup_frequency';
  static const String _keyLastBackupTime = 'last_backup_time';

  SyncStatus _currentStatus = SyncStatus.idle;
  String? _lastError;
  DateTime? _lastSyncTime;

  /// Initialize Supabase connection
  Future<void> initialize() async {
    try {
      // Try to get existing Supabase instance or initialize if needed
      try {
        _supabase = Supabase.instance.client;
      } catch (e) {
        // Supabase not initialized, skip for now
        debugPrint('Supabase not initialized, sync features will be disabled');
      }
      _supabase = Supabase.instance.client;
      await _loadSyncSettings();
    } catch (e) {
      debugPrint('Error initializing backup sync service: $e');
    }
  }

  /// Get current sync status
  SyncStatus get currentStatus => _currentStatus;

  /// Get last error message
  String? get lastError => _lastError;

  /// Get last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Check if user is authenticated with Supabase
  bool get isAuthenticated => _supabase?.auth.currentUser != null;

  /// Sign in with email and password
  Future<bool> signIn(String email, String password) async {
    try {
      if (_supabase == null) return false;

      final response = await _supabase!.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await _setupUserTables();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Sign in error: $e');
      _lastError = e.toString();
      return false;
    }
  }

  /// Sign up with email and password
  Future<bool> signUp(String email, String password) async {
    try {
      if (_supabase == null) return false;

      final response = await _supabase!.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await _setupUserTables();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Sign up error: $e');
      _lastError = e.toString();
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _supabase?.auth.signOut();
    } catch (e) {
      debugPrint('Sign out error: $e');
    }
  }

  /// Perform full sync (upload and download)
  Future<bool> performFullSync() async {
    if (!isAuthenticated) {
      _lastError = 'User not authenticated';
      return false;
    }

    _currentStatus = SyncStatus.syncing;
    _lastError = null;

    try {
      // Upload local changes first
      await _uploadLocalChanges();
      
      // Then download remote changes
      await _downloadRemoteChanges();
      
      // Update last sync time
      await _updateLastSyncTime();
      
      _currentStatus = SyncStatus.success;
      return true;
    } catch (e) {
      debugPrint('Full sync error: $e');
      _lastError = e.toString();
      _currentStatus = SyncStatus.error;
      return false;
    }
  }

  /// Upload only local changes
  Future<bool> uploadLocalChanges() async {
    if (!isAuthenticated) return false;

    _currentStatus = SyncStatus.syncing;
    
    try {
      await _uploadLocalChanges();
      _currentStatus = SyncStatus.success;
      return true;
    } catch (e) {
      debugPrint('Upload error: $e');
      _lastError = e.toString();
      _currentStatus = SyncStatus.error;
      return false;
    }
  }

  /// Download only remote changes
  Future<bool> downloadRemoteChanges() async {
    if (!isAuthenticated) return false;

    _currentStatus = SyncStatus.syncing;
    
    try {
      await _downloadRemoteChanges();
      _currentStatus = SyncStatus.success;
      return true;
    } catch (e) {
      debugPrint('Download error: $e');
      _lastError = e.toString();
      _currentStatus = SyncStatus.error;
      return false;
    }
  }

  /// Create local backup
  Future<String?> createLocalBackup() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupFile = File('${backupDir.path}/shadowsuite_backup_$timestamp.json');

      // Collect all data
      final backupData = {
        'version': '1.0',
        'timestamp': DateTime.now().toIso8601String(),
        'notes': await _db.getAllNotes(),
        'todos': await _db.getAllTodos(),
        'voiceMemos': await _db.getAllVoiceMemos(),
        'accounts': await _db.getAllAccounts(),
        'transactions': await _db.getAllTransactions(),
        'categories': await _db.getAllCategories(),
        'settings': await _getAppSettings(),
      };

      // Convert to JSON and save
      final jsonString = jsonEncode(backupData);
      await backupFile.writeAsString(jsonString);

      // Update last backup time
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_keyLastBackupTime, DateTime.now().millisecondsSinceEpoch);

      return backupFile.path;
    } catch (e) {
      debugPrint('Local backup error: $e');
      _lastError = e.toString();
      return null;
    }
  }

  /// Restore from local backup
  Future<bool> restoreFromLocalBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        _lastError = 'Backup file not found';
        return false;
      }

      final jsonString = await backupFile.readAsString();
      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Validate backup format
      if (backupData['version'] == null) {
        _lastError = 'Invalid backup format';
        return false;
      }

      // Clear existing data (with confirmation)
      await _db.clearAllData();

      // Restore data
      if (backupData['notes'] != null) {
        for (final noteData in backupData['notes']) {
          // Restore notes
        }
      }

      if (backupData['todos'] != null) {
        for (final todoData in backupData['todos']) {
          // Restore todos
        }
      }

      // Continue for other data types...

      return true;
    } catch (e) {
      debugPrint('Restore backup error: $e');
      _lastError = e.toString();
      return false;
    }
  }

  /// Get list of local backups
  Future<List<FileSystemEntity>> getLocalBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      
      if (!await backupDir.exists()) {
        return [];
      }

      final backups = await backupDir.list().toList();
      backups.sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));
      
      return backups;
    } catch (e) {
      debugPrint('Error getting local backups: $e');
      return [];
    }
  }

  /// Enable/disable auto sync
  Future<void> setAutoSyncEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyAutoSyncEnabled, enabled);
  }

  /// Check if auto sync is enabled
  Future<bool> isAutoSyncEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyAutoSyncEnabled) ?? false;
  }

  /// Set sync on WiFi only
  Future<void> setSyncOnWifiOnly(bool wifiOnly) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keySyncOnWifiOnly, wifiOnly);
  }

  /// Check if sync on WiFi only is enabled
  Future<bool> isSyncOnWifiOnly() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keySyncOnWifiOnly) ?? true;
  }

  /// Set backup frequency (in hours)
  Future<void> setBackupFrequency(int hours) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyBackupFrequency, hours);
  }

  /// Get backup frequency (in hours)
  Future<int> getBackupFrequency() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyBackupFrequency) ?? 24; // Default: daily
  }

  /// Check if backup is due
  Future<bool> isBackupDue() async {
    final prefs = await SharedPreferences.getInstance();
    final lastBackupTime = prefs.getInt(_keyLastBackupTime) ?? 0;
    final frequency = await getBackupFrequency();
    
    if (lastBackupTime == 0) return true;
    
    final now = DateTime.now().millisecondsSinceEpoch;
    final timeDifference = now - lastBackupTime;
    final hoursPassed = timeDifference / (1000 * 60 * 60);
    
    return hoursPassed >= frequency;
  }

  // Private helper methods

  Future<void> _loadSyncSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSyncTimestamp = prefs.getInt(_keyLastSyncTime);
    
    if (lastSyncTimestamp != null) {
      _lastSyncTime = DateTime.fromMillisecondsSinceEpoch(lastSyncTimestamp);
    }
  }

  Future<void> _setupUserTables() async {
    if (_supabase == null || !isAuthenticated) return;

    try {
      final userId = _supabase!.auth.currentUser!.id;
      
      // Create user-specific tables if they don't exist
      // This would typically be done via database migrations
      // For now, we'll assume tables exist with RLS policies
      
    } catch (e) {
      debugPrint('Error setting up user tables: $e');
    }
  }

  Future<void> _uploadLocalChanges() async {
    if (_supabase == null || !isAuthenticated) return;

    final userId = _supabase!.auth.currentUser!.id;

    // Upload notes that need sync
    final notes = await _db.getAllNotes();
    for (final note in notes.where((n) => n.needsSync)) {
      await _supabase!.from('notes').upsert({
        'id': note.syncId ?? note.id.toString(),
        'user_id': userId,
        'title': note.title,
        'content': note.content,
        'tags': note.tags,
        'is_pinned': note.isPinned,
        'created_at': note.createdAt.toIso8601String(),
        'updated_at': note.updatedAt.toIso8601String(),
        'is_deleted': note.isDeleted,
      });
      
      // Mark as synced
      note.markSynced();
      await _db.saveNote(note);
    }

    // Similar for other data types...
  }

  Future<void> _downloadRemoteChanges() async {
    if (_supabase == null || !isAuthenticated) return;

    final userId = _supabase!.auth.currentUser!.id;
    final lastSync = _lastSyncTime ?? DateTime.fromMillisecondsSinceEpoch(0);

    // Download notes updated since last sync
    final remoteNotes = await _supabase!
        .from('notes')
        .select()
        .eq('user_id', userId)
        .gte('updated_at', lastSync.toIso8601String());

    for (final noteData in remoteNotes) {
      // Convert and save to local database
      // Handle conflicts if local version exists
    }

    // Similar for other data types...
  }

  Future<void> _updateLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    
    await prefs.setInt(_keyLastSyncTime, now.millisecondsSinceEpoch);
    _lastSyncTime = now;
  }

  Future<Map<String, dynamic>> _getAppSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    final settings = <String, dynamic>{};
    
    for (final key in keys) {
      final value = prefs.get(key);
      if (value != null) {
        settings[key] = value;
      }
    }
    
    return settings;
  }
}
