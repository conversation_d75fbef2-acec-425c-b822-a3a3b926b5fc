import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prayer_time.dart';
import '../models/location_settings.dart';
import '../providers/prayer_time_provider.dart';
import '../services/location_service.dart';

/// Screen for manually editing prayer times
class PrayerTimeEditorScreen extends ConsumerStatefulWidget {
  final DateTime date;
  final LocationSettings? location;

  const PrayerTimeEditorScreen({
    super.key,
    required this.date,
    this.location,
  });

  @override
  ConsumerState<PrayerTimeEditorScreen> createState() => _PrayerTimeEditorScreenState();
}

class _PrayerTimeEditorScreenState extends ConsumerState<PrayerTimeEditorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _locationController = TextEditingController();
  
  late TimeOfDay _fajrTime;
  late TimeOfDay _dhuhrTime;
  late TimeOfDay _asrTime;
  late TimeOfDay _maghribTime;
  late TimeOfDay _ishaTime;
  
  LocationSettings? _selectedLocation;
  CalculationMethod _calculationMethod = CalculationMethod.muslimWorldLeague;
  bool _isSearchingLocation = false;
  List<LocationSearchResult> _locationResults = [];

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.location;
    
    // Initialize with default times or existing prayer times
    _initializeTimes();
    
    if (_selectedLocation != null) {
      _locationController.text = _selectedLocation!.displayLocation;
      _calculationMethod = _selectedLocation!.calculationMethod;
    }
  }

  void _initializeTimes() {
    // Set default times for now
    _setDefaultTimes();
  }

  void _setDefaultTimes() {
    _fajrTime = const TimeOfDay(hour: 5, minute: 30);
    _dhuhrTime = const TimeOfDay(hour: 12, minute: 15);
    _asrTime = const TimeOfDay(hour: 15, minute: 45);
    _maghribTime = const TimeOfDay(hour: 18, minute: 20);
    _ishaTime = const TimeOfDay(hour: 19, minute: 45);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Prayer Times - ${_formatDate(widget.date)}'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _savePrayerTimes,
            icon: const Icon(Icons.save),
            tooltip: 'Save Prayer Times',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Location Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Location',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        labelText: 'Search Location',
                        hintText: 'Enter city name',
                        prefixIcon: Icon(Icons.location_on),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: _searchLocation,
                    ),
                    
                    if (_isSearchingLocation) ...[
                      const SizedBox(height: 8),
                      const LinearProgressIndicator(),
                    ],
                    
                    if (_locationResults.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Container(
                        height: 200,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ListView.builder(
                          itemCount: _locationResults.length,
                          itemBuilder: (context, index) {
                            final result = _locationResults[index];
                            return ListTile(
                              title: Text(result.city),
                              subtitle: Text(result.country),
                              onTap: () => _selectLocation(result),
                            );
                          },
                        ),
                      ),
                    ],
                    
                    const SizedBox(height: 16),
                    
                    DropdownButtonFormField<CalculationMethod>(
                      value: _calculationMethod,
                      decoration: const InputDecoration(
                        labelText: 'Calculation Method',
                        border: OutlineInputBorder(),
                      ),
                      items: CalculationMethod.values.map((method) {
                        return DropdownMenuItem(
                          value: method,
                          child: Text(_getCalculationMethodName(method)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _calculationMethod = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Prayer Times Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Prayer Times',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    _buildTimeField('Fajr', _fajrTime, (time) => _fajrTime = time),
                    const SizedBox(height: 12),
                    _buildTimeField('Dhuhr', _dhuhrTime, (time) => _dhuhrTime = time),
                    const SizedBox(height: 12),
                    _buildTimeField('Asr', _asrTime, (time) => _asrTime = time),
                    const SizedBox(height: 12),
                    _buildTimeField('Maghrib', _maghribTime, (time) => _maghribTime = time),
                    const SizedBox(height: 12),
                    _buildTimeField('Isha', _ishaTime, (time) => _ishaTime = time),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _resetToCalculated,
                    child: const Text('Reset to Calculated'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _savePrayerTimes,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Save Times'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeField(String label, TimeOfDay time, Function(TimeOfDay) onChanged) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: InkWell(
            onTap: () => _selectTime(time, onChanged),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.access_time),
                  const SizedBox(width: 8),
                  Text(
                    time.format(context),
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectTime(TimeOfDay currentTime, Function(TimeOfDay) onChanged) async {
    final time = await showTimePicker(
      context: context,
      initialTime: currentTime,
    );
    
    if (time != null) {
      setState(() {
        onChanged(time);
      });
    }
  }

  void _searchLocation(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _locationResults = [];
        _isSearchingLocation = false;
      });
      return;
    }

    setState(() {
      _isSearchingLocation = true;
    });

    try {
      final results = await LocationService().searchLocations(query);
      setState(() {
        _locationResults = results;
        _isSearchingLocation = false;
      });
    } catch (e) {
      setState(() {
        _isSearchingLocation = false;
        _locationResults = [];
      });
    }
  }

  void _selectLocation(LocationSearchResult result) {
    setState(() {
      _selectedLocation = LocationService().searchResultToLocationSettings(
        result,
        'user_id', // TODO: Get actual user ID
        method: _calculationMethod,
      );
      _locationController.text = result.displayName;
      _locationResults = [];
    });
  }

  void _resetToCalculated() {
    // TODO: Implement reset to calculated times
    _setDefaultTimes();
    setState(() {});
  }

  void _savePrayerTimes() {
    if (_formKey.currentState?.validate() ?? false) {
      // TODO: Save prayer times using provider
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Prayer times saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getCalculationMethodName(CalculationMethod method) {
    return method.displayName;
  }

  @override
  void dispose() {
    _locationController.dispose();
    super.dispose();
  }
}
