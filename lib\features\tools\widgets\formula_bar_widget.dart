import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/spreadsheet_selection_provider.dart';
import '../providers/formula_assistance_provider.dart';

/// Enhanced formula bar with auto-complete and function assistance
class FormulaBarWidget extends ConsumerStatefulWidget {
  final String? selectedCell;
  final String? currentValue;
  final Function(String value) onValueSubmitted;
  final Function(String value)? onValueChanged;

  const FormulaBarWidget({
    super.key,
    this.selectedCell,
    this.currentValue,
    required this.onValueSubmitted,
    this.onValueChanged,
  });

  @override
  ConsumerState<FormulaBarWidget> createState() => _FormulaBarWidgetState();
}

class _FormulaBarWidgetState extends ConsumerState<FormulaBarWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  OverlayEntry? _suggestionOverlay;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.currentValue ?? '');
    _focusNode = FocusNode();
    
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void didUpdateWidget(FormulaBarWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentValue != oldWidget.currentValue) {
      _controller.text = widget.currentValue ?? '';
    }
  }

  @override
  void dispose() {
    _removeSuggestionOverlay();
    _controller.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Formula bar header
          Row(
            children: [
              // Cell reference display
              Container(
                width: 80,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: BorderRadius.circular(4),
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: Text(
                  widget.selectedCell ?? 'A1',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontFamily: 'monospace',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Function button
              IconButton(
                onPressed: _showFunctionDialog,
                icon: const Icon(Icons.functions),
                tooltip: 'Insert Function',
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Formula input field
              Expanded(
                child: CompositedTransformTarget(
                  link: _layerLink,
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontFamily: 'monospace',
                    ),
                    decoration: InputDecoration(
                      hintText: 'Enter value or formula (e.g., =A1+B1)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      suffixIcon: _controller.text.isNotEmpty
                          ? IconButton(
                              onPressed: _clearFormula,
                              icon: const Icon(Icons.clear),
                              iconSize: 16,
                            )
                          : null,
                    ),
                    onSubmitted: _onSubmitted,
                    onChanged: _onChanged,
                  ),
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Apply/Cancel buttons
              Row(
                children: [
                  IconButton(
                    onPressed: _cancelEdit,
                    icon: const Icon(Icons.close),
                    tooltip: 'Cancel (Esc)',
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.errorContainer,
                    ),
                  ),
                  const SizedBox(width: 4),
                  IconButton(
                    onPressed: () => _onSubmitted(_controller.text),
                    icon: const Icon(Icons.check),
                    tooltip: 'Apply (Enter)',
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Quick function chips
          _buildQuickFunctionChips(),
        ],
      ),
    );
  }

  Widget _buildQuickFunctionChips() {
    final quickFunctions = [
      {'label': 'SUM', 'formula': '=SUM(A1:A10)'},
      {'label': 'AVERAGE', 'formula': '=AVERAGE(A1:A10)'},
      {'label': 'COUNT', 'formula': '=COUNT(A1:A10)'},
      {'label': 'MAX', 'formula': '=MAX(A1:A10)'},
      {'label': 'MIN', 'formula': '=MIN(A1:A10)'},
      {'label': 'IF', 'formula': '=IF(A1>0,"Positive","Negative")'},
      {'label': 'VLOOKUP', 'formula': '=VLOOKUP(A1,A:B,2,FALSE)'},
      {'label': 'TODAY', 'formula': '=TODAY()'},
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: quickFunctions.map((func) {
        return ActionChip(
          label: Text(
            func['label']!,
            style: const TextStyle(fontSize: 12),
          ),
          onPressed: () => _insertFunction(func['formula']!),
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          side: BorderSide(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          ),
        );
      }).toList(),
    );
  }

  void _onTextChanged() {
    widget.onValueChanged?.call(_controller.text);
    
    // Show suggestions for formulas
    if (_controller.text.startsWith('=') && _focusNode.hasFocus) {
      _showSuggestions();
    } else {
      _removeSuggestionOverlay();
    }
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _removeSuggestionOverlay();
    }
  }

  void _onSubmitted(String value) {
    widget.onValueSubmitted(value);
    _removeSuggestionOverlay();
  }

  void _onChanged(String value) {
    widget.onValueChanged?.call(value);
  }

  void _clearFormula() {
    _controller.clear();
    widget.onValueChanged?.call('');
  }

  void _cancelEdit() {
    _controller.text = widget.currentValue ?? '';
    _focusNode.unfocus();
    _removeSuggestionOverlay();
  }

  void _insertFunction(String formula) {
    _controller.text = formula;
    _controller.selection = TextSelection.fromPosition(
      TextPosition(offset: formula.length),
    );
    _focusNode.requestFocus();
  }

  void _showFunctionDialog() {
    showDialog(
      context: context,
      builder: (context) => _FunctionPickerDialog(
        onFunctionSelected: _insertFunction,
      ),
    );
  }

  void _showSuggestions() {
    _removeSuggestionOverlay();

    final suggestions = ref.read(formulaAssistanceProvider.notifier)
        .getFunctionSuggestions(_controller.text);
    
    if (suggestions.isEmpty) return;

    _suggestionOverlay = OverlayEntry(
      builder: (context) => CompositedTransformFollower(
        link: _layerLink,
        targetAnchor: Alignment.bottomLeft,
        followerAnchor: Alignment.topLeft,
        offset: const Offset(0, 4),
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: const BoxConstraints(
              maxHeight: 200,
              maxWidth: 300,
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: suggestions.length,
              itemBuilder: (context, index) {
                final suggestion = suggestions[index];
                return ListTile(
                  dense: true,
                  title: Text(
                    suggestion.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontFamily: 'monospace',
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        suggestion.syntax,
                        style: const TextStyle(
                          fontSize: 11,
                          fontFamily: 'monospace',
                          color: Colors.blue,
                        ),
                      ),
                      Text(
                        suggestion.description,
                        style: const TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                  onTap: () {
                    _insertFunction(suggestion.syntax);
                    _removeSuggestionOverlay();
                  },
                );
              },
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_suggestionOverlay!);
  }

  void _removeSuggestionOverlay() {
    _suggestionOverlay?.remove();
    _suggestionOverlay = null;
  }
}

/// Dialog for picking functions with categories
class _FunctionPickerDialog extends StatefulWidget {
  final Function(String formula) onFunctionSelected;

  const _FunctionPickerDialog({required this.onFunctionSelected});

  @override
  State<_FunctionPickerDialog> createState() => _FunctionPickerDialogState();
}

class _FunctionPickerDialogState extends State<_FunctionPickerDialog> {
  String selectedCategory = 'Math';
  
  final Map<String, List<Map<String, String>>> functionCategories = {
    'Math': [
      {'name': 'SUM', 'syntax': '=SUM(A1:A10)', 'desc': 'Adds all numbers in a range'},
      {'name': 'AVERAGE', 'syntax': '=AVERAGE(A1:A10)', 'desc': 'Calculates the average'},
      {'name': 'COUNT', 'syntax': '=COUNT(A1:A10)', 'desc': 'Counts numbers in a range'},
      {'name': 'MAX', 'syntax': '=MAX(A1:A10)', 'desc': 'Returns the largest value'},
      {'name': 'MIN', 'syntax': '=MIN(A1:A10)', 'desc': 'Returns the smallest value'},
      {'name': 'ROUND', 'syntax': '=ROUND(A1,2)', 'desc': 'Rounds a number to specified digits'},
    ],
    'Logical': [
      {'name': 'IF', 'syntax': '=IF(A1>0,"Positive","Negative")', 'desc': 'Returns value based on condition'},
      {'name': 'AND', 'syntax': '=AND(A1>0,B1>0)', 'desc': 'Returns TRUE if all conditions are true'},
      {'name': 'OR', 'syntax': '=OR(A1>0,B1>0)', 'desc': 'Returns TRUE if any condition is true'},
      {'name': 'NOT', 'syntax': '=NOT(A1>0)', 'desc': 'Reverses the logic of its argument'},
    ],
    'Text': [
      {'name': 'CONCAT', 'syntax': '=CONCAT(A1,B1)', 'desc': 'Joins text strings'},
      {'name': 'LEFT', 'syntax': '=LEFT(A1,5)', 'desc': 'Returns leftmost characters'},
      {'name': 'RIGHT', 'syntax': '=RIGHT(A1,5)', 'desc': 'Returns rightmost characters'},
      {'name': 'LEN', 'syntax': '=LEN(A1)', 'desc': 'Returns length of text'},
      {'name': 'UPPER', 'syntax': '=UPPER(A1)', 'desc': 'Converts text to uppercase'},
      {'name': 'LOWER', 'syntax': '=LOWER(A1)', 'desc': 'Converts text to lowercase'},
    ],
    'Date': [
      {'name': 'TODAY', 'syntax': '=TODAY()', 'desc': 'Returns current date'},
      {'name': 'NOW', 'syntax': '=NOW()', 'desc': 'Returns current date and time'},
      {'name': 'YEAR', 'syntax': '=YEAR(A1)', 'desc': 'Returns year from date'},
      {'name': 'MONTH', 'syntax': '=MONTH(A1)', 'desc': 'Returns month from date'},
      {'name': 'DAY', 'syntax': '=DAY(A1)', 'desc': 'Returns day from date'},
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Insert Function',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            
            // Category tabs
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: functionCategories.keys.map((category) {
                  final isSelected = selectedCategory == category;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ChoiceChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            selectedCategory = category;
                          });
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Function list
            Expanded(
              child: ListView.builder(
                itemCount: functionCategories[selectedCategory]?.length ?? 0,
                itemBuilder: (context, index) {
                  final function = functionCategories[selectedCategory]![index];
                  return ListTile(
                    title: Text(
                      function['name']!,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontFamily: 'monospace',
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          function['syntax']!,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                            color: Colors.blue,
                          ),
                        ),
                        Text(
                          function['desc']!,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                    onTap: () {
                      widget.onFunctionSelected(function['syntax']!);
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),
            ),
            
            // Close button
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
