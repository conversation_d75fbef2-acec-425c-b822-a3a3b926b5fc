import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/mobile_theme.dart';
import '../models/excel_function.dart';
import '../services/excel_engine_service.dart';
import '../widgets/mobile_theme_selector.dart';
import '../widgets/excel_formula_editor.dart';
import '../widgets/app_preview_widget.dart';

/// Enhanced Tool Builder screen with mobile themes and Excel functionality
class EnhancedToolBuilderScreen extends ConsumerStatefulWidget {
  const EnhancedToolBuilderScreen({super.key});

  @override
  ConsumerState<EnhancedToolBuilderScreen> createState() => _EnhancedToolBuilderScreenState();
}

class _EnhancedToolBuilderScreenState extends ConsumerState<EnhancedToolBuilderScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ExcelEngineService _excelEngine = ExcelEngineService();
  
  // App configuration
  String _appName = 'My App';
  String _appDescription = 'A custom mobile application';
  MobileTheme? _selectedTheme;
  BuildTarget _buildTarget = BuildTarget.both;
  
  // Excel data
  final Map<String, dynamic> _spreadsheetData = {};
  String _selectedCell = 'A1';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _initializeDefaultData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeDefaultData() {
    // Initialize with sample data
    for (int row = 1; row <= 10; row++) {
      for (int col = 1; col <= 5; col++) {
        final cellRef = '${String.fromCharCode(64 + col)}$row';
        _excelEngine.setCellValue(cellRef, '');
      }
    }
    
    // Add some sample data
    _excelEngine.setCellValue('A1', 'Product');
    _excelEngine.setCellValue('B1', 'Price');
    _excelEngine.setCellValue('C1', 'Quantity');
    _excelEngine.setCellValue('D1', 'Total');
    
    _excelEngine.setCellValue('A2', 'Apple');
    _excelEngine.setCellValue('B2', 1.50);
    _excelEngine.setCellValue('C2', 10);
    _excelEngine.setCellValue('D2', '=B2*C2');
    
    _excelEngine.setCellValue('A3', 'Banana');
    _excelEngine.setCellValue('B3', 0.75);
    _excelEngine.setCellValue('C3', 20);
    _excelEngine.setCellValue('D3', '=B3*C3');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ShadowSuite Tool Builder'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelp,
            tooltip: 'Help',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Project'),
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('Import Project'),
                ),
              ),
              const PopupMenuItem(
                value: 'build',
                child: ListTile(
                  leading: Icon(Icons.build),
                  title: Text('Build App'),
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(
              icon: Icon(Icons.settings),
              text: 'Project',
            ),
            Tab(
              icon: Icon(Icons.palette),
              text: 'Themes',
            ),
            Tab(
              icon: Icon(Icons.table_chart),
              text: 'Excel Data',
            ),
            Tab(
              icon: Icon(Icons.functions),
              text: 'Formulas',
            ),
            Tab(
              icon: Icon(Icons.preview),
              text: 'Preview',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProjectTab(),
          _buildThemesTab(),
          _buildExcelDataTab(),
          _buildFormulasTab(),
          _buildPreviewTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _buildApp,
        icon: const Icon(Icons.build),
        label: const Text('Build App'),
      ),
    );
  }

  Widget _buildProjectTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Project Configuration',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          
          // App Name
          TextFormField(
            initialValue: _appName,
            decoration: const InputDecoration(
              labelText: 'App Name',
              hintText: 'Enter your app name',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _appName = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // App Description
          TextFormField(
            initialValue: _appDescription,
            decoration: const InputDecoration(
              labelText: 'App Description',
              hintText: 'Describe your app',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
            onChanged: (value) {
              setState(() {
                _appDescription = value;
              });
            },
          ),
          
          const SizedBox(height: 24),
          
          // Build Target
          Text(
            'Build Target',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          SegmentedButton<BuildTarget>(
            segments: const [
              ButtonSegment(
                value: BuildTarget.android,
                label: Text('Android APK'),
                icon: Icon(Icons.android),
              ),
              ButtonSegment(
                value: BuildTarget.windows,
                label: Text('Windows EXE'),
                icon: Icon(Icons.desktop_windows),
              ),
              ButtonSegment(
                value: BuildTarget.both,
                label: Text('Both'),
                icon: Icon(Icons.devices),
              ),
            ],
            selected: {_buildTarget},
            onSelectionChanged: (Set<BuildTarget> selection) {
              setState(() {
                _buildTarget = selection.first;
              });
            },
          ),
          
          const SizedBox(height: 24),
          
          // Project Features
          Text(
            'Features',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          
          _buildFeatureCard(
            'Excel Integration',
            'Full spreadsheet functionality with formulas',
            Icons.table_chart,
            Colors.green,
            true,
          ),
          
          const SizedBox(height: 8),
          
          _buildFeatureCard(
            'Mobile Themes',
            'Android and iOS native styling',
            Icons.phone_android,
            Colors.blue,
            true,
          ),
          
          const SizedBox(height: 8),
          
          _buildFeatureCard(
            'Real-time Updates',
            'Live data synchronization',
            Icons.sync,
            Colors.orange,
            true,
          ),
          
          const SizedBox(height: 8),
          
          _buildFeatureCard(
            'Offline Support',
            'Works without internet connection',
            Icons.offline_bolt,
            Colors.purple,
            true,
          ),
        ],
      ),
    );
  }

  Widget _buildThemesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Mobile Themes',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose a theme that matches your target platform',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          
          MobileThemeSelector(
            selectedTheme: _selectedTheme,
            onThemeSelected: (theme) {
              setState(() {
                _selectedTheme = theme;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildExcelDataTab() {
    return Column(
      children: [
        // Toolbar
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ),
          child: Row(
            children: [
              Text(
                'Selected: $_selectedCell',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _addRow,
                tooltip: 'Add Row',
              ),
              IconButton(
                icon: const Icon(Icons.remove),
                onPressed: _removeRow,
                tooltip: 'Remove Row',
              ),
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: _clearData,
                tooltip: 'Clear All',
              ),
            ],
          ),
        ),
        
        // Spreadsheet
        Expanded(
          child: _buildSpreadsheet(),
        ),
      ],
    );
  }

  Widget _buildFormulasTab() {
    return Column(
      children: [
        // Formula bar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Formula Editor',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ExcelFormulaEditor(
                excelEngine: _excelEngine,
                selectedCell: _selectedCell,
                onCellSelected: (cell) {
                  setState(() {
                    _selectedCell = cell;
                  });
                },
              ),
            ],
          ),
        ),
        
        // Function library
        Expanded(
          child: _buildFunctionLibrary(),
        ),
      ],
    );
  }

  Widget _buildPreviewTab() {
    return AppPreviewWidget(
      appName: _appName,
      appDescription: _appDescription,
      theme: _selectedTheme,
      buildTarget: _buildTarget,
      excelData: _spreadsheetData,
    );
  }

  Widget _buildFeatureCard(String title, String description, IconData icon, Color color, bool enabled) {
    return Card(
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: Switch(
          value: enabled,
          onChanged: (value) {
            // Handle feature toggle
          },
        ),
      ),
    );
  }

  Widget _buildSpreadsheet() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: List.generate(6, (index) => DataColumn(
            label: Text(String.fromCharCode(65 + index)),
          )),
          rows: List.generate(20, (rowIndex) => DataRow(
            cells: List.generate(6, (colIndex) {
              final cellRef = '${String.fromCharCode(65 + colIndex)}${rowIndex + 1}';
              final value = _excelEngine.getCellValue(cellRef);
              
              return DataCell(
                Container(
                  width: 100,
                  child: Text(
                    value?.toString() ?? '',
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                onTap: () {
                  setState(() {
                    _selectedCell = cellRef;
                  });
                },
              );
            }),
          )),
        ),
      ),
    );
  }

  Widget _buildFunctionLibrary() {
    return DefaultTabController(
      length: ExcelFunctionLibrary.allCategories.length,
      child: Column(
        children: [
          TabBar(
            isScrollable: true,
            tabs: ExcelFunctionLibrary.allCategories.map((category) => 
              Tab(text: category)
            ).toList(),
          ),
          Expanded(
            child: TabBarView(
              children: ExcelFunctionLibrary.allCategories.map((category) => 
                _buildFunctionCategoryList(category)
              ).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionCategoryList(String category) {
    final functions = ExcelFunctionLibrary.getFunctionsByCategory(category);
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: functions.length,
      itemBuilder: (context, index) {
        final function = functions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getComplexityColor(function.complexity).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.functions,
                color: _getComplexityColor(function.complexity),
              ),
            ),
            title: Text(
              function.displayName,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(function.description),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Syntax:',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        function.syntax,
                        style: const TextStyle(fontFamily: 'monospace'),
                      ),
                    ),
                    
                    if (function.examples.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Text(
                        'Examples:',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      ...function.examples.map((example) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            example,
                            style: const TextStyle(fontFamily: 'monospace'),
                          ),
                        ),
                      )),
                    ],
                    
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _insertFunction(function),
                        child: const Text('Insert Function'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getComplexityColor(FunctionComplexity complexity) {
    switch (complexity) {
      case FunctionComplexity.basic:
        return Colors.green;
      case FunctionComplexity.intermediate:
        return Colors.orange;
      case FunctionComplexity.advanced:
        return Colors.red;
      case FunctionComplexity.expert:
        return Colors.purple;
    }
  }

  void _insertFunction(ExcelFunction function) {
    // Insert function into selected cell
    final formula = '=${function.name}()';
    _excelEngine.setCellValue(_selectedCell, formula);
    setState(() {});
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Inserted ${function.name} into $_selectedCell')),
    );
  }

  void _addRow() {
    // Add a new row to the spreadsheet
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add row functionality coming soon!')),
    );
  }

  void _removeRow() {
    // Remove the selected row
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Remove row functionality coming soon!')),
    );
  }

  void _clearData() {
    // Clear all data
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text('Are you sure you want to clear all spreadsheet data?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Clear all data
              for (int row = 1; row <= 20; row++) {
                for (int col = 1; col <= 6; col++) {
                  final cellRef = '${String.fromCharCode(64 + col)}$row';
                  _excelEngine.setCellValue(cellRef, '');
                }
              }
              setState(() {});
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _buildApp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Build App'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('App Name: $_appName'),
            Text('Target: ${_buildTarget.name}'),
            Text('Theme: ${_selectedTheme?.name ?? 'Default'}'),
            const SizedBox(height: 16),
            const Text('This will create a production-ready app with:'),
            const Text('• Excel functionality'),
            const Text('• Mobile-optimized UI'),
            const Text('• Offline support'),
            const Text('• Professional styling'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startBuild();
            },
            child: const Text('Build'),
          ),
        ],
      ),
    );
  }

  void _startBuild() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Building app... This may take a few minutes.'),
        duration: Duration(seconds: 3),
      ),
    );
    
    // Simulate build process
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App built successfully! Check the output folder.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ShadowSuite Tool Builder Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Project Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('Configure your app name, description, and build targets.'),
              SizedBox(height: 12),
              Text(
                'Themes Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('Choose from Android, iOS, or hybrid themes for native look and feel.'),
              SizedBox(height: 12),
              Text(
                'Excel Data Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('Create and edit spreadsheet data with full Excel compatibility.'),
              SizedBox(height: 12),
              Text(
                'Formulas Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('Use Excel functions with autocomplete and syntax highlighting.'),
              SizedBox(height: 12),
              Text(
                'Preview Tab:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('See how your app will look on different devices.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Export functionality coming soon!')),
        );
        break;
      case 'import':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Import functionality coming soon!')),
        );
        break;
      case 'build':
        _buildApp();
        break;
    }
  }
}

enum BuildTarget {
  android,
  windows,
  both,
}
