import 'package:flutter/material.dart';

enum NotificationType {
  todoReminder,
  dhikrReminder,
  voiceMemoTranscription,
  dailyReview,
  weeklyReport,
  backupComplete,
  syncComplete,
  achievement,
  general,
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

enum NotificationStatus {
  pending,
  sent,
  delivered,
  read,
  dismissed,
  failed,
}

class AppNotification {
  int id;
  String title;
  String body;
  NotificationType type;
  NotificationPriority priority;
  NotificationStatus status;
  DateTime scheduledAt;
  DateTime? sentAt;
  DateTime? readAt;
  DateTime createdAt;
  DateTime updatedAt;
  
  // Associated data
  String? associatedId; // ID of related todo, dhikr, etc.
  Map<String, dynamic>? data;
  
  // Notification settings
  bool isRepeating;
  Duration? repeatInterval;
  int maxRepeats;
  int currentRepeats;
  
  // Display settings
  String? iconPath;
  Color? color;
  String? soundPath;
  bool vibrate;
  bool showBadge;
  
  // Actions
  List<NotificationAction> actions;

  AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.scheduledAt,
    this.priority = NotificationPriority.normal,
    this.status = NotificationStatus.pending,
    this.associatedId,
    this.data,
    this.isRepeating = false,
    this.repeatInterval,
    this.maxRepeats = 0,
    this.currentRepeats = 0,
    this.iconPath,
    this.color,
    this.soundPath,
    this.vibrate = true,
    this.showBadge = true,
    List<NotificationAction>? actions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : actions = actions ?? [],
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  bool get isPending => status == NotificationStatus.pending;
  bool get isSent => status == NotificationStatus.sent;
  bool get isRead => status == NotificationStatus.read;
  bool get isOverdue => scheduledAt.isBefore(DateTime.now()) && isPending;
  bool get canRepeat => isRepeating && currentRepeats < maxRepeats;

  void markAsSent() {
    status = NotificationStatus.sent;
    sentAt = DateTime.now();
    updatedAt = DateTime.now();
  }

  void markAsRead() {
    status = NotificationStatus.read;
    readAt = DateTime.now();
    updatedAt = DateTime.now();
  }

  void markAsDismissed() {
    status = NotificationStatus.dismissed;
    updatedAt = DateTime.now();
  }

  void markAsFailed() {
    status = NotificationStatus.failed;
    updatedAt = DateTime.now();
  }

  void scheduleNext() {
    if (canRepeat && repeatInterval != null) {
      scheduledAt = scheduledAt.add(repeatInterval!);
      currentRepeats++;
      status = NotificationStatus.pending;
      sentAt = null;
      readAt = null;
      updatedAt = DateTime.now();
    }
  }

  AppNotification copyWith({
    String? title,
    String? body,
    NotificationType? type,
    NotificationPriority? priority,
    NotificationStatus? status,
    DateTime? scheduledAt,
    String? associatedId,
    Map<String, dynamic>? data,
    bool? isRepeating,
    Duration? repeatInterval,
    int? maxRepeats,
    String? iconPath,
    Color? color,
    String? soundPath,
    bool? vibrate,
    bool? showBadge,
    List<NotificationAction>? actions,
  }) {
    return AppNotification(
      id: id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      associatedId: associatedId ?? this.associatedId,
      data: data ?? this.data,
      isRepeating: isRepeating ?? this.isRepeating,
      repeatInterval: repeatInterval ?? this.repeatInterval,
      maxRepeats: maxRepeats ?? this.maxRepeats,
      currentRepeats: currentRepeats,
      iconPath: iconPath ?? this.iconPath,
      color: color ?? this.color,
      soundPath: soundPath ?? this.soundPath,
      vibrate: vibrate ?? this.vibrate,
      showBadge: showBadge ?? this.showBadge,
      actions: actions ?? this.actions,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    )..status = status ?? this.status;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.name,
      'priority': priority.name,
      'status': status.name,
      'scheduledAt': scheduledAt.toIso8601String(),
      'sentAt': sentAt?.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'associatedId': associatedId,
      'data': data,
      'isRepeating': isRepeating,
      'repeatInterval': repeatInterval?.inMilliseconds,
      'maxRepeats': maxRepeats,
      'currentRepeats': currentRepeats,
      'iconPath': iconPath,
      'color': color != null ? (color!.r * 255).round() << 16 | (color!.g * 255).round() << 8 | (color!.b * 255).round() | (color!.a * 255).round() << 24 : null,
      'soundPath': soundPath,
      'vibrate': vibrate,
      'showBadge': showBadge,
      'actions': actions.map((a) => a.toJson()).toList(),
    };
  }

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    final notification = AppNotification(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      type: NotificationType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => NotificationType.general,
      ),
      priority: NotificationPriority.values.firstWhere(
        (p) => p.name == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
      scheduledAt: DateTime.parse(json['scheduledAt']),
      associatedId: json['associatedId'],
      data: json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
      isRepeating: json['isRepeating'] ?? false,
      repeatInterval: json['repeatInterval'] != null 
          ? Duration(milliseconds: json['repeatInterval']) 
          : null,
      maxRepeats: json['maxRepeats'] ?? 0,
      iconPath: json['iconPath'],
      color: json['color'] != null ? Color(json['color']) : null,
      soundPath: json['soundPath'],
      vibrate: json['vibrate'] ?? true,
      showBadge: json['showBadge'] ?? true,
      actions: (json['actions'] as List?)
          ?.map((a) => NotificationAction.fromJson(a))
          .toList() ?? [],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );

    notification.status = NotificationStatus.values.firstWhere(
      (s) => s.name == json['status'],
      orElse: () => NotificationStatus.pending,
    );
    notification.currentRepeats = json['currentRepeats'] ?? 0;
    notification.sentAt = json['sentAt'] != null ? DateTime.parse(json['sentAt']) : null;
    notification.readAt = json['readAt'] != null ? DateTime.parse(json['readAt']) : null;

    return notification;
  }

  // Factory methods for common notification types
  factory AppNotification.todoReminder({
    required int todoId,
    required String todoTitle,
    required DateTime reminderTime,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Todo Reminder',
      body: 'Don\'t forget: $todoTitle',
      type: NotificationType.todoReminder,
      scheduledAt: reminderTime,
      associatedId: todoId.toString(),
      iconPath: 'assets/icons/todo.png',
      color: Colors.blue,
      actions: [
        NotificationAction(
          id: 'complete',
          title: 'Mark Complete',
          icon: Icons.check,
        ),
        NotificationAction(
          id: 'snooze',
          title: 'Snooze 15min',
          icon: Icons.snooze,
        ),
      ],
    );
  }

  factory AppNotification.dhikrReminder({
    required String dhikrName,
    required DateTime reminderTime,
    bool isRepeating = true,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Dhikr Reminder',
      body: 'Time for $dhikrName',
      type: NotificationType.dhikrReminder,
      scheduledAt: reminderTime,
      iconPath: 'assets/icons/dhikr.png',
      color: Colors.green,
      isRepeating: isRepeating,
      repeatInterval: const Duration(days: 1),
      maxRepeats: 365,
      actions: [
        NotificationAction(
          id: 'start',
          title: 'Start Now',
          icon: Icons.play_arrow,
        ),
        NotificationAction(
          id: 'skip',
          title: 'Skip Today',
          icon: Icons.skip_next,
        ),
      ],
    );
  }

  factory AppNotification.voiceMemoTranscription({
    required int memoId,
    required String memoTitle,
    required bool success,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: success ? 'Transcription Complete' : 'Transcription Failed',
      body: success 
          ? 'Voice memo "$memoTitle" has been transcribed'
          : 'Failed to transcribe "$memoTitle"',
      type: NotificationType.voiceMemoTranscription,
      scheduledAt: DateTime.now(),
      associatedId: memoId.toString(),
      iconPath: 'assets/icons/voice_memo.png',
      color: success ? Colors.green : Colors.red,
      priority: success ? NotificationPriority.normal : NotificationPriority.high,
    );
  }

  factory AppNotification.achievement({
    required String achievementTitle,
    required String description,
  }) {
    return AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Achievement Unlocked!',
      body: '$achievementTitle: $description',
      type: NotificationType.achievement,
      scheduledAt: DateTime.now(),
      iconPath: 'assets/icons/achievement.png',
      color: Colors.amber,
      priority: NotificationPriority.high,
    );
  }
}

class NotificationAction {
  final String id;
  final String title;
  final IconData? icon;
  final Color? color;
  final Map<String, dynamic>? data;

  const NotificationAction({
    required this.id,
    required this.title,
    this.icon,
    this.color,
    this.data,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'icon': icon?.codePoint,
      'color': color != null ? (color!.r * 255).round() << 16 | (color!.g * 255).round() << 8 | (color!.b * 255).round() | (color!.a * 255).round() << 24 : null,
      'data': data,
    };
  }

  factory NotificationAction.fromJson(Map<String, dynamic> json) {
    return NotificationAction(
      id: json['id'],
      title: json['title'],
      icon: json['icon'] != null ? IconData(json['icon']) : null,
      color: json['color'] != null ? Color(json['color']) : null,
      data: json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
    );
  }
}
