import 'package:flutter/material.dart';
import '../models/ui_component.dart';

/// Renders UI components on the canvas
class ComponentRender<PERSON> extends StatefulWidget {
  final UIComponent component;
  final bool isPreviewMode;
  final Function(dynamic value)? onValueChanged;

  const ComponentRenderer({
    super.key,
    required this.component,
    this.isPreviewMode = false,
    this.onValueChanged,
  });

  @override
  State<ComponentRenderer> createState() => _ComponentRendererState();
}

class _ComponentRendererState extends State<ComponentRenderer> {
  late dynamic _currentValue;
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _initializeValue();
  }

  @override
  void didUpdateWidget(ComponentRenderer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.component.id != widget.component.id) {
      _initializeValue();
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeValue() {
    switch (widget.component.type) {
      case UIComponentType.textField:
        _currentValue = widget.component.properties['defaultValue'] ?? '';
        break;
      case UIComponentType.toggle:
        _currentValue = widget.component.properties['defaultValue'] ?? false;
        break;
      case UIComponentType.slider:
        _currentValue = widget.component.properties['defaultValue'] ?? 50.0;
        break;
      case UIComponentType.dropdown:
        _currentValue = widget.component.properties['defaultValue'];
        break;
      default:
        _currentValue = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.component.size.width,
      height: widget.component.size.height,
      padding: EdgeInsets.all(widget.component.properties['padding'] ?? 8.0),
      decoration: BoxDecoration(
        color: _parseColor(widget.component.properties['backgroundColor'] ?? '#FFFFFF'),
        borderRadius: BorderRadius.circular(4),
        border: widget.isPreviewMode ? null : Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: _buildComponentWidget(),
    );
  }

  Widget _buildComponentWidget() {
    switch (widget.component.type) {
      // Input Components
      case UIComponentType.textField:
        return _buildTextField();
      case UIComponentType.button:
        return _buildButton();
      case UIComponentType.toggle:
        return _buildToggle();
      case UIComponentType.dropdown:
        return _buildDropdown();
      case UIComponentType.slider:
        return _buildSlider();

      // Output Components
      case UIComponentType.label:
        return _buildLabel();
      case UIComponentType.displayText:
        return _buildDisplayText();
      case UIComponentType.calculatedValue:
        return _buildCalculatedValue();
      case UIComponentType.progressBar:
        return _buildProgressBar();
      case UIComponentType.gauge:
        return _buildGauge();

      // Excel-Specific Components
      case UIComponentType.dataTable:
        return _buildDataTable();
      case UIComponentType.pivotTable:
        return _buildPivotTable();
      case UIComponentType.chartWidget:
        return _buildChartWidget();
      case UIComponentType.formulaDisplay:
        return _buildFormulaDisplay();
      case UIComponentType.cellReference:
        return _buildCellReference();

      // Layout Components
      case UIComponentType.image:
        return _buildImage();
      case UIComponentType.divider:
        return _buildDivider();
      case UIComponentType.container:
        return _buildContainer();
      case UIComponentType.spacer:
        return _buildSpacer();
    }
  }

  Widget _buildTextField() {
    final key = 'textfield_${widget.component.id}';
    _controllers[key] ??= TextEditingController(text: _currentValue?.toString() ?? '');
    
    return TextField(
      controller: _controllers[key],
      enabled: widget.component.properties['enabled'] ?? true,
      maxLines: widget.component.properties['multiline'] == true ? null : 1,
      style: TextStyle(
        color: _parseColor(widget.component.properties['textColor'] ?? '#000000'),
        fontSize: (widget.component.properties['fontSize'] as num?)?.toDouble() ?? 14.0,
      ),
      decoration: InputDecoration(
        hintText: widget.component.properties['placeholder'] ?? 'Enter text...',
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
      onChanged: widget.isPreviewMode ? (value) {
        setState(() {
          _currentValue = value;
        });
        widget.onValueChanged?.call(value);
      } : null,
    );
  }

  Widget _buildButton() {
    final buttonText = widget.component.properties['text'] ?? 'Button';
    final style = widget.component.properties['style'] ?? 'elevated';
    final enabled = widget.component.properties['enabled'] ?? true;
    
    final buttonStyle = ButtonStyle(
      backgroundColor: WidgetStateProperty.all(
        _parseColor(widget.component.properties['backgroundColor'] ?? '#2196F3'),
      ),
      foregroundColor: WidgetStateProperty.all(
        _parseColor(widget.component.properties['textColor'] ?? '#FFFFFF'),
      ),
    );

    Widget button;
    switch (style) {
      case 'outlined':
        button = OutlinedButton(
          onPressed: enabled && widget.isPreviewMode ? () => _handleButtonPress() : null,
          style: buttonStyle,
          child: Text(buttonText),
        );
        break;
      case 'text':
        button = TextButton(
          onPressed: enabled && widget.isPreviewMode ? () => _handleButtonPress() : null,
          style: buttonStyle,
          child: Text(buttonText),
        );
        break;
      default:
        button = ElevatedButton(
          onPressed: enabled && widget.isPreviewMode ? () => _handleButtonPress() : null,
          style: buttonStyle,
          child: Text(buttonText),
        );
    }

    return SizedBox.expand(child: button);
  }

  Widget _buildToggle() {
    final enabled = widget.component.properties['enabled'] ?? true;
    
    return Switch(
      value: _currentValue ?? false,
      onChanged: enabled && widget.isPreviewMode ? (value) {
        setState(() {
          _currentValue = value;
        });
        widget.onValueChanged?.call(value);
      } : null,
    );
  }

  Widget _buildLabel() {
    final text = widget.component.properties['text'] ?? 'Label';
    final alignment = widget.component.properties['alignment'] ?? 'left';
    
    TextAlign textAlign;
    switch (alignment) {
      case 'center':
        textAlign = TextAlign.center;
        break;
      case 'right':
        textAlign = TextAlign.right;
        break;
      case 'justify':
        textAlign = TextAlign.justify;
        break;
      default:
        textAlign = TextAlign.left;
    }
    
    return Text(
      text,
      style: TextStyle(
        color: _parseColor(widget.component.properties['textColor'] ?? '#000000'),
        fontSize: (widget.component.properties['fontSize'] as num?)?.toDouble() ?? 14.0,
        fontWeight: _parseFontWeight(widget.component.properties['fontWeight']),
      ),
      textAlign: textAlign,
      overflow: TextOverflow.ellipsis,
      maxLines: null,
    );
  }

  Widget _buildDropdown() {
    final options = List<String>.from(widget.component.properties['options'] ?? []);
    final enabled = widget.component.properties['enabled'] ?? true;
    
    if (options.isEmpty) {
      return const Center(
        child: Text('No options', style: TextStyle(color: Colors.grey)),
      );
    }
    
    return DropdownButtonFormField<String>(
      value: _currentValue is String && options.contains(_currentValue) ? _currentValue : null,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
      items: options.map((option) {
        return DropdownMenuItem(
          value: option,
          child: Text(option),
        );
      }).toList(),
      onChanged: enabled && widget.isPreviewMode ? (value) {
        setState(() {
          _currentValue = value;
        });
        widget.onValueChanged?.call(value);
      } : null,
    );
  }

  Widget _buildSlider() {
    final min = (widget.component.properties['min'] as num?)?.toDouble() ?? 0.0;
    final max = (widget.component.properties['max'] as num?)?.toDouble() ?? 100.0;
    final divisions = widget.component.properties['divisions'] as int?;
    final enabled = widget.component.properties['enabled'] ?? true;
    
    final value = (_currentValue as num?)?.toDouble() ?? min;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          value.toStringAsFixed(1),
          style: TextStyle(
            color: _parseColor(widget.component.properties['textColor'] ?? '#000000'),
            fontSize: 12,
          ),
        ),
        Slider(
          value: value.clamp(min, max),
          min: min,
          max: max,
          divisions: divisions,
          onChanged: enabled && widget.isPreviewMode ? (newValue) {
            setState(() {
              _currentValue = newValue;
            });
            widget.onValueChanged?.call(newValue);
          } : null,
        ),
      ],
    );
  }

  Widget _buildImage() {
    final url = widget.component.properties['url'] as String? ?? '';
    final fit = widget.component.properties['fit'] ?? 'contain';
    final placeholder = widget.component.properties['placeholder'] ?? 'No Image';
    
    BoxFit boxFit;
    switch (fit) {
      case 'cover':
        boxFit = BoxFit.cover;
        break;
      case 'fill':
        boxFit = BoxFit.fill;
        break;
      case 'fitWidth':
        boxFit = BoxFit.fitWidth;
        break;
      case 'fitHeight':
        boxFit = BoxFit.fitHeight;
        break;
      default:
        boxFit = BoxFit.contain;
    }
    
    if (url.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          border: Border.all(color: Colors.grey),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image, color: Colors.grey[400], size: 32),
              const SizedBox(height: 4),
              Text(
                placeholder,
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    
    return Image.network(
      url,
      fit: boxFit,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.red[50],
            border: Border.all(color: Colors.red),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red[400], size: 32),
                const SizedBox(height: 4),
                Text(
                  'Failed to load image',
                  style: TextStyle(color: Colors.red[600], fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDivider() {
    final thickness = (widget.component.properties['thickness'] as num?)?.toDouble() ?? 1.0;
    final color = _parseColor(widget.component.properties['color'] ?? '#E0E0E0');
    final orientation = widget.component.properties['orientation'] ?? 'horizontal';

    if (orientation == 'vertical') {
      return VerticalDivider(
        thickness: thickness,
        color: color,
      );
    } else {
      return Divider(
        thickness: thickness,
        color: color,
      );
    }
  }

  // Output Components
  Widget _buildDisplayText() {
    final text = widget.component.properties['text'] ?? 'Display Value';
    final alignment = widget.component.properties['alignment'] ?? 'center';
    final fontSize = (widget.component.properties['fontSize'] as num?)?.toDouble() ?? 16.0;
    final fontWeight = widget.component.properties['fontWeight'] ?? 'bold';
    final color = widget.component.properties['color'] ?? '#000000';

    TextAlign textAlign;
    switch (alignment) {
      case 'left':
        textAlign = TextAlign.left;
        break;
      case 'right':
        textAlign = TextAlign.right;
        break;
      case 'justify':
        textAlign = TextAlign.justify;
        break;
      default:
        textAlign = TextAlign.center;
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(8),
      child: Text(
        _currentValue?.toString() ?? text,
        style: TextStyle(
          color: _parseColor(color),
          fontSize: fontSize,
          fontWeight: _parseFontWeight(fontWeight),
        ),
        textAlign: textAlign,
        overflow: TextOverflow.ellipsis,
        maxLines: 3,
      ),
    );
  }

  Widget _buildCalculatedValue() {
    final formula = widget.component.properties['formula'] ?? '=SUM(A1:A10)';
    final format = widget.component.properties['format'] ?? 'number';
    final decimals = widget.component.properties['decimals'] ?? 2;
    final prefix = widget.component.properties['prefix'] ?? '';
    final suffix = widget.component.properties['suffix'] ?? '';

    // Mock calculated value for demonstration
    final calculatedValue = _mockCalculateFormula(formula);
    final formattedValue = _formatValue(calculatedValue, format, decimals, prefix, suffix);

    return Container(
      decoration: BoxDecoration(
        color: Colors.blue[50],
        border: Border.all(color: Colors.blue[200]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            formattedValue,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.isPreviewMode) ...[
            const SizedBox(height: 4),
            Text(
              formula,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    final value = (widget.component.properties['value'] as num?)?.toDouble() ?? 50.0;
    final min = (widget.component.properties['min'] as num?)?.toDouble() ?? 0.0;
    final max = (widget.component.properties['max'] as num?)?.toDouble() ?? 100.0;
    final showValue = widget.component.properties['showValue'] ?? true;
    final color = widget.component.properties['color'] ?? '#2196F3';

    final progress = ((value - min) / (max - min)).clamp(0.0, 1.0);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(_parseColor(color)),
          minHeight: 8,
        ),
        if (showValue) ...[
          const SizedBox(height: 4),
          Text(
            '${value.toStringAsFixed(1)}%',
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildGauge() {
    final value = (widget.component.properties['value'] as num?)?.toDouble() ?? 75.0;
    final min = (widget.component.properties['min'] as num?)?.toDouble() ?? 0.0;
    final max = (widget.component.properties['max'] as num?)?.toDouble() ?? 100.0;
    final segments = widget.component.properties['segments'] ?? 3;
    final showValue = widget.component.properties['showValue'] ?? true;

    final progress = ((value - min) / (max - min)).clamp(0.0, 1.0);

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Circular progress indicator as gauge
          SizedBox(
            width: 80,
            height: 80,
            child: CircularProgressIndicator(
              value: progress,
              strokeWidth: 8,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _getGaugeColor(progress, segments),
              ),
            ),
          ),
          if (showValue) ...[
            const SizedBox(height: 8),
            Text(
              value.toStringAsFixed(1),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  void _handleButtonPress() {
    final action = widget.component.properties['action'] ?? 'none';
    
    switch (action) {
      case 'updateCell':
        // Handle cell update action
        widget.onValueChanged?.call('button_pressed');
        break;
      case 'executeFormula':
        // Handle formula execution
        widget.onValueChanged?.call('formula_executed');
        break;
      case 'triggerCalculation':
        // Handle calculation trigger
        widget.onValueChanged?.call('calculation_triggered');
        break;
      default:
        // Default action
        widget.onValueChanged?.call('button_clicked');
    }
  }

  Color _parseColor(String hex) {
    try {
      return Color(int.parse(hex.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Colors.white;
    }
  }

  FontWeight _parseFontWeight(dynamic weight) {
    if (weight is String) {
      switch (weight.toLowerCase()) {
        case 'bold':
          return FontWeight.bold;
        case 'normal':
          return FontWeight.normal;
        case 'light':
          return FontWeight.w300;
        default:
          return FontWeight.normal;
      }
    }
    return FontWeight.normal;
  }

  // Excel-Specific Components (placeholder implementations)
  Widget _buildDataTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.table_chart, size: 32, color: Colors.blue),
            SizedBox(height: 8),
            Text('Data Table', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('Interactive data table', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildPivotTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.orange[50],
        border: Border.all(color: Colors.orange[200]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.pivot_table_chart, size: 32, color: Colors.orange),
            SizedBox(height: 8),
            Text('Pivot Table', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('Dynamic pivot analysis', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildChartWidget() {
    final chartType = widget.component.properties['chartType'] ?? 'column';

    return Container(
      decoration: BoxDecoration(
        color: Colors.green[50],
        border: Border.all(color: Colors.green[200]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(_getChartIcon(chartType), size: 32, color: Colors.green),
            const SizedBox(height: 8),
            Text('${chartType.toUpperCase()} Chart', style: const TextStyle(fontWeight: FontWeight.bold)),
            const Text('Interactive chart', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildFormulaDisplay() {
    final formula = widget.component.properties['formula'] ?? '=A1+B1';
    final result = _mockCalculateFormula(formula);

    return Container(
      decoration: BoxDecoration(
        color: Colors.purple[50],
        border: Border.all(color: Colors.purple[200]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(formula, style: const TextStyle(fontFamily: 'monospace', fontSize: 12, color: Colors.purple)),
          const SizedBox(height: 4),
          Text('= $result', style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildCellReference() {
    final cellAddress = widget.component.properties['cellAddress'] ?? 'A1';
    final cellValue = _getMockCellValue(cellAddress);

    return Container(
      decoration: BoxDecoration(
        color: Colors.cyan[50],
        border: Border.all(color: Colors.cyan[200]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(cellAddress, style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.cyan)),
          const SizedBox(height: 4),
          Text(cellValue.toString()),
        ],
      ),
    );
  }

  Widget _buildContainer() {
    return Container(
      decoration: BoxDecoration(
        color: _parseColor(widget.component.properties['backgroundColor'] ?? '#FFFFFF'),
        border: Border.all(color: _parseColor(widget.component.properties['borderColor'] ?? '#E0E0E0')),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Text('Container', style: TextStyle(color: Colors.grey)),
      ),
    );
  }

  Widget _buildSpacer() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
      ),
      child: const Center(
        child: Text('Space', style: TextStyle(fontSize: 10, color: Colors.grey)),
      ),
    );
  }

  // Helper methods
  double _mockCalculateFormula(String formula) {
    // Mock calculation for demonstration
    if (formula.contains('SUM')) return 150.0;
    if (formula.contains('+')) return 25.0;
    if (formula.contains('*')) return 100.0;
    return 42.0;
  }

  String _formatValue(double value, String format, int decimals, String prefix, String suffix) {
    String formatted;
    switch (format) {
      case 'currency':
        formatted = '\$${value.toStringAsFixed(decimals)}';
        break;
      case 'percentage':
        formatted = '${value.toStringAsFixed(decimals)}%';
        break;
      default:
        formatted = value.toStringAsFixed(decimals);
    }
    return '$prefix$formatted$suffix';
  }

  Color _getGaugeColor(double progress, int segments) {
    if (progress < 0.33) return Colors.red;
    if (progress < 0.66) return Colors.orange;
    return Colors.green;
  }

  IconData _getChartIcon(String chartType) {
    switch (chartType) {
      case 'line':
        return Icons.show_chart;
      case 'pie':
        return Icons.pie_chart;
      case 'bar':
        return Icons.bar_chart;
      default:
        return Icons.bar_chart;
    }
  }

  Map<String, dynamic> _generateMockTableData(String range) {
    return {
      'headers': ['Name', 'Value', 'Date', 'Status', 'Amount'],
      'rows': [
        ['Item 1', '100', '2024-01-01', 'Active', '\$50.00'],
        ['Item 2', '200', '2024-01-02', 'Pending', '\$75.00'],
        ['Item 3', '150', '2024-01-03', 'Complete', '\$25.00'],
      ],
    };
  }

  dynamic _getMockCellValue(String cellAddress) {
    final mockValues = {
      'A1': 'Revenue',
      'B1': '2024',
      'A2': 'Q1',
      'B2': '15000',
      'A3': 'Q2',
      'B3': '18000',
    };
    return mockValues[cellAddress] ?? '0';
  }
}
