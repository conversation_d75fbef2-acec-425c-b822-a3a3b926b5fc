import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  static const String _metricsKey = 'performance_metrics';
  static const String _settingsKey = 'performance_settings';

  final Map<String, Stopwatch> _activeTimers = {};
  final List<PerformanceMetric> _metrics = [];
  final StreamController<PerformanceMetric> _metricsController = 
      StreamController<PerformanceMetric>.broadcast();

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  PerformanceSettings _settings = PerformanceSettings();
  Timer? _memoryMonitorTimer;
  Timer? _cleanupTimer;

  // Getters
  Stream<PerformanceMetric> get metricsStream => _metricsController.stream;
  List<PerformanceMetric> get metrics => List.unmodifiable(_metrics);
  PerformanceSettings get settings => _settings;
  bool get isInitialized => _isInitialized;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      await _loadMetrics();
      _startMonitoring();
      _isInitialized = true;
      debugPrint('PerformanceService initialized');
    } catch (e) {
      debugPrint('Error initializing PerformanceService: $e');
    }
  }

  // Timer methods
  void startTimer(String name) {
    if (!_settings.enabled) return;
    
    _activeTimers[name] = Stopwatch()..start();
  }

  void stopTimer(String name, {Map<String, dynamic>? metadata}) {
    if (!_settings.enabled) return;
    
    final timer = _activeTimers.remove(name);
    if (timer != null) {
      timer.stop();
      _recordMetric(PerformanceMetric.timing(
        name: name,
        duration: timer.elapsed,
        metadata: metadata,
      ));
    }
  }

  Future<T> measureAsync<T>(
    String name,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) async {
    if (!_settings.enabled) {
      return await operation();
    }

    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      
      _recordMetric(PerformanceMetric.timing(
        name: name,
        duration: stopwatch.elapsed,
        metadata: metadata,
      ));
      
      return result;
    } catch (e) {
      stopwatch.stop();
      
      _recordMetric(PerformanceMetric.error(
        name: name,
        error: e.toString(),
        duration: stopwatch.elapsed,
        metadata: metadata,
      ));
      
      rethrow;
    }
  }

  T measureSync<T>(
    String name,
    T Function() operation, {
    Map<String, dynamic>? metadata,
  }) {
    if (!_settings.enabled) {
      return operation();
    }

    final stopwatch = Stopwatch()..start();
    try {
      final result = operation();
      stopwatch.stop();
      
      _recordMetric(PerformanceMetric.timing(
        name: name,
        duration: stopwatch.elapsed,
        metadata: metadata,
      ));
      
      return result;
    } catch (e) {
      stopwatch.stop();
      
      _recordMetric(PerformanceMetric.error(
        name: name,
        error: e.toString(),
        duration: stopwatch.elapsed,
        metadata: metadata,
      ));
      
      rethrow;
    }
  }

  // Memory monitoring
  void recordMemoryUsage(String context) {
    if (!_settings.memoryMonitoringEnabled) return;

    try {
      // Get memory info (platform-specific)
      final memoryInfo = _getMemoryInfo();
      
      _recordMetric(PerformanceMetric.memory(
        name: 'memory_usage_$context',
        memoryUsage: memoryInfo,
      ));
    } catch (e) {
      debugPrint('Error recording memory usage: $e');
    }
  }

  // Custom metrics
  void recordCustomMetric(String name, double value, {
    String? unit,
    Map<String, dynamic>? metadata,
  }) {
    if (!_settings.enabled) return;

    _recordMetric(PerformanceMetric.custom(
      name: name,
      value: value,
      unit: unit,
      metadata: metadata,
    ));
  }

  void recordEvent(String name, {Map<String, dynamic>? metadata}) {
    if (!_settings.enabled) return;

    _recordMetric(PerformanceMetric.event(
      name: name,
      metadata: metadata,
    ));
  }

  // Analytics
  PerformanceAnalytics getAnalytics({Duration? timeWindow}) {
    final cutoff = timeWindow != null 
        ? DateTime.now().subtract(timeWindow)
        : null;

    final relevantMetrics = cutoff != null
        ? _metrics.where((m) => m.timestamp.isAfter(cutoff)).toList()
        : _metrics;

    return PerformanceAnalytics.fromMetrics(relevantMetrics);
  }

  List<PerformanceMetric> getMetricsByName(String name) {
    return _metrics.where((m) => m.name == name).toList();
  }

  List<PerformanceMetric> getMetricsByType(MetricType type) {
    return _metrics.where((m) => m.type == type).toList();
  }

  // Optimization suggestions
  List<OptimizationSuggestion> getOptimizationSuggestions() {
    final suggestions = <OptimizationSuggestion>[];
    final analytics = getAnalytics(timeWindow: const Duration(hours: 24));

    // Check for slow operations
    for (final entry in analytics.averageTimings.entries) {
      if (entry.value.inMilliseconds > _settings.slowOperationThresholdMs) {
        suggestions.add(OptimizationSuggestion(
          type: SuggestionType.performance,
          title: 'Slow Operation Detected',
          description: '${entry.key} is taking ${entry.value.inMilliseconds}ms on average',
          priority: SuggestionPriority.medium,
          action: 'Consider optimizing ${entry.key}',
        ));
      }
    }

    // Check memory usage
    if (analytics.averageMemoryUsage > _settings.highMemoryThresholdMB * 1024 * 1024) {
      suggestions.add(OptimizationSuggestion(
        type: SuggestionType.memory,
        title: 'High Memory Usage',
        description: 'Average memory usage is ${(analytics.averageMemoryUsage / 1024 / 1024).toStringAsFixed(1)}MB',
        priority: SuggestionPriority.high,
        action: 'Consider implementing memory optimization strategies',
      ));
    }

    // Check error rate
    if (analytics.errorRate > _settings.highErrorRateThreshold) {
      suggestions.add(OptimizationSuggestion(
        type: SuggestionType.reliability,
        title: 'High Error Rate',
        description: 'Error rate is ${(analytics.errorRate * 100).toStringAsFixed(1)}%',
        priority: SuggestionPriority.high,
        action: 'Investigate and fix recurring errors',
      ));
    }

    return suggestions;
  }

  // Cache management
  void clearMetrics() {
    _metrics.clear();
    _saveMetrics();
  }

  void clearOldMetrics({Duration? olderThan}) {
    final cutoff = olderThan != null
        ? DateTime.now().subtract(olderThan)
        : DateTime.now().subtract(const Duration(days: 7));

    _metrics.removeWhere((m) => m.timestamp.isBefore(cutoff));
    _saveMetrics();
  }

  // Settings
  Future<void> updateSettings(PerformanceSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
    
    if (_settings.enabled) {
      _startMonitoring();
    } else {
      _stopMonitoring();
    }
  }

  // Private methods
  void _recordMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    _metricsController.add(metric);
    
    // Limit metrics count
    if (_metrics.length > _settings.maxMetricsCount) {
      _metrics.removeAt(0);
    }
    
    // Save periodically
    if (_metrics.length % 10 == 0) {
      _saveMetrics();
    }
  }

  void _startMonitoring() {
    if (!_settings.enabled) return;

    // Memory monitoring
    if (_settings.memoryMonitoringEnabled) {
      _memoryMonitorTimer?.cancel();
      _memoryMonitorTimer = Timer.periodic(
        Duration(seconds: _settings.memoryMonitorIntervalSeconds),
        (_) => recordMemoryUsage('periodic'),
      );
    }

    // Cleanup timer
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 1),
      (_) => clearOldMetrics(),
    );
  }

  void _stopMonitoring() {
    _memoryMonitorTimer?.cancel();
    _cleanupTimer?.cancel();
  }

  Map<String, dynamic> _getMemoryInfo() {
    // This is a simplified implementation
    // In a real app, you'd use platform-specific methods
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'used_memory': 0, // Would get actual memory usage
      'available_memory': 0, // Would get available memory
      'total_memory': 0, // Would get total memory
    };
  }

  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs?.getString(_settingsKey);
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _settings = PerformanceSettings.fromJson(settingsMap);
      }
    } catch (e) {
      debugPrint('Error loading performance settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final settingsJson = json.encode(_settings.toJson());
      await _prefs?.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving performance settings: $e');
    }
  }

  Future<void> _loadMetrics() async {
    try {
      final metricsJson = _prefs?.getString(_metricsKey);
      if (metricsJson != null) {
        final metricsList = json.decode(metricsJson) as List<dynamic>;
        _metrics.clear();
        _metrics.addAll(
          metricsList.map((json) => PerformanceMetric.fromJson(json))
        );
      }
    } catch (e) {
      debugPrint('Error loading performance metrics: $e');
    }
  }

  Future<void> _saveMetrics() async {
    try {
      final metricsJson = json.encode(
        _metrics.map((m) => m.toJson()).toList()
      );
      await _prefs?.setString(_metricsKey, metricsJson);
    } catch (e) {
      debugPrint('Error saving performance metrics: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _memoryMonitorTimer?.cancel();
    _cleanupTimer?.cancel();
    _metricsController.close();
  }
}

// Performance metric model
enum MetricType { timing, memory, custom, event, error }

class PerformanceMetric {
  final String name;
  final MetricType type;
  final DateTime timestamp;
  final Duration? duration;
  final double? value;
  final String? unit;
  final String? error;
  final Map<String, dynamic>? memoryUsage;
  final Map<String, dynamic>? metadata;

  PerformanceMetric({
    required this.name,
    required this.type,
    DateTime? timestamp,
    this.duration,
    this.value,
    this.unit,
    this.error,
    this.memoryUsage,
    this.metadata,
  }) : timestamp = timestamp ?? DateTime.now();

  factory PerformanceMetric.timing({
    required String name,
    required Duration duration,
    Map<String, dynamic>? metadata,
  }) {
    return PerformanceMetric(
      name: name,
      type: MetricType.timing,
      duration: duration,
      metadata: metadata,
    );
  }

  factory PerformanceMetric.memory({
    required String name,
    required Map<String, dynamic> memoryUsage,
    Map<String, dynamic>? metadata,
  }) {
    return PerformanceMetric(
      name: name,
      type: MetricType.memory,
      memoryUsage: memoryUsage,
      metadata: metadata,
    );
  }

  factory PerformanceMetric.custom({
    required String name,
    required double value,
    String? unit,
    Map<String, dynamic>? metadata,
  }) {
    return PerformanceMetric(
      name: name,
      type: MetricType.custom,
      value: value,
      unit: unit,
      metadata: metadata,
    );
  }

  factory PerformanceMetric.event({
    required String name,
    Map<String, dynamic>? metadata,
  }) {
    return PerformanceMetric(
      name: name,
      type: MetricType.event,
      metadata: metadata,
    );
  }

  factory PerformanceMetric.error({
    required String name,
    required String error,
    Duration? duration,
    Map<String, dynamic>? metadata,
  }) {
    return PerformanceMetric(
      name: name,
      type: MetricType.error,
      error: error,
      duration: duration,
      metadata: metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration?.inMicroseconds,
      'value': value,
      'unit': unit,
      'error': error,
      'memoryUsage': memoryUsage,
      'metadata': metadata,
    };
  }

  factory PerformanceMetric.fromJson(Map<String, dynamic> json) {
    return PerformanceMetric(
      name: json['name'],
      type: MetricType.values.firstWhere((t) => t.name == json['type']),
      timestamp: DateTime.parse(json['timestamp']),
      duration: json['duration'] != null 
          ? Duration(microseconds: json['duration']) 
          : null,
      value: json['value']?.toDouble(),
      unit: json['unit'],
      error: json['error'],
      memoryUsage: json['memoryUsage'] != null 
          ? Map<String, dynamic>.from(json['memoryUsage']) 
          : null,
      metadata: json['metadata'] != null 
          ? Map<String, dynamic>.from(json['metadata']) 
          : null,
    );
  }
}

// Performance settings model
class PerformanceSettings {
  final bool enabled;
  final bool memoryMonitoringEnabled;
  final int memoryMonitorIntervalSeconds;
  final int maxMetricsCount;
  final int slowOperationThresholdMs;
  final int highMemoryThresholdMB;
  final double highErrorRateThreshold;

  const PerformanceSettings({
    this.enabled = true,
    this.memoryMonitoringEnabled = true,
    this.memoryMonitorIntervalSeconds = 30,
    this.maxMetricsCount = 1000,
    this.slowOperationThresholdMs = 1000,
    this.highMemoryThresholdMB = 100,
    this.highErrorRateThreshold = 0.05,
  });

  PerformanceSettings copyWith({
    bool? enabled,
    bool? memoryMonitoringEnabled,
    int? memoryMonitorIntervalSeconds,
    int? maxMetricsCount,
    int? slowOperationThresholdMs,
    int? highMemoryThresholdMB,
    double? highErrorRateThreshold,
  }) {
    return PerformanceSettings(
      enabled: enabled ?? this.enabled,
      memoryMonitoringEnabled: memoryMonitoringEnabled ?? this.memoryMonitoringEnabled,
      memoryMonitorIntervalSeconds: memoryMonitorIntervalSeconds ?? this.memoryMonitorIntervalSeconds,
      maxMetricsCount: maxMetricsCount ?? this.maxMetricsCount,
      slowOperationThresholdMs: slowOperationThresholdMs ?? this.slowOperationThresholdMs,
      highMemoryThresholdMB: highMemoryThresholdMB ?? this.highMemoryThresholdMB,
      highErrorRateThreshold: highErrorRateThreshold ?? this.highErrorRateThreshold,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'memoryMonitoringEnabled': memoryMonitoringEnabled,
      'memoryMonitorIntervalSeconds': memoryMonitorIntervalSeconds,
      'maxMetricsCount': maxMetricsCount,
      'slowOperationThresholdMs': slowOperationThresholdMs,
      'highMemoryThresholdMB': highMemoryThresholdMB,
      'highErrorRateThreshold': highErrorRateThreshold,
    };
  }

  factory PerformanceSettings.fromJson(Map<String, dynamic> json) {
    return PerformanceSettings(
      enabled: json['enabled'] ?? true,
      memoryMonitoringEnabled: json['memoryMonitoringEnabled'] ?? true,
      memoryMonitorIntervalSeconds: json['memoryMonitorIntervalSeconds'] ?? 30,
      maxMetricsCount: json['maxMetricsCount'] ?? 1000,
      slowOperationThresholdMs: json['slowOperationThresholdMs'] ?? 1000,
      highMemoryThresholdMB: json['highMemoryThresholdMB'] ?? 100,
      highErrorRateThreshold: json['highErrorRateThreshold']?.toDouble() ?? 0.05,
    );
  }
}

// Performance analytics model
class PerformanceAnalytics {
  final Map<String, Duration> averageTimings;
  final double averageMemoryUsage;
  final double errorRate;
  final int totalMetrics;
  final DateTime? oldestMetric;
  final DateTime? newestMetric;

  PerformanceAnalytics({
    required this.averageTimings,
    required this.averageMemoryUsage,
    required this.errorRate,
    required this.totalMetrics,
    this.oldestMetric,
    this.newestMetric,
  });

  factory PerformanceAnalytics.fromMetrics(List<PerformanceMetric> metrics) {
    final timingMetrics = metrics.where((m) => m.type == MetricType.timing).toList();
    final memoryMetrics = metrics.where((m) => m.type == MetricType.memory).toList();
    final errorMetrics = metrics.where((m) => m.type == MetricType.error).toList();

    // Calculate average timings
    final timingGroups = <String, List<Duration>>{};
    for (final metric in timingMetrics) {
      if (metric.duration != null) {
        timingGroups.putIfAbsent(metric.name, () => []).add(metric.duration!);
      }
    }

    final averageTimings = <String, Duration>{};
    for (final entry in timingGroups.entries) {
      final totalMicroseconds = entry.value.fold(0, (sum, duration) => sum + duration.inMicroseconds);
      final averageMicroseconds = totalMicroseconds ~/ entry.value.length;
      averageTimings[entry.key] = Duration(microseconds: averageMicroseconds);
    }

    // Calculate average memory usage
    double averageMemoryUsage = 0;
    if (memoryMetrics.isNotEmpty) {
      final totalMemory = memoryMetrics.fold(0.0, (sum, metric) {
        final usedMemory = metric.memoryUsage?['used_memory'] ?? 0;
        return sum + (usedMemory as num).toDouble();
      });
      averageMemoryUsage = totalMemory / memoryMetrics.length;
    }

    // Calculate error rate
    final errorRate = metrics.isNotEmpty ? errorMetrics.length / metrics.length : 0.0;

    // Find oldest and newest metrics
    DateTime? oldestMetric;
    DateTime? newestMetric;
    if (metrics.isNotEmpty) {
      oldestMetric = metrics.map((m) => m.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
      newestMetric = metrics.map((m) => m.timestamp).reduce((a, b) => a.isAfter(b) ? a : b);
    }

    return PerformanceAnalytics(
      averageTimings: averageTimings,
      averageMemoryUsage: averageMemoryUsage,
      errorRate: errorRate,
      totalMetrics: metrics.length,
      oldestMetric: oldestMetric,
      newestMetric: newestMetric,
    );
  }
}

// Optimization suggestion model
enum SuggestionType { performance, memory, reliability, battery }
enum SuggestionPriority { low, medium, high, critical }

class OptimizationSuggestion {
  final SuggestionType type;
  final String title;
  final String description;
  final SuggestionPriority priority;
  final String action;

  OptimizationSuggestion({
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
    required this.action,
  });
}
