import 'package:flutter/material.dart';

/// Excel function model for ShadowSuite Tool Builder
class ExcelFunction {
  final String name;
  final String category;
  final String description;
  final List<ExcelParameter> parameters;
  final ExcelReturnType returnType;
  final String syntax;
  final List<String> examples;
  final FunctionComplexity complexity;
  final bool isSupported;
  final String? implementation;

  const ExcelFunction({
    required this.name,
    required this.category,
    required this.description,
    required this.parameters,
    required this.returnType,
    required this.syntax,
    this.examples = const [],
    this.complexity = FunctionComplexity.basic,
    this.isSupported = true,
    this.implementation,
  });

  factory ExcelFunction.fromJson(Map<String, dynamic> json) {
    return ExcelFunction(
      name: json['name'] as String,
      category: json['category'] as String,
      description: json['description'] as String,
      parameters: (json['parameters'] as List<dynamic>)
          .map((p) => ExcelParameter.fromJson(p as Map<String, dynamic>))
          .toList(),
      returnType: ExcelReturnType.values[json['return_type'] as int],
      syntax: json['syntax'] as String,
      examples: (json['examples'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      complexity: FunctionComplexity.values[json['complexity'] as int? ?? 0],
      isSupported: json['is_supported'] as bool? ?? true,
      implementation: json['implementation'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'category': category,
      'description': description,
      'parameters': parameters.map((p) => p.toJson()).toList(),
      'return_type': returnType.index,
      'syntax': syntax,
      'examples': examples,
      'complexity': complexity.index,
      'is_supported': isSupported,
      'implementation': implementation,
    };
  }

  String get displayName => name.toUpperCase();
  
  String get fullSyntax {
    final paramList = parameters.map((p) => p.isOptional ? '[${p.name}]' : p.name).join(', ');
    return '$name($paramList)';
  }

  List<String> get autocompleteVariants {
    return [
      name.toLowerCase(),
      name.toUpperCase(),
      displayName,
    ];
  }

  bool matches(String query) {
    final lowerQuery = query.toLowerCase();
    return name.toLowerCase().contains(lowerQuery) ||
           description.toLowerCase().contains(lowerQuery) ||
           category.toLowerCase().contains(lowerQuery);
  }
}

/// Excel function parameter
class ExcelParameter {
  final String name;
  final ExcelParameterType type;
  final String description;
  final bool isOptional;
  final dynamic defaultValue;
  final List<String>? allowedValues;

  const ExcelParameter({
    required this.name,
    required this.type,
    required this.description,
    this.isOptional = false,
    this.defaultValue,
    this.allowedValues,
  });

  factory ExcelParameter.fromJson(Map<String, dynamic> json) {
    return ExcelParameter(
      name: json['name'] as String,
      type: ExcelParameterType.values[json['type'] as int],
      description: json['description'] as String,
      isOptional: json['is_optional'] as bool? ?? false,
      defaultValue: json['default_value'],
      allowedValues: (json['allowed_values'] as List<dynamic>?)?.map((v) => v as String).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type.index,
      'description': description,
      'is_optional': isOptional,
      'default_value': defaultValue,
      'allowed_values': allowedValues,
    };
  }

  String get displayType {
    switch (type) {
      case ExcelParameterType.number:
        return 'Number';
      case ExcelParameterType.text:
        return 'Text';
      case ExcelParameterType.boolean:
        return 'Boolean';
      case ExcelParameterType.range:
        return 'Range';
      case ExcelParameterType.array:
        return 'Array';
      case ExcelParameterType.any:
        return 'Any';
    }
  }
}

enum ExcelParameterType {
  number,
  text,
  boolean,
  range,
  array,
  any,
}

enum ExcelReturnType {
  number,
  text,
  boolean,
  array,
  error,
  any,
}

enum FunctionComplexity {
  basic,
  intermediate,
  advanced,
  expert,
}

/// Excel function categories with predefined functions
class ExcelFunctionLibrary {
  static const Map<String, List<ExcelFunction>> categories = {
    'Math & Trig': [
      ExcelFunction(
        name: 'SUM',
        category: 'Math & Trig',
        description: 'Adds all the numbers in a range of cells',
        parameters: [
          ExcelParameter(
            name: 'number1',
            type: ExcelParameterType.range,
            description: 'The first number or range to add',
          ),
          ExcelParameter(
            name: 'number2',
            type: ExcelParameterType.range,
            description: 'Additional numbers or ranges to add',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.number,
        syntax: 'SUM(number1, [number2], ...)',
        examples: ['=SUM(A1:A10)', '=SUM(A1, B1, C1)', '=SUM(A1:A5, C1:C5)'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'AVERAGE',
        category: 'Math & Trig',
        description: 'Returns the average of its arguments',
        parameters: [
          ExcelParameter(
            name: 'number1',
            type: ExcelParameterType.range,
            description: 'The first number or range',
          ),
          ExcelParameter(
            name: 'number2',
            type: ExcelParameterType.range,
            description: 'Additional numbers or ranges',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.number,
        syntax: 'AVERAGE(number1, [number2], ...)',
        examples: ['=AVERAGE(A1:A10)', '=AVERAGE(A1, B1, C1)'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'MAX',
        category: 'Math & Trig',
        description: 'Returns the largest value in a set of values',
        parameters: [
          ExcelParameter(
            name: 'number1',
            type: ExcelParameterType.range,
            description: 'The first number or range',
          ),
          ExcelParameter(
            name: 'number2',
            type: ExcelParameterType.range,
            description: 'Additional numbers or ranges',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.number,
        syntax: 'MAX(number1, [number2], ...)',
        examples: ['=MAX(A1:A10)', '=MAX(A1, B1, C1)'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'MIN',
        category: 'Math & Trig',
        description: 'Returns the smallest value in a set of values',
        parameters: [
          ExcelParameter(
            name: 'number1',
            type: ExcelParameterType.range,
            description: 'The first number or range',
          ),
          ExcelParameter(
            name: 'number2',
            type: ExcelParameterType.range,
            description: 'Additional numbers or ranges',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.number,
        syntax: 'MIN(number1, [number2], ...)',
        examples: ['=MIN(A1:A10)', '=MIN(A1, B1, C1)'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'COUNT',
        category: 'Math & Trig',
        description: 'Counts the number of cells that contain numbers',
        parameters: [
          ExcelParameter(
            name: 'value1',
            type: ExcelParameterType.range,
            description: 'The first value or range',
          ),
          ExcelParameter(
            name: 'value2',
            type: ExcelParameterType.range,
            description: 'Additional values or ranges',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.number,
        syntax: 'COUNT(value1, [value2], ...)',
        examples: ['=COUNT(A1:A10)', '=COUNT(A1, B1, C1)'],
        complexity: FunctionComplexity.basic,
      ),
    ],
    'Text': [
      ExcelFunction(
        name: 'CONCATENATE',
        category: 'Text',
        description: 'Joins several text items into one text item',
        parameters: [
          ExcelParameter(
            name: 'text1',
            type: ExcelParameterType.text,
            description: 'The first text item',
          ),
          ExcelParameter(
            name: 'text2',
            type: ExcelParameterType.text,
            description: 'Additional text items',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.text,
        syntax: 'CONCATENATE(text1, [text2], ...)',
        examples: ['=CONCATENATE(A1, " ", B1)', '=CONCATENATE("Hello", " ", "World")'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'LEFT',
        category: 'Text',
        description: 'Returns the leftmost characters from a text value',
        parameters: [
          ExcelParameter(
            name: 'text',
            type: ExcelParameterType.text,
            description: 'The text string',
          ),
          ExcelParameter(
            name: 'num_chars',
            type: ExcelParameterType.number,
            description: 'Number of characters to extract',
            isOptional: true,
            defaultValue: 1,
          ),
        ],
        returnType: ExcelReturnType.text,
        syntax: 'LEFT(text, [num_chars])',
        examples: ['=LEFT(A1, 3)', '=LEFT("Hello World", 5)'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'RIGHT',
        category: 'Text',
        description: 'Returns the rightmost characters from a text value',
        parameters: [
          ExcelParameter(
            name: 'text',
            type: ExcelParameterType.text,
            description: 'The text string',
          ),
          ExcelParameter(
            name: 'num_chars',
            type: ExcelParameterType.number,
            description: 'Number of characters to extract',
            isOptional: true,
            defaultValue: 1,
          ),
        ],
        returnType: ExcelReturnType.text,
        syntax: 'RIGHT(text, [num_chars])',
        examples: ['=RIGHT(A1, 3)', '=RIGHT("Hello World", 5)'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'LEN',
        category: 'Text',
        description: 'Returns the number of characters in a text string',
        parameters: [
          ExcelParameter(
            name: 'text',
            type: ExcelParameterType.text,
            description: 'The text string',
          ),
        ],
        returnType: ExcelReturnType.number,
        syntax: 'LEN(text)',
        examples: ['=LEN(A1)', '=LEN("Hello World")'],
        complexity: FunctionComplexity.basic,
      ),
    ],
    'Logical': [
      ExcelFunction(
        name: 'IF',
        category: 'Logical',
        description: 'Specifies a logical test to perform',
        parameters: [
          ExcelParameter(
            name: 'logical_test',
            type: ExcelParameterType.boolean,
            description: 'The condition to test',
          ),
          ExcelParameter(
            name: 'value_if_true',
            type: ExcelParameterType.any,
            description: 'Value to return if condition is true',
          ),
          ExcelParameter(
            name: 'value_if_false',
            type: ExcelParameterType.any,
            description: 'Value to return if condition is false',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.any,
        syntax: 'IF(logical_test, value_if_true, [value_if_false])',
        examples: ['=IF(A1>10, "High", "Low")', '=IF(B1="", "Empty", B1)'],
        complexity: FunctionComplexity.intermediate,
      ),
      ExcelFunction(
        name: 'AND',
        category: 'Logical',
        description: 'Returns TRUE if all arguments are TRUE',
        parameters: [
          ExcelParameter(
            name: 'logical1',
            type: ExcelParameterType.boolean,
            description: 'The first logical value',
          ),
          ExcelParameter(
            name: 'logical2',
            type: ExcelParameterType.boolean,
            description: 'Additional logical values',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.boolean,
        syntax: 'AND(logical1, [logical2], ...)',
        examples: ['=AND(A1>0, B1<100)', '=AND(A1="Yes", B1="Yes")'],
        complexity: FunctionComplexity.intermediate,
      ),
      ExcelFunction(
        name: 'OR',
        category: 'Logical',
        description: 'Returns TRUE if any argument is TRUE',
        parameters: [
          ExcelParameter(
            name: 'logical1',
            type: ExcelParameterType.boolean,
            description: 'The first logical value',
          ),
          ExcelParameter(
            name: 'logical2',
            type: ExcelParameterType.boolean,
            description: 'Additional logical values',
            isOptional: true,
          ),
        ],
        returnType: ExcelReturnType.boolean,
        syntax: 'OR(logical1, [logical2], ...)',
        examples: ['=OR(A1>100, B1<0)', '=OR(A1="Yes", B1="Maybe")'],
        complexity: FunctionComplexity.intermediate,
      ),
    ],
    'Date & Time': [
      ExcelFunction(
        name: 'TODAY',
        category: 'Date & Time',
        description: 'Returns the current date',
        parameters: [],
        returnType: ExcelReturnType.number,
        syntax: 'TODAY()',
        examples: ['=TODAY()', '=TODAY()+30'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'NOW',
        category: 'Date & Time',
        description: 'Returns the current date and time',
        parameters: [],
        returnType: ExcelReturnType.number,
        syntax: 'NOW()',
        examples: ['=NOW()', '=NOW()-1'],
        complexity: FunctionComplexity.basic,
      ),
      ExcelFunction(
        name: 'YEAR',
        category: 'Date & Time',
        description: 'Returns the year of a date',
        parameters: [
          ExcelParameter(
            name: 'serial_number',
            type: ExcelParameterType.number,
            description: 'The date value',
          ),
        ],
        returnType: ExcelReturnType.number,
        syntax: 'YEAR(serial_number)',
        examples: ['=YEAR(TODAY())', '=YEAR(A1)'],
        complexity: FunctionComplexity.basic,
      ),
    ],
    'Lookup & Reference': [
      ExcelFunction(
        name: 'VLOOKUP',
        category: 'Lookup & Reference',
        description: 'Looks up a value in the first column of a range and returns a value in the same row from another column',
        parameters: [
          ExcelParameter(
            name: 'lookup_value',
            type: ExcelParameterType.any,
            description: 'The value to search for',
          ),
          ExcelParameter(
            name: 'table_array',
            type: ExcelParameterType.range,
            description: 'The range containing the data',
          ),
          ExcelParameter(
            name: 'col_index_num',
            type: ExcelParameterType.number,
            description: 'The column number to return',
          ),
          ExcelParameter(
            name: 'range_lookup',
            type: ExcelParameterType.boolean,
            description: 'TRUE for approximate match, FALSE for exact match',
            isOptional: true,
            defaultValue: true,
          ),
        ],
        returnType: ExcelReturnType.any,
        syntax: 'VLOOKUP(lookup_value, table_array, col_index_num, [range_lookup])',
        examples: ['=VLOOKUP(A1, B:D, 2, FALSE)', '=VLOOKUP("Apple", A1:C10, 3, TRUE)'],
        complexity: FunctionComplexity.advanced,
      ),
    ],
  };

  static List<ExcelFunction> get allFunctions {
    return categories.values.expand((functions) => functions).toList();
  }

  static List<String> get allCategories {
    return categories.keys.toList();
  }

  static List<ExcelFunction> getFunctionsByCategory(String category) {
    return categories[category] ?? [];
  }

  static List<ExcelFunction> searchFunctions(String query) {
    return allFunctions.where((func) => func.matches(query)).toList();
  }

  static ExcelFunction? getFunction(String name) {
    try {
      return allFunctions.firstWhere((func) => func.name.toLowerCase() == name.toLowerCase());
    } catch (e) {
      return null;
    }
  }
}
