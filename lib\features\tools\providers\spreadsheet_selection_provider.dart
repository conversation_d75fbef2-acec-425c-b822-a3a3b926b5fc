import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/spreadsheet_cell.dart';

/// Cell selection state
class CellSelectionState {
  final String? selectedCell;
  final Set<String> selectedRange;
  final String? editingCell;
  final String? editingText;
  final bool isMultiSelect;
  final bool isDragSelecting;
  final String? rangeStart;
  final String? rangeEnd;
  final Map<String, String> clipboard;
  final List<String> selectionHistory;

  const CellSelectionState({
    this.selectedCell,
    this.selectedRange = const {},
    this.editingCell,
    this.editingText,
    this.isMultiSelect = false,
    this.isDragSelecting = false,
    this.rangeStart,
    this.rangeEnd,
    this.clipboard = const {},
    this.selectionHistory = const [],
  });

  CellSelectionState copyWith({
    String? selectedCell,
    Set<String>? selectedRange,
    String? editingCell,
    String? editingText,
    bool? isMultiSelect,
    bool? isDragSelecting,
    String? rangeStart,
    String? rangeEnd,
    Map<String, String>? clipboard,
    List<String>? selectionHistory,
  }) {
    return CellSelectionState(
      selectedCell: selectedCell ?? this.selectedCell,
      selectedRange: selectedRange ?? this.selectedRange,
      editingCell: editingCell ?? this.editingCell,
      editingText: editingText ?? this.editingText,
      isMultiSelect: isMultiSelect ?? this.isMultiSelect,
      isDragSelecting: isDragSelecting ?? this.isDragSelecting,
      rangeStart: rangeStart ?? this.rangeStart,
      rangeEnd: rangeEnd ?? this.rangeEnd,
      clipboard: clipboard ?? this.clipboard,
      selectionHistory: selectionHistory ?? this.selectionHistory,
    );
  }
}

/// Cell selection notifier
class CellSelectionNotifier extends StateNotifier<CellSelectionState> {
  CellSelectionNotifier() : super(const CellSelectionState());

  /// Select a single cell
  void selectCell(String address, {bool addToSelection = false}) {
    if (addToSelection && state.isMultiSelect) {
      final newRange = Set<String>.from(state.selectedRange)..add(address);
      state = state.copyWith(
        selectedCell: address,
        selectedRange: newRange,
        selectionHistory: [...state.selectionHistory, address],
      );
    } else {
      state = state.copyWith(
        selectedCell: address,
        selectedRange: {address},
        isMultiSelect: false,
        selectionHistory: [address],
      );
    }
  }

  /// Start range selection
  void startRangeSelection(String startAddress) {
    state = state.copyWith(
      rangeStart: startAddress,
      isDragSelecting: true,
      selectedRange: {startAddress},
    );
  }

  /// Update range selection during drag
  void updateRangeSelection(String currentAddress) {
    if (state.rangeStart == null) return;

    final range = _calculateRange(state.rangeStart!, currentAddress);
    state = state.copyWith(
      rangeEnd: currentAddress,
      selectedRange: range,
      selectedCell: currentAddress,
    );
  }

  /// End range selection
  void endRangeSelection() {
    state = state.copyWith(
      isDragSelecting: false,
      rangeStart: null,
      rangeEnd: null,
    );
  }

  /// Toggle multi-select mode
  void toggleMultiSelect() {
    state = state.copyWith(isMultiSelect: !state.isMultiSelect);
  }

  /// Start editing a cell
  void startEditing(String address, String? initialText) {
    state = state.copyWith(
      editingCell: address,
      editingText: initialText ?? '',
      selectedCell: address,
    );
  }

  /// Update editing text
  void updateEditingText(String text) {
    state = state.copyWith(editingText: text);
  }

  /// Stop editing
  void stopEditing() {
    state = state.copyWith(
      editingCell: null,
      editingText: null,
    );
  }

  /// Clear selection
  void clearSelection() {
    state = state.copyWith(
      selectedCell: null,
      selectedRange: const {},
      isMultiSelect: false,
      editingCell: null,
      editingText: null,
    );
  }

  /// Navigate with keyboard
  void navigateWithKeyboard(LogicalKeyboardKey key, {bool extend = false}) {
    if (state.selectedCell == null) return;

    final currentPos = SpreadsheetUtils.parseCellAddress(state.selectedCell!);
    int newRow = currentPos['row']!;
    int newCol = currentPos['column']!;

    switch (key) {
      case LogicalKeyboardKey.arrowUp:
        newRow = (newRow - 1).clamp(1, 1000);
        break;
      case LogicalKeyboardKey.arrowDown:
        newRow = (newRow + 1).clamp(1, 1000);
        break;
      case LogicalKeyboardKey.arrowLeft:
        newCol = (newCol - 1).clamp(1, 26);
        break;
      case LogicalKeyboardKey.arrowRight:
        newCol = (newCol + 1).clamp(1, 26);
        break;
      case LogicalKeyboardKey.tab:
        newCol = (newCol + 1).clamp(1, 26);
        break;
      case LogicalKeyboardKey.enter:
        newRow = (newRow + 1).clamp(1, 1000);
        break;
    }

    final newAddress = SpreadsheetUtils.getCellAddress(newRow, newCol);
    
    if (extend) {
      if (state.rangeStart == null) {
        startRangeSelection(state.selectedCell!);
      }
      updateRangeSelection(newAddress);
    } else {
      selectCell(newAddress);
    }
  }

  /// Jump to data boundaries with Ctrl+Arrow
  void jumpToDataBoundary(LogicalKeyboardKey key, Map<String, SpreadsheetCell> cells) {
    if (state.selectedCell == null) return;

    final currentPos = SpreadsheetUtils.parseCellAddress(state.selectedCell!);
    int newRow = currentPos['row']!;
    int newCol = currentPos['column']!;

    switch (key) {
      case LogicalKeyboardKey.arrowUp:
        newRow = _findDataBoundaryUp(newRow, newCol, cells);
        break;
      case LogicalKeyboardKey.arrowDown:
        newRow = _findDataBoundaryDown(newRow, newCol, cells);
        break;
      case LogicalKeyboardKey.arrowLeft:
        newCol = _findDataBoundaryLeft(newRow, newCol, cells);
        break;
      case LogicalKeyboardKey.arrowRight:
        newCol = _findDataBoundaryRight(newRow, newCol, cells);
        break;
    }

    final newAddress = SpreadsheetUtils.getCellAddress(newRow, newCol);
    selectCell(newAddress);
  }

  /// Copy selected cells to clipboard
  void copyToClipboard(Map<String, SpreadsheetCell> cells) {
    final clipboard = <String, String>{};
    
    for (final address in state.selectedRange) {
      final cell = cells[address];
      if (cell != null) {
        clipboard[address] = cell.formula ?? cell.value?.toString() ?? '';
      }
    }
    
    state = state.copyWith(clipboard: clipboard);
    
    // Also copy to system clipboard
    final clipboardText = clipboard.values.join('\t');
    Clipboard.setData(ClipboardData(text: clipboardText));
  }

  /// Paste from clipboard
  void pasteFromClipboard(Map<String, SpreadsheetCell> cells, Function(String, String) onCellUpdate) {
    if (state.selectedCell == null || state.clipboard.isEmpty) return;

    final startPos = SpreadsheetUtils.parseCellAddress(state.selectedCell!);
    int rowOffset = 0;
    int colOffset = 0;

    for (final entry in state.clipboard.entries) {
      final sourcePos = SpreadsheetUtils.parseCellAddress(entry.key);
      final targetRow = startPos['row']! + rowOffset;
      final targetCol = startPos['column']! + colOffset;
      final targetAddress = SpreadsheetUtils.getCellAddress(targetRow, targetCol);
      
      onCellUpdate(targetAddress, entry.value);
      
      colOffset++;
      if (colOffset >= state.clipboard.length) {
        colOffset = 0;
        rowOffset++;
      }
    }
  }

  /// Calculate range between two addresses
  Set<String> _calculateRange(String start, String end) {
    final startPos = SpreadsheetUtils.parseCellAddress(start);
    final endPos = SpreadsheetUtils.parseCellAddress(end);
    
    final minRow = [startPos['row']!, endPos['row']!].reduce((a, b) => a < b ? a : b);
    final maxRow = [startPos['row']!, endPos['row']!].reduce((a, b) => a > b ? a : b);
    final minCol = [startPos['column']!, endPos['column']!].reduce((a, b) => a < b ? a : b);
    final maxCol = [startPos['column']!, endPos['column']!].reduce((a, b) => a > b ? a : b);
    
    final range = <String>{};
    for (int row = minRow; row <= maxRow; row++) {
      for (int col = minCol; col <= maxCol; col++) {
        range.add(SpreadsheetUtils.getCellAddress(row, col));
      }
    }
    
    return range;
  }

  /// Find data boundary methods
  int _findDataBoundaryUp(int row, int col, Map<String, SpreadsheetCell> cells) {
    for (int r = row - 1; r >= 1; r--) {
      final address = SpreadsheetUtils.getCellAddress(r, col);
      final cell = cells[address];
      if (cell == null || cell.isEmpty) {
        return r + 1;
      }
    }
    return 1;
  }

  int _findDataBoundaryDown(int row, int col, Map<String, SpreadsheetCell> cells) {
    for (int r = row + 1; r <= 1000; r++) {
      final address = SpreadsheetUtils.getCellAddress(r, col);
      final cell = cells[address];
      if (cell == null || cell.isEmpty) {
        return r - 1;
      }
    }
    return 1000;
  }

  int _findDataBoundaryLeft(int row, int col, Map<String, SpreadsheetCell> cells) {
    for (int c = col - 1; c >= 1; c--) {
      final address = SpreadsheetUtils.getCellAddress(row, c);
      final cell = cells[address];
      if (cell == null || cell.isEmpty) {
        return c + 1;
      }
    }
    return 1;
  }

  int _findDataBoundaryRight(int row, int col, Map<String, SpreadsheetCell> cells) {
    for (int c = col + 1; c <= 26; c++) {
      final address = SpreadsheetUtils.getCellAddress(row, c);
      final cell = cells[address];
      if (cell == null || cell.isEmpty) {
        return c - 1;
      }
    }
    return 26;
  }
}

/// Cell selection provider
final cellSelectionProvider = StateNotifierProvider<CellSelectionNotifier, CellSelectionState>(
  (ref) => CellSelectionNotifier(),
);
