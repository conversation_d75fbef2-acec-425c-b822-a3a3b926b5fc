class ValidationService {
  static final ValidationService _instance = ValidationService._internal();
  factory ValidationService() => _instance;
  ValidationService._internal();

  static ValidationService get instance => _instance;

  /// Validate email format
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return null; // Email is optional in most cases
    }
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Validate required text field
  static String? validateRequired(String? value, {String fieldName = 'Field'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validate text length
  static String? validateLength(String? value, {
    int? minLength,
    int? maxLength,
    String fieldName = 'Field',
  }) {
    if (value == null) return null;
    
    if (minLength != null && value.length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }
    
    if (maxLength != null && value.length > maxLength) {
      return '$fieldName must not exceed $maxLength characters';
    }
    
    return null;
  }

  /// Validate monetary amount
  static String? validateAmount(String? value, {
    double? minAmount,
    double? maxAmount,
    bool allowNegative = false,
  }) {
    if (value == null || value.trim().isEmpty) {
      return 'Amount is required';
    }
    
    final amount = double.tryParse(value);
    if (amount == null) {
      return 'Please enter a valid amount';
    }
    
    if (!allowNegative && amount < 0) {
      return 'Amount cannot be negative';
    }
    
    if (minAmount != null && amount < minAmount) {
      return 'Amount must be at least \$${minAmount.toStringAsFixed(2)}';
    }
    
    if (maxAmount != null && amount > maxAmount) {
      return 'Amount cannot exceed \$${maxAmount.toStringAsFixed(2)}';
    }
    
    return null;
  }

  /// Validate account name
  static String? validateAccountName(String? value) {
    final requiredValidation = validateRequired(value, fieldName: 'Account name');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = validateLength(value, minLength: 2, maxLength: 50, fieldName: 'Account name');
    if (lengthValidation != null) return lengthValidation;
    
    return null;
  }

  /// Validate transaction description
  static String? validateTransactionDescription(String? value) {
    final requiredValidation = validateRequired(value, fieldName: 'Description');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = validateLength(value, minLength: 2, maxLength: 200, fieldName: 'Description');
    if (lengthValidation != null) return lengthValidation;
    
    return null;
  }

  /// Validate note title
  static String? validateNoteTitle(String? value) {
    final requiredValidation = validateRequired(value, fieldName: 'Title');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = validateLength(value, minLength: 1, maxLength: 100, fieldName: 'Title');
    if (lengthValidation != null) return lengthValidation;
    
    return null;
  }

  /// Validate note content
  static String? validateNoteContent(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Content is required';
    }
    
    if (value.length > 10000) {
      return 'Content is too long (maximum 10,000 characters)';
    }
    
    return null;
  }

  /// Validate todo title
  static String? validateTodoTitle(String? value) {
    final requiredValidation = validateRequired(value, fieldName: 'Todo title');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = validateLength(value, minLength: 1, maxLength: 100, fieldName: 'Todo title');
    if (lengthValidation != null) return lengthValidation;
    
    return null;
  }

  /// Validate voice memo title
  static String? validateVoiceMemoTitle(String? value) {
    final requiredValidation = validateRequired(value, fieldName: 'Voice memo title');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = validateLength(value, minLength: 1, maxLength: 100, fieldName: 'Voice memo title');
    if (lengthValidation != null) return lengthValidation;
    
    return null;
  }

  /// Validate category name
  static String? validateCategoryName(String? value) {
    final requiredValidation = validateRequired(value, fieldName: 'Category name');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = validateLength(value, minLength: 2, maxLength: 50, fieldName: 'Category name');
    if (lengthValidation != null) return lengthValidation;
    
    return null;
  }

  /// Validate budget amount
  static String? validateBudgetAmount(String? value) {
    final amountValidation = validateAmount(value, minAmount: 0.01, maxAmount: 1000000);
    if (amountValidation != null) return amountValidation;
    
    return null;
  }

  /// Validate user name
  static String? validateUserName(String? value) {
    final requiredValidation = validateRequired(value, fieldName: 'Name');
    if (requiredValidation != null) return requiredValidation;
    
    final lengthValidation = validateLength(value, minLength: 2, maxLength: 50, fieldName: 'Name');
    if (lengthValidation != null) return lengthValidation;
    
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final nameRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!nameRegex.hasMatch(value!)) {
      return 'Name can only contain letters, spaces, hyphens, and apostrophes';
    }
    
    return null;
  }

  /// Validate file path
  static String? validateFilePath(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'File path is required';
    }
    
    // Basic file path validation
    if (value.contains('..') || value.contains('//')) {
      return 'Invalid file path';
    }
    
    return null;
  }

  /// Validate percentage (0-100)
  static String? validatePercentage(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Percentage is required';
    }
    
    final percentage = double.tryParse(value);
    if (percentage == null) {
      return 'Please enter a valid percentage';
    }
    
    if (percentage < 0 || percentage > 100) {
      return 'Percentage must be between 0 and 100';
    }
    
    return null;
  }

  /// Validate date is not in the future (for completed items)
  static String? validatePastDate(DateTime? date) {
    if (date == null) return null;
    
    if (date.isAfter(DateTime.now())) {
      return 'Date cannot be in the future';
    }
    
    return null;
  }

  /// Validate date is not too far in the past
  static String? validateReasonableDate(DateTime? date) {
    if (date == null) return null;
    
    final oneYearAgo = DateTime.now().subtract(const Duration(days: 365));
    if (date.isBefore(oneYearAgo)) {
      return 'Date seems too far in the past';
    }
    
    return null;
  }

  /// Validate phone number (optional, basic validation)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Phone number is optional
    }
    
    // Remove all non-digit characters for validation
    final digitsOnly = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  /// Validate URL (optional)
  static String? validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // URL is optional
    }
    
    final urlRegex = RegExp(r'^https?:\/\/[^\s/$.?#].[^\s]*$');
    if (!urlRegex.hasMatch(value)) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }

  /// Validate tag name
  static String? validateTagName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Tag name is required';
    }
    
    if (value.length > 30) {
      return 'Tag name must not exceed 30 characters';
    }
    
    // Tags should not contain special characters except hyphens and underscores
    final tagRegex = RegExp(r'^[a-zA-Z0-9\-_]+$');
    if (!tagRegex.hasMatch(value)) {
      return 'Tag can only contain letters, numbers, hyphens, and underscores';
    }
    
    return null;
  }

  /// Combine multiple validators
  static String? combineValidators(String? value, List<String? Function(String?)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }

  /// Validate form with multiple fields
  static Map<String, String> validateForm(Map<String, dynamic> formData, Map<String, String? Function(String?)> validators) {
    final errors = <String, String>{};
    
    for (final entry in validators.entries) {
      final fieldName = entry.key;
      final validator = entry.value;
      final value = formData[fieldName]?.toString();
      
      final error = validator(value);
      if (error != null) {
        errors[fieldName] = error;
      }
    }
    
    return errors;
  }

  /// Check if form is valid (no errors)
  static bool isFormValid(Map<String, String> errors) {
    return errors.isEmpty;
  }
}
