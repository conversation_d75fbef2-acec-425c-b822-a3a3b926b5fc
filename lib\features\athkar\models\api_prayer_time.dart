/// API Prayer Time model for external prayer times API
class ApiPrayerTime {
  final String prayerName;
  final String prayerNameArabic;
  final DateTime time;
  final String location;

  ApiPrayerTime({
    required this.prayerName,
    required this.prayerNameArabic,
    required this.time,
    required this.location,
  });

  @override
  String toString() {
    return 'ApiPrayerTime(prayerName: $prayerName, time: $time, location: $location)';
  }
}
