import 'dart:typed_data';
import 'package:excel/excel.dart';

/// Utility class for generating sample Excel files for testing
class SampleExcelGenerator {
  /// Generate a sample Excel file with various data types and formulas
  static Uint8List generateSampleExcel() {
    final excel = Excel.createExcel();
    final sheet = excel['Sheet1'];

    // Add headers
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Item');
    sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('Quantity');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Price');
    sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('Total');

    // Add sample data
    final items = [
      ['Laptop', 2, 999.99],
      ['Mouse', 5, 25.50],
      ['Keyboard', 3, 75.00],
      ['Monitor', 1, 299.99],
    ];

    for (int i = 0; i < items.length; i++) {
      final row = i + 2; // Start from row 2
      final item = items[i];
      
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(item[0] as String);
      sheet.cell(CellIndex.indexByString('B$row')).value = IntCellValue(item[1] as int);
      sheet.cell(CellIndex.indexByString('C$row')).value = DoubleCellValue(item[2] as double);
      
      // Add formula for total (Quantity * Price)
      sheet.cell(CellIndex.indexByString('D$row')).value = FormulaCellValue('=B$row*C$row');
    }

    // Add summary row
    final summaryRow = items.length + 3;
    sheet.cell(CellIndex.indexByString('A$summaryRow')).value = TextCellValue('TOTAL:');
    sheet.cell(CellIndex.indexByString('D$summaryRow')).value = FormulaCellValue('=SUM(D2:D${items.length + 1})');

    // Add some additional formulas for testing
    sheet.cell(CellIndex.indexByString('F1')).value = TextCellValue('Statistics');
    sheet.cell(CellIndex.indexByString('F2')).value = TextCellValue('Average Price:');
    sheet.cell(CellIndex.indexByString('G2')).value = FormulaCellValue('=AVERAGE(C2:C${items.length + 1})');

    sheet.cell(CellIndex.indexByString('F3')).value = TextCellValue('Max Price:');
    sheet.cell(CellIndex.indexByString('G3')).value = FormulaCellValue('=MAX(C2:C${items.length + 1})');

    sheet.cell(CellIndex.indexByString('F4')).value = TextCellValue('Min Price:');
    sheet.cell(CellIndex.indexByString('G4')).value = FormulaCellValue('=MIN(C2:C${items.length + 1})');

    // Add some boolean and date values
    sheet.cell(CellIndex.indexByString('F6')).value = TextCellValue('In Stock:');
    sheet.cell(CellIndex.indexByString('G6')).value = BoolCellValue(true);

    sheet.cell(CellIndex.indexByString('F7')).value = TextCellValue('Last Updated:');
    sheet.cell(CellIndex.indexByString('G7')).value = DateTimeCellValue(
      year: DateTime.now().year,
      month: DateTime.now().month,
      day: DateTime.now().day,
      hour: DateTime.now().hour,
      minute: DateTime.now().minute,
    );

    // Style the headers
    for (int col = 0; col < 4; col++) {
      final cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.blue,
        fontColorHex: ExcelColor.white,
      );
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0)).cellStyle = cellStyle;
    }

    // Generate and return the Excel file bytes
    return Uint8List.fromList(excel.encode()!);
  }

  /// Generate a simple CSV content for testing
  static String generateSampleCsv() {
    return '''Name,Age,City,Salary
John Doe,30,New York,75000
Jane Smith,25,Los Angeles,68000
Bob Johnson,35,Chicago,82000
Alice Brown,28,Houston,71000
Charlie Wilson,32,Phoenix,79000''';
  }

  /// Generate Excel with various formula types for comprehensive testing
  static Uint8List generateFormulaTestExcel() {
    final excel = Excel.createExcel();
    final sheet = excel['Sheet1'];

    // Math formulas
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Math Functions');
    sheet.cell(CellIndex.indexByString('A2')).value = IntCellValue(10);
    sheet.cell(CellIndex.indexByString('A3')).value = IntCellValue(20);
    sheet.cell(CellIndex.indexByString('A4')).value = FormulaCellValue('=SUM(A2:A3)');
    sheet.cell(CellIndex.indexByString('A5')).value = FormulaCellValue('=AVERAGE(A2:A3)');
    sheet.cell(CellIndex.indexByString('A6')).value = FormulaCellValue('=MAX(A2:A3)');
    sheet.cell(CellIndex.indexByString('A7')).value = FormulaCellValue('=MIN(A2:A3)');

    // Logical formulas
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('Logical Functions');
    sheet.cell(CellIndex.indexByString('C2')).value = FormulaCellValue('=IF(A2>15,"High","Low")');
    sheet.cell(CellIndex.indexByString('C3')).value = FormulaCellValue('=AND(A2>5,A3>15)');
    sheet.cell(CellIndex.indexByString('C4')).value = FormulaCellValue('=OR(A2>25,A3>15)');
    sheet.cell(CellIndex.indexByString('C5')).value = FormulaCellValue('=NOT(A2>25)');

    // Text formulas
    sheet.cell(CellIndex.indexByString('E1')).value = TextCellValue('Text Functions');
    sheet.cell(CellIndex.indexByString('E2')).value = TextCellValue('Hello World');
    sheet.cell(CellIndex.indexByString('E3')).value = FormulaCellValue('=LEN(E2)');
    sheet.cell(CellIndex.indexByString('E4')).value = FormulaCellValue('=UPPER(E2)');
    sheet.cell(CellIndex.indexByString('E5')).value = FormulaCellValue('=LOWER(E2)');
    sheet.cell(CellIndex.indexByString('E6')).value = FormulaCellValue('=LEFT(E2,5)');
    sheet.cell(CellIndex.indexByString('E7')).value = FormulaCellValue('=RIGHT(E2,5)');

    // Date formulas
    sheet.cell(CellIndex.indexByString('G1')).value = TextCellValue('Date Functions');
    sheet.cell(CellIndex.indexByString('G2')).value = FormulaCellValue('=TODAY()');
    sheet.cell(CellIndex.indexByString('G3')).value = FormulaCellValue('=NOW()');
    sheet.cell(CellIndex.indexByString('G4')).value = FormulaCellValue('=YEAR(G2)');
    sheet.cell(CellIndex.indexByString('G5')).value = FormulaCellValue('=MONTH(G2)');
    sheet.cell(CellIndex.indexByString('G6')).value = FormulaCellValue('=DAY(G2)');

    return Uint8List.fromList(excel.encode()!);
  }

  /// Generate Excel with named ranges (simplified representation)
  static Uint8List generateNamedRangeExcel() {
    final excel = Excel.createExcel();
    final sheet = excel['Sheet1'];

    // Create data that would typically have named ranges
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Revenue');
    sheet.cell(CellIndex.indexByString('B1')).value = IntCellValue(100000);

    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Expenses');
    sheet.cell(CellIndex.indexByString('B2')).value = IntCellValue(75000);

    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Profit');
    sheet.cell(CellIndex.indexByString('B3')).value = FormulaCellValue('=B1-B2');

    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Profit Margin');
    sheet.cell(CellIndex.indexByString('B5')).value = FormulaCellValue('=B3/B1*100');

    // Add comments to indicate these would be named ranges
    sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('Named Ranges:');
    sheet.cell(CellIndex.indexByString('D2')).value = TextCellValue('Revenue = B1');
    sheet.cell(CellIndex.indexByString('D3')).value = TextCellValue('Expenses = B2');
    sheet.cell(CellIndex.indexByString('D4')).value = TextCellValue('Profit = B3');

    return Uint8List.fromList(excel.encode()!);
  }
}
