import 'dart:math' as math;
import '../../features/tools/models/spreadsheet_cell.dart';

/// Enhanced formula engine with Excel-like functions
class FormulaEngine {
  static final FormulaEngine _instance = FormulaEngine._internal();
  factory FormulaEngine() => _instance;
  FormulaEngine._internal();

  /// Recalculate all formulas in a sheet
  Map<String, SpreadsheetCell> recalculateSheet(Map<String, SpreadsheetCell> cells) {
    final result = Map<String, SpreadsheetCell>.from(cells);
    final calculationOrder = _getCalculationOrder(result);
    
    for (final address in calculationOrder) {
      final cell = result[address];
      if (cell != null && cell.type == CellType.formula && cell.formula != null) {
        try {
          final value = evaluateFormula(cell.formula!, result);
          result[address] = cell.copyWith(value: value);
        } catch (e) {
          result[address] = cell.copyWith(value: '#ERROR: $e');
        }
      }
    }
    
    return result;
  }

  /// Evaluate a single formula
  dynamic evaluateFormula(String formula, Map<String, Spreadsheet<PERSON>ell> cells) {
    if (!formula.startsWith('=')) {
      return formula;
    }
    
    final expression = formula.substring(1); // Remove '=' prefix
    return _evaluateExpression(expression, cells);
  }

  /// Get calculation order to handle dependencies
  List<String> _getCalculationOrder(Map<String, SpreadsheetCell> cells) {
    final formulaCells = cells.entries
        .where((entry) => entry.value.type == CellType.formula)
        .map((entry) => entry.key)
        .toList();
    
    // Simple topological sort for dependencies
    final visited = <String>{};
    final result = <String>[];
    
    void visit(String address) {
      if (visited.contains(address)) return;
      visited.add(address);
      
      final cell = cells[address];
      if (cell?.formula != null) {
        final dependencies = _extractCellReferences(cell!.formula!);
        for (final dep in dependencies) {
          if (formulaCells.contains(dep)) {
            visit(dep);
          }
        }
      }
      
      result.add(address);
    }
    
    for (final address in formulaCells) {
      visit(address);
    }
    
    return result;
  }

  /// Extract cell references from formula
  List<String> _extractCellReferences(String formula) {
    final references = <String>[];
    final regex = RegExp(r'[A-Z]+\d+');
    final matches = regex.allMatches(formula);
    
    for (final match in matches) {
      references.add(match.group(0)!);
    }
    
    return references;
  }

  /// Evaluate expression with functions and operators
  dynamic _evaluateExpression(String expression, Map<String, SpreadsheetCell> cells) {
    expression = expression.trim();
    
    // Handle functions
    if (expression.contains('(')) {
      return _evaluateFunction(expression, cells);
    }
    
    // Handle simple arithmetic
    return _evaluateArithmetic(expression, cells);
  }

  /// Evaluate function calls
  dynamic _evaluateFunction(String expression, Map<String, SpreadsheetCell> cells) {
    // Extract function name and arguments
    final funcMatch = RegExp(r'(\w+)\((.*)\)').firstMatch(expression);
    if (funcMatch == null) {
      return _evaluateArithmetic(expression, cells);
    }
    
    final funcName = funcMatch.group(1)!.toUpperCase();
    final argsString = funcMatch.group(2)!;
    final args = _parseArguments(argsString, cells);
    
    switch (funcName) {
      case 'SUM':
        return _sum(args);
      case 'AVERAGE':
      case 'AVG':
        return _average(args);
      case 'MIN':
        return _min(args);
      case 'MAX':
        return _max(args);
      case 'COUNT':
        return _count(args);
      case 'IF':
        return _if(args);
      case 'VLOOKUP':
        return _vlookup(args, cells);
      case 'INDEX':
        return _index(args, cells);
      case 'MATCH':
        return _match(args, cells);
      case 'ROUND':
        return _round(args);
      case 'ABS':
        return _abs(args);
      case 'SQRT':
        return _sqrt(args);
      case 'POWER':
      case 'POW':
        return _power(args);
      case 'MOD':
        return _mod(args);
      case 'AND':
        return _and(args);
      case 'OR':
        return _or(args);
      case 'NOT':
        return _not(args);

      // Math & Trigonometry - Extended
      case 'PRODUCT':
        return _product(args);
      case 'ROUNDUP':
        return _roundUp(args);
      case 'ROUNDDOWN':
        return _roundDown(args);
      case 'INT':
        return _int(args);
      case 'EXP':
        return _exp(args);
      case 'LOG':
        return _log(args);
      case 'LN':
        return _ln(args);
      case 'PI':
        return _pi();
      case 'RAND':
        return _rand();
      case 'RANDBETWEEN':
        return _randBetween(args);
      case 'SIN':
        return _sin(args);
      case 'COS':
        return _cos(args);
      case 'TAN':
        return _tan(args);
      case 'ASIN':
        return _asin(args);
      case 'ACOS':
        return _acos(args);
      case 'ATAN':
        return _atan(args);
      case 'DEGREES':
        return _degrees(args);
      case 'RADIANS':
        return _radians(args);

      // Logical Functions - Extended
      case 'IFS':
        return _ifs(args);
      case 'IFERROR':
        return _ifError(args);
      case 'IFNA':
        return _ifNA(args);
      case 'SWITCH':
        return _switch(args);
      case 'XOR':
        return _xor(args);

      // Lookup & Reference - Extended
      case 'HLOOKUP':
        return _hlookup(args, cells);
      case 'XLOOKUP':
        return _xlookup(args, cells);
      case 'OFFSET':
        return _offset(args, cells);
      case 'CHOOSE':
        return _choose(args);
      case 'LOOKUP':
        return _lookup(args, cells);

      // Date & Time Functions
      case 'TODAY':
        return _today();
      case 'NOW':
        return _now();
      case 'YEAR':
        return _year(args);
      case 'MONTH':
        return _month(args);
      case 'DAY':
        return _day(args);
      case 'WEEKDAY':
        return _weekday(args);
      case 'HOUR':
        return _hour(args);
      case 'MINUTE':
        return _minute(args);
      case 'SECOND':
        return _second(args);
      case 'DATE':
        return _date(args);
      case 'TIME':
        return _time(args);

      // Text Functions
      case 'LEFT':
        return _left(args);
      case 'RIGHT':
        return _right(args);
      case 'MID':
        return _mid(args);
      case 'LEN':
        return _len(args);
      case 'FIND':
        return _find(args);
      case 'SEARCH':
        return _search(args);
      case 'SUBSTITUTE':
        return _substitute(args);
      case 'REPLACE':
        return _replace(args);
      case 'UPPER':
        return _upper(args);
      case 'LOWER':
        return _lower(args);
      case 'PROPER':
        return _proper(args);
      case 'TRIM':
        return _trim(args);
      case 'CONCAT':
        return _concat(args);
      case 'VALUE':
        return _value(args);

      // Statistical Functions
      case 'AVERAGEIF':
        return _averageIf(args, cells);
      case 'COUNTA':
        return _countA(args);
      case 'COUNTIFS':
        return _countIfs(args, cells);
      case 'MEDIAN':
        return _median(args);
      case 'STDEV':
        return _stdev(args);
      case 'VAR':
        return _var(args);

      // Financial Functions
      case 'PMT':
        return _pmt(args);
      case 'FV':
        return _fv(args);
      case 'NPV':
        return _npv(args);
      case 'IRR':
        return _irr(args);
      case 'RATE':
        return _rate(args);

      default:
        throw Exception('Unknown function: $funcName');
    }
  }

  /// Parse function arguments
  List<dynamic> _parseArguments(String argsString, Map<String, SpreadsheetCell> cells) {
    if (argsString.trim().isEmpty) return [];
    
    final args = <dynamic>[];
    final parts = argsString.split(',');
    
    for (final part in parts) {
      final trimmed = part.trim();
      if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
        // String literal
        args.add(trimmed.substring(1, trimmed.length - 1));
      } else if (RegExp(r'^[A-Z]+\d+$').hasMatch(trimmed)) {
        // Cell reference
        final cell = cells[trimmed];
        args.add(cell?.value ?? 0);
      } else if (RegExp(r'^-?\d+\.?\d*$').hasMatch(trimmed)) {
        // Number literal
        args.add(double.tryParse(trimmed) ?? 0);
      } else {
        // Expression
        args.add(_evaluateExpression(trimmed, cells));
      }
    }
    
    return args;
  }

  /// Evaluate arithmetic expressions
  dynamic _evaluateArithmetic(String expression, Map<String, SpreadsheetCell> cells) {
    // Replace cell references with values
    String processedExpression = expression;
    final cellRefs = _extractCellReferences(expression);
    
    for (final ref in cellRefs) {
      final cell = cells[ref];
      final value = cell?.value ?? 0;
      processedExpression = processedExpression.replaceAll(ref, value.toString());
    }
    
    // Simple arithmetic evaluation
    return _calculateArithmetic(processedExpression);
  }

  /// Calculate arithmetic expression
  double _calculateArithmetic(String expression) {
    // Very basic arithmetic parser
    // In a real implementation, this would be more sophisticated
    try {
      // Handle basic operations: +, -, *, /
      expression = expression.replaceAll(' ', '');
      
      // Simple evaluation for basic expressions
      if (expression.contains('+')) {
        final parts = expression.split('+');
        return parts.fold(0.0, (sum, part) => sum + (double.tryParse(part) ?? 0));
      } else if (expression.contains('-')) {
        final parts = expression.split('-');
        if (parts.length == 2) {
          return (double.tryParse(parts[0]) ?? 0) - (double.tryParse(parts[1]) ?? 0);
        }
      } else if (expression.contains('*')) {
        final parts = expression.split('*');
        return parts.fold(1.0, (product, part) => product * (double.tryParse(part) ?? 1));
      } else if (expression.contains('/')) {
        final parts = expression.split('/');
        if (parts.length == 2) {
          final divisor = double.tryParse(parts[1]) ?? 1;
          return divisor != 0 ? (double.tryParse(parts[0]) ?? 0) / divisor : double.infinity;
        }
      }
      
      return double.tryParse(expression) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // Excel-like functions implementation
  double _sum(List<dynamic> args) {
    return args.fold(0.0, (sum, arg) => sum + (double.tryParse(arg.toString()) ?? 0));
  }

  double _average(List<dynamic> args) {
    if (args.isEmpty) return 0;
    return _sum(args) / args.length;
  }

  double _min(List<dynamic> args) {
    if (args.isEmpty) return 0;
    return args.map((arg) => double.tryParse(arg.toString()) ?? double.infinity).reduce(math.min);
  }

  double _max(List<dynamic> args) {
    if (args.isEmpty) return 0;
    return args.map((arg) => double.tryParse(arg.toString()) ?? double.negativeInfinity).reduce(math.max);
  }

  int _count(List<dynamic> args) {
    return args.where((arg) => double.tryParse(arg.toString()) != null).length;
  }

  dynamic _if(List<dynamic> args) {
    if (args.length < 2) return '';
    final condition = args[0];
    final trueValue = args[1];
    final falseValue = args.length > 2 ? args[2] : '';
    
    // Simple condition evaluation
    bool conditionResult = false;
    if (condition is bool) {
      conditionResult = condition;
    } else if (condition is num) {
      conditionResult = condition != 0;
    } else {
      conditionResult = condition.toString().toLowerCase() == 'true';
    }
    
    return conditionResult ? trueValue : falseValue;
  }

  dynamic _vlookup(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified VLOOKUP implementation
    if (args.length < 3) return '#N/A';
    
    final lookupValue = args[0];
    // In a real implementation, this would handle table arrays
    return lookupValue; // Placeholder
  }

  dynamic _index(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified INDEX implementation
    if (args.length < 2) return '#N/A';
    return args[0]; // Placeholder
  }

  dynamic _match(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified MATCH implementation
    if (args.length < 2) return '#N/A';
    return 1; // Placeholder
  }

  double _round(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    final digits = args.length > 1 ? (int.tryParse(args[1].toString()) ?? 0) : 0;
    final factor = math.pow(10, digits);
    return (number * factor).round() / factor;
  }

  double _abs(List<dynamic> args) {
    if (args.isEmpty) return 0;
    return (double.tryParse(args[0].toString()) ?? 0).abs();
  }

  double _sqrt(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return number >= 0 ? math.sqrt(number) : double.nan;
  }

  double _power(List<dynamic> args) {
    if (args.length < 2) return 0;
    final base = double.tryParse(args[0].toString()) ?? 0;
    final exponent = double.tryParse(args[1].toString()) ?? 0;
    return math.pow(base, exponent).toDouble();
  }

  double _mod(List<dynamic> args) {
    if (args.length < 2) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    final divisor = double.tryParse(args[1].toString()) ?? 1;
    return divisor != 0 ? number % divisor : double.nan;
  }

  bool _and(List<dynamic> args) {
    return args.every((arg) {
      if (arg is bool) return arg;
      if (arg is num) return arg != 0;
      return arg.toString().toLowerCase() == 'true';
    });
  }

  bool _or(List<dynamic> args) {
    return args.any((arg) {
      if (arg is bool) return arg;
      if (arg is num) return arg != 0;
      return arg.toString().toLowerCase() == 'true';
    });
  }

  bool _not(List<dynamic> args) {
    if (args.isEmpty) return true;
    final arg = args[0];
    if (arg is bool) return !arg;
    if (arg is num) return arg == 0;
    return arg.toString().toLowerCase() != 'true';
  }

  // Math & Trigonometry Functions - Extended
  double _product(List<dynamic> args) {
    return args.fold(1.0, (product, arg) => product * (double.tryParse(arg.toString()) ?? 1));
  }

  double _roundUp(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    final digits = args.length > 1 ? (int.tryParse(args[1].toString()) ?? 0) : 0;
    final factor = math.pow(10, digits);
    return (number * factor).ceil() / factor;
  }

  double _roundDown(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    final digits = args.length > 1 ? (int.tryParse(args[1].toString()) ?? 0) : 0;
    final factor = math.pow(10, digits);
    return (number * factor).floor() / factor;
  }

  int _int(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return number.floor();
  }

  double _exp(List<dynamic> args) {
    if (args.isEmpty) return 1;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return math.exp(number);
  }

  double _log(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 1;
    final base = args.length > 1 ? (double.tryParse(args[1].toString()) ?? 10) : 10;
    return math.log(number) / math.log(base);
  }

  double _ln(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 1;
    return math.log(number);
  }

  double _pi() {
    return math.pi;
  }

  double _rand() {
    return math.Random().nextDouble();
  }

  double _randBetween(List<dynamic> args) {
    if (args.length < 2) return 0;
    final min = int.tryParse(args[0].toString()) ?? 0;
    final max = int.tryParse(args[1].toString()) ?? 1;
    return (math.Random().nextInt(max - min + 1) + min).toDouble();
  }

  double _sin(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return math.sin(number);
  }

  double _cos(List<dynamic> args) {
    if (args.isEmpty) return 1;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return math.cos(number);
  }

  double _tan(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return math.tan(number);
  }

  double _asin(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return math.asin(number.clamp(-1, 1));
  }

  double _acos(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return math.acos(number.clamp(-1, 1));
  }

  double _atan(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = double.tryParse(args[0].toString()) ?? 0;
    return math.atan(number);
  }

  double _degrees(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final radians = double.tryParse(args[0].toString()) ?? 0;
    return radians * 180 / math.pi;
  }

  double _radians(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final degrees = double.tryParse(args[0].toString()) ?? 0;
    return degrees * math.pi / 180;
  }

  // Logical Functions - Extended
  dynamic _ifs(List<dynamic> args) {
    if (args.length < 2) return '';

    for (int i = 0; i < args.length - 1; i += 2) {
      final condition = args[i];
      final value = args[i + 1];

      bool conditionResult = false;
      if (condition is bool) {
        conditionResult = condition;
      } else if (condition is num) {
        conditionResult = condition != 0;
      } else {
        conditionResult = condition.toString().toLowerCase() == 'true';
      }

      if (conditionResult) return value;
    }

    return args.length % 2 == 1 ? args.last : '';
  }

  dynamic _ifError(List<dynamic> args) {
    if (args.length < 2) return '';

    try {
      final value = args[0];
      if (value.toString().startsWith('#')) {
        return args[1];
      }
      return value;
    } catch (e) {
      return args[1];
    }
  }

  dynamic _ifNA(List<dynamic> args) {
    if (args.length < 2) return '';

    final value = args[0];
    if (value.toString() == '#N/A') {
      return args[1];
    }
    return value;
  }

  dynamic _switch(List<dynamic> args) {
    if (args.length < 3) return '';

    final expression = args[0];

    for (int i = 1; i < args.length - 1; i += 2) {
      if (args[i].toString() == expression.toString()) {
        return args[i + 1];
      }
    }

    return args.length % 2 == 0 ? args.last : '';
  }

  bool _xor(List<dynamic> args) {
    int trueCount = 0;

    for (final arg in args) {
      bool value = false;
      if (arg is bool) {
        value = arg;
      } else if (arg is num) {
        value = arg != 0;
      } else {
        value = arg.toString().toLowerCase() == 'true';
      }

      if (value) trueCount++;
    }

    return trueCount % 2 == 1;
  }

  // Lookup & Reference Functions - Extended
  dynamic _hlookup(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified HLOOKUP implementation
    if (args.length < 3) return '#N/A';
    return args[0]; // Placeholder
  }

  dynamic _xlookup(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified XLOOKUP implementation
    if (args.length < 3) return '#N/A';
    return args[0]; // Placeholder
  }

  dynamic _offset(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified OFFSET implementation
    if (args.length < 3) return '#REF!';
    return args[0]; // Placeholder
  }

  dynamic _choose(List<dynamic> args) {
    if (args.length < 2) return '';
    final index = int.tryParse(args[0].toString()) ?? 1;
    if (index < 1 || index >= args.length) return '#VALUE!';
    return args[index];
  }

  dynamic _lookup(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified LOOKUP implementation
    if (args.length < 2) return '#N/A';
    return args[0]; // Placeholder
  }

  // Date & Time Functions
  DateTime _today() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  DateTime _now() {
    return DateTime.now();
  }

  int _year(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().year;
    final dateStr = args[0].toString();
    final date = DateTime.tryParse(dateStr);
    return date?.year ?? DateTime.now().year;
  }

  int _month(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().month;
    final dateStr = args[0].toString();
    final date = DateTime.tryParse(dateStr);
    return date?.month ?? DateTime.now().month;
  }

  int _day(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().day;
    final dateStr = args[0].toString();
    final date = DateTime.tryParse(dateStr);
    return date?.day ?? DateTime.now().day;
  }

  int _weekday(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().weekday;
    final dateStr = args[0].toString();
    final date = DateTime.tryParse(dateStr);
    return date?.weekday ?? DateTime.now().weekday;
  }

  int _hour(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().hour;
    final dateStr = args[0].toString();
    final date = DateTime.tryParse(dateStr);
    return date?.hour ?? DateTime.now().hour;
  }

  int _minute(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().minute;
    final dateStr = args[0].toString();
    final date = DateTime.tryParse(dateStr);
    return date?.minute ?? DateTime.now().minute;
  }

  int _second(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().second;
    final dateStr = args[0].toString();
    final date = DateTime.tryParse(dateStr);
    return date?.second ?? DateTime.now().second;
  }

  DateTime _date(List<dynamic> args) {
    if (args.length < 3) return DateTime.now();
    final year = int.tryParse(args[0].toString()) ?? DateTime.now().year;
    final month = int.tryParse(args[1].toString()) ?? DateTime.now().month;
    final day = int.tryParse(args[2].toString()) ?? DateTime.now().day;
    return DateTime(year, month, day);
  }

  DateTime _time(List<dynamic> args) {
    if (args.length < 3) return DateTime.now();
    final hour = int.tryParse(args[0].toString()) ?? 0;
    final minute = int.tryParse(args[1].toString()) ?? 0;
    final second = int.tryParse(args[2].toString()) ?? 0;
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, hour, minute, second);
  }

  // Text Functions
  String _left(List<dynamic> args) {
    if (args.isEmpty) return '';
    final text = args[0].toString();
    final numChars = args.length > 1 ? (int.tryParse(args[1].toString()) ?? 1) : 1;
    return text.length >= numChars ? text.substring(0, numChars) : text;
  }

  String _right(List<dynamic> args) {
    if (args.isEmpty) return '';
    final text = args[0].toString();
    final numChars = args.length > 1 ? (int.tryParse(args[1].toString()) ?? 1) : 1;
    return text.length >= numChars ? text.substring(text.length - numChars) : text;
  }

  String _mid(List<dynamic> args) {
    if (args.length < 2) return '';
    final text = args[0].toString();
    final start = (int.tryParse(args[1].toString()) ?? 1) - 1; // Convert to 0-based
    final length = args.length > 2 ? (int.tryParse(args[2].toString()) ?? text.length) : text.length;

    if (start < 0 || start >= text.length) return '';
    final end = (start + length).clamp(0, text.length);
    return text.substring(start, end);
  }

  int _len(List<dynamic> args) {
    if (args.isEmpty) return 0;
    return args[0].toString().length;
  }

  int _find(List<dynamic> args) {
    if (args.length < 2) return 0;
    final findText = args[0].toString();
    final withinText = args[1].toString();
    final startNum = args.length > 2 ? (int.tryParse(args[2].toString()) ?? 1) - 1 : 0;

    final index = withinText.indexOf(findText, startNum);
    return index >= 0 ? index + 1 : 0; // Convert to 1-based
  }

  int _search(List<dynamic> args) {
    if (args.length < 2) return 0;
    final findText = args[0].toString().toLowerCase();
    final withinText = args[1].toString().toLowerCase();
    final startNum = args.length > 2 ? (int.tryParse(args[2].toString()) ?? 1) - 1 : 0;

    final index = withinText.indexOf(findText, startNum);
    return index >= 0 ? index + 1 : 0; // Convert to 1-based
  }

  String _substitute(List<dynamic> args) {
    if (args.length < 3) return '';
    final text = args[0].toString();
    final oldText = args[1].toString();
    final newText = args[2].toString();
    final instanceNum = args.length > 3 ? (int.tryParse(args[3].toString()) ?? 0) : 0;

    if (instanceNum == 0) {
      return text.replaceAll(oldText, newText);
    } else {
      // Replace only the specified instance
      int count = 0;
      String result = text;
      int index = 0;

      while ((index = result.indexOf(oldText, index)) >= 0) {
        count++;
        if (count == instanceNum) {
          result = result.substring(0, index) + newText + result.substring(index + oldText.length);
          break;
        }
        index += oldText.length;
      }

      return result;
    }
  }

  String _replace(List<dynamic> args) {
    if (args.length < 4) return '';
    final oldText = args[0].toString();
    final startNum = (int.tryParse(args[1].toString()) ?? 1) - 1; // Convert to 0-based
    final numChars = int.tryParse(args[2].toString()) ?? 0;
    final newText = args[3].toString();

    if (startNum < 0 || startNum >= oldText.length) return oldText;
    final end = (startNum + numChars).clamp(0, oldText.length);

    return oldText.substring(0, startNum) + newText + oldText.substring(end);
  }

  String _upper(List<dynamic> args) {
    if (args.isEmpty) return '';
    return args[0].toString().toUpperCase();
  }

  String _lower(List<dynamic> args) {
    if (args.isEmpty) return '';
    return args[0].toString().toLowerCase();
  }

  String _proper(List<dynamic> args) {
    if (args.isEmpty) return '';
    final text = args[0].toString();
    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  String _trim(List<dynamic> args) {
    if (args.isEmpty) return '';
    return args[0].toString().trim();
  }

  String _concat(List<dynamic> args) {
    return args.map((arg) => arg.toString()).join('');
  }

  double _value(List<dynamic> args) {
    if (args.isEmpty) return 0;
    return double.tryParse(args[0].toString()) ?? 0;
  }

  // Statistical Functions
  double _averageIf(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified AVERAGEIF implementation
    if (args.length < 2) return 0;

    final values = <double>[];

    // In a real implementation, this would evaluate the range and criteria
    for (final arg in args) {
      final value = double.tryParse(arg.toString());
      if (value != null) values.add(value);
    }

    return values.isEmpty ? 0 : values.reduce((a, b) => a + b) / values.length;
  }

  int _countA(List<dynamic> args) {
    return args.where((arg) => arg.toString().isNotEmpty).length;
  }

  int _countIfs(List<dynamic> args, Map<String, SpreadsheetCell> cells) {
    // Simplified COUNTIFS implementation
    if (args.length < 2) return 0;

    int count = 0;
    for (final arg in args) {
      if (arg.toString().isNotEmpty) count++;
    }

    return count;
  }

  double _median(List<dynamic> args) {
    final numbers = args
        .map((arg) => double.tryParse(arg.toString()))
        .where((value) => value != null)
        .cast<double>()
        .toList();

    if (numbers.isEmpty) return 0;

    numbers.sort();
    final middle = numbers.length ~/ 2;

    if (numbers.length % 2 == 0) {
      return (numbers[middle - 1] + numbers[middle]) / 2;
    } else {
      return numbers[middle];
    }
  }

  double _stdev(List<dynamic> args) {
    final numbers = args
        .map((arg) => double.tryParse(arg.toString()))
        .where((value) => value != null)
        .cast<double>()
        .toList();

    if (numbers.length < 2) return 0;

    final mean = numbers.reduce((a, b) => a + b) / numbers.length;
    final variance = numbers
        .map((value) => math.pow(value - mean, 2))
        .reduce((a, b) => a + b) / (numbers.length - 1);

    return math.sqrt(variance);
  }

  double _var(List<dynamic> args) {
    final numbers = args
        .map((arg) => double.tryParse(arg.toString()))
        .where((value) => value != null)
        .cast<double>()
        .toList();

    if (numbers.length < 2) return 0;

    final mean = numbers.reduce((a, b) => a + b) / numbers.length;
    return numbers
        .map((value) => math.pow(value - mean, 2))
        .reduce((a, b) => a + b) / (numbers.length - 1);
  }

  // Financial Functions
  double _pmt(List<dynamic> args) {
    if (args.length < 3) return 0;
    final rate = double.tryParse(args[0].toString()) ?? 0;
    final nper = double.tryParse(args[1].toString()) ?? 0;
    final pv = double.tryParse(args[2].toString()) ?? 0;
    final fv = args.length > 3 ? (double.tryParse(args[3].toString()) ?? 0) : 0;
    final type = args.length > 4 ? (int.tryParse(args[4].toString()) ?? 0) : 0;

    if (rate == 0) return -(pv + fv) / nper;

    final pvif = math.pow(1 + rate, nper);
    final pmt = -(pv * pvif + fv) / ((pvif - 1) / rate * (1 + rate * type));

    return pmt;
  }

  double _fv(List<dynamic> args) {
    if (args.length < 3) return 0;
    final rate = double.tryParse(args[0].toString()) ?? 0;
    final nper = double.tryParse(args[1].toString()) ?? 0;
    final pmt = double.tryParse(args[2].toString()) ?? 0;
    final pv = args.length > 3 ? (double.tryParse(args[3].toString()) ?? 0) : 0;
    final type = args.length > 4 ? (int.tryParse(args[4].toString()) ?? 0) : 0;

    if (rate == 0) return -(pv + pmt * nper);

    final pvif = math.pow(1 + rate, nper);
    return -(pv * pvif + pmt * (pvif - 1) / rate * (1 + rate * type));
  }

  double _npv(List<dynamic> args) {
    if (args.length < 2) return 0;
    final rate = double.tryParse(args[0].toString()) ?? 0;

    double npv = 0;
    for (int i = 1; i < args.length; i++) {
      final cashFlow = double.tryParse(args[i].toString()) ?? 0;
      npv += cashFlow / math.pow(1 + rate, i);
    }

    return npv;
  }

  double _irr(List<dynamic> args) {
    // Simplified IRR implementation using Newton-Raphson method
    if (args.isEmpty) return 0;

    final cashFlows = args
        .map((arg) => double.tryParse(arg.toString()) ?? 0)
        .toList();

    double rate = 0.1; // Initial guess
    const maxIterations = 100;
    const tolerance = 1e-6;

    for (int i = 0; i < maxIterations; i++) {
      double npv = 0;
      double dnpv = 0;

      for (int j = 0; j < cashFlows.length; j++) {
        final factor = math.pow(1 + rate, j);
        npv += cashFlows[j] / factor;
        dnpv -= j * cashFlows[j] / (factor * (1 + rate));
      }

      if (dnpv.abs() < tolerance) break;

      final newRate = rate - npv / dnpv;
      if ((newRate - rate).abs() < tolerance) break;

      rate = newRate;
    }

    return rate;
  }

  double _rate(List<dynamic> args) {
    if (args.length < 3) return 0;
    final nper = double.tryParse(args[0].toString()) ?? 0;
    final pmt = double.tryParse(args[1].toString()) ?? 0;
    final pv = double.tryParse(args[2].toString()) ?? 0;
    final fv = args.length > 3 ? (double.tryParse(args[3].toString()) ?? 0) : 0;
    final type = args.length > 4 ? (int.tryParse(args[4].toString()) ?? 0) : 0;

    // Simplified RATE calculation using iterative method
    double rate = 0.1; // Initial guess
    const maxIterations = 100;
    const tolerance = 1e-6;

    for (int i = 0; i < maxIterations; i++) {
      final pvif = math.pow(1 + rate, nper);
      final f = pv * pvif + pmt * (pvif - 1) / rate * (1 + rate * type) + fv;

      if (f.abs() < tolerance) break;

      final df = pv * nper * math.pow(1 + rate, nper - 1) +
                 pmt * ((pvif - 1) / (rate * rate) * (1 + rate * type) -
                       (pvif - 1) / rate * type +
                       nper * math.pow(1 + rate, nper - 1) / rate * (1 + rate * type));

      if (df.abs() < tolerance) break;

      rate = rate - f / df;
    }

    return rate;
  }
}
