import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Formula assistance state
class FormulaAssistanceState {
  final List<FunctionInfo> availableFunctions;
  final List<FunctionInfo> recentlyUsed;
  final Map<String, String> aliases;
  final List<FormulaTemplate> templates;
  final String? currentFormula;
  final List<String> formulaHistory;

  const FormulaAssistanceState({
    this.availableFunctions = const [],
    this.recentlyUsed = const [],
    this.aliases = const {},
    this.templates = const [],
    this.currentFormula,
    this.formulaHistory = const [],
  });

  FormulaAssistanceState copyWith({
    List<FunctionInfo>? availableFunctions,
    List<FunctionInfo>? recentlyUsed,
    Map<String, String>? aliases,
    List<FormulaTemplate>? templates,
    String? currentFormula,
    List<String>? formulaHistory,
  }) {
    return FormulaAssistanceState(
      availableFunctions: availableFunctions ?? this.availableFunctions,
      recentlyUsed: recentlyUsed ?? this.recentlyUsed,
      aliases: aliases ?? this.aliases,
      templates: templates ?? this.templates,
      currentFormula: currentFormula ?? this.currentFormula,
      formulaHistory: formulaHistory ?? this.formulaHistory,
    );
  }
}

/// Formula assistance notifier
class FormulaAssistanceNotifier extends StateNotifier<FormulaAssistanceState> {
  FormulaAssistanceNotifier() : super(const FormulaAssistanceState()) {
    _initializeFunctions();
  }

  void _initializeFunctions() {
    final functions = [
      // Math & Trigonometry
      FunctionInfo('SUM', 'SUM(range)', 'Adds all numbers in a range', FunctionCategory.math, ['TOTAL']),
      FunctionInfo('AVERAGE', 'AVERAGE(range)', 'Calculates the average of numbers', FunctionCategory.math, ['AVG', 'MEAN']),
      FunctionInfo('MIN', 'MIN(range)', 'Returns the minimum value', FunctionCategory.math, ['MINIMUM']),
      FunctionInfo('MAX', 'MAX(range)', 'Returns the maximum value', FunctionCategory.math, ['MAXIMUM']),
      FunctionInfo('COUNT', 'COUNT(range)', 'Counts cells containing numbers', FunctionCategory.math, []),
      FunctionInfo('PRODUCT', 'PRODUCT(range)', 'Multiplies all numbers in a range', FunctionCategory.math, ['MULTIPLY']),
      FunctionInfo('ROUND', 'ROUND(number, digits)', 'Rounds a number to specified digits', FunctionCategory.math, []),
      FunctionInfo('ROUNDUP', 'ROUNDUP(number, digits)', 'Rounds a number up', FunctionCategory.math, ['CEILING']),
      FunctionInfo('ROUNDDOWN', 'ROUNDDOWN(number, digits)', 'Rounds a number down', FunctionCategory.math, ['FLOOR']),
      FunctionInfo('ABS', 'ABS(number)', 'Returns absolute value', FunctionCategory.math, ['ABSOLUTE']),
      FunctionInfo('SQRT', 'SQRT(number)', 'Returns square root', FunctionCategory.math, []),
      FunctionInfo('POWER', 'POWER(base, exponent)', 'Raises number to a power', FunctionCategory.math, ['POW']),
      FunctionInfo('EXP', 'EXP(number)', 'Returns e raised to the power of number', FunctionCategory.math, []),
      FunctionInfo('LOG', 'LOG(number, base)', 'Returns logarithm of number', FunctionCategory.math, []),
      FunctionInfo('LN', 'LN(number)', 'Returns natural logarithm', FunctionCategory.math, []),
      FunctionInfo('PI', 'PI()', 'Returns the value of π', FunctionCategory.math, []),
      FunctionInfo('RAND', 'RAND()', 'Returns a random number between 0 and 1', FunctionCategory.math, ['RANDOM']),
      FunctionInfo('RANDBETWEEN', 'RANDBETWEEN(min, max)', 'Returns random integer between min and max', FunctionCategory.math, []),
      FunctionInfo('SIN', 'SIN(angle)', 'Returns sine of angle in radians', FunctionCategory.math, []),
      FunctionInfo('COS', 'COS(angle)', 'Returns cosine of angle in radians', FunctionCategory.math, []),
      FunctionInfo('TAN', 'TAN(angle)', 'Returns tangent of angle in radians', FunctionCategory.math, []),
      FunctionInfo('DEGREES', 'DEGREES(radians)', 'Converts radians to degrees', FunctionCategory.math, []),
      FunctionInfo('RADIANS', 'RADIANS(degrees)', 'Converts degrees to radians', FunctionCategory.math, []),

      // Logical Functions
      FunctionInfo('IF', 'IF(condition, true_value, false_value)', 'Returns value based on condition', FunctionCategory.logical, []),
      FunctionInfo('IFS', 'IFS(condition1, value1, condition2, value2, ...)', 'Multiple IF conditions', FunctionCategory.logical, []),
      FunctionInfo('AND', 'AND(condition1, condition2, ...)', 'Returns TRUE if all conditions are true', FunctionCategory.logical, []),
      FunctionInfo('OR', 'OR(condition1, condition2, ...)', 'Returns TRUE if any condition is true', FunctionCategory.logical, []),
      FunctionInfo('NOT', 'NOT(condition)', 'Reverses the logic of a condition', FunctionCategory.logical, []),
      FunctionInfo('XOR', 'XOR(condition1, condition2, ...)', 'Returns TRUE if odd number of conditions are true', FunctionCategory.logical, []),
      FunctionInfo('IFERROR', 'IFERROR(value, value_if_error)', 'Returns value if no error, otherwise returns alternative', FunctionCategory.logical, []),
      FunctionInfo('SWITCH', 'SWITCH(expression, value1, result1, value2, result2, ...)', 'Evaluates expression against list of values', FunctionCategory.logical, []),

      // Lookup & Reference
      FunctionInfo('VLOOKUP', 'VLOOKUP(lookup_value, table_array, col_index, exact_match)', 'Vertical lookup', FunctionCategory.lookup, []),
      FunctionInfo('HLOOKUP', 'HLOOKUP(lookup_value, table_array, row_index, exact_match)', 'Horizontal lookup', FunctionCategory.lookup, []),
      FunctionInfo('INDEX', 'INDEX(array, row, column)', 'Returns value at specified position', FunctionCategory.lookup, []),
      FunctionInfo('MATCH', 'MATCH(lookup_value, lookup_array, match_type)', 'Returns position of value', FunctionCategory.lookup, []),
      FunctionInfo('CHOOSE', 'CHOOSE(index, value1, value2, ...)', 'Returns value from list based on index', FunctionCategory.lookup, []),

      // Text Functions
      FunctionInfo('LEFT', 'LEFT(text, num_chars)', 'Returns leftmost characters', FunctionCategory.text, []),
      FunctionInfo('RIGHT', 'RIGHT(text, num_chars)', 'Returns rightmost characters', FunctionCategory.text, []),
      FunctionInfo('MID', 'MID(text, start, length)', 'Returns middle characters', FunctionCategory.text, ['SUBSTR']),
      FunctionInfo('LEN', 'LEN(text)', 'Returns length of text', FunctionCategory.text, ['LENGTH']),
      FunctionInfo('FIND', 'FIND(find_text, within_text, start_num)', 'Finds text (case sensitive)', FunctionCategory.text, []),
      FunctionInfo('SEARCH', 'SEARCH(find_text, within_text, start_num)', 'Finds text (case insensitive)', FunctionCategory.text, []),
      FunctionInfo('SUBSTITUTE', 'SUBSTITUTE(text, old_text, new_text, instance)', 'Substitutes text', FunctionCategory.text, ['REPLACE_TEXT']),
      FunctionInfo('UPPER', 'UPPER(text)', 'Converts to uppercase', FunctionCategory.text, ['UPPERCASE']),
      FunctionInfo('LOWER', 'LOWER(text)', 'Converts to lowercase', FunctionCategory.text, ['LOWERCASE']),
      FunctionInfo('PROPER', 'PROPER(text)', 'Converts to proper case', FunctionCategory.text, ['TITLE']),
      FunctionInfo('TRIM', 'TRIM(text)', 'Removes extra spaces', FunctionCategory.text, []),
      FunctionInfo('CONCAT', 'CONCAT(text1, text2, ...)', 'Concatenates text', FunctionCategory.text, ['CONCATENATE']),
      FunctionInfo('VALUE', 'VALUE(text)', 'Converts text to number', FunctionCategory.text, []),

      // Date & Time
      FunctionInfo('TODAY', 'TODAY()', 'Returns current date', FunctionCategory.dateTime, []),
      FunctionInfo('NOW', 'NOW()', 'Returns current date and time', FunctionCategory.dateTime, []),
      FunctionInfo('YEAR', 'YEAR(date)', 'Returns year from date', FunctionCategory.dateTime, []),
      FunctionInfo('MONTH', 'MONTH(date)', 'Returns month from date', FunctionCategory.dateTime, []),
      FunctionInfo('DAY', 'DAY(date)', 'Returns day from date', FunctionCategory.dateTime, []),
      FunctionInfo('WEEKDAY', 'WEEKDAY(date)', 'Returns day of week', FunctionCategory.dateTime, []),
      FunctionInfo('HOUR', 'HOUR(time)', 'Returns hour from time', FunctionCategory.dateTime, []),
      FunctionInfo('MINUTE', 'MINUTE(time)', 'Returns minute from time', FunctionCategory.dateTime, []),
      FunctionInfo('SECOND', 'SECOND(time)', 'Returns second from time', FunctionCategory.dateTime, []),
      FunctionInfo('DATE', 'DATE(year, month, day)', 'Creates date from components', FunctionCategory.dateTime, []),
      FunctionInfo('TIME', 'TIME(hour, minute, second)', 'Creates time from components', FunctionCategory.dateTime, []),

      // Statistical
      FunctionInfo('MEDIAN', 'MEDIAN(range)', 'Returns median value', FunctionCategory.statistical, []),
      FunctionInfo('STDEV', 'STDEV(range)', 'Returns standard deviation', FunctionCategory.statistical, ['STDDEV']),
      FunctionInfo('VAR', 'VAR(range)', 'Returns variance', FunctionCategory.statistical, ['VARIANCE']),
      FunctionInfo('COUNTA', 'COUNTA(range)', 'Counts non-empty cells', FunctionCategory.statistical, []),
      FunctionInfo('AVERAGEIF', 'AVERAGEIF(range, criteria)', 'Average with condition', FunctionCategory.statistical, []),
      FunctionInfo('COUNTIFS', 'COUNTIFS(range1, criteria1, range2, criteria2, ...)', 'Count with multiple conditions', FunctionCategory.statistical, []),

      // Financial
      FunctionInfo('PMT', 'PMT(rate, nper, pv, fv, type)', 'Payment for loan', FunctionCategory.financial, ['PAYMENT']),
      FunctionInfo('FV', 'FV(rate, nper, pmt, pv, type)', 'Future value', FunctionCategory.financial, []),
      FunctionInfo('NPV', 'NPV(rate, value1, value2, ...)', 'Net present value', FunctionCategory.financial, []),
      FunctionInfo('IRR', 'IRR(values)', 'Internal rate of return', FunctionCategory.financial, []),
      FunctionInfo('RATE', 'RATE(nper, pmt, pv, fv, type)', 'Interest rate', FunctionCategory.financial, []),
    ];

    final templates = [
      FormulaTemplate('Basic Sum', '=SUM(A1:A10)', 'Sum a range of cells'),
      FormulaTemplate('Conditional Sum', '=SUMIF(A1:A10,">0")', 'Sum cells meeting criteria'),
      FormulaTemplate('Average Calculation', '=AVERAGE(A1:A10)', 'Calculate average of range'),
      FormulaTemplate('Percentage Calculation', '=(A1/B1)*100', 'Calculate percentage'),
      FormulaTemplate('Compound Interest', '=A1*(1+B1)^C1', 'Calculate compound interest'),
      FormulaTemplate('Loan Payment', '=PMT(B1/12,C1*12,-A1)', 'Calculate monthly loan payment'),
      FormulaTemplate('Grade Lookup', '=IF(A1>=90,"A",IF(A1>=80,"B",IF(A1>=70,"C","F")))', 'Assign letter grades'),
      FormulaTemplate('Date Difference', '=DATEDIF(A1,B1,"D")', 'Calculate days between dates'),
      FormulaTemplate('Text Extraction', '=LEFT(A1,FIND(" ",A1)-1)', 'Extract first word'),
      FormulaTemplate('Data Validation', '=IF(ISNUMBER(A1),A1,"Invalid")', 'Validate numeric input'),
    ];

    final aliases = <String, String>{
      'AVG': 'AVERAGE',
      'MEAN': 'AVERAGE',
      'TOTAL': 'SUM',
      'MULTIPLY': 'PRODUCT',
      'POW': 'POWER',
      'CEILING': 'ROUNDUP',
      'FLOOR': 'ROUNDDOWN',
      'ABSOLUTE': 'ABS',
      'RANDOM': 'RAND',
      'LENGTH': 'LEN',
      'SUBSTR': 'MID',
      'UPPERCASE': 'UPPER',
      'LOWERCASE': 'LOWER',
      'TITLE': 'PROPER',
      'CONCATENATE': 'CONCAT',
      'STDDEV': 'STDEV',
      'VARIANCE': 'VAR',
      'PAYMENT': 'PMT',
    };

    state = state.copyWith(
      availableFunctions: functions,
      templates: templates,
      aliases: aliases,
    );
  }

  /// Get function suggestions based on input
  List<FunctionInfo> getFunctionSuggestions(String input) {
    if (input.isEmpty) return state.recentlyUsed.take(10).toList();

    final query = input.toLowerCase();
    final suggestions = <FunctionInfo>[];

    // Exact name matches first
    suggestions.addAll(
      state.availableFunctions.where((f) => f.name.toLowerCase().startsWith(query))
    );

    // Alias matches
    for (final entry in state.aliases.entries) {
      if (entry.key.toLowerCase().startsWith(query)) {
        final function = state.availableFunctions.firstWhere(
          (f) => f.name == entry.value,
          orElse: () => FunctionInfo('', '', '', FunctionCategory.math, []),
        );
        if (function.name.isNotEmpty && !suggestions.contains(function)) {
          suggestions.add(function);
        }
      }
    }

    // Description matches
    suggestions.addAll(
      state.availableFunctions.where((f) => 
        !suggestions.contains(f) && 
        f.description.toLowerCase().contains(query)
      )
    );

    return suggestions.take(20).toList();
  }

  /// Get function by category
  List<FunctionInfo> getFunctionsByCategory(FunctionCategory category) {
    return state.availableFunctions.where((f) => f.category == category).toList();
  }

  /// Add function to recently used
  void addToRecentlyUsed(String functionName) {
    final function = state.availableFunctions.firstWhere(
      (f) => f.name == functionName,
      orElse: () => FunctionInfo('', '', '', FunctionCategory.math, []),
    );

    if (function.name.isEmpty) return;

    final recentlyUsed = List<FunctionInfo>.from(state.recentlyUsed);
    recentlyUsed.removeWhere((f) => f.name == functionName);
    recentlyUsed.insert(0, function);

    state = state.copyWith(
      recentlyUsed: recentlyUsed.take(20).toList(),
    );
  }

  /// Add formula to history
  void addToHistory(String formula) {
    final history = List<String>.from(state.formulaHistory);
    history.removeWhere((f) => f == formula);
    history.insert(0, formula);

    state = state.copyWith(
      formulaHistory: history.take(50).toList(),
    );
  }

  /// Convert natural language to formula
  String? convertNaturalLanguage(String query) {
    final lowerQuery = query.toLowerCase();

    // Simple pattern matching for common queries
    if (lowerQuery.contains('sum') || lowerQuery.contains('total')) {
      if (lowerQuery.contains('if') || lowerQuery.contains('condition')) {
        return '=SUMIF(range, criteria)';
      }
      return '=SUM(range)';
    }

    if (lowerQuery.contains('average') || lowerQuery.contains('mean')) {
      if (lowerQuery.contains('if') || lowerQuery.contains('condition')) {
        return '=AVERAGEIF(range, criteria)';
      }
      return '=AVERAGE(range)';
    }

    if (lowerQuery.contains('count')) {
      if (lowerQuery.contains('if') || lowerQuery.contains('condition')) {
        return '=COUNTIF(range, criteria)';
      }
      if (lowerQuery.contains('non-empty') || lowerQuery.contains('filled')) {
        return '=COUNTA(range)';
      }
      return '=COUNT(range)';
    }

    if (lowerQuery.contains('maximum') || lowerQuery.contains('largest')) {
      return '=MAX(range)';
    }

    if (lowerQuery.contains('minimum') || lowerQuery.contains('smallest')) {
      return '=MIN(range)';
    }

    if (lowerQuery.contains('percentage') || lowerQuery.contains('percent')) {
      return '=(value/total)*100';
    }

    if (lowerQuery.contains('loan') && lowerQuery.contains('payment')) {
      return '=PMT(rate/12, months, -principal)';
    }

    return null;
  }

  /// Get formula templates by category
  List<FormulaTemplate> getTemplatesByCategory(String category) {
    return state.templates.where((t) => 
      t.description.toLowerCase().contains(category.toLowerCase())
    ).toList();
  }
}

/// Function information
class FunctionInfo {
  final String name;
  final String syntax;
  final String description;
  final FunctionCategory category;
  final List<String> aliases;

  const FunctionInfo(this.name, this.syntax, this.description, this.category, this.aliases);

  @override
  bool operator ==(Object other) {
    return other is FunctionInfo && other.name == name;
  }

  @override
  int get hashCode => name.hashCode;
}

/// Function categories
enum FunctionCategory {
  math,
  logical,
  lookup,
  text,
  dateTime,
  statistical,
  financial,
}

/// Formula template
class FormulaTemplate {
  final String name;
  final String formula;
  final String description;

  const FormulaTemplate(this.name, this.formula, this.description);
}

/// Formula assistance provider
final formulaAssistanceProvider = StateNotifierProvider<FormulaAssistanceNotifier, FormulaAssistanceState>(
  (ref) => FormulaAssistanceNotifier(),
);
