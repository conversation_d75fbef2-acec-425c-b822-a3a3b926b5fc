import 'dart:io';
import 'dart:typed_data';
import 'package:excel/excel.dart' as excel_lib;
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../models/spreadsheet_cell.dart';
import '../models/excel_workbook.dart';
import '../models/excel_worksheet.dart';

/// Enhanced Excel service with live editing and manipulation capabilities
class EnhancedExcelService {
  static final EnhancedExcelService _instance = EnhancedExcelService._internal();
  factory EnhancedExcelService() => _instance;
  EnhancedExcelService._internal();

  excel_lib.Excel? _currentWorkbook;
  String? _currentFilePath;
  ExcelWorkbook? _loadedWorkbook;

  /// Open Excel file for live editing
  Future<ExcelWorkbook> openExcelFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        throw Exception('No file selected');
      }

      final file = result.files.first;
      final bytes = file.bytes;
      
      if (bytes == null) {
        throw Exception('Could not read file data');
      }

      // Save file temporarily for editing
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/${file.name}');
      await tempFile.writeAsBytes(bytes);
      _currentFilePath = tempFile.path;

      _loadedWorkbook = await _loadExcelWorkbook(bytes, file.name);
      return _loadedWorkbook!;
    } catch (e) {
      throw Exception('Failed to open Excel file: $e');
    }
  }

  /// Load Excel workbook with all worksheets
  Future<ExcelWorkbook> _loadExcelWorkbook(Uint8List bytes, String fileName) async {
    try {
      _currentWorkbook = excel_lib.Excel.decodeBytes(bytes);
      final worksheets = <ExcelWorksheet>[];
      
      for (final sheetName in _currentWorkbook!.tables.keys) {
        final sheet = _currentWorkbook!.tables[sheetName];
        if (sheet != null) {
          final worksheet = await _parseWorksheet(sheet, sheetName);
          worksheets.add(worksheet);
        }
      }

      return ExcelWorkbook(
        fileName: fileName,
        filePath: _currentFilePath ?? '',
        worksheets: worksheets,
        activeWorksheet: worksheets.isNotEmpty ? worksheets.first.name : '',
        metadata: {
          'fileSize': bytes.length,
          'sheetCount': worksheets.length,
          'loadedAt': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      throw Exception('Failed to load Excel workbook: $e');
    }
  }

  /// Parse individual worksheet
  Future<ExcelWorksheet> _parseWorksheet(excel_lib.Sheet sheet, String sheetName) async {
    final cells = <String, SpreadsheetCell>{};
    int maxRow = 0;
    int maxColumn = 0;

    for (final row in sheet.rows) {
      for (int colIndex = 0; colIndex < row.length; colIndex++) {
        final cell = row[colIndex];
        if (cell != null && cell.value != null) {
          final rowIndex = sheet.rows.indexOf(row) + 1;
          final colIndex1Based = colIndex + 1;
          final cellAddress = _getCellAddress(rowIndex, colIndex1Based);
          
          final spreadsheetCell = _convertExcelCell(cell, cellAddress);
          cells[cellAddress] = spreadsheetCell;
          
          if (rowIndex > maxRow) maxRow = rowIndex;
          if (colIndex1Based > maxColumn) maxColumn = colIndex1Based;
        }
      }
    }

    return ExcelWorksheet(
      name: sheetName,
      cells: cells,
      maxRow: maxRow,
      maxColumn: maxColumn,
      isVisible: true,
      metadata: {
        'originalSheetName': sheetName,
        'cellCount': cells.length,
        'parsedAt': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Convert Excel cell to SpreadsheetCell
  SpreadsheetCell _convertExcelCell(excel_lib.Data cell, String address) {
    final value = cell.value;
    String? formula;
    CellType type = CellType.text;

    // Determine cell type and extract formula if present
    if (value is excel_lib.FormulaCellValue) {
      formula = value.formula;
      type = CellType.formula;
    } else if (value is num) {
      type = CellType.number;
    } else if (value is bool) {
      type = CellType.boolean;
    } else if (value is DateTime) {
      type = CellType.text; // Use text for dates since CellType.date doesn't exist
    }

    return SpreadsheetCell(
      address: address,
      value: _extractCellValue(value),
      formula: formula,
      type: type,
    );
  }

  /// Extract actual value from Excel cell
  dynamic _extractCellValue(dynamic excelValue) {
    if (excelValue is excel_lib.FormulaCellValue) {
      return excelValue.toString(); // Use toString for formula values
    } else if (excelValue is excel_lib.TextCellValue) {
      return excelValue.value;
    } else if (excelValue is excel_lib.IntCellValue) {
      return excelValue.value;
    } else if (excelValue is excel_lib.DoubleCellValue) {
      return excelValue.value;
    } else if (excelValue is excel_lib.BoolCellValue) {
      return excelValue.value;
    } else if (excelValue is excel_lib.DateCellValue) {
      return excelValue.toString(); // Use toString for date values
    }
    return excelValue?.toString() ?? '';
  }

  /// Convert row/column coordinates to cell address
  String _getCellAddress(int row, int col) {
    String colStr = '';
    int tempCol = col;
    
    while (tempCol > 0) {
      tempCol--;
      colStr = String.fromCharCode(65 + (tempCol % 26)) + colStr;
      tempCol ~/= 26;
    }
    
    return '$colStr$row';
  }

  /// Update cell value in live workbook
  Future<void> updateCellValue(String worksheetName, String cellAddress, dynamic value) async {
    if (_currentWorkbook == null) {
      throw Exception('No workbook loaded');
    }

    final sheet = _currentWorkbook!.tables[worksheetName];
    if (sheet == null) {
      throw Exception('Worksheet not found: $worksheetName');
    }

    final coords = _parseCellAddress(cellAddress);
    final row = coords['row']! - 1; // Convert to 0-based
    final col = coords['col']! - 1; // Convert to 0-based

    // Update the Excel workbook
    sheet.updateCell(excel_lib.CellIndex.indexByColumnRow(columnIndex: col, rowIndex: row), value);

    // Update our loaded workbook model
    if (_loadedWorkbook != null) {
      final worksheet = _loadedWorkbook!.getWorksheet(worksheetName);
      if (worksheet != null) {
        final updatedCell = SpreadsheetCell(
          address: cellAddress,
          value: value,
          type: _determineCellType(value),
        );
        
        final updatedWorksheet = worksheet.setCell(cellAddress, updatedCell);
        final worksheetIndex = _loadedWorkbook!.worksheets.indexWhere((ws) => ws.name == worksheetName);
        if (worksheetIndex != -1) {
          final updatedWorksheets = List<ExcelWorksheet>.from(_loadedWorkbook!.worksheets);
          updatedWorksheets[worksheetIndex] = updatedWorksheet;
          _loadedWorkbook = _loadedWorkbook!.copyWith(worksheets: updatedWorksheets);
        }
      }
    }
  }

  /// Parse cell address to coordinates
  Map<String, int> _parseCellAddress(String address) {
    final regex = RegExp(r'^([A-Z]+)(\d+)$');
    final match = regex.firstMatch(address);
    
    if (match != null) {
      final colStr = match.group(1)!;
      final rowStr = match.group(2)!;
      
      int col = 0;
      for (int i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.codeUnitAt(i) - 65 + 1);
      }
      
      return {
        'row': int.parse(rowStr),
        'col': col,
      };
    }
    
    return {'row': 1, 'col': 1};
  }

  /// Determine cell type from value
  CellType _determineCellType(dynamic value) {
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    if (value is DateTime) return CellType.text; // Use text for dates
    if (value.toString().startsWith('=')) return CellType.formula;
    return CellType.text;
  }

  /// Save changes to Excel file
  Future<void> saveWorkbook() async {
    if (_currentWorkbook == null || _currentFilePath == null) {
      throw Exception('No workbook loaded or file path available');
    }

    try {
      final bytes = _currentWorkbook!.encode();
      if (bytes != null) {
        final file = File(_currentFilePath!);
        await file.writeAsBytes(bytes);
      }
    } catch (e) {
      throw Exception('Failed to save workbook: $e');
    }
  }

  /// Export workbook to new file
  Future<String> exportWorkbook(String fileName) async {
    if (_currentWorkbook == null) {
      throw Exception('No workbook loaded');
    }

    try {
      final bytes = _currentWorkbook!.encode();
      if (bytes == null) {
        throw Exception('Failed to encode workbook');
      }

      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      await file.writeAsBytes(bytes);
      
      return filePath;
    } catch (e) {
      throw Exception('Failed to export workbook: $e');
    }
  }

  /// Clone worksheet as template
  ExcelWorksheet cloneWorksheetAsTemplate(String worksheetName, String newName) {
    if (_loadedWorkbook == null) {
      throw Exception('No workbook loaded');
    }

    final originalWorksheet = _loadedWorkbook!.getWorksheet(worksheetName);
    if (originalWorksheet == null) {
      throw Exception('Worksheet not found: $worksheetName');
    }

    return originalWorksheet.copyWith(
      name: newName,
      metadata: {
        ...originalWorksheet.metadata,
        'clonedFrom': worksheetName,
        'clonedAt': DateTime.now().toIso8601String(),
        'isTemplate': true,
      },
    );
  }

  /// Get current loaded workbook
  ExcelWorkbook? get currentWorkbook => _loadedWorkbook;

  /// Check if workbook is loaded
  bool get hasLoadedWorkbook => _loadedWorkbook != null;

  /// Close current workbook
  void closeWorkbook() {
    _currentWorkbook = null;
    _currentFilePath = null;
    _loadedWorkbook = null;
  }
}
