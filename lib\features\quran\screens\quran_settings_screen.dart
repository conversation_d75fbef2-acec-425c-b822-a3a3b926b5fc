import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/quran_settings.dart';
import '../providers/quran_settings_provider.dart';

/// Quran Reader settings screen
class QuranSettingsScreen extends ConsumerStatefulWidget {
  const QuranSettingsScreen({super.key});

  @override
  ConsumerState<QuranSettingsScreen> createState() => _QuranSettingsScreenState();
}

class _QuranSettingsScreenState extends ConsumerState<QuranSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(quranSettingsProvider);
    
    return StandardizedScaffold(
      title: 'Quran Reader Settings',
      currentRoute: '/quran/settings',
      showBackButton: true,
      actions: [
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.upload),
                title: Text('Import Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'reset',
              child: ListTile(
                leading: Icon(Icons.restore, color: Colors.red),
                title: Text('Reset to Defaults', style: TextStyle(color: Colors.red)),
                dense: true,
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.display_settings), text: 'Display'),
          Tab(icon: Icon(Icons.text_fields), text: 'Text'),
          Tab(icon: Icon(Icons.volume_up), text: 'Audio'),
          Tab(icon: Icon(Icons.bookmark), text: 'Bookmarks'),
          Tab(icon: Icon(Icons.download), text: 'Downloads'),
          Tab(icon: Icon(Icons.security), text: 'Privacy'),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDisplaySettings(settings),
          _buildTextSettings(settings),
          _buildAudioSettings(settings),
          _buildBookmarkSettings(settings),
          _buildDownloadSettings(settings),
          _buildPrivacySettings(settings),
        ],
      ),
    );
  }

  /// Build display settings tab
  Widget _buildDisplaySettings(QuranSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Reading Mode',
            subtitle: 'How the Quran is displayed',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Reading Mode
                ListTile(
                  leading: const Icon(Icons.book),
                  title: const Text('Reading Mode'),
                  subtitle: Text(_getReadingModeText(settings.readingMode)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showReadingModePicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Show Translation
                SwitchListTile(
                  secondary: const Icon(Icons.translate),
                  title: const Text('Show Translation'),
                  subtitle: const Text('Display translation below Arabic'),
                  value: settings.showTranslation,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateDisplaySettings(
                      showTranslation: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Show Transliteration
                SwitchListTile(
                  secondary: const Icon(Icons.text_fields),
                  title: const Text('Show Transliteration'),
                  subtitle: const Text('Display phonetic pronunciation'),
                  value: settings.showTransliteration,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateDisplaySettings(
                      showTransliteration: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Dark Mode
                SwitchListTile(
                  secondary: const Icon(Icons.dark_mode),
                  title: const Text('Dark Mode'),
                  subtitle: const Text('Use dark theme'),
                  value: settings.enableDarkMode,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateDisplaySettings(
                      enableDarkMode: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Night Mode
                SwitchListTile(
                  secondary: const Icon(Icons.nights_stay),
                  title: const Text('Night Mode'),
                  subtitle: const Text('Reduce eye strain in dark'),
                  value: settings.enableNightMode,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateDisplaySettings(
                      enableNightMode: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build text settings tab
  Widget _buildTextSettings(QuranSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Font Settings',
            subtitle: 'Customize text appearance',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Arabic Font
                ListTile(
                  leading: const Icon(Icons.font_download),
                  title: const Text('Arabic Font'),
                  subtitle: Text(_getArabicFontText(settings.arabicFont)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showArabicFontPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Arabic Font Size
                ListTile(
                  leading: const Icon(Icons.format_size),
                  title: const Text('Arabic Font Size'),
                  subtitle: Text('${settings.arabicFontSize.toInt()}pt'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.arabicFontSize,
                  min: 12.0,
                  max: 36.0,
                  divisions: 24,
                  label: '${settings.arabicFontSize.toInt()}pt',
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateFontSettings(
                      arabicFontSize: value,
                    );
                  },
                ),
                
                const Divider(),
                
                // Translation Font Size
                ListTile(
                  leading: const Icon(Icons.format_size),
                  title: const Text('Translation Font Size'),
                  subtitle: Text('${settings.translationFontSize.toInt()}pt'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.translationFontSize,
                  min: 10.0,
                  max: 24.0,
                  divisions: 14,
                  label: '${settings.translationFontSize.toInt()}pt',
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateFontSettings(
                      translationFontSize: value,
                    );
                  },
                ),
              ],
            ),
          ),
          
          SizedBox(height: ResponsiveSpacing.lg(context)),
          
          SectionHeader(
            title: 'Tajweed Settings',
            subtitle: 'Quranic recitation rules',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Tajweed
                SwitchListTile(
                  secondary: const Icon(Icons.palette),
                  title: const Text('Tajweed Highlighting'),
                  subtitle: const Text('Color-code Tajweed rules'),
                  value: settings.enableTajweedHighlighting,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateTajweedSettings(
                      enableHighlighting: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                if (settings.enableTajweedHighlighting) ...[
                  const Divider(),
                  
                  // Tajweed Style
                  ListTile(
                    leading: const Icon(Icons.style),
                    title: const Text('Tajweed Style'),
                    subtitle: Text(_getTajweedStyleText(settings.tajweedStyle)),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _showTajweedStylePicker(settings),
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build audio settings tab
  Widget _buildAudioSettings(QuranSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Recitation Settings',
            subtitle: 'Audio playback options',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Selected Reciter
                ListTile(
                  leading: const Icon(Icons.person),
                  title: const Text('Reciter'),
                  subtitle: Text(settings.selectedReciter),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showReciterPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Recitation Speed
                ListTile(
                  leading: const Icon(Icons.speed),
                  title: const Text('Recitation Speed'),
                  subtitle: Text(_getRecitationSpeedText(settings.recitationSpeed)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showRecitationSpeedPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Audio Highlighting
                SwitchListTile(
                  secondary: const Icon(Icons.highlight),
                  title: const Text('Audio Highlighting'),
                  subtitle: const Text('Highlight verses during playback'),
                  value: settings.enableAudioHighlighting,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateAudioSettings(
                      audioHighlighting: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Auto Play
                SwitchListTile(
                  secondary: const Icon(Icons.play_arrow),
                  title: const Text('Auto Play'),
                  subtitle: const Text('Automatically play next verse'),
                  value: settings.enableAutoPlay,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateAudioSettings(
                      autoPlay: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build bookmark settings tab
  Widget _buildBookmarkSettings(QuranSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Bookmark Settings',
            subtitle: 'Manage your saved verses',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Bookmarks
                SwitchListTile(
                  secondary: const Icon(Icons.bookmark),
                  title: const Text('Enable Bookmarks'),
                  subtitle: const Text('Save verses for later reading'),
                  value: settings.enableBookmarks,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateBookmarkSettings(
                      enableBookmarks: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Auto Bookmark Last Read
                SwitchListTile(
                  secondary: const Icon(Icons.auto_awesome),
                  title: const Text('Auto Bookmark Last Read'),
                  subtitle: const Text('Automatically bookmark where you stopped'),
                  value: settings.autoBookmarkLastRead,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateBookmarkSettings(
                      autoBookmarkLastRead: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Enable Notes
                SwitchListTile(
                  secondary: const Icon(Icons.note),
                  title: const Text('Bookmark Notes'),
                  subtitle: const Text('Add notes to your bookmarks'),
                  value: settings.enableBookmarkNotes,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateBookmarkSettings(
                      enableNotes: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build download settings tab
  Widget _buildDownloadSettings(QuranSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Offline Downloads',
            subtitle: 'Manage offline content',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Enable Offline Mode
                SwitchListTile(
                  secondary: const Icon(Icons.offline_bolt),
                  title: const Text('Offline Mode'),
                  subtitle: const Text('Use downloaded content when offline'),
                  value: settings.enableOfflineMode,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateOfflineSettings(
                      offlineMode: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Download Quality
                ListTile(
                  leading: const Icon(Icons.high_quality),
                  title: const Text('Download Quality'),
                  subtitle: Text(_getDownloadQualityText(settings.downloadQuality)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showDownloadQualityPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // WiFi Only Downloads
                SwitchListTile(
                  secondary: const Icon(Icons.wifi),
                  title: const Text('WiFi Only Downloads'),
                  subtitle: const Text('Only download on WiFi connection'),
                  value: settings.downloadOnWifiOnly,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updateOfflineSettings(
                      wifiOnly: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build privacy settings tab
  Widget _buildPrivacySettings(QuranSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Privacy & Security',
            subtitle: 'Protect your reading data',
          ),
          
          StandardCard(
            child: Column(
              children: [
                // Privacy Mode
                SwitchListTile(
                  secondary: const Icon(Icons.privacy_tip),
                  title: const Text('Privacy Mode'),
                  subtitle: const Text('Hide content from screenshots'),
                  value: settings.enablePrivacyMode,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updatePrivacySettings(
                      privacyMode: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Require PIN
                SwitchListTile(
                  secondary: const Icon(Icons.lock),
                  title: const Text('Require PIN'),
                  subtitle: const Text('PIN required to access Quran'),
                  value: settings.requirePinForAccess,
                  onChanged: (value) {
                    if (value) {
                      _setupPin(settings);
                    } else {
                      ref.read(quranSettingsProvider.notifier).updatePrivacySettings(
                        requirePin: false,
                      );
                    }
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                const Divider(),
                
                // Biometric Auth
                SwitchListTile(
                  secondary: const Icon(Icons.fingerprint),
                  title: const Text('Biometric Authentication'),
                  subtitle: const Text('Use fingerprint/face unlock'),
                  value: settings.enableBiometricAuth,
                  onChanged: (value) {
                    ref.read(quranSettingsProvider.notifier).updatePrivacySettings(
                      biometric: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportSettings();
        break;
      case 'import':
        _importSettings();
        break;
      case 'reset':
        _resetSettings();
        break;
    }
  }

  /// Export settings
  void _exportSettings() {
    ref.read(quranSettingsProvider.notifier).exportSettings();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings exported successfully')),
    );
  }

  /// Import settings
  void _importSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality will be implemented')),
    );
  }

  /// Reset settings
  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(quranSettingsProvider.notifier).resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to defaults')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  /// Get reading mode text
  String _getReadingModeText(QuranReadingMode mode) {
    switch (mode) {
      case QuranReadingMode.page:
        return 'Page View';
      case QuranReadingMode.verse:
        return 'Verse by Verse';
      case QuranReadingMode.continuous:
        return 'Continuous Scroll';
      case QuranReadingMode.mushaf:
        return 'Mushaf Style';
    }
  }

  /// Get Arabic font text
  String _getArabicFontText(ArabicFont font) {
    switch (font) {
      case ArabicFont.uthmanic:
        return 'Uthmanic';
      case ArabicFont.naskh:
        return 'Naskh';
      case ArabicFont.kufi:
        return 'Kufi';
      case ArabicFont.thuluth:
        return 'Thuluth';
      case ArabicFont.nastaliq:
        return 'Nastaliq';
      case ArabicFont.amiri:
        return 'Amiri';
      case ArabicFont.scheherazade:
        return 'Scheherazade';
      case ArabicFont.lateef:
        return 'Lateef';
    }
  }

  /// Get Tajweed style text
  String _getTajweedStyleText(TajweedStyle style) {
    switch (style) {
      case TajweedStyle.none:
        return 'None';
      case TajweedStyle.basic:
        return 'Basic';
      case TajweedStyle.advanced:
        return 'Advanced';
      case TajweedStyle.colorCoded:
        return 'Color Coded';
      case TajweedStyle.detailed:
        return 'Detailed';
    }
  }

  /// Get recitation speed text
  String _getRecitationSpeedText(RecitationSpeed speed) {
    switch (speed) {
      case RecitationSpeed.slow:
        return 'Slow';
      case RecitationSpeed.normal:
        return 'Normal';
      case RecitationSpeed.fast:
        return 'Fast';
    }
  }

  /// Get download quality text
  String _getDownloadQualityText(DownloadQuality quality) {
    switch (quality) {
      case DownloadQuality.low:
        return 'Low (32 kbps)';
      case DownloadQuality.medium:
        return 'Medium (64 kbps)';
      case DownloadQuality.high:
        return 'High (128 kbps)';
      case DownloadQuality.lossless:
        return 'Lossless (FLAC)';
    }
  }

  /// Show reading mode picker
  void _showReadingModePicker(QuranSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reading Mode'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: QuranReadingMode.values.map((mode) {
            return ListTile(
              title: Text(_getReadingModeText(mode)),
              trailing: settings.readingMode == mode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(quranSettingsProvider.notifier).updateReadingMode(mode);
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show Arabic font picker
  void _showArabicFontPicker(QuranSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Arabic Font'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: ArabicFont.values.length,
            itemBuilder: (context, index) {
              final font = ArabicFont.values[index];
              return ListTile(
                title: Text(_getArabicFontText(font)),
                trailing: settings.arabicFont == font
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  ref.read(quranSettingsProvider.notifier).updateFontSettings(
                    arabicFont: font,
                  );
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// Show Tajweed style picker
  void _showTajweedStylePicker(QuranSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tajweed Style'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TajweedStyle.values.map((style) {
            return ListTile(
              title: Text(_getTajweedStyleText(style)),
              trailing: settings.tajweedStyle == style
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(quranSettingsProvider.notifier).updateTajweedSettings(
                  style: style,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show reciter picker
  void _showReciterPicker(QuranSettings settings) {
    final reciters = [
      'Abdul Rahman Al-Sudais',
      'Mishary Rashid Alafasy',
      'Saad Al-Ghamdi',
      'Abdul Basit Abdul Samad',
      'Maher Al-Muaiqly',
      'Ahmed Al-Ajmy',
      'Yasser Al-Dosari',
      'Khalid Al-Jalil',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Reciter'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: reciters.length,
            itemBuilder: (context, index) {
              final reciter = reciters[index];
              return ListTile(
                title: Text(reciter),
                trailing: settings.selectedReciter == reciter
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  ref.read(quranSettingsProvider.notifier).updateAudioSettings(
                    selectedReciter: reciter,
                  );
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// Show recitation speed picker
  void _showRecitationSpeedPicker(QuranSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Recitation Speed'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: RecitationSpeed.values.map((speed) {
            return ListTile(
              title: Text(_getRecitationSpeedText(speed)),
              trailing: settings.recitationSpeed == speed
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(quranSettingsProvider.notifier).updateAudioSettings(
                  speed: speed,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show download quality picker
  void _showDownloadQualityPicker(QuranSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: DownloadQuality.values.map((quality) {
            return ListTile(
              title: Text(_getDownloadQualityText(quality)),
              trailing: settings.downloadQuality == quality
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(quranSettingsProvider.notifier).updateOfflineSettings(
                  downloadQuality: quality,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Setup PIN
  void _setupPin(QuranSettings settings) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('PIN setup will be implemented')),
    );
  }
}
