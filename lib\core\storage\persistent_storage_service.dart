import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistentStorageService {
  static final PersistentStorageService _instance = PersistentStorageService._internal();
  factory PersistentStorageService() => _instance;
  PersistentStorageService._internal();

  static const String _notesKey = 'notes_data';
  static const String _todosKey = 'todos_data';
  static const String _voiceMemosKey = 'voice_memos_data';
  static const String _dhikrRoutinesKey = 'dhikr_routines_data';
  static const String _dhikrSessionsKey = 'dhikr_sessions_data';
  static const String _userProfileKey = 'user_profile_data';
  static const String _settingsKey = 'app_settings_data';
  static const String _customToolsKey = 'custom_tools_data';
  static const String _calculationHistoryKey = 'calculation_history_data';
  static const String _accountsKey = 'accounts_data';
  static const String _transactionsKey = 'transactions_data';
  static const String _categoriesKey = 'categories_data';
  static const String _budgetsKey = 'budgets_data';
  static const String _financialGoalsKey = 'financial_goals_data';

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      debugPrint('PersistentStorageService initialized');
    } catch (e) {
      debugPrint('Error initializing PersistentStorageService: $e');
      rethrow;
    }
  }

  // Generic save/load methods
  Future<void> saveData(String key, List<Map<String, dynamic>> data) async {
    if (!_isInitialized || _prefs == null) {
      throw Exception('Storage service not initialized');
    }

    try {
      final jsonString = json.encode(data);
      await _prefs!.setString(key, jsonString);
      debugPrint('Saved ${data.length} items to $key');
    } catch (e) {
      debugPrint('Error saving data to $key: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> loadData(String key) async {
    if (!_isInitialized || _prefs == null) {
      throw Exception('Storage service not initialized');
    }

    try {
      final jsonString = _prefs!.getString(key);
      if (jsonString == null || jsonString.isEmpty) {
        return [];
      }

      final List<dynamic> jsonList = json.decode(jsonString);
      final result = jsonList.cast<Map<String, dynamic>>();
      debugPrint('Loaded ${result.length} items from $key');
      return result;
    } catch (e) {
      debugPrint('Error loading data from $key: $e');
      return [];
    }
  }

  Future<void> saveObject(String key, Map<String, dynamic> data) async {
    if (!_isInitialized || _prefs == null) {
      throw Exception('Storage service not initialized');
    }

    try {
      final jsonString = json.encode(data);
      await _prefs!.setString(key, jsonString);
      debugPrint('Saved object to $key');
    } catch (e) {
      debugPrint('Error saving object to $key: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> loadObject(String key) async {
    if (!_isInitialized || _prefs == null) {
      throw Exception('Storage service not initialized');
    }

    try {
      final jsonString = _prefs!.getString(key);
      if (jsonString == null || jsonString.isEmpty) {
        return null;
      }

      final result = json.decode(jsonString) as Map<String, dynamic>;
      debugPrint('Loaded object from $key');
      return result;
    } catch (e) {
      debugPrint('Error loading object from $key: $e');
      return null;
    }
  }

  // Specific data type methods
  Future<void> saveNotes(List<Map<String, dynamic>> notes) async {
    await saveData(_notesKey, notes);
  }

  Future<List<Map<String, dynamic>>> loadNotes() async {
    return await loadData(_notesKey);
  }

  Future<void> saveTodos(List<Map<String, dynamic>> todos) async {
    await saveData(_todosKey, todos);
  }

  Future<List<Map<String, dynamic>>> loadTodos() async {
    return await loadData(_todosKey);
  }

  Future<void> saveVoiceMemos(List<Map<String, dynamic>> voiceMemos) async {
    await saveData(_voiceMemosKey, voiceMemos);
  }

  Future<List<Map<String, dynamic>>> loadVoiceMemos() async {
    return await loadData(_voiceMemosKey);
  }

  Future<void> saveDhikrRoutines(List<Map<String, dynamic>> routines) async {
    await saveData(_dhikrRoutinesKey, routines);
  }

  Future<List<Map<String, dynamic>>> loadDhikrRoutines() async {
    return await loadData(_dhikrRoutinesKey);
  }

  Future<void> saveDhikrSessions(List<Map<String, dynamic>> sessions) async {
    await saveData(_dhikrSessionsKey, sessions);
  }

  Future<List<Map<String, dynamic>>> loadDhikrSessions() async {
    return await loadData(_dhikrSessionsKey);
  }

  Future<void> saveUserProfile(Map<String, dynamic> profile) async {
    await saveObject(_userProfileKey, profile);
  }

  Future<Map<String, dynamic>?> loadUserProfile() async {
    return await loadObject(_userProfileKey);
  }

  Future<void> saveSettings(Map<String, dynamic> settings) async {
    await saveObject(_settingsKey, settings);
  }

  Future<Map<String, dynamic>?> loadSettings() async {
    return await loadObject(_settingsKey);
  }

  Future<void> saveCustomTools(List<Map<String, dynamic>> tools) async {
    await saveData(_customToolsKey, tools);
  }

  Future<List<Map<String, dynamic>>> loadCustomTools() async {
    return await loadData(_customToolsKey);
  }

  Future<void> saveAccounts(List<Map<String, dynamic>> accounts) async {
    await saveData(_accountsKey, accounts);
  }

  Future<List<Map<String, dynamic>>> loadAccounts() async {
    return await loadData(_accountsKey);
  }

  Future<void> saveTransactions(List<Map<String, dynamic>> transactions) async {
    await saveData(_transactionsKey, transactions);
  }

  Future<List<Map<String, dynamic>>> loadTransactions() async {
    return await loadData(_transactionsKey);
  }

  Future<void> saveCategories(List<Map<String, dynamic>> categories) async {
    await saveData(_categoriesKey, categories);
  }

  Future<List<Map<String, dynamic>>> loadCategories() async {
    return await loadData(_categoriesKey);
  }

  Future<void> saveBudgets(List<Map<String, dynamic>> budgets) async {
    await saveData(_budgetsKey, budgets);
  }

  Future<List<Map<String, dynamic>>> loadBudgets() async {
    return await loadData(_budgetsKey);
  }

  Future<void> saveCalculationHistory(List<Map<String, dynamic>> history) async {
    await saveData(_calculationHistoryKey, history);
  }

  Future<List<Map<String, dynamic>>> loadCalculationHistory() async {
    return await loadData(_calculationHistoryKey);
  }

  Future<void> saveFinancialGoals(List<Map<String, dynamic>> goals) async {
    await saveData(_financialGoalsKey, goals);
  }

  Future<List<Map<String, dynamic>>> loadFinancialGoals() async {
    return await loadData(_financialGoalsKey);
  }

  // Utility methods
  Future<void> clearAllData() async {
    if (!_isInitialized || _prefs == null) return;

    try {
      await _prefs!.clear();
      debugPrint('Cleared all persistent data');
    } catch (e) {
      debugPrint('Error clearing data: $e');
    }
  }

  Future<void> deleteData(String key) async {
    if (!_isInitialized || _prefs == null) return;

    try {
      await _prefs!.remove(key);
      debugPrint('Deleted data for key: $key');
    } catch (e) {
      debugPrint('Error deleting data for $key: $e');
    }
  }

  Future<bool> hasData(String key) async {
    if (!_isInitialized || _prefs == null) return false;
    return _prefs!.containsKey(key);
  }

  Future<Set<String>> getAllKeys() async {
    if (!_isInitialized || _prefs == null) return {};
    return _prefs!.getKeys();
  }

  // Backup and restore
  Future<Map<String, dynamic>> exportAllData() async {
    if (!_isInitialized || _prefs == null) return {};

    try {
      final allKeys = await getAllKeys();
      final exportData = <String, dynamic>{};

      for (final key in allKeys) {
        final value = _prefs!.get(key);
        if (value != null) {
          exportData[key] = value;
        }
      }

      debugPrint('Exported ${exportData.length} data entries');
      return exportData;
    } catch (e) {
      debugPrint('Error exporting data: $e');
      return {};
    }
  }

  Future<void> importAllData(Map<String, dynamic> data) async {
    if (!_isInitialized || _prefs == null) return;

    try {
      for (final entry in data.entries) {
        if (entry.value is String) {
          await _prefs!.setString(entry.key, entry.value);
        } else if (entry.value is int) {
          await _prefs!.setInt(entry.key, entry.value);
        } else if (entry.value is double) {
          await _prefs!.setDouble(entry.key, entry.value);
        } else if (entry.value is bool) {
          await _prefs!.setBool(entry.key, entry.value);
        } else if (entry.value is List<String>) {
          await _prefs!.setStringList(entry.key, entry.value);
        }
      }

      debugPrint('Imported ${data.length} data entries');
    } catch (e) {
      debugPrint('Error importing data: $e');
      rethrow;
    }
  }

  // Data validation and migration
  Future<void> validateAndMigrateData() async {
    if (!_isInitialized) return;

    try {
      // Check if we need to migrate any old data formats
      final allKeys = await getAllKeys();
      
      for (final key in allKeys) {
        if (key.endsWith('_data')) {
          // Validate JSON structure
          final data = await loadData(key);
          if (data.isEmpty) continue;
          
          // Basic validation - ensure each item has required fields
          bool needsMigration = false;
          for (final item in data) {
            if (!item.containsKey('id') || !item.containsKey('createdAt')) {
              needsMigration = true;
              break;
            }
          }
          
          if (needsMigration) {
            debugPrint('Migrating data for key: $key');
            await _migrateDataFormat(key, data);
          }
        }
      }
    } catch (e) {
      debugPrint('Error during data validation/migration: $e');
    }
  }

  Future<void> _migrateDataFormat(String key, List<Map<String, dynamic>> data) async {
    try {
      final migratedData = <Map<String, dynamic>>[];
      
      for (int i = 0; i < data.length; i++) {
        final item = Map<String, dynamic>.from(data[i]);
        
        // Ensure ID exists
        if (!item.containsKey('id')) {
          item['id'] = 'migrated_${DateTime.now().millisecondsSinceEpoch}_$i';
        }
        
        // Ensure timestamps exist
        if (!item.containsKey('createdAt')) {
          item['createdAt'] = DateTime.now().toIso8601String();
        }
        
        if (!item.containsKey('modifiedAt')) {
          item['modifiedAt'] = DateTime.now().toIso8601String();
        }
        
        migratedData.add(item);
      }
      
      await saveData(key, migratedData);
      debugPrint('Successfully migrated ${migratedData.length} items for $key');
    } catch (e) {
      debugPrint('Error migrating data for $key: $e');
    }
  }
}
