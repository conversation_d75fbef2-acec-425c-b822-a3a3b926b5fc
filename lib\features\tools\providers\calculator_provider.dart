import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/calculator.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Calculator list provider
final calculatorsProvider = StateNotifierProvider<CalculatorNotifier, List<Calculator>>((ref) {
  return CalculatorNotifier(ref);
});

class CalculatorNotifier extends StateNotifier<List<Calculator>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  CalculatorNotifier(this.ref) : super([]) {
    loadCalculators();
  }

  Future<void> loadCalculators() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final calculators = await _db.getAllCalculators(userId: userProfile.id.toString());
      state = calculators;
    }
  }

  Future<void> addCalculator(Calculator calculator) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      calculator.userId = userProfile.id.toString();
      final savedCalculator = await _db.saveCalculator(calculator);
      state = [...state, savedCalculator];
    }
  }

  Future<void> updateCalculator(Calculator calculator) async {
    final savedCalculator = await _db.saveCalculator(calculator);
    state = state.map((c) => c.id == savedCalculator.id ? savedCalculator : c).toList();
  }

  Future<void> deleteCalculator(int calculatorId) async {
    await _db.deleteCalculator(calculatorId);
    state = state.where((c) => c.id != calculatorId).toList();
  }

  Future<void> incrementUsage(int calculatorId) async {
    final calculatorIndex = state.indexWhere((c) => c.id == calculatorId);
    if (calculatorIndex != -1) {
      final calculator = state[calculatorIndex];
      calculator.incrementUsage();
      await updateCalculator(calculator);
    }
  }

  Future<void> toggleFavorite(int calculatorId) async {
    final calculatorIndex = state.indexWhere((c) => c.id == calculatorId);
    if (calculatorIndex != -1) {
      final calculator = state[calculatorIndex];
      calculator.toggleFavorite();
      await updateCalculator(calculator);
    }
  }

  List<Calculator> getCalculatorsByType(CalculatorType type) {
    return state.where((calc) => calc.type == type).toList();
  }

  List<Calculator> getFavoriteCalculators() {
    return state.where((calc) => calc.isFavorite).toList()
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));
  }

  List<Calculator> getMostUsedCalculators({int limit = 5}) {
    final sorted = [...state]..sort((a, b) => b.usageCount.compareTo(a.usageCount));
    return sorted.take(limit).toList();
  }
}

// Favorite calculators provider
final favoriteCalculatorsProvider = Provider<List<Calculator>>((ref) {
  final calculators = ref.watch(calculatorsProvider);
  return calculators.where((calc) => calc.isFavorite).toList()
    ..sort((a, b) => b.usageCount.compareTo(a.usageCount));
});

// Calculators by type provider
final calculatorsByTypeProvider = Provider.family<List<Calculator>, CalculatorType>((ref, type) {
  final calculators = ref.watch(calculatorsProvider);
  return calculators.where((calc) => calc.type == type).toList();
});

// Calculator statistics provider
final calculatorStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final calculators = ref.watch(calculatorsProvider);
  
  final totalUsage = calculators.fold<int>(0, (sum, calc) => sum + calc.usageCount);
  final favoriteCount = calculators.where((calc) => calc.isFavorite).length;
  final customCount = calculators.where((calc) => calc.isCustom).length;
  
  final typeStats = <CalculatorType, int>{};
  for (final calc in calculators) {
    typeStats[calc.type] = (typeStats[calc.type] ?? 0) + 1;
  }
  
  return {
    'total': calculators.length,
    'favorites': favoriteCount,
    'custom': customCount,
    'totalUsage': totalUsage,
    'typeStats': typeStats,
    'mostUsed': calculators.isNotEmpty 
        ? calculators.reduce((a, b) => a.usageCount > b.usageCount ? a : b)
        : null,
  };
});

// Search calculators provider
final searchCalculatorsProvider = StateProvider<String>((ref) => '');

final filteredCalculatorsProvider = Provider<List<Calculator>>((ref) {
  final calculators = ref.watch(calculatorsProvider);
  final searchQuery = ref.watch(searchCalculatorsProvider);
  
  if (searchQuery.isEmpty) {
    return calculators;
  }
  
  final query = searchQuery.toLowerCase();
  return calculators.where((calc) {
    return calc.name.toLowerCase().contains(query) ||
           calc.description.toLowerCase().contains(query) ||
           calc.typeDisplayName.toLowerCase().contains(query);
  }).toList();
});
