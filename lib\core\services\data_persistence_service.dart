import 'package:flutter/foundation.dart';
import '../database/database_service.dart';
import '../../features/memo/models/note.dart';
import '../../features/memo/models/todo.dart';
import '../../features/memo/models/voice_memo.dart';
import '../../features/money/models/account.dart';
import '../../features/money/models/transaction.dart';
import '../../features/money/models/category.dart';

class DataPersistenceService {
  static final DataPersistenceService _instance = DataPersistenceService._internal();
  factory DataPersistenceService() => _instance;
  DataPersistenceService._internal();

  static DataPersistenceService get instance => _instance;

  final DatabaseService _db = DatabaseService.instance;

  /// Verify that all data types can be saved and loaded correctly
  Future<Map<String, bool>> verifyDataPersistence() async {
    final results = <String, bool>{};

    try {
      // Test Notes persistence
      results['notes'] = await _testNotesPersistence();
      
      // Test Todos persistence
      results['todos'] = await _testTodosPersistence();
      
      // Test Voice Memos persistence
      results['voiceMemos'] = await _testVoiceMemosPersistence();
      
      // Test Accounts persistence
      results['accounts'] = await _testAccountsPersistence();
      
      // Test Transactions persistence
      results['transactions'] = await _testTransactionsPersistence();
      
      // Test Categories persistence
      results['categories'] = await _testCategoriesPersistence();
      
      // Test User Profile persistence
      results['userProfile'] = await _testUserProfilePersistence();
      
    } catch (e) {
      debugPrint('Error during data persistence verification: $e');
    }

    return results;
  }

  Future<bool> _testNotesPersistence() async {
    try {
      // Create test note
      final testNote = Note.create(
        title: 'Test Note',
        content: 'This is a test note for persistence verification',
        userId: 'test_user',
      );

      // Save note
      final savedNote = await _db.saveNote(testNote);
      if (savedNote.id == 0) return false;

      // Load notes and verify
      final notes = await _db.getAllNotes(userId: 'test_user');
      final foundNote = notes.firstWhere(
        (n) => n.id == savedNote.id,
        orElse: () => Note(),
      );

      if (foundNote.id == 0) return false;
      if (foundNote.title != testNote.title) return false;
      if (foundNote.content != testNote.content) return false;

      // Clean up
      await _db.deleteNote(savedNote.id);
      
      return true;
    } catch (e) {
      debugPrint('Notes persistence test failed: $e');
      return false;
    }
  }

  Future<bool> _testTodosPersistence() async {
    try {
      // Create test todo
      final testTodo = Todo.create(
        title: 'Test Todo',
        description: 'This is a test todo for persistence verification',
        userId: 'test_user',
      );

      // Save todo
      final savedTodo = await _db.saveTodo(testTodo);
      if (savedTodo.id == 0) return false;

      // Load todos and verify
      final todos = await _db.getAllTodos(userId: 'test_user');
      final foundTodo = todos.firstWhere(
        (t) => t.id == savedTodo.id,
        orElse: () => Todo(),
      );

      if (foundTodo.id == 0) return false;
      if (foundTodo.title != testTodo.title) return false;
      if (foundTodo.description != testTodo.description) return false;

      // Test completion toggle
      foundTodo.complete();
      await _db.saveTodo(foundTodo);
      
      final updatedTodos = await _db.getAllTodos(userId: 'test_user');
      final updatedTodo = updatedTodos.firstWhere((t) => t.id == savedTodo.id);
      if (!updatedTodo.isCompleted) return false;

      // Clean up
      await _db.deleteTodo(savedTodo.id);
      
      return true;
    } catch (e) {
      debugPrint('Todos persistence test failed: $e');
      return false;
    }
  }

  Future<bool> _testVoiceMemosPersistence() async {
    try {
      // Create test voice memo
      final testMemo = VoiceMemo.create(
        title: 'Test Voice Memo',
        filePath: '/test/path/memo.aac',
        durationMs: 30000,
        fileSizeBytes: 1024.0,
        userId: 'test_user',
      );

      // Save voice memo
      final savedMemo = await _db.saveVoiceMemo(testMemo);
      if (savedMemo.id == 0) return false;

      // Load voice memos and verify
      final memos = await _db.getAllVoiceMemos(userId: 'test_user');
      final foundMemo = memos.firstWhere(
        (m) => m.id == savedMemo.id,
        orElse: () => VoiceMemo(),
      );

      if (foundMemo.id == 0) return false;
      if (foundMemo.title != testMemo.title) return false;
      if (foundMemo.filePath != testMemo.filePath) return false;
      if (foundMemo.durationMs != testMemo.durationMs) return false;

      // Clean up
      await _db.deleteVoiceMemo(savedMemo.id);
      
      return true;
    } catch (e) {
      debugPrint('Voice memos persistence test failed: $e');
      return false;
    }
  }

  Future<bool> _testAccountsPersistence() async {
    try {
      // Create test account
      final testAccount = Account.create(
        name: 'Test Account',
        type: AccountType.checking,
        balance: 1000.0,
        userId: 'test_user',
      );

      // Save account
      final savedAccount = await _db.saveAccount(testAccount);
      if (savedAccount.id == 0) return false;

      // Load accounts and verify
      final accounts = await _db.getAllAccounts(userId: 'test_user');
      final foundAccount = accounts.firstWhere(
        (a) => a.id == savedAccount.id,
        orElse: () => Account(),
      );

      if (foundAccount.id == 0) return false;
      if (foundAccount.name != testAccount.name) return false;
      if (foundAccount.type != testAccount.type) return false;
      if (foundAccount.balance != testAccount.balance) return false;

      // Clean up
      await _db.deleteAccount(savedAccount.id);
      
      return true;
    } catch (e) {
      debugPrint('Accounts persistence test failed: $e');
      return false;
    }
  }

  Future<bool> _testTransactionsPersistence() async {
    try {
      // Create test account first
      final testAccount = Account.create(
        name: 'Test Account for Transaction',
        type: AccountType.checking,
        balance: 1000.0,
        userId: 'test_user',
      );
      final savedAccount = await _db.saveAccount(testAccount);

      // Create test transaction
      final testTransaction = Transaction.create(
        description: 'Test Transaction',
        amount: 50.0,
        type: TransactionType.expense,
        category: 'Test Category',
        accountId: savedAccount.id,
        userId: 'test_user',
      );

      // Save transaction
      final savedTransaction = await _db.saveTransaction(testTransaction);
      if (savedTransaction.id == 0) return false;

      // Load transactions and verify
      final transactions = await _db.getAllTransactions(userId: 'test_user');
      final foundTransaction = transactions.firstWhere(
        (t) => t.id == savedTransaction.id,
        orElse: () => Transaction(),
      );

      if (foundTransaction.id == 0) return false;
      if (foundTransaction.description != testTransaction.description) return false;
      if (foundTransaction.amount != testTransaction.amount) return false;
      if (foundTransaction.type != testTransaction.type) return false;

      // Clean up
      await _db.deleteTransaction(savedTransaction.id);
      await _db.deleteAccount(savedAccount.id);
      
      return true;
    } catch (e) {
      debugPrint('Transactions persistence test failed: $e');
      return false;
    }
  }

  Future<bool> _testCategoriesPersistence() async {
    try {
      // Create test category
      final testCategory = MoneyCategory.create(
        name: 'Test Category',
        type: CategoryType.expense,
        userId: 'test_user',
        description: 'Test category for persistence verification',
      );

      // Save category
      final savedCategory = await _db.saveCategory(testCategory);
      if (savedCategory.id == 0) return false;

      // Load categories and verify
      final categories = await _db.getAllCategories(userId: 'test_user');
      final foundCategory = categories.firstWhere(
        (c) => c.id == savedCategory.id,
        orElse: () => MoneyCategory(),
      );

      if (foundCategory.id == 0) return false;
      if (foundCategory.name != testCategory.name) return false;
      if (foundCategory.type != testCategory.type) return false;
      if (foundCategory.description != testCategory.description) return false;

      // Clean up
      await _db.deleteCategory(savedCategory.id);
      
      return true;
    } catch (e) {
      debugPrint('Categories persistence test failed: $e');
      return false;
    }
  }

  Future<bool> _testUserProfilePersistence() async {
    try {
      // Create test user profile
      final testProfile = await _db.createUserProfile(
        name: 'Test User',
        email: '<EMAIL>',
      );

      if (testProfile.id == 0) return false;

      // Load user profile and verify
      final loadedProfile = await _db.getUserProfile();
      if (loadedProfile == null) return false;
      if (loadedProfile.name != testProfile.name) return false;
      if (loadedProfile.email != testProfile.email) return false;

      // Test profile update
      testProfile.updateProfile(name: 'Updated Test User');
      await _db.updateUserProfile(testProfile);

      final reloadedProfile = await _db.getUserProfile();
      if (reloadedProfile?.name != 'Updated Test User') return false;

      return true;
    } catch (e) {
      debugPrint('User profile persistence test failed: $e');
      return false;
    }
  }

  /// Generate a comprehensive persistence report
  Future<String> generatePersistenceReport() async {
    final results = await verifyDataPersistence();
    final buffer = StringBuffer();
    
    buffer.writeln('=== ShadowSuite Data Persistence Report ===');
    buffer.writeln('Generated: ${DateTime.now()}');
    buffer.writeln();
    
    int passedTests = 0;
    int totalTests = results.length;
    
    for (final entry in results.entries) {
      final status = entry.value ? '✅ PASS' : '❌ FAIL';
      buffer.writeln('${entry.key}: $status');
      if (entry.value) passedTests++;
    }
    
    buffer.writeln();
    buffer.writeln('Summary: $passedTests/$totalTests tests passed');
    
    if (passedTests == totalTests) {
      buffer.writeln('🎉 All data persistence tests passed!');
    } else {
      buffer.writeln('⚠️  Some data persistence tests failed. Check logs for details.');
    }
    
    return buffer.toString();
  }

  /// Quick health check for data persistence
  Future<bool> isDataPersistenceHealthy() async {
    final results = await verifyDataPersistence();
    return results.values.every((result) => result);
  }
}
