import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../models/user_profile.dart';
import '../../core/database/database_service.dart';
import '../../features/money/models/category.dart';

// User Profile Provider
final userProfileProvider = StateNotifierProvider<UserProfileNotifier, UserProfile?>((ref) {
  return UserProfileNotifier();
});

class UserProfileNotifier extends StateNotifier<UserProfile?> {
  UserProfileNotifier() : super(null) {
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    final profile = await DatabaseService.instance.getUserProfile();

    if (profile == null) {
      // Auto-create default user profile for immediate use
      await _createDefaultProfile();
    } else {
      state = profile;
    }
  }

  Future<void> _createDefaultProfile() async {
    final profile = await DatabaseService.instance.createUserProfile(
      name: 'ShadowSuite User',
      email: null,
    );

    // Initialize default categories for Money Flow
    await _initializeDefaultCategories(profile.id.toString());

    state = profile;
  }

  Future<void> _initializeDefaultCategories(String userId) async {
    try {
      // Check if categories already exist
      final existingCategories = await DatabaseService.instance.getAllCategories(userId: userId);

      if (existingCategories.isEmpty) {
        // Create default categories
        final defaultCategories = MoneyCategory.getDefaultCategories(userId);

        for (final category in defaultCategories) {
          await DatabaseService.instance.saveCategory(category);
        }
      }
    } catch (e) {
      // Ignore errors during category initialization
      debugPrint('Error initializing default categories: $e');
    }
  }

  Future<void> createProfile({
    required String name,
    String? email,
  }) async {
    final profile = await DatabaseService.instance.createUserProfile(
      name: name,
      email: email,
    );
    state = profile;
  }

  Future<void> updateProfile({
    String? name,
    String? avatarPath,
    String? languageCode,
    String? themeMode,
    bool? syncEnabled,
    bool? notificationsEnabled,
    String? notificationSound,
  }) async {
    if (state == null) return;

    state!.updateProfile(
      name: name,
      avatarPath: avatarPath,
      languageCode: languageCode,
      themeMode: themeMode,
      syncEnabled: syncEnabled,
      notificationsEnabled: notificationsEnabled,
      notificationSound: notificationSound,
    );

    await DatabaseService.instance.updateUserProfile(state!);
    state = state; // Trigger rebuild
  }

  Future<void> updateMemoSettings(Map<String, dynamic> settings) async {
    if (state == null) return;

    state!.updateMemoSettings(settings);
    await DatabaseService.instance.updateUserProfile(state!);
    state = state; // Trigger rebuild
  }

  Future<void> updateAthkarSettings(Map<String, dynamic> settings) async {
    if (state == null) return;

    state!.updateAthkarSettings(settings);
    await DatabaseService.instance.updateUserProfile(state!);
    state = state; // Trigger rebuild
  }

  Future<void> updateToolsSettings(Map<String, dynamic> settings) async {
    if (state == null) return;

    state!.updateToolsSettings(settings);
    await DatabaseService.instance.updateUserProfile(state!);
    state = state; // Trigger rebuild
  }

  Future<void> updateMoneySettings(Map<String, dynamic> settings) async {
    if (state == null) return;

    state!.updateMoneySettings(settings);
    await DatabaseService.instance.updateUserProfile(state!);
    state = state; // Trigger rebuild
  }

  String get userId => state?.id.toString() ?? '';
  bool get isLoggedIn => state != null;
  bool get syncEnabled => state?.syncEnabled ?? false;
  String get userName => state?.name ?? '';
  String get userEmail => state?.email ?? '';
  String get languageCode => state?.languageCode ?? 'en';
  String get themeMode => state?.themeMode ?? 'system';
  bool get notificationsEnabled => state?.notificationsEnabled ?? true;
}

// Sync Status Provider
final syncEnabledProvider = Provider<bool>((ref) {
  final userProfile = ref.watch(userProfileProvider);
  return userProfile?.syncEnabled ?? false;
});

// Theme Mode Provider
final themeModeProvider = Provider<String>((ref) {
  final userProfile = ref.watch(userProfileProvider);
  return userProfile?.themeMode ?? 'system';
});

// Language Provider
final languageProvider = Provider<String>((ref) {
  final userProfile = ref.watch(userProfileProvider);
  return userProfile?.languageCode ?? 'en';
});

// Notifications Provider
final notificationsEnabledProvider = Provider<bool>((ref) {
  final userProfile = ref.watch(userProfileProvider);
  return userProfile?.notificationsEnabled ?? true;
});
