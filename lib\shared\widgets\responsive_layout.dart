import 'package:flutter/material.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget body;
  final Widget? drawer;
  final PreferredSizeWidget? appBar;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? endDrawer;

  const ResponsiveLayout({
    super.key,
    required this.body,
    this.drawer,
    this.appBar,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.endDrawer,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200) {
          // Desktop layout with permanent navigation rail
          return _buildDesktopLayout(context);
        } else if (constraints.maxWidth >= 600) {
          // Tablet layout with collapsible drawer
          return _buildTabletLayout(context);
        } else {
          // Mobile layout with standard drawer
          return _buildMobileLayout(context);
        }
      },
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      body: Row(
        children: [
          if (drawer != null)
            SizedBox(
              width: 280,
              child: drawer!,
            ),
          Expanded(
            child: body,
          ),
        ],
      ),
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      endDrawer: endDrawer,
    );
  }

  Widget _buildTabletLayout(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      drawer: drawer,
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      endDrawer: endDrawer,
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      drawer: drawer,
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      endDrawer: endDrawer,
    );
  }
}

class ResponsiveBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1200;
  static const double desktop = 1200;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }

  static int getGridCrossAxisCount(BuildContext context, {
    int mobileCount = 1,
    int tabletCount = 2,
    int desktopCount = 3,
  }) {
    if (isDesktop(context)) return desktopCount;
    if (isTablet(context)) return tabletCount;
    return mobileCount;
  }

  static double getGridChildAspectRatio(BuildContext context, {
    double mobileRatio = 1.0,
    double tabletRatio = 1.2,
    double desktopRatio = 1.5,
  }) {
    if (isDesktop(context)) return desktopRatio;
    if (isTablet(context)) return tabletRatio;
    return mobileRatio;
  }

  static EdgeInsets getResponsivePadding(BuildContext context, {
    EdgeInsets mobilePadding = const EdgeInsets.all(16),
    EdgeInsets tabletPadding = const EdgeInsets.all(24),
    EdgeInsets desktopPadding = const EdgeInsets.all(32),
  }) {
    if (isDesktop(context)) return desktopPadding;
    if (isTablet(context)) return tabletPadding;
    return mobilePadding;
  }

  static double getResponsiveValue(BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    if (isDesktop(context)) return desktop;
    if (isTablet(context)) return tablet;
    return mobile;
  }
}

class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints) mobile;
  final Widget Function(BuildContext context, BoxConstraints constraints)? tablet;
  final Widget Function(BuildContext context, BoxConstraints constraints)? desktop;

  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= ResponsiveBreakpoints.desktop) {
          return desktop?.call(context, constraints) ?? 
                 tablet?.call(context, constraints) ?? 
                 mobile(context, constraints);
        } else if (constraints.maxWidth >= ResponsiveBreakpoints.mobile) {
          return tablet?.call(context, constraints) ?? mobile(context, constraints);
        } else {
          return mobile(context, constraints);
        }
      },
    );
  }
}

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 16,
    this.runSpacing = 16,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveBreakpoints.getGridCrossAxisCount(
      context,
      mobileCount: mobileColumns,
      tabletCount: tabletColumns,
      desktopCount: desktopColumns,
    );

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
        childAspectRatio: ResponsiveBreakpoints.getGridChildAspectRatio(context),
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// Enhanced responsive spacing utilities
class ResponsiveSpacing {
  static double xs(BuildContext context) => ResponsiveBreakpoints.getResponsiveValue(
    context,
    mobile: 4,
    tablet: 6,
    desktop: 8,
  );

  static double sm(BuildContext context) => ResponsiveBreakpoints.getResponsiveValue(
    context,
    mobile: 8,
    tablet: 12,
    desktop: 16,
  );

  static double md(BuildContext context) => ResponsiveBreakpoints.getResponsiveValue(
    context,
    mobile: 16,
    tablet: 20,
    desktop: 24,
  );

  static double lg(BuildContext context) => ResponsiveBreakpoints.getResponsiveValue(
    context,
    mobile: 24,
    tablet: 32,
    desktop: 40,
  );

  static double xl(BuildContext context) => ResponsiveBreakpoints.getResponsiveValue(
    context,
    mobile: 32,
    tablet: 48,
    desktop: 64,
  );
}

/// Screen size detection utilities
class ScreenSize {
  static bool isMobile(BuildContext context) => ResponsiveBreakpoints.isMobile(context);
  static bool isTablet(BuildContext context) => ResponsiveBreakpoints.isTablet(context);
  static bool isDesktop(BuildContext context) => ResponsiveBreakpoints.isDesktop(context);

  static bool shouldUseSidebar(BuildContext context) => isDesktop(context);
  static bool shouldUseBottomNavigation(BuildContext context) => isMobile(context);
  static bool shouldUseDrawer(BuildContext context) => isTablet(context);
}
