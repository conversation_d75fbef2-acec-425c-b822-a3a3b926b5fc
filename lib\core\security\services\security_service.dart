import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  static const String _encryptionKeyKey = 'encryption_key';
  static const String _securitySettingsKey = 'security_settings';
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _pinCodeKey = 'pin_code';
  static const String _lastActivityKey = 'last_activity';

  encrypt.Encrypter? _encrypter;
  encrypt.IV? _iv;
  SharedPreferences? _prefs;
  bool _isInitialized = false;
  SecuritySettings _settings = SecuritySettings();

  final StreamController<SecurityEvent> _securityEventController = 
      StreamController<SecurityEvent>.broadcast();

  // Getters
  Stream<SecurityEvent> get securityEventStream => _securityEventController.stream;
  SecuritySettings get settings => _settings;
  bool get isInitialized => _isInitialized;
  bool get isEncryptionEnabled => _encrypter != null;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      await _initializeEncryption();
      _isInitialized = true;
      debugPrint('SecurityService initialized');
    } catch (e) {
      debugPrint('Error initializing SecurityService: $e');
    }
  }

  // Encryption methods
  Future<void> enableEncryption([String? customKey]) async {
    try {
      final key = customKey ?? _generateSecureKey();
      final keyBytes = encrypt.Key.fromBase64(key);
      _encrypter = encrypt.Encrypter(encrypt.AES(keyBytes));
      _iv = encrypt.IV.fromSecureRandom(16);

      // Store encrypted key
      await _prefs?.setString(_encryptionKeyKey, key);
      
      _emitSecurityEvent(SecurityEvent.encryptionEnabled());
      debugPrint('Encryption enabled');
    } catch (e) {
      debugPrint('Error enabling encryption: $e');
      rethrow;
    }
  }

  Future<void> disableEncryption() async {
    try {
      _encrypter = null;
      _iv = null;
      await _prefs?.remove(_encryptionKeyKey);
      
      _emitSecurityEvent(SecurityEvent.encryptionDisabled());
      debugPrint('Encryption disabled');
    } catch (e) {
      debugPrint('Error disabling encryption: $e');
      rethrow;
    }
  }

  String encryptData(String data) {
    if (_encrypter == null || _iv == null) {
      throw Exception('Encryption not initialized');
    }

    try {
      final encrypted = _encrypter!.encrypt(data, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      debugPrint('Error encrypting data: $e');
      rethrow;
    }
  }

  String decryptData(String encryptedData) {
    if (_encrypter == null || _iv == null) {
      throw Exception('Encryption not initialized');
    }

    try {
      final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
      return _encrypter!.decrypt(encrypted, iv: _iv);
    } catch (e) {
      debugPrint('Error decrypting data: $e');
      rethrow;
    }
  }

  // Hash methods
  String hashData(String data, {String? salt}) {
    final saltBytes = salt != null ? utf8.encode(salt) : _generateSalt();
    final dataBytes = utf8.encode(data);
    final combined = [...dataBytes, ...saltBytes];
    
    final digest = sha256.convert(combined);
    return digest.toString();
  }

  String hashPassword(String password) {
    final salt = _generateSalt();
    final saltString = base64.encode(salt);
    final hash = hashData(password, salt: saltString);
    return '$saltString:$hash';
  }

  bool verifyPassword(String password, String hashedPassword) {
    try {
      final parts = hashedPassword.split(':');
      if (parts.length != 2) return false;
      
      final salt = parts[0];
      final hash = parts[1];
      final computedHash = hashData(password, salt: salt);
      
      return computedHash == hash;
    } catch (e) {
      debugPrint('Error verifying password: $e');
      return false;
    }
  }

  // PIN code methods
  Future<void> setPinCode(String pinCode) async {
    try {
      final hashedPin = hashPassword(pinCode);
      await _prefs?.setString(_pinCodeKey, hashedPin);
      
      final newSettings = _settings.copyWith(pinCodeEnabled: true);
      await _updateSettings(newSettings);
      
      _emitSecurityEvent(SecurityEvent.pinCodeSet());
      debugPrint('PIN code set');
    } catch (e) {
      debugPrint('Error setting PIN code: $e');
      rethrow;
    }
  }

  Future<bool> verifyPinCode(String pinCode) async {
    try {
      final hashedPin = _prefs?.getString(_pinCodeKey);
      if (hashedPin == null) return false;
      
      final isValid = verifyPassword(pinCode, hashedPin);
      
      if (isValid) {
        await _updateLastActivity();
        _emitSecurityEvent(SecurityEvent.pinCodeVerified());
      } else {
        _emitSecurityEvent(SecurityEvent.pinCodeFailed());
      }
      
      return isValid;
    } catch (e) {
      debugPrint('Error verifying PIN code: $e');
      return false;
    }
  }

  Future<void> removePinCode() async {
    try {
      await _prefs?.remove(_pinCodeKey);
      
      final newSettings = _settings.copyWith(pinCodeEnabled: false);
      await _updateSettings(newSettings);
      
      _emitSecurityEvent(SecurityEvent.pinCodeRemoved());
      debugPrint('PIN code removed');
    } catch (e) {
      debugPrint('Error removing PIN code: $e');
      rethrow;
    }
  }

  // Biometric authentication
  Future<void> enableBiometricAuth() async {
    try {
      await _prefs?.setBool(_biometricEnabledKey, true);
      
      final newSettings = _settings.copyWith(biometricEnabled: true);
      await _updateSettings(newSettings);
      
      _emitSecurityEvent(SecurityEvent.biometricEnabled());
      debugPrint('Biometric authentication enabled');
    } catch (e) {
      debugPrint('Error enabling biometric auth: $e');
      rethrow;
    }
  }

  Future<void> disableBiometricAuth() async {
    try {
      await _prefs?.setBool(_biometricEnabledKey, false);
      
      final newSettings = _settings.copyWith(biometricEnabled: false);
      await _updateSettings(newSettings);
      
      _emitSecurityEvent(SecurityEvent.biometricDisabled());
      debugPrint('Biometric authentication disabled');
    } catch (e) {
      debugPrint('Error disabling biometric auth: $e');
      rethrow;
    }
  }

  // Session management
  Future<void> updateLastActivity() async {
    await _updateLastActivity();
  }

  bool isSessionExpired() {
    if (!_settings.autoLockEnabled) return false;
    
    final lastActivity = _getLastActivity();
    if (lastActivity == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(lastActivity);
    
    return difference.inMinutes >= _settings.autoLockTimeoutMinutes;
  }

  Future<void> lockApp() async {
    _emitSecurityEvent(SecurityEvent.appLocked());
  }

  Future<void> unlockApp() async {
    await _updateLastActivity();
    _emitSecurityEvent(SecurityEvent.appUnlocked());
  }

  // Data sanitization
  String sanitizeInput(String input) {
    // Remove potentially dangerous characters
    return input
        .replaceAll(RegExp(r'[<>"' "']"), '')
        .replaceAll(RegExp(r'script', caseSensitive: false), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .trim();
  }

  Map<String, dynamic> sanitizeData(Map<String, dynamic> data) {
    final sanitized = <String, dynamic>{};
    
    for (final entry in data.entries) {
      final key = sanitizeInput(entry.key);
      final value = entry.value;
      
      if (value is String) {
        sanitized[key] = sanitizeInput(value);
      } else if (value is Map<String, dynamic>) {
        sanitized[key] = sanitizeData(value);
      } else if (value is List) {
        sanitized[key] = value.map((item) {
          if (item is String) {
            return sanitizeInput(item);
          } else if (item is Map<String, dynamic>) {
            return sanitizeData(item);
          }
          return item;
        }).toList();
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  // Secure random generation
  String generateSecureToken([int length = 32]) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (_) => random.nextInt(256));
    return base64.encode(bytes);
  }

  String generateSecurePassword([int length = 16]) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random.secure();
    return List.generate(length, (_) => chars[random.nextInt(chars.length)]).join();
  }

  // Settings management
  Future<void> updateSecuritySettings(SecuritySettings newSettings) async {
    await _updateSettings(newSettings);
  }

  // Private methods
  Future<void> _initializeEncryption() async {
    try {
      final storedKey = _prefs?.getString(_encryptionKeyKey);
      if (storedKey != null) {
        final keyBytes = encrypt.Key.fromBase64(storedKey);
        _encrypter = encrypt.Encrypter(encrypt.AES(keyBytes));
        _iv = encrypt.IV.fromSecureRandom(16);
      }
    } catch (e) {
      debugPrint('Error initializing encryption: $e');
    }
  }

  String _generateSecureKey() {
    final key = encrypt.Key.fromSecureRandom(32);
    return key.base64;
  }

  List<int> _generateSalt([int length = 16]) {
    final random = Random.secure();
    return List<int>.generate(length, (_) => random.nextInt(256));
  }

  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs?.getString(_securitySettingsKey);
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _settings = SecuritySettings.fromJson(settingsMap);
      }
    } catch (e) {
      debugPrint('Error loading security settings: $e');
    }
  }

  Future<void> _updateSettings(SecuritySettings newSettings) async {
    try {
      _settings = newSettings;
      final settingsJson = json.encode(_settings.toJson());
      await _prefs?.setString(_securitySettingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error updating security settings: $e');
    }
  }

  Future<void> _updateLastActivity() async {
    try {
      final now = DateTime.now().toIso8601String();
      await _prefs?.setString(_lastActivityKey, now);
    } catch (e) {
      debugPrint('Error updating last activity: $e');
    }
  }

  DateTime? _getLastActivity() {
    try {
      final lastActivityString = _prefs?.getString(_lastActivityKey);
      if (lastActivityString != null) {
        return DateTime.parse(lastActivityString);
      }
    } catch (e) {
      debugPrint('Error getting last activity: $e');
    }
    return null;
  }

  void _emitSecurityEvent(SecurityEvent event) {
    _securityEventController.add(event);
  }

  // Dispose resources
  void dispose() {
    _securityEventController.close();
  }
}

// Security settings model
class SecuritySettings {
  final bool encryptionEnabled;
  final bool pinCodeEnabled;
  final bool biometricEnabled;
  final bool autoLockEnabled;
  final int autoLockTimeoutMinutes;
  final bool dataEncryptionEnabled;
  final bool secureKeyboardEnabled;
  final bool screenshotProtectionEnabled;

  const SecuritySettings({
    this.encryptionEnabled = false,
    this.pinCodeEnabled = false,
    this.biometricEnabled = false,
    this.autoLockEnabled = true,
    this.autoLockTimeoutMinutes = 5,
    this.dataEncryptionEnabled = true,
    this.secureKeyboardEnabled = false,
    this.screenshotProtectionEnabled = false,
  });

  SecuritySettings copyWith({
    bool? encryptionEnabled,
    bool? pinCodeEnabled,
    bool? biometricEnabled,
    bool? autoLockEnabled,
    int? autoLockTimeoutMinutes,
    bool? dataEncryptionEnabled,
    bool? secureKeyboardEnabled,
    bool? screenshotProtectionEnabled,
  }) {
    return SecuritySettings(
      encryptionEnabled: encryptionEnabled ?? this.encryptionEnabled,
      pinCodeEnabled: pinCodeEnabled ?? this.pinCodeEnabled,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      autoLockEnabled: autoLockEnabled ?? this.autoLockEnabled,
      autoLockTimeoutMinutes: autoLockTimeoutMinutes ?? this.autoLockTimeoutMinutes,
      dataEncryptionEnabled: dataEncryptionEnabled ?? this.dataEncryptionEnabled,
      secureKeyboardEnabled: secureKeyboardEnabled ?? this.secureKeyboardEnabled,
      screenshotProtectionEnabled: screenshotProtectionEnabled ?? this.screenshotProtectionEnabled,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'encryptionEnabled': encryptionEnabled,
      'pinCodeEnabled': pinCodeEnabled,
      'biometricEnabled': biometricEnabled,
      'autoLockEnabled': autoLockEnabled,
      'autoLockTimeoutMinutes': autoLockTimeoutMinutes,
      'dataEncryptionEnabled': dataEncryptionEnabled,
      'secureKeyboardEnabled': secureKeyboardEnabled,
      'screenshotProtectionEnabled': screenshotProtectionEnabled,
    };
  }

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    return SecuritySettings(
      encryptionEnabled: json['encryptionEnabled'] ?? false,
      pinCodeEnabled: json['pinCodeEnabled'] ?? false,
      biometricEnabled: json['biometricEnabled'] ?? false,
      autoLockEnabled: json['autoLockEnabled'] ?? true,
      autoLockTimeoutMinutes: json['autoLockTimeoutMinutes'] ?? 5,
      dataEncryptionEnabled: json['dataEncryptionEnabled'] ?? true,
      secureKeyboardEnabled: json['secureKeyboardEnabled'] ?? false,
      screenshotProtectionEnabled: json['screenshotProtectionEnabled'] ?? false,
    );
  }
}

// Security event model
class SecurityEvent {
  final String type;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  SecurityEvent({
    required this.type,
    DateTime? timestamp,
    this.data,
  }) : timestamp = timestamp ?? DateTime.now();

  factory SecurityEvent.encryptionEnabled() {
    return SecurityEvent(type: 'encryption_enabled');
  }

  factory SecurityEvent.encryptionDisabled() {
    return SecurityEvent(type: 'encryption_disabled');
  }

  factory SecurityEvent.pinCodeSet() {
    return SecurityEvent(type: 'pin_code_set');
  }

  factory SecurityEvent.pinCodeVerified() {
    return SecurityEvent(type: 'pin_code_verified');
  }

  factory SecurityEvent.pinCodeFailed() {
    return SecurityEvent(type: 'pin_code_failed');
  }

  factory SecurityEvent.pinCodeRemoved() {
    return SecurityEvent(type: 'pin_code_removed');
  }

  factory SecurityEvent.biometricEnabled() {
    return SecurityEvent(type: 'biometric_enabled');
  }

  factory SecurityEvent.biometricDisabled() {
    return SecurityEvent(type: 'biometric_disabled');
  }

  factory SecurityEvent.appLocked() {
    return SecurityEvent(type: 'app_locked');
  }

  factory SecurityEvent.appUnlocked() {
    return SecurityEvent(type: 'app_unlocked');
  }
}
