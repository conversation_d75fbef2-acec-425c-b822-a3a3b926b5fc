/// Quran data models for authentic Quran integration

/// Revelation type enum
enum RevelationType {
  meccan,
  medinan,
}

/// Surah information
class SurahInfo {
  final int number;
  final String name;
  final String englishName;
  final RevelationType revelationType;
  final int verseCount;

  const SurahInfo({
    required this.number,
    required this.name,
    required this.englishName,
    required this.revelationType,
    required this.verseCount,
  });

  SurahInfo copyWith({
    int? number,
    String? name,
    String? englishName,
    RevelationType? revelationType,
    int? verseCount,
  }) {
    return SurahInfo(
      number: number ?? this.number,
      name: name ?? this.name,
      englishName: englishName ?? this.englishName,
      revelationType: revelationType ?? this.revelationType,
      verseCount: verseCount ?? this.verseCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'revelationType': revelationType.name,
      'verseCount': verseCount,
    };
  }

  factory SurahInfo.fromJson(Map<String, dynamic> json) {
    return SurahInfo(
      number: json['number'],
      name: json['name'],
      englishName: json['englishName'],
      revelationType: RevelationType.values.firstWhere(
        (e) => e.name == json['revelationType'],
        orElse: () => RevelationType.meccan,
      ),
      verseCount: json['verseCount'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SurahInfo && other.number == number;
  }

  @override
  int get hashCode => number.hashCode;
}

/// Individual verse
class Verse {
  final int number;
  final String arabicText;
  final int surahNumber;
  final String? translation;
  final bool isBookmarked;

  const Verse({
    required this.number,
    required this.arabicText,
    required this.surahNumber,
    this.translation,
    this.isBookmarked = false,
  });

  Verse copyWith({
    int? number,
    String? arabicText,
    int? surahNumber,
    String? translation,
    bool? isBookmarked,
  }) {
    return Verse(
      number: number ?? this.number,
      arabicText: arabicText ?? this.arabicText,
      surahNumber: surahNumber ?? this.surahNumber,
      translation: translation ?? this.translation,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'arabicText': arabicText,
      'surahNumber': surahNumber,
      'translation': translation,
      'isBookmarked': isBookmarked,
    };
  }

  factory Verse.fromJson(Map<String, dynamic> json) {
    return Verse(
      number: json['number'],
      arabicText: json['arabicText'],
      surahNumber: json['surahNumber'],
      translation: json['translation'],
      isBookmarked: json['isBookmarked'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Verse && 
           other.number == number && 
           other.surahNumber == surahNumber;
  }

  @override
  int get hashCode => Object.hash(number, surahNumber);
}

/// Complete surah with verses
class Surah {
  final SurahInfo info;
  final List<Verse> verses;

  const Surah({
    required this.info,
    required this.verses,
  });

  Surah copyWith({
    SurahInfo? info,
    List<Verse>? verses,
  }) {
    return Surah(
      info: info ?? this.info,
      verses: verses ?? this.verses,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'info': info.toJson(),
      'verses': verses.map((v) => v.toJson()).toList(),
    };
  }

  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      info: SurahInfo.fromJson(json['info']),
      verses: (json['verses'] as List)
          .map((v) => Verse.fromJson(v))
          .toList(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Surah && other.info == info;
  }

  @override
  int get hashCode => info.hashCode;
}

/// Search result
class SearchResult {
  final SurahInfo surah;
  final Verse verse;
  final String matchedText;

  const SearchResult({
    required this.surah,
    required this.verse,
    required this.matchedText,
  });

  Map<String, dynamic> toJson() {
    return {
      'surah': surah.toJson(),
      'verse': verse.toJson(),
      'matchedText': matchedText,
    };
  }

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    return SearchResult(
      surah: SurahInfo.fromJson(json['surah']),
      verse: Verse.fromJson(json['verse']),
      matchedText: json['matchedText'],
    );
  }
}

/// Bookmark model
class QuranBookmark {
  final int surahNumber;
  final int verseNumber;
  final String surahName;
  final String verseText;
  final DateTime createdAt;
  final String? note;

  const QuranBookmark({
    required this.surahNumber,
    required this.verseNumber,
    required this.surahName,
    required this.verseText,
    required this.createdAt,
    this.note,
  });

  QuranBookmark copyWith({
    int? surahNumber,
    int? verseNumber,
    String? surahName,
    String? verseText,
    DateTime? createdAt,
    String? note,
  }) {
    return QuranBookmark(
      surahNumber: surahNumber ?? this.surahNumber,
      verseNumber: verseNumber ?? this.verseNumber,
      surahName: surahName ?? this.surahName,
      verseText: verseText ?? this.verseText,
      createdAt: createdAt ?? this.createdAt,
      note: note ?? this.note,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'surahName': surahName,
      'verseText': verseText,
      'createdAt': createdAt.toIso8601String(),
      'note': note,
    };
  }

  factory QuranBookmark.fromJson(Map<String, dynamic> json) {
    return QuranBookmark(
      surahNumber: json['surahNumber'],
      verseNumber: json['verseNumber'],
      surahName: json['surahName'],
      verseText: json['verseText'],
      createdAt: DateTime.parse(json['createdAt']),
      note: json['note'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranBookmark && 
           other.surahNumber == surahNumber && 
           other.verseNumber == verseNumber;
  }

  @override
  int get hashCode => Object.hash(surahNumber, verseNumber);
}

/// Reading progress
class ReadingProgress {
  final int surahNumber;
  final int verseNumber;
  final DateTime lastRead;
  final int totalVersesRead;

  const ReadingProgress({
    required this.surahNumber,
    required this.verseNumber,
    required this.lastRead,
    required this.totalVersesRead,
  });

  ReadingProgress copyWith({
    int? surahNumber,
    int? verseNumber,
    DateTime? lastRead,
    int? totalVersesRead,
  }) {
    return ReadingProgress(
      surahNumber: surahNumber ?? this.surahNumber,
      verseNumber: verseNumber ?? this.verseNumber,
      lastRead: lastRead ?? this.lastRead,
      totalVersesRead: totalVersesRead ?? this.totalVersesRead,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'lastRead': lastRead.toIso8601String(),
      'totalVersesRead': totalVersesRead,
    };
  }

  factory ReadingProgress.fromJson(Map<String, dynamic> json) {
    return ReadingProgress(
      surahNumber: json['surahNumber'],
      verseNumber: json['verseNumber'],
      lastRead: DateTime.parse(json['lastRead']),
      totalVersesRead: json['totalVersesRead'],
    );
  }
}
