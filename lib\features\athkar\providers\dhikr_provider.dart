import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Dhikr list provider
final dhikrsProvider = StateNotifierProvider<DhikrNotifier, List<Dhikr>>((ref) {
  return DhikrNotifier(ref);
});

class DhikrNotifier extends StateNotifier<List<Dhikr>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  DhikrNotifier(this.ref) : super([]) {
    loadDhikrs();
  }

  Future<void> loadDhikrs() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final dhikrs = await _db.getAllDhikrs(userId: userProfile.id.toString());
      state = dhikrs;
    }
  }

  Future<void> addDhikr(Dhikr dhikr) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      dhikr.userId = userProfile.id.toString();
      final savedDhikr = await _db.saveDhikr(dhikr);
      state = [...state, savedDhikr];
    }
  }

  Future<void> updateDhikr(Dhikr dhikr) async {
    final savedDhikr = await _db.saveDhikr(dhikr);
    state = state.map((d) => d.id == savedDhikr.id ? savedDhikr : d).toList();
  }

  Future<void> deleteDhikr(int dhikrId) async {
    await _db.deleteDhikr(dhikrId);
    state = state.where((d) => d.id != dhikrId).toList();
  }

  List<Dhikr> getDhikrsByCategory(String category) {
    return state.where((dhikr) => dhikr.category == category).toList();
  }

  List<String> getCategories() {
    final categories = state.map((dhikr) => dhikr.category).toSet().toList();
    categories.sort();
    return categories;
  }
}

// Dhikr categories provider
final dhikrCategoriesProvider = Provider<List<String>>((ref) {
  final dhikrs = ref.watch(dhikrsProvider);
  final categories = dhikrs.map((dhikr) => dhikr.category).toSet().toList();
  categories.sort();
  return categories;
});

// Dhikrs by category provider
final dhikrsByCategoryProvider = Provider.family<List<Dhikr>, String>((ref, category) {
  final dhikrs = ref.watch(dhikrsProvider);
  return dhikrs.where((dhikr) => dhikr.category == category).toList();
});

// Dhikr statistics provider
final dhikrStatsProvider = Provider<Map<String, int>>((ref) {
  final dhikrs = ref.watch(dhikrsProvider);
  
  final stats = <String, int>{
    'total': dhikrs.length,
    'custom': dhikrs.where((d) => d.isCustom).length,
    'categories': dhikrs.map((d) => d.category).toSet().length,
  };
  
  return stats;
});

// Search dhikrs provider
final searchDhikrsProvider = StateProvider<String>((ref) => '');

final filteredDhikrsProvider = Provider<List<Dhikr>>((ref) {
  final dhikrs = ref.watch(dhikrsProvider);
  final searchQuery = ref.watch(searchDhikrsProvider);
  
  if (searchQuery.isEmpty) {
    return dhikrs;
  }
  
  final query = searchQuery.toLowerCase();
  return dhikrs.where((dhikr) {
    return dhikr.arabicText.toLowerCase().contains(query) ||
           dhikr.transliteration.toLowerCase().contains(query) ||
           dhikr.translation.toLowerCase().contains(query) ||
           dhikr.category.toLowerCase().contains(query);
  }).toList();
});
