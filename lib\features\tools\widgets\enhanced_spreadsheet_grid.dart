import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/spreadsheet_selection_provider.dart';
import '../models/spreadsheet_cell.dart';
import 'advanced_cell_editor.dart';

/// Enhanced spreadsheet grid with advanced selection and editing
class EnhancedSpreadsheetGrid extends ConsumerStatefulWidget {
  final Map<String, SpreadsheetCell> cells;
  final Function(String address, String value) onCellChanged;
  final int rows;
  final int columns;

  const EnhancedSpreadsheetGrid({
    super.key,
    required this.cells,
    required this.onCellChanged,
    this.rows = 20,
    this.columns = 10,
  });

  @override
  ConsumerState<EnhancedSpreadsheetGrid> createState() => _EnhancedSpreadsheetGridState();
}

class _EnhancedSpreadsheetGridState extends ConsumerState<EnhancedSpreadsheetGrid> {
  late FocusNode _gridFocusNode;
  OverlayEntry? _editorOverlay;
  GlobalKey? _editingCellKey;

  @override
  void initState() {
    super.initState();
    _gridFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _removeEditorOverlay();
    _gridFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectionState = ref.watch(cellSelectionProvider);
    
    return Focus(
      focusNode: _gridFocusNode,
      onKeyEvent: _handleKeyEvent,
      child: GestureDetector(
        onTap: () => _gridFocusNode.requestFocus(),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              // Column headers
              _buildColumnHeaders(),
              
              // Grid rows
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: List.generate(
                      widget.rows,
                      (rowIndex) => _buildGridRow(rowIndex + 1, selectionState),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildColumnHeaders() {
    return Container(
      height: 32,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        children: [
          // Row header space
          Container(
            width: 60,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
            ),
          ),
          
          // Column headers
          ...List.generate(
            widget.columns,
            (colIndex) => _buildColumnHeader(colIndex + 1),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(int columnNumber) {
    final columnLetter = SpreadsheetUtils.columnNumberToLetter(columnNumber);
    
    return Container(
      width: 100,
      height: 32,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Center(
        child: Text(
          columnLetter,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }

  Widget _buildGridRow(int rowNumber, CellSelectionState selectionState) {
    return Container(
      height: 32,
      child: Row(
        children: [
          // Row header
          _buildRowHeader(rowNumber),
          
          // Row cells
          ...List.generate(
            widget.columns,
            (colIndex) => _buildGridCell(rowNumber, colIndex + 1, selectionState),
          ),
        ],
      ),
    );
  }

  Widget _buildRowHeader(int rowNumber) {
    return Container(
      width: 60,
      height: 32,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Center(
        child: Text(
          rowNumber.toString(),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }

  Widget _buildGridCell(int row, int column, CellSelectionState selectionState) {
    final address = SpreadsheetUtils.getCellAddress(row, column);
    final cell = widget.cells[address];
    final isSelected = selectionState.selectedRange.contains(address);
    final isEditing = selectionState.editingCell == address;
    final cellKey = GlobalKey();

    if (isEditing) {
      _editingCellKey = cellKey;
    }

    return GestureDetector(
      onTap: () => _handleCellTap(address, false),
      onDoubleTap: () => _startEditing(address, cell?.formula ?? cell?.value?.toString()),
      onLongPress: () => _showContextMenu(address),
      onPanStart: (details) => _handlePanStart(address),
      onPanUpdate: (details) => _handlePanUpdate(details),
      onPanEnd: (details) => _handlePanEnd(),
      child: Container(
        key: cellKey,
        width: 100,
        height: 32,
        decoration: BoxDecoration(
          color: _getCellBackgroundColor(isSelected, isEditing),
          border: Border(
            right: BorderSide(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
            bottom: BorderSide(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
        ),
        child: isEditing ? null : _buildCellContent(cell),
      ),
    );
  }

  Color _getCellBackgroundColor(bool isSelected, bool isEditing) {
    if (isEditing) {
      return Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3);
    } else if (isSelected) {
      return Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.5);
    } else {
      return Theme.of(context).colorScheme.surface;
    }
  }

  Widget _buildCellContent(SpreadsheetCell? cell) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          cell?.displayValue ?? '',
          style: TextStyle(
            fontSize: 12,
            color: cell?.type == CellType.error 
                ? Colors.red 
                : Theme.of(context).colorScheme.onSurface,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  void _handleCellTap(String address, bool isCtrlPressed) {
    final selectionNotifier = ref.read(cellSelectionProvider.notifier);
    
    if (isCtrlPressed) {
      selectionNotifier.selectCell(address, addToSelection: true);
    } else {
      selectionNotifier.selectCell(address);
    }
  }

  void _handlePanStart(String address) {
    final selectionNotifier = ref.read(cellSelectionProvider.notifier);
    selectionNotifier.startRangeSelection(address);
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    // Calculate which cell the drag is over
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    
    // Calculate cell position based on coordinates
    final column = ((localPosition.dx - 60) / 100).floor() + 1;
    final row = ((localPosition.dy - 32) / 32).floor() + 1;
    
    if (column >= 1 && column <= widget.columns && row >= 1 && row <= widget.rows) {
      final address = SpreadsheetUtils.getCellAddress(row, column);
      final selectionNotifier = ref.read(cellSelectionProvider.notifier);
      selectionNotifier.updateRangeSelection(address);
    }
  }

  void _handlePanEnd() {
    final selectionNotifier = ref.read(cellSelectionProvider.notifier);
    selectionNotifier.endRangeSelection();
  }

  void _startEditing(String address, String? initialValue) {
    final selectionNotifier = ref.read(cellSelectionProvider.notifier);
    selectionNotifier.startEditing(address, initialValue);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showEditorOverlay(address, initialValue);
    });
  }

  void _showEditorOverlay(String address, String? initialValue) {
    _removeEditorOverlay();
    
    if (_editingCellKey?.currentContext == null) return;
    
    final RenderBox cellRenderBox = _editingCellKey!.currentContext!.findRenderObject() as RenderBox;
    final cellPosition = cellRenderBox.localToGlobal(Offset.zero);
    final cellSize = cellRenderBox.size;

    _editorOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: cellPosition.dx,
        top: cellPosition.dy,
        width: cellSize.width.clamp(200, 400),
        child: AdvancedCellEditor(
          cellAddress: address,
          initialValue: initialValue,
          onValueChanged: (value) {
            // Update cell value in real-time
          },
          onEditingComplete: () {
            final selectionState = ref.read(cellSelectionProvider);
            if (selectionState.editingText != null) {
              widget.onCellChanged(address, selectionState.editingText!);
            }
            _stopEditing();
          },
          onEditingCanceled: () {
            _stopEditing();
          },
        ),
      ),
    );

    Overlay.of(context).insert(_editorOverlay!);
  }

  void _stopEditing() {
    final selectionNotifier = ref.read(cellSelectionProvider.notifier);
    selectionNotifier.stopEditing();
    _removeEditorOverlay();
  }

  void _removeEditorOverlay() {
    _editorOverlay?.remove();
    _editorOverlay = null;
    _editingCellKey = null;
  }

  void _showContextMenu(String address) {
    // TODO: Implement context menu with options like:
    // - Insert function
    // - Copy/Paste
    // - Clear cell
    // - Format cell
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      final selectionNotifier = ref.read(cellSelectionProvider.notifier);
      final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
      final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowUp:
        case LogicalKeyboardKey.arrowDown:
        case LogicalKeyboardKey.arrowLeft:
        case LogicalKeyboardKey.arrowRight:
          if (isCtrlPressed) {
            selectionNotifier.jumpToDataBoundary(event.logicalKey, widget.cells);
          } else {
            selectionNotifier.navigateWithKeyboard(event.logicalKey, extend: isShiftPressed);
          }
          return KeyEventResult.handled;
          
        case LogicalKeyboardKey.tab:
          selectionNotifier.navigateWithKeyboard(event.logicalKey);
          return KeyEventResult.handled;
          
        case LogicalKeyboardKey.enter:
          selectionNotifier.navigateWithKeyboard(event.logicalKey);
          return KeyEventResult.handled;
          
        case LogicalKeyboardKey.f2:
          final selectionState = ref.read(cellSelectionProvider);
          if (selectionState.selectedCell != null) {
            final cell = widget.cells[selectionState.selectedCell!];
            _startEditing(selectionState.selectedCell!, 
                         cell?.formula ?? cell?.value?.toString());
          }
          return KeyEventResult.handled;
          
        case LogicalKeyboardKey.delete:
        case LogicalKeyboardKey.backspace:
          final selectionState = ref.read(cellSelectionProvider);
          for (final address in selectionState.selectedRange) {
            widget.onCellChanged(address, '');
          }
          return KeyEventResult.handled;
          
        case LogicalKeyboardKey.keyC:
          if (isCtrlPressed) {
            selectionNotifier.copyToClipboard(widget.cells);
            return KeyEventResult.handled;
          }
          break;
          
        case LogicalKeyboardKey.keyV:
          if (isCtrlPressed) {
            selectionNotifier.pasteFromClipboard(widget.cells, widget.onCellChanged);
            return KeyEventResult.handled;
          }
          break;
      }
      
      // Start editing if user types a character
      if (event.character != null && event.character!.isNotEmpty) {
        final selectionState = ref.read(cellSelectionProvider);
        if (selectionState.selectedCell != null) {
          _startEditing(selectionState.selectedCell!, event.character);
          return KeyEventResult.handled;
        }
      }
    }
    
    return KeyEventResult.ignored;
  }
}
