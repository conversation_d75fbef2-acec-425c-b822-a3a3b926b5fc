import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/notification.dart';
import '../services/notification_service.dart';

// Notification service provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// Notifications stream provider
final notificationsProvider = StreamProvider<List<AppNotification>>((ref) {
  final service = ref.watch(notificationServiceProvider);
  return service.notificationsStream;
});

// New notification stream provider
final newNotificationProvider = StreamProvider<AppNotification>((ref) {
  final service = ref.watch(notificationServiceProvider);
  return service.newNotificationStream;
});

// Unread count provider
final unreadNotificationCountProvider = Provider<int>((ref) {
  final notificationsAsync = ref.watch(notificationsProvider);
  return notificationsAsync.when(
    data: (notifications) => notifications.where((n) => n.isSent && !n.isRead).length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Pending notifications provider
final pendingNotificationsProvider = Provider<List<AppNotification>>((ref) {
  final notificationsAsync = ref.watch(notificationsProvider);
  return notificationsAsync.when(
    data: (notifications) => notifications.where((n) => n.isPending).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Notification actions provider
final notificationActionsProvider = Provider<NotificationActions>((ref) {
  final service = ref.watch(notificationServiceProvider);
  return NotificationActions(service);
});

// Notification helper class
class NotificationActions {
  final NotificationService _service;

  NotificationActions(this._service);

  // Schedule notifications
  Future<void> scheduleTodoReminder({
    required int todoId,
    required String todoTitle,
    required DateTime reminderTime,
  }) async {
    final notification = AppNotification.todoReminder(
      todoId: todoId,
      todoTitle: todoTitle,
      reminderTime: reminderTime,
    );
    await _service.scheduleNotification(notification);
  }

  Future<void> scheduleDhikrReminder({
    required String dhikrName,
    required DateTime reminderTime,
    bool isRepeating = true,
  }) async {
    final notification = AppNotification.dhikrReminder(
      dhikrName: dhikrName,
      reminderTime: reminderTime,
      isRepeating: isRepeating,
    );
    await _service.scheduleNotification(notification);
  }

  Future<void> scheduleVoiceMemoTranscription({
    required int memoId,
    required String memoTitle,
    required bool success,
  }) async {
    final notification = AppNotification.voiceMemoTranscription(
      memoId: memoId,
      memoTitle: memoTitle,
      success: success,
    );
    await _service.scheduleNotification(notification);
  }

  Future<void> scheduleAchievement({
    required String achievementTitle,
    required String description,
  }) async {
    final notification = AppNotification.achievement(
      achievementTitle: achievementTitle,
      description: description,
    );
    await _service.scheduleNotification(notification);
  }

  Future<void> scheduleDailyReview() async {
    final now = DateTime.now();
    final reviewTime = DateTime(now.year, now.month, now.day, 20, 0); // 8 PM
    
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Daily Review',
      body: 'How was your productivity today?',
      type: NotificationType.dailyReview,
      scheduledAt: reviewTime,
      isRepeating: true,
      repeatInterval: const Duration(days: 1),
      maxRepeats: 365,
      iconPath: 'assets/icons/review.png',
      color: Colors.purple,
    );
    
    await _service.scheduleNotification(notification);
  }

  Future<void> scheduleWeeklyReport() async {
    final now = DateTime.now();
    final daysUntilSunday = 7 - now.weekday;
    final nextSunday = now.add(Duration(days: daysUntilSunday));
    final reportTime = DateTime(nextSunday.year, nextSunday.month, nextSunday.day, 18, 0);
    
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Weekly Report',
      body: 'Your weekly productivity report is ready!',
      type: NotificationType.weeklyReport,
      scheduledAt: reportTime,
      isRepeating: true,
      repeatInterval: const Duration(days: 7),
      maxRepeats: 52,
      iconPath: 'assets/icons/report.png',
      color: Colors.indigo,
    );
    
    await _service.scheduleNotification(notification);
  }

  Future<void> scheduleBackupComplete() async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Backup Complete',
      body: 'Your data has been successfully backed up',
      type: NotificationType.backupComplete,
      scheduledAt: DateTime.now(),
      iconPath: 'assets/icons/backup.png',
      color: Colors.green,
      priority: NotificationPriority.low,
    );
    
    await _service.scheduleNotification(notification);
  }

  Future<void> scheduleSyncComplete() async {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Sync Complete',
      body: 'Your data has been synchronized across devices',
      type: NotificationType.syncComplete,
      scheduledAt: DateTime.now(),
      iconPath: 'assets/icons/sync.png',
      color: Colors.blue,
      priority: NotificationPriority.low,
    );
    
    await _service.scheduleNotification(notification);
  }

  // Notification management
  Future<void> markAsRead(int notificationId) async {
    await _service.updateNotificationStatus(notificationId, NotificationStatus.read);
  }

  Future<void> dismiss(int notificationId) async {
    await _service.updateNotificationStatus(notificationId, NotificationStatus.dismissed);
  }

  Future<void> cancel(int notificationId) async {
    await _service.cancelNotification(notificationId);
  }

  Future<void> handleAction(int notificationId, String actionId) async {
    await _service.handleNotificationAction(notificationId, actionId);
  }

  Future<void> clearAll() async {
    await _service.clearAllNotifications();
  }

  Future<void> clearRead() async {
    await _service.clearReadNotifications();
  }

  // Settings
  Future<void> updateSettings({
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? badgeEnabled,
    TimeOfDay? quietHoursStart,
    TimeOfDay? quietHoursEnd,
  }) async {
    await _service.updateSettings(
      notificationsEnabled: notificationsEnabled,
      soundEnabled: soundEnabled,
      vibrationEnabled: vibrationEnabled,
      badgeEnabled: badgeEnabled,
      quietHoursStart: quietHoursStart,
      quietHoursEnd: quietHoursEnd,
    );
  }

  // Bulk operations
  Future<void> scheduleMultipleTodoReminders(List<Map<String, dynamic>> todos) async {
    for (final todo in todos) {
      if (todo['reminderTime'] != null) {
        await scheduleTodoReminder(
          todoId: todo['id'],
          todoTitle: todo['title'],
          reminderTime: todo['reminderTime'],
        );
      }
    }
  }

  Future<void> cancelTodoReminders(int todoId) async {
    final notifications = await _service.notificationsStream.first;
    final todoNotifications = notifications.where((n) => 
        n.type == NotificationType.todoReminder && 
        n.associatedId == todoId.toString()).toList();
    
    for (final notification in todoNotifications) {
      await _service.cancelNotification(notification.id);
    }
  }

  Future<void> rescheduleTodoReminder({
    required int todoId,
    required DateTime newReminderTime,
  }) async {
    await cancelTodoReminders(todoId);
    // Note: Would need todo title from todo service
    await scheduleTodoReminder(
      todoId: todoId,
      todoTitle: 'Todo Task', // Placeholder
      reminderTime: newReminderTime,
    );
  }

  // Statistics
  Future<Map<String, int>> getNotificationStats() async {
    final notifications = await _service.notificationsStream.first;
    
    return {
      'total': notifications.length,
      'pending': notifications.where((n) => n.isPending).length,
      'sent': notifications.where((n) => n.isSent).length,
      'read': notifications.where((n) => n.isRead).length,
      'dismissed': notifications.where((n) => n.status == NotificationStatus.dismissed).length,
      'failed': notifications.where((n) => n.status == NotificationStatus.failed).length,
      'todoReminders': notifications.where((n) => n.type == NotificationType.todoReminder).length,
      'dhikrReminders': notifications.where((n) => n.type == NotificationType.dhikrReminder).length,
      'achievements': notifications.where((n) => n.type == NotificationType.achievement).length,
    };
  }
}

// Notification settings provider
final notificationSettingsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.watch(notificationServiceProvider);
  return {
    'enabled': service.notificationsEnabled,
    'unreadCount': service.unreadCount,
    'isInQuietHours': service.isInQuietHours,
  };
});

// Initialize notification service provider
final initializeNotificationServiceProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(notificationServiceProvider);
  await service.initialize();
});
