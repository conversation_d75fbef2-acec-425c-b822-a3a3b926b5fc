enum PrayerType { fajr, dhuhr, asr, maghrib, isha }

class PrayerTime {
  int id = 0;
  PrayerType prayerType = PrayerType.fajr;
  DateTime prayerTime = DateTime.now();
  bool notificationEnabled = true;
  int reminderMinutes = 10; // Minutes before prayer time
  String userId = '';
  
  // Location data
  double? latitude;
  double? longitude;
  String? locationName;
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  PrayerTime();

  PrayerTime.create({
    required this.prayerType,
    required this.prayerTime,
    required this.userId,
    this.notificationEnabled = true,
    this.reminderMinutes = 10,
    this.latitude,
    this.longitude,
    this.locationName,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  String get prayerName {
    switch (prayerType) {
      case PrayerType.fajr:
        return 'Fajr';
      case PrayerType.dhuhr:
        return 'Dhuhr';
      case PrayerType.asr:
        return 'Asr';
      case PrayerType.maghrib:
        return 'Maghrib';
      case PrayerType.isha:
        return 'Isha';
    }
  }

  String get prayerNameArabic {
    switch (prayerType) {
      case PrayerType.fajr:
        return 'الفجر';
      case PrayerType.dhuhr:
        return 'الظهر';
      case PrayerType.asr:
        return 'العصر';
      case PrayerType.maghrib:
        return 'المغرب';
      case PrayerType.isha:
        return 'العشاء';
    }
  }

  String get formattedTime {
    final hour = prayerTime.hour;
    final minute = prayerTime.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  bool get isToday {
    final now = DateTime.now();
    return prayerTime.year == now.year &&
           prayerTime.month == now.month &&
           prayerTime.day == now.day;
  }

  bool get isPassed {
    return DateTime.now().isAfter(prayerTime);
  }

  bool get isUpcoming {
    final now = DateTime.now();
    return prayerTime.isAfter(now) && isToday;
  }

  Duration get timeUntilPrayer {
    final now = DateTime.now();
    if (prayerTime.isAfter(now)) {
      return prayerTime.difference(now);
    }
    return Duration.zero;
  }

  String get timeUntilPrayerFormatted {
    final duration = timeUntilPrayer;
    if (duration == Duration.zero) return 'Passed';
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  void updatePrayerTime({
    DateTime? prayerTime,
    bool? notificationEnabled,
    int? reminderMinutes,
    double? latitude,
    double? longitude,
    String? locationName,
  }) {
    if (prayerTime != null) this.prayerTime = prayerTime;
    if (notificationEnabled != null) this.notificationEnabled = notificationEnabled;
    if (reminderMinutes != null) this.reminderMinutes = reminderMinutes;
    if (latitude != null) this.latitude = latitude;
    if (longitude != null) this.longitude = longitude;
    if (locationName != null) this.locationName = locationName;
    
    updateTimestamp();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'prayerType': prayerType.name,
      'prayerTime': prayerTime.toIso8601String(),
      'notificationEnabled': notificationEnabled,
      'reminderMinutes': reminderMinutes,
      'latitude': latitude,
      'longitude': longitude,
      'locationName': locationName,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory PrayerTime.fromJson(Map<String, dynamic> json) {
    final prayerTime = PrayerTime();
    prayerTime.id = json['id'] ?? 0;
    prayerTime.prayerType = PrayerType.values.firstWhere(
      (p) => p.name == json['prayerType'],
      orElse: () => PrayerType.fajr,
    );
    prayerTime.prayerTime = DateTime.parse(json['prayerTime']);
    prayerTime.notificationEnabled = json['notificationEnabled'] ?? true;
    prayerTime.reminderMinutes = json['reminderMinutes'] ?? 10;
    prayerTime.latitude = json['latitude']?.toDouble();
    prayerTime.longitude = json['longitude']?.toDouble();
    prayerTime.locationName = json['locationName'];
    prayerTime.userId = json['userId'] ?? '';
    prayerTime.createdAt = DateTime.parse(json['createdAt']);
    prayerTime.updatedAt = DateTime.parse(json['updatedAt']);
    prayerTime.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    prayerTime.isDeleted = json['isDeleted'] ?? false;
    prayerTime.syncId = json['syncId'];
    return prayerTime;
  }
}
