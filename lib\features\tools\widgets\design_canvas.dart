import 'package:flutter/material.dart';
import '../models/ui_component.dart';

/// Advanced design canvas with zoom, pan, and grid features
class DesignCanvas extends StatefulWidget {
  final List<UIComponent> components;
  final UIComponent? selectedComponent;
  final Function(UIComponent) onComponentSelected;
  final Function(UIComponent) onComponentUpdated;
  final Function(ComponentType) onComponentAdded;
  final bool isPreviewMode;
  final double zoom;
  final Offset offset;
  final Function(double) onZoomChanged;
  final Function(Offset) onOffsetChanged;

  const DesignCanvas({
    super.key,
    required this.components,
    required this.selectedComponent,
    required this.onComponentSelected,
    required this.onComponentUpdated,
    required this.onComponentAdded,
    this.isPreviewMode = false,
    this.zoom = 1.0,
    this.offset = Offset.zero,
    required this.onZoomChanged,
    required this.onOffsetChanged,
  });

  @override
  State<DesignCanvas> createState() => _DesignCanvasState();
}

class _DesignCanvasState extends State<DesignCanvas>
    with TickerProviderStateMixin {
  late TransformationController _transformationController;
  bool _showGrid = true;
  bool _snapToGrid = true;
  double _gridSize = 20.0;
  
  // Animation controllers
  late AnimationController _selectionController;
  late Animation<double> _selectionAnimation;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _selectionAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _selectionController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Stack(
        children: [
          // Main canvas
          _buildCanvas(),
          
          // Canvas controls
          if (!widget.isPreviewMode) _buildCanvasControls(),
          
          // Zoom indicator
          _buildZoomIndicator(),
        ],
      ),
    );
  }

  Widget _buildCanvas() {
    return InteractiveViewer(
      transformationController: _transformationController,
      minScale: 0.1,
      maxScale: 5.0,
      onInteractionUpdate: (details) {
        widget.onZoomChanged(_transformationController.value.getMaxScaleOnAxis());
      },
      child: DragTarget<ComponentType>(
        onAccept: (componentType) {
          widget.onComponentAdded(componentType);
        },
        builder: (context, candidateData, rejectedData) {
          return Container(
            width: 2000,
            height: 2000,
            decoration: BoxDecoration(
              color: candidateData.isNotEmpty
                  ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1)
                  : Colors.white,
            ),
            child: Stack(
              children: [
                // Grid background
                if (_showGrid && !widget.isPreviewMode) _buildGrid(),
                
                // Empty state
                if (widget.components.isEmpty) _buildEmptyState(),
                
                // Components
                ...widget.components.map((component) => _buildCanvasComponent(component)),
                
                // Drop indicator
                if (candidateData.isNotEmpty) _buildDropIndicator(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildGrid() {
    return CustomPaint(
      size: const Size(2000, 2000),
      painter: GridPainter(
        gridSize: _gridSize,
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.isPreviewMode ? Icons.preview : Icons.design_services,
            size: 80,
            color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            widget.isPreviewMode ? 'Preview Mode' : 'Design Canvas',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          if (!widget.isPreviewMode) ...[
            Text(
              'Drag components from the palette to start building',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Use the grid for precise positioning',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCanvasComponent(UIComponent component) {
    final isSelected = widget.selectedComponent == component;

    return Positioned(
      left: component.position.x,
      top: component.position.y,
      child: GestureDetector(
        onTap: () {
          widget.onComponentSelected(component);
          if (isSelected) {
            _selectionController.forward().then((_) {
              _selectionController.reverse();
            });
          }
        },
        onPanStart: widget.isPreviewMode ? null : (details) {
          widget.onComponentSelected(component);
        },
        onPanUpdate: widget.isPreviewMode ? null : (details) {
          double newX = component.position.x + details.delta.dx;
          double newY = component.position.y + details.delta.dy;

          // Snap to grid if enabled
          if (_snapToGrid) {
            newX = (newX / _gridSize).round() * _gridSize;
            newY = (newY / _gridSize).round() * _gridSize;
          }

          final updatedComponent = component.copyWith(
            position: component.position.copyWith(x: newX, y: newY),
          );
          widget.onComponentUpdated(updatedComponent);
        },
        child: AnimatedBuilder(
          animation: _selectionAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: isSelected ? _selectionAnimation.value : 1.0,
              child: Container(
                width: component.position.width,
                height: component.position.height,
                decoration: BoxDecoration(
                  border: isSelected && !widget.isPreviewMode
                      ? Border.all(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : null,
                  borderRadius: BorderRadius.circular(8.0), // Default border radius
                  boxShadow: isSelected && !widget.isPreviewMode
                      ? [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ]
                      : null,
                ),
                child: Stack(
                  children: [
                    _buildComponentWidget(component),

                    // Selection handles
                    if (isSelected && !widget.isPreviewMode) _buildSelectionHandles(component),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildComponentWidget(UIComponent component) {
    final componentWidget = _createComponentWidget(component);

    return Container(
      width: component.position.width,
      height: component.position.height,
      decoration: BoxDecoration(
        color: _parseColor(component.style.backgroundColor) ?? Colors.transparent,
        borderRadius: BorderRadius.circular(8.0), // Default border radius
        border: Border.all(
          color: _parseColor(component.style.borderColor) ?? Theme.of(context).colorScheme.outline,
          width: 1.0,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: componentWidget,
      ),
    );
  }

  Widget _createComponentWidget(UIComponent component) {
    switch (component.type) {
      case ComponentType.textField:
        return TextField(
          decoration: InputDecoration(
            labelText: component.label,
            hintText: component.properties['placeholder'] as String? ?? '',
            border: const OutlineInputBorder(),
          ),
          enabled: widget.isPreviewMode,
          maxLines: (component.properties['isMultiline'] as bool?) == true ? null : 1,
          style: TextStyle(color: _parseColor(component.style.textColor)),
        );

      case ComponentType.button:
        return _buildButton(component);

      case ComponentType.label:
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            component.label,
            style: TextStyle(
              color: _parseColor(component.style.textColor),
              fontWeight: FontWeight.w500,
            ),
          ),
        );

      case ComponentType.outputField:
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            component.label,
            style: TextStyle(color: _parseColor(component.style.textColor)),
          ),
        );

      case ComponentType.dropdown:
        return DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: component.label,
            border: const OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem(value: 'option1', child: Text('Option 1')),
            DropdownMenuItem(value: 'option2', child: Text('Option 2')),
          ],
          onChanged: widget.isPreviewMode ? (value) {} : null,
        );

      case ComponentType.checkbox:
        return CheckboxListTile(
          title: Text(component.label),
          value: false,
          onChanged: widget.isPreviewMode ? (value) {} : null,
        );

      case ComponentType.toggle:
        return SwitchListTile(
          title: Text(component.label),
          value: false,
          onChanged: widget.isPreviewMode ? (value) {} : null,
        );

      default:
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              component.label,
              style: TextStyle(color: _parseColor(component.style.textColor)),
            ),
          ),
        );
    }
  }

  Widget _buildButton(UIComponent component) {
    final style = component.properties['buttonStyle'] as String? ?? 'elevated';

    switch (style) {
      case 'outlined':
        return OutlinedButton(
          onPressed: widget.isPreviewMode ? () {} : null,
          child: Text(component.label),
        );
      case 'text':
        return TextButton(
          onPressed: widget.isPreviewMode ? () {} : null,
          child: Text(component.label),
        );
      case 'filled':
        return FilledButton(
          onPressed: widget.isPreviewMode ? () {} : null,
          child: Text(component.label),
        );
      default:
        return ElevatedButton(
          onPressed: widget.isPreviewMode ? () {} : null,
          child: Text(component.label),
        );
    }
  }

  Widget _buildSelectionHandles(UIComponent component) {
    const handleSize = 8.0;

    return Stack(
      children: [
        // Corner handles
        Positioned(
          left: -handleSize / 2,
          top: -handleSize / 2,
          child: _buildHandle(),
        ),
        Positioned(
          right: -handleSize / 2,
          top: -handleSize / 2,
          child: _buildHandle(),
        ),
        Positioned(
          left: -handleSize / 2,
          bottom: -handleSize / 2,
          child: _buildHandle(),
        ),
        Positioned(
          right: -handleSize / 2,
          bottom: -handleSize / 2,
          child: _buildHandle(),
        ),
      ],
    );
  }

  /// Parse color string to Color object
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;

    try {
      // Handle hex colors
      if (colorString.startsWith('#')) {
        final hexColor = colorString.substring(1);
        if (hexColor.length == 6) {
          return Color(int.parse('FF$hexColor', radix: 16));
        } else if (hexColor.length == 8) {
          return Color(int.parse(hexColor, radix: 16));
        }
      }

      // Handle named colors
      switch (colorString.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'grey':
        case 'gray':
          return Colors.grey;
        default:
          return null;
      }
    } catch (e) {
      return null;
    }
  }

  Widget _buildHandle() {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        border: Border.all(color: Colors.white, width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  Widget _buildDropIndicator() {
    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.primary,
          width: 2,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          'Drop component here',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildCanvasControls() {
    return Positioned(
      top: 16,
      right: 16,
      child: Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    _showGrid = !_showGrid;
                  });
                },
                icon: Icon(_showGrid ? Icons.grid_on : Icons.grid_off),
                tooltip: 'Toggle Grid',
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _snapToGrid = !_snapToGrid;
                  });
                },
                icon: Icon(_snapToGrid ? Icons.grid_4x4 : Icons.grid_3x3),
                tooltip: 'Snap to Grid',
              ),
              IconButton(
                onPressed: () {
                  _transformationController.value = Matrix4.identity();
                },
                icon: const Icon(Icons.center_focus_strong),
                tooltip: 'Reset View',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildZoomIndicator() {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Text(
            '${(widget.zoom * 100).round()}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom painter for drawing grid
class GridPainter extends CustomPainter {
  final double gridSize;
  final Color color;

  GridPainter({required this.gridSize, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 0.5;

    // Draw vertical lines
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
