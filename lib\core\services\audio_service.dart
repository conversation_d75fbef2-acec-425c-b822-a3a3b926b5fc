import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
// import 'package:record/record.dart'; // Temporarily disabled for build compatibility
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import '../permissions/permission_service.dart';

enum RecordingState { idle, recording, paused, stopped }
enum PlaybackState { idle, playing, paused, stopped }

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  // final AudioRecorder _recorder = AudioRecorder(); // Temporarily disabled
  final AudioPlayer _player = AudioPlayer();
  final PermissionService _permissionService = PermissionService();
  
  // Recording state
  RecordingState _recordingState = RecordingState.idle;
  String? _currentRecordingPath;
  Timer? _recordingTimer;
  Duration _recordingDuration = Duration.zero;
  
  // Playback state
  PlaybackState _playbackState = PlaybackState.idle;
  String? _currentPlaybackPath;
  Duration _playbackDuration = Duration.zero;
  Duration _playbackPosition = Duration.zero;
  
  // Stream controllers
  final StreamController<RecordingState> _recordingStateController = StreamController<RecordingState>.broadcast();
  final StreamController<PlaybackState> _playbackStateController = StreamController<PlaybackState>.broadcast();
  final StreamController<Duration> _recordingDurationController = StreamController<Duration>.broadcast();
  final StreamController<Duration> _playbackPositionController = StreamController<Duration>.broadcast();
  final StreamController<Duration> _playbackDurationController = StreamController<Duration>.broadcast();
  final StreamController<double> _amplitudeController = StreamController<double>.broadcast();

  // Getters
  RecordingState get recordingState => _recordingState;
  PlaybackState get playbackState => _playbackState;
  Duration get recordingDuration => _recordingDuration;
  Duration get playbackDuration => _playbackDuration;
  Duration get playbackPosition => _playbackPosition;
  String? get currentRecordingPath => _currentRecordingPath;
  String? get currentPlaybackPath => _currentPlaybackPath;

  // Streams
  Stream<RecordingState> get recordingStateStream => _recordingStateController.stream;
  Stream<PlaybackState> get playbackStateStream => _playbackStateController.stream;
  Stream<Duration> get recordingDurationStream => _recordingDurationController.stream;
  Stream<Duration> get playbackPositionStream => _playbackPositionController.stream;
  Stream<Duration> get playbackDurationStream => _playbackDurationController.stream;
  Stream<double> get amplitudeStream => _amplitudeController.stream;

  Future<void> initialize() async {
    // Initialize audio player listeners
    _player.onDurationChanged.listen((duration) {
      _playbackDuration = duration;
      _playbackDurationController.add(duration);
    });

    _player.onPositionChanged.listen((position) {
      _playbackPosition = position;
      _playbackPositionController.add(position);
    });

    _player.onPlayerStateChanged.listen((state) {
      switch (state) {
        case PlayerState.playing:
          _updatePlaybackState(PlaybackState.playing);
          break;
        case PlayerState.paused:
          _updatePlaybackState(PlaybackState.paused);
          break;
        case PlayerState.stopped:
          _updatePlaybackState(PlaybackState.stopped);
          break;
        case PlayerState.completed:
          _updatePlaybackState(PlaybackState.stopped);
          _playbackPosition = Duration.zero;
          _playbackPositionController.add(Duration.zero);
          break;
        default:
          break;
      }
    });
  }

  Future<bool> requestPermissions() async {
    return await _permissionService.requestVoiceRecordingPermissions();
  }

  Future<bool> hasPermissions() async {
    return await _permissionService.hasVoiceRecordingPermissions();
  }

  Future<String> _generateRecordingPath() async {
    final directory = await getApplicationDocumentsDirectory();
    final recordingsDir = Directory('${directory.path}/recordings');
    
    if (!await recordingsDir.exists()) {
      await recordingsDir.create(recursive: true);
    }
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${recordingsDir.path}/recording_$timestamp.aac';
  }

  Future<bool> startRecording({
    String? customPath,
    int bitRate = 128000,
    int sampleRate = 44100,
  }) async {
    try {
      if (_recordingState != RecordingState.idle) {
        return false;
      }

      final hasPermission = await hasPermissions();
      if (!hasPermission) {
        final granted = await requestPermissions();
        if (!granted) {
          return false;
        }
      }

      _currentRecordingPath = customPath ?? await _generateRecordingPath();
      
      // Recording temporarily disabled - record package not compatible
      // TODO: Re-enable when record package is compatible
      debugPrint('Audio recording temporarily disabled');
      
      _updateRecordingState(RecordingState.recording);
      _startRecordingTimer();
      _startAmplitudeMonitoring();
      
      return true;
    } catch (e) {
      debugPrint('Error starting recording: $e');
      return false;
    }
  }

  Future<bool> pauseRecording() async {
    try {
      if (_recordingState != RecordingState.recording) {
        return false;
      }

      // await _recorder.pause(); // Temporarily disabled
      _updateRecordingState(RecordingState.paused);
      _stopRecordingTimer();
      
      return true;
    } catch (e) {
      debugPrint('Error pausing recording: $e');
      return false;
    }
  }

  Future<bool> resumeRecording() async {
    try {
      if (_recordingState != RecordingState.paused) {
        return false;
      }

      // await _recorder.resume(); // Temporarily disabled
      _updateRecordingState(RecordingState.recording);
      _startRecordingTimer();
      
      return true;
    } catch (e) {
      debugPrint('Error resuming recording: $e');
      return false;
    }
  }

  Future<String?> stopRecording() async {
    try {
      if (_recordingState == RecordingState.idle) {
        return null;
      }

      // final path = await _recorder.stop(); // Temporarily disabled
      final path = _currentRecordingPath; // Use current path as fallback
      _updateRecordingState(RecordingState.stopped);
      _stopRecordingTimer();
      
      final finalPath = _currentRecordingPath;
      _currentRecordingPath = null;
      _recordingDuration = Duration.zero;
      _recordingDurationController.add(Duration.zero);
      
      return finalPath;
    } catch (e) {
      debugPrint('Error stopping recording: $e');
      return null;
    }
  }

  Future<bool> playAudio(String filePath) async {
    try {
      await stopPlayback(); // Stop any current playback
      
      _currentPlaybackPath = filePath;
      await _player.play(DeviceFileSource(filePath));
      
      return true;
    } catch (e) {
      debugPrint('Error playing audio: $e');
      return false;
    }
  }

  Future<bool> pausePlayback() async {
    try {
      await _player.pause();
      return true;
    } catch (e) {
      debugPrint('Error pausing playback: $e');
      return false;
    }
  }

  Future<bool> resumePlayback() async {
    try {
      await _player.resume();
      return true;
    } catch (e) {
      debugPrint('Error resuming playback: $e');
      return false;
    }
  }

  Future<bool> stopPlayback() async {
    try {
      await _player.stop();
      _currentPlaybackPath = null;
      return true;
    } catch (e) {
      debugPrint('Error stopping playback: $e');
      return false;
    }
  }

  Future<bool> seekTo(Duration position) async {
    try {
      await _player.seek(position);
      return true;
    } catch (e) {
      debugPrint('Error seeking: $e');
      return false;
    }
  }

  Future<bool> setVolume(double volume) async {
    try {
      await _player.setVolume(volume.clamp(0.0, 1.0));
      return true;
    } catch (e) {
      debugPrint('Error setting volume: $e');
      return false;
    }
  }

  Future<Duration?> getAudioDuration(String filePath) async {
    try {
      final tempPlayer = AudioPlayer();
      await tempPlayer.setSource(DeviceFileSource(filePath));
      
      final completer = Completer<Duration?>();
      late StreamSubscription subscription;
      
      subscription = tempPlayer.onDurationChanged.listen((duration) {
        subscription.cancel();
        tempPlayer.dispose();
        completer.complete(duration);
      });
      
      // Timeout after 5 seconds
      Timer(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          subscription.cancel();
          tempPlayer.dispose();
          completer.complete(null);
        }
      });
      
      return await completer.future;
    } catch (e) {
      debugPrint('Error getting audio duration: $e');
      return null;
    }
  }

  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      debugPrint('Error getting file size: $e');
      return 0;
    }
  }

  Future<bool> deleteAudioFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting audio file: $e');
      return false;
    }
  }

  void _updateRecordingState(RecordingState state) {
    _recordingState = state;
    _recordingStateController.add(state);
  }

  void _updatePlaybackState(PlaybackState state) {
    _playbackState = state;
    _playbackStateController.add(state);
  }

  void _startRecordingTimer() {
    _recordingTimer?.cancel();
    _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      _recordingDuration = Duration(milliseconds: _recordingDuration.inMilliseconds + 100);
      _recordingDurationController.add(_recordingDuration);
    });
  }

  void _stopRecordingTimer() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
  }

  void _startAmplitudeMonitoring() {
    Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (_recordingState != RecordingState.recording) {
        timer.cancel();
        return;
      }
      
      try {
        // final amplitude = await _recorder.getAmplitude(); // Temporarily disabled
        _amplitudeController.add(0.0); // Default amplitude
      } catch (e) {
        // Ignore amplitude errors
      }
    });
  }

  void dispose() {
    _recordingTimer?.cancel();
    // _recorder.dispose(); // Temporarily disabled
    _player.dispose();
    _recordingStateController.close();
    _playbackStateController.close();
    _recordingDurationController.close();
    _playbackPositionController.close();
    _playbackDurationController.close();
    _amplitudeController.close();
  }
}
