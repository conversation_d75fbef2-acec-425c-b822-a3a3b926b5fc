import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/budget_provider.dart';
import '../providers/transaction_provider.dart';
import '../providers/category_provider.dart';
import '../models/budget.dart';
import '../models/transaction.dart';

/// Comprehensive budget tracking tab for Money Flow
class BudgetTrackingTab extends ConsumerStatefulWidget {
  const BudgetTrackingTab({super.key});

  @override
  ConsumerState<BudgetTrackingTab> createState() => _BudgetTrackingTabState();
}

class _BudgetTrackingTabState extends ConsumerState<BudgetTrackingTab> {
  String _selectedPeriod = 'This Month';
  String _selectedView = 'Overview';
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _setDefaultDateRange();
  }

  void _setDefaultDateRange() {
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    final budgets = ref.watch(budgetsProvider);
    final transactions = ref.watch(transactionsProvider);
    final categories = ref.watch(categoriesProvider);

    return Scaffold(
      body: Column(
        children: [
          // Budget controls
          _buildBudgetControls(),
          
          // Budget content
          Expanded(
            child: _buildBudgetContent(budgets, transactions, categories),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createBudget,
        icon: const Icon(Icons.add),
        label: const Text('Create Budget'),
      ),
    );
  }

  Widget _buildBudgetControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Period and view selectors
          Row(
            children: [
              // Period selector
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: const InputDecoration(
                    labelText: 'Period',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'This Week',
                    'This Month',
                    'This Quarter',
                    'This Year',
                    'Custom Range',
                  ].map((period) {
                    return DropdownMenuItem(
                      value: period,
                      child: Text(period),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                      if (value != 'Custom Range') {
                        _updateDateRangeForPeriod(value);
                      }
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // View selector
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedView,
                  decoration: const InputDecoration(
                    labelText: 'View',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'Overview',
                    'Category Budgets',
                    'Performance',
                    'Alerts',
                  ].map((view) {
                    return DropdownMenuItem(
                      value: view,
                      child: Text(view),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedView = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Date range and actions
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range),
                  label: Text(
                    _selectedDateRange != null
                        ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                        : 'Select Date Range',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () => setState(() {}),
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetContent(List<Budget> budgets, List<Transaction> transactions, List<dynamic> categories) {
    switch (_selectedView) {
      case 'Overview':
        return _buildOverviewView(budgets, transactions);
      case 'Category Budgets':
        return _buildCategoryBudgetsView(budgets, transactions, categories);
      case 'Performance':
        return _buildPerformanceView(budgets, transactions);
      case 'Alerts':
        return _buildAlertsView(budgets, transactions);
      default:
        return _buildOverviewView(budgets, transactions);
    }
  }

  Widget _buildOverviewView(List<Budget> budgets, List<Transaction> transactions) {
    final filteredTransactions = _filterTransactionsByDateRange(transactions);
    final totalBudget = budgets.fold(0.0, (sum, budget) => sum + budget.budgetAmount);
    final totalSpent = filteredTransactions
        .where((t) => t.amount < 0)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
    final remaining = totalBudget - totalSpent;
    final spentPercentage = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0.0;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          Row(
            children: [
              Expanded(child: _buildSummaryCard('Total Budget', totalBudget, Colors.blue)),
              const SizedBox(width: 16),
              Expanded(child: _buildSummaryCard('Total Spent', totalSpent, Colors.red)),
              const SizedBox(width: 16),
              Expanded(child: _buildSummaryCard('Remaining', remaining, remaining >= 0 ? Colors.green : Colors.red)),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Overall progress
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Overall Budget Progress',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  LinearProgressIndicator(
                    value: spentPercentage / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      spentPercentage > 100 ? Colors.red : 
                      spentPercentage > 80 ? Colors.orange : Colors.green,
                    ),
                    minHeight: 8,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${spentPercentage.toStringAsFixed(1)}% spent'),
                      Text('\$${totalSpent.toStringAsFixed(2)} of \$${totalBudget.toStringAsFixed(2)}'),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Budget breakdown
          _buildSectionHeader('Budget Breakdown'),
          ...budgets.map((budget) => _buildBudgetCard(budget, filteredTransactions)),
        ],
      ),
    );
  }

  Widget _buildCategoryBudgetsView(List<Budget> budgets, List<Transaction> transactions, List<dynamic> categories) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Category Budgets'),
          
          if (budgets.isEmpty) ...[
            _buildEmptyBudgetState(),
          ] else ...[
            ...budgets.map((budget) => _buildDetailedBudgetCard(budget, transactions, categories)),
          ],
        ],
      ),
    );
  }

  Widget _buildPerformanceView(List<Budget> budgets, List<Transaction> transactions) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Budget Performance'),
          
          // Performance metrics
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Performance Metrics',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  _buildPerformanceMetric('Budgets on Track', '${_getBudgetsOnTrack(budgets, transactions)}/${budgets.length}'),
                  _buildPerformanceMetric('Average Spending Rate', '${_getAverageSpendingRate(budgets, transactions).toStringAsFixed(1)}%'),
                  _buildPerformanceMetric('Best Performing Category', _getBestPerformingCategory(budgets, transactions)),
                  _buildPerformanceMetric('Needs Attention', _getCategoryNeedingAttention(budgets, transactions)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsView(List<Budget> budgets, List<Transaction> transactions) {
    final alerts = _generateBudgetAlerts(budgets, transactions);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Budget Alerts'),
          
          if (alerts.isEmpty) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 64,
                        color: Colors.green,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'All budgets are on track!',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No budget alerts at this time.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ] else ...[
            ...alerts.map((alert) => _buildAlertCard(alert)),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, double amount, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '\$${amount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildBudgetCard(Budget budget, List<Transaction> transactions) {
    final spent = _getSpentAmount(budget, transactions);
    final percentage = budget.budgetAmount > 0 ? (spent / budget.budgetAmount) * 100 : 0.0;
    final remaining = budget.budgetAmount - spent;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    budget.name,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Text(
                  '\$${spent.toStringAsFixed(2)} / \$${budget.budgetAmount.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: percentage > 100 ? Colors.red : Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                percentage > 100 ? Colors.red : 
                percentage > 80 ? Colors.orange : Colors.green,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${percentage.toStringAsFixed(1)}% used'),
                Text(
                  'Remaining: \$${remaining.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: remaining >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedBudgetCard(Budget budget, List<Transaction> transactions, List<dynamic> categories) {
    final spent = _getSpentAmount(budget, transactions);
    final percentage = budget.budgetAmount > 0 ? (spent / budget.budgetAmount) * 100 : 0.0;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          budget.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            percentage > 100 ? Colors.red : 
            percentage > 80 ? Colors.orange : Colors.green,
          ),
        ),
        trailing: Text(
          '${percentage.toStringAsFixed(1)}%',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: percentage > 100 ? Colors.red : Colors.green,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBudgetDetailRow('Budget Amount', '\$${budget.budgetAmount.toStringAsFixed(2)}'),
                _buildBudgetDetailRow('Amount Spent', '\$${spent.toStringAsFixed(2)}'),
                _buildBudgetDetailRow('Remaining', '\$${(budget.budgetAmount - spent).toStringAsFixed(2)}'),
                _buildBudgetDetailRow('Period', _formatBudgetPeriod(budget)),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _editBudget(budget),
                        icon: const Icon(Icons.edit),
                        label: const Text('Edit'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _viewBudgetTransactions(budget, transactions),
                        icon: const Icon(Icons.list),
                        label: const Text('View Transactions'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyBudgetState() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.account_balance_wallet_outlined,
                size: 64,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                'No Budgets Created',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Create your first budget to start tracking your spending',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _createBudget,
                icon: const Icon(Icons.add),
                label: const Text('Create Budget'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceMetric(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertCard(BudgetAlert alert) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: _getAlertColor(alert.severity).withValues(alpha: 0.1),
      child: ListTile(
        leading: Icon(
          _getAlertIcon(alert.severity),
          color: _getAlertColor(alert.severity),
        ),
        title: Text(
          alert.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(alert.message),
        trailing: IconButton(
          onPressed: () => _dismissAlert(alert),
          icon: const Icon(Icons.close),
        ),
      ),
    );
  }

  // Helper methods
  List<Transaction> _filterTransactionsByDateRange(List<Transaction> transactions) {
    if (_selectedDateRange == null) return transactions;
    
    return transactions.where((transaction) {
      return transaction.date.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
             transaction.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
    }).toList();
  }

  double _getSpentAmount(Budget budget, List<Transaction> transactions) {
    final filteredTransactions = _filterTransactionsByDateRange(transactions);

    return filteredTransactions
        .where((t) => t.category == budget.category && t.amount < 0)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
  }

  int _getBudgetsOnTrack(List<Budget> budgets, List<Transaction> transactions) {
    return budgets.where((budget) {
      final spent = _getSpentAmount(budget, transactions);
      final percentage = budget.budgetAmount > 0 ? (spent / budget.budgetAmount) * 100 : 0.0;
      return percentage <= 80; // Consider on track if under 80%
    }).length;
  }

  double _getAverageSpendingRate(List<Budget> budgets, List<Transaction> transactions) {
    if (budgets.isEmpty) return 0.0;
    
    final totalPercentage = budgets.fold(0.0, (sum, budget) {
      final spent = _getSpentAmount(budget, transactions);
      return sum + (budget.budgetAmount > 0 ? (spent / budget.budgetAmount) * 100 : 0.0);
    });
    
    return totalPercentage / budgets.length;
  }

  String _getBestPerformingCategory(List<Budget> budgets, List<Transaction> transactions) {
    if (budgets.isEmpty) return 'N/A';
    
    Budget? bestBudget;
    double lowestPercentage = double.infinity;
    
    for (final budget in budgets) {
      final spent = _getSpentAmount(budget, transactions);
      final percentage = budget.budgetAmount > 0 ? (spent / budget.budgetAmount) * 100 : 0.0;
      
      if (percentage < lowestPercentage) {
        lowestPercentage = percentage;
        bestBudget = budget;
      }
    }
    
    return bestBudget?.name ?? 'N/A';
  }

  String _getCategoryNeedingAttention(List<Budget> budgets, List<Transaction> transactions) {
    Budget? worstBudget;
    double highestPercentage = 0.0;
    
    for (final budget in budgets) {
      final spent = _getSpentAmount(budget, transactions);
      final percentage = budget.budgetAmount > 0 ? (spent / budget.budgetAmount) * 100 : 0.0;
      
      if (percentage > highestPercentage && percentage > 80) {
        highestPercentage = percentage;
        worstBudget = budget;
      }
    }
    
    return worstBudget?.name ?? 'None';
  }

  List<BudgetAlert> _generateBudgetAlerts(List<Budget> budgets, List<Transaction> transactions) {
    final alerts = <BudgetAlert>[];
    
    for (final budget in budgets) {
      final spent = _getSpentAmount(budget, transactions);
      final percentage = budget.budgetAmount > 0 ? (spent / budget.budgetAmount) * 100 : 0.0;
      
      if (percentage > 100) {
        alerts.add(BudgetAlert(
          id: '${budget.id}_exceeded',
          budgetId: budget.id,
          severity: AlertSeverity.critical,
          title: 'Budget Exceeded',
          message: '${budget.name} has exceeded its budget by \$${(spent - budget.budgetAmount).toStringAsFixed(2)}',
        ));
      } else if (percentage > 90) {
        alerts.add(BudgetAlert(
          id: '${budget.id}_warning',
          budgetId: budget.id,
          severity: AlertSeverity.warning,
          title: 'Budget Warning',
          message: '${budget.name} is at ${percentage.toStringAsFixed(1)}% of budget',
        ));
      } else if (percentage > 80) {
        alerts.add(BudgetAlert(
          id: '${budget.id}_caution',
          budgetId: budget.id,
          severity: AlertSeverity.info,
          title: 'Budget Caution',
          message: '${budget.name} is approaching budget limit (${percentage.toStringAsFixed(1)}%)',
        ));
      }
    }
    
    return alerts;
  }

  Color _getAlertColor(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.critical:
        return Colors.red;
      case AlertSeverity.warning:
        return Colors.orange;
      case AlertSeverity.info:
        return Colors.blue;
    }
  }

  IconData _getAlertIcon(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.critical:
        return Icons.error;
      case AlertSeverity.warning:
        return Icons.warning;
      case AlertSeverity.info:
        return Icons.info;
    }
  }

  void _updateDateRangeForPeriod(String period) {
    final now = DateTime.now();
    
    switch (period) {
      case 'This Week':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        _selectedDateRange = DateTimeRange(
          start: startOfWeek,
          end: startOfWeek.add(const Duration(days: 6)),
        );
        break;
      case 'This Month':
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0),
        );
        break;
      case 'This Quarter':
        final quarterStart = DateTime(now.year, ((now.month - 1) ~/ 3) * 3 + 1, 1);
        _selectedDateRange = DateTimeRange(
          start: quarterStart,
          end: DateTime(quarterStart.year, quarterStart.month + 3, 0),
        );
        break;
      case 'This Year':
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, 1, 1),
          end: DateTime(now.year, 12, 31),
        );
        break;
    }
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
        _selectedPeriod = 'Custom Range';
      });
    }
  }

  void _createBudget() {
    // TODO: Navigate to budget creation screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Budget creation screen coming soon'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _editBudget(Budget budget) {
    // TODO: Navigate to budget edit screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit budget: ${budget.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _viewBudgetTransactions(Budget budget, List<Transaction> transactions) {
    // TODO: Show budget transactions dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('View transactions for: ${budget.name}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _dismissAlert(BudgetAlert alert) {
    // TODO: Implement alert dismissal
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Alert dismissed'),
        backgroundColor: Colors.green,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatBudgetPeriod(Budget budget) {
    // TODO: Format budget period based on budget model
    return 'Monthly'; // Placeholder
  }
}

/// Budget alert model
class BudgetAlert {
  final String id;
  final int budgetId;
  final AlertSeverity severity;
  final String title;
  final String message;

  BudgetAlert({
    required this.id,
    required this.budgetId,
    required this.severity,
    required this.title,
    required this.message,
  });
}

/// Alert severity levels
enum AlertSeverity {
  info,
  warning,
  critical,
}
