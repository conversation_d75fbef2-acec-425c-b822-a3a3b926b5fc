import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/financial_goal.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Financial goal list provider
final financialGoalsProvider = StateNotifierProvider<FinancialGoalNotifier, List<FinancialGoal>>((ref) {
  return FinancialGoalNotifier(ref);
});

class FinancialGoalNotifier extends StateNotifier<List<FinancialGoal>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  FinancialGoalNotifier(this.ref) : super([]) {
    loadGoals();
  }

  Future<void> loadGoals() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final goals = await _db.getAllFinancialGoals(userId: userProfile.id.toString());
      state = goals;
    }
  }

  Future<void> addGoal(FinancialGoal goal) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      goal.userId = userProfile.id.toString();
      final savedGoal = await _db.saveFinancialGoal(goal);
      state = [...state, savedGoal];
    }
  }

  Future<void> updateGoal(FinancialGoal goal) async {
    final savedGoal = await _db.saveFinancialGoal(goal);
    state = state.map((g) => g.id == savedGoal.id ? savedGoal : g).toList();
  }

  Future<void> deleteGoal(int goalId) async {
    await _db.deleteFinancialGoal(goalId);
    state = state.where((g) => g.id != goalId).toList();
  }

  Future<void> addContribution(int goalId, double amount) async {
    final goalIndex = state.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = state[goalIndex];
      goal.addContribution(amount);
      await updateGoal(goal);
    }
  }

  Future<void> removeContribution(int goalId, double amount) async {
    final goalIndex = state.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = state[goalIndex];
      goal.removeContribution(amount);
      await updateGoal(goal);
    }
  }

  Future<void> completeGoal(int goalId) async {
    final goalIndex = state.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = state[goalIndex];
      goal.completeGoal();
      await updateGoal(goal);
    }
  }

  Future<void> pauseGoal(int goalId) async {
    final goalIndex = state.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = state[goalIndex];
      goal.pauseGoal();
      await updateGoal(goal);
    }
  }

  Future<void> resumeGoal(int goalId) async {
    final goalIndex = state.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = state[goalIndex];
      goal.resumeGoal();
      await updateGoal(goal);
    }
  }

  Future<void> cancelGoal(int goalId) async {
    final goalIndex = state.indexWhere((g) => g.id == goalId);
    if (goalIndex != -1) {
      final goal = state[goalIndex];
      goal.cancelGoal();
      await updateGoal(goal);
    }
  }

  List<FinancialGoal> getActiveGoals() {
    return state.where((goal) => goal.status == GoalStatus.active).toList();
  }

  List<FinancialGoal> getCompletedGoals() {
    return state.where((goal) => goal.status == GoalStatus.completed).toList();
  }

  List<FinancialGoal> getGoalsByType(GoalType type) {
    return state.where((goal) => goal.type == type).toList();
  }

  List<FinancialGoal> getOverdueGoals() {
    return state.where((goal) => goal.isOverdue).toList();
  }

  List<FinancialGoal> getGoalsNearDeadline({int days = 30}) {
    final cutoffDate = DateTime.now().add(Duration(days: days));
    return state.where((goal) => 
      goal.status == GoalStatus.active &&
      goal.targetDate.isBefore(cutoffDate) &&
      !goal.isCompleted
    ).toList();
  }

  double getTotalGoalAmount() {
    return getActiveGoals().fold(0.0, (sum, goal) => sum + goal.targetAmount);
  }

  double getTotalSavedAmount() {
    return getActiveGoals().fold(0.0, (sum, goal) => sum + goal.currentAmount);
  }

  double getTotalRemainingAmount() {
    return getActiveGoals().fold(0.0, (sum, goal) => sum + goal.remainingAmount);
  }

  double getAverageProgress() {
    final activeGoals = getActiveGoals();
    if (activeGoals.isEmpty) return 0.0;
    
    final totalProgress = activeGoals.fold(0.0, (sum, goal) => sum + goal.progressPercentage);
    return totalProgress / activeGoals.length;
  }
}

// Active goals provider
final activeGoalsProvider = Provider<List<FinancialGoal>>((ref) {
  final goals = ref.watch(financialGoalsProvider);
  return goals.where((goal) => goal.status == GoalStatus.active).toList();
});

// Completed goals provider
final completedGoalsProvider = Provider<List<FinancialGoal>>((ref) {
  final goals = ref.watch(financialGoalsProvider);
  return goals.where((goal) => goal.status == GoalStatus.completed).toList();
});

// Goals by type provider
final goalsByTypeProvider = Provider.family<List<FinancialGoal>, GoalType>((ref, type) {
  final goals = ref.watch(financialGoalsProvider);
  return goals.where((goal) => goal.type == type).toList();
});

// Overdue goals provider
final overdueGoalsProvider = Provider<List<FinancialGoal>>((ref) {
  final goals = ref.watch(financialGoalsProvider);
  return goals.where((goal) => goal.isOverdue).toList();
});

// Goal statistics provider
final goalStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final goals = ref.watch(financialGoalsProvider);
  final activeGoals = goals.where((g) => g.status == GoalStatus.active).toList();
  final completedGoals = goals.where((g) => g.status == GoalStatus.completed).toList();
  
  final totalTargetAmount = activeGoals.fold(0.0, (sum, g) => sum + g.targetAmount);
  final totalCurrentAmount = activeGoals.fold(0.0, (sum, g) => sum + g.currentAmount);
  final totalRemainingAmount = activeGoals.fold(0.0, (sum, g) => sum + g.remainingAmount);
  
  final averageProgress = activeGoals.isNotEmpty 
      ? activeGoals.fold(0.0, (sum, g) => sum + g.progressPercentage) / activeGoals.length
      : 0.0;
  
  final overdueCount = goals.where((g) => g.isOverdue).length;
  final nearDeadlineCount = goals.where((g) => 
    g.status == GoalStatus.active &&
    g.daysRemaining <= 30 &&
    g.daysRemaining > 0
  ).length;
  
  final typeStats = <GoalType, int>{};
  for (final goal in goals) {
    typeStats[goal.type] = (typeStats[goal.type] ?? 0) + 1;
  }
  
  return {
    'totalGoals': goals.length,
    'activeGoals': activeGoals.length,
    'completedGoals': completedGoals.length,
    'totalTargetAmount': totalTargetAmount,
    'totalCurrentAmount': totalCurrentAmount,
    'totalRemainingAmount': totalRemainingAmount,
    'averageProgress': averageProgress,
    'overdueCount': overdueCount,
    'nearDeadlineCount': nearDeadlineCount,
    'typeStats': typeStats,
  };
});
