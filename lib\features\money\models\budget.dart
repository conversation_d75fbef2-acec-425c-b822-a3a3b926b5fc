enum BudgetPeriod { weekly, monthly, quarterly, yearly }

enum BudgetStatus { onTrack, warning, exceeded }

class Budget {
  int id = 0;
  String name = '';
  String description = '';
  String category = '';
  double budgetAmount = 0.0;
  double spentAmount = 0.0;
  BudgetPeriod period = BudgetPeriod.monthly;
  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now();
  bool isActive = true;
  double warningThreshold = 0.8; // 80% of budget
  bool notificationsEnabled = true;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Budget();

  Budget.create({
    required this.name,
    required this.category,
    required this.budgetAmount,
    required this.period,
    required this.userId,
    this.description = '',
    DateTime? startDate,
    this.warningThreshold = 0.8,
    this.notificationsEnabled = true,
  }) {
    this.startDate = startDate ?? DateTime.now();
    _calculateEndDate();
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  String get periodDisplayName {
    switch (period) {
      case BudgetPeriod.weekly:
        return 'Weekly';
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.quarterly:
        return 'Quarterly';
      case BudgetPeriod.yearly:
        return 'Yearly';
    }
  }

  double get remainingAmount {
    return budgetAmount - spentAmount;
  }

  double get spentPercentage {
    if (budgetAmount == 0) return 0.0;
    return (spentAmount / budgetAmount).clamp(0.0, 1.0);
  }

  BudgetStatus get status {
    final percentage = spentPercentage;
    if (percentage >= 1.0) {
      return BudgetStatus.exceeded;
    } else if (percentage >= warningThreshold) {
      return BudgetStatus.warning;
    } else {
      return BudgetStatus.onTrack;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case BudgetStatus.onTrack:
        return 'On Track';
      case BudgetStatus.warning:
        return 'Warning';
      case BudgetStatus.exceeded:
        return 'Exceeded';
    }
  }

  String get formattedBudgetAmount {
    return '\$${budgetAmount.toStringAsFixed(2)}';
  }

  String get formattedSpentAmount {
    return '\$${spentAmount.toStringAsFixed(2)}';
  }

  String get formattedRemainingAmount {
    return '\$${remainingAmount.toStringAsFixed(2)}';
  }

  String get formattedDateRange {
    final start = '${startDate.day}/${startDate.month}/${startDate.year}';
    final end = '${endDate.day}/${endDate.month}/${endDate.year}';
    return '$start - $end';
  }

  bool get isCurrentPeriod {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  bool get isExpired {
    return DateTime.now().isAfter(endDate);
  }

  int get daysRemaining {
    if (isExpired) return 0;
    return endDate.difference(DateTime.now()).inDays;
  }

  double get dailyBudgetRemaining {
    if (daysRemaining <= 0) return 0.0;
    return remainingAmount / daysRemaining;
  }

  void addExpense(double amount) {
    spentAmount += amount;
    updateTimestamp();
  }

  void removeExpense(double amount) {
    spentAmount = (spentAmount - amount).clamp(0.0, double.infinity);
    updateTimestamp();
  }

  void resetSpentAmount() {
    spentAmount = 0.0;
    updateTimestamp();
  }

  void updateBudget({
    String? name,
    String? description,
    String? category,
    double? budgetAmount,
    BudgetPeriod? period,
    DateTime? startDate,
    double? warningThreshold,
    bool? notificationsEnabled,
    bool? isActive,
  }) {
    if (name != null) this.name = name;
    if (description != null) this.description = description;
    if (category != null) this.category = category;
    if (budgetAmount != null) this.budgetAmount = budgetAmount;
    if (period != null) {
      this.period = period;
      _calculateEndDate();
    }
    if (startDate != null) {
      this.startDate = startDate;
      _calculateEndDate();
    }
    if (warningThreshold != null) this.warningThreshold = warningThreshold;
    if (notificationsEnabled != null) this.notificationsEnabled = notificationsEnabled;
    if (isActive != null) this.isActive = isActive;
    
    updateTimestamp();
  }

  void _calculateEndDate() {
    switch (period) {
      case BudgetPeriod.weekly:
        endDate = startDate.add(const Duration(days: 7));
        break;
      case BudgetPeriod.monthly:
        endDate = DateTime(startDate.year, startDate.month + 1, startDate.day);
        break;
      case BudgetPeriod.quarterly:
        endDate = DateTime(startDate.year, startDate.month + 3, startDate.day);
        break;
      case BudgetPeriod.yearly:
        endDate = DateTime(startDate.year + 1, startDate.month, startDate.day);
        break;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'budgetAmount': budgetAmount,
      'spentAmount': spentAmount,
      'period': period.name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'warningThreshold': warningThreshold,
      'notificationsEnabled': notificationsEnabled,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Budget.fromJson(Map<String, dynamic> json) {
    final budget = Budget();
    budget.id = json['id'] ?? 0;
    budget.name = json['name'] ?? '';
    budget.description = json['description'] ?? '';
    budget.category = json['category'] ?? '';
    budget.budgetAmount = (json['budgetAmount'] ?? 0.0).toDouble();
    budget.spentAmount = (json['spentAmount'] ?? 0.0).toDouble();
    budget.period = BudgetPeriod.values.firstWhere(
      (p) => p.name == json['period'],
      orElse: () => BudgetPeriod.monthly,
    );
    budget.startDate = DateTime.parse(json['startDate']);
    budget.endDate = DateTime.parse(json['endDate']);
    budget.isActive = json['isActive'] ?? true;
    budget.warningThreshold = (json['warningThreshold'] ?? 0.8).toDouble();
    budget.notificationsEnabled = json['notificationsEnabled'] ?? true;
    budget.userId = json['userId'] ?? '';
    budget.createdAt = DateTime.parse(json['createdAt']);
    budget.updatedAt = DateTime.parse(json['updatedAt']);
    budget.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    budget.isDeleted = json['isDeleted'] ?? false;
    budget.syncId = json['syncId'];
    return budget;
  }
}
