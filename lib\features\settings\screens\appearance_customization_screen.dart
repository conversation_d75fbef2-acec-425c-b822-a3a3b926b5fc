import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../providers/appearance_settings_provider.dart';
import '../models/appearance_settings.dart' as app_settings;
// Supporting widgets will be created inline for now

/// Comprehensive appearance customization screen
class AppearanceCustomizationScreen extends ConsumerStatefulWidget {
  const AppearanceCustomizationScreen({super.key});

  @override
  ConsumerState<AppearanceCustomizationScreen> createState() => _AppearanceCustomizationScreenState();
}

class _AppearanceCustomizationScreenState extends ConsumerState<AppearanceCustomizationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(appearanceSettingsProvider);
    final notifier = ref.read(appearanceSettingsProvider.notifier);

    return StandardizedScaffold(
      title: 'Appearance Customization',
      currentRoute: '/settings/appearance',
      body: Column(
        children: [
          // Tab bar
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs: const [
                Tab(icon: Icon(Icons.palette), text: 'Colors'),
                Tab(icon: Icon(Icons.text_fields), text: 'Typography'),
                Tab(icon: Icon(Icons.tab), text: 'Tabs'),
                Tab(icon: Icon(Icons.border_style), text: 'Borders'),
                Tab(icon: Icon(Icons.space_bar), text: 'Spacing'),
                Tab(icon: Icon(Icons.apps), text: 'Icons'),
                Tab(icon: Icon(Icons.view_headline), text: 'Headers'),
                Tab(icon: Icon(Icons.animation), text: 'Animations'),
              ],
            ),
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildColorsTab(settings, notifier),
                _buildTypographyTab(settings, notifier),
                _buildTabsTab(settings, notifier),
                _buildBordersTab(settings, notifier),
                _buildSpacingTab(settings, notifier),
                _buildIconsTab(settings, notifier),
                _buildHeadersTab(settings, notifier),
                _buildAnimationsTab(settings, notifier),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showResetDialog(notifier),
        icon: const Icon(Icons.refresh),
        label: const Text('Reset All'),
      ),
    );
  }

  Widget _buildColorsTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Color Customization',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Primary Color',
          color: settings.primaryColor,
          onColorChanged: (color) => notifier.updateColorSettings(primaryColor: color),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Secondary Color',
          color: settings.secondaryColor,
          onColorChanged: (color) => notifier.updateColorSettings(secondaryColor: color),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Background Color',
          color: settings.backgroundColor,
          onColorChanged: (color) => notifier.updateColorSettings(backgroundColor: color),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Surface Color',
          color: settings.surfaceColor,
          onColorChanged: (color) => notifier.updateColorSettings(surfaceColor: color),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Error Color',
          color: settings.errorColor,
          onColorChanged: (color) => notifier.updateColorSettings(errorColor: color),
        ),
      ],
    );
  }

  Widget _buildTypographyTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Typography Settings',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Body Text Size',
          value: settings.bodyTextSize,
          min: 10.0,
          max: 24.0,
          divisions: 14,
          onChanged: (value) => notifier.updateTypographySettings(bodyTextSize: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Heading Text Size',
          value: settings.headingTextSize,
          min: 16.0,
          max: 36.0,
          divisions: 20,
          onChanged: (value) => notifier.updateTypographySettings(headingTextSize: value),
        ),
        const SizedBox(height: 16),
        
        DropdownSettingWidget<FontWeight>(
          title: 'Text Weight',
          value: settings.textWeight,
          items: const [
            DropdownMenuItem(value: FontWeight.w300, child: Text('Light')),
            DropdownMenuItem(value: FontWeight.normal, child: Text('Normal')),
            DropdownMenuItem(value: FontWeight.w500, child: Text('Medium')),
            DropdownMenuItem(value: FontWeight.bold, child: Text('Bold')),
            DropdownMenuItem(value: FontWeight.w900, child: Text('Black')),
          ],
          onChanged: (value) => notifier.updateTypographySettings(textWeight: value),
        ),
        const SizedBox(height: 16),
        
        DropdownSettingWidget<String>(
          title: 'Font Family',
          value: settings.fontFamily,
          items: const [
            DropdownMenuItem(value: 'Roboto', child: Text('Roboto')),
            DropdownMenuItem(value: 'Arial', child: Text('Arial')),
            DropdownMenuItem(value: 'Helvetica', child: Text('Helvetica')),
            DropdownMenuItem(value: 'Times New Roman', child: Text('Times New Roman')),
            DropdownMenuItem(value: 'Georgia', child: Text('Georgia')),
          ],
          onChanged: (value) => notifier.updateTypographySettings(fontFamily: value),
        ),
      ],
    );
  }

  Widget _buildTabsTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Tab Customization',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Tab Size',
          value: settings.tabSize,
          min: 32.0,
          max: 72.0,
          divisions: 20,
          onChanged: (value) => notifier.updateTabSettings(tabSize: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Tab Padding',
          value: settings.tabPadding,
          min: 8.0,
          max: 32.0,
          divisions: 24,
          onChanged: (value) => notifier.updateTabSettings(tabPadding: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Tab Border Radius',
          value: settings.tabBorderRadius,
          min: 0.0,
          max: 24.0,
          divisions: 24,
          onChanged: (value) => notifier.updateTabSettings(tabBorderRadius: value),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Active Tab Color',
          color: settings.tabActiveColor,
          onColorChanged: (color) => notifier.updateTabSettings(tabActiveColor: color),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Inactive Tab Color',
          color: settings.tabInactiveColor,
          onColorChanged: (color) => notifier.updateTabSettings(tabInactiveColor: color),
        ),
      ],
    );
  }

  Widget _buildBordersTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Border Settings',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Border Thickness',
          value: settings.borderThickness,
          min: 0.0,
          max: 8.0,
          divisions: 16,
          onChanged: (value) => notifier.updateBorderSettings(borderThickness: value),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Border Color',
          color: settings.borderColor,
          onColorChanged: (color) => notifier.updateBorderSettings(borderColor: color),
        ),
        const SizedBox(height: 16),
        
        DropdownSettingWidget<app_settings.BorderStyle>(
          title: 'Border Style',
          value: settings.borderStyle,
          items: const [
            DropdownMenuItem(value: app_settings.BorderStyle.solid, child: Text('Solid')),
            DropdownMenuItem(value: app_settings.BorderStyle.dashed, child: Text('Dashed')),
            DropdownMenuItem(value: app_settings.BorderStyle.dotted, child: Text('Dotted')),
            DropdownMenuItem(value: app_settings.BorderStyle.none, child: Text('None')),
          ],
          onChanged: (value) => notifier.updateBorderSettings(borderStyle: value),
        ),
      ],
    );
  }

  Widget _buildSpacingTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Spacing Controls',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Horizontal Margin',
          value: settings.marginHorizontal,
          min: 0.0,
          max: 32.0,
          divisions: 32,
          onChanged: (value) => notifier.updateSpacingSettings(marginHorizontal: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Vertical Margin',
          value: settings.marginVertical,
          min: 0.0,
          max: 32.0,
          divisions: 32,
          onChanged: (value) => notifier.updateSpacingSettings(marginVertical: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Horizontal Padding',
          value: settings.paddingHorizontal,
          min: 0.0,
          max: 32.0,
          divisions: 32,
          onChanged: (value) => notifier.updateSpacingSettings(paddingHorizontal: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Vertical Padding',
          value: settings.paddingVertical,
          min: 0.0,
          max: 32.0,
          divisions: 32,
          onChanged: (value) => notifier.updateSpacingSettings(paddingVertical: value),
        ),
      ],
    );
  }

  Widget _buildIconsTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Icon Settings',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Icon Size',
          value: settings.iconSize,
          min: 16.0,
          max: 48.0,
          divisions: 32,
          onChanged: (value) => notifier.updateIconSettings(iconSize: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Icon Spacing',
          value: settings.iconSpacing,
          min: 0.0,
          max: 24.0,
          divisions: 24,
          onChanged: (value) => notifier.updateIconSettings(iconSpacing: value),
        ),
        const SizedBox(height: 16),
        
        DropdownSettingWidget<app_settings.IconStyle>(
          title: 'Icon Style',
          value: settings.iconStyle,
          items: const [
            DropdownMenuItem(value: app_settings.IconStyle.filled, child: Text('Filled')),
            DropdownMenuItem(value: app_settings.IconStyle.outlined, child: Text('Outlined')),
            DropdownMenuItem(value: app_settings.IconStyle.rounded, child: Text('Rounded')),
            DropdownMenuItem(value: app_settings.IconStyle.sharp, child: Text('Sharp')),
            DropdownMenuItem(value: app_settings.IconStyle.twoTone, child: Text('Two Tone')),
          ],
          onChanged: (value) => notifier.updateIconSettings(iconStyle: value),
        ),
      ],
    );
  }

  Widget _buildHeadersTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Header Settings',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Header Height',
          value: settings.headerHeight,
          min: 40.0,
          max: 80.0,
          divisions: 40,
          onChanged: (value) => notifier.updateHeaderSettings(headerHeight: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Header Thickness',
          value: settings.headerThickness,
          min: 0.0,
          max: 8.0,
          divisions: 16,
          onChanged: (value) => notifier.updateHeaderSettings(headerThickness: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Header Text Size',
          value: settings.headerTextSize,
          min: 14.0,
          max: 28.0,
          divisions: 14,
          onChanged: (value) => notifier.updateHeaderSettings(headerTextSize: value),
        ),
        const SizedBox(height: 16),
        
        ColorPickerWidget(
          title: 'Header Background',
          color: settings.headerBackground,
          onColorChanged: (color) => notifier.updateHeaderSettings(headerBackground: color),
        ),
      ],
    );
  }

  Widget _buildAnimationsTab(app_settings.AppearanceSettings settings, AppearanceSettingsNotifier notifier) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Animation Settings',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        SwitchListTile(
          title: const Text('Enable Animations'),
          subtitle: const Text('Turn animations on or off globally'),
          value: settings.animationsEnabled,
          onChanged: (value) => notifier.updateAnimationSettings(animationsEnabled: value),
        ),
        const SizedBox(height: 16),
        
        SliderSettingWidget(
          title: 'Animation Speed (ms)',
          value: settings.animationSpeed,
          min: 100.0,
          max: 1000.0,
          divisions: 18,
          onChanged: (value) => notifier.updateAnimationSettings(animationSpeed: value),
        ),
      ],
    );
  }

  void _showResetDialog(AppearanceSettingsNotifier notifier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Settings'),
        content: const Text('Are you sure you want to reset all appearance settings to their defaults? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              notifier.resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All settings reset to defaults'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Reset All'),
          ),
        ],
      ),
    );
  }
}

/// Supporting widget for color picker
class ColorPickerWidget extends StatelessWidget {
  final String title;
  final Color color;
  final ValueChanged<Color> onColorChanged;

  const ColorPickerWidget({
    super.key,
    required this.title,
    required this.color,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(title),
      trailing: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey),
        ),
      ),
      onTap: () => _showColorPicker(context),
    );
  }

  void _showColorPicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select $title'),
        content: SingleChildScrollView(
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              Colors.red, Colors.pink, Colors.purple, Colors.deepPurple,
              Colors.indigo, Colors.blue, Colors.lightBlue, Colors.cyan,
              Colors.teal, Colors.green, Colors.lightGreen, Colors.lime,
              Colors.yellow, Colors.amber, Colors.orange, Colors.deepOrange,
              Colors.brown, Colors.grey, Colors.blueGrey, Colors.black,
            ].map((color) => GestureDetector(
              onTap: () {
                onColorChanged(color);
                Navigator.of(context).pop();
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: this.color == color ? Colors.white : Colors.transparent,
                    width: 2,
                  ),
                ),
              ),
            )).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

/// Supporting widget for slider settings
class SliderSettingWidget extends StatelessWidget {
  final String title;
  final double value;
  final double min;
  final double max;
  final int? divisions;
  final ValueChanged<double> onChanged;

  const SliderSettingWidget({
    super.key,
    required this.title,
    required this.value,
    required this.min,
    required this.max,
    this.divisions,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
            Text(value.toStringAsFixed(1), style: const TextStyle(color: Colors.grey)),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: onChanged,
        ),
      ],
    );
  }
}

/// Supporting widget for dropdown settings
class DropdownSettingWidget<T> extends StatelessWidget {
  final String title;
  final T value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?> onChanged;

  const DropdownSettingWidget({
    super.key,
    required this.title,
    required this.value,
    required this.items,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }
}
