import 'dart:math' as math;
import 'package:flutter/foundation.dart';

/// Comprehensive formula engine for Excel-like calculations
class FormulaEngine {
  static final FormulaEngine _instance = FormulaEngine._internal();
  factory FormulaEngine() => _instance;
  FormulaEngine._internal();

  final Map<String, dynamic> _cellValues = {};
  final Map<String, String> _cellFormulas = {};
  final Map<String, Set<String>> _dependencies = {};
  final Map<String, Set<String>> _dependents = {};

  /// Parse and evaluate a formula
  dynamic evaluateFormula(String formula, String cellAddress) {
    try {
      if (!formula.startsWith('=')) {
        return formula; // Not a formula, return as-is
      }

      final expression = formula.substring(1); // Remove '=' prefix
      final result = _parseExpression(expression, cellAddress);
      
      // Update dependencies
      _updateDependencies(cellAddress, expression);
      
      return result;
    } catch (e) {
      debugPrint('Formula evaluation error: $e');
      return '#ERROR!';
    }
  }

  /// Set cell value and recalculate dependents
  void setCellValue(String cellAddress, dynamic value) {
    _cellValues[cellAddress] = value;
    _recalculateDependents(cellAddress);
  }

  /// Set cell formula
  void setCellFormula(String cellAddress, String formula) {
    _cellFormulas[cellAddress] = formula;
    final result = evaluateFormula(formula, cellAddress);
    setCellValue(cellAddress, result);
  }

  /// Get cell value
  dynamic getCellValue(String cellAddress) {
    return _cellValues[cellAddress] ?? 0;
  }

  /// Get cell formula
  String? getCellFormula(String cellAddress) {
    return _cellFormulas[cellAddress];
  }

  /// Parse expression with function support
  dynamic _parseExpression(String expression, String cellAddress) {
    expression = expression.trim();
    
    // Handle functions
    if (expression.contains('(')) {
      return _parseFunction(expression, cellAddress);
    }
    
    // Handle cell references
    if (_isCellReference(expression)) {
      return getCellValue(expression);
    }
    
    // Handle arithmetic expressions
    return _parseArithmetic(expression, cellAddress);
  }

  /// Parse function calls
  dynamic _parseFunction(String expression, String cellAddress) {
    final functionMatch = RegExp(r'(\w+)\((.*)\)').firstMatch(expression);
    if (functionMatch == null) {
      return _parseArithmetic(expression, cellAddress);
    }

    final functionName = functionMatch.group(1)!.toUpperCase();
    final argsString = functionMatch.group(2)!;
    final args = _parseArguments(argsString, cellAddress);

    return _executeFunction(functionName, args);
  }

  /// Execute built-in functions
  dynamic _executeFunction(String functionName, List<dynamic> args) {
    switch (functionName) {
      // Mathematical Functions
      case 'SUM':
        return _sum(args);
      case 'AVERAGE':
        return _average(args);
      case 'COUNT':
        return _count(args);
      case 'MAX':
        return _max(args);
      case 'MIN':
        return _min(args);
      case 'ROUND':
        return _round(args);
      case 'ABS':
        return _abs(args);
      case 'SQRT':
        return _sqrt(args);
      case 'POWER':
        return _power(args);
      
      // Logical Functions
      case 'IF':
        return _if(args);
      case 'AND':
        return _and(args);
      case 'OR':
        return _or(args);
      case 'NOT':
        return _not(args);
      case 'IFERROR':
        return _ifError(args);
      
      // Lookup Functions
      case 'VLOOKUP':
        return _vlookup(args);
      case 'HLOOKUP':
        return _hlookup(args);
      case 'INDEX':
        return _index(args);
      case 'MATCH':
        return _match(args);
      
      // Text Functions
      case 'CONCATENATE':
        return _concatenate(args);
      case 'LEFT':
        return _left(args);
      case 'RIGHT':
        return _right(args);
      case 'MID':
        return _mid(args);
      case 'FIND':
        return _find(args);
      case 'LEN':
        return _len(args);
      case 'UPPER':
        return _upper(args);
      case 'LOWER':
        return _lower(args);
      
      // Date/Time Functions
      case 'TODAY':
        return _today();
      case 'NOW':
        return _now();
      case 'DATE':
        return _date(args);
      case 'TIME':
        return _time(args);
      case 'YEAR':
        return _year(args);
      case 'MONTH':
        return _month(args);
      case 'DAY':
        return _day(args);
      
      // Statistical Functions
      case 'STDEV':
        return _stdev(args);
      case 'VAR':
        return _var(args);
      case 'MEDIAN':
        return _median(args);
      case 'MODE':
        return _mode(args);
      
      default:
        return '#NAME?'; // Unknown function
    }
  }

  /// Parse function arguments
  List<dynamic> _parseArguments(String argsString, String cellAddress) {
    if (argsString.trim().isEmpty) return [];
    
    final args = <dynamic>[];
    final parts = argsString.split(',');
    
    for (final part in parts) {
      final trimmed = part.trim();
      
      // Handle range references (e.g., A1:A10)
      if (trimmed.contains(':')) {
        args.addAll(_expandRange(trimmed));
      } else {
        args.add(_parseExpression(trimmed, cellAddress));
      }
    }
    
    return args;
  }

  /// Expand cell range to individual values
  List<dynamic> _expandRange(String range) {
    final parts = range.split(':');
    if (parts.length != 2) return [];
    
    final startCell = parts[0].trim();
    final endCell = parts[1].trim();
    
    final startCoords = _parseCellAddress(startCell);
    final endCoords = _parseCellAddress(endCell);

    if (startCoords == null || endCoords == null) return [];

    final values = <dynamic>[];
    for (int row = startCoords['row']!; row <= endCoords['row']!; row++) {
      for (int col = startCoords['col']!; col <= endCoords['col']!; col++) {
        final cellAddress = _coordsToAddress(row, col);
        values.add(getCellValue(cellAddress));
      }
    }
    
    return values;
  }

  /// Parse arithmetic expressions
  dynamic _parseArithmetic(String expression, String cellAddress) {
    // Simple arithmetic parser (can be enhanced)
    expression = expression.replaceAllMapped(RegExp(r'[A-Z]+\d+'), (match) {
      final cellRef = match.group(0)!;
      return getCellValue(cellRef).toString();
    });
    
    // Handle basic operations
    if (expression.contains('+')) {
      final parts = expression.split('+');
      return _toNumber(parts[0]) + _toNumber(parts[1]);
    }
    if (expression.contains('-')) {
      final parts = expression.split('-');
      return _toNumber(parts[0]) - _toNumber(parts[1]);
    }
    if (expression.contains('*')) {
      final parts = expression.split('*');
      return _toNumber(parts[0]) * _toNumber(parts[1]);
    }
    if (expression.contains('/')) {
      final parts = expression.split('/');
      final divisor = _toNumber(parts[1]);
      return divisor != 0 ? _toNumber(parts[0]) / divisor : '#DIV/0!';
    }
    
    return _toNumber(expression);
  }

  /// Mathematical Functions Implementation
  double _sum(List<dynamic> args) {
    return args.fold(0.0, (sum, value) => sum + _toNumber(value));
  }

  double _average(List<dynamic> args) {
    if (args.isEmpty) return 0;
    return _sum(args) / args.length;
  }

  int _count(List<dynamic> args) {
    return args.where((value) => _isNumber(value)).length;
  }

  double _max(List<dynamic> args) {
    final numbers = args.map(_toNumber).where((n) => !n.isNaN);
    return numbers.isEmpty ? 0 : numbers.reduce(math.max);
  }

  double _min(List<dynamic> args) {
    final numbers = args.map(_toNumber).where((n) => !n.isNaN);
    return numbers.isEmpty ? 0 : numbers.reduce(math.min);
  }

  double _round(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final number = _toNumber(args[0]);
    final digits = args.length > 1 ? _toNumber(args[1]).toInt() : 0;
    final factor = math.pow(10, digits);
    return (number * factor).round() / factor;
  }

  double _abs(List<dynamic> args) {
    return args.isEmpty ? 0 : _toNumber(args[0]).abs();
  }

  double _sqrt(List<dynamic> args) {
    return args.isEmpty ? 0 : math.sqrt(_toNumber(args[0]));
  }

  double _power(List<dynamic> args) {
    if (args.length < 2) return 0;
    return math.pow(_toNumber(args[0]), _toNumber(args[1])).toDouble();
  }

  /// Logical Functions Implementation
  dynamic _if(List<dynamic> args) {
    if (args.length < 2) return '';
    final condition = _toBool(args[0]);
    return condition ? args[1] : (args.length > 2 ? args[2] : '');
  }

  bool _and(List<dynamic> args) {
    return args.every(_toBool);
  }

  bool _or(List<dynamic> args) {
    return args.any(_toBool);
  }

  bool _not(List<dynamic> args) {
    return args.isEmpty ? true : !_toBool(args[0]);
  }

  dynamic _ifError(List<dynamic> args) {
    if (args.isEmpty) return '';
    final value = args[0];
    return value.toString().startsWith('#') ? (args.length > 1 ? args[1] : '') : value;
  }

  /// Helper functions
  bool _isCellReference(String text) {
    return RegExp(r'^[A-Z]+\d+$').hasMatch(text.toUpperCase());
  }

  Map<String, int>? _parseCellAddress(String address) {
    final match = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(address.toUpperCase());
    if (match == null) return null;
    
    final colStr = match.group(1)!;
    final rowStr = match.group(2)!;
    
    int col = 0;
    for (int i = 0; i < colStr.length; i++) {
      col = col * 26 + (colStr.codeUnitAt(i) - 'A'.codeUnitAt(0) + 1);
    }
    
    return {'row': int.parse(rowStr), 'col': col};
  }

  String _coordsToAddress(int row, int col) {
    String colStr = '';
    while (col > 0) {
      col--;
      colStr = String.fromCharCode('A'.codeUnitAt(0) + (col % 26)) + colStr;
      col ~/= 26;
    }
    return '$colStr$row';
  }

  double _toNumber(dynamic value) {
    if (value is num) return value.toDouble();
    if (value is String) {
      final parsed = double.tryParse(value);
      return parsed ?? double.nan;
    }
    return double.nan;
  }

  bool _isNumber(dynamic value) {
    return value is num || (value is String && double.tryParse(value) != null);
  }

  bool _toBool(dynamic value) {
    if (value is bool) return value;
    if (value is num) return value != 0;
    if (value is String) return value.toLowerCase() == 'true';
    return false;
  }

  void _updateDependencies(String cellAddress, String expression) {
    // Clear existing dependencies
    _dependencies[cellAddress]?.clear();
    _dependencies[cellAddress] = <String>{};
    
    // Find cell references in expression
    final cellRefs = RegExp(r'[A-Z]+\d+').allMatches(expression);
    for (final match in cellRefs) {
      final refCell = match.group(0)!;
      _dependencies[cellAddress]!.add(refCell);
      _dependents[refCell] ??= <String>{};
      _dependents[refCell]!.add(cellAddress);
    }
  }

  void _recalculateDependents(String cellAddress) {
    final dependents = _dependents[cellAddress];
    if (dependents == null) return;
    
    for (final dependent in dependents) {
      final formula = _cellFormulas[dependent];
      if (formula != null) {
        final result = evaluateFormula(formula, dependent);
        _cellValues[dependent] = result;
        _recalculateDependents(dependent); // Recursive recalculation
      }
    }
  }

  /// Lookup Functions Implementation
  dynamic _vlookup(List<dynamic> args) {
    if (args.length < 3) return '#N/A';
    final lookupValue = args[0];
    final tableArray = args[1]; // Should be a range
    final colIndex = _toNumber(args[2]).toInt();
    final exactMatch = args.length > 3 ? _toBool(args[3]) : false;

    // Simplified implementation - would need proper range handling
    return '#N/A'; // Placeholder for complex lookup logic
  }

  dynamic _hlookup(List<dynamic> args) {
    if (args.length < 3) return '#N/A';
    // Similar to VLOOKUP but horizontal
    return '#N/A'; // Placeholder
  }

  dynamic _index(List<dynamic> args) {
    if (args.length < 2) return '#N/A';
    // Return value at specified row/column
    return '#N/A'; // Placeholder
  }

  dynamic _match(List<dynamic> args) {
    if (args.length < 2) return '#N/A';
    final lookupValue = args[0];
    final lookupArray = args[1];
    // Return position of value in array
    return 1; // Placeholder
  }

  /// Text Functions Implementation
  String _concatenate(List<dynamic> args) {
    return args.map((arg) => arg.toString()).join('');
  }

  String _left(List<dynamic> args) {
    if (args.isEmpty) return '';
    final text = args[0].toString();
    final numChars = args.length > 1 ? _toNumber(args[1]).toInt() : 1;
    return text.length >= numChars ? text.substring(0, numChars) : text;
  }

  String _right(List<dynamic> args) {
    if (args.isEmpty) return '';
    final text = args[0].toString();
    final numChars = args.length > 1 ? _toNumber(args[1]).toInt() : 1;
    return text.length >= numChars ? text.substring(text.length - numChars) : text;
  }

  String _mid(List<dynamic> args) {
    if (args.length < 2) return '';
    final text = args[0].toString();
    final start = _toNumber(args[1]).toInt() - 1; // Excel is 1-based
    final length = args.length > 2 ? _toNumber(args[2]).toInt() : text.length;

    if (start < 0 || start >= text.length) return '';
    final end = math.min(start + length, text.length);
    return text.substring(start, end);
  }

  int _find(List<dynamic> args) {
    if (args.length < 2) return 0;
    final findText = args[0].toString();
    final withinText = args[1].toString();
    final startNum = args.length > 2 ? _toNumber(args[2]).toInt() - 1 : 0;

    final index = withinText.indexOf(findText, startNum);
    return index >= 0 ? index + 1 : 0; // Excel is 1-based
  }

  int _len(List<dynamic> args) {
    return args.isEmpty ? 0 : args[0].toString().length;
  }

  String _upper(List<dynamic> args) {
    return args.isEmpty ? '' : args[0].toString().toUpperCase();
  }

  String _lower(List<dynamic> args) {
    return args.isEmpty ? '' : args[0].toString().toLowerCase();
  }

  /// Date/Time Functions Implementation
  DateTime _today() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  DateTime _now() {
    return DateTime.now();
  }

  DateTime _date(List<dynamic> args) {
    if (args.length < 3) return DateTime.now();
    final year = _toNumber(args[0]).toInt();
    final month = _toNumber(args[1]).toInt();
    final day = _toNumber(args[2]).toInt();
    return DateTime(year, month, day);
  }

  DateTime _time(List<dynamic> args) {
    if (args.length < 3) return DateTime.now();
    final hour = _toNumber(args[0]).toInt();
    final minute = _toNumber(args[1]).toInt();
    final second = _toNumber(args[2]).toInt();
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, hour, minute, second);
  }

  int _year(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().year;
    // Assume first argument is a date
    return DateTime.now().year; // Simplified
  }

  int _month(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().month;
    return DateTime.now().month; // Simplified
  }

  int _day(List<dynamic> args) {
    if (args.isEmpty) return DateTime.now().day;
    return DateTime.now().day; // Simplified
  }

  /// Statistical Functions Implementation
  double _stdev(List<dynamic> args) {
    if (args.length < 2) return 0;
    final numbers = args.map(_toNumber).where((n) => !n.isNaN).toList();
    if (numbers.length < 2) return 0;

    final mean = numbers.reduce((a, b) => a + b) / numbers.length;
    final variance = numbers.map((n) => math.pow(n - mean, 2)).reduce((a, b) => a + b) / (numbers.length - 1);
    return math.sqrt(variance);
  }

  double _var(List<dynamic> args) {
    if (args.length < 2) return 0;
    final numbers = args.map(_toNumber).where((n) => !n.isNaN).toList();
    if (numbers.length < 2) return 0;

    final mean = numbers.reduce((a, b) => a + b) / numbers.length;
    return numbers.map((n) => math.pow(n - mean, 2)).reduce((a, b) => a + b) / (numbers.length - 1);
  }

  double _median(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final numbers = args.map(_toNumber).where((n) => !n.isNaN).toList();
    if (numbers.isEmpty) return 0;

    numbers.sort();
    final middle = numbers.length ~/ 2;
    if (numbers.length % 2 == 0) {
      return (numbers[middle - 1] + numbers[middle]) / 2;
    } else {
      return numbers[middle];
    }
  }

  double _mode(List<dynamic> args) {
    if (args.isEmpty) return 0;
    final numbers = args.map(_toNumber).where((n) => !n.isNaN).toList();
    if (numbers.isEmpty) return 0;

    final frequency = <double, int>{};
    for (final num in numbers) {
      frequency[num] = (frequency[num] ?? 0) + 1;
    }

    int maxFreq = 0;
    double mode = 0;
    frequency.forEach((num, freq) {
      if (freq > maxFreq) {
        maxFreq = freq;
        mode = num;
      }
    });

    return maxFreq > 1 ? mode : 0; // Return 0 if no mode found
  }
}
