import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Comprehensive help system for spreadsheet formulas
class FormulaHelpSystem extends StatefulWidget {
  const FormulaHelpSystem({super.key});

  @override
  State<FormulaHelpSystem> createState() => _FormulaHelpSystemState();
}

class _FormulaHelpSystemState extends State<FormulaHelpSystem> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'All';
  FunctionReference? _selectedFunction;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 900,
        height: 700,
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: Row(
                children: [
                  // Sidebar with categories and search
                  SizedBox(width: 300, child: _buildSidebar()),
                  
                  // Divider
                  VerticalDivider(
                    width: 1,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  
                  // Main content area
                  Expanded(child: _buildMainContent()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.help_outline,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Formula Reference & Help',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search functions...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value.toLowerCase();
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // Categories
          Text(
            'Categories',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          Expanded(
            child: ListView(
              children: [
                _buildCategoryItem('All'),
                _buildCategoryItem('Mathematical'),
                _buildCategoryItem('Logical'),
                _buildCategoryItem('Text'),
                _buildCategoryItem('Date & Time'),
                _buildCategoryItem('Lookup & Reference'),
                _buildCategoryItem('Statistical'),
                _buildCategoryItem('Financial'),
                _buildCategoryItem('Engineering'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(String category) {
    final isSelected = _selectedCategory == category;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: ListTile(
        dense: true,
        selected: isSelected,
        selectedTileColor: Theme.of(context).colorScheme.primaryContainer,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        leading: Icon(
          _getCategoryIcon(category),
          size: 20,
          color: isSelected 
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        title: Text(
          category,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected 
              ? Theme.of(context).colorScheme.primary
              : null,
          ),
        ),
        trailing: Text(
          '${_getFunctionCount(category)}',
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        onTap: () {
          setState(() {
            _selectedCategory = category;
            _selectedFunction = null;
          });
        },
      ),
    );
  }

  Widget _buildMainContent() {
    if (_selectedFunction != null) {
      return _buildFunctionDetail(_selectedFunction!);
    }
    
    return _buildFunctionList();
  }

  Widget _buildFunctionList() {
    final filteredFunctions = _getFilteredFunctions();
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _selectedCategory == 'All' 
              ? 'All Functions (${filteredFunctions.length})'
              : '$_selectedCategory Functions (${filteredFunctions.length})',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Expanded(
            child: ListView.builder(
              itemCount: filteredFunctions.length,
              itemBuilder: (context, index) {
                final function = filteredFunctions[index];
                return _buildFunctionCard(function);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionCard(FunctionReference function) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getCategoryIcon(function.category),
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        title: Text(
          function.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontFamily: 'monospace',
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(function.description),
            const SizedBox(height: 4),
            Text(
              function.syntax,
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          setState(() {
            _selectedFunction = function;
          });
        },
      ),
    );
  }

  Widget _buildFunctionDetail(FunctionReference function) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          Row(
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedFunction = null;
                  });
                },
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              Text(
                function.name,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'monospace',
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => _copyToClipboard(function.syntax),
                icon: const Icon(Icons.copy),
                tooltip: 'Copy syntax',
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description
                  _buildSection('Description', function.description),
                  
                  // Syntax
                  _buildSection('Syntax', function.syntax, isCode: true),
                  
                  // Parameters
                  if (function.parameters.isNotEmpty) ...[
                    _buildSection('Parameters', ''),
                    ...function.parameters.map((param) => _buildParameterItem(param)),
                  ],
                  
                  // Examples
                  if (function.examples.isNotEmpty) ...[
                    _buildSection('Examples', ''),
                    ...function.examples.map((example) => _buildExampleItem(example)),
                  ],
                  
                  // Notes
                  if (function.notes.isNotEmpty)
                    _buildSection('Notes', function.notes),
                  
                  // Related functions
                  if (function.relatedFunctions.isNotEmpty) ...[
                    _buildSection('Related Functions', ''),
                    Wrap(
                      spacing: 8,
                      children: function.relatedFunctions.map((related) {
                        return Chip(
                          label: Text(related),
                          onDeleted: () {
                            // Navigate to related function
                            final relatedFunc = _allFunctions.firstWhere(
                              (f) => f.name == related,
                              orElse: () => function,
                            );
                            setState(() {
                              _selectedFunction = relatedFunc;
                            });
                          },
                          deleteIcon: const Icon(Icons.arrow_forward, size: 16),
                        );
                      }).toList(),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, String content, {bool isCode = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        if (content.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isCode 
                ? Theme.of(context).colorScheme.surfaceContainerHighest
                : null,
              borderRadius: BorderRadius.circular(8),
              border: isCode 
                ? Border.all(color: Theme.of(context).colorScheme.outline)
                : null,
            ),
            child: Text(
              content,
              style: TextStyle(
                fontFamily: isCode ? 'monospace' : null,
                color: isCode ? Theme.of(context).colorScheme.primary : null,
              ),
            ),
          ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildParameterItem(ParameterInfo param) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              param.name,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(param.description),
                if (param.type.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Type: ${param.type}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExampleItem(ExampleInfo example) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                example.formula,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => _copyToClipboard(example.formula),
                icon: const Icon(Icons.copy, size: 16),
                tooltip: 'Copy example',
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Result: ${example.result}',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (example.description.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              example.description,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ],
      ),
    );
  }

  List<FunctionReference> _getFilteredFunctions() {
    var functions = _allFunctions;
    
    // Filter by category
    if (_selectedCategory != 'All') {
      functions = functions.where((f) => f.category == _selectedCategory).toList();
    }
    
    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      functions = functions.where((f) {
        return f.name.toLowerCase().contains(_searchQuery) ||
               f.description.toLowerCase().contains(_searchQuery);
      }).toList();
    }
    
    return functions;
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Mathematical':
        return Icons.calculate;
      case 'Logical':
        return Icons.psychology;
      case 'Text':
        return Icons.text_fields;
      case 'Date & Time':
        return Icons.calendar_today;
      case 'Lookup & Reference':
        return Icons.search;
      case 'Statistical':
        return Icons.analytics;
      case 'Financial':
        return Icons.attach_money;
      case 'Engineering':
        return Icons.engineering;
      default:
        return Icons.functions;
    }
  }

  int _getFunctionCount(String category) {
    if (category == 'All') return _allFunctions.length;
    return _allFunctions.where((f) => f.category == category).length;
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied: $text'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// Function reference model
class FunctionReference {
  final String name;
  final String category;
  final String description;
  final String syntax;
  final List<ParameterInfo> parameters;
  final List<ExampleInfo> examples;
  final String notes;
  final List<String> relatedFunctions;

  const FunctionReference({
    required this.name,
    required this.category,
    required this.description,
    required this.syntax,
    this.parameters = const [],
    this.examples = const [],
    this.notes = '',
    this.relatedFunctions = const [],
  });
}

/// Parameter information model
class ParameterInfo {
  final String name;
  final String description;
  final String type;
  final bool required;

  const ParameterInfo({
    required this.name,
    required this.description,
    this.type = '',
    this.required = true,
  });
}

/// Example information model
class ExampleInfo {
  final String formula;
  final String result;
  final String description;

  const ExampleInfo({
    required this.formula,
    required this.result,
    this.description = '',
  });
}

/// Sample function references
final List<FunctionReference> _allFunctions = [
  FunctionReference(
    name: 'SUM',
    category: 'Mathematical',
    description: 'Adds all numbers in a range of cells',
    syntax: 'SUM(number1, [number2], ...)',
    parameters: [
      ParameterInfo(name: 'number1', description: 'The first number or range to add'),
      ParameterInfo(name: 'number2', description: 'Additional numbers or ranges to add', required: false),
    ],
    examples: [
      ExampleInfo(formula: '=SUM(A1:A10)', result: '55', description: 'Adds all values in range A1 to A10'),
      ExampleInfo(formula: '=SUM(1,2,3,4,5)', result: '15', description: 'Adds individual numbers'),
    ],
    notes: 'SUM ignores text values and empty cells. Use SUMIF for conditional sums.',
    relatedFunctions: ['AVERAGE', 'COUNT', 'SUMIF'],
  ),
  // Add more function references here...
];
