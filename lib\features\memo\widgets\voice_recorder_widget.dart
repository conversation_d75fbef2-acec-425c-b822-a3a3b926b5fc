import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../services/voice_recording_service.dart';
import '../providers/voice_memo_provider.dart';
import '../models/voice_memo.dart';

/// Widget for recording voice memos with Windows support
class VoiceRecorderWidget extends ConsumerStatefulWidget {
  const VoiceRecorderWidget({super.key});

  @override
  ConsumerState<VoiceRecorderWidget> createState() => _VoiceRecorderWidgetState();
}

class _VoiceRecorderWidgetState extends ConsumerState<VoiceRecorderWidget>
    with TickerProviderStateMixin {
  final VoiceRecordingService _recordingService = VoiceRecordingService();
  final TextEditingController _titleController = TextEditingController();
  
  late AnimationController _pulseController;
  late AnimationController _waveController;
  Timer? _durationTimer;
  
  Duration _recordingDuration = Duration.zero;
  bool _hasPermission = false;
  bool _isCheckingPermission = false;
  String _selectedCategory = 'general';
  Color _selectedColor = Colors.orange;

  final List<String> _categories = ['general', 'work', 'personal', 'ideas', 'meetings'];
  final List<Color> _colors = [
    Colors.orange,
    Colors.blue,
    Colors.green,
    Colors.purple,
    Colors.red,
    Colors.teal,
    Colors.amber,
    Colors.indigo,
  ];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _checkPermission();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _durationTimer?.cancel();
    _titleController.dispose();
    super.dispose();
  }

  Future<void> _checkPermission() async {
    setState(() => _isCheckingPermission = true);
    
    try {
      final hasPermission = await _recordingService.checkMicrophonePermission();
      setState(() {
        _hasPermission = hasPermission;
        _isCheckingPermission = false;
      });
    } catch (e) {
      setState(() {
        _hasPermission = false;
        _isCheckingPermission = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking microphone permission: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingPermission) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Checking microphone permission...'),
          ],
        ),
      );
    }

    if (!_hasPermission) {
      return _buildPermissionDeniedView();
    }

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Recording controls
          Expanded(
            flex: 3,
            child: _buildRecordingControls(),
          ),
          
          // Recording settings
          Expanded(
            flex: 2,
            child: _buildRecordingSettings(),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionDeniedView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic_off,
            size: 64,
            color: Colors.red.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 16),
          const Text(
            'Microphone Permission Required',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Please grant microphone permission to record voice memos.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _checkPermission,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Check Permission'),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingControls() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Recording visualization
        Stack(
          alignment: Alignment.center,
          children: [
            // Pulse animation
            if (_recordingService.isActive)
              AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Container(
                    width: 200 + (_pulseController.value * 50),
                    height: 200 + (_pulseController.value * 50),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _selectedColor.withValues(alpha: 0.3 - (_pulseController.value * 0.2)),
                    ),
                  );
                },
              ),
            
            // Main recording button
            GestureDetector(
              onTap: _toggleRecording,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _recordingService.isRecording ? Colors.red : _selectedColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  _recordingService.isRecording 
                      ? (_recordingService.isPaused ? Icons.play_arrow : Icons.stop)
                      : Icons.mic,
                  size: 48,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Recording duration
        Text(
          _formatDuration(_recordingDuration),
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'monospace',
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Recording status
        Text(
          _getRecordingStatus(),
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Control buttons
        if (_recordingService.isRecording) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                onPressed: _pauseResumeRecording,
                icon: Icon(
                  _recordingService.isPaused ? Icons.play_arrow : Icons.pause,
                  size: 32,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
              IconButton(
                onPressed: _stopAndSaveRecording,
                icon: const Icon(Icons.save, size: 32),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              IconButton(
                onPressed: _cancelRecording,
                icon: const Icon(Icons.delete, size: 32),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildRecordingSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recording Settings',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Title input
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Recording Title',
                hintText: 'Enter a title for your recording',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Category selection
            Row(
              children: [
                const Icon(Icons.category),
                const SizedBox(width: 8),
                const Text('Category:'),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButton<String>(
                    value: _selectedCategory,
                    isExpanded: true,
                    items: _categories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedCategory = value);
                      }
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Color selection
            Row(
              children: [
                const Icon(Icons.palette),
                const SizedBox(width: 8),
                const Text('Color:'),
                const SizedBox(width: 16),
                Expanded(
                  child: Wrap(
                    spacing: 8,
                    children: _colors.map((color) {
                      return GestureDetector(
                        onTap: () => setState(() => _selectedColor = color),
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                            border: _selectedColor == color
                                ? Border.all(color: Colors.black, width: 2)
                                : null,
                          ),
                          child: _selectedColor == color
                              ? const Icon(Icons.check, color: Colors.white, size: 16)
                              : null,
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _toggleRecording() async {
    if (_recordingService.isRecording) {
      await _stopAndSaveRecording();
    } else {
      await _startRecording();
    }
  }

  Future<void> _startRecording() async {
    try {
      final success = await _recordingService.startRecording();
      if (success) {
        _pulseController.repeat();
        _waveController.repeat();
        _startDurationTimer();
        setState(() {});
      } else {
        _showError('Failed to start recording');
      }
    } catch (e) {
      _showError('Error starting recording: $e');
    }
  }

  Future<void> _stopAndSaveRecording() async {
    try {
      final filePath = await _recordingService.stopRecording();
      if (filePath != null) {
        await _saveVoiceMemo(filePath);
        _resetRecording();
      } else {
        _showError('Failed to stop recording');
      }
    } catch (e) {
      _showError('Error stopping recording: $e');
    }
  }

  Future<void> _pauseResumeRecording() async {
    try {
      if (_recordingService.isPaused) {
        await _recordingService.resumeRecording();
        _pulseController.repeat();
      } else {
        await _recordingService.pauseRecording();
        _pulseController.stop();
      }
      setState(() {});
    } catch (e) {
      _showError('Error pausing/resuming recording: $e');
    }
  }

  Future<void> _cancelRecording() async {
    try {
      await _recordingService.stopRecording();
      _resetRecording();
    } catch (e) {
      _showError('Error canceling recording: $e');
    }
  }

  Future<void> _saveVoiceMemo(String filePath) async {
    final title = _titleController.text.trim().isEmpty 
        ? 'Recording ${DateTime.now().toString().substring(0, 16)}'
        : _titleController.text.trim();

    await ref.read(voiceMemosProvider.notifier).createVoiceMemo(
      title: title,
      filePath: filePath,
      durationMs: _recordingDuration.inMilliseconds,
      fileSizeBytes: 0.0, // TODO: Get actual file size
      description: 'Voice memo recorded with category: $_selectedCategory',
      audioFormat: 'aac',
      isFavorite: false,
      tags: [_selectedCategory],
    );
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Voice memo saved successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _resetRecording() {
    _pulseController.stop();
    _waveController.stop();
    _durationTimer?.cancel();
    setState(() {
      _recordingDuration = Duration.zero;
    });
    _titleController.clear();
  }

  void _startDurationTimer() {
    _durationTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordingDuration = _recordingService.currentRecordingDuration;
      });
    });
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  String _getRecordingStatus() {
    if (!_recordingService.isRecording) {
      return 'Tap to start recording';
    } else if (_recordingService.isPaused) {
      return 'Recording paused';
    } else {
      return 'Recording in progress...';
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
