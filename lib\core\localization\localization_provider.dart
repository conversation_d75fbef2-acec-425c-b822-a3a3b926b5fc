import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Supported locales
const List<Locale> supportedLocales = [
  Locale('en', 'US'), // English
  Locale('ar', 'SA'), // Arabic
];

// Default locale
const Locale defaultLocale = Locale('en', 'US');

// Locale provider
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  return LocaleNotifier();
});

class LocaleNotifier extends StateNotifier<Locale> {
  static const String _localeKey = 'selected_locale';
  
  LocaleNotifier() : super(defaultLocale) {
    _loadLocale();
  }

  Future<void> _loadLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localeCode = prefs.getString(_localeKey);
      
      if (localeCode != null) {
        final parts = localeCode.split('_');
        if (parts.length == 2) {
          final locale = Locale(parts[0], parts[1]);
          if (supportedLocales.contains(locale)) {
            state = locale;
          }
        }
      }
    } catch (e) {
      // If loading fails, keep default locale
    }
  }

  Future<void> setLocale(Locale locale) async {
    if (supportedLocales.contains(locale)) {
      state = locale;
      await _saveLocale(locale);
    }
  }

  Future<void> _saveLocale(Locale locale) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localeKey, '${locale.languageCode}_${locale.countryCode}');
    } catch (e) {
      // Handle save error silently
    }
  }

  bool get isArabic => state.languageCode == 'ar';
  bool get isEnglish => state.languageCode == 'en';
  
  TextDirection get textDirection => isArabic ? TextDirection.rtl : TextDirection.ltr;
}

// Text direction provider
final textDirectionProvider = Provider<TextDirection>((ref) {
  final locale = ref.watch(localeProvider);
  return locale.languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr;
});

// Is RTL provider
final isRTLProvider = Provider<bool>((ref) {
  final locale = ref.watch(localeProvider);
  return locale.languageCode == 'ar';
});

// Language name provider
final languageNameProvider = Provider<String>((ref) {
  final locale = ref.watch(localeProvider);
  switch (locale.languageCode) {
    case 'ar':
      return 'العربية';
    case 'en':
      return 'English';
    default:
      return 'English';
  }
});

// Locale resolution helper
class LocaleResolution {
  static Locale resolve(Locale? locale, Iterable<Locale> supportedLocales) {
    // If the locale is null, return the default locale
    if (locale == null) {
      return defaultLocale;
    }

    // Check if the exact locale is supported
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode &&
          supportedLocale.countryCode == locale.countryCode) {
        return supportedLocale;
      }
    }

    // Check if the language is supported (ignore country)
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return supportedLocale;
      }
    }

    // Return default locale if no match found
    return defaultLocale;
  }
}

// Language switching service
class LanguageService {
  static Future<void> switchLanguage(WidgetRef ref, Locale locale) async {
    await ref.read(localeProvider.notifier).setLocale(locale);
  }

  static Future<void> toggleLanguage(WidgetRef ref) async {
    final currentLocale = ref.read(localeProvider);
    final newLocale = currentLocale.languageCode == 'en' 
        ? const Locale('ar', 'SA')
        : const Locale('en', 'US');
    
    await switchLanguage(ref, newLocale);
  }

  static List<LanguageOption> getAvailableLanguages() {
    return [
      LanguageOption(
        locale: const Locale('en', 'US'),
        name: 'English',
        nativeName: 'English',
        flag: '🇺🇸',
      ),
      LanguageOption(
        locale: const Locale('ar', 'SA'),
        name: 'Arabic',
        nativeName: 'العربية',
        flag: '🇸🇦',
      ),
    ];
  }
}

class LanguageOption {
  final Locale locale;
  final String name;
  final String nativeName;
  final String flag;

  const LanguageOption({
    required this.locale,
    required this.name,
    required this.nativeName,
    required this.flag,
  });
}

// RTL-aware padding helper
class RTLPadding {
  static EdgeInsets symmetric({
    required BuildContext context,
    double horizontal = 0.0,
    double vertical = 0.0,
  }) {
    return EdgeInsets.symmetric(
      horizontal: horizontal,
      vertical: vertical,
    );
  }

  static EdgeInsets only({
    required BuildContext context,
    double start = 0.0,
    double end = 0.0,
    double top = 0.0,
    double bottom = 0.0,
  }) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    
    return EdgeInsets.only(
      left: isRTL ? end : start,
      right: isRTL ? start : end,
      top: top,
      bottom: bottom,
    );
  }

  static EdgeInsets fromLTRB({
    required BuildContext context,
    required double left,
    required double top,
    required double right,
    required double bottom,
  }) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    
    return EdgeInsets.fromLTRB(
      isRTL ? right : left,
      top,
      isRTL ? left : right,
      bottom,
    );
  }
}

// RTL-aware alignment helper
class RTLAlignment {
  static Alignment centerStart(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return isRTL ? Alignment.centerRight : Alignment.centerLeft;
  }

  static Alignment centerEnd(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return isRTL ? Alignment.centerLeft : Alignment.centerRight;
  }

  static Alignment topStart(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return isRTL ? Alignment.topRight : Alignment.topLeft;
  }

  static Alignment topEnd(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return isRTL ? Alignment.topLeft : Alignment.topRight;
  }

  static Alignment bottomStart(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return isRTL ? Alignment.bottomRight : Alignment.bottomLeft;
  }

  static Alignment bottomEnd(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return isRTL ? Alignment.bottomLeft : Alignment.bottomRight;
  }
}

// RTL-aware icon helper
class RTLIcon {
  static IconData arrow(BuildContext context, {bool forward = true}) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    
    if (forward) {
      return isRTL ? Icons.arrow_back : Icons.arrow_forward;
    } else {
      return isRTL ? Icons.arrow_forward : Icons.arrow_back;
    }
  }

  static IconData chevron(BuildContext context, {bool forward = true}) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    
    if (forward) {
      return isRTL ? Icons.chevron_left : Icons.chevron_right;
    } else {
      return isRTL ? Icons.chevron_right : Icons.chevron_left;
    }
  }
}
