import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/example_tools_service.dart';

/// Tool Builder main screen with tabbed interface
class ToolBuilderHomeScreen extends ConsumerStatefulWidget {
  const ToolBuilderHomeScreen({super.key});

  @override
  ConsumerState<ToolBuilderHomeScreen> createState() => _ToolBuilderHomeScreenState();
}

class _ToolBuilderHomeScreenState extends ConsumerState<ToolBuilderHomeScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tool Builder'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.add_circle), text: 'Create'),
            Tab(icon: Icon(Icons.grid_on), text: 'Backend'),
            Tab(icon: Icon(Icons.design_services), text: 'UI Builder'),
            Tab(icon: Icon(Icons.folder), text: 'My Tools'),
            Tab(icon: Icon(Icons.widgets), text: 'Templates'),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'import':
                  context.go('/tools/import-excel');
                  break;
                case 'settings':
                  _showSettings(context);
                  break;
                case 'help':
                  _showHelp(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'import',
                child: Row(
                  children: [
                    Icon(Icons.upload_file),
                    SizedBox(width: 8),
                    Text('Import Excel'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'help',
                child: Row(
                  children: [
                    Icon(Icons.help),
                    SizedBox(width: 8),
                    Text('Help'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDashboardTab(context),
          _buildCreateTab(context),
          _buildBackendTab(context),
          _buildUIBuilderTab(context),
          _buildMyToolsTab(context),
          _buildTemplatesTab(context),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 0: // Dashboard
        return FloatingActionButton(
          onPressed: () => _tabController.animateTo(1), // Go to Create tab
          tooltip: 'Create Tool',
          child: const Icon(Icons.add),
        );
      case 1: // Create
        return FloatingActionButton(
          onPressed: () => context.go('/tools/create'),
          tooltip: 'Start Creating',
          child: const Icon(Icons.create),
        );
      case 2: // Backend
        return FloatingActionButton(
          onPressed: () => context.go('/tools/backend-formula'),
          tooltip: 'Open Backend',
          child: const Icon(Icons.grid_on),
        );
      case 3: // UI Builder
        return FloatingActionButton(
          onPressed: () => context.go('/tools/ui-builder'),
          tooltip: 'Open UI Builder',
          child: const Icon(Icons.design_services),
        );
      case 4: // My Tools
        return FloatingActionButton(
          onPressed: () => _tabController.animateTo(1), // Go to Create tab
          tooltip: 'Create Tool',
          child: const Icon(Icons.add),
        );
      case 5: // Templates
        return FloatingActionButton(
          onPressed: () => context.go('/tools/templates'),
          tooltip: 'Browse Templates',
          child: const Icon(Icons.widgets),
        );
      default:
        return FloatingActionButton(
          onPressed: () => _tabController.animateTo(1),
          child: const Icon(Icons.add),
        );
    }
  }

  // Tab builders
  Widget _buildDashboardTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Icon(
                    Icons.build_circle,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Excel-Powered Tool Builder',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Create interactive tools with spreadsheet logic and visual UI builder',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Quick Actions
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _tabController.animateTo(1), // Go to Create tab
                  icon: const Icon(Icons.add),
                  label: const Text('Create New Tool'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => context.go('/tools/import-excel'),
                  icon: const Icon(Icons.upload_file),
                  label: const Text('Import Excel'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Recent Tools Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Recent Tools',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextButton(
                        onPressed: () => _tabController.animateTo(4), // Go to My Tools tab
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 200,
                    child: _buildRecentToolsList(context),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Quick Start Guide
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Quick Start Guide',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildQuickStartStep(
                    context,
                    '1. Create Tool',
                    'Start with a name and description',
                    Icons.create,
                  ),
                  _buildQuickStartStep(
                    context,
                    '2. Build Backend',
                    'Design spreadsheet logic with formulas',
                    Icons.grid_on,
                  ),
                  _buildQuickStartStep(
                    context,
                    '3. Design UI',
                    'Create visual interface with drag-and-drop',
                    Icons.design_services,
                  ),
                  _buildQuickStartStep(
                    context,
                    '4. Test & Share',
                    'Preview your tool and export for sharing',
                    Icons.share,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Create New Tool',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose how to start building your custom tool',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          // Creation Options
          _buildCreationOption(
            context,
            title: 'Start from Scratch',
            subtitle: 'Build a completely new tool with custom logic',
            icon: Icons.create,
            onTap: () => context.go('/tools/create'),
          ),
          const SizedBox(height: 16),
          _buildCreationOption(
            context,
            title: 'Import Excel File',
            subtitle: 'Convert existing Excel spreadsheet to interactive tool',
            icon: Icons.upload_file,
            onTap: () => context.go('/tools/import-excel'),
          ),
          const SizedBox(height: 16),
          _buildCreationOption(
            context,
            title: 'Use Template',
            subtitle: 'Start with pre-built templates for common use cases',
            icon: Icons.widgets,
            onTap: () => _tabController.animateTo(5), // Go to Templates tab
          ),
          const SizedBox(height: 16),
          _buildCreationOption(
            context,
            title: 'Clone Existing Tool',
            subtitle: 'Duplicate and modify one of your existing tools',
            icon: Icons.copy,
            onTap: () => _tabController.animateTo(4), // Go to My Tools tab
          ),
        ],
      ),
    );
  }

  Widget _buildBackendTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Backend Formula Engine',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Design your tool\'s logic with Excel-like formulas and data',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          ElevatedButton.icon(
            onPressed: () => context.go('/tools/backend-formula'),
            icon: const Icon(Icons.grid_on),
            label: const Text('Open Backend Formula Engine'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(16),
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Features',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildFeatureItem(Icons.functions, 'Excel-like formulas (SUM, VLOOKUP, etc.)'),
                  _buildFeatureItem(Icons.table_chart, 'Dynamic spreadsheet interface'),
                  _buildFeatureItem(Icons.edit, 'Direct data entry and editing'),
                  _buildFeatureItem(Icons.keyboard, 'Keyboard navigation (Enter, Tab, arrows)'),
                  _buildFeatureItem(Icons.undo, 'Comprehensive undo/redo system'),
                  _buildFeatureItem(Icons.settings, 'Custom matrix size configuration'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUIBuilderTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'UI Builder',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create beautiful user interfaces with drag-and-drop components',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          ElevatedButton.icon(
            onPressed: () => context.go('/tools/ui-builder'),
            icon: const Icon(Icons.design_services),
            label: const Text('Open UI Builder'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(16),
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Features',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildFeatureItem(Icons.drag_indicator, 'Drag-and-drop interface'),
                  _buildFeatureItem(Icons.palette, 'Full color customization'),
                  _buildFeatureItem(Icons.link, 'Cell binding with live data'),
                  _buildFeatureItem(Icons.select_all, 'Multi-cell selection support'),
                  _buildFeatureItem(Icons.preview, 'Real-time preview'),
                  _buildFeatureItem(Icons.widgets, 'Pre-built UI templates'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMyToolsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'My Tools',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _tabController.animateTo(1), // Go to Create tab
                icon: const Icon(Icons.add),
                label: const Text('Create'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Manage your saved tools and projects',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          // Tools list will be implemented here
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: _buildRecentToolsList(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Template Gallery',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start with professional templates for common use cases',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),

          // Quick Actions
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _loadExampleTools,
                  icon: const Icon(Icons.download),
                  label: const Text('Load Example Tools'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => context.go('/tools/templates'),
                  icon: const Icon(Icons.widgets),
                  label: const Text('Browse All'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Example Tools Section
          Text(
            'Example Tools',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ready-to-use tools with complete backend logic and UI',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),

          // Example Tools Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.1,
            children: [
              _buildExampleToolCard(context, 'Basic Calculator', Icons.calculate, 'Simple arithmetic operations'),
              _buildExampleToolCard(context, 'Unit Converter', Icons.swap_horiz, 'Convert between units'),
              _buildExampleToolCard(context, 'Budget Tracker', Icons.account_balance_wallet, 'Track income and expenses'),
              _buildExampleToolCard(context, 'Grade Calculator', Icons.school, 'Calculate final grades'),
              _buildExampleToolCard(context, 'Loan Calculator', Icons.home, 'Calculate loan payments'),
              _buildExampleToolCard(context, 'Tip Calculator', Icons.restaurant, 'Calculate tips and totals'),
              _buildExampleToolCard(context, 'BMI Calculator', Icons.fitness_center, 'Calculate body mass index'),
              _buildExampleToolCard(context, 'Compound Interest', Icons.trending_up, 'Investment growth calculator'),
            ],
          ),

          const SizedBox(height: 24),

          // Template categories
          Text(
            'Template Categories',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildTemplateCard(context, 'Financial', Icons.attach_money, 'Budgets, loans, investments'),
              _buildTemplateCard(context, 'Business', Icons.business, 'Invoices, inventory, reports'),
              _buildTemplateCard(context, 'Personal', Icons.person, 'Fitness, goals, planning'),
              _buildTemplateCard(context, 'Education', Icons.school, 'Grades, schedules, tracking'),
            ],
          ),
        ],
      ),
    );
  }

  void _showSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tool Builder Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.auto_fix_high),
              title: Text('Auto-save'),
              subtitle: Text('Automatically save changes'),
              trailing: Switch(value: true, onChanged: null),
            ),
            ListTile(
              leading: Icon(Icons.grid_on),
              title: Text('Show grid'),
              subtitle: Text('Display grid lines in spreadsheet'),
              trailing: Switch(value: true, onChanged: null),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tool Builder Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Getting Started:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. Create a new tool with a name and description'),
              Text('2. Build the backend logic using spreadsheet formulas'),
              Text('3. Design the user interface with drag-and-drop'),
              Text('4. Preview and test your tool'),
              Text('5. Export or share your creation'),
              SizedBox(height: 16),
              Text(
                'Features:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Excel-like formula engine'),
              Text('• Visual UI builder'),
              Text('• Import/export Excel files'),
              Text('• Real-time calculations'),
              Text('• Custom styling and colors'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildRecentToolsList(BuildContext context) {
    // Placeholder for recent tools list - will be connected to provider later
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.build_circle_outlined,
            size: 48,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No tools yet',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first tool to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => context.go('/tools/create'),
            icon: const Icon(Icons.add),
            label: const Text('Create Tool'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStartStep(
    BuildContext context,
    String title,
    String description,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 16,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreationOption(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(BuildContext context, String title, IconData icon, String description) {
    return Card(
      child: InkWell(
        onTap: () => context.go('/tools/templates'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Load example tools into the app
  Future<void> _loadExampleTools() async {
    try {
      final exampleService = ExampleToolsService();

      // Check if example tools already exist
      final exists = await exampleService.exampleToolsExist();
      if (exists) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Example tools are already loaded'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Show loading dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Loading example tools...'),
              ],
            ),
          ),
        );
      }

      // Create all example tools
      final tools = await exampleService.createAllExampleTools();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${tools.length} example tools loaded successfully!'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'View Tools',
              onPressed: () => _tabController.animateTo(4), // Go to My Tools tab
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog if open

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load example tools: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Build example tool card
  Widget _buildExampleToolCard(BuildContext context, String title, IconData icon, String description) {
    return Card(
      child: InkWell(
        onTap: () {
          // Show tool preview dialog
          _showExampleToolPreview(context, title, description);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 28,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show example tool preview dialog
  void _showExampleToolPreview(BuildContext context, String title, String description) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.preview,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Text(
              'This example tool includes:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            _buildFeatureItem(Icons.grid_on, 'Complete backend spreadsheet logic'),
            _buildFeatureItem(Icons.design_services, 'Professional UI interface'),
            _buildFeatureItem(Icons.functions, 'Working formulas and calculations'),
            _buildFeatureItem(Icons.play_arrow, 'Ready to use immediately'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _loadExampleTools();
            },
            icon: const Icon(Icons.download),
            label: const Text('Load All Examples'),
          ),
        ],
      ),
    );
  }
}
