import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/conversion.dart';
import '../providers/conversion_provider.dart';

class UnitConverterScreen extends ConsumerStatefulWidget {
  final ConversionCategory category;

  const UnitConverterScreen({
    super.key,
    required this.category,
  });

  @override
  ConsumerState<UnitConverterScreen> createState() => _UnitConverterScreenState();
}

class _UnitConverterScreenState extends ConsumerState<UnitConverterScreen> {
  late TextEditingController _inputController;
  late ConversionCategory _selectedCategory;
  String _fromUnit = '';
  String _toUnit = '';
  double _inputValue = 0.0;
  double _outputValue = 0.0;
  List<ConversionUnit> _availableUnits = [];

  @override
  void initState() {
    super.initState();
    _inputController = TextEditingController();
    _selectedCategory = widget.category;
    _loadUnitsForCategory();
    
    // Set initial values
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(currentConversionProvider.notifier).updateCategory(_selectedCategory);
    });
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  void _loadUnitsForCategory() {
    final conversionService = ref.read(conversionServiceProvider);
    _availableUnits = conversionService.getUnitsForCategory(_selectedCategory);
    
    if (_availableUnits.isNotEmpty) {
      _fromUnit = _availableUnits.first.symbol;
      _toUnit = _availableUnits.length > 1 ? _availableUnits[1].symbol : _availableUnits.first.symbol;
    }
  }

  @override
  Widget build(BuildContext context) {
    final conversionService = ref.watch(conversionServiceProvider);
    final currentConversion = ref.watch(currentConversionProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('${conversionService.getCategoryDisplayName(_selectedCategory)} Converter'),
        actions: [
          IconButton(
            onPressed: _showHistory,
            icon: const Icon(Icons.history),
            tooltip: 'Conversion History',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category Selector
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Conversion Category',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<ConversionCategory>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: ConversionCategory.values.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(conversionService.getCategoryDisplayName(category)),
                        );
                      }).toList(),
                      onChanged: (category) {
                        if (category != null) {
                          setState(() {
                            _selectedCategory = category;
                            _loadUnitsForCategory();
                            _inputController.clear();
                            _inputValue = 0.0;
                            _outputValue = 0.0;
                          });
                          ref.read(currentConversionProvider.notifier).updateCategory(category);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'From',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: _inputController,
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              hintText: 'Enter value',
                            ),
                            onChanged: (value) {
                              final parsedValue = double.tryParse(value) ?? 0.0;
                              setState(() {
                                _inputValue = parsedValue;
                              });
                              ref.read(currentConversionProvider.notifier).updateInputValue(parsedValue);
                              _performConversion();
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _fromUnit.isNotEmpty ? _fromUnit : null,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            items: _availableUnits.map((unit) {
                              return DropdownMenuItem(
                                value: unit.symbol,
                                child: Text('${unit.name} (${unit.symbol})'),
                              );
                            }).toList(),
                            onChanged: (unit) {
                              if (unit != null) {
                                setState(() {
                                  _fromUnit = unit;
                                });
                                ref.read(currentConversionProvider.notifier).updateFromUnit(unit);
                                _performConversion();
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Swap Button
            Center(
              child: IconButton.filled(
                onPressed: _swapUnits,
                icon: const Icon(Icons.swap_vert),
                tooltip: 'Swap units',
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Output Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'To',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Theme.of(context).colorScheme.outline),
                              borderRadius: BorderRadius.circular(4),
                              color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    conversionService.formatResult(_outputValue),
                                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                      color: Theme.of(context).colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                IconButton(
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(
                                      text: conversionService.formatResult(_outputValue),
                                    ));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(content: Text('Result copied to clipboard')),
                                    );
                                  },
                                  icon: const Icon(Icons.copy),
                                  tooltip: 'Copy result',
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _toUnit.isNotEmpty ? _toUnit : null,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            items: _availableUnits.map((unit) {
                              return DropdownMenuItem(
                                value: unit.symbol,
                                child: Text('${unit.name} (${unit.symbol})'),
                              );
                            }).toList(),
                            onChanged: (unit) {
                              if (unit != null) {
                                setState(() {
                                  _toUnit = unit;
                                });
                                ref.read(currentConversionProvider.notifier).updateToUnit(unit);
                                _performConversion();
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quick Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quick Actions',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _saveConversion,
                          icon: const Icon(Icons.save),
                          label: const Text('Save'),
                        ),
                        ElevatedButton.icon(
                          onPressed: _clearAll,
                          icon: const Icon(Icons.clear),
                          label: const Text('Clear'),
                        ),
                        ElevatedButton.icon(
                          onPressed: _showCommonConversions,
                          icon: const Icon(Icons.list),
                          label: const Text('Common'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Conversion Formula (for educational purposes)
            if (_inputValue > 0) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Conversion Details',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${conversionService.formatResult(_inputValue)} $_fromUnit = ${conversionService.formatResult(_outputValue)} $_toUnit',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontFamily: 'monospace',
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getConversionExplanation(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _performConversion() {
    if (_fromUnit.isEmpty || _toUnit.isEmpty || _inputValue == 0) {
      setState(() {
        _outputValue = 0.0;
      });
      return;
    }

    try {
      final conversionService = ref.read(conversionServiceProvider);
      final result = conversionService.convert(_inputValue, _selectedCategory, _fromUnit, _toUnit);
      setState(() {
        _outputValue = result;
      });
    } catch (e) {
      setState(() {
        _outputValue = 0.0;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Conversion error: $e')),
      );
    }
  }

  void _swapUnits() {
    setState(() {
      final temp = _fromUnit;
      _fromUnit = _toUnit;
      _toUnit = temp;
      
      _inputController.text = ref.read(conversionServiceProvider).formatResult(_outputValue);
      _inputValue = _outputValue;
    });
    
    ref.read(currentConversionProvider.notifier).swapUnits();
    _performConversion();
  }

  void _saveConversion() {
    if (_inputValue == 0 || _outputValue == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please perform a conversion first')),
      );
      return;
    }

    final conversion = Conversion.create(
      category: _selectedCategory,
      fromUnit: _fromUnit,
      toUnit: _toUnit,
      inputValue: _inputValue,
      outputValue: _outputValue,
      userId: '', // Will be set by provider
    );

    ref.read(conversionsProvider.notifier).addConversion(conversion);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Conversion saved to history')),
    );
  }

  void _clearAll() {
    setState(() {
      _inputController.clear();
      _inputValue = 0.0;
      _outputValue = 0.0;
    });
    ref.read(currentConversionProvider.notifier).updateInputValue(0.0);
  }

  void _showHistory() {
    final conversions = ref.read(conversionsByCategoryProvider(_selectedCategory));
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.3,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Conversion History',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              Expanded(
                child: conversions.isEmpty
                    ? const Center(child: Text('No conversions yet'))
                    : ListView.builder(
                        controller: scrollController,
                        itemCount: conversions.length,
                        itemBuilder: (context, index) {
                          final conversion = conversions[index];
                          return ListTile(
                            title: Text(conversion.formattedConversion),
                            subtitle: Text(conversion.categoryDisplayName),
                            onTap: () {
                              // Load this conversion
                              setState(() {
                                _fromUnit = conversion.fromUnit;
                                _toUnit = conversion.toUnit;
                                _inputValue = conversion.inputValue;
                                _outputValue = conversion.outputValue;
                                _inputController.text = ref.read(conversionServiceProvider)
                                    .formatResult(conversion.inputValue);
                              });
                              Navigator.of(context).pop();
                            },
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCommonConversions() {
    final commonConversions = _getCommonConversions();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Common ${ref.read(conversionServiceProvider).getCategoryDisplayName(_selectedCategory)} Conversions'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: commonConversions.length,
            itemBuilder: (context, index) {
              final conversion = commonConversions[index];
              return ListTile(
                title: Text(conversion['title']!),
                subtitle: Text(conversion['description']!),
                onTap: () {
                  setState(() {
                    _fromUnit = conversion['from']!;
                    _toUnit = conversion['to']!;
                    _inputController.text = conversion['value']!;
                    _inputValue = double.parse(conversion['value']!);
                  });
                  _performConversion();
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  List<Map<String, String>> _getCommonConversions() {
    switch (_selectedCategory) {
      case ConversionCategory.length:
        return [
          {'title': '1 meter to feet', 'description': '1 m = 3.28 ft', 'from': 'm', 'to': 'ft', 'value': '1'},
          {'title': '1 inch to centimeters', 'description': '1 in = 2.54 cm', 'from': 'in', 'to': 'cm', 'value': '1'},
          {'title': '1 mile to kilometers', 'description': '1 mi = 1.61 km', 'from': 'mi', 'to': 'km', 'value': '1'},
        ];
      case ConversionCategory.weight:
        return [
          {'title': '1 kilogram to pounds', 'description': '1 kg = 2.20 lb', 'from': 'kg', 'to': 'lb', 'value': '1'},
          {'title': '1 pound to grams', 'description': '1 lb = 454 g', 'from': 'lb', 'to': 'g', 'value': '1'},
          {'title': '1 ounce to grams', 'description': '1 oz = 28.3 g', 'from': 'oz', 'to': 'g', 'value': '1'},
        ];
      case ConversionCategory.temperature:
        return [
          {'title': 'Freezing point', 'description': '0°C = 32°F', 'from': '°C', 'to': '°F', 'value': '0'},
          {'title': 'Boiling point', 'description': '100°C = 212°F', 'from': '°C', 'to': '°F', 'value': '100'},
          {'title': 'Room temperature', 'description': '20°C = 68°F', 'from': '°C', 'to': '°F', 'value': '20'},
        ];
      default:
        return [];
    }
  }

  String _getConversionExplanation() {
    if (_selectedCategory == ConversionCategory.temperature) {
      if (_fromUnit == '°C' && _toUnit == '°F') {
        return 'Formula: °F = (°C × 9/5) + 32';
      } else if (_fromUnit == '°F' && _toUnit == '°C') {
        return 'Formula: °C = (°F - 32) × 5/9';
      }
    }
    return 'Conversion performed using standard conversion factors';
  }
}
