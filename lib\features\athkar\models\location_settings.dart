import 'package:flutter/material.dart';

enum CalculationMethod {
  muslimWorldLeague,
  egyptian,
  karachi,
  ummAlQura,
  dubai,
  moonsightingCommittee,
  northAmerica,
  kuwait,
  qatar,
  singapore,
  tehran,
  turkey,
  jordan, // Default for Jordan
}

extension CalculationMethodExtension on CalculationMethod {
  String get displayName {
    switch (this) {
      case CalculationMethod.muslimWorldLeague:
        return 'Muslim World League';
      case CalculationMethod.egyptian:
        return 'Egyptian General Authority';
      case CalculationMethod.karachi:
        return 'University of Islamic Sciences, Karachi';
      case CalculationMethod.ummAlQura:
        return 'Umm Al-Qura University, Makkah';
      case CalculationMethod.dubai:
        return 'Dubai';
      case CalculationMethod.moonsightingCommittee:
        return 'Moonsighting Committee Worldwide';
      case CalculationMethod.northAmerica:
        return 'Islamic Society of North America';
      case CalculationMethod.kuwait:
        return 'Kuwait';
      case CalculationMethod.qatar:
        return 'Qatar';
      case CalculationMethod.singapore:
        return 'Singapore';
      case CalculationMethod.tehran:
        return 'Institute of Geophysics, University of Tehran';
      case CalculationMethod.turkey:
        return 'Diyanet İşleri Başkanlığı, Turkey';
      case CalculationMethod.jordan:
        return 'Ministry of Awqaf, Jordan';
    }
  }

  String get description {
    switch (this) {
      case CalculationMethod.muslimWorldLeague:
        return 'Fajr: 18°, Isha: 17°';
      case CalculationMethod.egyptian:
        return 'Fajr: 19.5°, Isha: 17.5°';
      case CalculationMethod.karachi:
        return 'Fajr: 18°, Isha: 18°';
      case CalculationMethod.ummAlQura:
        return 'Fajr: 18.5°, Isha: 90 min after Maghrib';
      case CalculationMethod.dubai:
        return 'Fajr: 18.2°, Isha: 18.2°';
      case CalculationMethod.moonsightingCommittee:
        return 'Fajr: 18°, Isha: 18°';
      case CalculationMethod.northAmerica:
        return 'Fajr: 15°, Isha: 15°';
      case CalculationMethod.kuwait:
        return 'Fajr: 18°, Isha: 17.5°';
      case CalculationMethod.qatar:
        return 'Fajr: 18°, Isha: 90 min after Maghrib';
      case CalculationMethod.singapore:
        return 'Fajr: 20°, Isha: 18°';
      case CalculationMethod.tehran:
        return 'Fajr: 17.7°, Isha: 14°';
      case CalculationMethod.turkey:
        return 'Fajr: 18°, Isha: 17°';
      case CalculationMethod.jordan:
        return 'Fajr: 18°, Isha: 18° (Official Jordan method)';
    }
  }
}

class LocationSettings {
  int id = 0;
  String name = '';
  double latitude = 31.9539; // Amman, Jordan default
  double longitude = 35.9106; // Amman, Jordan default
  String timezone = 'Asia/Amman'; // Jordan timezone default
  String country = 'Jordan';
  String city = 'Amman';
  CalculationMethod calculationMethod = CalculationMethod.jordan;
  
  // Prayer time adjustments (in minutes)
  int fajrAdjustment = 0;
  int dhuhrAdjustment = 0;
  int asrAdjustment = 0;
  int maghribAdjustment = 0;
  int ishaAdjustment = 0;
  
  // Advanced settings
  bool useHighLatitudeRule = false;
  int highLatitudeRule = 1; // 1: Middle of Night, 2: One Seventh, 3: Angle Based
  bool summerTimeAdjustment = true;
  
  // User and sync
  String userId = '';
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  LocationSettings();

  LocationSettings.create({
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.userId,
    this.timezone = 'Asia/Amman',
    this.country = 'Jordan',
    this.city = 'Amman',
    this.calculationMethod = CalculationMethod.jordan,
    this.fajrAdjustment = 0,
    this.dhuhrAdjustment = 0,
    this.asrAdjustment = 0,
    this.maghribAdjustment = 0,
    this.ishaAdjustment = 0,
    this.useHighLatitudeRule = false,
    this.highLatitudeRule = 1,
    this.summerTimeAdjustment = true,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  String get displayLocation => '$city, $country';
  String get coordinates => '${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)}';

  // Get popular Jordan cities
  static List<Map<String, dynamic>> getJordanCities() {
    return [
      {
        'name': 'Amman',
        'latitude': 31.9539,
        'longitude': 35.9106,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Zarqa',
        'latitude': 32.0728,
        'longitude': 36.0876,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Irbid',
        'latitude': 32.5556,
        'longitude': 35.8500,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Russeifa',
        'latitude': 32.0167,
        'longitude': 36.0500,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Aqaba',
        'latitude': 29.5321,
        'longitude': 35.0063,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Madaba',
        'latitude': 31.7167,
        'longitude': 35.7833,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Salt',
        'latitude': 32.0389,
        'longitude': 35.7272,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Mafraq',
        'latitude': 32.3436,
        'longitude': 36.2081,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Jerash',
        'latitude': 32.2811,
        'longitude': 35.8981,
        'timezone': 'Asia/Amman',
      },
      {
        'name': 'Karak',
        'latitude': 31.1833,
        'longitude': 35.7000,
        'timezone': 'Asia/Amman',
      },
    ];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'country': country,
      'city': city,
      'calculationMethod': calculationMethod.name,
      'fajrAdjustment': fajrAdjustment,
      'dhuhrAdjustment': dhuhrAdjustment,
      'asrAdjustment': asrAdjustment,
      'maghribAdjustment': maghribAdjustment,
      'ishaAdjustment': ishaAdjustment,
      'useHighLatitudeRule': useHighLatitudeRule,
      'highLatitudeRule': highLatitudeRule,
      'summerTimeAdjustment': summerTimeAdjustment,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory LocationSettings.fromJson(Map<String, dynamic> json) {
    final settings = LocationSettings();
    settings.id = json['id'] ?? 0;
    settings.name = json['name'] ?? '';
    settings.latitude = json['latitude']?.toDouble() ?? 31.9539;
    settings.longitude = json['longitude']?.toDouble() ?? 35.9106;
    settings.timezone = json['timezone'] ?? 'Asia/Amman';
    settings.country = json['country'] ?? 'Jordan';
    settings.city = json['city'] ?? 'Amman';
    settings.calculationMethod = CalculationMethod.values.firstWhere(
      (method) => method.name == json['calculationMethod'],
      orElse: () => CalculationMethod.jordan,
    );
    settings.fajrAdjustment = json['fajrAdjustment'] ?? 0;
    settings.dhuhrAdjustment = json['dhuhrAdjustment'] ?? 0;
    settings.asrAdjustment = json['asrAdjustment'] ?? 0;
    settings.maghribAdjustment = json['maghribAdjustment'] ?? 0;
    settings.ishaAdjustment = json['ishaAdjustment'] ?? 0;
    settings.useHighLatitudeRule = json['useHighLatitudeRule'] ?? false;
    settings.highLatitudeRule = json['highLatitudeRule'] ?? 1;
    settings.summerTimeAdjustment = json['summerTimeAdjustment'] ?? true;
    settings.userId = json['userId'] ?? '';
    settings.createdAt = DateTime.parse(json['createdAt']);
    settings.updatedAt = DateTime.parse(json['updatedAt']);
    settings.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    settings.isDeleted = json['isDeleted'] ?? false;
    settings.syncId = json['syncId'];
    return settings;
  }

  // Create default Jordan location
  static LocationSettings createDefaultJordan(String userId) {
    return LocationSettings.create(
      name: 'Amman, Jordan',
      latitude: 31.9539,
      longitude: 35.9106,
      userId: userId,
      timezone: 'Asia/Amman',
      country: 'Jordan',
      city: 'Amman',
      calculationMethod: CalculationMethod.jordan,
    );
  }
}
