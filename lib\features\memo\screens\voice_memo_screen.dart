import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/voice_memo_provider.dart';
import '../../../shared/providers/user_provider.dart';

class VoiceMemoScreen extends ConsumerStatefulWidget {
  const VoiceMemoScreen({super.key});

  @override
  ConsumerState<VoiceMemoScreen> createState() => _VoiceMemoScreenState();
}

class _VoiceMemoScreenState extends ConsumerState<VoiceMemoScreen> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isRecording = false;
  String? _recordedFilePath;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _startRecording() async {
    final success = await ref.read(recordingProvider.notifier).startRecording();
    if (success) {
      setState(() {
        _isRecording = true;
      });
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to start recording. Please check permissions.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    final filePath = await ref.read(recordingProvider.notifier).stopRecording();
    if (filePath != null) {
      setState(() {
        _isRecording = false;
        _recordedFilePath = filePath;
      });
    }
  }

  Future<void> _saveVoiceMemo() async {
    if (_recordedFilePath == null || _titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please provide a title and record audio'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final file = File(_recordedFilePath!);
      final fileSize = await file.length();
      final userId = ref.read(userProfileProvider)?.id.toString() ?? '';

      // For now, we'll estimate duration based on file size
      // In a real app, you'd want to get the actual duration
      final estimatedDuration = (fileSize / 1000).round(); // Rough estimate

      await ref.read(voiceMemosProvider.notifier).createVoiceMemo(
        title: _titleController.text.trim(),
        filePath: _recordedFilePath!,
        durationMs: estimatedDuration * 1000,
        fileSizeBytes: fileSize.toDouble(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Voice memo saved successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving voice memo: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _discardRecording() {
    if (_recordedFilePath != null) {
      final file = File(_recordedFilePath!);
      if (file.existsSync()) {
        file.deleteSync();
      }
    }
    setState(() {
      _recordedFilePath = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final recordingState = ref.watch(recordingProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Memo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_recordedFilePath != null)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveVoiceMemo,
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Recording Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Icon(
                      _isRecording ? Icons.mic : Icons.mic_none,
                      size: 80,
                      color: _isRecording ? Colors.red : Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _isRecording 
                          ? 'Recording...' 
                          : _recordedFilePath != null 
                              ? 'Recording Complete'
                              : 'Ready to Record',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _isRecording ? Colors.red : null,
                      ),
                    ),
                    if (_isRecording) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Duration: ${_formatDuration(recordingState.duration)}',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Recording Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (!_isRecording && _recordedFilePath != null)
                  ElevatedButton.icon(
                    onPressed: _discardRecording,
                    icon: const Icon(Icons.delete),
                    label: const Text('Discard'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                
                ElevatedButton.icon(
                  onPressed: _isRecording ? _stopRecording : _startRecording,
                  icon: Icon(_isRecording ? Icons.stop : Icons.mic),
                  label: Text(_isRecording ? 'Stop' : 'Record'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isRecording ? Colors.red : Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),

                if (!_isRecording && _recordedFilePath != null)
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Implement playback
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Playback feature coming soon'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Play'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 32),

            // Memo Details (only show if recording is complete)
            if (_recordedFilePath != null) ...[
              Text(
                'Memo Details',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Title *',
                  border: OutlineInputBorder(),
                  hintText: 'Enter a title for your memo',
                ),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              TextField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                  hintText: 'Add a description...',
                ),
                maxLines: 3,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(height: 24),

              // Save Button
              ElevatedButton.icon(
                onPressed: _saveVoiceMemo,
                icon: const Icon(Icons.save),
                label: const Text('Save Voice Memo'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ],

            // Instructions (only show if no recording)
            if (!_isRecording && _recordedFilePath == null) ...[
              const Spacer(),
              Card(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tips for better recordings:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text('• Find a quiet environment'),
                      const Text('• Hold the device close to your mouth'),
                      const Text('• Speak clearly and at normal pace'),
                      const Text('• Tap record when ready to start'),
                    ],
                  ),
                ),
              ),
              const Spacer(),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
