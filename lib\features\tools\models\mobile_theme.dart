import 'package:flutter/material.dart';

/// Mobile theme model for ShadowSuite Tool Builder
class MobileTheme {
  final String id;
  final String name;
  final String description;
  final MobileThemeType type;
  final ColorScheme lightColorScheme;
  final ColorScheme darkColorScheme;
  final MobileLayoutConfig layoutConfig;
  final MobileTypography typography;
  final MobileComponents components;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime? modifiedAt;

  const MobileTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.lightColorScheme,
    required this.darkColorScheme,
    required this.layoutConfig,
    required this.typography,
    required this.components,
    this.isDefault = false,
    required this.createdAt,
    this.modifiedAt,
  });

  factory MobileTheme.fromJson(Map<String, dynamic> json) {
    return MobileTheme(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: MobileThemeType.values[json['type'] as int],
      lightColorScheme: _colorSchemeFromJson(json['light_color_scheme'] as Map<String, dynamic>),
      darkColorScheme: _colorSchemeFromJson(json['dark_color_scheme'] as Map<String, dynamic>),
      layoutConfig: MobileLayoutConfig.fromJson(json['layout_config'] as Map<String, dynamic>),
      typography: MobileTypography.fromJson(json['typography'] as Map<String, dynamic>),
      components: MobileComponents.fromJson(json['components'] as Map<String, dynamic>),
      isDefault: json['is_default'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      modifiedAt: json['modified_at'] != null ? DateTime.parse(json['modified_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'light_color_scheme': _colorSchemeToJson(lightColorScheme),
      'dark_color_scheme': _colorSchemeToJson(darkColorScheme),
      'layout_config': layoutConfig.toJson(),
      'typography': typography.toJson(),
      'components': components.toJson(),
      'is_default': isDefault,
      'created_at': createdAt.toIso8601String(),
      'modified_at': modifiedAt?.toIso8601String(),
    };
  }

  static ColorScheme _colorSchemeFromJson(Map<String, dynamic> json) {
    return ColorScheme(
      brightness: Brightness.values[json['brightness'] as int],
      primary: Color(json['primary'] as int),
      onPrimary: Color(json['on_primary'] as int),
      secondary: Color(json['secondary'] as int),
      onSecondary: Color(json['on_secondary'] as int),
      error: Color(json['error'] as int),
      onError: Color(json['on_error'] as int),
      surface: Color(json['surface'] as int),
      onSurface: Color(json['on_surface'] as int),
    );
  }

  static Map<String, dynamic> _colorSchemeToJson(ColorScheme scheme) {
    return {
      'brightness': scheme.brightness.index,
      'primary': scheme.primary.value,
      'on_primary': scheme.onPrimary.value,
      'secondary': scheme.secondary.value,
      'on_secondary': scheme.onSecondary.value,
      'error': scheme.error.value,
      'on_error': scheme.onError.value,
      'surface': scheme.surface.value,
      'on_surface': scheme.onSurface.value,
    };
  }
}

enum MobileThemeType {
  android,
  ios,
  hybrid,
}

/// Mobile layout configuration
class MobileLayoutConfig {
  final double screenPadding;
  final double cardSpacing;
  final double borderRadius;
  final double elevation;
  final bool useSystemBars;
  final bool enableGestures;
  final NavigationStyle navigationStyle;
  final AppBarStyle appBarStyle;
  final BottomBarStyle bottomBarStyle;

  const MobileLayoutConfig({
    this.screenPadding = 16.0,
    this.cardSpacing = 12.0,
    this.borderRadius = 12.0,
    this.elevation = 2.0,
    this.useSystemBars = true,
    this.enableGestures = true,
    this.navigationStyle = NavigationStyle.bottomNav,
    this.appBarStyle = AppBarStyle.elevated,
    this.bottomBarStyle = BottomBarStyle.floating,
  });

  factory MobileLayoutConfig.fromJson(Map<String, dynamic> json) {
    return MobileLayoutConfig(
      screenPadding: (json['screen_padding'] as num?)?.toDouble() ?? 16.0,
      cardSpacing: (json['card_spacing'] as num?)?.toDouble() ?? 12.0,
      borderRadius: (json['border_radius'] as num?)?.toDouble() ?? 12.0,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      useSystemBars: json['use_system_bars'] as bool? ?? true,
      enableGestures: json['enable_gestures'] as bool? ?? true,
      navigationStyle: NavigationStyle.values[json['navigation_style'] as int? ?? 0],
      appBarStyle: AppBarStyle.values[json['app_bar_style'] as int? ?? 0],
      bottomBarStyle: BottomBarStyle.values[json['bottom_bar_style'] as int? ?? 0],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'screen_padding': screenPadding,
      'card_spacing': cardSpacing,
      'border_radius': borderRadius,
      'elevation': elevation,
      'use_system_bars': useSystemBars,
      'enable_gestures': enableGestures,
      'navigation_style': navigationStyle.index,
      'app_bar_style': appBarStyle.index,
      'bottom_bar_style': bottomBarStyle.index,
    };
  }
}

/// Mobile typography configuration
class MobileTypography {
  final String fontFamily;
  final double scaleFactor;
  final FontWeight headingWeight;
  final FontWeight bodyWeight;
  final double lineHeight;
  final double letterSpacing;

  const MobileTypography({
    this.fontFamily = 'Roboto',
    this.scaleFactor = 1.0,
    this.headingWeight = FontWeight.w600,
    this.bodyWeight = FontWeight.w400,
    this.lineHeight = 1.4,
    this.letterSpacing = 0.0,
  });

  factory MobileTypography.fromJson(Map<String, dynamic> json) {
    return MobileTypography(
      fontFamily: json['font_family'] as String? ?? 'Roboto',
      scaleFactor: (json['scale_factor'] as num?)?.toDouble() ?? 1.0,
      headingWeight: FontWeight.values[json['heading_weight'] as int? ?? 5],
      bodyWeight: FontWeight.values[json['body_weight'] as int? ?? 3],
      lineHeight: (json['line_height'] as num?)?.toDouble() ?? 1.4,
      letterSpacing: (json['letter_spacing'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'font_family': fontFamily,
      'scale_factor': scaleFactor,
      'heading_weight': headingWeight.index,
      'body_weight': bodyWeight.index,
      'line_height': lineHeight,
      'letter_spacing': letterSpacing,
    };
  }
}

/// Mobile components configuration
class MobileComponents {
  final ButtonStyle buttonStyle;
  final InputStyle inputStyle;
  final CardStyle cardStyle;
  final ListStyle listStyle;
  final bool enableRipple;
  final bool enableShadows;
  final bool enableAnimations;

  const MobileComponents({
    this.buttonStyle = ButtonStyle.filled,
    this.inputStyle = InputStyle.outlined,
    this.cardStyle = CardStyle.elevated,
    this.listStyle = ListStyle.material,
    this.enableRipple = true,
    this.enableShadows = true,
    this.enableAnimations = true,
  });

  factory MobileComponents.fromJson(Map<String, dynamic> json) {
    return MobileComponents(
      buttonStyle: ButtonStyle.values[json['button_style'] as int? ?? 0],
      inputStyle: InputStyle.values[json['input_style'] as int? ?? 0],
      cardStyle: CardStyle.values[json['card_style'] as int? ?? 0],
      listStyle: ListStyle.values[json['list_style'] as int? ?? 0],
      enableRipple: json['enable_ripple'] as bool? ?? true,
      enableShadows: json['enable_shadows'] as bool? ?? true,
      enableAnimations: json['enable_animations'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'button_style': buttonStyle.index,
      'input_style': inputStyle.index,
      'card_style': cardStyle.index,
      'list_style': listStyle.index,
      'enable_ripple': enableRipple,
      'enable_shadows': enableShadows,
      'enable_animations': enableAnimations,
    };
  }
}

enum NavigationStyle {
  bottomNav,
  drawer,
  tabs,
  rail,
}

enum AppBarStyle {
  elevated,
  flat,
  transparent,
  gradient,
}

enum BottomBarStyle {
  floating,
  fixed,
  notched,
}

enum ButtonStyle {
  filled,
  outlined,
  text,
  elevated,
}

enum InputStyle {
  outlined,
  filled,
  underlined,
}

enum CardStyle {
  elevated,
  outlined,
  filled,
}

enum ListStyle {
  material,
  ios,
  custom,
}
