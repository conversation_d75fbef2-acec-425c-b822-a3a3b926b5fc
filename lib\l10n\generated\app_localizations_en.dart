// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'ShadowSuite';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get athkar => 'Athkar';

  @override
  String get quran => 'Quran';

  @override
  String get prayerTimes => 'Prayer Times';

  @override
  String get tools => 'Tools';

  @override
  String get memo => 'Memo Suite';

  @override
  String get moneyFlow => 'Money Flow';

  @override
  String get settings => 'Settings';

  @override
  String get profile => 'Profile';

  @override
  String get dhikr => 'Dhikr';

  @override
  String get progress => 'Progress';

  @override
  String get notes => 'Notes';

  @override
  String get todos => 'Todos';

  @override
  String get voiceMemos => 'Voice Memos';

  @override
  String get calculator => 'Calculator';

  @override
  String get converter => 'Converter';

  @override
  String get toolBuilder => 'Tool Builder';

  @override
  String get accounts => 'Accounts';

  @override
  String get transactions => 'Transactions';

  @override
  String get budgets => 'Budgets';

  @override
  String get reports => 'Reports';

  @override
  String get add => 'Add';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get refresh => 'Refresh';

  @override
  String get share => 'Share';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get backup => 'Backup';

  @override
  String get restore => 'Restore';

  @override
  String get sync => 'Sync';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signOut => 'Sign Out';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get name => 'Name';

  @override
  String get title => 'Title';

  @override
  String get description => 'Description';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get amount => 'Amount';

  @override
  String get category => 'Category';

  @override
  String get priority => 'Priority';

  @override
  String get status => 'Status';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get notifications => 'Notifications';

  @override
  String get privacy => 'Privacy';

  @override
  String get security => 'Security';

  @override
  String get about => 'About';

  @override
  String get version => 'Version';

  @override
  String get help => 'Help';

  @override
  String get support => 'Support';

  @override
  String get feedback => 'Feedback';

  @override
  String get rateApp => 'Rate App';

  @override
  String get translationSettings => 'Translation Settings';

  @override
  String get displayMode => 'Display Mode';

  @override
  String get arabicOnly => 'Arabic Only';

  @override
  String get arabicWithTranslation => 'Arabic + Translation';

  @override
  String get arabicWithTransliteration => 'Arabic + Transliteration';

  @override
  String get arabicWithBoth => 'Arabic + Translation + Transliteration';

  @override
  String get translationOnly => 'Translation Only';

  @override
  String get fontSize => 'Font Size';

  @override
  String get fontFamily => 'Font Family';

  @override
  String get showReference => 'Show Reference';

  @override
  String get showRecommendedCount => 'Show Recommended Count';

  @override
  String get rightToLeftText => 'Right-to-Left Text';

  @override
  String get resetToDefaults => 'Reset to Defaults';

  @override
  String completedTasks(int count) {
    return 'Completed Tasks ($count)';
  }

  @override
  String activeTasks(int count) {
    return 'Active Tasks ($count)';
  }

  @override
  String get clearCompleted => 'Clear Completed';

  @override
  String get duplicate => 'Duplicate';

  @override
  String get tapToCount => 'Tap to Count';

  @override
  String get completed => 'Completed!';

  @override
  String get nextPrayer => 'Next Prayer';

  @override
  String get todaysPrayerTimes => 'Today\'s Prayer Times';

  @override
  String get currentLocation => 'Current Location';

  @override
  String get locationSettings => 'Location Settings';

  @override
  String get fetchFromAPI => 'Fetch from API';

  @override
  String get useSample => 'Use Sample';

  @override
  String get updateTimes => 'Update Times';

  @override
  String get changeLocation => 'Change Location';
}
