import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/services/authentication_service.dart';

class AuthenticationScreen extends StatefulWidget {
  final VoidCallback? onAuthenticated;
  final bool canCancel;

  const AuthenticationScreen({
    super.key,
    this.onAuthenticated,
    this.canCancel = false,
  });

  @override
  State<AuthenticationScreen> createState() => _AuthenticationScreenState();
}

class _AuthenticationScreenState extends State<AuthenticationScreen> {
  final AuthenticationService _authService = AuthenticationService.instance;
  final TextEditingController _pinController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  
  AuthenticationMethod _authMethod = AuthenticationMethod.none;
  bool _isLoading = true;
  bool _isAuthenticating = false;
  bool _isAccountLocked = false;
  int _remainingLockoutMinutes = 0;
  String _errorMessage = '';
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  @override
  void dispose() {
    _pinController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _initializeAuth() async {
    try {
      final authMethod = await _authService.getAuthenticationMethod();
      final isLocked = await _authService.isAccountLocked();
      final remainingMinutes = await _authService.getRemainingLockoutMinutes();

      setState(() {
        _authMethod = authMethod;
        _isAccountLocked = isLocked;
        _remainingLockoutMinutes = remainingMinutes;
        _isLoading = false;
      });

      // Try biometric authentication if available and enabled
      if (!_isAccountLocked && 
          _authMethod != AuthenticationMethod.none &&
          await _authService.isBiometricEnabled() &&
          await _authService.isBiometricAvailable()) {
        _authenticateWithBiometrics();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to initialize authentication';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_isAccountLocked) {
      return _buildLockedScreen();
    }

    return Scaffold(
      appBar: widget.canCancel
          ? AppBar(
              title: const Text('Authentication Required'),
              backgroundColor: Colors.transparent,
              elevation: 0,
            )
          : null,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo/Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Icon(
                  Icons.security,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 32),
              
              Text(
                'ShadowSuite',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Please authenticate to continue',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 48),
              
              if (_errorMessage.isNotEmpty) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],
              
              // Authentication form based on method
              if (_authMethod == AuthenticationMethod.pin)
                _buildPinForm()
              else if (_authMethod == AuthenticationMethod.password)
                _buildPasswordForm()
              else
                _buildNoAuthMessage(),
              
              const SizedBox(height: 32),
              
              // Biometric authentication button
              if (_authMethod != AuthenticationMethod.none &&
                  !_isAccountLocked) ...[
                FutureBuilder<bool>(
                  future: _authService.isBiometricAvailable(),
                  builder: (context, snapshot) {
                    if (snapshot.data == true) {
                      return Column(
                        children: [
                          const Divider(),
                          const SizedBox(height: 16),
                          OutlinedButton.icon(
                            onPressed: _isAuthenticating ? null : _authenticateWithBiometrics,
                            icon: const Icon(Icons.fingerprint),
                            label: const Text('Use Biometric Authentication'),
                          ),
                        ],
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPinForm() {
    return Column(
      children: [
        TextField(
          controller: _pinController,
          decoration: const InputDecoration(
            labelText: 'Enter PIN',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.lock),
          ),
          keyboardType: TextInputType.number,
          obscureText: true,
          maxLength: 6,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          onSubmitted: (_) => _authenticateWithPin(),
        ),
        
        const SizedBox(height: 24),
        
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isAuthenticating ? null : _authenticateWithPin,
            child: _isAuthenticating
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Authenticate'),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordForm() {
    return Column(
      children: [
        TextField(
          controller: _passwordController,
          decoration: InputDecoration(
            labelText: 'Enter Password',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.lock),
            suffixIcon: IconButton(
              onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
              icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
            ),
          ),
          obscureText: _obscurePassword,
          onSubmitted: (_) => _authenticateWithPassword(),
        ),
        
        const SizedBox(height: 24),
        
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isAuthenticating ? null : _authenticateWithPassword,
            child: _isAuthenticating
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Authenticate'),
          ),
        ),
      ],
    );
  }

  Widget _buildNoAuthMessage() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(Icons.info, color: Colors.blue, size: 48),
            const SizedBox(height: 16),
            const Text(
              'No Authentication Set Up',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'You can set up authentication in the app settings for enhanced security.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: widget.onAuthenticated,
              child: const Text('Continue'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLockedScreen() {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.lock,
                size: 120,
                color: Colors.red,
              ),
              
              const SizedBox(height: 32),
              
              const Text(
                'Account Locked',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              
              const SizedBox(height: 16),
              
              Text(
                'Too many failed authentication attempts.\nPlease try again in $_remainingLockoutMinutes minutes.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              
              const SizedBox(height: 32),
              
              ElevatedButton(
                onPressed: _initializeAuth,
                child: const Text('Check Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _authenticateWithPin() async {
    if (_pinController.text.isEmpty) {
      setState(() => _errorMessage = 'Please enter your PIN');
      return;
    }

    setState(() {
      _isAuthenticating = true;
      _errorMessage = '';
    });

    try {
      final isAuthenticated = await _authService.authenticateWithPin(_pinController.text);
      
      if (isAuthenticated) {
        widget.onAuthenticated?.call();
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        setState(() {
          _errorMessage = 'Invalid PIN. Please try again.';
          _pinController.clear();
        });
        
        // Check if account is now locked
        final isLocked = await _authService.isAccountLocked();
        if (isLocked) {
          _initializeAuth();
        }
      }
    } catch (e) {
      setState(() => _errorMessage = 'Authentication failed. Please try again.');
    } finally {
      setState(() => _isAuthenticating = false);
    }
  }

  Future<void> _authenticateWithPassword() async {
    if (_passwordController.text.isEmpty) {
      setState(() => _errorMessage = 'Please enter your password');
      return;
    }

    setState(() {
      _isAuthenticating = true;
      _errorMessage = '';
    });

    try {
      final isAuthenticated = await _authService.authenticateWithPassword(_passwordController.text);
      
      if (isAuthenticated) {
        widget.onAuthenticated?.call();
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        setState(() {
          _errorMessage = 'Invalid password. Please try again.';
          _passwordController.clear();
        });
        
        // Check if account is now locked
        final isLocked = await _authService.isAccountLocked();
        if (isLocked) {
          _initializeAuth();
        }
      }
    } catch (e) {
      setState(() => _errorMessage = 'Authentication failed. Please try again.');
    } finally {
      setState(() => _isAuthenticating = false);
    }
  }

  Future<void> _authenticateWithBiometrics() async {
    setState(() {
      _isAuthenticating = true;
      _errorMessage = '';
    });

    try {
      final isAuthenticated = await _authService.authenticateWithBiometrics(
        reason: 'Please authenticate to access ShadowSuite',
      );
      
      if (isAuthenticated) {
        widget.onAuthenticated?.call();
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      setState(() => _errorMessage = 'Biometric authentication failed');
    } finally {
      setState(() => _isAuthenticating = false);
    }
  }
}
