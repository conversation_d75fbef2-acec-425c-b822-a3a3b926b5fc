import 'package:flutter/material.dart';
import '../models/custom_dhikr_routine.dart';

class DhikrStepEditorScreen extends StatefulWidget {
  final DhikrStep? step;
  final Function(DhikrStep) onSave;

  const DhikrStepEditorScreen({
    super.key,
    this.step,
    required this.onSave,
  });

  @override
  State<DhikrStepEditorScreen> createState() => _DhikrStepEditorScreenState();
}

class _DhikrStepEditorScreenState extends State<DhikrStepEditorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _arabicController = TextEditingController();
  final _transliterationController = TextEditingController();
  final _translationController = TextEditingController();
  final _reminderTextController = TextEditingController();
  final _targetCountController = TextEditingController();
  final _durationController = TextEditingController();

  late DhikrStepType _selectedType;
  late Color _selectedColor;
  late int _targetCount;
  late Duration _duration;

  @override
  void initState() {
    super.initState();
    
    if (widget.step != null) {
      _loadStepData();
    } else {
      _selectedType = DhikrStepType.dhikr;
      _selectedColor = Colors.blue;
      _targetCount = 33;
      _duration = const Duration(seconds: 30);
      _targetCountController.text = _targetCount.toString();
      _durationController.text = _duration.inSeconds.toString();
    }
  }

  void _loadStepData() {
    final step = widget.step!;
    _selectedType = step.type;
    _selectedColor = step.color;
    _targetCount = step.targetCount;
    _duration = step.duration;
    
    _arabicController.text = step.arabicText;
    _transliterationController.text = step.transliteration;
    _translationController.text = step.translation;
    _reminderTextController.text = step.reminderText;
    _targetCountController.text = _targetCount.toString();
    _durationController.text = _duration.inSeconds.toString();
  }

  @override
  void dispose() {
    _arabicController.dispose();
    _transliterationController.dispose();
    _translationController.dispose();
    _reminderTextController.dispose();
    _targetCountController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.step == null ? 'Add Step' : 'Edit Step'),
        actions: [
          IconButton(
            onPressed: _saveStep,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Step Type Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Step Type',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      ...DhikrStepType.values.map((type) => RadioListTile<DhikrStepType>(
                        title: Text(_getTypeDisplayName(type)),
                        subtitle: Text(_getTypeDescription(type)),
                        value: type,
                        groupValue: _selectedType,
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      )),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Color Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Color',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      Wrap(
                        spacing: 8,
                        children: [
                          Colors.blue,
                          Colors.green,
                          Colors.purple,
                          Colors.orange,
                          Colors.red,
                          Colors.teal,
                          Colors.indigo,
                          Colors.brown,
                        ].map((color) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedColor = color;
                              });
                            },
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: color,
                                shape: BoxShape.circle,
                                border: _selectedColor == color
                                    ? Border.all(color: Colors.black, width: 3)
                                    : null,
                              ),
                              child: _selectedColor == color
                                  ? const Icon(Icons.check, color: Colors.white)
                                  : null,
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Step Content
              if (_selectedType == DhikrStepType.dhikr) _buildDhikrContent(),
              if (_selectedType == DhikrStepType.pause) _buildPauseContent(),
              if (_selectedType == DhikrStepType.reminder) _buildReminderContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDhikrContent() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dhikr Content',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _arabicController,
              decoration: const InputDecoration(
                labelText: 'Arabic Text',
                border: OutlineInputBorder(),
                hintText: 'Enter Arabic dhikr text',
              ),
              textDirection: TextDirection.rtl,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter Arabic text';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _transliterationController,
              decoration: const InputDecoration(
                labelText: 'Transliteration',
                border: OutlineInputBorder(),
                hintText: 'Enter transliteration',
              ),
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _translationController,
              decoration: const InputDecoration(
                labelText: 'Translation',
                border: OutlineInputBorder(),
                hintText: 'Enter English translation',
              ),
              maxLines: 2,
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _targetCountController,
              decoration: const InputDecoration(
                labelText: 'Target Count',
                border: OutlineInputBorder(),
                hintText: 'How many times to recite',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter target count';
                }
                final count = int.tryParse(value);
                if (count == null || count <= 0) {
                  return 'Please enter a valid number';
                }
                return null;
              },
              onChanged: (value) {
                final count = int.tryParse(value);
                if (count != null) {
                  _targetCount = count;
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPauseContent() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pause Duration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _durationController,
              decoration: const InputDecoration(
                labelText: 'Duration (seconds)',
                border: OutlineInputBorder(),
                hintText: 'Enter pause duration in seconds',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter duration';
                }
                final seconds = int.tryParse(value);
                if (seconds == null || seconds <= 0) {
                  return 'Please enter a valid number';
                }
                return null;
              },
              onChanged: (value) {
                final seconds = int.tryParse(value);
                if (seconds != null) {
                  _duration = Duration(seconds: seconds);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderContent() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reminder Text',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _reminderTextController,
              decoration: const InputDecoration(
                labelText: 'Reminder Message',
                border: OutlineInputBorder(),
                hintText: 'Enter reminder message',
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter reminder text';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getTypeDisplayName(DhikrStepType type) {
    switch (type) {
      case DhikrStepType.dhikr:
        return 'Dhikr';
      case DhikrStepType.pause:
        return 'Pause';
      case DhikrStepType.reminder:
        return 'Reminder';
    }
  }

  String _getTypeDescription(DhikrStepType type) {
    switch (type) {
      case DhikrStepType.dhikr:
        return 'Recite dhikr with counter';
      case DhikrStepType.pause:
        return 'Take a timed break';
      case DhikrStepType.reminder:
        return 'Show a reminder message';
    }
  }

  void _saveStep() {
    if (_formKey.currentState!.validate()) {
      final step = DhikrStep.create(
        type: _selectedType,
        arabicText: _arabicController.text,
        transliteration: _transliterationController.text,
        translation: _translationController.text,
        reminderText: _reminderTextController.text,
        targetCount: _targetCount,
        duration: _duration,
        color: _selectedColor,
      );
      
      if (widget.step != null) {
        step.id = widget.step!.id;
      }
      
      widget.onSave(step);
      Navigator.of(context).pop();
    }
  }
}
