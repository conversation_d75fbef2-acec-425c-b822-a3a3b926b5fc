import 'package:flutter/material.dart';

class SettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final bool enabled;

  const SettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.enabled = true,
  });

  // Switch tile
  factory SettingsTile.switch_({
    required String title,
    String? subtitle,
    Widget? leading,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool enabled = true,
  }) {
    return SettingsTile(
      title: title,
      subtitle: subtitle,
      leading: leading,
      enabled: enabled,
      trailing: Switch(
        value: value,
        onChanged: enabled ? onChanged : null,
      ),
      onTap: enabled ? () => onChanged(!value) : null,
    );
  }

  // Navigation tile
  factory SettingsTile.navigation({
    required String title,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    required VoidCallback onTap,
    bool enabled = true,
  }) {
    return SettingsTile(
      title: title,
      subtitle: subtitle,
      leading: leading,
      trailing: trailing ?? const Icon(Icons.chevron_right),
      onTap: enabled ? onTap : null,
      enabled: enabled,
    );
  }

  // Dropdown tile
  static Widget dropdown<T>({
    required String title,
    String? subtitle,
    Widget? leading,
    required T value,
    required List<T> items,
    required Widget Function(T) itemBuilder,
    required ValueChanged<T> onChanged,
    bool enabled = true,
  }) {
    return SettingsTile(
      title: title,
      subtitle: subtitle,
      leading: leading,
      enabled: enabled,
      trailing: DropdownButton<T>(
        value: value,
        items: items.map((item) {
          return DropdownMenuItem<T>(
            value: item,
            child: itemBuilder(item),
          );
        }).toList(),
        onChanged: enabled ? (T? newValue) {
          if (newValue != null) onChanged(newValue);
        } : null,
        underline: const SizedBox(),
      ),
    );
  }

  // Slider tile
  static Widget slider({
    required String title,
    String? subtitle,
    Widget? leading,
    required double value,
    required double min,
    required double max,
    int? divisions,
    required ValueChanged<double> onChanged,
    bool enabled = true,
  }) {
    return _SliderSettingsTile(
      title: title,
      subtitle: subtitle,
      leading: leading,
      value: value,
      min: min,
      max: max,
      divisions: divisions,
      onChanged: onChanged,
      enabled: enabled,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: enabled ? null : Theme.of(context).disabledColor,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: TextStyle(
                color: enabled
                    ? Theme.of(context).textTheme.bodySmall?.color
                    : Theme.of(context).disabledColor,
              ),
            )
          : null,
      leading: leading,
      trailing: trailing,
      onTap: onTap,
      enabled: enabled,
    );
  }
}

class _SliderSettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final double value;
  final double min;
  final double max;
  final int? divisions;
  final ValueChanged<double> onChanged;
  final bool enabled;

  const _SliderSettingsTile({
    required this.title,
    this.subtitle,
    this.leading,
    required this.value,
    required this.min,
    required this.max,
    this.divisions,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          title: Text(
            title,
            style: TextStyle(
              color: enabled ? null : Theme.of(context).disabledColor,
            ),
          ),
          subtitle: subtitle != null
              ? Text(
                  subtitle!,
                  style: TextStyle(
                    color: enabled
                        ? Theme.of(context).textTheme.bodySmall?.color
                        : Theme.of(context).disabledColor,
                  ),
                )
              : null,
          leading: leading,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: enabled ? onChanged : null,
          ),
        ),
      ],
    );
  }
}
