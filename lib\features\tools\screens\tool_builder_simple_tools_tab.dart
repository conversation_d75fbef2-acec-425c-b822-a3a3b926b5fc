import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Simple Tools tab with pre-built calculators and converters
class ToolBuilderSimpleToolsTab extends ConsumerStatefulWidget {
  const ToolBuilderSimpleToolsTab({super.key});

  @override
  ConsumerState<ToolBuilderSimpleToolsTab> createState() => _ToolBuilderSimpleToolsTabState();
}

class _ToolBuilderSimpleToolsTabState extends ConsumerState<ToolBuilderSimpleToolsTab> {
  String _searchQuery = '';
  String _selectedCategory = 'All';

  final List<String> _categories = [
    'All',
    'Calculators',
    'Converters',
    'Financial',
    'Health',
    'Engineering',
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search and Filter Bar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            children: [
              // Search Bar
              TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Search tools...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
              ),
              const SizedBox(height: 12),
              
              // Category Filter
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _categories.map((category) {
                    final isSelected = _selectedCategory == category;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                        },
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        selectedColor: Theme.of(context).colorScheme.primaryContainer,
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        
        // Tools Grid
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Featured Tools Section
                Text(
                  'Featured Tools',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: _getCrossAxisCount(context),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.2,
                  children: [
                    _buildToolCard(
                      context,
                      'Basic Calculator',
                      'Simple arithmetic operations',
                      Icons.calculate,
                      Colors.blue,
                      () => context.go('/tools/calculator'),
                      isFeatured: true,
                    ),
                    _buildToolCard(
                      context,
                      'Unit Converter',
                      'Convert between different units',
                      Icons.swap_horiz,
                      Colors.green,
                      () => context.go('/tools/converter'),
                      isFeatured: true,
                    ),
                    _buildToolCard(
                      context,
                      'Scientific Calculator',
                      'Advanced mathematical functions',
                      Icons.functions,
                      Colors.purple,
                      () => context.go('/tools/scientific-calculator'),
                      isFeatured: true,
                    ),
                  ],
                ),
                
                const SizedBox(height: 32),
                
                // All Tools Section
                Text(
                  'All Tools',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: _getCrossAxisCount(context),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.2,
                  children: _getFilteredTools(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _getFilteredTools() {
    final allTools = [
      _ToolData('BMI Calculator', 'Calculate body mass index', Icons.fitness_center, Colors.orange, () => context.go('/tools/bmi-calculator'), 'Health'),
      _ToolData('Tip Calculator', 'Calculate tips and totals', Icons.restaurant, Colors.red, () => context.go('/tools/tip-calculator'), 'Financial'),
      _ToolData('Mortgage Calculator', 'Calculate loan payments', Icons.home, Colors.indigo, () => context.go('/tools/mortgage-calculator'), 'Financial'),
      _ToolData('Temperature Converter', 'Convert temperature units', Icons.thermostat, Colors.cyan, () => context.go('/tools/temperature-converter'), 'Converters'),
      _ToolData('Currency Converter', 'Convert between currencies', Icons.currency_exchange, Colors.amber, () => context.go('/tools/currency-converter'), 'Financial'),
      _ToolData('Area Calculator', 'Calculate area of shapes', Icons.crop_square, Colors.teal, () => context.go('/tools/area-calculator'), 'Engineering'),
      _ToolData('Percentage Calculator', 'Calculate percentages', Icons.percent, Colors.pink, () => context.go('/tools/percentage-calculator'), 'Calculators'),
      _ToolData('Age Calculator', 'Calculate age and dates', Icons.cake, Colors.brown, () => context.go('/tools/age-calculator'), 'Calculators'),
    ];

    return allTools
        .where((tool) {
          final matchesSearch = _searchQuery.isEmpty || 
              tool.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              tool.description.toLowerCase().contains(_searchQuery.toLowerCase());
          
          final matchesCategory = _selectedCategory == 'All' || tool.category == _selectedCategory;
          
          return matchesSearch && matchesCategory;
        })
        .map((tool) => _buildToolCard(
              context,
              tool.name,
              tool.description,
              tool.icon,
              tool.color,
              tool.onTap,
            ))
        .toList();
  }

  Widget _buildToolCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap, {
    bool isFeatured = false,
  }) {
    return Card(
      elevation: isFeatured ? 4 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isFeatured 
                ? Border.all(color: color.withValues(alpha: 0.3), width: 2)
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isFeatured)
                  Align(
                    alignment: Alignment.topRight,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'FEATURED',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    size: 32,
                    color: color,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }
}

class _ToolData {
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final String category;

  _ToolData(this.name, this.description, this.icon, this.color, this.onTap, this.category);
}
