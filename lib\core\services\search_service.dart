import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../database/database_service.dart';

enum SearchCategory {
  all,
  notes,
  todos,
  voiceMemos,
  accounts,
  transactions,
  categories,
  athkar,
  quran,
  tools,
}

class SearchResult {
  final String id;
  final String title;
  final String subtitle;
  final String content;
  final SearchCategory category;
  final DateTime? date;
  final Map<String, dynamic> metadata;
  final String route;

  SearchResult({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.content,
    required this.category,
    this.date,
    this.metadata = const {},
    required this.route,
  });

  String get categoryDisplayName {
    switch (category) {
      case SearchCategory.all:
        return 'All';
      case SearchCategory.notes:
        return 'Notes';
      case SearchCategory.todos:
        return 'Todos';
      case SearchCategory.voiceMemos:
        return 'Voice Memos';
      case SearchCategory.accounts:
        return 'Accounts';
      case SearchCategory.transactions:
        return 'Transactions';
      case SearchCategory.categories:
        return 'Categories';
      case SearchCategory.athkar:
        return 'Athkar';
      case SearchCategory.quran:
        return 'Quran';
      case SearchCategory.tools:
        return 'Tools';
    }
  }

  @override
  String toString() {
    return 'SearchResult(title: $title, category: $category)';
  }
}

class SearchService {
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  static SearchService get instance => _instance;

  final DatabaseService _db = DatabaseService.instance;

  /// Perform a comprehensive search across all app data
  Future<List<SearchResult>> search(
    String query, {
    SearchCategory category = SearchCategory.all,
    int limit = 50,
  }) async {
    if (query.trim().isEmpty) return [];

    final results = <SearchResult>[];
    final normalizedQuery = query.toLowerCase().trim();

    try {
      // Search in different categories based on the filter
      if (category == SearchCategory.all || category == SearchCategory.notes) {
        final noteResults = await _searchNotes(normalizedQuery, limit);
        results.addAll(noteResults);
      }

      if (category == SearchCategory.all || category == SearchCategory.todos) {
        final todoResults = await _searchTodos(normalizedQuery, limit);
        results.addAll(todoResults);
      }

      if (category == SearchCategory.all || category == SearchCategory.voiceMemos) {
        final voiceMemoResults = await _searchVoiceMemos(normalizedQuery, limit);
        results.addAll(voiceMemoResults);
      }

      if (category == SearchCategory.all || category == SearchCategory.accounts) {
        final accountResults = await _searchAccounts(normalizedQuery, limit);
        results.addAll(accountResults);
      }

      if (category == SearchCategory.all || category == SearchCategory.transactions) {
        final transactionResults = await _searchTransactions(normalizedQuery, limit);
        results.addAll(transactionResults);
      }

      if (category == SearchCategory.all || category == SearchCategory.categories) {
        final categoryResults = await _searchCategories(normalizedQuery, limit);
        results.addAll(categoryResults);
      }

      if (category == SearchCategory.all || category == SearchCategory.athkar) {
        final athkarResults = await _searchAthkar(normalizedQuery, limit);
        results.addAll(athkarResults);
      }

      if (category == SearchCategory.all || category == SearchCategory.quran) {
        final quranResults = await _searchQuran(normalizedQuery, limit);
        results.addAll(quranResults);
      }

      // Sort results by relevance and date
      results.sort((a, b) {
        // First, sort by relevance (exact matches first)
        final aExactMatch = a.title.toLowerCase().contains(normalizedQuery) ||
                           a.content.toLowerCase().contains(normalizedQuery);
        final bExactMatch = b.title.toLowerCase().contains(normalizedQuery) ||
                           b.content.toLowerCase().contains(normalizedQuery);
        
        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;
        
        // Then sort by date (newest first)
        if (a.date != null && b.date != null) {
          return b.date!.compareTo(a.date!);
        }
        if (a.date != null) return -1;
        if (b.date != null) return 1;
        
        // Finally, sort alphabetically
        return a.title.compareTo(b.title);
      });

      return results.take(limit).toList();
    } catch (e) {
      debugPrint('Error performing search: $e');
      return [];
    }
  }

  Future<List<SearchResult>> _searchNotes(String query, int limit) async {
    try {
      final notes = await _db.getAllNotes();
      final results = <SearchResult>[];

      for (final note in notes) {
        if (note.isDeleted) continue;
        
        final titleMatch = note.title.toLowerCase().contains(query);
        final contentMatch = note.content.toLowerCase().contains(query);
        
        if (titleMatch || contentMatch) {
          results.add(SearchResult(
            id: note.id.toString(),
            title: note.title.isEmpty ? 'Untitled Note' : note.title,
            subtitle: _truncateText(note.content, 100),
            content: note.content,
            category: SearchCategory.notes,
            date: note.updatedAt,
            metadata: {
              'noteId': note.id,
              'isPinned': note.isPinned,
              'tags': note.tags,
            },
            route: '/notes/${note.id}',
          ));
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error searching notes: $e');
      return [];
    }
  }

  Future<List<SearchResult>> _searchTodos(String query, int limit) async {
    try {
      final todos = await _db.getAllTodos();
      final results = <SearchResult>[];

      for (final todo in todos) {
        if (todo.isDeleted) continue;
        
        final titleMatch = todo.title.toLowerCase().contains(query);
        final descriptionMatch = todo.description.toLowerCase().contains(query);
        
        if (titleMatch || descriptionMatch) {
          results.add(SearchResult(
            id: todo.id.toString(),
            title: todo.title,
            subtitle: todo.description.isEmpty 
                ? (todo.isCompleted ? 'Completed' : 'Pending')
                : _truncateText(todo.description, 100),
            content: '${todo.title} ${todo.description}',
            category: SearchCategory.todos,
            date: todo.updatedAt,
            metadata: {
              'todoId': todo.id,
              'isCompleted': todo.isCompleted,
              'priority': todo.priority.name,
              'dueDate': todo.dueDate?.toIso8601String(),
            },
            route: '/todos/${todo.id}',
          ));
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error searching todos: $e');
      return [];
    }
  }

  Future<List<SearchResult>> _searchVoiceMemos(String query, int limit) async {
    try {
      final voiceMemos = await _db.getAllVoiceMemos();
      final results = <SearchResult>[];

      for (final memo in voiceMemos) {
        if (memo.isDeleted) continue;
        
        final titleMatch = memo.title.toLowerCase().contains(query);
        final transcriptionMatch = memo.transcription?.toLowerCase().contains(query) ?? false;
        
        if (titleMatch || transcriptionMatch) {
          results.add(SearchResult(
            id: memo.id.toString(),
            title: memo.title,
            subtitle: memo.transcription?.isEmpty ?? true 
                ? 'Voice memo • ${_formatDuration(memo.durationMs ~/ 1000)}'
                : _truncateText(memo.transcription!, 100),
            content: '${memo.title} ${memo.transcription ?? ''}',
            category: SearchCategory.voiceMemos,
            date: memo.updatedAt,
            metadata: {
              'memoId': memo.id,
              'duration': memo.durationMs,
              'filePath': memo.filePath,
            },
            route: '/voice-memos/${memo.id}',
          ));
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error searching voice memos: $e');
      return [];
    }
  }

  Future<List<SearchResult>> _searchAccounts(String query, int limit) async {
    try {
      final accounts = await _db.getAllAccounts();
      final results = <SearchResult>[];

      for (final account in accounts) {
        if (account.isDeleted) continue;
        
        final nameMatch = account.name.toLowerCase().contains(query);
        final descriptionMatch = account.description.toLowerCase().contains(query);
        
        if (nameMatch || descriptionMatch) {
          results.add(SearchResult(
            id: account.id.toString(),
            title: account.name,
            subtitle: '${account.type.name.toUpperCase()} • \$${account.balance.toStringAsFixed(2)}',
            content: '${account.name} ${account.description}',
            category: SearchCategory.accounts,
            date: account.updatedAt,
            metadata: {
              'accountId': account.id,
              'balance': account.balance,
              'type': account.type.name,
            },
            route: '/money/accounts/${account.id}',
          ));
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error searching accounts: $e');
      return [];
    }
  }

  Future<List<SearchResult>> _searchTransactions(String query, int limit) async {
    try {
      final transactions = await _db.getAllTransactions();
      final results = <SearchResult>[];

      for (final transaction in transactions) {
        if (transaction.isDeleted) continue;
        
        final descriptionMatch = transaction.description.toLowerCase().contains(query);
        final notesMatch = transaction.notes?.toLowerCase().contains(query) ?? false;
        
        if (descriptionMatch || notesMatch) {
          results.add(SearchResult(
            id: transaction.id.toString(),
            title: transaction.description,
            subtitle: '${transaction.type.name.toUpperCase()} • \$${transaction.amount.toStringAsFixed(2)}',
            content: '${transaction.description} ${transaction.notes}',
            category: SearchCategory.transactions,
            date: transaction.date,
            metadata: {
              'transactionId': transaction.id,
              'amount': transaction.amount,
              'type': transaction.type.name,
              'accountId': transaction.accountId,
            },
            route: '/money/transactions/${transaction.id}',
          ));
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error searching transactions: $e');
      return [];
    }
  }

  Future<List<SearchResult>> _searchCategories(String query, int limit) async {
    try {
      final categories = await _db.getAllCategories();
      final results = <SearchResult>[];

      for (final category in categories) {
        if (category.isDeleted) continue;
        
        final nameMatch = category.name.toLowerCase().contains(query);
        final descriptionMatch = category.description.toLowerCase().contains(query);
        
        if (nameMatch || descriptionMatch) {
          results.add(SearchResult(
            id: category.id.toString(),
            title: category.name,
            subtitle: '${category.type.name.toUpperCase()} Category',
            content: '${category.name} ${category.description}',
            category: SearchCategory.categories,
            date: category.updatedAt,
            metadata: {
              'categoryId': category.id,
              'type': category.type.name,
              'budgetLimit': category.budgetLimit,
            },
            route: '/money/categories/${category.id}',
          ));
        }
      }

      return results;
    } catch (e) {
      debugPrint('Error searching categories: $e');
      return [];
    }
  }

  Future<List<SearchResult>> _searchAthkar(String query, int limit) async {
    // This would search through dhikr routines and sessions
    // For now, return empty list as the implementation would be complex
    return [];
  }

  Future<List<SearchResult>> _searchQuran(String query, int limit) async {
    // This would search through Quran verses and translations
    // For now, return empty list as the implementation would be complex
    return [];
  }

  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Get search suggestions based on recent searches and popular content
  Future<List<String>> getSearchSuggestions(String query) async {
    final suggestions = <String>[];
    
    // Add some common search terms
    final commonTerms = [
      'notes',
      'todos',
      'voice memos',
      'accounts',
      'transactions',
      'income',
      'expenses',
      'budget',
      'prayer times',
      'dhikr',
      'quran',
    ];
    
    for (final term in commonTerms) {
      if (term.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(term);
      }
    }
    
    return suggestions.take(5).toList();
  }
}

// Provider for search service
final searchServiceProvider = Provider<SearchService>((ref) {
  return SearchService.instance;
});

// Provider for search results
final searchResultsProvider = FutureProvider.family<List<SearchResult>, Map<String, dynamic>>((ref, params) async {
  final searchService = ref.read(searchServiceProvider);
  final query = params['query'] as String;
  final category = params['category'] as SearchCategory? ?? SearchCategory.all;
  final limit = params['limit'] as int? ?? 50;
  
  return await searchService.search(query, category: category, limit: limit);
});

// Provider for search suggestions
final searchSuggestionsProvider = FutureProvider.family<List<String>, String>((ref, query) async {
  final searchService = ref.read(searchServiceProvider);
  return await searchService.getSearchSuggestions(query);
});
