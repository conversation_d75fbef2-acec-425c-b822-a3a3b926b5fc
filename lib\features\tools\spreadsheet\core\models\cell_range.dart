import 'package:flutter/foundation.dart';
import 'simple_cell.dart';

/// Simple cell range model
@immutable
class CellRange {
  final int startRow;
  final int startColumn;
  final int endRow;
  final int endColumn;

  const CellRange({
    required this.startRow,
    required this.startColumn,
    required this.endRow,
    required this.endColumn,
  });

  /// Create range from single cell
  factory CellRange.fromCell(String address) {
    final coords = CellCoordinates.fromAddress(address);
    return CellRange(
      startRow: coords.row,
      startColumn: coords.column,
      endRow: coords.row,
      endColumn: coords.column,
    );
  }

  /// Create entire row range
  factory CellRange.entireRow(int row) {
    return CellRange(
      startRow: row,
      startColumn: 1,
      endRow: row,
      endColumn: 16384, // Excel max columns
    );
  }

  /// Create entire column range
  factory CellRange.entireColumn(int column) {
    return CellRange(
      startRow: 1,
      startColumn: column,
      endRow: 1048576, // Excel max rows
      endColumn: column,
    );
  }

  /// Create range from A1 notation (e.g., "A1:B2")
  factory CellRange.fromA1Notation(String notation) {
    if (notation.contains(':')) {
      final parts = notation.split(':');
      final startCoords = CellCoordinates.fromAddress(parts[0]);
      final endCoords = CellCoordinates.fromAddress(parts[1]);
      return CellRange(
        startRow: startCoords.row,
        startColumn: startCoords.column,
        endRow: endCoords.row,
        endColumn: endCoords.column,
      );
    } else {
      // Single cell
      return CellRange.fromCell(notation);
    }
  }

  /// Get all cell addresses in range
  List<String> get addresses {
    final addresses = <String>[];
    for (int row = startRow; row <= endRow; row++) {
      for (int col = startColumn; col <= endColumn; col++) {
        addresses.add(CellCoordinates(row, col).address);
      }
    }
    return addresses;
  }

  /// Get cell count
  int get cellCount => (endRow - startRow + 1) * (endColumn - startColumn + 1);

  /// Check if single cell
  bool get isSingleCell => startRow == endRow && startColumn == endColumn;

  /// Check if entire row
  bool get isEntireRow => startColumn == 1 && endColumn == 16384;

  /// Check if entire column
  bool get isEntireColumn => startRow == 1 && endRow == 1048576;

  /// Get A1 notation (e.g., A1:B2)
  String get a1Notation {
    final startAddr = CellCoordinates(startRow, startColumn).address;
    if (isSingleCell) return startAddr;
    final endAddr = CellCoordinates(endRow, endColumn).address;
    return '$startAddr:$endAddr';
  }

  /// Check if contains another range
  bool containsRange(CellRange other) {
    return startRow <= other.startRow &&
        startColumn <= other.startColumn &&
        endRow >= other.endRow &&
        endColumn >= other.endColumn;
  }

  /// Check if contains cell
  bool containsCell(String address) {
    final coords = CellCoordinates.fromAddress(address);
    return coords.row >= startRow &&
        coords.row <= endRow &&
        coords.column >= startColumn &&
        coords.column <= endColumn;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CellRange &&
        other.startRow == startRow &&
        other.startColumn == startColumn &&
        other.endRow == endRow &&
        other.endColumn == endColumn;
  }

  @override
  int get hashCode => Object.hash(startRow, startColumn, endRow, endColumn);

  @override
  String toString() => a1Notation;
}