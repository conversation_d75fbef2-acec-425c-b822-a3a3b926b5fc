import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/ui_component.dart';

/// Enhanced property panel with comprehensive styling options
class PropertyPanel extends StatefulWidget {
  final UIComponent? selectedComponent;
  final Function(UIComponent) onComponentUpdated;
  final Function() onComponentDeleted;
  final List<String> availableCells;
  final Map<String, String> cellBindings;
  final Function(String, String) onCellBindingChanged;

  const PropertyPanel({
    super.key,
    required this.selectedComponent,
    required this.onComponentUpdated,
    required this.onComponentDeleted,
    required this.availableCells,
    required this.cellBindings,
    required this.onCellBindingChanged,
  });

  @override
  State<PropertyPanel> createState() => _PropertyPanelState();
}

class _PropertyPanelState extends State<PropertyPanel>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _labelController = TextEditingController();
  final _widthController = TextEditingController();
  final _heightController = TextEditingController();
  final _cellBindingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _updateControllers();
  }

  @override
  void didUpdateWidget(PropertyPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedComponent != widget.selectedComponent) {
      _updateControllers();
    }
  }

  void _updateControllers() {
    final component = widget.selectedComponent;
    if (component != null) {
      _labelController.text = component.label;
      _widthController.text = component.position.width.toString();
      _heightController.text = component.position.height.toString();
      _cellBindingController.text = widget.cellBindings[component.id] ?? '';
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _labelController.dispose();
    _widthController.dispose();
    _heightController.dispose();
    _cellBindingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.selectedComponent == null) {
      return const SizedBox.shrink();
    }

    return Container(
      width: 320,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          left: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(-2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(child: _buildTabContent()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final component = widget.selectedComponent!;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getComponentIcon(component.type),
            color: Theme.of(context).colorScheme.onPrimary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Properties',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _getComponentTypeName(component.type),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onComponentDeleted,
            icon: Icon(
              Icons.delete,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            tooltip: 'Delete Component',
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.settings), text: 'Basic'),
          Tab(icon: Icon(Icons.palette), text: 'Style'),
          Tab(icon: Icon(Icons.link), text: 'Data'),
          Tab(icon: Icon(Icons.animation), text: 'Effects'),
        ],
        labelStyle: const TextStyle(fontSize: 12),
        isScrollable: false,
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildBasicProperties(),
        _buildStyleProperties(),
        _buildDataProperties(),
        _buildEffectsProperties(),
      ],
    );
  }

  Widget _buildBasicProperties() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Label
        TextField(
          controller: _labelController,
          decoration: const InputDecoration(
            labelText: 'Label',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.label),
          ),
          onChanged: (value) => _updateComponent((component) => component.copyWith(label: value)),
        ),

        const SizedBox(height: 16),

        // Dimensions
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _widthController,
                decoration: const InputDecoration(
                  labelText: 'Width',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.width_normal),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                onChanged: (value) {
                  final width = double.tryParse(value);
                  if (width != null && width > 0) {
                    _updateComponent((component) => component.copyWith(
                      position: component.position.copyWith(width: width),
                    ));
                  }
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _heightController,
                decoration: const InputDecoration(
                  labelText: 'Height',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.height),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                onChanged: (value) {
                  final height = double.tryParse(value);
                  if (height != null && height > 0) {
                    _updateComponent((component) => component.copyWith(
                      position: component.position.copyWith(height: height),
                    ));
                  }
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Position
        Row(
          children: [
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'X Position',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.horizontal_rule),
                ),
                keyboardType: TextInputType.number,
                controller: TextEditingController(text: widget.selectedComponent!.position.x.toString()),
                onChanged: (value) {
                  final x = double.tryParse(value);
                  if (x != null) {
                    _updateComponent((component) => component.copyWith(
                      position: component.position.copyWith(x: x),
                    ));
                  }
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'Y Position',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.vertical_align_center),
                ),
                keyboardType: TextInputType.number,
                controller: TextEditingController(text: widget.selectedComponent!.position.y.toString()),
                onChanged: (value) {
                  final y = double.tryParse(value);
                  if (y != null) {
                    _updateComponent((component) => component.copyWith(
                      position: component.position.copyWith(y: y),
                    ));
                  }
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Cell Binding
        TextField(
          controller: _cellBindingController,
          decoration: const InputDecoration(
            labelText: 'Bind to Cell',
            hintText: 'e.g., A1, B2',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.link),
          ),
          onChanged: (value) {
            widget.onCellBindingChanged(widget.selectedComponent!.id, value);
          },
        ),

        const SizedBox(height: 16),

        // Component-specific properties
        ..._buildComponentSpecificProperties(),
      ],
    );
  }

  Widget _buildStyleProperties() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          'Style Properties',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Style properties will be available in a future update.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildDataProperties() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          'Data Properties',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Data binding and validation properties will be available in a future update.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildEffectsProperties() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          'Effects Properties',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Animation and visual effects will be available in a future update.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildComponentSpecificProperties() {
    return [
      Text(
        'Component-specific properties will be available in a future update.',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    ];
  }

  void _updateComponent(UIComponent Function(UIComponent) updater) {
    final component = widget.selectedComponent!;
    final updatedComponent = updater(component);
    widget.onComponentUpdated(updatedComponent);
  }

  IconData _getComponentIcon(ComponentType type) {
    switch (type) {
      case ComponentType.textField:
        return Icons.text_fields;
      case ComponentType.button:
        return Icons.smart_button;
      case ComponentType.label:
        return Icons.label;
      case ComponentType.dropdown:
        return Icons.arrow_drop_down;
      case ComponentType.checkbox:
        return Icons.check_box;
      case ComponentType.toggle:
        return Icons.toggle_on;
      default:
        return Icons.widgets;
    }
  }

  String _getComponentTypeName(ComponentType type) {
    return type.toString().split('.').last;
  }
}
