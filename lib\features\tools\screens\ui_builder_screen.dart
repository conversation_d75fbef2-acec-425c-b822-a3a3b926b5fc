import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;

import '../models/ui_component.dart';
import '../models/mobile_app_theme.dart';
import '../services/formula_engine_service.dart';
import '../services/mobile_app_theme_service.dart';
import '../widgets/mobile_component_renderer.dart';

/// Visual UI builder with drag-and-drop interface
class UIBuilderScreen extends ConsumerStatefulWidget {
  final int toolId;

  const UIBuilderScreen({
    super.key,
    required this.toolId,
  });

  @override
  ConsumerState<UIBuilderScreen> createState() => _UIBuilderScreenState();
}

class _UIBuilderScreenState extends ConsumerState<UIBuilderScreen> {
  final List<UIComponentData> _components = [];
  UIComponentData? _selectedComponent;
  bool _isPreviewMode = false;

  // Enhanced features
  final Map<String, String> _cellBindings = {}; // componentId -> cellAddress
  final List<String> _availableCells = ['A1', 'A2', 'A3', 'B1', 'B2', 'B3', 'C1', 'C2', 'C3'];
  bool _showCellPicker = false;
  final Map<String, Color> _componentColors = {};

  // Backend data integration
  final Map<String, dynamic> _cellValues = {};
  late final FormulaEngineService _formulaEngine;

  // Mobile app theme system
  late final MobileAppThemeService _themeService;
  MobileAppTheme? _selectedTheme;
  MobileAppTemplate? _selectedTemplate;
  MobileAppPreview _previewDevice = MobileAppPreview.androidPhone;
  bool _showMobilePreview = false;

  // Auto-save functionality
  bool _hasUnsavedChanges = false;

  // Undo/Redo functionality
  final List<List<UIComponentData>> _undoStack = [];
  final List<List<UIComponentData>> _redoStack = [];

  // Enhanced UI features
  String _componentSearchQuery = '';
  bool _showGrid = true;
  bool _snapToGrid = true;
  double _gridSize = 10.0;

  @override
  void initState() {
    super.initState();
    _formulaEngine = FormulaEngineService();
    _themeService = MobileAppThemeService();
    _themeService.initialize();
    _selectedTheme = _themeService.getThemeById('material_android');
    _loadSampleCellData();
  }

  /// Load sample cell data for demonstration
  void _loadSampleCellData() {
    _cellValues['A1'] = 10;
    _cellValues['A2'] = 20;
    _cellValues['A3'] = 30; // This could be a formula result
    _cellValues['B1'] = 'Hello';
    _cellValues['B2'] = 'World';
    _cellValues['B3'] = 'Hello World'; // This could be a formula result
  }

  /// Get the display value for a cell binding
  String _getCellDisplayValue(String? cellAddress) {
    if (cellAddress == null || cellAddress.isEmpty) {
      return 'No binding';
    }

    final value = _cellValues[cellAddress];
    if (value == null) {
      return 'Cell $cellAddress (empty)';
    }

    return value.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UI Builder'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        leading: IconButton(
          onPressed: _handleBackNavigation,
          icon: const Icon(Icons.arrow_back),
          tooltip: 'Back to Formula Engine',
        ),
        actions: [
          IconButton(
            onPressed: _showCellReferencePicker,
            icon: const Icon(Icons.link),
            tooltip: 'Cell Reference Picker',
          ),
          IconButton(
            onPressed: _showColorCustomization,
            icon: const Icon(Icons.palette),
            tooltip: 'Color Customization',
          ),
          IconButton(
            onPressed: _showTemplateGallery,
            icon: const Icon(Icons.widgets),
            tooltip: 'Template Gallery',
          ),
          IconButton(
            onPressed: _showUndoRedo,
            icon: const Icon(Icons.undo),
            tooltip: 'Undo/Redo',
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _isPreviewMode = !_isPreviewMode;
              });
            },
            icon: Icon(_isPreviewMode ? Icons.edit : Icons.preview),
            tooltip: _isPreviewMode ? 'Edit Mode' : 'Preview Mode',
          ),
          IconButton(
            onPressed: _quickPreview,
            icon: const Icon(Icons.visibility),
            tooltip: 'Quick Preview',
          ),
          IconButton(
            onPressed: _saveUI,
            icon: const Icon(Icons.save),
            tooltip: 'Save UI',
          ),
          IconButton(
            onPressed: () => context.go('/tools/preview/${widget.toolId}'),
            icon: const Icon(Icons.play_arrow),
            tooltip: 'Test Tool',
          ),
          const VerticalDivider(),
          IconButton(
            icon: Icon(_showMobilePreview ? Icons.desktop_windows : Icons.phone_android),
            onPressed: () {
              setState(() {
                _showMobilePreview = !_showMobilePreview;
              });
            },
            tooltip: _showMobilePreview ? 'Desktop Preview' : 'Mobile Preview',
          ),
          PopupMenuButton<MobileAppPreview>(
            icon: const Icon(Icons.devices),
            tooltip: 'Device Preview',
            onSelected: (device) {
              setState(() {
                _previewDevice = device;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: MobileAppPreview.androidPhone,
                child: Row(
                  children: [
                    Icon(Icons.phone_android),
                    SizedBox(width: 8),
                    Text('Android Phone'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: MobileAppPreview.iphone,
                child: Row(
                  children: [
                    Icon(Icons.phone_iphone),
                    SizedBox(width: 8),
                    Text('iPhone'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: MobileAppPreview.tablet,
                child: Row(
                  children: [
                    Icon(Icons.tablet),
                    SizedBox(width: 8),
                    Text('Tablet'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: MobileAppPreview.desktop,
                child: Row(
                  children: [
                    Icon(Icons.desktop_windows),
                    SizedBox(width: 8),
                    Text('Desktop'),
                  ],
                ),
              ),
            ],
          ),
          PopupMenuButton<MobileAppTheme>(
            icon: const Icon(Icons.palette),
            tooltip: 'App Theme',
            onSelected: (theme) {
              setState(() {
                _selectedTheme = theme;
              });
            },
            itemBuilder: (context) => _themeService.getThemes().map((theme) =>
              PopupMenuItem(
                value: theme,
                child: Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(theme.name),
                  ],
                ),
              ),
            ).toList(),
          ),
        ],
      ),
      body: Row(
        children: [
          // Component Palette (left sidebar)
          if (!_isPreviewMode) _buildComponentPalette(),
          
          // Main Canvas
          Expanded(
            flex: 3,
            child: _buildCanvas(),
          ),
          
          // Property Panel (right sidebar)
          if (!_isPreviewMode && _selectedComponent != null) _buildPropertyPanel(),
        ],
      ),
    );
  }

  Widget _buildComponentPalette() {
    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Components',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Component List
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              children: [
                _buildComponentCategory('Input', [
                  _buildComponentItem('Text Field', Icons.text_fields, ComponentType.textField),
                  _buildComponentItem('Dropdown', Icons.arrow_drop_down, ComponentType.dropdown),
                  _buildComponentItem('Toggle', Icons.toggle_on, ComponentType.toggle),
                  _buildComponentItem('Slider', Icons.tune, ComponentType.slider),
                ]),
                
                _buildComponentCategory('Output', [
                  _buildComponentItem('Output Field', Icons.output, ComponentType.outputField),
                  _buildComponentItem('Label', Icons.label, ComponentType.label),
                  _buildComponentItem('Chart', Icons.bar_chart, ComponentType.chart),
                ]),
                
                _buildComponentCategory('Actions', [
                  _buildComponentItem('Button', Icons.smart_button, ComponentType.button),
                  _buildComponentItem('Checkbox', Icons.check_box, ComponentType.checkbox),
                ]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentCategory(String title, List<Widget> items) {
    return ExpansionTile(
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      initiallyExpanded: true,
      children: items,
    );
  }

  Widget _buildComponentItem(String name, IconData icon, ComponentType type) {
    return Draggable<ComponentType>(
      data: type,
      feedback: Material(
        elevation: 4,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: Theme.of(context).colorScheme.onPrimary),
              const SizedBox(width: 8),
              Text(
                name,
                style: TextStyle(color: Theme.of(context).colorScheme.onPrimary),
              ),
            ],
          ),
        ),
      ),
      child: ListTile(
        leading: Icon(icon),
        title: Text(name),
        dense: true,
        onTap: () => _addComponent(type),
      ),
    );
  }

  Widget _buildCanvas() {
    if (_showMobilePreview && _selectedTheme != null) {
      return _buildMobilePreview();
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: DragTarget<ComponentType>(
        onAcceptWithDetails: (details) => _addComponent(details.data),
        builder: (context, candidateData, rejectedData) {
          return Stack(
            children: [
              // Canvas Background
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: candidateData.isNotEmpty
                      ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1)
                      : null,
                ),
                child: _components.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.design_services,
                              size: 64,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _isPreviewMode ? 'Preview Mode' : 'Drag components here',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            if (!_isPreviewMode) ...[
                              const SizedBox(height: 8),
                              Text(
                                'Start building your UI by dragging components from the left panel',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ],
                        ),
                      )
                    : null,
              ),

              // Components
              ..._components.map((component) => _buildCanvasComponent(component)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMobilePreview() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Center(
        child: Container(
          width: _previewDevice.screenSize.width,
          height: _previewDevice.screenSize.height,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Container(
              margin: EdgeInsets.all(_previewDevice.deviceType == 'ios' ? 4 : 2),
              decoration: BoxDecoration(
                color: _selectedTheme!.colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
              ),
              child: _buildMobileApp(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileApp() {
    if (_components.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.phone_android,
              size: 48,
              color: _selectedTheme!.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'Mobile App Preview',
              style: _selectedTheme!.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Add components to see your mobile app',
              style: _selectedTheme!.textTheme.bodyMedium?.copyWith(
                color: _selectedTheme!.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Convert UI components to mobile components
    final mobileComponents = _convertToMobileComponents();

    if (mobileComponents.isEmpty) {
      return const Center(
        child: Text('No compatible mobile components'),
      );
    }

    return MobileComponentRenderer(
      component: MobileComponentData(
        id: 'mobile_app_root',
        type: MobileComponentType.scaffold,
        label: 'Mobile App',
        children: mobileComponents,
      ),
      theme: _selectedTheme!,
      cellValues: _cellValues,
      formulaEngine: _formulaEngine,
    );
  }

  List<MobileComponentData> _convertToMobileComponents() {
    final mobileComponents = <MobileComponentData>[];

    // Add app bar if we have components
    if (_components.isNotEmpty) {
      mobileComponents.add(MobileComponentData(
        id: 'mobile_app_bar',
        type: MobileComponentType.appBar,
        label: 'My Tool',
        properties: {'title': 'My Tool', 'centerTitle': true},
      ));
    }

    // Convert UI components to mobile components
    for (final component in _components) {
      final mobileComponent = _convertUIComponentToMobile(component);
      if (mobileComponent != null) {
        mobileComponents.add(mobileComponent);
      }
    }

    return mobileComponents;
  }

  MobileComponentData? _convertUIComponentToMobile(UIComponentData component) {
    switch (component.type) {
      case ComponentType.textField:
        return MobileComponentData(
          id: component.id,
          type: MobileComponentType.textField,
          label: component.label,
          cellBinding: _cellBindings[component.id],
          properties: {'hint': 'Enter ${component.label.toLowerCase()}'},
        );
      case ComponentType.outputField:
        return MobileComponentData(
          id: component.id,
          type: MobileComponentType.formulaOutput,
          label: component.label,
          cellBinding: _cellBindings[component.id],
          properties: {'fontSize': 18, 'alignment': 'center'},
        );
      case ComponentType.button:
        return MobileComponentData(
          id: component.id,
          type: MobileComponentType.button,
          label: component.label,
          properties: {'text': component.label, 'style': 'elevated'},
        );
      case ComponentType.label:
        return MobileComponentData(
          id: component.id,
          type: MobileComponentType.text,
          label: component.label,
          properties: {'fontSize': 16},
        );
      case ComponentType.toggle:
        return MobileComponentData(
          id: component.id,
          type: MobileComponentType.switchWidget,
          label: component.label,
          properties: {'title': component.label},
        );
      case ComponentType.dropdown:
        return MobileComponentData(
          id: component.id,
          type: MobileComponentType.dropdownButton,
          label: component.label,
          properties: {'items': ['Option 1', 'Option 2', 'Option 3']},
        );
      default:
        return null;
    }
  }

  Widget _buildCanvasComponent(UIComponentData component) {
    final isSelected = _selectedComponent == component;
    
    return Positioned(
      left: component.x,
      top: component.y,
      child: GestureDetector(
        onTap: () {
          if (!_isPreviewMode) {
            setState(() {
              _selectedComponent = component;
            });
          }
        },
        onPanUpdate: _isPreviewMode ? null : (details) {
          setState(() {
            component.x += details.delta.dx;
            component.y += details.delta.dy;
          });
        },
        child: Container(
          width: component.width,
          height: component.height,
          decoration: BoxDecoration(
            border: isSelected && !_isPreviewMode
                ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2)
                : null,
          ),
          child: _buildComponentWidget(component),
        ),
      ),
    );
  }

  Widget _buildComponentWidget(UIComponentData component) {
    switch (component.type) {
      case ComponentType.textField:
        return TextField(
          decoration: InputDecoration(
            labelText: component.label,
            border: const OutlineInputBorder(),
          ),
          enabled: _isPreviewMode,
        );
      case ComponentType.outputField:
        final cellBinding = _cellBindings[component.id];
        final displayValue = _getCellDisplayValue(cellBinding);

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(4),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                component.label,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                displayValue,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              if (cellBinding != null && cellBinding.isNotEmpty) ...[
                const SizedBox(height: 2),
                Text(
                  'Cell: $cellBinding',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        );
      case ComponentType.button:
        return ElevatedButton(
          onPressed: _isPreviewMode ? () {} : null,
          child: Text(component.label),
        );
      case ComponentType.label:
        return Text(
          component.label,
          style: Theme.of(context).textTheme.bodyMedium,
        );
      case ComponentType.toggle:
        return SwitchListTile(
          title: Text(component.label),
          value: false,
          onChanged: _isPreviewMode ? (value) {} : null,
        );
      case ComponentType.dropdown:
        return DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: component.label,
            border: const OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem(value: 'option1', child: Text('Option 1')),
            DropdownMenuItem(value: 'option2', child: Text('Option 2')),
          ],
          onChanged: _isPreviewMode ? (value) {} : null,
        );
      default:
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(component.label),
        );
    }
  }

  Widget _buildPropertyPanel() {
    return Container(
      width: 300,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          left: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Properties',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Properties
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Label',
                    border: OutlineInputBorder(),
                  ),
                  controller: TextEditingController(text: _selectedComponent?.label),
                  onChanged: (value) {
                    setState(() {
                      _selectedComponent?.label = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                // Enhanced Cell Binding with Picker
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          labelText: 'Bind to Cell',
                          hintText: 'e.g., A1, B2',
                          border: OutlineInputBorder(),
                        ),
                        controller: TextEditingController(
                          text: _cellBindings[_selectedComponent?.id] ?? '',
                        ),
                        onChanged: (value) {
                          if (_selectedComponent != null) {
                            setState(() {
                              _cellBindings[_selectedComponent!.id] = value;
                              _hasUnsavedChanges = true;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: _showCellPickerDialog,
                      icon: const Icon(Icons.grid_view),
                      tooltip: 'Pick Cell',
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          labelText: 'Width',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        controller: TextEditingController(
                          text: _selectedComponent?.width.toString(),
                        ),
                        onChanged: (value) {
                          final width = double.tryParse(value);
                          if (width != null) {
                            setState(() {
                              _selectedComponent?.width = width;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          labelText: 'Height',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        controller: TextEditingController(
                          text: _selectedComponent?.height.toString(),
                        ),
                        onChanged: (value) {
                          final height = double.tryParse(value);
                          if (height != null) {
                            setState(() {
                              _selectedComponent?.height = height;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _components.remove(_selectedComponent);
                      _selectedComponent = null;
                    });
                  },
                  icon: const Icon(Icons.delete),
                  label: const Text('Delete Component'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.error,
                    foregroundColor: Theme.of(context).colorScheme.onError,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addComponent(ComponentType type) {
    _saveStateToUndoStack();

    setState(() {
      final componentId = _generateUniqueId();
      _components.add(UIComponentData(
        id: componentId,
        type: type,
        label: _getDefaultLabel(type),
        x: 50.0 + (_components.length * 20),
        y: 50.0 + (_components.length * 20),
        width: _getDefaultWidth(type),
        height: _getDefaultHeight(type),
      ));
      _hasUnsavedChanges = true;
    });
  }

  void _saveStateToUndoStack() {
    _undoStack.add(List<UIComponentData>.from(_components));
    _redoStack.clear(); // Clear redo stack when new action is performed

    // Limit undo stack size to prevent memory issues
    if (_undoStack.length > 50) {
      _undoStack.removeAt(0);
    }
  }

  /// Generate unique component ID
  String _generateUniqueId() {
    return 'comp_${DateTime.now().millisecondsSinceEpoch}_${math.Random().nextInt(1000)}';
  }

  String _getDefaultLabel(ComponentType type) {
    switch (type) {
      case ComponentType.textField:
        return 'Text Input';
      case ComponentType.outputField:
        return 'Output';
      case ComponentType.button:
        return 'Button';
      case ComponentType.label:
        return 'Label';
      case ComponentType.toggle:
        return 'Toggle';
      case ComponentType.dropdown:
        return 'Dropdown';
      default:
        return 'Component';
    }
  }

  double _getDefaultWidth(ComponentType type) {
    switch (type) {
      case ComponentType.textField:
      case ComponentType.outputField:
      case ComponentType.dropdown:
        return 200;
      case ComponentType.button:
        return 120;
      case ComponentType.toggle:
        return 150;
      default:
        return 100;
    }
  }

  double _getDefaultHeight(ComponentType type) {
    switch (type) {
      case ComponentType.toggle:
        return 60;
      default:
        return 40;
    }
  }

  /// Handle back navigation with confirmation
  void _handleBackNavigation() {
    if (_hasUnsavedChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Unsaved Changes'),
          content: const Text('You have unsaved changes. What would you like to do?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.go('/tools/backend-formula/${widget.toolId}');
              },
              child: const Text('Exit Without Saving'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _saveUI();
                context.go('/tools/backend-formula/${widget.toolId}');
              },
              child: const Text('Save & Exit'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    } else {
      context.go('/tools/backend-formula/${widget.toolId}');
    }
  }

  /// Show cell reference picker dialog
  void _showCellReferencePicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Available Cell References'),
        content: SizedBox(
          width: 300,
          height: 400,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 2,
            ),
            itemCount: _availableCells.length,
            itemBuilder: (context, index) {
              final cell = _availableCells[index];
              return Card(
                child: InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    _showComponentBindingDialog(cell);
                  },
                  child: Center(
                    child: Text(
                      cell,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show component binding dialog for selected cell
  void _showComponentBindingDialog(String cellAddress) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Bind Components to $cellAddress'),
        content: SizedBox(
          width: 300,
          height: 300,
          child: ListView.builder(
            itemCount: _components.length,
            itemBuilder: (context, index) {
              final component = _components[index];
              final isCurrentlyBound = _cellBindings[component.id] == cellAddress;

              return CheckboxListTile(
                title: Text('${component.label} (${component.type.name})'),
                subtitle: Text('ID: ${component.id}'),
                value: isCurrentlyBound,
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      _cellBindings[component.id] = cellAddress;
                    } else {
                      _cellBindings.remove(component.id);
                    }
                    _hasUnsavedChanges = true;
                  });
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show color customization dialog
  void _showColorCustomization() {
    if (_selectedComponent == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a component first')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Customize ${_selectedComponent!.label}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose a color:'),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                Colors.blue,
                Colors.green,
                Colors.red,
                Colors.orange,
                Colors.purple,
                Colors.teal,
                Colors.pink,
                Colors.indigo,
              ].map((color) => GestureDetector(
                onTap: () {
                  setState(() {
                    _componentColors[_selectedComponent!.id] = color;
                    _selectedComponent!.customColor = color;
                    _hasUnsavedChanges = true;
                  });
                  Navigator.of(context).pop();
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _componentColors[_selectedComponent!.id] == color
                          ? Colors.black
                          : Colors.transparent,
                      width: 2,
                    ),
                  ),
                ),
              )).toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _componentColors.remove(_selectedComponent!.id);
                _selectedComponent!.customColor = null;
                _hasUnsavedChanges = true;
              });
              Navigator.of(context).pop();
            },
            child: const Text('Reset'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Quick preview functionality
  void _quickPreview() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quick Preview'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: _components.map((component) => Positioned(
                left: component.x * 0.5, // Scale down for preview
                top: component.y * 0.5,
                child: SizedBox(
                  width: component.width * 0.5,
                  height: component.height * 0.5,
                  child: _buildComponentWidget(component),
                ),
              )).toList(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show cell picker dialog for property panel
  void _showCellPickerDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Cell'),
        content: SizedBox(
          width: 200,
          height: 300,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1.5,
            ),
            itemCount: _availableCells.length,
            itemBuilder: (context, index) {
              final cell = _availableCells[index];
              return Card(
                child: InkWell(
                  onTap: () {
                    if (_selectedComponent != null) {
                      setState(() {
                        _cellBindings[_selectedComponent!.id] = cell;
                        _hasUnsavedChanges = true;
                      });
                    }
                    Navigator.of(context).pop();
                  },
                  child: Center(
                    child: Text(
                      cell,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _saveUI() {
    // TODO: Save UI layout using ToolStorageService
    setState(() {
      _hasUnsavedChanges = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('UI layout saved successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Show template gallery for quick UI creation
  void _showTemplateGallery() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Template Gallery'),
        content: SizedBox(
          width: 600,
          height: 400,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: _uiTemplates.length,
            itemBuilder: (context, index) {
              final template = _uiTemplates[index];
              return Card(
                child: InkWell(
                  onTap: () => _applyTemplate(template),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(template.icon, size: 32),
                        const SizedBox(height: 8),
                        Text(
                          template.name,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          template.description,
                          style: Theme.of(context).textTheme.bodySmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Show undo/redo options
  void _showUndoRedo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Undo/Redo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.undo),
              title: const Text('Undo Last Action'),
              onTap: () {
                Navigator.of(context).pop();
                _undoLastAction();
              },
            ),
            ListTile(
              leading: const Icon(Icons.redo),
              title: const Text('Redo Last Action'),
              onTap: () {
                Navigator.of(context).pop();
                _redoLastAction();
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: const Text('Clear All Components'),
              onTap: () {
                Navigator.of(context).pop();
                _clearAllComponents();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Apply a template to the canvas
  void _applyTemplate(UITemplate template) {
    Navigator.of(context).pop();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Apply ${template.name}?'),
        content: const Text('This will replace all current components. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _components.clear();
                _components.addAll(template.components.map((comp) => UIComponentData(
                  id: _generateUniqueId(),
                  type: comp.type,
                  label: comp.label,
                  x: comp.x,
                  y: comp.y,
                  width: comp.width,
                  height: comp.height,
                  cellBinding: comp.cellBinding,
                  customColor: comp.customColor,
                )));
                _hasUnsavedChanges = true;
              });

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${template.name} template applied!')),
              );
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  /// Undo last action
  void _undoLastAction() {
    if (_undoStack.isNotEmpty) {
      final previousState = _undoStack.removeLast();
      _redoStack.add(List<UIComponentData>.from(_components));

      setState(() {
        _components.clear();
        _components.addAll(previousState);
        _hasUnsavedChanges = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Action undone')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Nothing to undo')),
      );
    }
  }

  /// Redo last action
  void _redoLastAction() {
    if (_redoStack.isNotEmpty) {
      final nextState = _redoStack.removeLast();
      _undoStack.add(List<UIComponentData>.from(_components));

      setState(() {
        _components.clear();
        _components.addAll(nextState);
        _hasUnsavedChanges = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Action redone')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Nothing to redo')),
      );
    }
  }

  /// Clear all components
  void _clearAllComponents() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Components'),
        content: const Text('Are you sure you want to remove all components?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _components.clear();
                _selectedComponent = null;
                _cellBindings.clear();
                _componentColors.clear();
                _hasUnsavedChanges = true;
              });

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('All components cleared!')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  /// Predefined UI templates
  List<UITemplate> get _uiTemplates => [
    UITemplate(
      name: 'Calculator Layout',
      description: 'Basic calculator interface with input and output fields',
      icon: Icons.calculate,
      components: [
        UIComponentData(
          id: 'input1',
          type: ComponentType.textField,
          label: 'Number 1',
          x: 50,
          y: 50,
          width: 200,
          height: 56,
          cellBinding: 'A1',
        ),
        UIComponentData(
          id: 'input2',
          type: ComponentType.textField,
          label: 'Number 2',
          x: 50,
          y: 120,
          width: 200,
          height: 56,
          cellBinding: 'A2',
        ),
        UIComponentData(
          id: 'result',
          type: ComponentType.outputField,
          label: 'Result',
          x: 50,
          y: 190,
          width: 200,
          height: 56,
          cellBinding: 'A3',
        ),
        UIComponentData(
          id: 'calculate',
          type: ComponentType.button,
          label: 'Calculate',
          x: 270,
          y: 120,
          width: 100,
          height: 40,
        ),
      ],
    ),
    UITemplate(
      name: 'Form Layout',
      description: 'Data entry form with multiple input types',
      icon: Icons.assignment,
      components: [
        UIComponentData(
          id: 'name',
          type: ComponentType.textField,
          label: 'Name',
          x: 50,
          y: 50,
          width: 250,
          height: 56,
          cellBinding: 'A1',
        ),
        UIComponentData(
          id: 'email',
          type: ComponentType.textField,
          label: 'Email',
          x: 50,
          y: 120,
          width: 250,
          height: 56,
          cellBinding: 'A2',
        ),
        UIComponentData(
          id: 'category',
          type: ComponentType.dropdown,
          label: 'Category',
          x: 50,
          y: 190,
          width: 250,
          height: 56,
          cellBinding: 'A3',
        ),
        UIComponentData(
          id: 'active',
          type: ComponentType.toggle,
          label: 'Active',
          x: 50,
          y: 260,
          width: 250,
          height: 56,
          cellBinding: 'A4',
        ),
        UIComponentData(
          id: 'submit',
          type: ComponentType.button,
          label: 'Submit',
          x: 320,
          y: 190,
          width: 100,
          height: 40,
        ),
      ],
    ),
    UITemplate(
      name: 'Dashboard Layout',
      description: 'Dashboard with metrics and charts',
      icon: Icons.dashboard,
      components: [
        UIComponentData(
          id: 'metric1',
          type: ComponentType.outputField,
          label: 'Total Sales',
          x: 50,
          y: 50,
          width: 150,
          height: 80,
          cellBinding: 'B1',
        ),
        UIComponentData(
          id: 'metric2',
          type: ComponentType.outputField,
          label: 'Orders',
          x: 220,
          y: 50,
          width: 150,
          height: 80,
          cellBinding: 'B2',
        ),
        UIComponentData(
          id: 'chart',
          type: ComponentType.chart,
          label: 'Sales Chart',
          x: 50,
          y: 150,
          width: 320,
          height: 200,
          cellBinding: 'B3:B10',
        ),
      ],
    ),
    UITemplate(
      name: 'Settings Panel',
      description: 'Configuration panel with various controls',
      icon: Icons.settings,
      components: [
        UIComponentData(
          id: 'setting1',
          type: ComponentType.toggle,
          label: 'Enable Notifications',
          x: 50,
          y: 50,
          width: 250,
          height: 56,
          cellBinding: 'C1',
        ),
        UIComponentData(
          id: 'setting2',
          type: ComponentType.slider,
          label: 'Volume',
          x: 50,
          y: 120,
          width: 250,
          height: 56,
          cellBinding: 'C2',
        ),
        UIComponentData(
          id: 'setting3',
          type: ComponentType.dropdown,
          label: 'Theme',
          x: 50,
          y: 190,
          width: 250,
          height: 56,
          cellBinding: 'C3',
        ),
        UIComponentData(
          id: 'save',
          type: ComponentType.button,
          label: 'Save Settings',
          x: 320,
          y: 120,
          width: 120,
          height: 40,
        ),
      ],
    ),
  ];

  /// Show component help dialog
  void _showComponentHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Component Guide'),
        content: const SizedBox(
          width: 400,
          height: 300,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Input Components:', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('• Text Field: Collect user input'),
                Text('• Dropdown: Select from options'),
                Text('• Toggle: On/off switch'),
                Text('• Slider: Range selection'),
                SizedBox(height: 16),
                Text('Output Components:', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('• Output Field: Display calculated results'),
                Text('• Label: Static text display'),
                Text('• Chart: Data visualization'),
                SizedBox(height: 16),
                Text('Action Components:', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('• Button: Trigger actions'),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show template selection dialog
  void _showTemplateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Template'),
        content: const SizedBox(
          width: 300,
          height: 200,
          child: Column(
            children: [
              ListTile(
                leading: Icon(Icons.calculate),
                title: Text('Calculator'),
                subtitle: Text('Basic calculator layout'),
              ),
              ListTile(
                leading: Icon(Icons.dashboard),
                title: Text('Dashboard'),
                subtitle: Text('Data dashboard with charts'),
              ),
              ListTile(
                leading: Icon(Icons.assignment),
                title: Text('Form'),
                subtitle: Text('Data entry form'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Import from Excel file
  void _importFromExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Excel import functionality coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}

// Data classes
class UIComponentData {
  final String id;
  ComponentType type;
  String label;
  double x;
  double y;
  double width;
  double height;
  String? cellBinding;
  Color? customColor;

  UIComponentData({
    required this.id,
    required this.type,
    required this.label,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.cellBinding,
    this.customColor,
  });
}

enum ComponentType {
  textField,
  outputField,
  button,
  label,
  toggle,
  dropdown,
  slider,
  checkbox,
  chart,
}

/// UI Template for quick layout creation
class UITemplate {
  final String name;
  final String description;
  final IconData icon;
  final List<UIComponentData> components;

  const UITemplate({
    required this.name,
    required this.description,
    required this.icon,
    required this.components,
  });
}
