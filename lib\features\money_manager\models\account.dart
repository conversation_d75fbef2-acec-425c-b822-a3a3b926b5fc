import 'package:flutter/material.dart';

/// Account model for Money Manager
class Account {
  final String id;
  final String name;
  final String description;
  final AccountType type;
  final String currency;
  final double initialBalance;
  final double currentBalance;
  final String iconName;
  final Color color;
  final bool allowNegativeBalance;
  final bool isVisible;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? modifiedAt;
  final Map<String, dynamic> metadata;

  const Account({
    required this.id,
    required this.name,
    this.description = '',
    required this.type,
    this.currency = 'USD',
    this.initialBalance = 0.0,
    this.currentBalance = 0.0,
    this.iconName = 'account_balance_wallet',
    this.color = Colors.blue,
    this.allowNegativeBalance = false,
    this.isVisible = true,
    this.isActive = true,
    required this.createdAt,
    this.modifiedAt,
    this.metadata = const {},
  });

  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      type: AccountType.values[json['type'] as int],
      currency: json['currency'] as String? ?? 'USD',
      initialBalance: (json['initial_balance'] as num?)?.toDouble() ?? 0.0,
      currentBalance: (json['current_balance'] as num?)?.toDouble() ?? 0.0,
      iconName: json['icon_name'] as String? ?? 'account_balance_wallet',
      color: Color(json['color'] as int? ?? Colors.blue.value),
      allowNegativeBalance: json['allow_negative_balance'] as bool? ?? false,
      isVisible: json['is_visible'] as bool? ?? true,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      modifiedAt: json['modified_at'] != null 
          ? DateTime.parse(json['modified_at'] as String)
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'currency': currency,
      'initial_balance': initialBalance,
      'current_balance': currentBalance,
      'icon_name': iconName,
      'color': color.value,
      'allow_negative_balance': allowNegativeBalance,
      'is_visible': isVisible,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'modified_at': modifiedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  Account copyWith({
    String? id,
    String? name,
    String? description,
    AccountType? type,
    String? currency,
    double? initialBalance,
    double? currentBalance,
    String? iconName,
    Color? color,
    bool? allowNegativeBalance,
    bool? isVisible,
    bool? isActive,
    DateTime? createdAt,
    DateTime? modifiedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      currency: currency ?? this.currency,
      initialBalance: initialBalance ?? this.initialBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      allowNegativeBalance: allowNegativeBalance ?? this.allowNegativeBalance,
      isVisible: isVisible ?? this.isVisible,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? DateTime.now(),
      metadata: metadata ?? this.metadata,
    );
  }

  String get formattedBalance => _formatCurrency(currentBalance, currency);
  String get formattedInitialBalance => _formatCurrency(initialBalance, currency);
  
  double get balanceChange => currentBalance - initialBalance;
  String get formattedBalanceChange => _formatCurrency(balanceChange, currency);
  
  bool get isPositiveBalance => currentBalance >= 0;
  bool get hasBalanceChanged => balanceChange != 0;

  IconData get icon {
    switch (iconName) {
      case 'account_balance_wallet':
        return Icons.account_balance_wallet;
      case 'credit_card':
        return Icons.credit_card;
      case 'savings':
        return Icons.savings;
      case 'account_balance':
        return Icons.account_balance;
      case 'monetization_on':
        return Icons.monetization_on;
      case 'payment':
        return Icons.payment;
      case 'local_atm':
        return Icons.local_atm;
      case 'business':
        return Icons.business;
      case 'home':
        return Icons.home;
      case 'school':
        return Icons.school;
      default:
        return Icons.account_balance_wallet;
    }
  }

  String _formatCurrency(double amount, String currency) {
    final symbols = {
      'USD': '\$', 'EUR': '€', 'GBP': '£', 'JPY': '¥', 'CNY': '¥', 'INR': '₹',
      'CAD': 'C\$', 'AUD': 'A\$', 'CHF': 'CHF', 'SEK': 'kr', 'NOK': 'kr',
      'DKK': 'kr', 'PLN': 'zł', 'CZK': 'Kč', 'HUF': 'Ft', 'RUB': '₽',
      'BRL': 'R\$', 'MXN': '\$', 'ZAR': 'R', 'KRW': '₩', 'SGD': 'S\$',
      'HKD': 'HK\$', 'NZD': 'NZ\$', 'TRY': '₺', 'ILS': '₪', 'AED': 'د.إ',
      'SAR': 'ر.س', 'EGP': 'ج.م', 'MAD': 'د.م.', 'TND': 'د.ت', 'JOD': 'د.أ',
      'LBP': 'ل.ل', 'SYP': 'ل.س', 'IQD': 'ع.د', 'KWD': 'د.ك', 'BHD': 'د.ب',
      'OMR': 'ر.ع.', 'QAR': 'ر.ق', 'YER': 'ر.ي',
    };

    final symbol = symbols[currency] ?? currency;
    final formattedAmount = amount.toStringAsFixed(2);

    if (amount < 0) {
      return '-$symbol${formattedAmount.substring(1)}';
    }

    return '$symbol$formattedAmount';
  }
}

enum AccountType {
  checking,
  savings,
  credit,
  investment,
  cash,
  loan,
  business,
  other,
}

extension AccountTypeExtension on AccountType {
  String get displayName {
    switch (this) {
      case AccountType.checking:
        return 'Checking';
      case AccountType.savings:
        return 'Savings';
      case AccountType.credit:
        return 'Credit Card';
      case AccountType.investment:
        return 'Investment';
      case AccountType.cash:
        return 'Cash';
      case AccountType.loan:
        return 'Loan';
      case AccountType.business:
        return 'Business';
      case AccountType.other:
        return 'Other';
    }
  }

  String get description {
    switch (this) {
      case AccountType.checking:
        return 'Everyday spending account';
      case AccountType.savings:
        return 'Money saving account';
      case AccountType.credit:
        return 'Credit card account';
      case AccountType.investment:
        return 'Investment portfolio';
      case AccountType.cash:
        return 'Physical cash';
      case AccountType.loan:
        return 'Loan or debt account';
      case AccountType.business:
        return 'Business account';
      case AccountType.other:
        return 'Other account type';
    }
  }

  IconData get defaultIcon {
    switch (this) {
      case AccountType.checking:
        return Icons.account_balance_wallet;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.credit:
        return Icons.credit_card;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.cash:
        return Icons.monetization_on;
      case AccountType.loan:
        return Icons.account_balance;
      case AccountType.business:
        return Icons.business;
      case AccountType.other:
        return Icons.account_balance_wallet;
    }
  }

  Color get defaultColor {
    switch (this) {
      case AccountType.checking:
        return Colors.blue;
      case AccountType.savings:
        return Colors.green;
      case AccountType.credit:
        return Colors.orange;
      case AccountType.investment:
        return Colors.purple;
      case AccountType.cash:
        return Colors.amber;
      case AccountType.loan:
        return Colors.red;
      case AccountType.business:
        return Colors.indigo;
      case AccountType.other:
        return Colors.grey;
    }
  }
}
