import '../models/custom_tool.dart';

/// Simplified service for creating example tools
class ExampleToolsService {
  static final ExampleToolsService _instance = ExampleToolsService._internal();
  factory ExampleToolsService() => _instance;
  ExampleToolsService._internal();

  /// Get all example tools
  Future<List<CustomTool>> getExampleTools() async {
    return [
      _createCalculatorTool(),
      _createUnitConverterTool(),
      _createBudgetTrackerTool(),
      _createGradeCalculatorTool(),
      _createLoanCalculatorTool(),
      _createTipCalculatorTool(),
      _createBMICalculatorTool(),
      _createCompoundInterestTool(),
      _createDiscountCalculatorTool(),
      _createTimeTrackerTool(),
    ];
  }

  /// Get featured example tools
  Future<List<CustomTool>> getFeaturedTools() async {
    return [
      _createCalculatorTool(),
      _createBudgetTrackerTool(),
      _createLoanCalculatorTool(),
    ];
  }

  /// Check if example tools exist
  Future<bool> exampleToolsExist() async {
    // For now, always return false to trigger creation
    return false;
  }

  /// Create all example tools
  Future<List<CustomTool>> createAllExampleTools() async {
    return await getExampleTools();
  }

  /// Create Basic Calculator Tool
  CustomTool _createCalculatorTool() {
    return CustomTool(
      id: 'example_calculator',
      name: 'Basic Calculator',
      description: 'A simple calculator for basic arithmetic operations',
      category: ToolCategory.calculator.name,
      backendData: {
        'A1': {'value': 0, 'type': 'number'},
        'B1': {'value': '+', 'type': 'text'},
        'C1': {'value': 0, 'type': 'number'},
        'D1': {'value': 0, 'type': 'formula', 'formula': '=A1+C1'},
      },
      uiLayout: {
        'components': [
          {'id': 'input_a', 'type': 'input', 'label': 'First Number'},
          {'id': 'operation', 'type': 'dropdown', 'label': 'Operation'},
          {'id': 'input_b', 'type': 'input', 'label': 'Second Number'},
          {'id': 'result', 'type': 'output', 'label': 'Result'},
        ]
      },
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Unit Converter Tool
  CustomTool _createUnitConverterTool() {
    return CustomTool(
      id: 'example_unit_converter',
      name: 'Unit Converter',
      description: 'Convert between different units of measurement',
      category: ToolCategory.utility.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Budget Tracker Tool
  CustomTool _createBudgetTrackerTool() {
    return CustomTool(
      id: 'example_budget_tracker',
      name: 'Budget Tracker',
      description: 'Track your income and expenses to manage your budget',
      category: ToolCategory.finance.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Grade Calculator Tool
  CustomTool _createGradeCalculatorTool() {
    return CustomTool(
      id: 'example_grade_calculator',
      name: 'Grade Calculator',
      description: 'Calculate your final grade based on assignments and exams',
      category: ToolCategory.education.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Loan Calculator Tool
  CustomTool _createLoanCalculatorTool() {
    return CustomTool(
      id: 'example_loan_calculator',
      name: 'Loan Calculator',
      description: 'Calculate monthly payments and total interest for loans',
      category: ToolCategory.finance.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Tip Calculator Tool
  CustomTool _createTipCalculatorTool() {
    return CustomTool(
      id: 'example_tip_calculator',
      name: 'Tip Calculator',
      description: 'Calculate tip and total bill amount',
      category: ToolCategory.utility.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create BMI Calculator Tool
  CustomTool _createBMICalculatorTool() {
    return CustomTool(
      id: 'example_bmi_calculator',
      name: 'BMI Calculator',
      description: 'Calculate Body Mass Index and health category',
      category: ToolCategory.health.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Compound Interest Tool
  CustomTool _createCompoundInterestTool() {
    return CustomTool(
      id: 'example_compound_interest',
      name: 'Compound Interest Calculator',
      description: 'Calculate compound interest and investment growth',
      category: ToolCategory.finance.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Discount Calculator Tool
  CustomTool _createDiscountCalculatorTool() {
    return CustomTool(
      id: 'example_discount_calculator',
      name: 'Discount Calculator',
      description: 'Calculate discounted price and savings',
      category: ToolCategory.utility.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Create Time Tracker Tool
  CustomTool _createTimeTrackerTool() {
    return CustomTool(
      id: 'example_time_tracker',
      name: 'Time Tracker',
      description: 'Track work hours and calculate total time',
      category: ToolCategory.productivity.name,
      backendData: {},
      uiLayout: {},
      isActive: true,
      isFromTemplate: true,
      isFromExcel: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }
}
