import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:local_auth/local_auth.dart';
import '../services/tool_security_service.dart';

/// Screen for configuring tool security settings
class ToolSecurityScreen extends ConsumerStatefulWidget {
  final int toolId;
  final String toolName;

  const ToolSecurityScreen({
    super.key,
    required this.toolId,
    required this.toolName,
  });

  @override
  ConsumerState<ToolSecurityScreen> createState() => _ToolSecurityScreenState();
}

class _ToolSecurityScreenState extends ConsumerState<ToolSecurityScreen> {
  final ToolSecurityService _securityService = ToolSecurityService();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  
  ToolSecurityStatus? _securityStatus;
  List<BiometricType> _availableBiometrics = [];
  bool _isLoading = true;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void initState() {
    super.initState();
    _loadSecurityStatus();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadSecurityStatus() async {
    setState(() => _isLoading = true);
    
    try {
      final status = await _securityService.getToolSecurityStatus(widget.toolId);
      final biometrics = await _securityService.getAvailableBiometrics();
      
      setState(() {
        _securityStatus = status;
        _availableBiometrics = biometrics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load security status');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Security - ${widget.toolName}'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _securityStatus == null
              ? const Center(child: Text('Failed to load security settings'))
              : _buildSecuritySettings(),
    );
  }

  Widget _buildSecuritySettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Security Overview Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _securityStatus!.isProtected ? Icons.security : Icons.security_outlined,
                        color: _securityStatus!.isProtected ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Security Status',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _securityStatus!.isProtected
                        ? 'This tool is protected'
                        : 'This tool is not protected',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: _securityStatus!.isProtected ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Password Protection Section
          Text(
            'Password Protection',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('Enable Password Protection'),
                    subtitle: Text(
                      _securityStatus!.hasPasswordProtection
                          ? 'Password protection is enabled'
                          : 'Protect this tool with a password',
                    ),
                    value: _securityStatus!.hasPasswordProtection,
                    onChanged: (value) {
                      if (value) {
                        _showPasswordSetupDialog();
                      } else {
                        _removePasswordProtection();
                      }
                    },
                  ),
                  
                  if (_securityStatus!.hasPasswordProtection) ...[
                    const Divider(),
                    ListTile(
                      leading: const Icon(Icons.edit),
                      title: const Text('Change Password'),
                      subtitle: const Text('Update your tool password'),
                      onTap: _showPasswordChangeDialog,
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Biometric Authentication Section
          Text(
            'Biometric Authentication',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('Enable Biometric Authentication'),
                    subtitle: Text(
                      _securityStatus!.biometricAvailable
                          ? _securityStatus!.hasBiometricProtection
                              ? 'Biometric authentication is enabled'
                              : 'Use fingerprint or face recognition'
                          : 'Biometric authentication not available',
                    ),
                    value: _securityStatus!.hasBiometricProtection,
                    onChanged: _securityStatus!.biometricAvailable
                        ? (value) {
                            if (value) {
                              _enableBiometricAuth();
                            } else {
                              _disableBiometricAuth();
                            }
                          }
                        : null,
                  ),
                  
                  if (_availableBiometrics.isNotEmpty) ...[
                    const Divider(),
                    const Text('Available biometric methods:'),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: _availableBiometrics.map((type) {
                        return Chip(
                          label: Text(_getBiometricTypeName(type)),
                          avatar: Icon(_getBiometricTypeIcon(type)),
                        );
                      }).toList(),
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Test Authentication Section
          if (_securityStatus!.isProtected) ...[
            Text(
              'Test Authentication',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.verified_user),
                      title: const Text('Test Authentication'),
                      subtitle: const Text('Verify your security settings work correctly'),
                      onTap: _testAuthentication,
                    ),
                  ],
                ),
              ),
            ),
          ],
          
          const SizedBox(height: 24),
          
          // Clear All Security
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: ListTile(
                leading: Icon(
                  Icons.security_outlined,
                  color: Theme.of(context).colorScheme.error,
                ),
                title: Text(
                  'Remove All Security',
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
                subtitle: const Text('Remove all security protection from this tool'),
                onTap: _clearAllSecurity,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showPasswordSetupDialog() {
    _passwordController.clear();
    _confirmPasswordController.clear();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: 'Password',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
                  icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _confirmPasswordController,
              obscureText: _obscureConfirmPassword,
              decoration: InputDecoration(
                labelText: 'Confirm Password',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  onPressed: () => setState(() => _obscureConfirmPassword = !_obscureConfirmPassword),
                  icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _setPassword,
            child: const Text('Set Password'),
          ),
        ],
      ),
    );
  }

  void _showPasswordChangeDialog() {
    _passwordController.clear();
    _confirmPasswordController.clear();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: 'New Password',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
                  icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _confirmPasswordController,
              obscureText: _obscureConfirmPassword,
              decoration: InputDecoration(
                labelText: 'Confirm New Password',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  onPressed: () => setState(() => _obscureConfirmPassword = !_obscureConfirmPassword),
                  icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _changePassword,
            child: const Text('Change Password'),
          ),
        ],
      ),
    );
  }

  Future<void> _setPassword() async {
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;

    if (password.isEmpty) {
      _showErrorSnackBar('Password cannot be empty');
      return;
    }

    if (password.length < 6) {
      _showErrorSnackBar('Password must be at least 6 characters');
      return;
    }

    if (password != confirmPassword) {
      _showErrorSnackBar('Passwords do not match');
      return;
    }

    Navigator.of(context).pop();

    final success = await _securityService.setToolPassword(widget.toolId, password);
    if (success) {
      _showSuccessSnackBar('Password protection enabled');
      _loadSecurityStatus();
    } else {
      _showErrorSnackBar('Failed to set password');
    }
  }

  Future<void> _changePassword() async {
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;

    if (password.isEmpty) {
      _showErrorSnackBar('Password cannot be empty');
      return;
    }

    if (password.length < 6) {
      _showErrorSnackBar('Password must be at least 6 characters');
      return;
    }

    if (password != confirmPassword) {
      _showErrorSnackBar('Passwords do not match');
      return;
    }

    Navigator.of(context).pop();

    final success = await _securityService.setToolPassword(widget.toolId, password);
    if (success) {
      _showSuccessSnackBar('Password changed successfully');
    } else {
      _showErrorSnackBar('Failed to change password');
    }
  }

  Future<void> _removePasswordProtection() async {
    final success = await _securityService.removeToolPassword(widget.toolId);
    if (success) {
      _showSuccessSnackBar('Password protection removed');
      _loadSecurityStatus();
    } else {
      _showErrorSnackBar('Failed to remove password protection');
    }
  }

  Future<void> _enableBiometricAuth() async {
    // Test biometric authentication first
    final authenticated = await _securityService.authenticateWithBiometrics(
      reason: 'Authenticate to enable biometric protection',
    );

    if (!authenticated) {
      _showErrorSnackBar('Biometric authentication failed');
      return;
    }

    final success = await _securityService.enableBiometricAuth(widget.toolId);
    if (success) {
      _showSuccessSnackBar('Biometric authentication enabled');
      _loadSecurityStatus();
    } else {
      _showErrorSnackBar('Failed to enable biometric authentication');
    }
  }

  Future<void> _disableBiometricAuth() async {
    final success = await _securityService.disableBiometricAuth(widget.toolId);
    if (success) {
      _showSuccessSnackBar('Biometric authentication disabled');
      _loadSecurityStatus();
    } else {
      _showErrorSnackBar('Failed to disable biometric authentication');
    }
  }

  Future<void> _testAuthentication() async {
    final success = await _securityService.authenticateForTool(widget.toolId);
    if (success) {
      _showSuccessSnackBar('Authentication successful!');
    } else {
      _showErrorSnackBar('Authentication failed');
    }
  }

  Future<void> _clearAllSecurity() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove All Security'),
        content: const Text(
          'Are you sure you want to remove all security protection from this tool? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Remove All'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _securityService.clearToolSecurity(widget.toolId);
      if (success) {
        _showSuccessSnackBar('All security protection removed');
        _loadSecurityStatus();
      } else {
        _showErrorSnackBar('Failed to remove security protection');
      }
    }
  }

  String _getBiometricTypeName(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return 'Face Recognition';
      case BiometricType.fingerprint:
        return 'Fingerprint';
      case BiometricType.iris:
        return 'Iris Scan';
      case BiometricType.weak:
        return 'Weak Biometric';
      case BiometricType.strong:
        return 'Strong Biometric';
    }
  }

  IconData _getBiometricTypeIcon(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return Icons.face;
      case BiometricType.fingerprint:
        return Icons.fingerprint;
      case BiometricType.iris:
        return Icons.visibility;
      case BiometricType.weak:
        return Icons.security;
      case BiometricType.strong:
        return Icons.verified_user;
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
