import 'dart:convert';

class UserProfile {
  int id = 0;
  String? email;

  String name = '';
  String? avatarPath;
  String languageCode = 'en';
  String themeMode = 'system'; // 'light', 'dark', 'system'

  bool syncEnabled = false;
  bool notificationsEnabled = true;
  String? notificationSound;

  // App-specific settings (stored as JSON strings)
  String memoSettings = '{}';
  String athkarSettings = '{}';
  String toolsSettings = '{}';
  String moneySettings = '{}';

  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  UserProfile();

  UserProfile.create({
    required this.name,
    required this.email,
    this.languageCode = 'en',
    this.themeMode = 'system',
    this.syncEnabled = false,
    this.notificationsEnabled = true,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void updateProfile({
    String? name,
    String? avatarPath,
    String? languageCode,
    String? themeMode,
    bool? syncEnabled,
    bool? notificationsEnabled,
    String? notificationSound,
  }) {
    if (name != null) this.name = name;
    if (avatarPath != null) this.avatarPath = avatarPath;
    if (languageCode != null) this.languageCode = languageCode;
    if (themeMode != null) this.themeMode = themeMode;
    if (syncEnabled != null) this.syncEnabled = syncEnabled;
    if (notificationsEnabled != null) this.notificationsEnabled = notificationsEnabled;
    if (notificationSound != null) this.notificationSound = notificationSound;
    
    updateTimestamp();
  }

  void updateMemoSettings(Map<String, dynamic> settings) {
    final current = jsonDecode(memoSettings) as Map<String, dynamic>;
    memoSettings = jsonEncode({...current, ...settings});
    updateTimestamp();
  }

  void updateAthkarSettings(Map<String, dynamic> settings) {
    final current = jsonDecode(athkarSettings) as Map<String, dynamic>;
    athkarSettings = jsonEncode({...current, ...settings});
    updateTimestamp();
  }

  void updateToolsSettings(Map<String, dynamic> settings) {
    final current = jsonDecode(toolsSettings) as Map<String, dynamic>;
    toolsSettings = jsonEncode({...current, ...settings});
    updateTimestamp();
  }

  void updateMoneySettings(Map<String, dynamic> settings) {
    final current = jsonDecode(moneySettings) as Map<String, dynamic>;
    moneySettings = jsonEncode({...current, ...settings});
    updateTimestamp();
  }

  Map<String, dynamic> getMemoSettings() {
    return jsonDecode(memoSettings) as Map<String, dynamic>;
  }

  Map<String, dynamic> getAthkarSettings() {
    return jsonDecode(athkarSettings) as Map<String, dynamic>;
  }

  Map<String, dynamic> getToolsSettings() {
    return jsonDecode(toolsSettings) as Map<String, dynamic>;
  }

  Map<String, dynamic> getMoneySettings() {
    return jsonDecode(moneySettings) as Map<String, dynamic>;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'avatarPath': avatarPath,
      'languageCode': languageCode,
      'themeMode': themeMode,
      'syncEnabled': syncEnabled,
      'notificationsEnabled': notificationsEnabled,
      'notificationSound': notificationSound,
      'memoSettings': getMemoSettings(),
      'athkarSettings': getAthkarSettings(),
      'toolsSettings': getToolsSettings(),
      'moneySettings': getMoneySettings(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    final profile = UserProfile();
    profile.id = json['id'] ?? 0;
    profile.email = json['email'];
    profile.name = json['name'] ?? '';
    profile.avatarPath = json['avatarPath'];
    profile.languageCode = json['languageCode'] ?? 'en';
    profile.themeMode = json['themeMode'] ?? 'system';
    profile.syncEnabled = json['syncEnabled'] ?? false;
    profile.notificationsEnabled = json['notificationsEnabled'] ?? true;
    profile.notificationSound = json['notificationSound'];
    profile.memoSettings = jsonEncode(json['memoSettings'] ?? {});
    profile.athkarSettings = jsonEncode(json['athkarSettings'] ?? {});
    profile.toolsSettings = jsonEncode(json['toolsSettings'] ?? {});
    profile.moneySettings = jsonEncode(json['moneySettings'] ?? {});
    profile.createdAt = DateTime.parse(json['createdAt']);
    profile.updatedAt = DateTime.parse(json['updatedAt']);
    profile.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    profile.isDeleted = json['isDeleted'] ?? false;
    profile.syncId = json['syncId'];
    return profile;
  }
}
