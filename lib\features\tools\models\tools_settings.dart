import 'package:flutter/material.dart';

/// Grid snap options
enum GridSnapMode {
  none,
  pixel,
  grid,
  smart,
}

/// Export format options
enum ExportFormat {
  flutter,
  html,
  css,
  json,
  xml,
  pdf,
  png,
  svg,
}

/// Template category options
enum TemplateCategory {
  mobile,
  web,
  desktop,
  dashboard,
  form,
  card,
  list,
  navigation,
  custom,
}

/// Code generation style
enum CodeGenerationStyle {
  clean,
  verbose,
  minimal,
  documented,
}

/// Version control system
enum VersionControlSystem {
  none,
  git,
  svn,
  mercurial,
}

/// Deployment target
enum DeploymentTarget {
  none,
  firebase,
  github,
  netlify,
  vercel,
  aws,
  azure,
  custom,
}

/// Tools Builder application settings
class ToolsSettings {
  // UI Builder Settings
  final bool enableDragDrop;
  final bool enableSnapping;
  final GridSnapMode gridSnapMode;
  final int gridSize;
  final bool showGrid;
  final bool showRulers;
  final bool showGuidelines;
  final double zoomLevel;
  final bool enableAutoLayout;
  final bool enableResponsivePreview;
  final List<String> devicePresets;

  // Template Settings
  final String defaultTemplate;
  final List<TemplateCategory> enabledCategories;
  final bool enableCustomTemplates;
  final bool autoSaveTemplates;
  final String templatesDirectory;
  final bool enableTemplateSharing;
  final bool enableTemplateVersioning;

  // Code Generation Settings
  final CodeGenerationStyle codeStyle;
  final bool enableAutoGeneration;
  final bool generateComments;
  final bool generateDocumentation;
  final bool enableCodeFormatting;
  final String indentationStyle;
  final int indentationSize;
  final bool enableLinting;
  final List<String> enabledLintRules;

  // Component Library Settings
  final List<String> enabledLibraries;
  final bool enableCustomComponents;
  final String customComponentsPath;
  final bool enableComponentPreview;
  final bool enableComponentSearch;
  final bool enableComponentCategories;
  final Map<String, List<String>> componentCategories;

  // Export Settings
  final List<ExportFormat> enabledExportFormats;
  final ExportFormat defaultExportFormat;
  final String exportDirectory;
  final bool enableBatchExport;
  final bool enableExportPreview;
  final bool compressExports;
  final Map<ExportFormat, Map<String, dynamic>> exportSettings;

  // Auto-save Settings
  final bool enableAutoSave;
  final int autoSaveIntervalSeconds;
  final bool saveOnPreview;
  final bool saveOnExport;
  final int maxAutoSaveFiles;
  final String autoSaveDirectory;

  // Collaboration Settings
  final bool enableCollaboration;
  final bool enableRealTimeEditing;
  final bool enableComments;
  final bool enableVersionHistory;
  final bool enableUserPresence;
  final String collaborationServer;
  final int maxCollaborators;

  // Version Control Settings
  final VersionControlSystem versionControlSystem;
  final bool enableAutoCommit;
  final String defaultCommitMessage;
  final bool enableBranchManagement;
  final String defaultBranch;
  final bool enableMergeConflictResolution;
  final Map<String, String> versionControlConfig;

  // Deployment Settings
  final List<DeploymentTarget> enabledTargets;
  final DeploymentTarget defaultTarget;
  final bool enableAutoDeploy;
  final bool enablePreviewDeploy;
  final bool enableProductionDeploy;
  final Map<DeploymentTarget, Map<String, String>> deploymentConfigs;

  // Performance Settings
  final bool enablePerformanceMonitoring;
  final bool enableCodeOptimization;
  final bool enableAssetOptimization;
  final bool enableLazyLoading;
  final int maxUndoSteps;
  final bool enableMemoryOptimization;
  final int maxCacheSize;

  // Editor Settings
  final String editorTheme;
  final double editorFontSize;
  final String editorFontFamily;
  final bool enableSyntaxHighlighting;
  final bool enableCodeCompletion;
  final bool enableErrorHighlighting;
  final bool enableBracketMatching;
  final bool enableLineNumbers;
  final bool enableWordWrap;

  // Preview Settings
  final bool enableLivePreview;
  final bool enableHotReload;
  final bool enableDeviceSimulation;
  final bool enableNetworkSimulation;
  final List<String> previewDevices;
  final bool enableAccessibilityPreview;
  final bool enablePerformancePreview;

  // Integration Settings
  final bool enableFigmaIntegration;
  final bool enableSketchIntegration;
  final bool enableAdobeXDIntegration;
  final bool enableAPIIntegration;
  final bool enableDatabaseIntegration;
  final Map<String, Map<String, String>> integrationConfigs;

  // Backup & Sync Settings
  final bool enableAutoBackup;
  final bool backupToCloud;
  final bool syncAcrossDevices;
  final int backupFrequencyHours;
  final String lastBackupDate;
  final int maxBackupFiles;
  final bool enableIncrementalBackup;

  // Privacy & Security Settings
  final bool enableEncryption;
  final bool requirePinForAccess;
  final String pinHash;
  final bool enableBiometricAuth;
  final int autoLockMinutes;
  final bool enableAuditLog;
  final bool enableSecureMode;

  // Advanced Settings
  final bool enableDebugMode;
  final bool enableBetaFeatures;
  final bool enableExperimentalFeatures;
  final bool enableAnalytics;
  final bool enableCrashReporting;
  final bool enablePerformanceReporting;
  final Map<String, dynamic> experimentalFlags;

  const ToolsSettings({
    // UI Builder Settings
    this.enableDragDrop = true,
    this.enableSnapping = true,
    this.gridSnapMode = GridSnapMode.grid,
    this.gridSize = 8,
    this.showGrid = true,
    this.showRulers = false,
    this.showGuidelines = true,
    this.zoomLevel = 1.0,
    this.enableAutoLayout = true,
    this.enableResponsivePreview = true,
    this.devicePresets = const ['Mobile', 'Tablet', 'Desktop'],

    // Template Settings
    this.defaultTemplate = 'blank',
    this.enabledCategories = const [TemplateCategory.mobile, TemplateCategory.web],
    this.enableCustomTemplates = true,
    this.autoSaveTemplates = true,
    this.templatesDirectory = 'templates',
    this.enableTemplateSharing = false,
    this.enableTemplateVersioning = true,

    // Code Generation Settings
    this.codeStyle = CodeGenerationStyle.clean,
    this.enableAutoGeneration = true,
    this.generateComments = true,
    this.generateDocumentation = false,
    this.enableCodeFormatting = true,
    this.indentationStyle = 'spaces',
    this.indentationSize = 2,
    this.enableLinting = true,
    this.enabledLintRules = const [],

    // Component Library Settings
    this.enabledLibraries = const ['Material', 'Cupertino'],
    this.enableCustomComponents = true,
    this.customComponentsPath = 'components',
    this.enableComponentPreview = true,
    this.enableComponentSearch = true,
    this.enableComponentCategories = true,
    this.componentCategories = const {},

    // Export Settings
    this.enabledExportFormats = const [ExportFormat.flutter, ExportFormat.html],
    this.defaultExportFormat = ExportFormat.flutter,
    this.exportDirectory = 'exports',
    this.enableBatchExport = true,
    this.enableExportPreview = true,
    this.compressExports = false,
    this.exportSettings = const {},

    // Auto-save Settings
    this.enableAutoSave = true,
    this.autoSaveIntervalSeconds = 30,
    this.saveOnPreview = true,
    this.saveOnExport = true,
    this.maxAutoSaveFiles = 10,
    this.autoSaveDirectory = 'autosave',

    // Collaboration Settings
    this.enableCollaboration = false,
    this.enableRealTimeEditing = false,
    this.enableComments = true,
    this.enableVersionHistory = true,
    this.enableUserPresence = false,
    this.collaborationServer = '',
    this.maxCollaborators = 5,

    // Version Control Settings
    this.versionControlSystem = VersionControlSystem.none,
    this.enableAutoCommit = false,
    this.defaultCommitMessage = 'Auto-commit: UI changes',
    this.enableBranchManagement = false,
    this.defaultBranch = 'main',
    this.enableMergeConflictResolution = false,
    this.versionControlConfig = const {},

    // Deployment Settings
    this.enabledTargets = const [],
    this.defaultTarget = DeploymentTarget.none,
    this.enableAutoDeploy = false,
    this.enablePreviewDeploy = false,
    this.enableProductionDeploy = false,
    this.deploymentConfigs = const {},

    // Performance Settings
    this.enablePerformanceMonitoring = false,
    this.enableCodeOptimization = true,
    this.enableAssetOptimization = true,
    this.enableLazyLoading = true,
    this.maxUndoSteps = 50,
    this.enableMemoryOptimization = true,
    this.maxCacheSize = 100,

    // Editor Settings
    this.editorTheme = 'dark',
    this.editorFontSize = 14.0,
    this.editorFontFamily = 'Fira Code',
    this.enableSyntaxHighlighting = true,
    this.enableCodeCompletion = true,
    this.enableErrorHighlighting = true,
    this.enableBracketMatching = true,
    this.enableLineNumbers = true,
    this.enableWordWrap = false,

    // Preview Settings
    this.enableLivePreview = true,
    this.enableHotReload = true,
    this.enableDeviceSimulation = true,
    this.enableNetworkSimulation = false,
    this.previewDevices = const ['iPhone 12', 'iPad Pro', 'MacBook Pro'],
    this.enableAccessibilityPreview = false,
    this.enablePerformancePreview = false,

    // Integration Settings
    this.enableFigmaIntegration = false,
    this.enableSketchIntegration = false,
    this.enableAdobeXDIntegration = false,
    this.enableAPIIntegration = false,
    this.enableDatabaseIntegration = false,
    this.integrationConfigs = const {},

    // Backup & Sync Settings
    this.enableAutoBackup = true,
    this.backupToCloud = false,
    this.syncAcrossDevices = false,
    this.backupFrequencyHours = 24,
    this.lastBackupDate = '',
    this.maxBackupFiles = 10,
    this.enableIncrementalBackup = true,

    // Privacy & Security Settings
    this.enableEncryption = false,
    this.requirePinForAccess = false,
    this.pinHash = '',
    this.enableBiometricAuth = false,
    this.autoLockMinutes = 15,
    this.enableAuditLog = false,
    this.enableSecureMode = false,

    // Advanced Settings
    this.enableDebugMode = false,
    this.enableBetaFeatures = false,
    this.enableExperimentalFeatures = false,
    this.enableAnalytics = true,
    this.enableCrashReporting = true,
    this.enablePerformanceReporting = false,
    this.experimentalFlags = const {},
  });

  /// Create a copy with modified values
  ToolsSettings copyWith({
    bool? enableDragDrop,
    bool? enableSnapping,
    GridSnapMode? gridSnapMode,
    int? gridSize,
    bool? showGrid,
    bool? showRulers,
    bool? showGuidelines,
    double? zoomLevel,
    bool? enableAutoLayout,
    bool? enableResponsivePreview,
    List<String>? devicePresets,
    String? defaultTemplate,
    List<TemplateCategory>? enabledCategories,
    bool? enableCustomTemplates,
    bool? autoSaveTemplates,
    String? templatesDirectory,
    bool? enableTemplateSharing,
    bool? enableTemplateVersioning,
    CodeGenerationStyle? codeStyle,
    bool? enableAutoGeneration,
    bool? generateComments,
    bool? generateDocumentation,
    bool? enableCodeFormatting,
    String? indentationStyle,
    int? indentationSize,
    bool? enableLinting,
    List<String>? enabledLintRules,
    List<String>? enabledLibraries,
    bool? enableCustomComponents,
    String? customComponentsPath,
    bool? enableComponentPreview,
    bool? enableComponentSearch,
    bool? enableComponentCategories,
    Map<String, List<String>>? componentCategories,
    List<ExportFormat>? enabledExportFormats,
    ExportFormat? defaultExportFormat,
    String? exportDirectory,
    bool? enableBatchExport,
    bool? enableExportPreview,
    bool? compressExports,
    Map<ExportFormat, Map<String, dynamic>>? exportSettings,
    bool? enableAutoSave,
    int? autoSaveIntervalSeconds,
    bool? saveOnPreview,
    bool? saveOnExport,
    int? maxAutoSaveFiles,
    String? autoSaveDirectory,
    bool? enableCollaboration,
    bool? enableRealTimeEditing,
    bool? enableComments,
    bool? enableVersionHistory,
    bool? enableUserPresence,
    String? collaborationServer,
    int? maxCollaborators,
    VersionControlSystem? versionControlSystem,
    bool? enableAutoCommit,
    String? defaultCommitMessage,
    bool? enableBranchManagement,
    String? defaultBranch,
    bool? enableMergeConflictResolution,
    Map<String, String>? versionControlConfig,
    List<DeploymentTarget>? enabledTargets,
    DeploymentTarget? defaultTarget,
    bool? enableAutoDeploy,
    bool? enablePreviewDeploy,
    bool? enableProductionDeploy,
    Map<DeploymentTarget, Map<String, String>>? deploymentConfigs,
    bool? enablePerformanceMonitoring,
    bool? enableCodeOptimization,
    bool? enableAssetOptimization,
    bool? enableLazyLoading,
    int? maxUndoSteps,
    bool? enableMemoryOptimization,
    int? maxCacheSize,
    String? editorTheme,
    double? editorFontSize,
    String? editorFontFamily,
    bool? enableSyntaxHighlighting,
    bool? enableCodeCompletion,
    bool? enableErrorHighlighting,
    bool? enableBracketMatching,
    bool? enableLineNumbers,
    bool? enableWordWrap,
    bool? enableLivePreview,
    bool? enableHotReload,
    bool? enableDeviceSimulation,
    bool? enableNetworkSimulation,
    List<String>? previewDevices,
    bool? enableAccessibilityPreview,
    bool? enablePerformancePreview,
    bool? enableFigmaIntegration,
    bool? enableSketchIntegration,
    bool? enableAdobeXDIntegration,
    bool? enableAPIIntegration,
    bool? enableDatabaseIntegration,
    Map<String, Map<String, String>>? integrationConfigs,
    bool? enableAutoBackup,
    bool? backupToCloud,
    bool? syncAcrossDevices,
    int? backupFrequencyHours,
    String? lastBackupDate,
    int? maxBackupFiles,
    bool? enableIncrementalBackup,
    bool? enableEncryption,
    bool? requirePinForAccess,
    String? pinHash,
    bool? enableBiometricAuth,
    int? autoLockMinutes,
    bool? enableAuditLog,
    bool? enableSecureMode,
    bool? enableDebugMode,
    bool? enableBetaFeatures,
    bool? enableExperimentalFeatures,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    bool? enablePerformanceReporting,
    Map<String, dynamic>? experimentalFlags,
  }) {
    return ToolsSettings(
      enableDragDrop: enableDragDrop ?? this.enableDragDrop,
      enableSnapping: enableSnapping ?? this.enableSnapping,
      gridSnapMode: gridSnapMode ?? this.gridSnapMode,
      gridSize: gridSize ?? this.gridSize,
      showGrid: showGrid ?? this.showGrid,
      showRulers: showRulers ?? this.showRulers,
      showGuidelines: showGuidelines ?? this.showGuidelines,
      zoomLevel: zoomLevel ?? this.zoomLevel,
      enableAutoLayout: enableAutoLayout ?? this.enableAutoLayout,
      enableResponsivePreview: enableResponsivePreview ?? this.enableResponsivePreview,
      devicePresets: devicePresets ?? this.devicePresets,
      defaultTemplate: defaultTemplate ?? this.defaultTemplate,
      enabledCategories: enabledCategories ?? this.enabledCategories,
      enableCustomTemplates: enableCustomTemplates ?? this.enableCustomTemplates,
      autoSaveTemplates: autoSaveTemplates ?? this.autoSaveTemplates,
      templatesDirectory: templatesDirectory ?? this.templatesDirectory,
      enableTemplateSharing: enableTemplateSharing ?? this.enableTemplateSharing,
      enableTemplateVersioning: enableTemplateVersioning ?? this.enableTemplateVersioning,
      codeStyle: codeStyle ?? this.codeStyle,
      enableAutoGeneration: enableAutoGeneration ?? this.enableAutoGeneration,
      generateComments: generateComments ?? this.generateComments,
      generateDocumentation: generateDocumentation ?? this.generateDocumentation,
      enableCodeFormatting: enableCodeFormatting ?? this.enableCodeFormatting,
      indentationStyle: indentationStyle ?? this.indentationStyle,
      indentationSize: indentationSize ?? this.indentationSize,
      enableLinting: enableLinting ?? this.enableLinting,
      enabledLintRules: enabledLintRules ?? this.enabledLintRules,
      enabledLibraries: enabledLibraries ?? this.enabledLibraries,
      enableCustomComponents: enableCustomComponents ?? this.enableCustomComponents,
      customComponentsPath: customComponentsPath ?? this.customComponentsPath,
      enableComponentPreview: enableComponentPreview ?? this.enableComponentPreview,
      enableComponentSearch: enableComponentSearch ?? this.enableComponentSearch,
      enableComponentCategories: enableComponentCategories ?? this.enableComponentCategories,
      componentCategories: componentCategories ?? this.componentCategories,
      enabledExportFormats: enabledExportFormats ?? this.enabledExportFormats,
      defaultExportFormat: defaultExportFormat ?? this.defaultExportFormat,
      exportDirectory: exportDirectory ?? this.exportDirectory,
      enableBatchExport: enableBatchExport ?? this.enableBatchExport,
      enableExportPreview: enableExportPreview ?? this.enableExportPreview,
      compressExports: compressExports ?? this.compressExports,
      exportSettings: exportSettings ?? this.exportSettings,
      enableAutoSave: enableAutoSave ?? this.enableAutoSave,
      autoSaveIntervalSeconds: autoSaveIntervalSeconds ?? this.autoSaveIntervalSeconds,
      saveOnPreview: saveOnPreview ?? this.saveOnPreview,
      saveOnExport: saveOnExport ?? this.saveOnExport,
      maxAutoSaveFiles: maxAutoSaveFiles ?? this.maxAutoSaveFiles,
      autoSaveDirectory: autoSaveDirectory ?? this.autoSaveDirectory,
      enableCollaboration: enableCollaboration ?? this.enableCollaboration,
      enableRealTimeEditing: enableRealTimeEditing ?? this.enableRealTimeEditing,
      enableComments: enableComments ?? this.enableComments,
      enableVersionHistory: enableVersionHistory ?? this.enableVersionHistory,
      enableUserPresence: enableUserPresence ?? this.enableUserPresence,
      collaborationServer: collaborationServer ?? this.collaborationServer,
      maxCollaborators: maxCollaborators ?? this.maxCollaborators,
      versionControlSystem: versionControlSystem ?? this.versionControlSystem,
      enableAutoCommit: enableAutoCommit ?? this.enableAutoCommit,
      defaultCommitMessage: defaultCommitMessage ?? this.defaultCommitMessage,
      enableBranchManagement: enableBranchManagement ?? this.enableBranchManagement,
      defaultBranch: defaultBranch ?? this.defaultBranch,
      enableMergeConflictResolution: enableMergeConflictResolution ?? this.enableMergeConflictResolution,
      versionControlConfig: versionControlConfig ?? this.versionControlConfig,
      enabledTargets: enabledTargets ?? this.enabledTargets,
      defaultTarget: defaultTarget ?? this.defaultTarget,
      enableAutoDeploy: enableAutoDeploy ?? this.enableAutoDeploy,
      enablePreviewDeploy: enablePreviewDeploy ?? this.enablePreviewDeploy,
      enableProductionDeploy: enableProductionDeploy ?? this.enableProductionDeploy,
      deploymentConfigs: deploymentConfigs ?? this.deploymentConfigs,
      enablePerformanceMonitoring: enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      enableCodeOptimization: enableCodeOptimization ?? this.enableCodeOptimization,
      enableAssetOptimization: enableAssetOptimization ?? this.enableAssetOptimization,
      enableLazyLoading: enableLazyLoading ?? this.enableLazyLoading,
      maxUndoSteps: maxUndoSteps ?? this.maxUndoSteps,
      enableMemoryOptimization: enableMemoryOptimization ?? this.enableMemoryOptimization,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      editorTheme: editorTheme ?? this.editorTheme,
      editorFontSize: editorFontSize ?? this.editorFontSize,
      editorFontFamily: editorFontFamily ?? this.editorFontFamily,
      enableSyntaxHighlighting: enableSyntaxHighlighting ?? this.enableSyntaxHighlighting,
      enableCodeCompletion: enableCodeCompletion ?? this.enableCodeCompletion,
      enableErrorHighlighting: enableErrorHighlighting ?? this.enableErrorHighlighting,
      enableBracketMatching: enableBracketMatching ?? this.enableBracketMatching,
      enableLineNumbers: enableLineNumbers ?? this.enableLineNumbers,
      enableWordWrap: enableWordWrap ?? this.enableWordWrap,
      enableLivePreview: enableLivePreview ?? this.enableLivePreview,
      enableHotReload: enableHotReload ?? this.enableHotReload,
      enableDeviceSimulation: enableDeviceSimulation ?? this.enableDeviceSimulation,
      enableNetworkSimulation: enableNetworkSimulation ?? this.enableNetworkSimulation,
      previewDevices: previewDevices ?? this.previewDevices,
      enableAccessibilityPreview: enableAccessibilityPreview ?? this.enableAccessibilityPreview,
      enablePerformancePreview: enablePerformancePreview ?? this.enablePerformancePreview,
      enableFigmaIntegration: enableFigmaIntegration ?? this.enableFigmaIntegration,
      enableSketchIntegration: enableSketchIntegration ?? this.enableSketchIntegration,
      enableAdobeXDIntegration: enableAdobeXDIntegration ?? this.enableAdobeXDIntegration,
      enableAPIIntegration: enableAPIIntegration ?? this.enableAPIIntegration,
      enableDatabaseIntegration: enableDatabaseIntegration ?? this.enableDatabaseIntegration,
      integrationConfigs: integrationConfigs ?? this.integrationConfigs,
      enableAutoBackup: enableAutoBackup ?? this.enableAutoBackup,
      backupToCloud: backupToCloud ?? this.backupToCloud,
      syncAcrossDevices: syncAcrossDevices ?? this.syncAcrossDevices,
      backupFrequencyHours: backupFrequencyHours ?? this.backupFrequencyHours,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
      maxBackupFiles: maxBackupFiles ?? this.maxBackupFiles,
      enableIncrementalBackup: enableIncrementalBackup ?? this.enableIncrementalBackup,
      enableEncryption: enableEncryption ?? this.enableEncryption,
      requirePinForAccess: requirePinForAccess ?? this.requirePinForAccess,
      pinHash: pinHash ?? this.pinHash,
      enableBiometricAuth: enableBiometricAuth ?? this.enableBiometricAuth,
      autoLockMinutes: autoLockMinutes ?? this.autoLockMinutes,
      enableAuditLog: enableAuditLog ?? this.enableAuditLog,
      enableSecureMode: enableSecureMode ?? this.enableSecureMode,
      enableDebugMode: enableDebugMode ?? this.enableDebugMode,
      enableBetaFeatures: enableBetaFeatures ?? this.enableBetaFeatures,
      enableExperimentalFeatures: enableExperimentalFeatures ?? this.enableExperimentalFeatures,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      enablePerformanceReporting: enablePerformanceReporting ?? this.enablePerformanceReporting,
      experimentalFlags: experimentalFlags ?? this.experimentalFlags,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'enableDragDrop': enableDragDrop,
      'enableSnapping': enableSnapping,
      'gridSnapMode': gridSnapMode.index,
      'gridSize': gridSize,
      'showGrid': showGrid,
      'showRulers': showRulers,
      'showGuidelines': showGuidelines,
      'zoomLevel': zoomLevel,
      'enableAutoLayout': enableAutoLayout,
      'enableResponsivePreview': enableResponsivePreview,
      'devicePresets': devicePresets,
      'defaultTemplate': defaultTemplate,
      'enabledCategories': enabledCategories.map((c) => c.index).toList(),
      'enableCustomTemplates': enableCustomTemplates,
      'autoSaveTemplates': autoSaveTemplates,
      'templatesDirectory': templatesDirectory,
      'enableTemplateSharing': enableTemplateSharing,
      'enableTemplateVersioning': enableTemplateVersioning,
      'codeStyle': codeStyle.index,
      'enableAutoGeneration': enableAutoGeneration,
      'generateComments': generateComments,
      'generateDocumentation': generateDocumentation,
      'enableCodeFormatting': enableCodeFormatting,
      'indentationStyle': indentationStyle,
      'indentationSize': indentationSize,
      'enableLinting': enableLinting,
      'enabledLintRules': enabledLintRules,
      'enabledLibraries': enabledLibraries,
      'enableCustomComponents': enableCustomComponents,
      'customComponentsPath': customComponentsPath,
      'enableComponentPreview': enableComponentPreview,
      'enableComponentSearch': enableComponentSearch,
      'enableComponentCategories': enableComponentCategories,
      'componentCategories': componentCategories,
      'enabledExportFormats': enabledExportFormats.map((f) => f.index).toList(),
      'defaultExportFormat': defaultExportFormat.index,
      'exportDirectory': exportDirectory,
      'enableBatchExport': enableBatchExport,
      'enableExportPreview': enableExportPreview,
      'compressExports': compressExports,
      'exportSettings': exportSettings.map((k, v) => MapEntry(k.index, v)),
      'enableAutoSave': enableAutoSave,
      'autoSaveIntervalSeconds': autoSaveIntervalSeconds,
      'saveOnPreview': saveOnPreview,
      'saveOnExport': saveOnExport,
      'maxAutoSaveFiles': maxAutoSaveFiles,
      'autoSaveDirectory': autoSaveDirectory,
      'enableCollaboration': enableCollaboration,
      'enableRealTimeEditing': enableRealTimeEditing,
      'enableComments': enableComments,
      'enableVersionHistory': enableVersionHistory,
      'enableUserPresence': enableUserPresence,
      'collaborationServer': collaborationServer,
      'maxCollaborators': maxCollaborators,
      'versionControlSystem': versionControlSystem.index,
      'enableAutoCommit': enableAutoCommit,
      'defaultCommitMessage': defaultCommitMessage,
      'enableBranchManagement': enableBranchManagement,
      'defaultBranch': defaultBranch,
      'enableMergeConflictResolution': enableMergeConflictResolution,
      'versionControlConfig': versionControlConfig,
      'enabledTargets': enabledTargets.map((t) => t.index).toList(),
      'defaultTarget': defaultTarget.index,
      'enableAutoDeploy': enableAutoDeploy,
      'enablePreviewDeploy': enablePreviewDeploy,
      'enableProductionDeploy': enableProductionDeploy,
      'deploymentConfigs': deploymentConfigs.map((k, v) => MapEntry(k.index, v)),
      'enablePerformanceMonitoring': enablePerformanceMonitoring,
      'enableCodeOptimization': enableCodeOptimization,
      'enableAssetOptimization': enableAssetOptimization,
      'enableLazyLoading': enableLazyLoading,
      'maxUndoSteps': maxUndoSteps,
      'enableMemoryOptimization': enableMemoryOptimization,
      'maxCacheSize': maxCacheSize,
      'editorTheme': editorTheme,
      'editorFontSize': editorFontSize,
      'editorFontFamily': editorFontFamily,
      'enableSyntaxHighlighting': enableSyntaxHighlighting,
      'enableCodeCompletion': enableCodeCompletion,
      'enableErrorHighlighting': enableErrorHighlighting,
      'enableBracketMatching': enableBracketMatching,
      'enableLineNumbers': enableLineNumbers,
      'enableWordWrap': enableWordWrap,
      'enableLivePreview': enableLivePreview,
      'enableHotReload': enableHotReload,
      'enableDeviceSimulation': enableDeviceSimulation,
      'enableNetworkSimulation': enableNetworkSimulation,
      'previewDevices': previewDevices,
      'enableAccessibilityPreview': enableAccessibilityPreview,
      'enablePerformancePreview': enablePerformancePreview,
      'enableFigmaIntegration': enableFigmaIntegration,
      'enableSketchIntegration': enableSketchIntegration,
      'enableAdobeXDIntegration': enableAdobeXDIntegration,
      'enableAPIIntegration': enableAPIIntegration,
      'enableDatabaseIntegration': enableDatabaseIntegration,
      'integrationConfigs': integrationConfigs,
      'enableAutoBackup': enableAutoBackup,
      'backupToCloud': backupToCloud,
      'syncAcrossDevices': syncAcrossDevices,
      'backupFrequencyHours': backupFrequencyHours,
      'lastBackupDate': lastBackupDate,
      'maxBackupFiles': maxBackupFiles,
      'enableIncrementalBackup': enableIncrementalBackup,
      'enableEncryption': enableEncryption,
      'requirePinForAccess': requirePinForAccess,
      'pinHash': pinHash,
      'enableBiometricAuth': enableBiometricAuth,
      'autoLockMinutes': autoLockMinutes,
      'enableAuditLog': enableAuditLog,
      'enableSecureMode': enableSecureMode,
      'enableDebugMode': enableDebugMode,
      'enableBetaFeatures': enableBetaFeatures,
      'enableExperimentalFeatures': enableExperimentalFeatures,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'enablePerformanceReporting': enablePerformanceReporting,
      'experimentalFlags': experimentalFlags,
    };
  }

  /// Create from JSON
  factory ToolsSettings.fromJson(Map<String, dynamic> json) {
    return ToolsSettings(
      enableDragDrop: json['enableDragDrop'] ?? true,
      enableSnapping: json['enableSnapping'] ?? true,
      gridSnapMode: GridSnapMode.values[json['gridSnapMode'] ?? 2],
      gridSize: json['gridSize'] ?? 8,
      showGrid: json['showGrid'] ?? true,
      showRulers: json['showRulers'] ?? false,
      showGuidelines: json['showGuidelines'] ?? true,
      zoomLevel: (json['zoomLevel'] as num?)?.toDouble() ?? 1.0,
      enableAutoLayout: json['enableAutoLayout'] ?? true,
      enableResponsivePreview: json['enableResponsivePreview'] ?? true,
      devicePresets: List<String>.from(json['devicePresets'] ?? ['Mobile', 'Tablet', 'Desktop']),
      defaultTemplate: json['defaultTemplate'] ?? 'blank',
      enabledCategories: (json['enabledCategories'] as List<dynamic>?)
          ?.map((i) => TemplateCategory.values[i])
          .toList() ?? [TemplateCategory.mobile, TemplateCategory.web],
      enableCustomTemplates: json['enableCustomTemplates'] ?? true,
      autoSaveTemplates: json['autoSaveTemplates'] ?? true,
      templatesDirectory: json['templatesDirectory'] ?? 'templates',
      enableTemplateSharing: json['enableTemplateSharing'] ?? false,
      enableTemplateVersioning: json['enableTemplateVersioning'] ?? true,
      codeStyle: CodeGenerationStyle.values[json['codeStyle'] ?? 0],
      enableAutoGeneration: json['enableAutoGeneration'] ?? true,
      generateComments: json['generateComments'] ?? true,
      generateDocumentation: json['generateDocumentation'] ?? false,
      enableCodeFormatting: json['enableCodeFormatting'] ?? true,
      indentationStyle: json['indentationStyle'] ?? 'spaces',
      indentationSize: json['indentationSize'] ?? 2,
      enableLinting: json['enableLinting'] ?? true,
      enabledLintRules: List<String>.from(json['enabledLintRules'] ?? []),
      enabledLibraries: List<String>.from(json['enabledLibraries'] ?? ['Material', 'Cupertino']),
      enableCustomComponents: json['enableCustomComponents'] ?? true,
      customComponentsPath: json['customComponentsPath'] ?? 'components',
      enableComponentPreview: json['enableComponentPreview'] ?? true,
      enableComponentSearch: json['enableComponentSearch'] ?? true,
      enableComponentCategories: json['enableComponentCategories'] ?? true,
      componentCategories: Map<String, List<String>>.from(json['componentCategories'] ?? {}),
      enabledExportFormats: (json['enabledExportFormats'] as List<dynamic>?)
          ?.map((i) => ExportFormat.values[i])
          .toList() ?? [ExportFormat.flutter, ExportFormat.html],
      defaultExportFormat: ExportFormat.values[json['defaultExportFormat'] ?? 0],
      exportDirectory: json['exportDirectory'] ?? 'exports',
      enableBatchExport: json['enableBatchExport'] ?? true,
      enableExportPreview: json['enableExportPreview'] ?? true,
      compressExports: json['compressExports'] ?? false,
      exportSettings: (json['exportSettings'] as Map<String, dynamic>?)
          ?.map((k, v) => MapEntry(ExportFormat.values[int.parse(k)], v as Map<String, dynamic>)) ?? {},
      enableAutoSave: json['enableAutoSave'] ?? true,
      autoSaveIntervalSeconds: json['autoSaveIntervalSeconds'] ?? 30,
      saveOnPreview: json['saveOnPreview'] ?? true,
      saveOnExport: json['saveOnExport'] ?? true,
      maxAutoSaveFiles: json['maxAutoSaveFiles'] ?? 10,
      autoSaveDirectory: json['autoSaveDirectory'] ?? 'autosave',
      enableCollaboration: json['enableCollaboration'] ?? false,
      enableRealTimeEditing: json['enableRealTimeEditing'] ?? false,
      enableComments: json['enableComments'] ?? true,
      enableVersionHistory: json['enableVersionHistory'] ?? true,
      enableUserPresence: json['enableUserPresence'] ?? false,
      collaborationServer: json['collaborationServer'] ?? '',
      maxCollaborators: json['maxCollaborators'] ?? 5,
      versionControlSystem: VersionControlSystem.values[json['versionControlSystem'] ?? 0],
      enableAutoCommit: json['enableAutoCommit'] ?? false,
      defaultCommitMessage: json['defaultCommitMessage'] ?? 'Auto-commit: UI changes',
      enableBranchManagement: json['enableBranchManagement'] ?? false,
      defaultBranch: json['defaultBranch'] ?? 'main',
      enableMergeConflictResolution: json['enableMergeConflictResolution'] ?? false,
      versionControlConfig: Map<String, String>.from(json['versionControlConfig'] ?? {}),
      enabledTargets: (json['enabledTargets'] as List<dynamic>?)
          ?.map((i) => DeploymentTarget.values[i])
          .toList() ?? [],
      defaultTarget: DeploymentTarget.values[json['defaultTarget'] ?? 0],
      enableAutoDeploy: json['enableAutoDeploy'] ?? false,
      enablePreviewDeploy: json['enablePreviewDeploy'] ?? false,
      enableProductionDeploy: json['enableProductionDeploy'] ?? false,
      deploymentConfigs: (json['deploymentConfigs'] as Map<String, dynamic>?)
          ?.map((k, v) => MapEntry(DeploymentTarget.values[int.parse(k)], v as Map<String, String>)) ?? {},
      enablePerformanceMonitoring: json['enablePerformanceMonitoring'] ?? false,
      enableCodeOptimization: json['enableCodeOptimization'] ?? true,
      enableAssetOptimization: json['enableAssetOptimization'] ?? true,
      enableLazyLoading: json['enableLazyLoading'] ?? true,
      maxUndoSteps: json['maxUndoSteps'] ?? 50,
      enableMemoryOptimization: json['enableMemoryOptimization'] ?? true,
      maxCacheSize: json['maxCacheSize'] ?? 100,
      editorTheme: json['editorTheme'] ?? 'dark',
      editorFontSize: (json['editorFontSize'] as num?)?.toDouble() ?? 14.0,
      editorFontFamily: json['editorFontFamily'] ?? 'Fira Code',
      enableSyntaxHighlighting: json['enableSyntaxHighlighting'] ?? true,
      enableCodeCompletion: json['enableCodeCompletion'] ?? true,
      enableErrorHighlighting: json['enableErrorHighlighting'] ?? true,
      enableBracketMatching: json['enableBracketMatching'] ?? true,
      enableLineNumbers: json['enableLineNumbers'] ?? true,
      enableWordWrap: json['enableWordWrap'] ?? false,
      enableLivePreview: json['enableLivePreview'] ?? true,
      enableHotReload: json['enableHotReload'] ?? true,
      enableDeviceSimulation: json['enableDeviceSimulation'] ?? true,
      enableNetworkSimulation: json['enableNetworkSimulation'] ?? false,
      previewDevices: List<String>.from(json['previewDevices'] ?? ['iPhone 12', 'iPad Pro', 'MacBook Pro']),
      enableAccessibilityPreview: json['enableAccessibilityPreview'] ?? false,
      enablePerformancePreview: json['enablePerformancePreview'] ?? false,
      enableFigmaIntegration: json['enableFigmaIntegration'] ?? false,
      enableSketchIntegration: json['enableSketchIntegration'] ?? false,
      enableAdobeXDIntegration: json['enableAdobeXDIntegration'] ?? false,
      enableAPIIntegration: json['enableAPIIntegration'] ?? false,
      enableDatabaseIntegration: json['enableDatabaseIntegration'] ?? false,
      integrationConfigs: Map<String, Map<String, String>>.from(json['integrationConfigs'] ?? {}),
      enableAutoBackup: json['enableAutoBackup'] ?? true,
      backupToCloud: json['backupToCloud'] ?? false,
      syncAcrossDevices: json['syncAcrossDevices'] ?? false,
      backupFrequencyHours: json['backupFrequencyHours'] ?? 24,
      lastBackupDate: json['lastBackupDate'] ?? '',
      maxBackupFiles: json['maxBackupFiles'] ?? 10,
      enableIncrementalBackup: json['enableIncrementalBackup'] ?? true,
      enableEncryption: json['enableEncryption'] ?? false,
      requirePinForAccess: json['requirePinForAccess'] ?? false,
      pinHash: json['pinHash'] ?? '',
      enableBiometricAuth: json['enableBiometricAuth'] ?? false,
      autoLockMinutes: json['autoLockMinutes'] ?? 15,
      enableAuditLog: json['enableAuditLog'] ?? false,
      enableSecureMode: json['enableSecureMode'] ?? false,
      enableDebugMode: json['enableDebugMode'] ?? false,
      enableBetaFeatures: json['enableBetaFeatures'] ?? false,
      enableExperimentalFeatures: json['enableExperimentalFeatures'] ?? false,
      enableAnalytics: json['enableAnalytics'] ?? true,
      enableCrashReporting: json['enableCrashReporting'] ?? true,
      enablePerformanceReporting: json['enablePerformanceReporting'] ?? false,
      experimentalFlags: Map<String, dynamic>.from(json['experimentalFlags'] ?? {}),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ToolsSettings &&
        other.enableDragDrop == enableDragDrop &&
        other.gridSize == gridSize &&
        other.codeStyle == codeStyle;
    // Note: Truncated for brevity - in real implementation, compare all fields
  }

  @override
  int get hashCode => Object.hashAll([
        enableDragDrop,
        gridSize,
        codeStyle,
        // Note: Truncated for brevity - in real implementation, hash all fields
      ]);

  @override
  String toString() => 'ToolsSettings(gridSize: $gridSize, codeStyle: $codeStyle)';
}
