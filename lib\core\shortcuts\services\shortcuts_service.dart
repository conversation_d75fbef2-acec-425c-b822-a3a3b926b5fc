import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ShortcutsService {
  static final ShortcutsService _instance = ShortcutsService._internal();
  factory ShortcutsService() => _instance;
  ShortcutsService._internal();

  static const String _shortcutsKey = 'keyboard_shortcuts';
  static const String _quickActionsKey = 'quick_actions';
  static const String _settingsKey = 'shortcuts_settings';

  final Map<String, KeyboardShortcut> _shortcuts = {};
  final Map<String, QuickAction> _quickActions = {};
  final StreamController<ShortcutEvent> _eventController = 
      StreamController<ShortcutEvent>.broadcast();

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  ShortcutsSettings _settings = ShortcutsSettings();

  // Getters
  Stream<ShortcutEvent> get eventStream => _eventController.stream;
  Map<String, KeyboardShortcut> get shortcuts => Map.unmodifiable(_shortcuts);
  Map<String, QuickAction> get quickActions => Map.unmodifiable(_quickActions);
  ShortcutsSettings get settings => _settings;
  bool get isInitialized => _isInitialized;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      await _loadShortcuts();
      await _loadQuickActions();
      _registerDefaultShortcuts();
      _registerDefaultQuickActions();
      _isInitialized = true;
      debugPrint('ShortcutsService initialized');
    } catch (e) {
      debugPrint('Error initializing ShortcutsService: $e');
    }
  }

  // Keyboard shortcuts
  void registerShortcut(KeyboardShortcut shortcut) {
    _shortcuts[shortcut.id] = shortcut;
    _saveShortcuts();
  }

  void unregisterShortcut(String shortcutId) {
    _shortcuts.remove(shortcutId);
    _saveShortcuts();
  }

  bool handleKeyEvent(KeyEvent event) {
    if (!_settings.keyboardShortcutsEnabled) return false;
    if (event is! KeyDownEvent) return false;

    final pressedKeys = HardwareKeyboard.instance.logicalKeysPressed;
    
    for (final shortcut in _shortcuts.values) {
      if (shortcut.matches(pressedKeys)) {
        _executeShortcut(shortcut);
        return true;
      }
    }

    return false;
  }

  KeyboardShortcut? findShortcutByKeys(Set<LogicalKeyboardKey> keys) {
    for (final shortcut in _shortcuts.values) {
      if (shortcut.matches(keys)) {
        return shortcut;
      }
    }
    return null;
  }

  List<KeyboardShortcut> getShortcutsByCategory(String category) {
    return _shortcuts.values
        .where((s) => s.category == category)
        .toList();
  }

  // Quick actions
  void registerQuickAction(QuickAction action) {
    _quickActions[action.id] = action;
    _saveQuickActions();
  }

  void unregisterQuickAction(String actionId) {
    _quickActions.remove(actionId);
    _saveQuickActions();
  }

  void executeQuickAction(String actionId) {
    final action = _quickActions[actionId];
    if (action != null) {
      _executeQuickAction(action);
    }
  }

  List<QuickAction> searchQuickActions(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _quickActions.values
        .where((action) =>
            action.title.toLowerCase().contains(lowercaseQuery) ||
            action.description.toLowerCase().contains(lowercaseQuery) ||
            action.keywords.any((k) => k.toLowerCase().contains(lowercaseQuery)))
        .toList();
  }

  List<QuickAction> getQuickActionsByCategory(String category) {
    return _quickActions.values
        .where((a) => a.category == category)
        .toList();
  }

  List<QuickAction> getFavoriteQuickActions() {
    return _quickActions.values
        .where((a) => a.isFavorite)
        .toList();
  }

  List<QuickAction> getRecentQuickActions() {
    final recent = _quickActions.values
        .where((a) => a.lastUsed != null)
        .toList();
    recent.sort((a, b) => b.lastUsed!.compareTo(a.lastUsed!));
    return recent.take(10).toList();
  }

  // Batch operations
  void executeBatchActions(List<String> actionIds) {
    for (final actionId in actionIds) {
      executeQuickAction(actionId);
    }
  }

  // Settings
  Future<void> updateSettings(ShortcutsSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
  }

  // Private methods
  void _executeShortcut(KeyboardShortcut shortcut) {
    try {
      shortcut.action();
      shortcut.incrementUsageCount();
      _saveShortcuts();
      
      _eventController.add(ShortcutEvent.shortcutExecuted(shortcut.id));
      debugPrint('Executed shortcut: ${shortcut.title}');
    } catch (e) {
      debugPrint('Error executing shortcut ${shortcut.id}: $e');
      _eventController.add(ShortcutEvent.shortcutFailed(shortcut.id, e.toString()));
    }
  }

  void _executeQuickAction(QuickAction action) {
    try {
      action.action();
      action.markAsUsed();
      _saveQuickActions();
      
      _eventController.add(ShortcutEvent.quickActionExecuted(action.id));
      debugPrint('Executed quick action: ${action.title}');
    } catch (e) {
      debugPrint('Error executing quick action ${action.id}: $e');
      _eventController.add(ShortcutEvent.quickActionFailed(action.id, e.toString()));
    }
  }

  void _registerDefaultShortcuts() {
    // Navigation shortcuts
    registerShortcut(KeyboardShortcut(
      id: 'nav_dashboard',
      title: 'Go to Dashboard',
      description: 'Navigate to the main dashboard',
      category: 'Navigation',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.digit1},
      action: () => _eventController.add(ShortcutEvent.navigate('/dashboard')),
    ));

    registerShortcut(KeyboardShortcut(
      id: 'nav_notes',
      title: 'Go to Notes',
      description: 'Navigate to the notes section',
      category: 'Navigation',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.digit2},
      action: () => _eventController.add(ShortcutEvent.navigate('/notes')),
    ));

    registerShortcut(KeyboardShortcut(
      id: 'nav_todos',
      title: 'Go to Todos',
      description: 'Navigate to the todos section',
      category: 'Navigation',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.digit3},
      action: () => _eventController.add(ShortcutEvent.navigate('/todos')),
    ));

    // Action shortcuts
    registerShortcut(KeyboardShortcut(
      id: 'create_note',
      title: 'Create New Note',
      description: 'Create a new note',
      category: 'Actions',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.keyN},
      action: () => _eventController.add(ShortcutEvent.action('create_note')),
    ));

    registerShortcut(KeyboardShortcut(
      id: 'create_todo',
      title: 'Create New Todo',
      description: 'Create a new todo item',
      category: 'Actions',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.keyT},
      action: () => _eventController.add(ShortcutEvent.action('create_todo')),
    ));

    registerShortcut(KeyboardShortcut(
      id: 'search',
      title: 'Global Search',
      description: 'Open global search',
      category: 'Actions',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.keyF},
      action: () => _eventController.add(ShortcutEvent.action('global_search')),
    ));

    // System shortcuts
    registerShortcut(KeyboardShortcut(
      id: 'settings',
      title: 'Open Settings',
      description: 'Open application settings',
      category: 'System',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.comma},
      action: () => _eventController.add(ShortcutEvent.navigate('/settings')),
    ));

    registerShortcut(KeyboardShortcut(
      id: 'sync_now',
      title: 'Sync Now',
      description: 'Trigger immediate sync',
      category: 'System',
      keys: {LogicalKeyboardKey.control, LogicalKeyboardKey.keyS},
      action: () => _eventController.add(ShortcutEvent.action('sync_now')),
    ));
  }

  void _registerDefaultQuickActions() {
    // Note actions
    registerQuickAction(QuickAction(
      id: 'create_note',
      title: 'Create Note',
      description: 'Create a new note',
      category: 'Notes',
      icon: 'note_add',
      keywords: ['note', 'create', 'new', 'write'],
      action: () => _eventController.add(ShortcutEvent.action('create_note')),
    ));

    registerQuickAction(QuickAction(
      id: 'search_notes',
      title: 'Search Notes',
      description: 'Search through your notes',
      category: 'Notes',
      icon: 'search',
      keywords: ['search', 'find', 'notes'],
      action: () => _eventController.add(ShortcutEvent.action('search_notes')),
    ));

    // Todo actions
    registerQuickAction(QuickAction(
      id: 'create_todo',
      title: 'Create Todo',
      description: 'Create a new todo item',
      category: 'Todos',
      icon: 'add_task',
      keywords: ['todo', 'task', 'create', 'new'],
      action: () => _eventController.add(ShortcutEvent.action('create_todo')),
    ));

    registerQuickAction(QuickAction(
      id: 'view_overdue_todos',
      title: 'View Overdue Todos',
      description: 'Show all overdue todo items',
      category: 'Todos',
      icon: 'schedule',
      keywords: ['overdue', 'late', 'todos', 'tasks'],
      action: () => _eventController.add(ShortcutEvent.action('view_overdue_todos')),
    ));

    // Voice memo actions
    registerQuickAction(QuickAction(
      id: 'start_recording',
      title: 'Start Recording',
      description: 'Start recording a voice memo',
      category: 'Voice Memos',
      icon: 'mic',
      keywords: ['record', 'voice', 'memo', 'audio'],
      action: () => _eventController.add(ShortcutEvent.action('start_recording')),
    ));

    // Dhikr actions
    registerQuickAction(QuickAction(
      id: 'start_dhikr',
      title: 'Start Dhikr Counter',
      description: 'Start the dhikr counter',
      category: 'Dhikr',
      icon: 'psychology',
      keywords: ['dhikr', 'counter', 'prayer', 'remembrance'],
      action: () => _eventController.add(ShortcutEvent.action('start_dhikr')),
    ));

    // System actions
    registerQuickAction(QuickAction(
      id: 'sync_data',
      title: 'Sync Data',
      description: 'Synchronize data with cloud',
      category: 'System',
      icon: 'sync',
      keywords: ['sync', 'backup', 'cloud', 'upload'],
      action: () => _eventController.add(ShortcutEvent.action('sync_now')),
    ));

    registerQuickAction(QuickAction(
      id: 'export_data',
      title: 'Export Data',
      description: 'Export all data to file',
      category: 'System',
      icon: 'download',
      keywords: ['export', 'backup', 'download', 'save'],
      action: () => _eventController.add(ShortcutEvent.action('export_data')),
    ));

    registerQuickAction(QuickAction(
      id: 'toggle_theme',
      title: 'Toggle Theme',
      description: 'Switch between light and dark theme',
      category: 'System',
      icon: 'brightness_6',
      keywords: ['theme', 'dark', 'light', 'appearance'],
      action: () => _eventController.add(ShortcutEvent.action('toggle_theme')),
    ));
  }

  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs?.getString(_settingsKey);
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _settings = ShortcutsSettings.fromJson(settingsMap);
      }
    } catch (e) {
      debugPrint('Error loading shortcuts settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final settingsJson = json.encode(_settings.toJson());
      await _prefs?.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving shortcuts settings: $e');
    }
  }

  Future<void> _loadShortcuts() async {
    try {
      final shortcutsJson = _prefs?.getString(_shortcutsKey);
      if (shortcutsJson != null) {
        final shortcutsList = json.decode(shortcutsJson) as List<dynamic>;
        for (final shortcutJson in shortcutsList) {
          try {
            final shortcut = KeyboardShortcut.fromJson(shortcutJson);
            _shortcuts[shortcut.id] = shortcut;
          } catch (e) {
            debugPrint('Error loading shortcut: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading shortcuts: $e');
    }
  }

  Future<void> _saveShortcuts() async {
    try {
      final shortcutsJson = json.encode(
        _shortcuts.values.map((s) => s.toJson()).toList()
      );
      await _prefs?.setString(_shortcutsKey, shortcutsJson);
    } catch (e) {
      debugPrint('Error saving shortcuts: $e');
    }
  }

  Future<void> _loadQuickActions() async {
    try {
      final actionsJson = _prefs?.getString(_quickActionsKey);
      if (actionsJson != null) {
        final actionsList = json.decode(actionsJson) as List<dynamic>;
        for (final actionJson in actionsList) {
          try {
            final action = QuickAction.fromJson(actionJson);
            _quickActions[action.id] = action;
          } catch (e) {
            debugPrint('Error loading quick action: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading quick actions: $e');
    }
  }

  Future<void> _saveQuickActions() async {
    try {
      final actionsJson = json.encode(
        _quickActions.values.map((a) => a.toJson()).toList()
      );
      await _prefs?.setString(_quickActionsKey, actionsJson);
    } catch (e) {
      debugPrint('Error saving quick actions: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _eventController.close();
  }
}

// Keyboard shortcut model
class KeyboardShortcut {
  final String id;
  final String title;
  final String description;
  final String category;
  final Set<LogicalKeyboardKey> keys;
  final VoidCallback action;
  int usageCount;
  DateTime? lastUsed;

  KeyboardShortcut({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.keys,
    required this.action,
    this.usageCount = 0,
    this.lastUsed,
  });

  bool matches(Set<LogicalKeyboardKey> pressedKeys) {
    return keys.length == pressedKeys.length &&
           keys.every((key) => pressedKeys.contains(key));
  }

  void incrementUsageCount() {
    usageCount++;
    lastUsed = DateTime.now();
  }

  String get keysDisplayString {
    final keyNames = keys.map((key) => _getKeyDisplayName(key)).toList();
    keyNames.sort(); // Sort for consistent display
    return keyNames.join(' + ');
  }

  String _getKeyDisplayName(LogicalKeyboardKey key) {
    if (key == LogicalKeyboardKey.control) return 'Ctrl';
    if (key == LogicalKeyboardKey.alt) return 'Alt';
    if (key == LogicalKeyboardKey.shift) return 'Shift';
    if (key == LogicalKeyboardKey.meta) return 'Cmd';
    return key.keyLabel.toUpperCase();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'keys': keys.map((k) => k.keyId).toList(),
      'usageCount': usageCount,
      'lastUsed': lastUsed?.toIso8601String(),
    };
  }

  factory KeyboardShortcut.fromJson(Map<String, dynamic> json) {
    return KeyboardShortcut(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      category: json['category'],
      keys: (json['keys'] as List<dynamic>)
          .map((keyId) => LogicalKeyboardKey.findKeyByKeyId(keyId))
          .where((key) => key != null)
          .cast<LogicalKeyboardKey>()
          .toSet(),
      action: () {}, // Actions are registered separately
      usageCount: json['usageCount'] ?? 0,
      lastUsed: json['lastUsed'] != null ? DateTime.parse(json['lastUsed']) : null,
    );
  }
}

// Quick action model
class QuickAction {
  final String id;
  final String title;
  final String description;
  final String category;
  final String icon;
  final List<String> keywords;
  final VoidCallback action;
  bool isFavorite;
  int usageCount;
  DateTime? lastUsed;

  QuickAction({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.icon,
    required this.keywords,
    required this.action,
    this.isFavorite = false,
    this.usageCount = 0,
    this.lastUsed,
  });

  void markAsUsed() {
    usageCount++;
    lastUsed = DateTime.now();
  }

  void toggleFavorite() {
    isFavorite = !isFavorite;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'icon': icon,
      'keywords': keywords,
      'isFavorite': isFavorite,
      'usageCount': usageCount,
      'lastUsed': lastUsed?.toIso8601String(),
    };
  }

  factory QuickAction.fromJson(Map<String, dynamic> json) {
    return QuickAction(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      category: json['category'],
      icon: json['icon'],
      keywords: List<String>.from(json['keywords'] ?? []),
      action: () {}, // Actions are registered separately
      isFavorite: json['isFavorite'] ?? false,
      usageCount: json['usageCount'] ?? 0,
      lastUsed: json['lastUsed'] != null ? DateTime.parse(json['lastUsed']) : null,
    );
  }
}

// Shortcuts settings model
class ShortcutsSettings {
  final bool keyboardShortcutsEnabled;
  final bool quickActionsEnabled;
  final bool showShortcutHints;
  final bool enableGlobalShortcuts;

  const ShortcutsSettings({
    this.keyboardShortcutsEnabled = true,
    this.quickActionsEnabled = true,
    this.showShortcutHints = true,
    this.enableGlobalShortcuts = false,
  });

  ShortcutsSettings copyWith({
    bool? keyboardShortcutsEnabled,
    bool? quickActionsEnabled,
    bool? showShortcutHints,
    bool? enableGlobalShortcuts,
  }) {
    return ShortcutsSettings(
      keyboardShortcutsEnabled: keyboardShortcutsEnabled ?? this.keyboardShortcutsEnabled,
      quickActionsEnabled: quickActionsEnabled ?? this.quickActionsEnabled,
      showShortcutHints: showShortcutHints ?? this.showShortcutHints,
      enableGlobalShortcuts: enableGlobalShortcuts ?? this.enableGlobalShortcuts,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'keyboardShortcutsEnabled': keyboardShortcutsEnabled,
      'quickActionsEnabled': quickActionsEnabled,
      'showShortcutHints': showShortcutHints,
      'enableGlobalShortcuts': enableGlobalShortcuts,
    };
  }

  factory ShortcutsSettings.fromJson(Map<String, dynamic> json) {
    return ShortcutsSettings(
      keyboardShortcutsEnabled: json['keyboardShortcutsEnabled'] ?? true,
      quickActionsEnabled: json['quickActionsEnabled'] ?? true,
      showShortcutHints: json['showShortcutHints'] ?? true,
      enableGlobalShortcuts: json['enableGlobalShortcuts'] ?? false,
    );
  }
}

// Shortcut event model
class ShortcutEvent {
  final String type;
  final String? data;
  final DateTime timestamp;

  ShortcutEvent({
    required this.type,
    this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory ShortcutEvent.shortcutExecuted(String shortcutId) {
    return ShortcutEvent(type: 'shortcut_executed', data: shortcutId);
  }

  factory ShortcutEvent.shortcutFailed(String shortcutId, String error) {
    return ShortcutEvent(type: 'shortcut_failed', data: '$shortcutId:$error');
  }

  factory ShortcutEvent.quickActionExecuted(String actionId) {
    return ShortcutEvent(type: 'quick_action_executed', data: actionId);
  }

  factory ShortcutEvent.quickActionFailed(String actionId, String error) {
    return ShortcutEvent(type: 'quick_action_failed', data: '$actionId:$error');
  }

  factory ShortcutEvent.navigate(String route) {
    return ShortcutEvent(type: 'navigate', data: route);
  }

  factory ShortcutEvent.action(String action) {
    return ShortcutEvent(type: 'action', data: action);
  }
}
