enum ScreenReaderType {
  none,
  talkback,
  voiceOver,
  nvda,
  jaws,
  other,
}

enum AppNavigationMode {
  touch,
  keyboard,
  voice,
  switch_,
  eye,
}

enum ColorBlindnessType {
  none,
  protanopia,
  deuteranopia,
  tritanopia,
  achromatopsia,
}

class AccessibilitySettings {
  // Visual accessibility
  bool highContrastMode;
  bool largeTextMode;
  bool extraLargeTextMode;
  double textScaleFactor;
  bool boldTextMode;
  bool reduceMotion;
  bool reduceTransparency;
  ColorBlindnessType colorBlindnessType;
  bool invertColors;
  double brightnessAdjustment;
  double contrastAdjustment;

  // Motor accessibility
  double buttonSize;
  double touchTargetSize;
  bool stickyKeys;
  bool slowKeys;
  bool bounceKeys;
  int keyRepeatDelay;
  int keyRepeatRate;
  bool oneHandedMode;
  bool swipeGestures;
  bool longPressEnabled;
  int longPressDuration;

  // Cognitive accessibility
  bool simplifiedInterface;
  bool showTooltips;
  bool showHelpText;
  bool confirmActions;
  bool autoSave;
  int autoSaveInterval;
  bool focusIndicators;
  bool breadcrumbs;
  bool progressIndicators;

  // Auditory accessibility
  bool soundEnabled;
  bool hapticFeedback;
  bool visualAlerts;
  double soundVolume;
  bool captionsEnabled;
  bool audioDescriptions;

  // Screen reader support
  ScreenReaderType screenReaderType;
  bool screenReaderEnabled;
  bool announceChanges;
  bool announceNotifications;
  bool speakPasswords;
  double speechRate;
  double speechPitch;
  double speechVolume;

  // Navigation
  AppNavigationMode primaryNavigationMode;
  List<AppNavigationMode> enabledNavigationModes;
  bool tabNavigation;
  bool arrowKeyNavigation;
  bool skipLinks;
  bool landmarkNavigation;
  bool headingNavigation;

  // Timing
  bool extendedTimeouts;
  int timeoutDuration;
  bool pauseAnimations;
  bool autoplayMedia;
  bool infiniteScrolling;

  AccessibilitySettings({
    this.highContrastMode = false,
    this.largeTextMode = false,
    this.extraLargeTextMode = false,
    this.textScaleFactor = 1.0,
    this.boldTextMode = false,
    this.reduceMotion = false,
    this.reduceTransparency = false,
    this.colorBlindnessType = ColorBlindnessType.none,
    this.invertColors = false,
    this.brightnessAdjustment = 0.0,
    this.contrastAdjustment = 0.0,
    this.buttonSize = 1.0,
    this.touchTargetSize = 44.0,
    this.stickyKeys = false,
    this.slowKeys = false,
    this.bounceKeys = false,
    this.keyRepeatDelay = 500,
    this.keyRepeatRate = 30,
    this.oneHandedMode = false,
    this.swipeGestures = true,
    this.longPressEnabled = true,
    this.longPressDuration = 500,
    this.simplifiedInterface = false,
    this.showTooltips = true,
    this.showHelpText = false,
    this.confirmActions = false,
    this.autoSave = true,
    this.autoSaveInterval = 30,
    this.focusIndicators = true,
    this.breadcrumbs = false,
    this.progressIndicators = true,
    this.soundEnabled = true,
    this.hapticFeedback = true,
    this.visualAlerts = false,
    this.soundVolume = 0.7,
    this.captionsEnabled = false,
    this.audioDescriptions = false,
    this.screenReaderType = ScreenReaderType.none,
    this.screenReaderEnabled = false,
    this.announceChanges = true,
    this.announceNotifications = true,
    this.speakPasswords = false,
    this.speechRate = 1.0,
    this.speechPitch = 1.0,
    this.speechVolume = 0.8,
    this.primaryNavigationMode = AppNavigationMode.touch,
    List<AppNavigationMode>? enabledNavigationModes,
    this.tabNavigation = true,
    this.arrowKeyNavigation = true,
    this.skipLinks = false,
    this.landmarkNavigation = false,
    this.headingNavigation = false,
    this.extendedTimeouts = false,
    this.timeoutDuration = 30,
    this.pauseAnimations = false,
    this.autoplayMedia = true,
    this.infiniteScrolling = true,
  }) : enabledNavigationModes = enabledNavigationModes ?? [AppNavigationMode.touch];

  // Computed properties
  bool get hasVisualImpairment => 
      highContrastMode || largeTextMode || extraLargeTextMode || 
      colorBlindnessType != ColorBlindnessType.none || invertColors;

  bool get hasMotorImpairment => 
      buttonSize > 1.2 || touchTargetSize > 48 || stickyKeys || 
      slowKeys || oneHandedMode || longPressDuration > 800;

  bool get hasCognitiveImpairment => 
      simplifiedInterface || showHelpText || confirmActions || 
      extendedTimeouts || !autoplayMedia;

  bool get hasAuditoryImpairment => 
      !soundEnabled || captionsEnabled || visualAlerts;

  bool get usesScreenReader => 
      screenReaderEnabled && screenReaderType != ScreenReaderType.none;

  bool get usesKeyboardNavigation =>
      enabledNavigationModes.contains(AppNavigationMode.keyboard) ||
      tabNavigation || arrowKeyNavigation;

  bool get needsSimplifiedUI => 
      simplifiedInterface || hasCognitiveImpairment || usesScreenReader;

  // Methods
  AccessibilitySettings copyWith({
    bool? highContrastMode,
    bool? largeTextMode,
    bool? extraLargeTextMode,
    double? textScaleFactor,
    bool? boldTextMode,
    bool? reduceMotion,
    bool? reduceTransparency,
    ColorBlindnessType? colorBlindnessType,
    bool? invertColors,
    double? brightnessAdjustment,
    double? contrastAdjustment,
    double? buttonSize,
    double? touchTargetSize,
    bool? stickyKeys,
    bool? slowKeys,
    bool? bounceKeys,
    int? keyRepeatDelay,
    int? keyRepeatRate,
    bool? oneHandedMode,
    bool? swipeGestures,
    bool? longPressEnabled,
    int? longPressDuration,
    bool? simplifiedInterface,
    bool? showTooltips,
    bool? showHelpText,
    bool? confirmActions,
    bool? autoSave,
    int? autoSaveInterval,
    bool? focusIndicators,
    bool? breadcrumbs,
    bool? progressIndicators,
    bool? soundEnabled,
    bool? hapticFeedback,
    bool? visualAlerts,
    double? soundVolume,
    bool? captionsEnabled,
    bool? audioDescriptions,
    ScreenReaderType? screenReaderType,
    bool? screenReaderEnabled,
    bool? announceChanges,
    bool? announceNotifications,
    bool? speakPasswords,
    double? speechRate,
    double? speechPitch,
    double? speechVolume,
    AppNavigationMode? primaryNavigationMode,
    List<AppNavigationMode>? enabledNavigationModes,
    bool? tabNavigation,
    bool? arrowKeyNavigation,
    bool? skipLinks,
    bool? landmarkNavigation,
    bool? headingNavigation,
    bool? extendedTimeouts,
    int? timeoutDuration,
    bool? pauseAnimations,
    bool? autoplayMedia,
    bool? infiniteScrolling,
  }) {
    return AccessibilitySettings(
      highContrastMode: highContrastMode ?? this.highContrastMode,
      largeTextMode: largeTextMode ?? this.largeTextMode,
      extraLargeTextMode: extraLargeTextMode ?? this.extraLargeTextMode,
      textScaleFactor: textScaleFactor ?? this.textScaleFactor,
      boldTextMode: boldTextMode ?? this.boldTextMode,
      reduceMotion: reduceMotion ?? this.reduceMotion,
      reduceTransparency: reduceTransparency ?? this.reduceTransparency,
      colorBlindnessType: colorBlindnessType ?? this.colorBlindnessType,
      invertColors: invertColors ?? this.invertColors,
      brightnessAdjustment: brightnessAdjustment ?? this.brightnessAdjustment,
      contrastAdjustment: contrastAdjustment ?? this.contrastAdjustment,
      buttonSize: buttonSize ?? this.buttonSize,
      touchTargetSize: touchTargetSize ?? this.touchTargetSize,
      stickyKeys: stickyKeys ?? this.stickyKeys,
      slowKeys: slowKeys ?? this.slowKeys,
      bounceKeys: bounceKeys ?? this.bounceKeys,
      keyRepeatDelay: keyRepeatDelay ?? this.keyRepeatDelay,
      keyRepeatRate: keyRepeatRate ?? this.keyRepeatRate,
      oneHandedMode: oneHandedMode ?? this.oneHandedMode,
      swipeGestures: swipeGestures ?? this.swipeGestures,
      longPressEnabled: longPressEnabled ?? this.longPressEnabled,
      longPressDuration: longPressDuration ?? this.longPressDuration,
      simplifiedInterface: simplifiedInterface ?? this.simplifiedInterface,
      showTooltips: showTooltips ?? this.showTooltips,
      showHelpText: showHelpText ?? this.showHelpText,
      confirmActions: confirmActions ?? this.confirmActions,
      autoSave: autoSave ?? this.autoSave,
      autoSaveInterval: autoSaveInterval ?? this.autoSaveInterval,
      focusIndicators: focusIndicators ?? this.focusIndicators,
      breadcrumbs: breadcrumbs ?? this.breadcrumbs,
      progressIndicators: progressIndicators ?? this.progressIndicators,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      hapticFeedback: hapticFeedback ?? this.hapticFeedback,
      visualAlerts: visualAlerts ?? this.visualAlerts,
      soundVolume: soundVolume ?? this.soundVolume,
      captionsEnabled: captionsEnabled ?? this.captionsEnabled,
      audioDescriptions: audioDescriptions ?? this.audioDescriptions,
      screenReaderType: screenReaderType ?? this.screenReaderType,
      screenReaderEnabled: screenReaderEnabled ?? this.screenReaderEnabled,
      announceChanges: announceChanges ?? this.announceChanges,
      announceNotifications: announceNotifications ?? this.announceNotifications,
      speakPasswords: speakPasswords ?? this.speakPasswords,
      speechRate: speechRate ?? this.speechRate,
      speechPitch: speechPitch ?? this.speechPitch,
      speechVolume: speechVolume ?? this.speechVolume,
      primaryNavigationMode: primaryNavigationMode ?? this.primaryNavigationMode,
      enabledNavigationModes: enabledNavigationModes ?? this.enabledNavigationModes,
      tabNavigation: tabNavigation ?? this.tabNavigation,
      arrowKeyNavigation: arrowKeyNavigation ?? this.arrowKeyNavigation,
      skipLinks: skipLinks ?? this.skipLinks,
      landmarkNavigation: landmarkNavigation ?? this.landmarkNavigation,
      headingNavigation: headingNavigation ?? this.headingNavigation,
      extendedTimeouts: extendedTimeouts ?? this.extendedTimeouts,
      timeoutDuration: timeoutDuration ?? this.timeoutDuration,
      pauseAnimations: pauseAnimations ?? this.pauseAnimations,
      autoplayMedia: autoplayMedia ?? this.autoplayMedia,
      infiniteScrolling: infiniteScrolling ?? this.infiniteScrolling,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'highContrastMode': highContrastMode,
      'largeTextMode': largeTextMode,
      'extraLargeTextMode': extraLargeTextMode,
      'textScaleFactor': textScaleFactor,
      'boldTextMode': boldTextMode,
      'reduceMotion': reduceMotion,
      'reduceTransparency': reduceTransparency,
      'colorBlindnessType': colorBlindnessType.name,
      'invertColors': invertColors,
      'brightnessAdjustment': brightnessAdjustment,
      'contrastAdjustment': contrastAdjustment,
      'buttonSize': buttonSize,
      'touchTargetSize': touchTargetSize,
      'stickyKeys': stickyKeys,
      'slowKeys': slowKeys,
      'bounceKeys': bounceKeys,
      'keyRepeatDelay': keyRepeatDelay,
      'keyRepeatRate': keyRepeatRate,
      'oneHandedMode': oneHandedMode,
      'swipeGestures': swipeGestures,
      'longPressEnabled': longPressEnabled,
      'longPressDuration': longPressDuration,
      'simplifiedInterface': simplifiedInterface,
      'showTooltips': showTooltips,
      'showHelpText': showHelpText,
      'confirmActions': confirmActions,
      'autoSave': autoSave,
      'autoSaveInterval': autoSaveInterval,
      'focusIndicators': focusIndicators,
      'breadcrumbs': breadcrumbs,
      'progressIndicators': progressIndicators,
      'soundEnabled': soundEnabled,
      'hapticFeedback': hapticFeedback,
      'visualAlerts': visualAlerts,
      'soundVolume': soundVolume,
      'captionsEnabled': captionsEnabled,
      'audioDescriptions': audioDescriptions,
      'screenReaderType': screenReaderType.name,
      'screenReaderEnabled': screenReaderEnabled,
      'announceChanges': announceChanges,
      'announceNotifications': announceNotifications,
      'speakPasswords': speakPasswords,
      'speechRate': speechRate,
      'speechPitch': speechPitch,
      'speechVolume': speechVolume,
      'primaryNavigationMode': primaryNavigationMode.name,
      'enabledNavigationModes': enabledNavigationModes.map((m) => m.name).toList(),
      'tabNavigation': tabNavigation,
      'arrowKeyNavigation': arrowKeyNavigation,
      'skipLinks': skipLinks,
      'landmarkNavigation': landmarkNavigation,
      'headingNavigation': headingNavigation,
      'extendedTimeouts': extendedTimeouts,
      'timeoutDuration': timeoutDuration,
      'pauseAnimations': pauseAnimations,
      'autoplayMedia': autoplayMedia,
      'infiniteScrolling': infiniteScrolling,
    };
  }

  factory AccessibilitySettings.fromJson(Map<String, dynamic> json) {
    return AccessibilitySettings(
      highContrastMode: json['highContrastMode'] ?? false,
      largeTextMode: json['largeTextMode'] ?? false,
      extraLargeTextMode: json['extraLargeTextMode'] ?? false,
      textScaleFactor: (json['textScaleFactor'] ?? 1.0).toDouble(),
      boldTextMode: json['boldTextMode'] ?? false,
      reduceMotion: json['reduceMotion'] ?? false,
      reduceTransparency: json['reduceTransparency'] ?? false,
      colorBlindnessType: ColorBlindnessType.values.firstWhere(
        (t) => t.name == json['colorBlindnessType'],
        orElse: () => ColorBlindnessType.none,
      ),
      invertColors: json['invertColors'] ?? false,
      brightnessAdjustment: (json['brightnessAdjustment'] ?? 0.0).toDouble(),
      contrastAdjustment: (json['contrastAdjustment'] ?? 0.0).toDouble(),
      buttonSize: (json['buttonSize'] ?? 1.0).toDouble(),
      touchTargetSize: (json['touchTargetSize'] ?? 44.0).toDouble(),
      stickyKeys: json['stickyKeys'] ?? false,
      slowKeys: json['slowKeys'] ?? false,
      bounceKeys: json['bounceKeys'] ?? false,
      keyRepeatDelay: json['keyRepeatDelay'] ?? 500,
      keyRepeatRate: json['keyRepeatRate'] ?? 30,
      oneHandedMode: json['oneHandedMode'] ?? false,
      swipeGestures: json['swipeGestures'] ?? true,
      longPressEnabled: json['longPressEnabled'] ?? true,
      longPressDuration: json['longPressDuration'] ?? 500,
      simplifiedInterface: json['simplifiedInterface'] ?? false,
      showTooltips: json['showTooltips'] ?? true,
      showHelpText: json['showHelpText'] ?? false,
      confirmActions: json['confirmActions'] ?? false,
      autoSave: json['autoSave'] ?? true,
      autoSaveInterval: json['autoSaveInterval'] ?? 30,
      focusIndicators: json['focusIndicators'] ?? true,
      breadcrumbs: json['breadcrumbs'] ?? false,
      progressIndicators: json['progressIndicators'] ?? true,
      soundEnabled: json['soundEnabled'] ?? true,
      hapticFeedback: json['hapticFeedback'] ?? true,
      visualAlerts: json['visualAlerts'] ?? false,
      soundVolume: (json['soundVolume'] ?? 0.7).toDouble(),
      captionsEnabled: json['captionsEnabled'] ?? false,
      audioDescriptions: json['audioDescriptions'] ?? false,
      screenReaderType: ScreenReaderType.values.firstWhere(
        (t) => t.name == json['screenReaderType'],
        orElse: () => ScreenReaderType.none,
      ),
      screenReaderEnabled: json['screenReaderEnabled'] ?? false,
      announceChanges: json['announceChanges'] ?? true,
      announceNotifications: json['announceNotifications'] ?? true,
      speakPasswords: json['speakPasswords'] ?? false,
      speechRate: (json['speechRate'] ?? 1.0).toDouble(),
      speechPitch: (json['speechPitch'] ?? 1.0).toDouble(),
      speechVolume: (json['speechVolume'] ?? 0.8).toDouble(),
      primaryNavigationMode: AppNavigationMode.values.firstWhere(
        (m) => m.name == json['primaryNavigationMode'],
        orElse: () => AppNavigationMode.touch,
      ),
      enabledNavigationModes: (json['enabledNavigationModes'] as List?)
          ?.map((name) => AppNavigationMode.values.firstWhere(
                (m) => m.name == name,
                orElse: () => AppNavigationMode.touch,
              ))
          .toList() ?? [AppNavigationMode.touch],
      tabNavigation: json['tabNavigation'] ?? true,
      arrowKeyNavigation: json['arrowKeyNavigation'] ?? true,
      skipLinks: json['skipLinks'] ?? false,
      landmarkNavigation: json['landmarkNavigation'] ?? false,
      headingNavigation: json['headingNavigation'] ?? false,
      extendedTimeouts: json['extendedTimeouts'] ?? false,
      timeoutDuration: json['timeoutDuration'] ?? 30,
      pauseAnimations: json['pauseAnimations'] ?? false,
      autoplayMedia: json['autoplayMedia'] ?? true,
      infiniteScrolling: json['infiniteScrolling'] ?? true,
    );
  }
}
