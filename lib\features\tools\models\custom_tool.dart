import 'package:equatable/equatable.dart';

class CustomTool extends Equatable {
  final String id;
  final String name;
  final String description;
  final String category;
  final Map<String, dynamic> backendData;
  final Map<String, dynamic> uiLayout;
  final bool isActive;
  final bool isFromTemplate;
  final bool isFromExcel;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int? usageCount;

  const CustomTool({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.backendData,
    required this.uiLayout,
    required this.isActive,
    required this.isFromTemplate,
    required this.isFromExcel,
    required this.createdAt,
    required this.updatedAt,
    this.usageCount,
  });

  factory CustomTool.fromJson(Map<String, dynamic> json) {
    return CustomTool(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? 'Other',
      backendData: Map<String, dynamic>.from(json['backendData'] ?? {}),
      uiLayout: Map<String, dynamic>.from(json['uiLayout'] ?? {}),
      isActive: json['isActive'] ?? true,
      isFromTemplate: json['isFromTemplate'] ?? false,
      isFromExcel: json['isFromExcel'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      usageCount: json['usageCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'backendData': backendData,
      'uiLayout': uiLayout,
      'isActive': isActive,
      'isFromTemplate': isFromTemplate,
      'isFromExcel': isFromExcel,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'usageCount': usageCount,
    };
  }

  CustomTool copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    Map<String, dynamic>? backendData,
    Map<String, dynamic>? uiLayout,
    bool? isActive,
    bool? isFromTemplate,
    bool? isFromExcel,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? usageCount,
  }) {
    return CustomTool(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      backendData: backendData ?? this.backendData,
      uiLayout: uiLayout ?? this.uiLayout,
      isActive: isActive ?? this.isActive,
      isFromTemplate: isFromTemplate ?? this.isFromTemplate,
      isFromExcel: isFromExcel ?? this.isFromExcel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      usageCount: usageCount ?? this.usageCount,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        backendData,
        uiLayout,
        isActive,
        isFromTemplate,
        isFromExcel,
        createdAt,
        updatedAt,
        usageCount,
      ];
}

/// Tool categories enum
enum ToolCategory {
  calculator,
  converter,
  utility,
  finance,
  health,
  education,
  productivity,
  custom,
}

/// Component types enum
enum ComponentType {
  input,
  output,
  button,
  slider,
  dropdown,
  checkbox,
  text,
  chart,
  table,
}

/// Tool component model
class ToolComponent extends Equatable {
  final String id;
  final ComponentType type;
  final String label;
  final Map<String, dynamic> properties;

  const ToolComponent({
    required this.id,
    required this.type,
    required this.label,
    this.properties = const {},
  });

  factory ToolComponent.fromJson(Map<String, dynamic> json) {
    return ToolComponent(
      id: json['id'] ?? '',
      type: ComponentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ComponentType.input,
      ),
      label: json['label'] ?? '',
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'label': label,
      'properties': properties,
    };
  }

  ToolComponent copyWith({
    String? id,
    ComponentType? type,
    String? label,
    Map<String, dynamic>? properties,
  }) {
    return ToolComponent(
      id: id ?? this.id,
      type: type ?? this.type,
      label: label ?? this.label,
      properties: properties ?? this.properties,
    );
  }

  @override
  List<Object?> get props => [id, type, label, properties];
}

/// Extensions for better display names
extension ToolCategoryExtension on ToolCategory {
  String get displayName {
    switch (this) {
      case ToolCategory.calculator:
        return 'Calculator';
      case ToolCategory.converter:
        return 'Converter';
      case ToolCategory.utility:
        return 'Utility';
      case ToolCategory.finance:
        return 'Finance';
      case ToolCategory.health:
        return 'Health';
      case ToolCategory.education:
        return 'Education';
      case ToolCategory.productivity:
        return 'Productivity';
      case ToolCategory.custom:
        return 'Custom';
    }
  }

  String get icon {
    switch (this) {
      case ToolCategory.calculator:
        return '🧮';
      case ToolCategory.converter:
        return '🔄';
      case ToolCategory.utility:
        return '🛠️';
      case ToolCategory.finance:
        return '💰';
      case ToolCategory.health:
        return '🏥';
      case ToolCategory.education:
        return '📚';
      case ToolCategory.productivity:
        return '⚡';
      case ToolCategory.custom:
        return '🎨';
    }
  }
}

extension ComponentTypeExtension on ComponentType {
  String get displayName {
    switch (this) {
      case ComponentType.input:
        return 'Input Field';
      case ComponentType.output:
        return 'Output Display';
      case ComponentType.button:
        return 'Button';
      case ComponentType.slider:
        return 'Slider';
      case ComponentType.dropdown:
        return 'Dropdown';
      case ComponentType.checkbox:
        return 'Checkbox';
      case ComponentType.text:
        return 'Text';
      case ComponentType.chart:
        return 'Chart';
      case ComponentType.table:
        return 'Table';
    }
  }
}


