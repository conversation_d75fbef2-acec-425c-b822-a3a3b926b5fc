import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/custom_tool.dart';
import '../../../core/database/database_service.dart';

/// Custom tools state
class CustomToolsState {
  final List<CustomTool> tools;
  final bool isLoading;
  final String? error;

  const CustomToolsState({
    this.tools = const [],
    this.isLoading = false,
    this.error,
  });

  CustomToolsState copyWith({
    List<CustomTool>? tools,
    bool? isLoading,
    String? error,
  }) {
    return CustomToolsState(
      tools: tools ?? this.tools,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Custom tools notifier
class CustomToolsNotifier extends StateNotifier<CustomToolsState> {
  CustomToolsNotifier() : super(const CustomToolsState());

  final DatabaseService _db = DatabaseService.instance;

  /// Load all custom tools
  Future<void> loadTools() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final tools = await _db.getAllCustomTools();
      state = state.copyWith(isLoading: false, tools: tools);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load tools: $e',
      );
    }
  }

  /// Add a new tool
  Future<void> addTool(CustomTool tool) async {
    try {
      final savedTool = await _db.saveCustomTool(tool);
      final updatedTools = [...state.tools, savedTool];
      state = state.copyWith(tools: updatedTools);
    } catch (e) {
      state = state.copyWith(error: 'Failed to add tool: $e');
    }
  }

  /// Update an existing tool
  Future<void> updateTool(CustomTool tool) async {
    try {
      final savedTool = await _db.saveCustomTool(tool);
      final updatedTools = state.tools.map((t) {
        return t.id == tool.id ? savedTool : t;
      }).toList();

      state = state.copyWith(tools: updatedTools);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update tool: $e');
    }
  }

  /// Delete a tool
  Future<void> deleteTool(String toolId) async {
    try {
      await _db.deleteCustomTool(toolId);
      final updatedTools = state.tools.where((t) => t.id != toolId).toList();
      state = state.copyWith(tools: updatedTools);
    } catch (e) {
      state = state.copyWith(error: 'Failed to delete tool: $e');
    }
  }

  /// Duplicate a tool
  Future<void> duplicateTool(CustomTool tool) async {
    try {
      final duplicatedTool = CustomTool(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: '${tool.name} Copy',
        description: tool.description,
        category: tool.category,
        backendData: Map<String, dynamic>.from(tool.backendData),
        uiLayout: Map<String, dynamic>.from(tool.uiLayout),
        isActive: tool.isActive,
        isFromTemplate: tool.isFromTemplate,
        isFromExcel: tool.isFromExcel,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        usageCount: 0,
      );

      await addTool(duplicatedTool);
    } catch (e) {
      state = state.copyWith(error: 'Failed to duplicate tool: $e');
    }
  }

  /// Toggle tool active status
  Future<void> toggleToolActive(String toolId) async {
    try {
      final updatedTools = state.tools.map((tool) {
        if (tool.id == toolId) {
          return CustomTool(
            id: tool.id,
            name: tool.name,
            description: tool.description,
            category: tool.category,
            backendData: tool.backendData,
            uiLayout: tool.uiLayout,
            isActive: !tool.isActive,
            isFromTemplate: tool.isFromTemplate,
            isFromExcel: tool.isFromExcel,
            createdAt: tool.createdAt,
            updatedAt: DateTime.now(),
            usageCount: tool.usageCount,
          );
        }
        return tool;
      }).toList();

      state = state.copyWith(tools: updatedTools);
    } catch (e) {
      state = state.copyWith(error: 'Failed to toggle tool status: $e');
    }
  }

  /// Increment usage count
  Future<void> incrementUsage(String toolId) async {
    try {
      final updatedTools = state.tools.map((tool) {
        if (tool.id == toolId) {
          return CustomTool(
            id: tool.id,
            name: tool.name,
            description: tool.description,
            category: tool.category,
            backendData: tool.backendData,
            uiLayout: tool.uiLayout,
            isActive: tool.isActive,
            isFromTemplate: tool.isFromTemplate,
            isFromExcel: tool.isFromExcel,
            createdAt: tool.createdAt,
            updatedAt: DateTime.now(),
            usageCount: (tool.usageCount ?? 0) + 1,
          );
        }
        return tool;
      }).toList();

      state = state.copyWith(tools: updatedTools);
    } catch (e) {
      state = state.copyWith(error: 'Failed to increment usage: $e');
    }
  }
}

/// Custom tools provider
final customToolsProvider = StateNotifierProvider<CustomToolsNotifier, CustomToolsState>(
  (ref) => CustomToolsNotifier(),
);

/// Active tools provider
final activeToolsProvider = Provider<List<CustomTool>>((ref) {
  final toolsState = ref.watch(customToolsProvider);
  return toolsState.tools.where((tool) => tool.isActive).toList();
});

/// Tools by category provider
final toolsByCategoryProvider = Provider.family<List<CustomTool>, String>((ref, category) {
  final toolsState = ref.watch(customToolsProvider);
  if (category == 'All') {
    return toolsState.tools;
  }
  return toolsState.tools.where((tool) => tool.category == category).toList();
});

/// Tool statistics provider
final toolStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final toolsState = ref.watch(customToolsProvider);
  final tools = toolsState.tools;
  
  return {
    'total': tools.length,
    'active': tools.where((tool) => tool.isActive).length,
    'fromTemplate': tools.where((tool) => tool.isFromTemplate).length,
    'fromExcel': tools.where((tool) => tool.isFromExcel).length,
    'totalUsage': tools.fold<int>(0, (sum, tool) => sum + (tool.usageCount ?? 0)),
  };
});
