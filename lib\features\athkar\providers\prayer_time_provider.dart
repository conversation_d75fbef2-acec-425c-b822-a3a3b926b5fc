import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prayer_time.dart';
import '../services/prayer_times_api_service.dart';
import '../models/location_settings.dart';
import '../services/prayer_time_calculation_service.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Prayer times provider
final prayerTimesProvider = StateNotifierProvider<PrayerTimeNotifier, List<PrayerTime>>((ref) {
  return PrayerTimeNotifier(ref);
});

class PrayerTimeNotifier extends StateNotifier<List<PrayerTime>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  PrayerTimeNotifier(this.ref) : super([]) {
    loadPrayerTimes();
  }

  Future<void> loadPrayerTimes() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final prayerTimes = await _db.getAllPrayerTimes(userId: userProfile.id.toString());
      state = prayerTimes;
    }
  }

  Future<void> addPrayerTime(PrayerTime prayerTime) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      prayerTime.userId = userProfile.id.toString();
      final savedPrayerTime = await _db.savePrayerTime(prayerTime);
      state = [...state, savedPrayerTime];
    }
  }

  Future<void> updatePrayerTime(PrayerTime prayerTime) async {
    final savedPrayerTime = await _db.savePrayerTime(prayerTime);
    state = state.map((p) => p.id == savedPrayerTime.id ? savedPrayerTime : p).toList();
  }

  Future<void> deletePrayerTime(int prayerTimeId) async {
    await _db.deletePrayerTime(prayerTimeId);
    state = state.where((p) => p.id != prayerTimeId).toList();
  }

  Future<void> updatePrayerTimesForToday(List<PrayerTime> todayPrayerTimes) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile == null) return;

    // Remove existing prayer times for today
    final today = DateTime.now();
    final existingTodayPrayers = state.where((p) => 
      p.prayerTime.year == today.year &&
      p.prayerTime.month == today.month &&
      p.prayerTime.day == today.day
    ).toList();

    for (final prayer in existingTodayPrayers) {
      await _db.deletePrayerTime(prayer.id);
    }

    // Add new prayer times
    final newPrayerTimes = <PrayerTime>[];
    for (final prayerTime in todayPrayerTimes) {
      prayerTime.userId = userProfile.id.toString();
      final savedPrayerTime = await _db.savePrayerTime(prayerTime);
      newPrayerTimes.add(savedPrayerTime);
    }

    // Update state
    final filteredState = state.where((p) => 
      !(p.prayerTime.year == today.year &&
        p.prayerTime.month == today.month &&
        p.prayerTime.day == today.day)
    ).toList();

    state = [...filteredState, ...newPrayerTimes];
  }

  List<PrayerTime> getTodayPrayerTimes() {
    final today = DateTime.now();
    return state.where((prayer) =>
      prayer.prayerTime.year == today.year &&
      prayer.prayerTime.month == today.month &&
      prayer.prayerTime.day == today.day
    ).toList()..sort((a, b) => a.prayerTime.compareTo(b.prayerTime));
  }

  PrayerTime? getNextPrayer() {
    final now = DateTime.now();
    final todayPrayers = getTodayPrayerTimes();
    
    for (final prayer in todayPrayers) {
      if (prayer.prayerTime.isAfter(now)) {
        return prayer;
      }
    }
    
    return null; // All prayers for today have passed
  }

  PrayerTime? getCurrentPrayer() {
    final now = DateTime.now();
    final todayPrayers = getTodayPrayerTimes();
    
    PrayerTime? currentPrayer;
    for (final prayer in todayPrayers) {
      if (prayer.prayerTime.isBefore(now)) {
        currentPrayer = prayer;
      } else {
        break;
      }
    }
    
    return currentPrayer;
  }
}

// Today's prayer times provider
final todayPrayerTimesProvider = Provider<List<PrayerTime>>((ref) {
  final prayerTimes = ref.watch(prayerTimesProvider);
  final today = DateTime.now();
  
  return prayerTimes.where((prayer) =>
    prayer.prayerTime.year == today.year &&
    prayer.prayerTime.month == today.month &&
    prayer.prayerTime.day == today.day
  ).toList()..sort((a, b) => a.prayerTime.compareTo(b.prayerTime));
});

// Next prayer provider
final nextPrayerProvider = Provider<PrayerTime?>((ref) {
  final todayPrayers = ref.watch(todayPrayerTimesProvider);
  final now = DateTime.now();
  
  for (final prayer in todayPrayers) {
    if (prayer.prayerTime.isAfter(now)) {
      return prayer;
    }
  }
  
  return null;
});

// Current prayer provider
final currentPrayerProvider = Provider<PrayerTime?>((ref) {
  final todayPrayers = ref.watch(todayPrayerTimesProvider);
  final now = DateTime.now();
  
  PrayerTime? currentPrayer;
  for (final prayer in todayPrayers) {
    if (prayer.prayerTime.isBefore(now)) {
      currentPrayer = prayer;
    } else {
      break;
    }
  }
  
  return currentPrayer;
});

// Location settings provider
final locationSettingsProvider = StateNotifierProvider<LocationSettingsNotifier, LocationSettings?>((ref) {
  return LocationSettingsNotifier(ref);
});

class LocationSettingsNotifier extends StateNotifier<LocationSettings?> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  LocationSettingsNotifier(this.ref) : super(null) {
    loadLocationSettings();
  }

  Future<void> loadLocationSettings() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      // For now, create default Jordan location
      state = LocationSettings.createDefaultJordan(userProfile.id.toString());
    }
  }

  Future<void> updateLocationSettings(LocationSettings settings) async {
    state = settings;
    // In a real app, save to database
  }

  Future<void> generatePrayerTimesForToday() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile == null || state == null) return;

    final prayerTimes = PrayerTimeCalculationService.getDefaultJordanPrayerTimes(
      userProfile.id.toString(),
    );

    await ref.read(prayerTimesProvider.notifier).updatePrayerTimesForToday(prayerTimes);
  }

  Future<void> fetchRealPrayerTimes({
    required String country,
    required String city,
  }) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile == null) return;

    try {
      final apiPrayerTimes = await PrayerTimesApiService.fetchTodayPrayerTimes(
        country: country,
        city: city,
      );

      // Convert API prayer times to our format
      final prayerTimes = apiPrayerTimes.map((apiTime) {
        final prayerType = _getPrayerTypeFromName(apiTime.prayerName);
        return PrayerTime.create(
          prayerType: prayerType,
          prayerTime: apiTime.time,
          userId: userProfile.id.toString(),
          locationName: '$city, $country',
        );
      }).toList();

      await ref.read(prayerTimesProvider.notifier).updatePrayerTimesForToday(prayerTimes);
    } catch (e) {
      // Fallback to default prayer times if API fails
      await generatePrayerTimesForToday();
    }
  }

  PrayerType _getPrayerTypeFromName(String prayerName) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return PrayerType.fajr;
      case 'dhuhr':
      case 'zuhr':
        return PrayerType.dhuhr;
      case 'asr':
        return PrayerType.asr;
      case 'maghrib':
        return PrayerType.maghrib;
      case 'isha':
        return PrayerType.isha;
      default:
        return PrayerType.fajr;
    }
  }
}

// Prayer time statistics provider
final prayerTimeStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final prayerTimes = ref.watch(prayerTimesProvider);
  final todayPrayers = ref.watch(todayPrayerTimesProvider);
  final nextPrayer = ref.watch(nextPrayerProvider);

  final now = DateTime.now();
  final passedPrayers = todayPrayers.where((p) => p.prayerTime.isBefore(now)).length;

  return {
    'totalPrayerTimes': prayerTimes.length,
    'todayPrayers': todayPrayers.length,
    'passedPrayers': passedPrayers,
    'remainingPrayers': todayPrayers.length - passedPrayers,
    'nextPrayerName': nextPrayer?.prayerName,
    'nextPrayerTime': nextPrayer?.formattedTime,
    'timeUntilNext': nextPrayer?.timeUntilPrayerFormatted,
  };
});
