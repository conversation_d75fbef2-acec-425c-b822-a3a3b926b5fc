import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/budget.dart';
import '../models/transaction.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';
import 'transaction_provider.dart';

// Budget list provider
final budgetsProvider = StateNotifierProvider<BudgetNotifier, List<Budget>>((ref) {
  return BudgetNotifier(ref);
});

class BudgetNotifier extends StateNotifier<List<Budget>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  BudgetNotifier(this.ref) : super([]) {
    loadBudgets();
  }

  Future<void> loadBudgets() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final budgets = await _db.getAllBudgets(userId: userProfile.id.toString());
      state = budgets;
      _updateBudgetSpending();
    }
  }

  Future<void> addBudget(Budget budget) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      budget.userId = userProfile.id.toString();
      final savedBudget = await _db.saveBudget(budget);
      state = [...state, savedBudget];
      _updateBudgetSpending();
    }
  }

  Future<void> updateBudget(Budget budget) async {
    final savedBudget = await _db.saveBudget(budget);
    state = state.map((b) => b.id == savedBudget.id ? savedBudget : b).toList();
  }

  Future<void> deleteBudget(int budgetId) async {
    await _db.deleteBudget(budgetId);
    state = state.where((b) => b.id != budgetId).toList();
  }

  void _updateBudgetSpending() {
    final transactions = ref.read(transactionsProvider);
    
    for (final budget in state) {
      if (!budget.isCurrentPeriod) continue;
      
      final categoryExpenses = transactions
          .where((t) => 
            t.category == budget.category &&
            t.type == TransactionType.expense &&
            t.transactionDate.isAfter(budget.startDate) &&
            t.transactionDate.isBefore(budget.endDate)
          )
          .fold(0.0, (sum, t) => sum + t.amount);
      
      if (budget.spentAmount != categoryExpenses) {
        budget.spentAmount = categoryExpenses;
        updateBudget(budget);
      }
    }
  }

  List<Budget> getActiveBudgets() {
    return state.where((budget) => budget.isActive).toList();
  }

  List<Budget> getCurrentBudgets() {
    return state.where((budget) => budget.isCurrentPeriod).toList();
  }

  List<Budget> getOverBudgets() {
    return state.where((budget) => budget.status == BudgetStatus.exceeded).toList();
  }

  List<Budget> getWarningBudgets() {
    return state.where((budget) => budget.status == BudgetStatus.warning).toList();
  }

  Budget? getBudgetForCategory(String category) {
    try {
      return state.firstWhere(
        (budget) => budget.category == category && budget.isCurrentPeriod,
      );
    } catch (e) {
      return null;
    }
  }

  double getTotalBudgetAmount() {
    return getCurrentBudgets().fold(0.0, (sum, budget) => sum + budget.budgetAmount);
  }

  double getTotalSpentAmount() {
    return getCurrentBudgets().fold(0.0, (sum, budget) => sum + budget.spentAmount);
  }

  double getTotalRemainingAmount() {
    return getCurrentBudgets().fold(0.0, (sum, budget) => sum + budget.remainingAmount);
  }
}

// Active budgets provider
final activeBudgetsProvider = Provider<List<Budget>>((ref) {
  final budgets = ref.watch(budgetsProvider);
  return budgets.where((budget) => budget.isActive).toList();
});

// Current period budgets provider
final currentBudgetsProvider = Provider<List<Budget>>((ref) {
  final budgets = ref.watch(budgetsProvider);
  return budgets.where((budget) => budget.isCurrentPeriod).toList();
});

// Budget alerts provider
final budgetAlertsProvider = Provider<List<Budget>>((ref) {
  final budgets = ref.watch(budgetsProvider);
  return budgets.where((budget) => 
    budget.isCurrentPeriod && 
    (budget.status == BudgetStatus.warning || budget.status == BudgetStatus.exceeded)
  ).toList();
});

// Budget statistics provider
final budgetStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final budgets = ref.watch(budgetsProvider);
  final currentBudgets = budgets.where((b) => b.isCurrentPeriod).toList();
  final activeBudgets = budgets.where((b) => b.isActive).toList();
  
  final totalBudget = currentBudgets.fold(0.0, (sum, b) => sum + b.budgetAmount);
  final totalSpent = currentBudgets.fold(0.0, (sum, b) => sum + b.spentAmount);
  final totalRemaining = currentBudgets.fold(0.0, (sum, b) => sum + b.remainingAmount);
  
  final onTrackCount = currentBudgets.where((b) => b.status == BudgetStatus.onTrack).length;
  final warningCount = currentBudgets.where((b) => b.status == BudgetStatus.warning).length;
  final exceededCount = currentBudgets.where((b) => b.status == BudgetStatus.exceeded).length;
  
  return {
    'totalBudgets': budgets.length,
    'activeBudgets': activeBudgets.length,
    'currentBudgets': currentBudgets.length,
    'totalBudgetAmount': totalBudget,
    'totalSpentAmount': totalSpent,
    'totalRemainingAmount': totalRemaining,
    'spentPercentage': totalBudget > 0 ? (totalSpent / totalBudget) : 0.0,
    'onTrackCount': onTrackCount,
    'warningCount': warningCount,
    'exceededCount': exceededCount,
  };
});

// Budget by category provider
final budgetByCategoryProvider = Provider.family<Budget?, String>((ref, category) {
  final budgets = ref.watch(budgetsProvider);
  try {
    return budgets.firstWhere(
      (budget) => budget.category == category && budget.isCurrentPeriod,
    );
  } catch (e) {
    return null;
  }
});
