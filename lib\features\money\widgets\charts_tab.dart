import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/transaction_provider.dart';
import '../providers/account_provider.dart';
import '../models/transaction.dart';

/// Interactive charts tab for Money Flow
class ChartsTab extends ConsumerStatefulWidget {
  const ChartsTab({super.key});

  @override
  ConsumerState<ChartsTab> createState() => _ChartsTabState();
}

class _ChartsTabState extends ConsumerState<ChartsTab> {
  String _selectedChartType = 'Pie Chart';
  String _selectedDataType = 'Categories';
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _setDefaultDateRange();
  }

  void _setDefaultDateRange() {
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    final transactions = ref.watch(transactionsProvider);
    final accounts = ref.watch(accountsProvider);

    return Scaffold(
      body: Column(
        children: [
          // Chart controls
          _buildChartControls(),
          
          // Chart content
          Expanded(
            child: _buildChartContent(transactions, accounts),
          ),
        ],
      ),
    );
  }

  Widget _buildChartControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Chart type and data type selectors
          Row(
            children: [
              // Chart type dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedChartType,
                  decoration: const InputDecoration(
                    labelText: 'Chart Type',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'Pie Chart',
                    'Bar Chart',
                    'Line Chart',
                    'Donut Chart',
                    'Area Chart',
                    'Scatter Plot',
                  ].map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Icon(_getChartIcon(type), size: 20),
                          const SizedBox(width: 8),
                          Text(type),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedChartType = value!;
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Data type selector
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedDataType,
                  decoration: const InputDecoration(
                    labelText: 'Data Type',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'Categories',
                    'Accounts',
                    'Monthly Trends',
                    'Income vs Expenses',
                    'Daily Spending',
                    'Weekly Summary',
                  ].map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDataType = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Date range and refresh
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range),
                  label: Text(
                    _selectedDateRange != null
                        ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                        : 'Select Date Range',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () => setState(() {}),
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartContent(List<Transaction> transactions, List<dynamic> accounts) {
    final filteredTransactions = _filterTransactionsByDateRange(transactions);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart title
          Text(
            '$_selectedChartType - $_selectedDataType',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Main chart
          _buildMainChart(filteredTransactions, accounts),
          
          const SizedBox(height: 24),
          
          // Chart insights
          _buildChartInsights(filteredTransactions),
          
          const SizedBox(height: 24),
          
          // Data table
          _buildDataTable(filteredTransactions),
        ],
      ),
    );
  }

  Widget _buildMainChart(List<Transaction> transactions, List<dynamic> accounts) {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildChartWidget(transactions, accounts),
    );
  }

  Widget _buildChartWidget(List<Transaction> transactions, List<dynamic> accounts) {
    switch (_selectedChartType) {
      case 'Pie Chart':
        return _buildPieChart(transactions);
      case 'Bar Chart':
        return _buildBarChart(transactions);
      case 'Line Chart':
        return _buildLineChart(transactions);
      case 'Donut Chart':
        return _buildDonutChart(transactions);
      case 'Area Chart':
        return _buildAreaChart(transactions);
      case 'Scatter Plot':
        return _buildScatterPlot(transactions);
      default:
        return _buildPieChart(transactions);
    }
  }

  Widget _buildPieChart(List<Transaction> transactions) {
    final data = _getChartData(transactions);
    
    return Column(
      children: [
        // Chart area
        Expanded(
          flex: 3,
          child: Center(
            child: Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Theme.of(context).colorScheme.outline),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.pie_chart, size: 64, color: Colors.blue),
                    SizedBox(height: 8),
                    Text(
                      'Interactive Pie Chart',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '(Chart library integration)',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        
        // Legend
        Expanded(
          flex: 1,
          child: _buildChartLegend(data),
        ),
      ],
    );
  }

  Widget _buildBarChart(List<Transaction> transactions) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bar_chart, size: 64, color: Colors.green),
          SizedBox(height: 16),
          Text(
            'Interactive Bar Chart',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Chart library integration would be implemented here',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildLineChart(List<Transaction> transactions) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.show_chart, size: 64, color: Colors.orange),
          SizedBox(height: 16),
          Text(
            'Interactive Line Chart',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Time series data visualization',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildDonutChart(List<Transaction> transactions) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.donut_small, size: 64, color: Colors.purple),
          SizedBox(height: 16),
          Text(
            'Interactive Donut Chart',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Enhanced pie chart with center space',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAreaChart(List<Transaction> transactions) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.area_chart, size: 64, color: Colors.teal),
          SizedBox(height: 16),
          Text(
            'Interactive Area Chart',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Filled line chart for trend analysis',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildScatterPlot(List<Transaction> transactions) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.scatter_plot, size: 64, color: Colors.indigo),
          SizedBox(height: 16),
          Text(
            'Interactive Scatter Plot',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Correlation analysis visualization',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildChartLegend(Map<String, double> data) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    
    return Wrap(
      alignment: WrapAlignment.center,
      children: data.entries.take(8).toList().asMap().entries.map((entry) {
        final index = entry.key;
        final dataEntry = entry.value;
        final color = colors[index % colors.length];
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${dataEntry.key}: \$${dataEntry.value.toStringAsFixed(0)}',
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildChartInsights(List<Transaction> transactions) {
    final insights = _generateInsights(transactions);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Chart Insights',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            ...insights.map((insight) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(child: Text(insight)),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildDataTable(List<Transaction> transactions) {
    final data = _getChartData(transactions);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Table',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Table(
              border: TableBorder.all(color: Theme.of(context).colorScheme.outline),
              children: [
                TableRow(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  children: const [
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Category', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Amount', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8),
                      child: Text('Percentage', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
                
                ...data.entries.map((entry) {
                  final total = data.values.fold(0.0, (a, b) => a + b);
                  final percentage = (entry.value / total) * 100;
                  
                  return TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text(entry.key),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('\$${entry.value.toStringAsFixed(2)}'),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('${percentage.toStringAsFixed(1)}%'),
                      ),
                    ],
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Map<String, double> _getChartData(List<Transaction> transactions) {
    switch (_selectedDataType) {
      case 'Categories':
        return _getCategoryData(transactions);
      case 'Accounts':
        return _getAccountData(transactions);
      case 'Monthly Trends':
        return _getMonthlyData(transactions);
      case 'Income vs Expenses':
        return _getIncomeExpenseData(transactions);
      case 'Daily Spending':
        return _getDailyData(transactions);
      case 'Weekly Summary':
        return _getWeeklyData(transactions);
      default:
        return _getCategoryData(transactions);
    }
  }

  Map<String, double> _getCategoryData(List<Transaction> transactions) {
    final categoryTotals = <String, double>{};
    
    for (final transaction in transactions.where((t) => t.amount < 0)) {
      final categoryName = 'Category ${transaction.category}'; // In real app, would lookup category name
      categoryTotals[categoryName] = (categoryTotals[categoryName] ?? 0) + transaction.amount.abs();
    }
    
    return categoryTotals;
  }

  Map<String, double> _getAccountData(List<Transaction> transactions) {
    final accountTotals = <String, double>{};
    
    for (final transaction in transactions) {
      final accountName = 'Account ${transaction.accountId}'; // In real app, would lookup account name
      accountTotals[accountName] = (accountTotals[accountName] ?? 0) + transaction.amount.abs();
    }
    
    return accountTotals;
  }

  Map<String, double> _getMonthlyData(List<Transaction> transactions) {
    final monthlyTotals = <String, double>{};
    
    for (final transaction in transactions) {
      final monthKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyTotals[monthKey] = (monthlyTotals[monthKey] ?? 0) + transaction.amount.abs();
    }
    
    return monthlyTotals;
  }

  Map<String, double> _getIncomeExpenseData(List<Transaction> transactions) {
    final income = transactions.where((t) => t.amount > 0).fold(0.0, (sum, t) => sum + t.amount);
    final expenses = transactions.where((t) => t.amount < 0).fold(0.0, (sum, t) => sum + t.amount.abs());
    
    return {
      'Income': income,
      'Expenses': expenses,
    };
  }

  Map<String, double> _getDailyData(List<Transaction> transactions) {
    final dailyTotals = <String, double>{};
    
    for (final transaction in transactions.where((t) => t.amount < 0)) {
      final dayKey = '${transaction.date.day}/${transaction.date.month}';
      dailyTotals[dayKey] = (dailyTotals[dayKey] ?? 0) + transaction.amount.abs();
    }
    
    return dailyTotals;
  }

  Map<String, double> _getWeeklyData(List<Transaction> transactions) {
    final weeklyTotals = <String, double>{};
    
    for (final transaction in transactions) {
      final weekStart = transaction.date.subtract(Duration(days: transaction.date.weekday - 1));
      final weekKey = 'Week of ${weekStart.day}/${weekStart.month}';
      weeklyTotals[weekKey] = (weeklyTotals[weekKey] ?? 0) + transaction.amount.abs();
    }
    
    return weeklyTotals;
  }

  List<String> _generateInsights(List<Transaction> transactions) {
    final insights = <String>[];
    
    if (transactions.isEmpty) {
      insights.add('No transactions found for the selected period.');
      return insights;
    }
    
    final income = transactions.where((t) => t.amount > 0).fold(0.0, (sum, t) => sum + t.amount);
    final expenses = transactions.where((t) => t.amount < 0).fold(0.0, (sum, t) => sum + t.amount.abs());
    
    if (income > expenses) {
      insights.add('You had a positive cash flow of \$${(income - expenses).toStringAsFixed(2)} this period.');
    } else if (expenses > income) {
      insights.add('You spent \$${(expenses - income).toStringAsFixed(2)} more than you earned this period.');
    }
    
    if (transactions.isNotEmpty) {
      final avgTransaction = transactions.fold(0.0, (sum, t) => sum + t.amount.abs()) / transactions.length;
      insights.add('Your average transaction amount was \$${avgTransaction.toStringAsFixed(2)}.');
    }
    
    final data = _getChartData(transactions);
    if (data.isNotEmpty) {
      final topCategory = data.entries.reduce((a, b) => a.value > b.value ? a : b);
      insights.add('${topCategory.key} was your largest category with \$${topCategory.value.toStringAsFixed(2)}.');
    }
    
    return insights;
  }

  List<Transaction> _filterTransactionsByDateRange(List<Transaction> transactions) {
    if (_selectedDateRange == null) return transactions;
    
    return transactions.where((transaction) {
      return transaction.date.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
             transaction.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
    }).toList();
  }

  IconData _getChartIcon(String chartType) {
    switch (chartType) {
      case 'Pie Chart':
        return Icons.pie_chart;
      case 'Bar Chart':
        return Icons.bar_chart;
      case 'Line Chart':
        return Icons.show_chart;
      case 'Donut Chart':
        return Icons.donut_small;
      case 'Area Chart':
        return Icons.area_chart;
      case 'Scatter Plot':
        return Icons.scatter_plot;
      default:
        return Icons.pie_chart;
    }
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
