import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/transaction_provider.dart';
import '../providers/account_provider.dart';
import '../providers/category_provider.dart';
import '../models/transaction.dart';
import '../models/account.dart';

/// Comprehensive transactions management tab for Money Flow
class TransactionsManagementTab extends ConsumerStatefulWidget {
  const TransactionsManagementTab({super.key});

  @override
  ConsumerState<TransactionsManagementTab> createState() => _TransactionsManagementTabState();
}

class _TransactionsManagementTabState extends ConsumerState<TransactionsManagementTab> {
  String _searchQuery = '';
  String _selectedFilter = 'All';
  String _selectedAccount = 'All';
  String _selectedCategory = 'All';
  String _sortBy = 'Date';
  DateTimeRange? _selectedDateRange;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _setDefaultDateRange();
  }

  void _setDefaultDateRange() {
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transactions = ref.watch(transactionsProvider);
    final accounts = ref.watch(accountsProvider);
    final categories = ref.watch(categoriesProvider);
    final filteredTransactions = _filterTransactions(transactions);

    return Scaffold(
      body: Column(
        children: [
          // Transactions toolbar
          _buildTransactionsToolbar(accounts, categories),
          
          // Transactions summary
          _buildTransactionsSummary(filteredTransactions),
          
          // Transactions content
          Expanded(
            child: _buildTransactionsContent(filteredTransactions, accounts, categories),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addTransaction,
        icon: const Icon(Icons.add),
        label: const Text('Add Transaction'),
      ),
    );
  }

  Widget _buildTransactionsToolbar(List<Account> accounts, List<dynamic> categories) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Search and basic filters
          Row(
            children: [
              // Search field
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search transactions...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Type filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedFilter,
                  decoration: const InputDecoration(
                    labelText: 'Type',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'All',
                    'Income',
                    'Expense',
                    'Transfer',
                  ].map((filter) {
                    return DropdownMenuItem(
                      value: filter,
                      child: Text(filter),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Sort dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: const InputDecoration(
                    labelText: 'Sort By',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'Date',
                    'Amount',
                    'Description',
                    'Account',
                    'Category',
                  ].map((sort) {
                    return DropdownMenuItem(
                      value: sort,
                      child: Text(sort),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Advanced filters
          Row(
            children: [
              // Account filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedAccount,
                  decoration: const InputDecoration(
                    labelText: 'Account',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: 'All', child: Text('All Accounts')),
                    ...accounts.map((account) {
                      return DropdownMenuItem(
                        value: account.id.toString(),
                        child: Text(account.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedAccount = value!;
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Category filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: 'All', child: Text('All Categories')),
                    ...categories.map((category) {
                      return DropdownMenuItem(
                        value: category.id.toString(),
                        child: Text(category.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Date range
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range),
                  label: Text(
                    _selectedDateRange != null
                        ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                        : 'Select Date Range',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsSummary(List<Transaction> transactions) {
    final income = transactions
        .where((t) => t.amount > 0)
        .fold(0.0, (sum, t) => sum + t.amount);
    
    final expenses = transactions
        .where((t) => t.amount < 0)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
    
    final netIncome = income - expenses;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(child: _buildSummaryCard('Income', '\$${income.toStringAsFixed(2)}', Icons.trending_up, Colors.green)),
          const SizedBox(width: 16),
          Expanded(child: _buildSummaryCard('Expenses', '\$${expenses.toStringAsFixed(2)}', Icons.trending_down, Colors.red)),
          const SizedBox(width: 16),
          Expanded(child: _buildSummaryCard('Net Income', '\$${netIncome.toStringAsFixed(2)}', Icons.account_balance, netIncome >= 0 ? Colors.green : Colors.red)),
          const SizedBox(width: 16),
          Expanded(child: _buildSummaryCard('Transactions', transactions.length.toString(), Icons.list, Colors.blue)),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsContent(List<Transaction> transactions, List<Account> accounts, List<dynamic> categories) {
    if (transactions.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        final account = accounts.firstWhere(
          (a) => a.id.toString() == transaction.accountId,
          orElse: () => Account.create(name: 'Unknown', type: AccountType.checking, userId: ''),
        );
        final category = categories.firstWhere(
          (c) => c.id.toString() == transaction.category,
          orElse: () => null,
        );
        
        return _buildTransactionCard(transaction, account, category);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No Transactions Found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty 
              ? 'Try adjusting your search or filter criteria'
              : 'Add your first transaction to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addTransaction,
            icon: const Icon(Icons.add),
            label: const Text('Add Transaction'),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(Transaction transaction, Account account, dynamic category) {
    final isIncome = transaction.amount >= 0;
    final color = isIncome ? Colors.green : Colors.red;
    final icon = isIncome ? Icons.add_circle : Icons.remove_circle;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color),
        ),
        title: Text(
          transaction.description,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${account.name} • ${_formatDate(transaction.date)}'),
            if (category != null)
              Text(
                category.name,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 12,
                ),
              ),
            if (transaction.notes?.isNotEmpty == true)
              Text(
                transaction.notes!,
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${isIncome ? '+' : '-'}\$${transaction.amount.abs().toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 16,
              ),
            ),
            if (transaction.isRecurring)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Recurring',
                  style: TextStyle(fontSize: 10, color: Colors.blue),
                ),
              ),
          ],
        ),
        onTap: () => _viewTransactionDetails(transaction),
        onLongPress: () => _showTransactionActions(transaction),
      ),
    );
  }

  List<Transaction> _filterTransactions(List<Transaction> transactions) {
    var filtered = transactions;
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        return transaction.description.toLowerCase().contains(_searchQuery) ||
               (transaction.notes?.toLowerCase().contains(_searchQuery) ?? false) ||
               transaction.category.toLowerCase().contains(_searchQuery);
      }).toList();
    }
    
    // Apply type filter
    switch (_selectedFilter) {
      case 'Income':
        filtered = filtered.where((t) => t.amount > 0).toList();
        break;
      case 'Expense':
        filtered = filtered.where((t) => t.amount < 0).toList();
        break;
      case 'Transfer':
        filtered = filtered.where((t) => t.type == TransactionType.transfer).toList();
        break;
    }
    
    // Apply account filter
    if (_selectedAccount != 'All') {
      filtered = filtered.where((t) => t.accountId == _selectedAccount).toList();
    }
    
    // Apply category filter
    if (_selectedCategory != 'All') {
      filtered = filtered.where((t) => t.category == _selectedCategory).toList();
    }
    
    // Apply date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((transaction) {
        return transaction.date.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
               transaction.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }
    
    // Apply sorting
    switch (_sortBy) {
      case 'Date':
        filtered.sort((a, b) => b.date.compareTo(a.date));
        break;
      case 'Amount':
        filtered.sort((a, b) => b.amount.abs().compareTo(a.amount.abs()));
        break;
      case 'Description':
        filtered.sort((a, b) => a.description.compareTo(b.description));
        break;
      case 'Account':
        filtered.sort((a, b) => a.accountId.compareTo(b.accountId));
        break;
      case 'Category':
        filtered.sort((a, b) => a.category.compareTo(b.category));
        break;
    }
    
    return filtered;
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _addTransaction() {
    context.push('/money-flow/transaction/new');
  }

  void _viewTransactionDetails(Transaction transaction) {
    context.push('/money-flow/transaction/${transaction.id}');
  }

  void _showTransactionActions(Transaction transaction) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Transaction'),
              onTap: () {
                Navigator.pop(context);
                context.push('/money-flow/transaction/${transaction.id}/edit');
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Duplicate Transaction'),
              onTap: () {
                Navigator.pop(context);
                _duplicateTransaction(transaction);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Transaction'),
              onTap: () {
                Navigator.pop(context);
                _shareTransaction(transaction);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Transaction', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _deleteTransaction(transaction);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _duplicateTransaction(Transaction transaction) {
    // TODO: Implement transaction duplication
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Transaction duplicated'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareTransaction(Transaction transaction) {
    // TODO: Implement transaction sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Transaction shared'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _deleteTransaction(Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text('Are you sure you want to delete "${transaction.description}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(transactionsProvider.notifier).deleteTransaction(transaction.id);
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Transaction deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
