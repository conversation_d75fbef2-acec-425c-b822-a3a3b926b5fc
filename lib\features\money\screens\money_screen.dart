import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/base_screen.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/financial_goal.dart';
import '../providers/account_provider.dart';
import '../providers/transaction_provider.dart';
import '../providers/budget_provider.dart';
import '../providers/financial_goal_provider.dart';
import '../widgets/financial_charts.dart';
import '../widgets/accounts_management_tab.dart';
import '../widgets/transactions_management_tab.dart';
import '../widgets/categories_management_tab.dart';
import '../widgets/reports_tab.dart';
import '../widgets/charts_tab.dart';
import '../widgets/budget_tracking_tab.dart';
import 'transaction_form_screen.dart';
import 'account_form_screen.dart';
import 'financial_goal_form_screen.dart';

class MoneyScreen extends ConsumerStatefulWidget {
  const MoneyScreen({super.key});

  @override
  ConsumerState<MoneyScreen> createState() => _MoneyScreenState();
}

class _MoneyScreenState extends ConsumerState<MoneyScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MoneyBaseScreen(
      title: 'Money Flow',
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
          Tab(icon: Icon(Icons.account_balance_wallet), text: 'Accounts'),
          Tab(icon: Icon(Icons.receipt_long), text: 'Transactions'),
          Tab(icon: Icon(Icons.category), text: 'Categories'),
          Tab(icon: Icon(Icons.bar_chart), text: 'Charts'),
          Tab(icon: Icon(Icons.assessment), text: 'Reports'),
          Tab(icon: Icon(Icons.account_balance_wallet), text: 'Budget'),
          Tab(icon: Icon(Icons.settings), text: 'Settings'),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          const AccountsManagementTab(),
          const TransactionsManagementTab(),
          const CategoriesManagementTab(),
          const ChartsTab(),
          const ReportsTab(),
          const BudgetTrackingTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return Consumer(
      builder: (context, ref, child) {
        final accountStats = ref.watch(accountStatsProvider);
        final transactionStats = ref.watch(transactionStatsProvider);
        final budgetStats = ref.watch(budgetStatsProvider);
        final goalStats = ref.watch(goalStatsProvider);
        final recentTransactions = ref.watch(recentTransactionsProvider);
        final budgetAlerts = ref.watch(budgetAlertsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Financial Summary Cards
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: _getCrossAxisCount(context),
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1.5,
                children: [
                  _buildSummaryCard(
                    'Net Worth',
                    '\$${accountStats['netWorth'].toStringAsFixed(2)}',
                    Icons.trending_up,
                    Colors.green,
                    accountStats['netWorth'] >= 0 ? Colors.green : Colors.red,
                  ),
                  _buildSummaryCard(
                    'Monthly Income',
                    '\$${transactionStats['monthlyIncome'].toStringAsFixed(2)}',
                    Icons.arrow_upward,
                    Colors.blue,
                    Colors.blue,
                  ),
                  _buildSummaryCard(
                    'Monthly Expenses',
                    '\$${transactionStats['monthlyExpenses'].toStringAsFixed(2)}',
                    Icons.arrow_downward,
                    Colors.orange,
                    Colors.orange,
                  ),
                  _buildSummaryCard(
                    'Budget Used',
                    '${(budgetStats['spentPercentage'] * 100).toStringAsFixed(1)}%',
                    Icons.pie_chart,
                    Colors.purple,
                    budgetStats['spentPercentage'] > 0.8 ? Colors.red : Colors.purple,
                  ),
                ],
              ),
              
              const SizedBox(height: 16),

              // Budget Alerts
              if (budgetAlerts.isNotEmpty) ...[
                Card(
                  color: Colors.orange.withValues(alpha: 0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.warning, color: Colors.orange),
                            const SizedBox(width: 8),
                            Text(
                              'Budget Alerts',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ...budgetAlerts.take(3).map((budget) => ListTile(
                          dense: true,
                          leading: Icon(
                            budget.status == BudgetStatus.exceeded 
                                ? Icons.error 
                                : Icons.warning,
                            color: budget.status == BudgetStatus.exceeded 
                                ? Colors.red 
                                : Colors.orange,
                          ),
                          title: Text(budget.name),
                          subtitle: Text(
                            '${(budget.spentPercentage * 100).toStringAsFixed(1)}% used (${budget.formattedSpentAmount} of ${budget.formattedBudgetAmount})',
                          ),
                          trailing: Text(
                            budget.statusDisplayName,
                            style: TextStyle(
                              color: budget.status == BudgetStatus.exceeded 
                                  ? Colors.red 
                                  : Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Financial Charts
              Text(
                'Financial Overview',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const FinancialCharts(),
              const SizedBox(height: 24),

              // Recent Transactions
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Recent Transactions',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const Spacer(),
                          TextButton(
                            onPressed: () => _tabController.animateTo(2),
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (recentTransactions.isEmpty) ...[
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(32),
                            child: Text('No transactions yet'),
                          ),
                        ),
                      ] else ...[
                        ...recentTransactions.take(5).map((transaction) => ListTile(
                          leading: CircleAvatar(
                            backgroundColor: _getTransactionColor(transaction.type),
                            child: Icon(
                              _getTransactionIcon(transaction.type),
                              color: Colors.white,
                            ),
                          ),
                          title: Text(transaction.description),
                          subtitle: Text('${transaction.category} • ${transaction.formattedDate}'),
                          trailing: Text(
                            transaction.formattedAmount,
                            style: TextStyle(
                              color: _getTransactionColor(transaction.type),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),

              // Quick Stats
              Row(
                children: [
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Text(
                              'Active Goals',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              goalStats['activeGoals'].toString(),
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${(goalStats['averageProgress'] * 100).toStringAsFixed(1)}% avg progress',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Text(
                              'Accounts',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              accountStats['activeAccounts'].toString(),
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '\$${accountStats['totalBalance'].toStringAsFixed(2)} total',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final accounts = ref.watch(accountsProvider);
        final accountStats = ref.watch(accountStatsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Account Summary
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem('Total', '\$${accountStats['totalBalance'].toStringAsFixed(2)}', Icons.account_balance),
                      _buildStatItem('Assets', '\$${accountStats['assets'].toStringAsFixed(2)}', Icons.trending_up),
                      _buildStatItem('Debts', '\$${accountStats['liabilities'].toStringAsFixed(2)}', Icons.trending_down),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Quick Actions
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _addAccount,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Account'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: _addTransaction,
                    icon: const Icon(Icons.receipt_long),
                    label: const Text('Add Transaction'),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Accounts List
              Text(
                'Your Accounts',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              
              if (accounts.isEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.account_balance_wallet_outlined,
                          size: 48,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No accounts yet',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Add your first account to start tracking your finances',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                ...accounts.map((account) => Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getAccountTypeColor(account.type),
                      child: Icon(
                        _getAccountTypeIcon(account.type),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(account.name),
                    subtitle: Text('${account.typeDisplayName} • ${account.bankName ?? 'No bank'}'),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          account.formattedBalance,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: account.balance >= 0 ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (!account.isActive)
                          Text(
                            'Inactive',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                      ],
                    ),
                    onTap: () => _editAccount(account),
                  ),
                )),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildTransactionsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final transactions = ref.watch(transactionsProvider);
        final transactionStats = ref.watch(transactionStatsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction Summary
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem('Income', '\$${transactionStats['monthlyIncome'].toStringAsFixed(2)}', Icons.arrow_upward),
                      _buildStatItem('Expenses', '\$${transactionStats['monthlyExpenses'].toStringAsFixed(2)}', Icons.arrow_downward),
                      _buildStatItem('Net', '\$${transactionStats['monthlyNet'].toStringAsFixed(2)}', Icons.account_balance),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Quick Actions
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _addTransaction,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Transaction'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: _showTransactionFilters,
                    icon: const Icon(Icons.filter_list),
                    label: const Text('Filter'),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Transactions List
              Text(
                'Recent Transactions',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              
              if (transactions.isEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.receipt_long_outlined,
                          size: 48,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No transactions yet',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Add your first transaction to start tracking your spending',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                ...transactions.take(20).map((transaction) => Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getTransactionColor(transaction.type),
                      child: Icon(
                        _getTransactionIcon(transaction.type),
                        color: Colors.white,
                      ),
                    ),
                    title: Text(transaction.description),
                    subtitle: Text('${transaction.category} • ${transaction.formattedDateTime}'),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          transaction.formattedAmount,
                          style: TextStyle(
                            color: _getTransactionColor(transaction.type),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (transaction.tags.isNotEmpty)
                          Text(
                            transaction.tags.first,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                      ],
                    ),
                    onTap: () => _editTransaction(transaction),
                  ),
                )),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildGoalsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final goals = ref.watch(financialGoalsProvider);
        final goalStats = ref.watch(goalStatsProvider);
        final activeGoals = ref.watch(activeGoalsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Goals Summary
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem('Active', goalStats['activeGoals'].toString(), Icons.flag),
                      _buildStatItem('Progress', '${(goalStats['averageProgress'] * 100).toStringAsFixed(1)}%', Icons.trending_up),
                      _buildStatItem('Saved', '\$${goalStats['totalCurrentAmount'].toStringAsFixed(0)}', Icons.savings),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Quick Actions
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _addGoal,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Goal'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: _showGoalTypes,
                    icon: const Icon(Icons.category),
                    label: const Text('Categories'),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Goals List
              Text(
                'Your Goals',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              
              if (goals.isEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.flag_outlined,
                          size: 48,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No financial goals yet',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Set your first financial goal to start saving',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                ...activeGoals.map((goal) => Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                goal.name,
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                            ),
                            Text(
                              '${(goal.progressPercentage * 100).toStringAsFixed(1)}%',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: goal.progressPercentage,
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${goal.formattedCurrentAmount} of ${goal.formattedTargetAmount}',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            Text(
                              '${goal.daysRemaining} days left',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: goal.isOverdue ? Colors.red : null,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Categories Header
          Row(
            children: [
              Text(
                'Transaction Categories',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _addCategory,
                icon: const Icon(Icons.add),
                label: const Text('Add Category'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Income Categories
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Income Categories',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildCategoryChip('Salary', Colors.green, Icons.work),
                      _buildCategoryChip('Freelance', Colors.green, Icons.laptop),
                      _buildCategoryChip('Investment', Colors.green, Icons.trending_up),
                      _buildCategoryChip('Business', Colors.green, Icons.business),
                      _buildCategoryChip('Other Income', Colors.green, Icons.attach_money),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Expense Categories
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Expense Categories',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildCategoryChip('Food & Dining', Colors.red, Icons.restaurant),
                      _buildCategoryChip('Transportation', Colors.red, Icons.directions_car),
                      _buildCategoryChip('Shopping', Colors.red, Icons.shopping_bag),
                      _buildCategoryChip('Entertainment', Colors.red, Icons.movie),
                      _buildCategoryChip('Bills & Utilities', Colors.red, Icons.receipt),
                      _buildCategoryChip('Healthcare', Colors.red, Icons.local_hospital),
                      _buildCategoryChip('Education', Colors.red, Icons.school),
                      _buildCategoryChip('Travel', Colors.red, Icons.flight),
                      _buildCategoryChip('Personal Care', Colors.red, Icons.spa),
                      _buildCategoryChip('Other Expenses', Colors.red, Icons.more_horiz),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Financial Charts & Analytics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Chart Type Selector
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chart Types',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      FilterChip(
                        label: const Text('Spending by Category'),
                        selected: true,
                        onSelected: (selected) {},
                      ),
                      FilterChip(
                        label: const Text('Income vs Expenses'),
                        selected: false,
                        onSelected: (selected) {},
                      ),
                      FilterChip(
                        label: const Text('Monthly Trends'),
                        selected: false,
                        onSelected: (selected) {},
                      ),
                      FilterChip(
                        label: const Text('Account Balances'),
                        selected: false,
                        onSelected: (selected) {},
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Charts Display
          const FinancialCharts(),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Financial Reports',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Report Types
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: _getCrossAxisCount(context),
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1.2,
            children: [
              _buildReportCard(
                'Monthly Summary',
                'Income, expenses, and net worth for this month',
                Icons.calendar_month,
                Colors.blue,
                () => _generateMonthlyReport(),
              ),
              _buildReportCard(
                'Category Analysis',
                'Spending breakdown by category',
                Icons.pie_chart,
                Colors.green,
                () => _generateCategoryReport(),
              ),
              _buildReportCard(
                'Cash Flow',
                'Money in vs money out over time',
                Icons.trending_up,
                Colors.orange,
                () => _generateCashFlowReport(),
              ),
              _buildReportCard(
                'Net Worth',
                'Assets, liabilities, and net worth trends',
                Icons.account_balance,
                Colors.purple,
                () => _generateNetWorthReport(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'MoneyFlow Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Currency Settings
          Card(
            child: ListTile(
              leading: const Icon(Icons.attach_money),
              title: const Text('Currency'),
              subtitle: const Text('USD - US Dollar'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _showCurrencySettings,
            ),
          ),
          const SizedBox(height: 8),

          // Budget Settings
          Card(
            child: ListTile(
              leading: const Icon(Icons.account_balance_wallet),
              title: const Text('Budget Settings'),
              subtitle: const Text('Configure budget alerts and limits'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _showBudgetSettings,
            ),
          ),
          const SizedBox(height: 8),

          // Data Export
          Card(
            child: ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Export Data'),
              subtitle: const Text('Export transactions and reports'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _exportData,
            ),
          ),
          const SizedBox(height: 8),

          // Data Import
          Card(
            child: ListTile(
              leading: const Icon(Icons.upload),
              title: const Text('Import Data'),
              subtitle: const Text('Import from CSV or other formats'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _importData,
            ),
          ),
          const SizedBox(height: 8),

          // Backup & Sync
          Card(
            child: ListTile(
              leading: const Icon(Icons.cloud_sync),
              title: const Text('Backup & Sync'),
              subtitle: const Text('Cloud backup and synchronization'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _showBackupSettings,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color iconColor, Color valueColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: iconColor, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: valueColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 0: // Overview
        return FloatingActionButton(
          onPressed: () => _showQuickAddDialog(),
          tooltip: 'Quick Add',
          child: const Icon(Icons.add),
        );
      case 1: // Accounts
        return FloatingActionButton(
          onPressed: _addAccount,
          tooltip: 'Add Account',
          child: const Icon(Icons.account_balance),
        );
      case 2: // Transactions
        return FloatingActionButton(
          onPressed: _addTransaction,
          tooltip: 'Add Transaction',
          child: const Icon(Icons.add),
        );
      case 3: // Categories
        return FloatingActionButton(
          onPressed: _addCategory,
          tooltip: 'Add Category',
          child: const Icon(Icons.category),
        );
      case 4: // Charts
        return FloatingActionButton(
          onPressed: _addTransaction,
          tooltip: 'Add Transaction',
          child: const Icon(Icons.add),
        );
      case 5: // Reports
        return FloatingActionButton(
          onPressed: _generateMonthlyReport,
          tooltip: 'Generate Report',
          child: const Icon(Icons.assessment),
        );
      case 6: // Goals
        return FloatingActionButton(
          onPressed: _addGoal,
          tooltip: 'Add Goal',
          child: const Icon(Icons.flag),
        );
      case 7: // Settings
        return FloatingActionButton(
          onPressed: _exportData,
          tooltip: 'Export Data',
          child: const Icon(Icons.download),
        );
      default:
        return FloatingActionButton(
          onPressed: _addTransaction,
          child: const Icon(Icons.add),
        );
    }
  }

  void _showQuickAddDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quick Add',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Add Transaction'),
              onTap: () {
                Navigator.of(context).pop();
                _addTransaction();
              },
            ),

            ListTile(
              leading: const Icon(Icons.account_balance),
              title: const Text('Add Account'),
              onTap: () {
                Navigator.of(context).pop();
                _addAccount();
              },
            ),

            ListTile(
              leading: const Icon(Icons.flag),
              title: const Text('Add Goal'),
              onTap: () {
                Navigator.of(context).pop();
                _addGoal();
              },
            ),
          ],
        ),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 2;
    return 2;
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Icons.arrow_upward;
      case TransactionType.expense:
        return Icons.arrow_downward;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  Color _getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Colors.blue;
      case AccountType.savings:
        return Colors.green;
      case AccountType.credit:
        return Colors.orange;
      case AccountType.investment:
        return Colors.purple;
      case AccountType.cash:
        return Colors.brown;
      case AccountType.loan:
        return Colors.red;
      case AccountType.other:
        return Colors.grey;
    }
  }

  IconData _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.credit:
        return Icons.credit_card;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.cash:
        return Icons.money;
      case AccountType.loan:
        return Icons.money_off;
      case AccountType.other:
        return Icons.account_balance_wallet;
    }
  }

  void _addAccount() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AccountFormScreen(),
      ),
    );
  }

  void _editAccount(account) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AccountFormScreen(account: account),
      ),
    );
  }

  void _addTransaction() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TransactionFormScreen(),
      ),
    );
  }

  void _editTransaction(transaction) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TransactionFormScreen(transaction: transaction),
      ),
    );
  }

  void _addGoal() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const FinancialGoalFormScreen(),
      ),
    );
  }

  void _showTransactionFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'Filter Transactions',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Transaction Type Filter
                      Text(
                        'Transaction Type',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: TransactionType.values.map((type) {
                          return FilterChip(
                            label: Text(type.name),
                            selected: false, // TODO: Implement filter state
                            onSelected: (selected) {
                              // TODO: Implement filter logic
                            },
                          );
                        }).toList(),
                      ),

                      const SizedBox(height: 16),

                      // Date Range Filter
                      Text(
                        'Date Range',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                // TODO: Implement date picker
                              },
                              child: const Text('Start Date'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                // TODO: Implement date picker
                              },
                              child: const Text('End Date'),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Amount Range Filter
                      Text(
                        'Amount Range',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'Min Amount',
                                prefixText: '\$',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'Max Amount',
                                prefixText: '\$',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Search Field
                      TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'Search Description',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.search),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Clear Filters'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Apply Filters'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showGoalTypes() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goal Categories',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            ...GoalType.values.map((type) => ListTile(
              leading: CircleAvatar(
                child: Icon(_getGoalTypeIcon(type)),
              ),
              title: Text(_getGoalTypeDisplayName(type)),
              subtitle: Text(_getGoalTypeDescription(type)),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => FinancialGoalFormScreen(
                      goal: FinancialGoal.create(
                        name: _getGoalTypeDisplayName(type),
                        type: type,
                        targetAmount: 1000,
                        targetDate: DateTime.now().add(const Duration(days: 365)),
                        userId: '',
                      ),
                    ),
                  ),
                );
              },
            )),
          ],
        ),
      ),
    );
  }

  IconData _getGoalTypeIcon(GoalType type) {
    switch (type) {
      case GoalType.savings:
        return Icons.savings;
      case GoalType.debt:
        return Icons.money_off;
      case GoalType.investment:
        return Icons.trending_up;
      case GoalType.purchase:
        return Icons.shopping_cart;
      case GoalType.emergency:
        return Icons.security;
    }
  }

  String _getGoalTypeDisplayName(GoalType type) {
    switch (type) {
      case GoalType.savings:
        return 'Savings Goal';
      case GoalType.debt:
        return 'Debt Payoff';
      case GoalType.investment:
        return 'Investment Goal';
      case GoalType.purchase:
        return 'Purchase Goal';
      case GoalType.emergency:
        return 'Emergency Fund';
    }
  }

  String _getGoalTypeDescription(GoalType type) {
    switch (type) {
      case GoalType.savings:
        return 'Save money for future needs';
      case GoalType.debt:
        return 'Pay off existing debts';
      case GoalType.investment:
        return 'Build investment portfolio';
      case GoalType.purchase:
        return 'Save for a specific purchase';
      case GoalType.emergency:
        return 'Build emergency fund';
    }
  }

  // Helper methods for new tabs

  Widget _buildCategoryChip(String label, Color color, IconData icon) {
    return Chip(
      avatar: Icon(icon, size: 16, color: Colors.white),
      label: Text(
        label,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildReportCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Action methods for new tabs

  void _addCategory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Category Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Category Type',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'income', child: Text('Income')),
                DropdownMenuItem(value: 'expense', child: Text('Expense')),
              ],
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _generateMonthlyReport() {
    // TODO: Implement monthly report generation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Generating monthly report...')),
    );
  }

  void _generateCategoryReport() {
    // TODO: Implement category report generation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Generating category report...')),
    );
  }

  void _generateCashFlowReport() {
    // TODO: Implement cash flow report generation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Generating cash flow report...')),
    );
  }

  void _generateNetWorthReport() {
    // TODO: Implement net worth report generation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Generating net worth report...')),
    );
  }

  void _showCurrencySettings() {
    // TODO: Implement currency settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Currency settings coming soon...')),
    );
  }

  void _showBudgetSettings() {
    // TODO: Implement budget settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Budget settings coming soon...')),
    );
  }

  void _exportData() {
    // TODO: Implement data export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Data export coming soon...')),
    );
  }

  void _importData() {
    // TODO: Implement data import
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Data import coming soon...')),
    );
  }

  void _showBackupSettings() {
    // TODO: Implement backup settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Backup settings coming soon...')),
    );
  }
}
