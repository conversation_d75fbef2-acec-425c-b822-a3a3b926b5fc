import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../models/calculation_history.dart';
import '../providers/calculation_history_provider.dart';

class MortgageCalculatorScreen extends ConsumerStatefulWidget {
  const MortgageCalculatorScreen({super.key});

  @override
  ConsumerState<MortgageCalculatorScreen> createState() => _MortgageCalculatorScreenState();
}

class _MortgageCalculatorScreenState extends ConsumerState<MortgageCalculatorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _loanAmountController = TextEditingController();
  final _downPaymentController = TextEditingController();
  final _interestRateController = TextEditingController();
  final _loanTermController = TextEditingController();
  final _propertyTaxController = TextEditingController();
  final _homeInsuranceController = TextEditingController();
  final _pmiController = TextEditingController();
  final _hoaController = TextEditingController();

  double _monthlyPayment = 0;
  double _totalInterest = 0;
  double _totalPayment = 0;
  double _principalAndInterest = 0;
  double _monthlyTaxes = 0;
  double _monthlyInsurance = 0;
  double _monthlyPMI = 0;
  double _monthlyHOA = 0;
  
  List<Map<String, dynamic>> _amortizationSchedule = [];

  @override
  void initState() {
    super.initState();
    // Set default values
    _loanAmountController.text = '300000';
    _downPaymentController.text = '60000';
    _interestRateController.text = '6.5';
    _loanTermController.text = '30';
    _propertyTaxController.text = '3600';
    _homeInsuranceController.text = '1200';
    _pmiController.text = '150';
    _hoaController.text = '0';
  }

  @override
  void dispose() {
    _loanAmountController.dispose();
    _downPaymentController.dispose();
    _interestRateController.dispose();
    _loanTermController.dispose();
    _propertyTaxController.dispose();
    _homeInsuranceController.dispose();
    _pmiController.dispose();
    _hoaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mortgage Calculator'),
        actions: [
          IconButton(
            onPressed: _showAmortizationSchedule,
            icon: const Icon(Icons.table_chart),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Loan Details
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Loan Details',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      _buildCurrencyField(
                        controller: _loanAmountController,
                        label: 'Home Price',
                        hint: 'Enter the total home price',
                      ),
                      const SizedBox(height: 16),
                      
                      _buildCurrencyField(
                        controller: _downPaymentController,
                        label: 'Down Payment',
                        hint: 'Enter your down payment amount',
                      ),
                      const SizedBox(height: 16),
                      
                      _buildPercentageField(
                        controller: _interestRateController,
                        label: 'Interest Rate (Annual)',
                        hint: 'Enter annual interest rate',
                      ),
                      const SizedBox(height: 16),
                      
                      _buildNumberField(
                        controller: _loanTermController,
                        label: 'Loan Term (Years)',
                        hint: 'Enter loan term in years',
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Additional Costs
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Additional Monthly Costs',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      _buildCurrencyField(
                        controller: _propertyTaxController,
                        label: 'Property Tax (Annual)',
                        hint: 'Enter annual property tax',
                      ),
                      const SizedBox(height: 16),
                      
                      _buildCurrencyField(
                        controller: _homeInsuranceController,
                        label: 'Home Insurance (Annual)',
                        hint: 'Enter annual home insurance',
                      ),
                      const SizedBox(height: 16),
                      
                      _buildCurrencyField(
                        controller: _pmiController,
                        label: 'PMI (Monthly)',
                        hint: 'Enter monthly PMI if applicable',
                      ),
                      const SizedBox(height: 16),
                      
                      _buildCurrencyField(
                        controller: _hoaController,
                        label: 'HOA Fees (Monthly)',
                        hint: 'Enter monthly HOA fees if applicable',
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Calculate Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _calculateMortgage,
                  child: const Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Calculate Mortgage', style: TextStyle(fontSize: 18)),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Results
              if (_monthlyPayment > 0) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Monthly Payment Breakdown',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),
                        
                        _buildResultRow('Principal & Interest', _principalAndInterest),
                        _buildResultRow('Property Tax', _monthlyTaxes),
                        _buildResultRow('Home Insurance', _monthlyInsurance),
                        if (_monthlyPMI > 0) _buildResultRow('PMI', _monthlyPMI),
                        if (_monthlyHOA > 0) _buildResultRow('HOA Fees', _monthlyHOA),
                        
                        const Divider(),
                        
                        _buildResultRow(
                          'Total Monthly Payment',
                          _monthlyPayment,
                          isTotal: true,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Loan Summary',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),
                        
                        _buildResultRow('Total Interest Paid', _totalInterest),
                        _buildResultRow('Total Amount Paid', _totalPayment),
                        
                        const SizedBox(height: 16),
                        
                        // Loan-to-Value Ratio
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Loan-to-Value Ratio:'),
                            Text(
                              '${_calculateLTV().toStringAsFixed(1)}%',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrencyField({
    required TextEditingController controller,
    required String label,
    required String hint,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: '\$',
        border: const OutlineInputBorder(),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a value';
        }
        if (double.tryParse(value) == null) {
          return 'Please enter a valid number';
        }
        return null;
      },
    );
  }

  Widget _buildPercentageField({
    required TextEditingController controller,
    required String label,
    required String hint,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        suffixText: '%',
        border: const OutlineInputBorder(),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,3}')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a value';
        }
        final rate = double.tryParse(value);
        if (rate == null || rate < 0 || rate > 50) {
          return 'Please enter a valid interest rate (0-50%)';
        }
        return null;
      },
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required String hint,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a value';
        }
        final years = int.tryParse(value);
        if (years == null || years < 1 || years > 50) {
          return 'Please enter a valid loan term (1-50 years)';
        }
        return null;
      },
    );
  }

  Widget _buildResultRow(String label, double value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            '\$${value.toStringAsFixed(2)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  void _calculateMortgage() {
    if (!_formKey.currentState!.validate()) return;

    final homePrice = double.parse(_loanAmountController.text);
    final downPayment = double.parse(_downPaymentController.text);
    final annualRate = double.parse(_interestRateController.text) / 100;
    final loanTermYears = int.parse(_loanTermController.text);
    final annualPropertyTax = double.parse(_propertyTaxController.text);
    final annualInsurance = double.parse(_homeInsuranceController.text);
    final monthlyPMI = double.parse(_pmiController.text);
    final monthlyHOA = double.parse(_hoaController.text);

    // Calculate loan amount
    final loanAmount = homePrice - downPayment;
    
    // Calculate monthly payment (Principal & Interest)
    final monthlyRate = annualRate / 12;
    final numberOfPayments = loanTermYears * 12;
    
    double monthlyPI;
    if (monthlyRate == 0) {
      monthlyPI = loanAmount / numberOfPayments;
    } else {
      monthlyPI = loanAmount * 
          (monthlyRate * math.pow(1 + monthlyRate, numberOfPayments)) /
          (math.pow(1 + monthlyRate, numberOfPayments) - 1);
    }

    // Calculate other monthly costs
    final monthlyTax = annualPropertyTax / 12;
    final monthlyIns = annualInsurance / 12;

    // Calculate totals
    final totalPI = monthlyPI * numberOfPayments;
    final totalInt = totalPI - loanAmount;
    final totalMonthly = monthlyPI + monthlyTax + monthlyIns + monthlyPMI + monthlyHOA;

    setState(() {
      _principalAndInterest = monthlyPI;
      _monthlyTaxes = monthlyTax;
      _monthlyInsurance = monthlyIns;
      _monthlyPMI = monthlyPMI;
      _monthlyHOA = monthlyHOA;
      _monthlyPayment = totalMonthly;
      _totalInterest = totalInt;
      _totalPayment = totalPI;
    });

    // Generate amortization schedule
    _generateAmortizationSchedule(loanAmount, monthlyRate, numberOfPayments.toInt());

    // Save calculation
    _saveCalculation();
  }

  void _generateAmortizationSchedule(double loanAmount, double monthlyRate, int numberOfPayments) {
    _amortizationSchedule.clear();
    double remainingBalance = loanAmount;

    for (int i = 1; i <= numberOfPayments; i++) {
      final interestPayment = remainingBalance * monthlyRate;
      final principalPayment = _principalAndInterest - interestPayment;
      remainingBalance -= principalPayment;

      _amortizationSchedule.add({
        'payment': i,
        'principal': principalPayment,
        'interest': interestPayment,
        'balance': remainingBalance,
      });
    }
  }

  double _calculateLTV() {
    final homePrice = double.tryParse(_loanAmountController.text) ?? 0;
    final downPayment = double.tryParse(_downPaymentController.text) ?? 0;
    if (homePrice == 0) return 0;
    return ((homePrice - downPayment) / homePrice) * 100;
  }

  void _saveCalculation() {
    final expression = 'Mortgage: \$${_loanAmountController.text} home, '
        '\$${_downPaymentController.text} down, '
        '${_interestRateController.text}% rate, '
        '${_loanTermController.text} years';
    
    final calculation = CalculationHistory.create(
      type: CalculationType.mortgage,
      expression: expression,
      result: 'Monthly Payment: \$${_monthlyPayment.toStringAsFixed(2)}',
      userId: '',
    );
    
    ref.read(calculationHistoryProvider.notifier).addCalculation(calculation);
  }

  void _showAmortizationSchedule() {
    if (_amortizationSchedule.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please calculate mortgage first')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'Amortization Schedule',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: _amortizationSchedule.length,
                  itemBuilder: (context, index) {
                    final payment = _amortizationSchedule[index];
                    return Card(
                      child: ListTile(
                        title: Text('Payment ${payment['payment']}'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Principal: \$${payment['principal'].toStringAsFixed(2)}'),
                            Text('Interest: \$${payment['interest'].toStringAsFixed(2)}'),
                            Text('Balance: \$${payment['balance'].toStringAsFixed(2)}'),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
