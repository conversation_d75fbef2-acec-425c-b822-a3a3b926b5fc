class DhikrSession {
  int id = 0;
  int dhikrId = 0;
  int targetCount = 33;
  int currentCount = 0;
  bool isCompleted = false;
  DateTime startTime = DateTime.now();
  DateTime? endTime;
  int durationSeconds = 0;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  DhikrSession();

  DhikrSession.create({
    required this.dhikrId,
    required this.targetCount,
    required this.userId,
  }) {
    final now = DateTime.now();
    startTime = now;
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void increment() {
    if (currentCount < targetCount) {
      currentCount++;
      updateTimestamp();
      
      if (currentCount >= targetCount && !isCompleted) {
        complete();
      }
    }
  }

  void decrement() {
    if (currentCount > 0) {
      currentCount--;
      if (isCompleted) {
        isCompleted = false;
        endTime = null;
      }
      updateTimestamp();
    }
  }

  void reset() {
    currentCount = 0;
    isCompleted = false;
    endTime = null;
    startTime = DateTime.now();
    durationSeconds = 0;
    updateTimestamp();
  }

  void complete() {
    if (!isCompleted) {
      isCompleted = true;
      endTime = DateTime.now();
      durationSeconds = endTime!.difference(startTime).inSeconds;
      updateTimestamp();
    }
  }

  void setCount(int count) {
    if (count >= 0 && count <= targetCount) {
      currentCount = count;
      
      if (count >= targetCount && !isCompleted) {
        complete();
      } else if (count < targetCount && isCompleted) {
        isCompleted = false;
        endTime = null;
      }
      
      updateTimestamp();
    }
  }

  double get progressPercentage {
    if (targetCount == 0) return 0.0;
    return (currentCount / targetCount).clamp(0.0, 1.0);
  }

  String get formattedDuration {
    final duration = isCompleted && endTime != null
        ? endTime!.difference(startTime)
        : DateTime.now().difference(startTime);
    
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds.remainder(60);
    
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  bool get isActive => !isCompleted && currentCount > 0;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dhikrId': dhikrId,
      'targetCount': targetCount,
      'currentCount': currentCount,
      'isCompleted': isCompleted,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'durationSeconds': durationSeconds,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory DhikrSession.fromJson(Map<String, dynamic> json) {
    final session = DhikrSession();
    session.id = json['id'] ?? 0;
    session.dhikrId = json['dhikrId'] ?? 0;
    session.targetCount = json['targetCount'] ?? 33;
    session.currentCount = json['currentCount'] ?? 0;
    session.isCompleted = json['isCompleted'] ?? false;
    session.startTime = DateTime.parse(json['startTime']);
    session.endTime = json['endTime'] != null ? DateTime.parse(json['endTime']) : null;
    session.durationSeconds = json['durationSeconds'] ?? 0;
    session.userId = json['userId'] ?? '';
    session.createdAt = DateTime.parse(json['createdAt']);
    session.updatedAt = DateTime.parse(json['updatedAt']);
    session.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    session.isDeleted = json['isDeleted'] ?? false;
    session.syncId = json['syncId'];
    return session;
  }
}
