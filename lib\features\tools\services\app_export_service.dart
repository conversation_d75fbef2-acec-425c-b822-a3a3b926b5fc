import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import '../models/ui_component.dart';
import '../models/mobile_app_theme.dart';

// Import the UIComponentData class from the UI builder screen
class UIComponentData {
  final String id;
  ComponentType type;
  String label;
  double x;
  double y;
  double width;
  double height;
  String? cellBinding;

  UIComponentData({
    required this.id,
    required this.type,
    required this.label,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.cellBinding,
  });
}

/// Service for exporting Tool Builder apps to various formats
class AppExportService {
  static final AppExportService _instance = AppExportService._internal();
  factory AppExportService() => _instance;
  AppExportService._internal();

  /// Export app as Flutter project
  Future<Map<String, String>> exportAsFlutterProject({
    required String appName,
    required List<UIComponentData> components,
    required MobileAppTheme theme,
    required Map<String, String> cellBindings,
    required Map<String, dynamic> cellValues,
  }) async {
    final files = <String, String>{};
    
    // Generate main.dart
    files['lib/main.dart'] = _generateMainDart(appName, theme);
    
    // Generate app.dart
    files['lib/app.dart'] = _generateAppDart(appName, components, theme, cellBindings);
    
    // Generate home_screen.dart
    files['lib/screens/home_screen.dart'] = _generateHomeScreen(components, cellBindings, cellValues);
    
    // Generate theme.dart
    files['lib/theme/app_theme.dart'] = _generateThemeDart(theme);
    
    // Generate models
    files['lib/models/app_state.dart'] = _generateAppStateDart(components);
    
    // Generate services
    files['lib/services/calculation_service.dart'] = _generateCalculationService();
    
    // Generate pubspec.yaml
    files['pubspec.yaml'] = _generatePubspecYaml(appName);
    
    // Generate README.md
    files['README.md'] = _generateReadme(appName, components.length);
    
    return files;
  }

  /// Export app as Android APK configuration
  Map<String, dynamic> exportAsAndroidConfig({
    required String appName,
    required List<UIComponentData> components,
    required MobileAppTheme theme,
  }) {
    return {
      'appName': appName,
      'packageName': 'com.toolbuilder.${appName.toLowerCase().replaceAll(' ', '')}',
      'versionName': '1.0.0',
      'versionCode': 1,
      'minSdkVersion': 21,
      'targetSdkVersion': 34,
      'theme': {
        'primaryColor': '#${theme.colorScheme.primary.value.toRadixString(16).padLeft(8, '0').substring(2)}',
        'accentColor': '#${theme.colorScheme.secondary.value.toRadixString(16).padLeft(8, '0').substring(2)}',
        'backgroundColor': '#${theme.colorScheme.surface.value.toRadixString(16).padLeft(8, '0').substring(2)}',
      },
      'features': _extractAppFeatures(components),
      'permissions': _getRequiredPermissions(components),
    };
  }

  /// Export app as web deployment
  Map<String, String> exportAsWebApp({
    required String appName,
    required List<UIComponentData> components,
    required MobileAppTheme theme,
    required Map<String, String> cellBindings,
  }) {
    return {
      'web/index.html': _generateIndexHtml(appName, theme),
      'web/manifest.json': _generateWebManifest(appName),
      'web/sw.js': _generateServiceWorker(appName),
      'lib/main.dart': _generateWebMainDart(appName, components, theme, cellBindings),
    };
  }

  /// Generate main.dart file
  String _generateMainDart(String appName, MobileAppTheme theme) {
    return '''
import 'package:flutter/material.dart';
import 'app.dart';

void main() {
  runApp(${_toCamelCase(appName)}App());
}
''';
  }

  /// Generate app.dart file
  String _generateAppDart(String appName, List<UIComponentData> components, MobileAppTheme theme, Map<String, String> cellBindings) {
    return '''
import 'package:flutter/material.dart';
import 'screens/home_screen.dart';
import 'theme/app_theme.dart';

class ${_toCamelCase(appName)}App extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '$appName',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      home: HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
''';
  }

  /// Generate home screen
  String _generateHomeScreen(List<UIComponentData> components, Map<String, String> cellBindings, Map<String, dynamic> cellValues) {
    final buffer = StringBuffer();
    
    buffer.writeln('''
import 'package:flutter/material.dart';
import '../services/calculation_service.dart';
import '../models/app_state.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final CalculationService _calculationService = CalculationService();
  late AppState _appState;

  @override
  void initState() {
    super.initState();
    _appState = AppState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My Tool'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
''');

    // Generate components
    for (int i = 0; i < components.length; i++) {
      final component = components[i];
      buffer.writeln('            ${_generateComponentWidget(component, cellBindings)},');
      if (i < components.length - 1) {
        buffer.writeln('            SizedBox(height: 16),');
      }
    }

    buffer.writeln('''
          ],
        ),
      ),
    );
  }
''');

    // Generate helper methods
    for (final component in components) {
      if (component.type == ComponentType.button) {
        buffer.writeln(_generateButtonHandler(component));
      }
    }

    buffer.writeln('}');
    
    return buffer.toString();
  }

  /// Generate component widget code
  String _generateComponentWidget(UIComponentData component, Map<String, String> cellBindings) {
    final cellBinding = cellBindings[component.id];
    
    switch (component.type) {
      case ComponentType.textField:
        return '''TextField(
              decoration: InputDecoration(
                labelText: '${component.label}',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) => setState(() {
                _appState.setValue('${component.id}', value);
              }),
            )''';
      
      case ComponentType.outputField:
        return '''Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).colorScheme.outline),
                borderRadius: BorderRadius.circular(8),
                color: Theme.of(context).colorScheme.surfaceVariant,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${component.label}',
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                  SizedBox(height: 8),
                  Text(
                    _appState.getValue('${component.id}', '${cellBinding ?? component.label}'),
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            )''';
      
      case ComponentType.button:
        return '''ElevatedButton(
              onPressed: _handle${_toCamelCase(component.label)}Press,
              child: Text('${component.label}'),
            )''';
      
      case ComponentType.toggle:
        return '''SwitchListTile(
              title: Text('${component.label}'),
              value: _appState.getBoolValue('${component.id}', false),
              onChanged: (value) => setState(() {
                _appState.setValue('${component.id}', value);
              }),
            )''';
      
      case ComponentType.slider:
        return '''Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${component.label}: \${_appState.getDoubleValue('${component.id}', 0.0).toStringAsFixed(1)}'),
                Slider(
                  value: _appState.getDoubleValue('${component.id}', 0.0),
                  min: 0.0,
                  max: 100.0,
                  onChanged: (value) => setState(() {
                    _appState.setValue('${component.id}', value);
                  }),
                ),
              ],
            )''';
      
      default:
        return '''Text('${component.label}')''';
    }
  }

  /// Generate button handler method
  String _generateButtonHandler(UIComponentData component) {
    return '''
  void _handle${_toCamelCase(component.label)}Press() {
    // Handle ${component.label} button press
    setState(() {
      // Add your button logic here
    });
  }
''';
  }

  /// Generate theme file
  String _generateThemeDart(MobileAppTheme theme) {
    return '''
import 'package:flutter/material.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Color(theme.colorScheme.primary.value),
        brightness: Brightness.light,
      ),
      useMaterial3: true,
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: ${theme.type == MobileAppThemeType.ios},
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Color(theme.colorScheme.primary.value),
        brightness: Brightness.dark,
      ),
      useMaterial3: true,
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: ${theme.type == MobileAppThemeType.ios},
      ),
    );
  }
}
''';
  }

  /// Generate app state model
  String _generateAppStateDart(List<UIComponentData> components) {
    return '''
class AppState {
  final Map<String, dynamic> _values = {};

  void setValue(String key, dynamic value) {
    _values[key] = value;
  }

  String getValue(String key, String defaultValue) {
    return _values[key]?.toString() ?? defaultValue;
  }

  bool getBoolValue(String key, bool defaultValue) {
    return _values[key] as bool? ?? defaultValue;
  }

  double getDoubleValue(String key, double defaultValue) {
    return _values[key] as double? ?? defaultValue;
  }

  int getIntValue(String key, int defaultValue) {
    return _values[key] as int? ?? defaultValue;
  }

  Map<String, dynamic> getAllValues() {
    return Map.unmodifiable(_values);
  }

  void clear() {
    _values.clear();
  }
}
''';
  }

  /// Generate calculation service
  String _generateCalculationService() {
    return '''
class CalculationService {
  double add(double a, double b) => a + b;
  double subtract(double a, double b) => a - b;
  double multiply(double a, double b) => a * b;
  double divide(double a, double b) => b != 0 ? a / b : 0;
  
  double percentage(double value, double percent) => value * (percent / 100);
  double compound(double principal, double rate, int periods) {
    return principal * pow(1 + rate, periods);
  }
}
''';
  }

  /// Generate pubspec.yaml
  String _generatePubspecYaml(String appName) {
    return '''
name: ${appName.toLowerCase().replaceAll(' ', '_')}
description: A Flutter app generated by Tool Builder
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
''';
  }

  /// Generate README.md
  String _generateReadme(String appName, int componentCount) {
    return '''
# $appName

A Flutter application generated by Tool Builder.

## Features

- $componentCount interactive components
- Modern Material Design 3 UI
- Responsive layout
- Built-in calculations and formulas

## Getting Started

1. Make sure you have Flutter installed
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the app

## Generated Components

This app was automatically generated from a Tool Builder design with $componentCount components.

## Customization

You can customize this app by modifying the generated code in the `lib/` directory.
''';
  }

  /// Generate index.html for web
  String _generateIndexHtml(String appName, MobileAppTheme theme) {
    return '''
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>$appName</title>
  <link rel="manifest" href="manifest.json">
  <meta name="theme-color" content="#${theme.colorScheme.primary.value.toRadixString(16).padLeft(8, '0').substring(2)}">
</head>
<body>
  <div id="loading">
    <style>
      #loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-family: Arial, sans-serif;
      }
    </style>
    Loading $appName...
  </div>
  <script src="main.dart.js"></script>
</body>
</html>
''';
  }

  /// Generate web manifest
  String _generateWebManifest(String appName) {
    return jsonEncode({
      'name': appName,
      'short_name': appName,
      'start_url': '.',
      'display': 'standalone',
      'background_color': '#ffffff',
      'theme_color': '#2196f3',
      'description': 'A Tool Builder generated app',
      'orientation': 'portrait-primary',
      'prefer_related_applications': false,
      'icons': [
        {
          'src': 'icons/Icon-192.png',
          'sizes': '192x192',
          'type': 'image/png',
        },
        {
          'src': 'icons/Icon-512.png',
          'sizes': '512x512',
          'type': 'image/png',
        },
      ],
    });
  }

  /// Generate service worker
  String _generateServiceWorker(String appName) {
    return '''
const CACHE_NAME = '${appName.toLowerCase()}-v1';
const urlsToCache = [
  '/',
  '/main.dart.js',
  '/manifest.json',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => response || fetch(event.request))
  );
});
''';
  }

  /// Generate web main.dart
  String _generateWebMainDart(String appName, List<UIComponentData> components, MobileAppTheme theme, Map<String, String> cellBindings) {
    return '''
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

void main() {
  runApp(${_toCamelCase(appName)}WebApp());
}

class ${_toCamelCase(appName)}WebApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '$appName',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Color(theme.colorScheme.primary.value),
        ),
        useMaterial3: true,
      ),
      home: WebHomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class WebHomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('$appName'),
        centerTitle: true,
      ),
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 600),
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Welcome to $appName',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              SizedBox(height: 24),
              Text(
                'This web app was generated from ${components.length} components.',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
''';
  }

  /// Extract app features from components
  List<String> _extractAppFeatures(List<UIComponentData> components) {
    final features = <String>[];
    
    if (components.any((c) => c.type == ComponentType.textField)) {
      features.add('Text Input');
    }
    if (components.any((c) => c.type == ComponentType.outputField)) {
      features.add('Data Display');
    }
    if (components.any((c) => c.type == ComponentType.button)) {
      features.add('Interactive Buttons');
    }
    if (components.any((c) => c.type == ComponentType.chart)) {
      features.add('Data Visualization');
    }
    if (components.any((c) => c.type == ComponentType.slider)) {
      features.add('Range Selection');
    }
    
    return features;
  }

  /// Get required permissions for components
  List<String> _getRequiredPermissions(List<UIComponentData> components) {
    final permissions = <String>[];
    
    // Add permissions based on component types
    if (components.any((c) => c.type == ComponentType.chart)) {
      permissions.add('INTERNET'); // For potential data fetching
    }
    
    return permissions;
  }

  /// Convert string to camelCase
  String _toCamelCase(String input) {
    return input
        .split(' ')
        .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join('');
  }
}
