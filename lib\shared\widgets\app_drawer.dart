import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/user_provider.dart';
import '../../core/navigation/app_router.dart';

class AppDrawer extends ConsumerWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfile = ref.watch(userProfileProvider);
    final syncEnabled = ref.watch(syncEnabledProvider);

    return Drawer(
      child: Column(
        children: [
          // User Profile Header
          UserAccountsDrawerHeader(
            accountName: Text(userProfile?.name ?? 'Guest User'),
            accountEmail: Text(userProfile?.email ?? 'No email'),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: userProfile?.avatarPath != null
                  ? ClipOval(
                      child: Image.asset(
                        userProfile!.avatarPath!,
                        fit: BoxFit.cover,
                        width: 72,
                        height: 72,
                      ),
                    )
                  : Text(
                      userProfile?.name.isNotEmpty == true
                          ? userProfile!.name[0].toUpperCase()
                          : 'G',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
            ),
            otherAccountsPictures: [
              if (syncEnabled)
                Icon(
                  Icons.cloud_done,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
            ],
          ),

          // Navigation Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  route: AppRoutes.dashboard,
                ),
                const Divider(),
                
                // Mini Apps Section
                _buildSectionHeader(context, 'Mini Apps'),
                _buildDrawerItem(
                  context,
                  icon: Icons.note_alt_outlined,
                  title: 'Memo Suite',
                  subtitle: 'Notes, todos & voice memos',
                  route: AppRoutes.memo,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.mosque_outlined,
                  title: 'Athkar Pro',
                  subtitle: 'Islamic remembrance',
                  route: AppRoutes.islami,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.build_outlined,
                  title: 'Tools Builder',
                  subtitle: 'Custom calculators',
                  route: AppRoutes.tools,
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.account_balance_wallet_outlined,
                  title: 'Money Flow',
                  subtitle: 'Financial tracking',
                  route: AppRoutes.money,
                ),
                
                const Divider(),
                
                // Settings Section
                _buildDrawerItem(
                  context,
                  icon: Icons.settings,
                  title: 'Settings',
                  route: AppRoutes.settings,
                ),
              ],
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                if (syncEnabled)
                  Row(
                    children: [
                      Icon(
                        Icons.cloud_done,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Sync enabled',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                const SizedBox(height: 8),
                Text(
                  'ShadowSuite v1.0.0',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    required String route,
    bool isComingSoon = false,
  }) {
    final isSelected = GoRouter.of(context).routerDelegate.currentConfiguration.uri.path == route;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      trailing: isComingSoon
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Soon',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSecondaryContainer,
                ),
              ),
            )
          : null,
      selected: isSelected,
      selectedTileColor: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
      onTap: () {
        if (isComingSoon) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$title is coming soon!'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else {
          context.go(route);
          Navigator.of(context).pop(); // Close drawer
        }
      },
    );
  }
}
