import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../core/services/permissions_service.dart';

class PermissionsScreen extends StatefulWidget {
  const PermissionsScreen({super.key});

  @override
  State<PermissionsScreen> createState() => _PermissionsScreenState();
}

class _PermissionsScreenState extends State<PermissionsScreen> {
  final PermissionsService _permissionsService = PermissionsService.instance;
  Map<String, PermissionStatus> _permissionStatuses = {};
  bool _isLoading = true;

  final List<Permission> _permissions = [
    Permission.microphone,
    Permission.camera,
    Permission.notification,
    Permission.storage,
    Permission.photos,
    Permission.videos,
    Permission.audio,
    Permission.locationWhenInUse,
    Permission.contacts,
    Permission.calendarWriteOnly,
  ];

  @override
  void initState() {
    super.initState();
    _loadPermissionStatuses();
  }

  Future<void> _loadPermissionStatuses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final statuses = await _permissionsService.getAllPermissionStatuses();
      setState(() {
        _permissionStatuses = statuses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Permissions'),
        actions: [
          IconButton(
            onPressed: _loadPermissionStatuses,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadPermissionStatuses,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.security,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Permission Management',
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'ShadowSuite respects your privacy. Permissions are only used for their intended features.',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Essential Permissions
                    Text(
                      'Essential Permissions',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'These permissions are required for core app functionality.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    const SizedBox(height: 16),

                    _buildPermissionCard(Permission.microphone, isEssential: true),
                    _buildPermissionCard(Permission.notification, isEssential: true),
                    _buildPermissionCard(Permission.storage, isEssential: true),

                    const SizedBox(height: 24),

                    // Optional Permissions
                    Text(
                      'Optional Permissions',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'These permissions enhance your experience but are not required.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    const SizedBox(height: 16),

                    _buildPermissionCard(Permission.camera),
                    _buildPermissionCard(Permission.locationWhenInUse),
                    _buildPermissionCard(Permission.contacts),
                    _buildPermissionCard(Permission.calendar),

                    const SizedBox(height: 24),

                    // Actions
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Quick Actions',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 16),
                            
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: _requestAllEssentialPermissions,
                                icon: const Icon(Icons.security),
                                label: const Text('Grant Essential Permissions'),
                              ),
                            ),
                            
                            const SizedBox(height: 8),
                            
                            SizedBox(
                              width: double.infinity,
                              child: OutlinedButton.icon(
                                onPressed: _openAppSettings,
                                icon: const Icon(Icons.settings),
                                label: const Text('Open App Settings'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Privacy Notice
                    Card(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.privacy_tip,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Privacy Notice',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '• Voice recordings are stored locally on your device\n'
                              '• Location data is only used for prayer times\n'
                              '• No personal data is shared with third parties\n'
                              '• You can revoke permissions at any time',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildPermissionCard(Permission permission, {bool isEssential = false}) {
    final permissionKey = permission.toString();
    final status = _permissionStatuses[permissionKey] ?? PermissionStatus.denied;
    final isGranted = status == PermissionStatus.granted;
    final isPermanentlyDenied = status == PermissionStatus.permanentlyDenied;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isGranted 
              ? Colors.green 
              : isPermanentlyDenied 
                  ? Colors.red 
                  : Colors.orange,
          child: Icon(
            _getPermissionIcon(permission),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Text(_permissionsService.getPermissionDisplayName(permission)),
            if (isEssential) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'REQUIRED',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_permissionsService.getPermissionDescription(permission)),
            const SizedBox(height: 4),
            Text(
              _getStatusText(status),
              style: TextStyle(
                color: isGranted 
                    ? Colors.green 
                    : isPermanentlyDenied 
                        ? Colors.red 
                        : Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        trailing: isGranted
            ? const Icon(Icons.check_circle, color: Colors.green)
            : isPermanentlyDenied
                ? IconButton(
                    onPressed: _openAppSettings,
                    icon: const Icon(Icons.settings, color: Colors.red),
                    tooltip: 'Open Settings',
                  )
                : IconButton(
                    onPressed: () => _requestPermission(permission),
                    icon: const Icon(Icons.arrow_forward, color: Colors.orange),
                    tooltip: 'Grant Permission',
                  ),
        onTap: isGranted 
            ? null 
            : isPermanentlyDenied 
                ? _openAppSettings 
                : () => _requestPermission(permission),
      ),
    );
  }

  IconData _getPermissionIcon(Permission permission) {
    switch (permission) {
      case Permission.microphone:
        return Icons.mic;
      case Permission.camera:
        return Icons.camera_alt;
      case Permission.storage:
      case Permission.photos:
      case Permission.videos:
      case Permission.audio:
        return Icons.folder;
      case Permission.notification:
        return Icons.notifications;
      case Permission.locationWhenInUse:
      case Permission.locationAlways:
        return Icons.location_on;
      case Permission.contacts:
        return Icons.contacts;
      case Permission.calendarWriteOnly:
        return Icons.calendar_today;
      default:
        return Icons.security;
    }
  }

  String _getStatusText(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Granted';
      case PermissionStatus.denied:
        return 'Denied';
      case PermissionStatus.restricted:
        return 'Restricted';
      case PermissionStatus.limited:
        return 'Limited';
      case PermissionStatus.permanentlyDenied:
        return 'Permanently Denied';
      case PermissionStatus.provisional:
        return 'Provisional';
      default:
        return 'Unknown';
    }
  }

  Future<void> _requestPermission(Permission permission) async {
    try {
      final status = await _permissionsService.requestPermission(permission);
      
      setState(() {
        _permissionStatuses[permission.toString()] = status;
      });

      if (mounted) {
        final message = status == PermissionStatus.granted
            ? '${_permissionsService.getPermissionDisplayName(permission)} permission granted'
            : _permissionsService.getPermissionDenialMessage(permission);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: status == PermissionStatus.granted 
                ? Colors.green 
                : Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permission: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _requestAllEssentialPermissions() async {
    try {
      final statuses = await _permissionsService.requestEssentialPermissions();
      
      setState(() {
        for (final entry in statuses.entries) {
          _permissionStatuses[entry.key.toString()] = entry.value;
        }
      });

      final grantedCount = statuses.values.where((s) => s == PermissionStatus.granted).length;
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$grantedCount of ${statuses.length} essential permissions granted'),
            backgroundColor: grantedCount == statuses.length ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open app settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
