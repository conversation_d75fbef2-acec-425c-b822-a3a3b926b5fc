/// User profile model for ShadowSuite
class UserProfile {
  final String id;
  final String? name;
  final String? email;
  final String? bio;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified;
  final Map<String, dynamic>? preferences;

  const UserProfile({
    required this.id,
    this.name,
    this.email,
    this.bio,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.preferences,
  });

  /// Create a new user profile
  factory UserProfile.create({
    required String id,
    String? name,
    String? email,
    String? bio,
    String? avatarUrl,
    Map<String, dynamic>? preferences,
  }) {
    final now = DateTime.now();
    return UserProfile(
      id: id,
      name: name,
      email: email,
      bio: bio,
      avatarUrl: avatarUrl,
      createdAt: now,
      updatedAt: now,
      isVerified: false,
      preferences: preferences ?? {},
    );
  }

  /// Create a copy with updated fields
  UserProfile copyWith({
    String? id,
    String? name,
    String? email,
    String? bio,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    Map<String, dynamic>? preferences,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      bio: bio ?? this.bio,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      preferences: preferences ?? this.preferences,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'bio': bio,
      'avatarUrl': avatarUrl,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isVerified': isVerified,
      'preferences': preferences,
    };
  }

  /// Create from JSON
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as String,
      name: json['name'] as String?,
      email: json['email'] as String?,
      bio: json['bio'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isVerified: json['isVerified'] as bool? ?? false,
      preferences: json['preferences'] as Map<String, dynamic>?,
    );
  }

  /// Get display name
  String get displayName {
    if (name?.isNotEmpty == true) return name!;
    if (email?.isNotEmpty == true) return email!.split('@').first;
    return 'User';
  }

  /// Get initials for avatar
  String get initials {
    if (name?.isNotEmpty == true) {
      final parts = name!.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name![0].toUpperCase();
    }
    if (email?.isNotEmpty == true) {
      return email![0].toUpperCase();
    }
    return 'U';
  }

  /// Check if profile is complete
  bool get isComplete {
    return name?.isNotEmpty == true && 
           email?.isNotEmpty == true && 
           bio?.isNotEmpty == true;
  }

  /// Get completion percentage
  double get completionPercentage {
    int completed = 0;
    int total = 4;

    if (name?.isNotEmpty == true) completed++;
    if (email?.isNotEmpty == true) completed++;
    if (bio?.isNotEmpty == true) completed++;
    if (avatarUrl?.isNotEmpty == true) completed++;

    return completed / total;
  }

  @override
  String toString() {
    return 'UserProfile(id: $id, name: $name, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
