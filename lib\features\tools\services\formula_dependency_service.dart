import '../models/spreadsheet_cell.dart';

/// Service for tracking formula dependencies and relationships
class FormulaDependencyService {
  static final FormulaDependencyService _instance = FormulaDependencyService._internal();
  factory FormulaDependencyService() => _instance;
  FormulaDependencyService._internal();

  /// Build dependency graph for all cells
  DependencyGraph buildDependencyGraph(Map<String, SpreadsheetCell> cells) {
    final graph = DependencyGraph();
    
    // First pass: identify all cells with formulas
    for (final entry in cells.entries) {
      final address = entry.key;
      final cell = entry.value;
      
      if (cell.hasFormula) {
        graph.addNode(address);
        
        // Extract dependencies from formula
        final dependencies = extractCellReferences(cell.formula!);
        for (final dependency in dependencies) {
          graph.addDependency(address, dependency);
        }
      }
    }
    
    return graph;
  }

  /// Extract cell references from a formula
  List<String> extractCellReferences(String formula) {
    final references = <String>[];
    
    // Match single cell references (A1, B2, etc.)
    final singleCellRegex = RegExp(r'\b[A-Z]+\d+\b');
    final singleMatches = singleCellRegex.allMatches(formula);
    for (final match in singleMatches) {
      references.add(match.group(0)!);
    }
    
    // Match range references (A1:B10, etc.)
    final rangeRegex = RegExp(r'\b[A-Z]+\d+:[A-Z]+\d+\b');
    final rangeMatches = rangeRegex.allMatches(formula);
    for (final match in rangeMatches) {
      final range = match.group(0)!;
      references.addAll(expandRange(range));
    }
    
    return references.toSet().toList(); // Remove duplicates
  }

  /// Expand a range reference into individual cell addresses
  List<String> expandRange(String range) {
    final parts = range.split(':');
    if (parts.length != 2) return [];
    
    final startAddress = parts[0];
    final endAddress = parts[1];
    
    final startPos = SpreadsheetUtils.parseCellAddress(startAddress);
    final endPos = SpreadsheetUtils.parseCellAddress(endAddress);
    
    final cells = <String>[];
    
    for (int row = startPos['row']!; row <= endPos['row']!; row++) {
      for (int col = startPos['column']!; col <= endPos['column']!; col++) {
        cells.add(SpreadsheetUtils.getCellAddress(row, col));
      }
    }
    
    return cells;
  }

  /// Get all cells that depend on a given cell
  List<String> getDependents(String cellAddress, Map<String, SpreadsheetCell> cells) {
    final dependents = <String>[];
    
    for (final entry in cells.entries) {
      final address = entry.key;
      final cell = entry.value;
      
      if (cell.hasFormula) {
        final dependencies = extractCellReferences(cell.formula!);
        if (dependencies.contains(cellAddress)) {
          dependents.add(address);
        }
      }
    }
    
    return dependents;
  }

  /// Get all cells that a given cell depends on
  List<String> getPrecedents(String cellAddress, Map<String, SpreadsheetCell> cells) {
    final cell = cells[cellAddress];
    if (cell == null || !cell.hasFormula) return [];
    
    return extractCellReferences(cell.formula!);
  }

  /// Check for circular references
  List<String> detectCircularReferences(Map<String, SpreadsheetCell> cells) {
    final graph = buildDependencyGraph(cells);
    return graph.detectCycles();
  }

  /// Get calculation order to avoid circular dependencies
  List<String> getCalculationOrder(Map<String, SpreadsheetCell> cells) {
    final graph = buildDependencyGraph(cells);
    return graph.topologicalSort();
  }

  /// Trace formula dependencies for visualization
  DependencyTrace traceDependencies(String cellAddress, Map<String, SpreadsheetCell> cells, {int maxDepth = 5}) {
    final trace = DependencyTrace(rootCell: cellAddress);
    final visited = <String>{};
    
    _traceDependenciesRecursive(cellAddress, cells, trace, visited, 0, maxDepth);
    
    return trace;
  }

  void _traceDependenciesRecursive(
    String cellAddress,
    Map<String, SpreadsheetCell> cells,
    DependencyTrace trace,
    Set<String> visited,
    int currentDepth,
    int maxDepth,
  ) {
    if (currentDepth >= maxDepth || visited.contains(cellAddress)) return;
    
    visited.add(cellAddress);
    
    final precedents = getPrecedents(cellAddress, cells);
    for (final precedent in precedents) {
      trace.addDependency(cellAddress, precedent, currentDepth);
      _traceDependenciesRecursive(precedent, cells, trace, visited, currentDepth + 1, maxDepth);
    }
    
    final dependents = getDependents(cellAddress, cells);
    for (final dependent in dependents) {
      trace.addDependent(cellAddress, dependent, currentDepth);
      _traceDependenciesRecursive(dependent, cells, trace, visited, currentDepth + 1, maxDepth);
    }
  }

  /// Analyze formula complexity
  FormulaComplexity analyzeComplexity(String formula) {
    final functions = _extractFunctions(formula);
    final cellReferences = extractCellReferences(formula);
    final operators = _extractOperators(formula);
    final nestingLevel = _calculateNestingLevel(formula);
    
    return FormulaComplexity(
      functions: functions,
      cellReferences: cellReferences,
      operators: operators,
      nestingLevel: nestingLevel,
      totalComplexity: _calculateComplexityScore(functions, cellReferences, operators, nestingLevel),
    );
  }

  List<String> _extractFunctions(String formula) {
    final functions = <String>[];
    final functionRegex = RegExp(r'\b[A-Z]+(?=\()');
    final matches = functionRegex.allMatches(formula);
    
    for (final match in matches) {
      functions.add(match.group(0)!);
    }
    
    return functions;
  }

  List<String> _extractOperators(String formula) {
    final operators = <String>[];
    final operatorRegex = RegExp(r'[+\-*/^<>=&]');
    final matches = operatorRegex.allMatches(formula);
    
    for (final match in matches) {
      operators.add(match.group(0)!);
    }
    
    return operators;
  }

  int _calculateNestingLevel(String formula) {
    int maxLevel = 0;
    int currentLevel = 0;
    
    for (int i = 0; i < formula.length; i++) {
      if (formula[i] == '(') {
        currentLevel++;
        maxLevel = maxLevel > currentLevel ? maxLevel : currentLevel;
      } else if (formula[i] == ')') {
        currentLevel--;
      }
    }
    
    return maxLevel;
  }

  double _calculateComplexityScore(
    List<String> functions,
    List<String> cellReferences,
    List<String> operators,
    int nestingLevel,
  ) {
    double score = 0;
    
    // Base complexity from components
    score += functions.length * 2.0;
    score += cellReferences.length * 1.0;
    score += operators.length * 0.5;
    score += nestingLevel * 3.0;
    
    // Bonus for complex functions
    final complexFunctions = ['VLOOKUP', 'HLOOKUP', 'INDEX', 'MATCH', 'SUMIFS', 'COUNTIFS', 'AVERAGEIFS'];
    for (final function in functions) {
      if (complexFunctions.contains(function)) {
        score += 5.0;
      }
    }
    
    return score;
  }
}

/// Dependency graph for tracking cell relationships
class DependencyGraph {
  final Map<String, Set<String>> _dependencies = {};
  final Map<String, Set<String>> _dependents = {};

  void addNode(String node) {
    _dependencies[node] ??= <String>{};
    _dependents[node] ??= <String>{};
  }

  void addDependency(String node, String dependency) {
    addNode(node);
    addNode(dependency);
    
    _dependencies[node]!.add(dependency);
    _dependents[dependency]!.add(node);
  }

  Set<String> getDependencies(String node) {
    return _dependencies[node] ?? <String>{};
  }

  Set<String> getDependents(String node) {
    return _dependents[node] ?? <String>{};
  }

  List<String> detectCycles() {
    final cycles = <String>[];
    final visited = <String>{};
    final recursionStack = <String>{};

    for (final node in _dependencies.keys) {
      if (!visited.contains(node)) {
        _detectCyclesRecursive(node, visited, recursionStack, cycles);
      }
    }

    return cycles;
  }

  bool _detectCyclesRecursive(
    String node,
    Set<String> visited,
    Set<String> recursionStack,
    List<String> cycles,
  ) {
    visited.add(node);
    recursionStack.add(node);

    for (final dependency in getDependencies(node)) {
      if (!visited.contains(dependency)) {
        if (_detectCyclesRecursive(dependency, visited, recursionStack, cycles)) {
          return true;
        }
      } else if (recursionStack.contains(dependency)) {
        cycles.add(node);
        return true;
      }
    }

    recursionStack.remove(node);
    return false;
  }

  List<String> topologicalSort() {
    final result = <String>[];
    final visited = <String>{};
    final stack = <String>[];

    for (final node in _dependencies.keys) {
      if (!visited.contains(node)) {
        _topologicalSortRecursive(node, visited, stack);
      }
    }

    return stack.reversed.toList();
  }

  void _topologicalSortRecursive(String node, Set<String> visited, List<String> stack) {
    visited.add(node);

    for (final dependency in getDependencies(node)) {
      if (!visited.contains(dependency)) {
        _topologicalSortRecursive(dependency, visited, stack);
      }
    }

    stack.add(node);
  }
}

/// Dependency trace for visualization
class DependencyTrace {
  final String rootCell;
  final Map<String, List<DependencyLink>> _links = {};

  DependencyTrace({required this.rootCell});

  void addDependency(String from, String to, int depth) {
    _links[from] ??= [];
    _links[from]!.add(DependencyLink(
      from: from,
      to: to,
      type: DependencyType.precedent,
      depth: depth,
    ));
  }

  void addDependent(String from, String to, int depth) {
    _links[from] ??= [];
    _links[from]!.add(DependencyLink(
      from: from,
      to: to,
      type: DependencyType.dependent,
      depth: depth,
    ));
  }

  List<DependencyLink> getAllLinks() {
    final allLinks = <DependencyLink>[];
    for (final links in _links.values) {
      allLinks.addAll(links);
    }
    return allLinks;
  }

  List<DependencyLink> getLinksForCell(String cellAddress) {
    return _links[cellAddress] ?? [];
  }
}

/// Dependency link between cells
class DependencyLink {
  final String from;
  final String to;
  final DependencyType type;
  final int depth;

  const DependencyLink({
    required this.from,
    required this.to,
    required this.type,
    required this.depth,
  });
}

/// Type of dependency relationship
enum DependencyType {
  precedent, // Cell that this cell depends on
  dependent, // Cell that depends on this cell
}

/// Formula complexity analysis
class FormulaComplexity {
  final List<String> functions;
  final List<String> cellReferences;
  final List<String> operators;
  final int nestingLevel;
  final double totalComplexity;

  const FormulaComplexity({
    required this.functions,
    required this.cellReferences,
    required this.operators,
    required this.nestingLevel,
    required this.totalComplexity,
  });

  String get complexityLevel {
    if (totalComplexity < 5) return 'Simple';
    if (totalComplexity < 15) return 'Moderate';
    if (totalComplexity < 30) return 'Complex';
    return 'Very Complex';
  }
}
