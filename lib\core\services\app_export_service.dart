import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../interfaces/app_module_interface.dart';

/// Service for exporting mini-apps as standalone Flutter projects
class AppExportService {
  static final AppExportService _instance = AppExportService._internal();
  factory AppExportService() => _instance;
  AppExportService._internal();

  /// Export a mini-app as a standalone Flutter project
  Future<String> exportApp(AppModuleInterface module) async {
    if (!module.canExportStandalone()) {
      throw Exception('Module ${module.name} cannot be exported as standalone');
    }

    try {
      // Create export directory
      final exportDir = await _createExportDirectory(module);
      
      // Generate project structure
      await _generateProjectStructure(exportDir, module);
      
      // Copy module files
      await _copyModuleFiles(exportDir, module);
      
      // Copy core dependencies
      await _copyCoreFiles(exportDir, module);
      
      // Generate configuration files
      await _generateConfigFiles(exportDir, module);
      
      // Generate main.dart entry point
      await _generateMainDart(exportDir, module);
      
      // Generate pubspec.yaml
      await _generatePubspec(exportDir, module);
      
      // Generate platform-specific files
      await _generatePlatformFiles(exportDir, module);
      
      debugPrint('Successfully exported ${module.name} to: ${exportDir.path}');
      return exportDir.path;
      
    } catch (e) {
      debugPrint('Error exporting app: $e');
      rethrow;
    }
  }

  /// Create export directory
  Future<Directory> _createExportDirectory(AppModuleInterface module) async {
    final documentsDir = await getApplicationDocumentsDirectory();
    final exportPath = '${documentsDir.path}/shadowsuite_exports/${module.id}_${DateTime.now().millisecondsSinceEpoch}';
    final exportDir = Directory(exportPath);
    
    if (await exportDir.exists()) {
      await exportDir.delete(recursive: true);
    }
    
    await exportDir.create(recursive: true);
    return exportDir;
  }

  /// Generate basic Flutter project structure
  Future<void> _generateProjectStructure(Directory exportDir, AppModuleInterface module) async {
    final directories = [
      'lib',
      'lib/core',
      'lib/shared',
      'lib/apps/${module.id}',
      'assets',
      'android',
      'android/app',
      'android/app/src',
      'android/app/src/main',
      'android/app/src/main/kotlin',
      'windows',
      'windows/runner',
    ];

    for (final dir in directories) {
      await Directory('${exportDir.path}/$dir').create(recursive: true);
    }
  }

  /// Copy module-specific files
  Future<void> _copyModuleFiles(Directory exportDir, AppModuleInterface module) async {
    final sourceModulePath = 'lib/apps/${module.id}';
    final targetModulePath = '${exportDir.path}/lib/apps/${module.id}';
    
    // Copy all module files
    final sourceDir = Directory(sourceModulePath);
    if (await sourceDir.exists()) {
      await _copyDirectory(sourceDir, Directory(targetModulePath));
    }
  }

  /// Copy required core files
  Future<void> _copyCoreFiles(Directory exportDir, AppModuleInterface module) async {
    final coreFiles = [
      'lib/core/interfaces',
      'lib/core/database',
      'lib/core/storage',
      'lib/core/services',
      'lib/shared/models',
      'lib/shared/widgets',
      'lib/shared/providers',
      'lib/shared/utils',
    ];

    for (final corePath in coreFiles) {
      final sourceDir = Directory(corePath);
      if (await sourceDir.exists()) {
        final targetPath = '${exportDir.path}/$corePath';
        await _copyDirectory(sourceDir, Directory(targetPath));
      }
    }
  }

  /// Generate main.dart for standalone app
  Future<void> _generateMainDart(Directory exportDir, AppModuleInterface module) async {
    final config = module.getExportConfig();
    final mainDartContent = '''
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'apps/${module.id}/app_module.dart';
import 'core/interfaces/app_module_interface.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize the module
  final module = ${_getModuleClassName(module)}();
  await module.initialize();
  
  // Register the module
  AppModuleRegistry.register(module);
  
  runApp(ProviderScope(child: ${config['appName']?.replaceAll(' ', '')}App()));
}

class ${config['appName']?.replaceAll(' ', '')}App extends ConsumerWidget {
  const ${config['appName']?.replaceAll(' ', '')}App({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final module = AppModuleRegistry.getModule('${module.id}')!;
    
    return MaterialApp.router(
      title: '${config['appName']}',
      theme: module.getThemeData(context),
      routerConfig: GoRouter(
        initialLocation: '/',
        routes: [
          GoRoute(
            path: '/',
            builder: (context, state) => module.homeScreen,
          ),
          ...module.routes,
        ],
      ),
    );
  }
}
''';

    final mainFile = File('${exportDir.path}/lib/main.dart');
    await mainFile.writeAsString(mainDartContent);
  }

  /// Generate pubspec.yaml for standalone app
  Future<void> _generatePubspec(Directory exportDir, AppModuleInterface module) async {
    final config = module.getExportConfig();
    final dependencies = config['dependencies'] as List<String>? ?? [];
    
    final pubspecContent = '''
name: ${module.id.toLowerCase()}
description: ${config['description']}
version: ${config['versionName']}+${config['versionCode']}

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.4.9
  go_router: ^12.1.3
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
${dependencies.map((dep) => '  $dep: ^2.0.0').join('\n')}

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/
${config['assets'] != null ? (config['assets'] as List).map((asset) => '    - $asset').join('\n') : ''}
''';

    final pubspecFile = File('${exportDir.path}/pubspec.yaml');
    await pubspecFile.writeAsString(pubspecContent);
  }

  /// Generate platform-specific configuration files
  Future<void> _generatePlatformFiles(Directory exportDir, AppModuleInterface module) async {
    final config = module.getExportConfig();
    
    // Generate Android configuration
    await _generateAndroidConfig(exportDir, config);
    
    // Generate Windows configuration
    await _generateWindowsConfig(exportDir, config);
  }

  /// Generate Android-specific files
  Future<void> _generateAndroidConfig(Directory exportDir, Map<String, dynamic> config) async {
    // Generate build.gradle
    final buildGradleContent = '''
android {
    compileSdkVersion ${config['targetSdkVersion']}
    
    defaultConfig {
        applicationId "${config['packageName']}"
        minSdkVersion ${config['minSdkVersion']}
        targetSdkVersion ${config['targetSdkVersion']}
        versionCode ${config['versionCode']}
        versionName "${config['versionName']}"
    }
}
''';

    final buildGradleFile = File('${exportDir.path}/android/app/build.gradle');
    await buildGradleFile.writeAsString(buildGradleContent);
    
    // Generate AndroidManifest.xml
    final manifestContent = '''
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="${config['packageName']}">
    
    <application
        android:label="${config['appName']}"
        android:name="\${applicationName}"
        android:icon="@mipmap/ic_launcher">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme">
            
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
    </application>
</manifest>
''';

    final manifestFile = File('${exportDir.path}/android/app/src/main/AndroidManifest.xml');
    await manifestFile.writeAsString(manifestContent);
  }

  /// Generate Windows-specific files
  Future<void> _generateWindowsConfig(Directory exportDir, Map<String, dynamic> config) async {
    // Generate CMakeLists.txt
    final cmakeContent = '''
cmake_minimum_required(VERSION 3.14)
project(${config['packageName']?.replaceAll('.', '_')})

set(BINARY_NAME "${config['appName']?.replaceAll(' ', '_').toLowerCase()}")

flutter_application(\${BINARY_NAME})
''';

    final cmakeFile = File('${exportDir.path}/windows/CMakeLists.txt');
    await cmakeFile.writeAsString(cmakeContent);
  }

  /// Generate additional configuration files
  Future<void> _generateConfigFiles(Directory exportDir, AppModuleInterface module) async {
    // Generate analysis_options.yaml
    final analysisOptionsContent = '''
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    avoid_print: false
''';

    final analysisFile = File('${exportDir.path}/analysis_options.yaml');
    await analysisFile.writeAsString(analysisOptionsContent);
    
    // Generate README.md
    final readmeContent = '''
# ${module.name}

${module.description}

## Features

${module.getExportConfig()['features'] != null ? (module.getExportConfig()['features'] as List).map((feature) => '- $feature').join('\n') : ''}

## Getting Started

1. Install Flutter SDK
2. Run `flutter pub get`
3. Run `flutter run` for development
4. Run `flutter build windows` for Windows executable
5. Run `flutter build apk` for Android APK

## Version

${module.version}

Generated from ShadowSuite on ${DateTime.now().toIso8601String()}
''';

    final readmeFile = File('${exportDir.path}/README.md');
    await readmeFile.writeAsString(readmeContent);
  }

  /// Copy directory recursively
  Future<void> _copyDirectory(Directory source, Directory target) async {
    await target.create(recursive: true);
    
    await for (final entity in source.list(recursive: false)) {
      if (entity is File) {
        final targetFile = File('${target.path}/${entity.path.split('/').last}');
        await entity.copy(targetFile.path);
      } else if (entity is Directory) {
        final targetDir = Directory('${target.path}/${entity.path.split('/').last}');
        await _copyDirectory(entity, targetDir);
      }
    }
  }

  /// Get module class name
  String _getModuleClassName(AppModuleInterface module) {
    return '${module.name.replaceAll(' ', '')}AppModule';
  }
}
