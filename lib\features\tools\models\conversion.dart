enum ConversionCategory {
  length,
  weight,
  temperature,
  volume,
  area,
  speed,
  time,
  energy,
  power,
  pressure,
  currency,
}

class ConversionUnit {
  final String name;
  final String symbol;
  final String category;
  final double factor; // Conversion factor to base unit
  final double offset; // Offset for temperature conversions

  const ConversionUnit({
    required this.name,
    required this.symbol,
    required this.category,
    required this.factor,
    this.offset = 0.0,
  });
}

class Conversion {
  int id = 0;
  ConversionCategory category = ConversionCategory.length;
  String fromUnit = '';
  String toUnit = '';
  double inputValue = 0.0;
  double outputValue = 0.0;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Conversion();

  Conversion.create({
    required this.category,
    required this.fromUnit,
    required this.toUnit,
    required this.inputValue,
    required this.outputValue,
    required this.userId,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  String get categoryDisplayName {
    switch (category) {
      case ConversionCategory.length:
        return 'Length';
      case ConversionCategory.weight:
        return 'Weight';
      case ConversionCategory.temperature:
        return 'Temperature';
      case ConversionCategory.volume:
        return 'Volume';
      case ConversionCategory.area:
        return 'Area';
      case ConversionCategory.speed:
        return 'Speed';
      case ConversionCategory.time:
        return 'Time';
      case ConversionCategory.energy:
        return 'Energy';
      case ConversionCategory.power:
        return 'Power';
      case ConversionCategory.pressure:
        return 'Pressure';
      case ConversionCategory.currency:
        return 'Currency';
    }
  }

  String get formattedConversion {
    return '$inputValue $fromUnit = $outputValue $toUnit';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category.name,
      'fromUnit': fromUnit,
      'toUnit': toUnit,
      'inputValue': inputValue,
      'outputValue': outputValue,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Conversion.fromJson(Map<String, dynamic> json) {
    final conversion = Conversion();
    conversion.id = json['id'] ?? 0;
    conversion.category = ConversionCategory.values.firstWhere(
      (c) => c.name == json['category'],
      orElse: () => ConversionCategory.length,
    );
    conversion.fromUnit = json['fromUnit'] ?? '';
    conversion.toUnit = json['toUnit'] ?? '';
    conversion.inputValue = (json['inputValue'] ?? 0.0).toDouble();
    conversion.outputValue = (json['outputValue'] ?? 0.0).toDouble();
    conversion.userId = json['userId'] ?? '';
    conversion.createdAt = DateTime.parse(json['createdAt']);
    conversion.updatedAt = DateTime.parse(json['updatedAt']);
    conversion.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    conversion.isDeleted = json['isDeleted'] ?? false;
    conversion.syncId = json['syncId'];
    return conversion;
  }
}

// Predefined conversion units
class ConversionUnits {
  static const Map<ConversionCategory, List<ConversionUnit>> units = {
    ConversionCategory.length: [
      ConversionUnit(name: 'Millimeter', symbol: 'mm', category: 'length', factor: 0.001),
      ConversionUnit(name: 'Centimeter', symbol: 'cm', category: 'length', factor: 0.01),
      ConversionUnit(name: 'Meter', symbol: 'm', category: 'length', factor: 1.0),
      ConversionUnit(name: 'Kilometer', symbol: 'km', category: 'length', factor: 1000.0),
      ConversionUnit(name: 'Inch', symbol: 'in', category: 'length', factor: 0.0254),
      ConversionUnit(name: 'Foot', symbol: 'ft', category: 'length', factor: 0.3048),
      ConversionUnit(name: 'Yard', symbol: 'yd', category: 'length', factor: 0.9144),
      ConversionUnit(name: 'Mile', symbol: 'mi', category: 'length', factor: 1609.344),
    ],
    
    ConversionCategory.weight: [
      ConversionUnit(name: 'Milligram', symbol: 'mg', category: 'weight', factor: 0.000001),
      ConversionUnit(name: 'Gram', symbol: 'g', category: 'weight', factor: 0.001),
      ConversionUnit(name: 'Kilogram', symbol: 'kg', category: 'weight', factor: 1.0),
      ConversionUnit(name: 'Ton', symbol: 't', category: 'weight', factor: 1000.0),
      ConversionUnit(name: 'Ounce', symbol: 'oz', category: 'weight', factor: 0.0283495),
      ConversionUnit(name: 'Pound', symbol: 'lb', category: 'weight', factor: 0.453592),
      ConversionUnit(name: 'Stone', symbol: 'st', category: 'weight', factor: 6.35029),
    ],
    
    ConversionCategory.temperature: [
      ConversionUnit(name: 'Celsius', symbol: '°C', category: 'temperature', factor: 1.0),
      ConversionUnit(name: 'Fahrenheit', symbol: '°F', category: 'temperature', factor: 1.8, offset: 32.0),
      ConversionUnit(name: 'Kelvin', symbol: 'K', category: 'temperature', factor: 1.0, offset: 273.15),
    ],
    
    ConversionCategory.volume: [
      ConversionUnit(name: 'Milliliter', symbol: 'ml', category: 'volume', factor: 0.001),
      ConversionUnit(name: 'Liter', symbol: 'l', category: 'volume', factor: 1.0),
      ConversionUnit(name: 'Cubic Meter', symbol: 'm³', category: 'volume', factor: 1000.0),
      ConversionUnit(name: 'Fluid Ounce', symbol: 'fl oz', category: 'volume', factor: 0.0295735),
      ConversionUnit(name: 'Cup', symbol: 'cup', category: 'volume', factor: 0.236588),
      ConversionUnit(name: 'Pint', symbol: 'pt', category: 'volume', factor: 0.473176),
      ConversionUnit(name: 'Quart', symbol: 'qt', category: 'volume', factor: 0.946353),
      ConversionUnit(name: 'Gallon', symbol: 'gal', category: 'volume', factor: 3.78541),
    ],
  };

  static List<ConversionUnit> getUnitsForCategory(ConversionCategory category) {
    return units[category] ?? [];
  }

  static ConversionUnit? getUnit(ConversionCategory category, String symbol) {
    final categoryUnits = units[category] ?? [];
    try {
      return categoryUnits.firstWhere((unit) => unit.symbol == symbol);
    } catch (e) {
      return null;
    }
  }
}
