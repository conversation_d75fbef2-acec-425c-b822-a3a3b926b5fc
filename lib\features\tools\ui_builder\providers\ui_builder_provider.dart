import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';

/// UI Builder state
class UIBuilderState {
  final Map<String, UIComponent> components;
  final String? selectedComponentId;
  final LayoutMode layoutMode;
  final bool isPreviewMode;
  final double gridSize;
  final bool snapToGrid;
  final List<String> undoStack;
  final List<String> redoStack;
  final DateTime lastModified;

  const UIBuilderState({
    this.components = const {},
    this.selectedComponentId,
    this.layoutMode = LayoutMode.grid,
    this.isPreviewMode = false,
    this.gridSize = 8.0,
    this.snapToGrid = true,
    this.undoStack = const [],
    this.redoStack = const [],
    required this.lastModified,
  });

  /// Create initial state
  factory UIBuilderState.initial() {
    return UIBuilderState(
      lastModified: DateTime.now(),
    );
  }

  /// Copy with modifications
  U<PERSON>uilderState copyWith({
    Map<String, UIComponent>? components,
    String? selectedComponentId,
    LayoutMode? layoutMode,
    bool? isPreviewMode,
    double? gridSize,
    bool? snapToGrid,
    List<String>? undoStack,
    List<String>? redoStack,
    DateTime? lastModified,
  }) {
    return UIBuilderState(
      components: components ?? this.components,
      selectedComponentId: selectedComponentId ?? this.selectedComponentId,
      layoutMode: layoutMode ?? this.layoutMode,
      isPreviewMode: isPreviewMode ?? this.isPreviewMode,
      gridSize: gridSize ?? this.gridSize,
      snapToGrid: snapToGrid ?? this.snapToGrid,
      undoStack: undoStack ?? this.undoStack,
      redoStack: redoStack ?? this.redoStack,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// Get selected component
  UIComponent? get selectedComponent {
    if (selectedComponentId == null) return null;
    return components[selectedComponentId];
  }

  /// Check if has selection
  bool get hasSelection => selectedComponentId != null;

  /// Get component count
  int get componentCount => components.length;

  /// Check if can undo
  bool get canUndo => undoStack.isNotEmpty;

  /// Check if can redo
  bool get canRedo => redoStack.isNotEmpty;

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'components': components.map((key, value) => MapEntry(key, value.toJson())),
      'selectedComponentId': selectedComponentId,
      'layoutMode': layoutMode.name,
      'isPreviewMode': isPreviewMode,
      'gridSize': gridSize,
      'snapToGrid': snapToGrid,
      'undoStack': undoStack,
      'redoStack': redoStack,
      'lastModified': lastModified.toIso8601String(),
    };
  }

  /// Create from JSON
  factory UIBuilderState.fromJson(Map<String, dynamic> json) {
    final componentsJson = json['components'] as Map<String, dynamic>? ?? {};
    final components = componentsJson.map(
      (key, value) => MapEntry(key, UIComponent.fromJson(value as Map<String, dynamic>)),
    );

    return UIBuilderState(
      components: components,
      selectedComponentId: json['selectedComponentId'] as String?,
      layoutMode: LayoutMode.values.firstWhere(
        (e) => e.name == json['layoutMode'],
        orElse: () => LayoutMode.grid,
      ),
      isPreviewMode: json['isPreviewMode'] as bool? ?? false,
      gridSize: (json['gridSize'] as num?)?.toDouble() ?? 8.0,
      snapToGrid: json['snapToGrid'] as bool? ?? true,
      undoStack: List<String>.from(json['undoStack'] as List? ?? []),
      redoStack: List<String>.from(json['redoStack'] as List? ?? []),
      lastModified: DateTime.parse(json['lastModified'] as String),
    );
  }
}

/// UI Builder notifier
class UIBuilderNotifier extends StateNotifier<UIBuilderState> {
  UIBuilderNotifier() : super(UIBuilderState.initial());

  /// Add component to canvas
  void addComponent(UIComponent component, {Offset? position}) {
    final updatedComponent = position != null 
        ? component.copyWith(position: position)
        : component;
    
    final updatedComponents = Map<String, UIComponent>.from(state.components);
    updatedComponents[component.id] = updatedComponent;

    _saveStateToUndo();
    
    state = state.copyWith(
      components: updatedComponents,
      selectedComponentId: component.id,
      lastModified: DateTime.now(),
    );
  }

  /// Remove component
  void removeComponent(String componentId) {
    final updatedComponents = Map<String, UIComponent>.from(state.components);
    updatedComponents.remove(componentId);

    _saveStateToUndo();

    state = state.copyWith(
      components: updatedComponents,
      selectedComponentId: state.selectedComponentId == componentId ? null : state.selectedComponentId,
      lastModified: DateTime.now(),
    );
  }

  /// Update component
  void updateComponent(String componentId, UIComponent updatedComponent) {
    final updatedComponents = Map<String, UIComponent>.from(state.components);
    updatedComponents[componentId] = updatedComponent;

    state = state.copyWith(
      components: updatedComponents,
      lastModified: DateTime.now(),
    );
  }

  /// Move component
  void moveComponent(String componentId, Offset newPosition) {
    final component = state.components[componentId];
    if (component == null) return;

    final snappedPosition = state.snapToGrid 
        ? _snapToGrid(newPosition)
        : newPosition;

    final updatedComponent = component.copyWith(position: snappedPosition);
    updateComponent(componentId, updatedComponent);
  }

  /// Resize component
  void resizeComponent(String componentId, Size newSize) {
    final component = state.components[componentId];
    if (component == null) return;

    final snappedSize = state.snapToGrid 
        ? _snapSizeToGrid(newSize)
        : newSize;

    final updatedComponent = component.copyWith(size: snappedSize);
    updateComponent(componentId, updatedComponent);
  }

  /// Select component
  void selectComponent(String? componentId) {
    state = state.copyWith(
      selectedComponentId: componentId,
      lastModified: DateTime.now(),
    );
  }

  /// Clear selection
  void clearSelection() {
    selectComponent(null);
  }

  /// Set layout mode
  void setLayoutMode(LayoutMode mode) {
    state = state.copyWith(
      layoutMode: mode,
      lastModified: DateTime.now(),
    );
  }

  /// Toggle preview mode
  void togglePreviewMode() {
    state = state.copyWith(
      isPreviewMode: !state.isPreviewMode,
      selectedComponentId: state.isPreviewMode ? state.selectedComponentId : null,
      lastModified: DateTime.now(),
    );
  }

  /// Set preview mode
  void setPreviewMode(bool isPreview) {
    state = state.copyWith(
      isPreviewMode: isPreview,
      selectedComponentId: isPreview ? null : state.selectedComponentId,
      lastModified: DateTime.now(),
    );
  }

  /// Set grid size
  void setGridSize(double size) {
    state = state.copyWith(
      gridSize: size,
      lastModified: DateTime.now(),
    );
  }

  /// Toggle snap to grid
  void toggleSnapToGrid() {
    state = state.copyWith(
      snapToGrid: !state.snapToGrid,
      lastModified: DateTime.now(),
    );
  }

  /// Duplicate component
  void duplicateComponent(String componentId) {
    final component = state.components[componentId];
    if (component == null) return;

    final duplicatedComponent = component.copyWith(
      id: '${component.type.name}_${DateTime.now().millisecondsSinceEpoch}',
      position: component.position + const Offset(20, 20),
      lastModified: DateTime.now(),
    );

    addComponent(duplicatedComponent);
  }

  /// Clear all components
  void clearAll() {
    _saveStateToUndo();
    
    state = state.copyWith(
      components: {},
      selectedComponentId: null,
      lastModified: DateTime.now(),
    );
  }

  /// Undo last action
  void undo() {
    if (!state.canUndo) return;

    final previousState = state.undoStack.last;
    final newUndoStack = List<String>.from(state.undoStack)..removeLast();
    final newRedoStack = List<String>.from(state.redoStack)..add(_serializeState());

    // Restore previous state
    final restoredState = UIBuilderState.fromJson(
      Map<String, dynamic>.from(
        // This would need proper JSON parsing implementation
        {} // Placeholder
      ),
    );

    state = restoredState.copyWith(
      undoStack: newUndoStack,
      redoStack: newRedoStack,
    );
  }

  /// Redo last undone action
  void redo() {
    if (!state.canRedo) return;

    final nextState = state.redoStack.last;
    final newRedoStack = List<String>.from(state.redoStack)..removeLast();
    final newUndoStack = List<String>.from(state.undoStack)..add(_serializeState());

    // Restore next state
    final restoredState = UIBuilderState.fromJson(
      Map<String, dynamic>.from(
        // This would need proper JSON parsing implementation
        {} // Placeholder
      ),
    );

    state = restoredState.copyWith(
      undoStack: newUndoStack,
      redoStack: newRedoStack,
    );
  }

  /// Save current state to undo stack
  void _saveStateToUndo() {
    final serializedState = _serializeState();
    final newUndoStack = List<String>.from(state.undoStack)..add(serializedState);
    
    // Limit undo stack size
    if (newUndoStack.length > 50) {
      newUndoStack.removeAt(0);
    }

    state = state.copyWith(
      undoStack: newUndoStack,
      redoStack: [], // Clear redo stack when new action is performed
    );
  }

  /// Serialize current state
  String _serializeState() {
    // This would need proper JSON serialization
    return state.toJson().toString();
  }

  /// Snap position to grid
  Offset _snapToGrid(Offset position) {
    final gridSize = state.gridSize;
    return Offset(
      (position.dx / gridSize).round() * gridSize,
      (position.dy / gridSize).round() * gridSize,
    );
  }

  /// Snap size to grid
  Size _snapSizeToGrid(Size size) {
    final gridSize = state.gridSize;
    return Size(
      (size.width / gridSize).round() * gridSize,
      (size.height / gridSize).round() * gridSize,
    );
  }
}

/// Provider for UI Builder
final uiBuilderProvider = StateNotifierProvider<UIBuilderNotifier, UIBuilderState>((ref) {
  return UIBuilderNotifier();
});
