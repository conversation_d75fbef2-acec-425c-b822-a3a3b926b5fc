import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/financial_goal.dart';

import '../providers/financial_goal_provider.dart';
import '../providers/account_provider.dart';

class FinancialGoalFormScreen extends ConsumerStatefulWidget {
  final FinancialGoal? goal;

  const FinancialGoalFormScreen({
    super.key,
    this.goal,
  });

  @override
  ConsumerState<FinancialGoalFormScreen> createState() => _FinancialGoalFormScreenState();
}

class _FinancialGoalFormScreenState extends ConsumerState<FinancialGoalFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _targetAmountController;
  late TextEditingController _currentAmountController;
  late TextEditingController _monthlyContributionController;
  
  GoalType _selectedType = GoalType.savings;
  DateTime _targetDate = DateTime.now().add(const Duration(days: 365));
  bool _autoContribute = false;
  int? _linkedAccountId;

  @override
  void initState() {
    super.initState();
    
    _nameController = TextEditingController();
    _descriptionController = TextEditingController();
    _targetAmountController = TextEditingController();
    _currentAmountController = TextEditingController(text: '0.00');
    _monthlyContributionController = TextEditingController(text: '0.00');
    
    if (widget.goal != null) {
      _loadGoalData();
    }
  }

  void _loadGoalData() {
    final goal = widget.goal!;
    _nameController.text = goal.name;
    _descriptionController.text = goal.description;
    _targetAmountController.text = goal.targetAmount.toString();
    _currentAmountController.text = goal.currentAmount.toString();
    _monthlyContributionController.text = goal.monthlyContribution.toString();
    _selectedType = goal.type;
    _targetDate = goal.targetDate;
    _autoContribute = goal.autoContribute;
    _linkedAccountId = goal.linkedAccountId;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _targetAmountController.dispose();
    _currentAmountController.dispose();
    _monthlyContributionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final accounts = ref.watch(activeAccountsProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.goal == null ? 'Add Financial Goal' : 'Edit Financial Goal'),
        actions: [
          if (widget.goal != null)
            IconButton(
              onPressed: _deleteGoal,
              icon: const Icon(Icons.delete),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Goal Type Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Goal Type',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      Wrap(
                        spacing: 8,
                        children: GoalType.values.map((type) {
                          final isSelected = _selectedType == type;
                          return FilterChip(
                            label: Text(_getGoalTypeDisplayName(type)),
                            selected: isSelected,
                            avatar: Icon(_getGoalTypeIcon(type)),
                            onSelected: (selected) {
                              setState(() {
                                _selectedType = type;
                                _updateGoalNameSuggestion();
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Basic Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Goal Details',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      // Goal Name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Goal Name',
                          border: OutlineInputBorder(),
                          hintText: 'e.g., Emergency Fund, Vacation',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a goal name';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description (optional)',
                          border: OutlineInputBorder(),
                          hintText: 'Brief description of your goal',
                        ),
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Financial Details
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Financial Details',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      // Target Amount
                      TextFormField(
                        controller: _targetAmountController,
                        decoration: const InputDecoration(
                          labelText: 'Target Amount',
                          prefixText: '\$',
                          border: OutlineInputBorder(),
                          hintText: 'Enter your target amount',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a target amount';
                          }
                          final amount = double.tryParse(value);
                          if (amount == null || amount <= 0) {
                            return 'Please enter a valid amount';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Current Amount
                      TextFormField(
                        controller: _currentAmountController,
                        decoration: const InputDecoration(
                          labelText: 'Current Amount',
                          prefixText: '\$',
                          border: OutlineInputBorder(),
                          hintText: 'Amount already saved',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter the current amount';
                          }
                          final amount = double.tryParse(value);
                          if (amount == null || amount < 0) {
                            return 'Please enter a valid amount';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Monthly Contribution
                      TextFormField(
                        controller: _monthlyContributionController,
                        decoration: const InputDecoration(
                          labelText: 'Monthly Contribution',
                          prefixText: '\$',
                          border: OutlineInputBorder(),
                          hintText: 'Amount to save each month',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a monthly contribution';
                          }
                          final amount = double.tryParse(value);
                          if (amount == null || amount < 0) {
                            return 'Please enter a valid amount';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Timeline and Settings
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Timeline & Settings',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      // Target Date
                      ListTile(
                        leading: const Icon(Icons.calendar_today),
                        title: const Text('Target Date'),
                        subtitle: Text('${_targetDate.day}/${_targetDate.month}/${_targetDate.year}'),
                        onTap: _selectTargetDate,
                        contentPadding: EdgeInsets.zero,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Linked Account
                      DropdownButtonFormField<int?>(
                        value: _linkedAccountId,
                        decoration: const InputDecoration(
                          labelText: 'Linked Account (optional)',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                            value: null,
                            child: Text('No linked account'),
                          ),
                          ...accounts.map((account) => DropdownMenuItem(
                            value: account.id,
                            child: Text('${account.name} (${account.formattedBalance})'),
                          )),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _linkedAccountId = value;
                          });
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Auto Contribute
                      SwitchListTile(
                        title: const Text('Auto Contribute'),
                        subtitle: const Text('Automatically contribute monthly amount'),
                        value: _autoContribute,
                        onChanged: (value) {
                          setState(() {
                            _autoContribute = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Progress Preview
              if (_targetAmountController.text.isNotEmpty && _currentAmountController.text.isNotEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Progress Preview',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),
                        
                        _buildProgressPreview(),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
              
              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveGoal,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      widget.goal == null ? 'Create Goal' : 'Update Goal',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressPreview() {
    final targetAmount = double.tryParse(_targetAmountController.text) ?? 0;
    final currentAmount = double.tryParse(_currentAmountController.text) ?? 0;
    final monthlyContribution = double.tryParse(_monthlyContributionController.text) ?? 0;
    
    if (targetAmount <= 0) return const SizedBox();
    
    final progress = (currentAmount / targetAmount).clamp(0.0, 1.0);
    final remainingAmount = targetAmount - currentAmount;
    final monthsToGoal = monthlyContribution > 0 ? (remainingAmount / monthlyContribution).ceil() : 0;
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('${(progress * 100).toStringAsFixed(1)}%'),
            Text('\$${currentAmount.toStringAsFixed(2)} of \$${targetAmount.toStringAsFixed(2)}'),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        ),
        const SizedBox(height: 16),
        
        if (monthlyContribution > 0) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Estimated completion:'),
              Text(
                monthsToGoal > 0 
                    ? '$monthsToGoal months'
                    : 'Goal achieved!',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: monthsToGoal > 0 ? null : Colors.green,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  String _getGoalTypeDisplayName(GoalType type) {
    switch (type) {
      case GoalType.savings:
        return 'Savings';
      case GoalType.debt:
        return 'Debt Payoff';
      case GoalType.investment:
        return 'Investment';
      case GoalType.purchase:
        return 'Purchase';
      case GoalType.emergency:
        return 'Emergency Fund';
    }
  }

  IconData _getGoalTypeIcon(GoalType type) {
    switch (type) {
      case GoalType.savings:
        return Icons.savings;
      case GoalType.debt:
        return Icons.money_off;
      case GoalType.investment:
        return Icons.trending_up;
      case GoalType.purchase:
        return Icons.shopping_cart;
      case GoalType.emergency:
        return Icons.security;
    }
  }

  void _updateGoalNameSuggestion() {
    if (_nameController.text.isEmpty) {
      switch (_selectedType) {
        case GoalType.savings:
          _nameController.text = 'Savings Goal';
          break;
        case GoalType.debt:
          _nameController.text = 'Debt Payoff';
          break;
        case GoalType.investment:
          _nameController.text = 'Investment Goal';
          break;
        case GoalType.purchase:
          _nameController.text = 'Purchase Goal';
          break;
        case GoalType.emergency:
          _nameController.text = 'Emergency Fund';
          break;
      }
    }
  }

  Future<void> _selectTargetDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _targetDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    
    if (date != null) {
      setState(() {
        _targetDate = date;
      });
    }
  }

  void _saveGoal() {
    if (!_formKey.currentState!.validate()) return;
    
    final name = _nameController.text.trim();
    final description = _descriptionController.text.trim();
    final targetAmount = double.parse(_targetAmountController.text);
    final currentAmount = double.parse(_currentAmountController.text);
    final monthlyContribution = double.parse(_monthlyContributionController.text);
    
    if (widget.goal == null) {
      // Create new goal
      final goal = FinancialGoal.create(
        name: name,
        type: _selectedType,
        targetAmount: targetAmount,
        targetDate: _targetDate,
        userId: '', // Will be set by provider
        description: description,
        currentAmount: currentAmount,
        monthlyContribution: monthlyContribution,
        autoContribute: _autoContribute,
        linkedAccountId: _linkedAccountId,
      );
      
      ref.read(financialGoalsProvider.notifier).addGoal(goal);
    } else {
      // Update existing goal
      widget.goal!.updateGoal(
        name: name,
        description: description,
        type: _selectedType,
        targetAmount: targetAmount,
        targetDate: _targetDate,
        monthlyContribution: monthlyContribution,
        autoContribute: _autoContribute,
        linkedAccountId: _linkedAccountId,
      );
      
      // Update current amount separately
      final amountDifference = currentAmount - widget.goal!.currentAmount;
      if (amountDifference != 0) {
        if (amountDifference > 0) {
          widget.goal!.addContribution(amountDifference);
        } else {
          widget.goal!.removeContribution(-amountDifference);
        }
      }
      
      ref.read(financialGoalsProvider.notifier).updateGoal(widget.goal!);
    }
    
    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.goal == null 
              ? 'Financial goal created successfully' 
              : 'Financial goal updated successfully',
        ),
      ),
    );
  }

  void _deleteGoal() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Goal'),
        content: const Text('Are you sure you want to delete this financial goal?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(financialGoalsProvider.notifier).deleteGoal(widget.goal!.id);
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close form
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Financial goal deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
