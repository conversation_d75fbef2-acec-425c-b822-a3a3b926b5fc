import 'package:flutter/material.dart';
import '../models/quran_models.dart';

/// Widget for searching Quran verses
class SearchWidget extends StatefulWidget {
  final List<QuranSearchResult> searchResults;
  final String searchQuery;
  final Function(String query) onSearch;
  final VoidCallback onClearSearch;
  final Function(QuranVerse verse) onVerseSelected;

  const SearchWidget({
    super.key,
    required this.searchResults,
    required this.searchQuery,
    required this.onSearch,
    required this.onClearSearch,
    required this.onVerseSelected,
  });

  @override
  State<SearchWidget> createState() => _SearchWidgetState();
}

class _SearchWidgetState extends State<SearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.searchQuery;
  }

  @override
  void didUpdateWidget(SearchWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _searchController.text = widget.searchQuery;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search bar
        _buildSearchBar(),
        
        // Search results
        Expanded(child: _buildSearchResults()),
      ],
    );
  }

  /// Build search bar
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search input
          TextField(
            controller: _searchController,
            focusNode: _searchFocusNode,
            decoration: InputDecoration(
              hintText: 'Search in Quran...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        widget.onClearSearch();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
            onSubmitted: (query) => widget.onSearch(query.trim()),
            textInputAction: TextInputAction.search,
          ),
          
          const SizedBox(height: 12),
          
          // Search button and results count
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () => widget.onSearch(_searchController.text.trim()),
                icon: const Icon(Icons.search),
                label: const Text('Search'),
              ),
              
              const SizedBox(width: 16),
              
              if (widget.searchResults.isNotEmpty)
                Text(
                  '${widget.searchResults.length} result${widget.searchResults.length == 1 ? '' : 's'} found',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
            ],
          ),
          
          // Search tips
          if (widget.searchQuery.isEmpty) ...[
            const SizedBox(height: 16),
            _buildSearchTips(),
          ],
        ],
      ),
    );
  }

  /// Build search tips
  Widget _buildSearchTips() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Search Tips',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• Search in Arabic text for exact matches\n'
            '• Use keywords to find relevant verses\n'
            '• Search is case-insensitive\n'
            '• Results are sorted by relevance',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build search results
  Widget _buildSearchResults() {
    if (widget.searchQuery.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Search the Quran',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Enter Arabic text or keywords to find verses',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (widget.searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try different search terms or check your spelling',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                _searchController.clear();
                widget.onClearSearch();
              },
              icon: const Icon(Icons.clear),
              label: const Text('Clear Search'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: widget.searchResults.length,
      itemBuilder: (context, index) {
        final result = widget.searchResults[index];
        return _buildSearchResultCard(result);
      },
    );
  }

  /// Build search result card
  Widget _buildSearchResultCard(QuranSearchResult result) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: InkWell(
        onTap: () => widget.onVerseSelected(result.verse),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Verse reference and relevance
              Row(
                children: [
                  // Verse address
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      result.verse.address,
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Relevance score
                  if (result.relevanceScore > 1.0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondaryContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star,
                            size: 12,
                            color: Theme.of(context).colorScheme.secondary,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            'Relevant',
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: Theme.of(context).colorScheme.secondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Arabic text with highlighting
              RichText(
                text: _buildHighlightedText(result),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
              
              const SizedBox(height: 8),
              
              // Matched text info
              if (result.matchedText.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.highlight,
                        size: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Match: "${result.matchedText}"',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build highlighted text with search matches
  TextSpan _buildHighlightedText(QuranSearchResult result) {
    final text = result.verse.arabicText;
    final matchStart = result.matchStart;
    final matchEnd = result.matchEnd;
    
    if (matchStart < 0 || matchEnd > text.length || matchStart >= matchEnd) {
      // No valid match, return normal text
      return TextSpan(
        text: text,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontFamily: 'Amiri',
          height: 1.8,
        ),
      );
    }
    
    final beforeMatch = text.substring(0, matchStart);
    final match = text.substring(matchStart, matchEnd);
    final afterMatch = text.substring(matchEnd);
    
    return TextSpan(
      children: [
        if (beforeMatch.isNotEmpty)
          TextSpan(
            text: beforeMatch,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontFamily: 'Amiri',
              height: 1.8,
            ),
          ),
        TextSpan(
          text: match,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontFamily: 'Amiri',
            height: 1.8,
            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (afterMatch.isNotEmpty)
          TextSpan(
            text: afterMatch,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontFamily: 'Amiri',
              height: 1.8,
            ),
          ),
      ],
    );
  }
}
