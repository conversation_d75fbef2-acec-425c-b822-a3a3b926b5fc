import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling tool security features
class ToolSecurityService {
  static final ToolSecurityService _instance = ToolSecurityService._internal();
  factory ToolSecurityService() => _instance;
  ToolSecurityService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  static const String _passwordPrefix = 'tool_password_';
  static const String _biometricPrefix = 'tool_biometric_';
  static const String _encryptionPrefix = 'tool_encrypted_';

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.isDeviceSupported();
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  /// Set password protection for a tool
  Future<bool> setToolPassword(int toolId, String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hashedPassword = _hashPassword(password);
      return await prefs.setString('$_passwordPrefix$toolId', hashedPassword);
    } catch (e) {
      return false;
    }
  }

  /// Verify tool password
  Future<bool> verifyToolPassword(int toolId, String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedHash = prefs.getString('$_passwordPrefix$toolId');
      if (storedHash == null) return false;
      
      final hashedPassword = _hashPassword(password);
      return storedHash == hashedPassword;
    } catch (e) {
      return false;
    }
  }

  /// Enable biometric authentication for a tool
  Future<bool> enableBiometricAuth(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool('$_biometricPrefix$toolId', true);
    } catch (e) {
      return false;
    }
  }

  /// Disable biometric authentication for a tool
  Future<bool> disableBiometricAuth(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove('$_biometricPrefix$toolId');
    } catch (e) {
      return false;
    }
  }

  /// Check if biometric authentication is enabled for a tool
  Future<bool> isBiometricEnabled(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('$_biometricPrefix$toolId') ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Authenticate with biometrics
  Future<bool> authenticateWithBiometrics({
    String reason = 'Please authenticate to access this tool',
  }) async {
    try {
      final isAvailable = await isBiometricAvailable();
      if (!isAvailable) return false;

      return await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: true,
        ),
      );
    } catch (e) {
      return false;
    }
  }

  /// Check if tool has any security protection
  Future<bool> isToolProtected(int toolId) async {
    final hasPassword = await hasToolPassword(toolId);
    final hasBiometric = await isBiometricEnabled(toolId);
    return hasPassword || hasBiometric;
  }

  /// Check if tool has password protection
  Future<bool> hasToolPassword(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey('$_passwordPrefix$toolId');
    } catch (e) {
      return false;
    }
  }

  /// Remove password protection from a tool
  Future<bool> removeToolPassword(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove('$_passwordPrefix$toolId');
    } catch (e) {
      return false;
    }
  }

  /// Authenticate user for tool access
  Future<bool> authenticateForTool(int toolId, {String? password}) async {
    final hasPassword = await hasToolPassword(toolId);
    final hasBiometric = await isBiometricEnabled(toolId);

    if (!hasPassword && !hasBiometric) {
      return true; // No protection enabled
    }

    // Try biometric first if enabled
    if (hasBiometric) {
      final biometricResult = await authenticateWithBiometrics(
        reason: 'Authenticate to access this tool',
      );
      if (biometricResult) return true;
    }

    // Try password if provided and enabled
    if (hasPassword && password != null) {
      return await verifyToolPassword(toolId, password);
    }

    return false;
  }

  /// Encrypt tool data
  Future<String?> encryptToolData(int toolId, String data) async {
    try {
      // Simple base64 encoding for now - in production, use proper encryption
      final bytes = utf8.encode(data);
      final encoded = base64.encode(bytes);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('$_encryptionPrefix$toolId', encoded);
      
      return encoded;
    } catch (e) {
      return null;
    }
  }

  /// Decrypt tool data
  Future<String?> decryptToolData(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encoded = prefs.getString('$_encryptionPrefix$toolId');
      if (encoded == null) return null;
      
      final bytes = base64.decode(encoded);
      return utf8.decode(bytes);
    } catch (e) {
      return null;
    }
  }

  /// Get security status for a tool
  Future<ToolSecurityStatus> getToolSecurityStatus(int toolId) async {
    final hasPassword = await hasToolPassword(toolId);
    final hasBiometric = await isBiometricEnabled(toolId);
    final biometricAvailable = await isBiometricAvailable();
    
    return ToolSecurityStatus(
      toolId: toolId,
      hasPasswordProtection: hasPassword,
      hasBiometricProtection: hasBiometric,
      biometricAvailable: biometricAvailable,
      isProtected: hasPassword || hasBiometric,
    );
  }

  /// Hash password using SHA-256
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Clear all security data for a tool
  Future<bool> clearToolSecurity(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_passwordPrefix$toolId');
      await prefs.remove('$_biometricPrefix$toolId');
      await prefs.remove('$_encryptionPrefix$toolId');
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Tool security status data class
class ToolSecurityStatus {
  final int toolId;
  final bool hasPasswordProtection;
  final bool hasBiometricProtection;
  final bool biometricAvailable;
  final bool isProtected;

  const ToolSecurityStatus({
    required this.toolId,
    required this.hasPasswordProtection,
    required this.hasBiometricProtection,
    required this.biometricAvailable,
    required this.isProtected,
  });

  Map<String, dynamic> toJson() {
    return {
      'toolId': toolId,
      'hasPasswordProtection': hasPasswordProtection,
      'hasBiometricProtection': hasBiometricProtection,
      'biometricAvailable': biometricAvailable,
      'isProtected': isProtected,
    };
  }

  factory ToolSecurityStatus.fromJson(Map<String, dynamic> json) {
    return ToolSecurityStatus(
      toolId: json['toolId'],
      hasPasswordProtection: json['hasPasswordProtection'],
      hasBiometricProtection: json['hasBiometricProtection'],
      biometricAvailable: json['biometricAvailable'],
      isProtected: json['isProtected'],
    );
  }
}
