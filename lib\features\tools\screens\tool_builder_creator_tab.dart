import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/tool_creation_provider.dart';
import 'tool_creation_pages/create_page.dart';
import 'tool_creation_pages/backend_page.dart';
import 'tool_creation_pages/ui_builder_page.dart';
import 'tool_creation_pages/preview_save_page.dart';

/// Tools Builder Creator tab with integrated 4-page workflow
class ToolBuilderCreatorTab extends ConsumerStatefulWidget {
  const ToolBuilderCreatorTab({super.key});

  @override
  ConsumerState<ToolBuilderCreatorTab> createState() => _ToolBuilderCreatorTabState();
}

class _ToolBuilderCreatorTabState extends ConsumerState<ToolBuilderCreatorTab> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<String> _pageNames = [
    'Create',
    'Backend',
    'UI Builder',
    'Preview & Save',
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final creationState = ref.watch(toolCreationProvider);
    
    return Column(
      children: [
        // Progress Header
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            children: [
              // Progress Indicator
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: (_currentPage + 1) / 4,
                      backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    '${_currentPage + 1} of 4',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // Page Title and Navigation
              Row(
                children: [
                  // Previous Button
                  if (_currentPage > 0)
                    IconButton(
                      onPressed: _previousPage,
                      icon: const Icon(Icons.arrow_back),
                      tooltip: 'Previous',
                    )
                  else
                    const SizedBox(width: 48),
                  
                  // Page Title
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          _pageNames[_currentPage],
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getPageDescription(_currentPage),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  
                  // Next Button
                  if (_currentPage < 3)
                    IconButton(
                      onPressed: _canProceedToNext() ? _nextPage : null,
                      icon: const Icon(Icons.arrow_forward),
                      tooltip: 'Next',
                    )
                  else
                    const SizedBox(width: 48),
                ],
              ),
              
              // Step Indicators
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(4, (index) {
                  final isActive = index == _currentPage;
                  final isCompleted = index < _currentPage;
                  
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: isCompleted
                                ? Theme.of(context).colorScheme.primary
                                : isActive
                                    ? Theme.of(context).colorScheme.primaryContainer
                                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Center(
                            child: isCompleted
                                ? Icon(
                                    Icons.check,
                                    size: 16,
                                    color: Theme.of(context).colorScheme.onPrimary,
                                  )
                                : Text(
                                    '${index + 1}',
                                    style: TextStyle(
                                      color: isActive
                                          ? Theme.of(context).colorScheme.onPrimaryContainer
                                          : Theme.of(context).colorScheme.onSurfaceVariant,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                          ),
                        ),
                        if (index < 3)
                          Container(
                            width: 24,
                            height: 2,
                            color: isCompleted
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                          ),
                      ],
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
        
        // Page Content
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (page) {
              setState(() {
                _currentPage = page;
              });
            },
            children: const [
              CreatePage(),
              BackendPage(),
              UIBuilderPage(),
              PreviewSavePage(),
            ],
          ),
        ),
        
        // Bottom Navigation
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              // Cancel/Reset Button
              OutlinedButton(
                onPressed: _showCancelDialog,
                child: const Text('Cancel'),
              ),
              
              const Spacer(),
              
              // Previous Button
              if (_currentPage > 0)
                OutlinedButton(
                  onPressed: _previousPage,
                  child: const Text('Previous'),
                ),
              
              if (_currentPage > 0) const SizedBox(width: 12),
              
              // Next/Save Button
              ElevatedButton(
                onPressed: _canProceedToNext() ? _nextPage : null,
                child: Text(_currentPage == 3 ? 'Save Tool' : 'Next'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getPageDescription(int page) {
    switch (page) {
      case 0:
        return 'Choose how to start your tool';
      case 1:
        return 'Build spreadsheet logic with formulas';
      case 2:
        return 'Design the user interface';
      case 3:
        return 'Test and save your tool';
      default:
        return '';
    }
  }

  bool _canProceedToNext() {
    final creationState = ref.read(toolCreationProvider);
    
    switch (_currentPage) {
      case 0:
        return creationState.toolName.isNotEmpty;
      case 1:
        return creationState.backendData.isNotEmpty;
      case 2:
        return creationState.uiComponents.isNotEmpty;
      case 3:
        return true;
      default:
        return false;
    }
  }

  void _nextPage() {
    if (_currentPage < 3) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _saveTool();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _saveTool() {
    ref.read(toolCreationProvider.notifier).saveTool();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tool saved successfully!'),
        backgroundColor: Colors.green,
      ),
    );
    
    // Reset to first page
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    
    // Reset creation state
    ref.read(toolCreationProvider.notifier).reset();
  }

  void _showCancelDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Tool Creation'),
        content: const Text(
          'Are you sure you want to cancel? All progress will be lost.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(toolCreationProvider.notifier).reset();
              _pageController.animateToPage(
                0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
