import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quran_models.dart';
import '../providers/quran_provider.dart';

/// Widget for reading Quran verses with proper Arabic text rendering
class VerseReaderWidget extends ConsumerStatefulWidget {
  final QuranSurah surah;

  const VerseReaderWidget({
    super.key,
    required this.surah,
  });

  @override
  ConsumerState<VerseReaderWidget> createState() => _VerseReaderWidgetState();
}

class _VerseReaderWidgetState extends ConsumerState<VerseReaderWidget> {
  final ScrollController _scrollController = ScrollController();
  int? _selectedVerseNumber;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quranState = ref.watch(quranProvider);
    final preferences = quranState.preferences;
    
    return Column(
      children: [
        // Surah header
        _buildSurahHeader(),
        
        // Verse list
        Expanded(
          child: _buildVerseList(preferences),
        ),
        
        // Navigation controls
        _buildNavigationControls(),
      ],
    );
  }

  /// Build Surah header with Bismillah
  Widget _buildSurahHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primaryContainer,
            Theme.of(context).colorScheme.secondaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Surah name in Arabic
          Text(
            'سورة ${widget.surah.arabicName}',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontFamily: 'Amiri',
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // English name and info
          Text(
            widget.surah.englishName,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            '${widget.surah.verseCount} verses • ${widget.surah.revelationType}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
          
          // Bismillah (except for Surah At-Tawbah)
          if (widget.surah.number != 9) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontFamily: 'Amiri',
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build verse list
  Widget _buildVerseList(QuranPreferences preferences) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: widget.surah.verses.length,
      itemBuilder: (context, index) {
        final verse = widget.surah.verses[index];
        return _buildVerseCard(verse, preferences);
      },
    );
  }

  /// Build individual verse card
  Widget _buildVerseCard(QuranVerse verse, QuranPreferences preferences) {
    final quranState = ref.watch(quranProvider);
    final isBookmarked = quranState.isVerseBookmarked(verse);
    final isSelected = _selectedVerseNumber == verse.verseNumber;
    final isCurrent = quranState.currentVerseNumber == verse.verseNumber;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isCurrent 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: isCurrent ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedVerseNumber = isSelected ? null : verse.verseNumber;
          });
          ref.read(quranProvider.notifier).navigateToVerse(
            verse.surahNumber,
            verse.verseNumber,
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Verse header
              Row(
                children: [
                  // Verse number
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isBookmarked 
                          ? Theme.of(context).colorScheme.primaryContainer
                          : Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      verse.verseNumber.toString(),
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isBookmarked 
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Bookmark button
                  IconButton(
                    onPressed: () => _toggleBookmark(verse),
                    icon: Icon(
                      isBookmarked ? Icons.bookmark : Icons.bookmark_outline,
                      color: isBookmarked 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    tooltip: isBookmarked ? 'Remove Bookmark' : 'Add Bookmark',
                  ),
                  
                  // More options
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    onSelected: (value) => _handleVerseAction(value, verse),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'copy',
                        child: ListTile(
                          leading: Icon(Icons.copy),
                          title: Text('Copy Verse'),
                          dense: true,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: ListTile(
                          leading: Icon(Icons.share),
                          title: Text('Share Verse'),
                          dense: true,
                        ),
                      ),
                      if (isBookmarked)
                        const PopupMenuItem(
                          value: 'note',
                          child: ListTile(
                            leading: Icon(Icons.note_add),
                            title: Text('Add Note'),
                            dense: true,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Arabic text
              Text(
                verse.arabicText,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontFamily: preferences.fontFamily,
                  fontSize: preferences.fontSize,
                  height: preferences.lineSpacing,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
              
              // Translation (if enabled)
              if (preferences.showTranslation && verse.translation != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    verse.translation!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
              
              // Bookmark note (if exists)
              if (isBookmarked) ...[
                const SizedBox(height: 8),
                _buildBookmarkNote(verse),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build bookmark note
  Widget _buildBookmarkNote(QuranVerse verse) {
    final quranState = ref.watch(quranProvider);
    final bookmark = quranState.getBookmarkForVerse(verse);
    
    if (bookmark?.note == null || bookmark!.note!.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.note,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              bookmark.note!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build navigation controls
  Widget _buildNavigationControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Previous Surah
          ElevatedButton.icon(
            onPressed: widget.surah.number > 1 ? () => _navigateToSurah(widget.surah.number - 1) : null,
            icon: const Icon(Icons.skip_previous),
            label: const Text('Previous'),
          ),
          
          // Scroll to top
          ElevatedButton.icon(
            onPressed: () => _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            ),
            icon: const Icon(Icons.vertical_align_top),
            label: const Text('Top'),
          ),
          
          // Next Surah
          ElevatedButton.icon(
            onPressed: widget.surah.number < 114 ? () => _navigateToSurah(widget.surah.number + 1) : null,
            icon: const Icon(Icons.skip_next),
            label: const Text('Next'),
          ),
        ],
      ),
    );
  }

  /// Toggle bookmark for verse
  void _toggleBookmark(QuranVerse verse) {
    ref.read(quranProvider.notifier).toggleBookmark(verse);
  }

  /// Handle verse actions
  void _handleVerseAction(String action, QuranVerse verse) {
    switch (action) {
      case 'copy':
        // TODO: Implement copy to clipboard
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Verse copied to clipboard')),
        );
        break;
      case 'share':
        // TODO: Implement share functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Share functionality coming soon')),
        );
        break;
      case 'note':
        _showAddNoteDialog(verse);
        break;
    }
  }

  /// Show add note dialog
  void _showAddNoteDialog(QuranVerse verse) {
    final TextEditingController noteController = TextEditingController();
    final bookmark = ref.read(quranProvider).getBookmarkForVerse(verse);
    if (bookmark?.note != null) {
      noteController.text = bookmark!.note!;
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Note for ${verse.address}'),
        content: TextField(
          controller: noteController,
          decoration: const InputDecoration(
            hintText: 'Enter your note...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (bookmark != null) {
                ref.read(quranProvider.notifier).updateBookmark(
                  bookmark.id,
                  note: noteController.text.trim(),
                );
              }
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// Navigate to different Surah
  void _navigateToSurah(int surahNumber) {
    ref.read(quranProvider.notifier).navigateToSurah(surahNumber);
  }
}
