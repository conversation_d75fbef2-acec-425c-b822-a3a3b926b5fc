import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/ui_component.dart';
import 'color_picker_widget.dart';

/// Comprehensive properties panel for UI components
class ComponentPropertiesPanel extends StatefulWidget {
  final UIComponent? selectedComponent;
  final Function(UIComponent) onComponentUpdated;
  final VoidCallback? onDuplicateComponent;
  final VoidCallback? onDeleteComponent;

  const ComponentPropertiesPanel({
    super.key,
    this.selectedComponent,
    required this.onComponentUpdated,
    this.onDuplicateComponent,
    this.onDeleteComponent,
  });

  @override
  State<ComponentPropertiesPanel> createState() => _ComponentPropertiesPanelState();
}

class _ComponentPropertiesPanelState extends State<ComponentPropertiesPanel> {
  late TextEditingController _labelController;
  late TextEditingController _widthController;
  late TextEditingController _heightController;
  late TextEditingController _xController;
  late TextEditingController _yController;
  late TextEditingController _paddingController;
  late TextEditingController _marginController;
  late TextEditingController _borderWidthController;
  late TextEditingController _borderRadiusController;
  late TextEditingController _fontSizeController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(ComponentPropertiesPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedComponent != oldWidget.selectedComponent) {
      _updateControllers();
    }
  }

  void _initializeControllers() {
    _labelController = TextEditingController();
    _widthController = TextEditingController();
    _heightController = TextEditingController();
    _xController = TextEditingController();
    _yController = TextEditingController();
    _paddingController = TextEditingController();
    _marginController = TextEditingController();
    _borderWidthController = TextEditingController();
    _borderRadiusController = TextEditingController();
    _fontSizeController = TextEditingController();
    _updateControllers();
  }

  void _updateControllers() {
    final component = widget.selectedComponent;
    if (component != null) {
      _labelController.text = component.label;
      _widthController.text = component.position.width.toString();
      _heightController.text = component.position.height.toString();
      _xController.text = component.position.x.toString();
      _yController.text = component.position.y.toString();
      _paddingController.text = component.style.padding?.toString() ?? '';
      _marginController.text = component.style.margin?.toString() ?? '';
      _borderWidthController.text = component.style.borderWidth?.toString() ?? '';
      _borderRadiusController.text = component.style.borderRadius?.toString() ?? '';
      _fontSizeController.text = component.style.fontSize ?? '';
    }
  }

  @override
  void dispose() {
    _labelController.dispose();
    _widthController.dispose();
    _heightController.dispose();
    _xController.dispose();
    _yController.dispose();
    _paddingController.dispose();
    _marginController.dispose();
    _borderWidthController.dispose();
    _borderRadiusController.dispose();
    _fontSizeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.selectedComponent == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'Select a component to edit its properties',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Component Properties'),
            _buildBasicProperties(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Position & Size'),
            _buildPositionProperties(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Styling'),
            _buildStyleProperties(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Colors'),
            _buildColorProperties(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Typography'),
            _buildTypographyProperties(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Behavior'),
            _buildBehaviorProperties(),
            const SizedBox(height: 24),

            _buildSectionHeader('Actions'),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildBasicProperties() {
    return Column(
      children: [
        _buildTextField(
          label: 'Label',
          controller: _labelController,
          onChanged: (value) => _updateComponent(
            widget.selectedComponent!.copyWith(label: value),
          ),
        ),
        const SizedBox(height: 12),
        _buildDropdown(
          label: 'Component Type',
          value: widget.selectedComponent!.type.name,
          items: ComponentType.values.map((e) => e.name).toList(),
          onChanged: (value) {
            final newType = ComponentType.values.firstWhere((e) => e.name == value);
            _updateComponent(widget.selectedComponent!.copyWith(type: newType));
          },
        ),
      ],
    );
  }

  Widget _buildPositionProperties() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                label: 'X Position',
                controller: _xController,
                onChanged: (value) => _updatePosition(x: value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNumberField(
                label: 'Y Position',
                controller: _yController,
                onChanged: (value) => _updatePosition(y: value),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                label: 'Width',
                controller: _widthController,
                onChanged: (value) => _updatePosition(width: value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNumberField(
                label: 'Height',
                controller: _heightController,
                onChanged: (value) => _updatePosition(height: value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStyleProperties() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                label: 'Padding',
                controller: _paddingController,
                onChanged: (value) => _updateStyle(padding: value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNumberField(
                label: 'Margin',
                controller: _marginController,
                onChanged: (value) => _updateStyle(margin: value),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                label: 'Border Width',
                controller: _borderWidthController,
                onChanged: (value) => _updateStyle(borderWidth: value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNumberField(
                label: 'Border Radius',
                controller: _borderRadiusController,
                onChanged: (value) => _updateStyle(borderRadius: value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildColorProperties() {
    return Column(
      children: [
        _buildColorPicker(
          label: 'Background Color',
          currentColor: widget.selectedComponent!.style.backgroundColor,
          onColorChanged: (color) => _updateStyle(backgroundColor: color),
        ),
        const SizedBox(height: 12),
        _buildColorPicker(
          label: 'Text Color',
          currentColor: widget.selectedComponent!.style.textColor,
          onColorChanged: (color) => _updateStyle(textColor: color),
        ),
        const SizedBox(height: 12),
        _buildColorPicker(
          label: 'Border Color',
          currentColor: widget.selectedComponent!.style.borderColor,
          onColorChanged: (color) => _updateStyle(borderColor: color),
        ),
      ],
    );
  }

  Widget _buildTypographyProperties() {
    return Column(
      children: [
        _buildTextField(
          label: 'Font Size',
          controller: _fontSizeController,
          onChanged: (value) => _updateStyle(fontSize: value),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildCheckbox(
                label: 'Bold',
                value: widget.selectedComponent!.style.bold,
                onChanged: (value) => _updateStyle(bold: value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildCheckbox(
                label: 'Italic',
                value: widget.selectedComponent!.style.italic,
                onChanged: (value) => _updateStyle(italic: value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBehaviorProperties() {
    return Column(
      children: [
        _buildCheckbox(
          label: 'Enabled',
          value: widget.selectedComponent!.behavior.isEnabled,
          onChanged: (value) => _updateBehavior(isEnabled: value),
        ),
        const SizedBox(height: 8),
        _buildCheckbox(
          label: 'Visible',
          value: widget.selectedComponent!.behavior.isVisible,
          onChanged: (value) => _updateBehavior(isVisible: value),
        ),
        const SizedBox(height: 8),
        _buildCheckbox(
          label: 'Draggable',
          value: widget.selectedComponent!.behavior.isDraggable,
          onChanged: (value) => _updateBehavior(isDraggable: value),
        ),
        const SizedBox(height: 8),
        _buildCheckbox(
          label: 'Resizable',
          value: widget.selectedComponent!.behavior.isResizable,
          onChanged: (value) => _updateBehavior(isResizable: value),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: widget.onDuplicateComponent,
            icon: const Icon(Icons.copy),
            label: const Text('Duplicate'),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: widget.onDeleteComponent,
            icon: const Icon(Icons.delete),
            label: const Text('Delete'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
          ),
        ),
      ],
    );
  }

  // Helper methods for building UI elements
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required Function(String) onChanged,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      onChanged: onChanged,
    );
  }

  Widget _buildNumberField({
    required String label,
    required TextEditingController controller,
    required Function(double?) onChanged,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
      onChanged: (value) => onChanged(double.tryParse(value)),
    );
  }

  Widget _buildDropdown({
    required String label,
    required String value,
    required List<String> items,
    required Function(String) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: items.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
      onChanged: (newValue) => onChanged(newValue!),
    );
  }

  Widget _buildCheckbox({
    required String label,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return CheckboxListTile(
      title: Text(label),
      value: value,
      onChanged: (newValue) => onChanged(newValue!),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildColorPicker({
    required String label,
    required String? currentColor,
    required Function(String) onColorChanged,
  }) {
    return ColorPickerWidget(
      label: label,
      currentColor: currentColor,
      onColorChanged: onColorChanged,
    );
  }

  // Update methods
  void _updateComponent(UIComponent component) {
    widget.onComponentUpdated(component);
  }

  void _updatePosition({double? x, double? y, double? width, double? height}) {
    final currentPosition = widget.selectedComponent!.position;
    final newPosition = currentPosition.copyWith(
      x: x ?? currentPosition.x,
      y: y ?? currentPosition.y,
      width: width ?? currentPosition.width,
      height: height ?? currentPosition.height,
    );
    _updateComponent(widget.selectedComponent!.copyWith(position: newPosition));
  }

  void _updateStyle({
    String? backgroundColor,
    String? textColor,
    String? borderColor,
    double? borderWidth,
    double? borderRadius,
    String? fontSize,
    bool? bold,
    bool? italic,
    double? padding,
    double? margin,
  }) {
    final currentStyle = widget.selectedComponent!.style;
    final newStyle = currentStyle.copyWith(
      backgroundColor: backgroundColor ?? currentStyle.backgroundColor,
      textColor: textColor ?? currentStyle.textColor,
      borderColor: borderColor ?? currentStyle.borderColor,
      borderWidth: borderWidth ?? currentStyle.borderWidth,
      borderRadius: borderRadius ?? currentStyle.borderRadius,
      fontSize: fontSize ?? currentStyle.fontSize,
      bold: bold ?? currentStyle.bold,
      italic: italic ?? currentStyle.italic,
      padding: padding ?? currentStyle.padding,
      margin: margin ?? currentStyle.margin,
    );
    _updateComponent(widget.selectedComponent!.copyWith(style: newStyle));
  }

  void _updateBehavior({
    bool? isEnabled,
    bool? isVisible,
    bool? isDraggable,
    bool? isResizable,
  }) {
    final currentBehavior = widget.selectedComponent!.behavior;
    final newBehavior = currentBehavior.copyWith(
      isEnabled: isEnabled ?? currentBehavior.isEnabled,
      isVisible: isVisible ?? currentBehavior.isVisible,
      isDraggable: isDraggable ?? currentBehavior.isDraggable,
      isResizable: isResizable ?? currentBehavior.isResizable,
    );
    _updateComponent(widget.selectedComponent!.copyWith(behavior: newBehavior));
  }
}
