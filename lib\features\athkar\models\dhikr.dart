class Dhikr {
  int id = 0;
  String arabicText = '';
  String transliteration = '';
  String translation = '';
  String? reference; // Quran/Hadith reference
  int recommendedCount = 33;
  String category = 'general'; // morning, evening, general, prayer, etc.
  bool isCustom = false;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Dhikr();

  Dhikr.create({
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.userId,
    this.reference,
    this.recommendedCount = 33,
    this.category = 'general',
    this.isCustom = false,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void updateDhikr({
    String? arabicText,
    String? transliteration,
    String? translation,
    String? reference,
    int? recommendedCount,
    String? category,
  }) {
    if (arabicText != null) this.arabicText = arabicText;
    if (transliteration != null) this.transliteration = transliteration;
    if (translation != null) this.translation = translation;
    if (reference != null) this.reference = reference;
    if (recommendedCount != null) this.recommendedCount = recommendedCount;
    if (category != null) this.category = category;
    
    updateTimestamp();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'reference': reference,
      'recommendedCount': recommendedCount,
      'category': category,
      'isCustom': isCustom,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Dhikr.fromJson(Map<String, dynamic> json) {
    final dhikr = Dhikr();
    dhikr.id = json['id'] ?? 0;
    dhikr.arabicText = json['arabicText'] ?? '';
    dhikr.transliteration = json['transliteration'] ?? '';
    dhikr.translation = json['translation'] ?? '';
    dhikr.reference = json['reference'];
    dhikr.recommendedCount = json['recommendedCount'] ?? 33;
    dhikr.category = json['category'] ?? 'general';
    dhikr.isCustom = json['isCustom'] ?? false;
    dhikr.userId = json['userId'] ?? '';
    dhikr.createdAt = DateTime.parse(json['createdAt']);
    dhikr.updatedAt = DateTime.parse(json['updatedAt']);
    dhikr.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    dhikr.isDeleted = json['isDeleted'] ?? false;
    dhikr.syncId = json['syncId'];
    return dhikr;
  }
}
