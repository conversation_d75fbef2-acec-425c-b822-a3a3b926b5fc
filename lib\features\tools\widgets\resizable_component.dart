import 'package:flutter/material.dart';
import '../models/ui_component.dart';

/// A resizable wrapper widget for UI components in the builder
class ResizableComponent extends StatefulWidget {
  final UIComponent component;
  final Widget child;
  final Function(UIComponent) onComponentUpdated;
  final bool isSelected;
  final bool showResizeHandles;
  final double gridSize;

  const ResizableComponent({
    super.key,
    required this.component,
    required this.child,
    required this.onComponentUpdated,
    this.isSelected = false,
    this.showResizeHandles = true,
    this.gridSize = 10.0,
  });

  @override
  State<ResizableComponent> createState() => _ResizableComponentState();
}

class _ResizableComponentState extends State<ResizableComponent> {
  bool _isDragging = false;
  bool _isResizing = false;
  Offset _dragStartPosition = Offset.zero;
  Size _resizeStartSize = Size.zero;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.component.position.x,
      top: widget.component.position.y,
      child: GestureDetector(
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        onTap: () => _selectComponent(),
        child: Container(
          width: widget.component.position.width,
          height: widget.component.position.height,
          decoration: BoxDecoration(
            border: widget.isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Stack(
            children: [
              // Main component content
              Positioned.fill(
                child: widget.child,
              ),
              
              // Resize handles (only show when selected and resizable)
              if (widget.isSelected && 
                  widget.showResizeHandles && 
                  widget.component.behavior.isResizable)
                ..._buildResizeHandles(),
              
              // Selection overlay
              if (widget.isSelected)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectComponent() {
    final updatedComponent = widget.component.copyWith(isSelected: true);
    widget.onComponentUpdated(updatedComponent);
  }

  void _onPanStart(DragStartDetails details) {
    _dragStartPosition = details.localPosition;
    _resizeStartSize = Size(
      widget.component.position.width,
      widget.component.position.height,
    );
    
    // Determine if this is a resize operation based on position
    final isNearEdge = _isNearEdge(details.localPosition);
    setState(() {
      _isResizing = isNearEdge;
      _isDragging = !isNearEdge && widget.component.behavior.isDraggable;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_isDragging) {
      _handleDrag(details);
    } else if (_isResizing) {
      _handleResize(details);
    }
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
      _isResizing = false;
    });
  }

  void _handleDrag(DragUpdateDetails details) {
    final newX = _snapToGrid(widget.component.position.x + details.delta.dx);
    final newY = _snapToGrid(widget.component.position.y + details.delta.dy);
    
    final updatedPosition = widget.component.position.copyWith(
      x: newX,
      y: newY,
    );
    
    final updatedComponent = widget.component.copyWith(position: updatedPosition);
    widget.onComponentUpdated(updatedComponent);
  }

  void _handleResize(DragUpdateDetails details) {
    final newWidth = _snapToGrid(
      (_resizeStartSize.width + details.localPosition.dx - _dragStartPosition.dx)
          .clamp(20.0, double.infinity),
    );
    final newHeight = _snapToGrid(
      (_resizeStartSize.height + details.localPosition.dy - _dragStartPosition.dy)
          .clamp(20.0, double.infinity),
    );
    
    final updatedPosition = widget.component.position.copyWith(
      width: newWidth,
      height: newHeight,
    );
    
    final updatedComponent = widget.component.copyWith(
      position: updatedPosition,
      isResizing: true,
    );
    widget.onComponentUpdated(updatedComponent);
  }

  bool _isNearEdge(Offset localPosition) {
    const edgeThreshold = 10.0;
    final width = widget.component.position.width;
    final height = widget.component.position.height;
    
    return (localPosition.dx > width - edgeThreshold ||
            localPosition.dy > height - edgeThreshold);
  }

  double _snapToGrid(double value) {
    return (value / widget.gridSize).round() * widget.gridSize;
  }

  List<Widget> _buildResizeHandles() {
    const handleSize = 8.0;
    final color = Theme.of(context).colorScheme.primary;
    
    return [
      // Bottom-right corner handle
      Positioned(
        right: -handleSize / 2,
        bottom: -handleSize / 2,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 1),
          ),
        ),
      ),
      
      // Right edge handle
      Positioned(
        right: -handleSize / 2,
        top: widget.component.position.height / 2 - handleSize / 2,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 1),
          ),
        ),
      ),
      
      // Bottom edge handle
      Positioned(
        left: widget.component.position.width / 2 - handleSize / 2,
        bottom: -handleSize / 2,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 1),
          ),
        ),
      ),
    ];
  }
}

/// Extension to provide color utilities
extension ColorExtensions on Color {
  String toHexString() {
    return '#${value.toRadixString(16).padLeft(8, '0').substring(2)}';
  }
  
  static Color fromHexString(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}
