import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/category.dart';
import '../providers/category_provider.dart';
import '../widgets/category_icon_picker.dart';

/// Category management screen for CRUD operations
class CategoryManagementScreen extends ConsumerStatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  ConsumerState<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends ConsumerState<CategoryManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  CategoryType _filterType = CategoryType.expense;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _filterType = CategoryType.values[_tabController.index];
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categories = ref.watch(categoriesProvider);
    
    return StandardizedScaffold(
      title: 'Category Management',
      currentRoute: '/money-flow/categories',
      showBackButton: true,
      actions: [
        IconButton(
          onPressed: _addCategory,
          icon: const Icon(Icons.add),
          tooltip: 'Add Category',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.trending_down), text: 'Expenses'),
          Tab(icon: Icon(Icons.trending_up), text: 'Income'),
          Tab(icon: Icon(Icons.swap_horiz), text: 'Transfers'),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          _buildSearchBar(),
          
          // Categories list
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCategoryList(CategoryType.expense, categories),
                _buildCategoryList(CategoryType.income, categories),
                _buildCategoryList(CategoryType.transfer, categories),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addCategory,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build search bar
  Widget _buildSearchBar() {
    return Container(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search categories...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  /// Build category list for specific type
  Widget _buildCategoryList(CategoryType type, List<MoneyCategory> allCategories) {
    final filteredCategories = allCategories
        .where((category) => 
            category.type == type && 
            !category.isDeleted &&
            (category.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             category.description.toLowerCase().contains(_searchQuery.toLowerCase())))
        .toList();

    if (filteredCategories.isEmpty) {
      return EmptyState(
        icon: _getTypeIcon(type),
        title: 'No ${type.name} categories',
        message: _searchQuery.isNotEmpty 
            ? 'No categories match your search'
            : 'Add your first ${type.name} category to get started',
        action: StandardActionButton(
          label: 'Add Category',
          icon: Icons.add,
          onPressed: _addCategory,
        ),
      );
    }

    return ListView.builder(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      itemCount: filteredCategories.length,
      itemBuilder: (context, index) {
        final category = filteredCategories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  /// Build individual category card
  Widget _buildCategoryCard(MoneyCategory category) {
    return StandardCard(
      onTap: () => _editCategory(category),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: category.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              category.icon,
              color: category.color,
              size: 24,
            ),
          ),
          
          SizedBox(width: ResponsiveSpacing.md(context)),
          
          // Category info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        category.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    if (category.isDefault)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'DEFAULT',
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                
                if (category.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    category.description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
                
                // Budget info for expense categories
                if (category.type == CategoryType.expense && category.budgetLimit > 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Budget: \$${category.budgetLimit.toStringAsFixed(0)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      if (category.enableBudgetAlert) ...[
                        const SizedBox(width: 8),
                        Icon(
                          Icons.notifications_active,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ],
                    ],
                  ),
                ],
              ],
            ),
          ),
          
          // Actions
          PopupMenuButton<String>(
            onSelected: (value) => _handleCategoryAction(value, category),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Edit'),
                  dense: true,
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: ListTile(
                  leading: Icon(Icons.copy),
                  title: Text('Duplicate'),
                  dense: true,
                ),
              ),
              if (!category.isDefault)
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('Delete', style: TextStyle(color: Colors.red)),
                    dense: true,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Get icon for category type
  IconData _getTypeIcon(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return Icons.trending_up;
      case CategoryType.expense:
        return Icons.trending_down;
      case CategoryType.transfer:
        return Icons.swap_horiz;
    }
  }

  /// Handle category actions
  void _handleCategoryAction(String action, MoneyCategory category) {
    switch (action) {
      case 'edit':
        _editCategory(category);
        break;
      case 'duplicate':
        _duplicateCategory(category);
        break;
      case 'delete':
        _deleteCategory(category);
        break;
    }
  }

  /// Add new category
  void _addCategory() {
    showDialog(
      context: context,
      builder: (context) => _CategoryFormDialog(
        type: _filterType,
        onSave: (category) {
          ref.read(categoriesProvider.notifier).addCategory(category);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Category added successfully')),
          );
        },
      ),
    );
  }

  /// Edit existing category
  void _editCategory(MoneyCategory category) {
    showDialog(
      context: context,
      builder: (context) => _CategoryFormDialog(
        category: category,
        onSave: (updatedCategory) {
          ref.read(categoriesProvider.notifier).updateCategory(updatedCategory);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Category updated successfully')),
          );
        },
      ),
    );
  }

  /// Duplicate category
  void _duplicateCategory(MoneyCategory category) {
    final duplicated = MoneyCategory.create(
      name: '${category.name} (Copy)',
      type: category.type,
      userId: category.userId,
      description: category.description,
      icon: category.icon,
      color: category.color,
      budgetLimit: category.budgetLimit,
      enableBudgetAlert: category.enableBudgetAlert,
      alertThreshold: category.alertThreshold,
    );
    
    ref.read(categoriesProvider.notifier).addCategory(duplicated);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Category duplicated successfully')),
    );
  }

  /// Delete category
  void _deleteCategory(MoneyCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(categoriesProvider.notifier).deleteCategory(category.id);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Category deleted successfully')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

/// Category form dialog for add/edit operations
class _CategoryFormDialog extends StatefulWidget {
  final MoneyCategory? category;
  final CategoryType? type;
  final Function(MoneyCategory category) onSave;

  const _CategoryFormDialog({
    this.category,
    this.type,
    required this.onSave,
  });

  @override
  State<_CategoryFormDialog> createState() => _CategoryFormDialogState();
}

class _CategoryFormDialogState extends State<_CategoryFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _budgetController;
  
  late CategoryType _selectedType;
  late IconData _selectedIcon;
  late Color _selectedColor;
  late bool _enableBudgetAlert;
  late double _alertThreshold;

  @override
  void initState() {
    super.initState();
    
    _nameController = TextEditingController();
    _descriptionController = TextEditingController();
    _budgetController = TextEditingController(text: '0');
    
    if (widget.category != null) {
      // Edit mode
      final category = widget.category!;
      _nameController.text = category.name;
      _descriptionController.text = category.description;
      _budgetController.text = category.budgetLimit.toString();
      _selectedType = category.type;
      _selectedIcon = category.icon;
      _selectedColor = category.color;
      _enableBudgetAlert = category.enableBudgetAlert;
      _alertThreshold = category.alertThreshold;
    } else {
      // Add mode
      _selectedType = widget.type ?? CategoryType.expense;
      _selectedIcon = Icons.category;
      _selectedColor = Colors.blue;
      _enableBudgetAlert = false;
      _alertThreshold = 0.8;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _budgetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                widget.category == null ? 'Add Category' : 'Edit Category',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Category name
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Category Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a category name';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              
              const SizedBox(height: 16),
              
              // Type selection
              DropdownButtonFormField<CategoryType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Category Type',
                  border: OutlineInputBorder(),
                ),
                items: CategoryType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.name.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
              
              const SizedBox(height: 16),
              
              // Icon and color selection
              Row(
                children: [
                  // Icon
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Icon',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: _showIconPicker,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(_selectedIcon, color: _selectedColor),
                                const SizedBox(width: 8),
                                const Text('Choose Icon'),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Color
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Color',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: _showColorPicker,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: _selectedColor,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                const Text('Choose Color'),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              // Budget settings for expense categories
              if (_selectedType == CategoryType.expense) ...[
                const SizedBox(height: 16),
                TextFormField(
                  controller: _budgetController,
                  decoration: const InputDecoration(
                    labelText: 'Monthly Budget Limit',
                    prefixText: '\$',
                    border: OutlineInputBorder(),
                    helperText: 'Set to 0 for no budget limit',
                  ),
                  keyboardType: TextInputType.number,
                ),
                
                const SizedBox(height: 8),
                
                SwitchListTile(
                  title: const Text('Enable Budget Alerts'),
                  subtitle: const Text('Get notified when approaching budget limit'),
                  value: _enableBudgetAlert,
                  onChanged: (value) {
                    setState(() {
                      _enableBudgetAlert = value;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
              
              const SizedBox(height: 24),
              
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _saveCategory,
                    child: Text(widget.category == null ? 'Add' : 'Update'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showIconPicker() {
    showDialog(
      context: context,
      builder: (context) => CategoryIconPicker(
        selectedIcon: _selectedIcon,
        selectedColor: _selectedColor,
        onIconSelected: (icon) {
          setState(() {
            _selectedIcon = icon;
          });
        },
      ),
    );
  }

  void _showColorPicker() {
    final colors = [
      Colors.red, Colors.pink, Colors.purple, Colors.deepPurple,
      Colors.indigo, Colors.blue, Colors.lightBlue, Colors.cyan,
      Colors.teal, Colors.green, Colors.lightGreen, Colors.lime,
      Colors.yellow, Colors.amber, Colors.orange, Colors.deepOrange,
      Colors.brown, Colors.grey, Colors.blueGrey,
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Color'),
        content: SizedBox(
          width: 300,
          height: 200,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 6,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: colors.length,
            itemBuilder: (context, index) {
              final color = colors[index];
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                  Navigator.of(context).pop();
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: _selectedColor == color
                        ? Border.all(color: Colors.black, width: 3)
                        : null,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void _saveCategory() {
    if (!_formKey.currentState!.validate()) return;
    
    final name = _nameController.text.trim();
    final description = _descriptionController.text.trim();
    final budgetLimit = double.tryParse(_budgetController.text) ?? 0.0;
    
    if (widget.category == null) {
      // Create new category
      final category = MoneyCategory.create(
        name: name,
        type: _selectedType,
        userId: '', // Will be set by provider
        description: description,
        icon: _selectedIcon,
        color: _selectedColor,
        budgetLimit: budgetLimit,
        enableBudgetAlert: _enableBudgetAlert,
        alertThreshold: _alertThreshold,
      );
      
      widget.onSave(category);
    } else {
      // Update existing category
      final category = widget.category!;
      category.name = name;
      category.description = description;
      category.type = _selectedType;
      category.icon = _selectedIcon;
      category.color = _selectedColor;
      category.budgetLimit = budgetLimit;
      category.enableBudgetAlert = _enableBudgetAlert;
      category.alertThreshold = _alertThreshold;
      category.updateTimestamp();
      
      widget.onSave(category);
    }
    
    Navigator.of(context).pop();
  }
}
