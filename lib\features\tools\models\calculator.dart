enum CalculatorType { 
  basic, 
  scientific, 
  mortgage, 
  tip, 
  bmi, 
  currency, 
  unit_converter,
  custom 
}

class Calculator {
  int id = 0;
  String name = '';
  String description = '';
  CalculatorType type = CalculatorType.basic;
  Map<String, dynamic> configuration = {};
  bool isFavorite = false;
  bool isCustom = false;
  String userId = '';
  
  // Usage statistics
  int usageCount = 0;
  DateTime? lastUsed;
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Calculator();

  Calculator.create({
    required this.name,
    required this.description,
    required this.type,
    required this.userId,
    this.configuration = const {},
    this.isFavorite = false,
    this.isCustom = false,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void incrementUsage() {
    usageCount++;
    lastUsed = DateTime.now();
    updateTimestamp();
  }

  void toggleFavorite() {
    isFavorite = !isFavorite;
    updateTimestamp();
  }

  String get typeDisplayName {
    switch (type) {
      case CalculatorType.basic:
        return 'Basic Calculator';
      case CalculatorType.scientific:
        return 'Scientific Calculator';
      case CalculatorType.mortgage:
        return 'Mortgage Calculator';
      case CalculatorType.tip:
        return 'Tip Calculator';
      case CalculatorType.bmi:
        return 'BMI Calculator';
      case CalculatorType.currency:
        return 'Currency Converter';
      case CalculatorType.unit_converter:
        return 'Unit Converter';
      case CalculatorType.custom:
        return 'Custom Calculator';
    }
  }

  String get iconName {
    switch (type) {
      case CalculatorType.basic:
        return 'calculate';
      case CalculatorType.scientific:
        return 'functions';
      case CalculatorType.mortgage:
        return 'home';
      case CalculatorType.tip:
        return 'restaurant';
      case CalculatorType.bmi:
        return 'fitness_center';
      case CalculatorType.currency:
        return 'currency_exchange';
      case CalculatorType.unit_converter:
        return 'straighten';
      case CalculatorType.custom:
        return 'build';
    }
  }

  void updateCalculator({
    String? name,
    String? description,
    Map<String, dynamic>? configuration,
    bool? isFavorite,
  }) {
    if (name != null) this.name = name;
    if (description != null) this.description = description;
    if (configuration != null) this.configuration = configuration;
    if (isFavorite != null) this.isFavorite = isFavorite;
    
    updateTimestamp();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'configuration': configuration,
      'isFavorite': isFavorite,
      'isCustom': isCustom,
      'usageCount': usageCount,
      'lastUsed': lastUsed?.toIso8601String(),
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Calculator.fromJson(Map<String, dynamic> json) {
    final calculator = Calculator();
    calculator.id = json['id'] ?? 0;
    calculator.name = json['name'] ?? '';
    calculator.description = json['description'] ?? '';
    calculator.type = CalculatorType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => CalculatorType.basic,
    );
    calculator.configuration = Map<String, dynamic>.from(json['configuration'] ?? {});
    calculator.isFavorite = json['isFavorite'] ?? false;
    calculator.isCustom = json['isCustom'] ?? false;
    calculator.usageCount = json['usageCount'] ?? 0;
    calculator.lastUsed = json['lastUsed'] != null ? DateTime.parse(json['lastUsed']) : null;
    calculator.userId = json['userId'] ?? '';
    calculator.createdAt = DateTime.parse(json['createdAt']);
    calculator.updatedAt = DateTime.parse(json['updatedAt']);
    calculator.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    calculator.isDeleted = json['isDeleted'] ?? false;
    calculator.syncId = json['syncId'];
    return calculator;
  }
}
