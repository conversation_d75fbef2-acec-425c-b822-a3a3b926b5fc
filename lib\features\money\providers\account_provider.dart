import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/account.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Account list provider
final accountsProvider = StateNotifierProvider<AccountNotifier, List<Account>>((ref) {
  return AccountNotifier(ref);
});

class AccountNotifier extends StateNotifier<List<Account>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  AccountNotifier(this.ref) : super([]) {
    loadAccounts();
  }

  Future<void> loadAccounts() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final accounts = await _db.getAllAccounts(userId: userProfile.id.toString());
      state = accounts;
    }
  }

  Future<void> addAccount(Account account) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      account.userId = userProfile.id.toString();
      final savedAccount = await _db.saveAccount(account);
      state = [...state, savedAccount];
    }
  }

  Future<void> updateAccount(Account account) async {
    final savedAccount = await _db.saveAccount(account);
    state = state.map((a) => a.id == savedAccount.id ? savedAccount : a).toList();
  }

  Future<void> deleteAccount(int accountId) async {
    await _db.deleteAccount(accountId);
    state = state.where((a) => a.id != accountId).toList();
  }

  Future<void> updateAccountBalance(int accountId, double newBalance) async {
    final accountIndex = state.indexWhere((a) => a.id == accountId);
    if (accountIndex != -1) {
      final account = state[accountIndex];
      account.updateBalance(newBalance);
      await updateAccount(account);
    }
  }

  List<Account> getActiveAccounts() {
    return state.where((account) => account.isActive).toList();
  }

  List<Account> getAccountsByType(AccountType type) {
    return state.where((account) => account.type == type).toList();
  }

  double getTotalBalance() {
    return state
        .where((account) => account.includeInTotal && account.isActive)
        .fold(0.0, (sum, account) => sum + account.balance);
  }

  double getNetWorth() {
    double assets = 0.0;
    double liabilities = 0.0;
    
    for (final account in state.where((a) => a.isActive && a.includeInTotal)) {
      if (account.isDebtAccount) {
        liabilities += account.balance.abs();
      } else {
        assets += account.balance;
      }
    }
    
    return assets - liabilities;
  }
}

// Active accounts provider
final activeAccountsProvider = Provider<List<Account>>((ref) {
  final accounts = ref.watch(accountsProvider);
  return accounts.where((account) => account.isActive).toList();
});

// Accounts by type provider
final accountsByTypeProvider = Provider.family<List<Account>, AccountType>((ref, type) {
  final accounts = ref.watch(accountsProvider);
  return accounts.where((account) => account.type == type).toList();
});

// Account statistics provider
final accountStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final accounts = ref.watch(accountsProvider);
  final activeAccounts = accounts.where((a) => a.isActive).toList();
  
  double totalBalance = 0.0;
  double assets = 0.0;
  double liabilities = 0.0;
  
  final typeStats = <AccountType, int>{};
  
  for (final account in activeAccounts) {
    if (account.includeInTotal) {
      totalBalance += account.balance;
      
      if (account.isDebtAccount) {
        liabilities += account.balance.abs();
      } else {
        assets += account.balance;
      }
    }
    
    typeStats[account.type] = (typeStats[account.type] ?? 0) + 1;
  }
  
  return {
    'totalAccounts': accounts.length,
    'activeAccounts': activeAccounts.length,
    'totalBalance': totalBalance,
    'assets': assets,
    'liabilities': liabilities,
    'netWorth': assets - liabilities,
    'typeStats': typeStats,
  };
});

// Search accounts provider
final searchAccountsProvider = StateProvider<String>((ref) => '');

final filteredAccountsProvider = Provider<List<Account>>((ref) {
  final accounts = ref.watch(accountsProvider);
  final searchQuery = ref.watch(searchAccountsProvider);
  
  if (searchQuery.isEmpty) {
    return accounts;
  }
  
  final query = searchQuery.toLowerCase();
  return accounts.where((account) {
    return account.name.toLowerCase().contains(query) ||
           account.description.toLowerCase().contains(query) ||
           account.typeDisplayName.toLowerCase().contains(query) ||
           (account.bankName?.toLowerCase().contains(query) ?? false);
  }).toList();
});
