import 'package:flutter/foundation.dart';

/// Represents a single verse in the Quran
@immutable
class QuranVerse {
  final int surahNumber;
  final int verseNumber;
  final String arabicText;
  final String? translation;
  final bool isBookmarked;

  const QuranVerse({
    required this.surahNumber,
    required this.verseNumber,
    required this.arabicText,
    this.translation,
    this.isBookmarked = false,
  });

  /// Get verse address (e.g., "2:255" for Ayat al-Kursi)
  String get address => '$surahNumber:$verseNumber';

  /// Get verse ID for unique identification
  String get id => '${surahNumber}_$verseNumber';

  /// Copy with modifications
  QuranVerse copyWith({
    int? surahNumber,
    int? verseNumber,
    String? arabicText,
    String? translation,
    bool? isBookmarked,
  }) {
    return QuranVerse(
      surahNumber: surahNumber ?? this.surahNumber,
      verseNumber: verseNumber ?? this.verseNumber,
      arabicText: arabicText ?? this.arabicText,
      translation: translation ?? this.translation,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'surahNumber': surahNumber,
      'verseNumber': verseNumber,
      'arabicText': arabicText,
      'translation': translation,
      'isBookmarked': isBookmarked,
    };
  }

  /// Create from JSON
  factory QuranVerse.fromJson(Map<String, dynamic> json) {
    return QuranVerse(
      surahNumber: json['surahNumber'] as int,
      verseNumber: json['verseNumber'] as int,
      arabicText: json['arabicText'] as String,
      translation: json['translation'] as String?,
      isBookmarked: json['isBookmarked'] as bool? ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranVerse &&
        other.surahNumber == surahNumber &&
        other.verseNumber == verseNumber &&
        other.arabicText == arabicText &&
        other.translation == translation &&
        other.isBookmarked == isBookmarked;
  }

  @override
  int get hashCode => Object.hash(
    surahNumber, verseNumber, arabicText, translation, isBookmarked,
  );

  @override
  String toString() => 'QuranVerse($address: ${arabicText.substring(0, 20)}...)';
}

/// Represents a Surah (chapter) in the Quran
@immutable
class QuranSurah {
  final int number;
  final String arabicName;
  final String englishName;
  final String transliteration;
  final int verseCount;
  final String revelationType; // 'Meccan' or 'Medinan'
  final List<QuranVerse> verses;

  const QuranSurah({
    required this.number,
    required this.arabicName,
    required this.englishName,
    required this.transliteration,
    required this.verseCount,
    required this.revelationType,
    this.verses = const [],
  });

  /// Get Surah display name
  String get displayName => '$number. $englishName ($transliteration)';

  /// Get short display name
  String get shortName => '$number. $englishName';

  /// Copy with modifications
  QuranSurah copyWith({
    int? number,
    String? arabicName,
    String? englishName,
    String? transliteration,
    int? verseCount,
    String? revelationType,
    List<QuranVerse>? verses,
  }) {
    return QuranSurah(
      number: number ?? this.number,
      arabicName: arabicName ?? this.arabicName,
      englishName: englishName ?? this.englishName,
      transliteration: transliteration ?? this.transliteration,
      verseCount: verseCount ?? this.verseCount,
      revelationType: revelationType ?? this.revelationType,
      verses: verses ?? this.verses,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'arabicName': arabicName,
      'englishName': englishName,
      'transliteration': transliteration,
      'verseCount': verseCount,
      'revelationType': revelationType,
      'verses': verses.map((v) => v.toJson()).toList(),
    };
  }

  /// Create from JSON
  factory QuranSurah.fromJson(Map<String, dynamic> json) {
    final versesJson = json['verses'] as List<dynamic>? ?? [];
    final verses = versesJson
        .map((v) => QuranVerse.fromJson(v as Map<String, dynamic>))
        .toList();

    return QuranSurah(
      number: json['number'] as int,
      arabicName: json['arabicName'] as String,
      englishName: json['englishName'] as String,
      transliteration: json['transliteration'] as String,
      verseCount: json['verseCount'] as int,
      revelationType: json['revelationType'] as String,
      verses: verses,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranSurah &&
        other.number == number &&
        other.arabicName == arabicName &&
        other.englishName == englishName &&
        other.transliteration == transliteration &&
        other.verseCount == verseCount &&
        other.revelationType == revelationType &&
        listEquals(other.verses, verses);
  }

  @override
  int get hashCode => Object.hash(
    number, arabicName, englishName, transliteration, verseCount, revelationType, verses,
  );

  @override
  String toString() => 'QuranSurah($displayName, $verseCount verses)';
}

/// Search result for Quran verses
@immutable
class QuranSearchResult {
  final QuranVerse verse;
  final String matchedText;
  final int matchStart;
  final int matchEnd;
  final double relevanceScore;

  const QuranSearchResult({
    required this.verse,
    required this.matchedText,
    required this.matchStart,
    required this.matchEnd,
    this.relevanceScore = 1.0,
  });

  /// Get highlighted text with match
  String getHighlightedText() {
    final text = verse.arabicText;
    if (matchStart >= 0 && matchEnd <= text.length) {
      return text.substring(0, matchStart) +
          '**${text.substring(matchStart, matchEnd)}**' +
          text.substring(matchEnd);
    }
    return text;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranSearchResult &&
        other.verse == verse &&
        other.matchedText == matchedText &&
        other.matchStart == matchStart &&
        other.matchEnd == matchEnd &&
        other.relevanceScore == relevanceScore;
  }

  @override
  int get hashCode => Object.hash(
    verse, matchedText, matchStart, matchEnd, relevanceScore,
  );

  @override
  String toString() => 'QuranSearchResult(${verse.address}: $matchedText)';
}

/// Bookmark for a Quran verse
@immutable
class QuranBookmark {
  final String id;
  final QuranVerse verse;
  final String? note;
  final DateTime createdAt;
  final List<String> tags;

  const QuranBookmark({
    required this.id,
    required this.verse,
    this.note,
    required this.createdAt,
    this.tags = const [],
  });

  /// Create new bookmark
  factory QuranBookmark.create(QuranVerse verse, {String? note, List<String>? tags}) {
    return QuranBookmark(
      id: '${verse.id}_${DateTime.now().millisecondsSinceEpoch}',
      verse: verse,
      note: note,
      createdAt: DateTime.now(),
      tags: tags ?? [],
    );
  }

  /// Copy with modifications
  QuranBookmark copyWith({
    String? id,
    QuranVerse? verse,
    String? note,
    DateTime? createdAt,
    List<String>? tags,
  }) {
    return QuranBookmark(
      id: id ?? this.id,
      verse: verse ?? this.verse,
      note: note ?? this.note,
      createdAt: createdAt ?? this.createdAt,
      tags: tags ?? this.tags,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'verse': verse.toJson(),
      'note': note,
      'createdAt': createdAt.toIso8601String(),
      'tags': tags,
    };
  }

  /// Create from JSON
  factory QuranBookmark.fromJson(Map<String, dynamic> json) {
    return QuranBookmark(
      id: json['id'] as String,
      verse: QuranVerse.fromJson(json['verse'] as Map<String, dynamic>),
      note: json['note'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      tags: List<String>.from(json['tags'] as List? ?? []),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranBookmark &&
        other.id == id &&
        other.verse == verse &&
        other.note == note &&
        other.createdAt == createdAt &&
        listEquals(other.tags, tags);
  }

  @override
  int get hashCode => Object.hash(id, verse, note, createdAt, tags);

  @override
  String toString() => 'QuranBookmark(${verse.address}: ${note ?? 'No note'})';
}

/// Quran reading preferences
@immutable
class QuranPreferences {
  final double fontSize;
  final String fontFamily;
  final bool showTranslation;
  final bool showVerseNumbers;
  final String theme; // 'light', 'dark', 'sepia'
  final double lineSpacing;
  final bool enableNightMode;

  const QuranPreferences({
    this.fontSize = 18.0,
    this.fontFamily = 'Amiri',
    this.showTranslation = false,
    this.showVerseNumbers = true,
    this.theme = 'light',
    this.lineSpacing = 1.5,
    this.enableNightMode = false,
  });

  /// Copy with modifications
  QuranPreferences copyWith({
    double? fontSize,
    String? fontFamily,
    bool? showTranslation,
    bool? showVerseNumbers,
    String? theme,
    double? lineSpacing,
    bool? enableNightMode,
  }) {
    return QuranPreferences(
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      showTranslation: showTranslation ?? this.showTranslation,
      showVerseNumbers: showVerseNumbers ?? this.showVerseNumbers,
      theme: theme ?? this.theme,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      enableNightMode: enableNightMode ?? this.enableNightMode,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'showTranslation': showTranslation,
      'showVerseNumbers': showVerseNumbers,
      'theme': theme,
      'lineSpacing': lineSpacing,
      'enableNightMode': enableNightMode,
    };
  }

  /// Create from JSON
  factory QuranPreferences.fromJson(Map<String, dynamic> json) {
    return QuranPreferences(
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 18.0,
      fontFamily: json['fontFamily'] as String? ?? 'Amiri',
      showTranslation: json['showTranslation'] as bool? ?? false,
      showVerseNumbers: json['showVerseNumbers'] as bool? ?? true,
      theme: json['theme'] as String? ?? 'light',
      lineSpacing: (json['lineSpacing'] as num?)?.toDouble() ?? 1.5,
      enableNightMode: json['enableNightMode'] as bool? ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranPreferences &&
        other.fontSize == fontSize &&
        other.fontFamily == fontFamily &&
        other.showTranslation == showTranslation &&
        other.showVerseNumbers == showVerseNumbers &&
        other.theme == theme &&
        other.lineSpacing == lineSpacing &&
        other.enableNightMode == enableNightMode;
  }

  @override
  int get hashCode => Object.hash(
    fontSize, fontFamily, showTranslation, showVerseNumbers, theme, lineSpacing, enableNightMode,
  );
}
