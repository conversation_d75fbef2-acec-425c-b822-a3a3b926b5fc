import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/quran_settings.dart';

/// Quran settings notifier
class QuranSettingsNotifier extends StateNotifier<QuranSettings> {
  QuranSettingsNotifier() : super(const QuranSettings()) {
    _loadSettings();
  }

  static const String _settingsKey = 'quran_settings';

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final Map<String, dynamic> data = json.decode(settingsJson);
        state = QuranSettings.fromJson(data);
      }
    } catch (e) {
      // If loading fails, keep default settings
      print('Error loading quran settings: $e');
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(state.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      print('Error saving quran settings: $e');
    }
  }

  /// Update settings
  Future<void> updateSettings(QuranSettings newSettings) async {
    state = newSettings;
    await _saveSettings();
  }

  /// Update reading mode
  Future<void> updateReadingMode(QuranReadingMode mode) async {
    state = state.copyWith(readingMode: mode);
    await _saveSettings();
  }

  /// Update font settings
  Future<void> updateFontSettings({
    ArabicFont? arabicFont,
    double? arabicFontSize,
    double? translationFontSize,
    String? translationFontFamily,
  }) async {
    state = state.copyWith(
      arabicFont: arabicFont,
      arabicFontSize: arabicFontSize,
      translationFontSize: translationFontSize,
      translationFontFamily: translationFontFamily,
    );
    await _saveSettings();
  }

  /// Update display settings
  Future<void> updateDisplaySettings({
    bool? showTranslation,
    bool? showTransliteration,
    List<TranslationLanguage>? selectedTranslations,
    bool? enableDarkMode,
    bool? enableNightMode,
  }) async {
    state = state.copyWith(
      showTranslation: showTranslation,
      showTransliteration: showTransliteration,
      selectedTranslations: selectedTranslations,
      enableDarkMode: enableDarkMode,
      enableNightMode: enableNightMode,
    );
    await _saveSettings();
  }

  /// Update Tajweed settings
  Future<void> updateTajweedSettings({
    TajweedStyle? style,
    bool? enableHighlighting,
    bool? showRules,
    bool? enableAudio,
    bool? showLegend,
  }) async {
    state = state.copyWith(
      tajweedStyle: style,
      enableTajweedHighlighting: enableHighlighting,
      showTajweedRules: showRules,
      enableTajweedAudio: enableAudio,
      showTajweedLegend: showLegend,
    );
    await _saveSettings();
  }

  /// Update reading settings
  Future<void> updateReadingSettings({
    bool? autoScroll,
    double? scrollSpeed,
    bool? highlightVerse,
    bool? showVerseNumbers,
    bool? showSurahHeaders,
    bool? showJuzMarkers,
    bool? showHizbMarkers,
    bool? showSajdahMarkers,
    double? lineSpacing,
    double? wordSpacing,
    bool? fullscreen,
    bool? keepScreenOn,
    bool? enableGestures,
    bool? enableZoom,
  }) async {
    state = state.copyWith(
      enableAutoScroll: autoScroll,
      autoScrollSpeed: scrollSpeed,
      highlightCurrentVerse: highlightVerse,
      showVerseNumbers: showVerseNumbers,
      showSurahHeaders: showSurahHeaders,
      showJuzMarkers: showJuzMarkers,
      showHizbMarkers: showHizbMarkers,
      showSajdahMarkers: showSajdahMarkers,
      lineSpacing: lineSpacing,
      wordSpacing: wordSpacing,
      enableFullscreen: fullscreen,
      keepScreenOn: keepScreenOn,
      enableGestures: enableGestures,
      enableZoom: enableZoom,
    );
    await _saveSettings();
  }

  /// Update audio settings
  Future<void> updateAudioSettings({
    String? selectedReciter,
    RecitationSpeed? speed,
    bool? audioHighlighting,
    bool? repeatMode,
    int? repeatCount,
    bool? autoPlay,
    bool? continuousPlay,
    double? volume,
    bool? audioBookmarks,
    DownloadQuality? quality,
  }) async {
    state = state.copyWith(
      selectedReciter: selectedReciter,
      recitationSpeed: speed,
      enableAudioHighlighting: audioHighlighting,
      enableRepeatMode: repeatMode,
      repeatCount: repeatCount,
      enableAutoPlay: autoPlay,
      enableContinuousPlay: continuousPlay,
      audioVolume: volume,
      enableAudioBookmarks: audioBookmarks,
      audioQuality: quality,
    );
    await _saveSettings();
  }

  /// Update search settings
  Future<void> updateSearchSettings({
    bool? advancedSearch,
    bool? searchTranslation,
    bool? searchTransliteration,
    bool? rootWordSearch,
    bool? topicSearch,
    bool? caseSensitive,
    bool? wholeWord,
    int? maxHistory,
  }) async {
    state = state.copyWith(
      enableAdvancedSearch: advancedSearch,
      searchInTranslation: searchTranslation,
      searchInTransliteration: searchTransliteration,
      enableRootWordSearch: rootWordSearch,
      enableTopicSearch: topicSearch,
      caseSensitiveSearch: caseSensitive,
      wholeWordSearch: wholeWord,
      maxSearchHistory: maxHistory,
    );
    await _saveSettings();
  }

  /// Update bookmark settings
  Future<void> updateBookmarkSettings({
    bool? enableBookmarks,
    List<BookmarkCategory>? categories,
    bool? enableNotes,
    bool? enableTags,
    bool? enableSharing,
    bool? autoBookmarkLastRead,
    String? defaultCategory,
  }) async {
    state = state.copyWith(
      enableBookmarks: enableBookmarks,
      bookmarkCategories: categories,
      enableBookmarkNotes: enableNotes,
      enableBookmarkTags: enableTags,
      enableBookmarkSharing: enableSharing,
      autoBookmarkLastRead: autoBookmarkLastRead,
      defaultBookmarkCategory: defaultCategory,
    );
    await _saveSettings();
  }

  /// Update progress tracking
  Future<void> updateProgressSettings({
    bool? trackProgress,
    bool? showStats,
    bool? enableGoals,
    int? dailyGoal,
    bool? enableStreaks,
    bool? enableAchievements,
    bool? showCompletion,
  }) async {
    state = state.copyWith(
      trackReadingProgress: trackProgress,
      showProgressStats: showStats,
      enableReadingGoals: enableGoals,
      dailyReadingGoal: dailyGoal,
      enableStreaks: enableStreaks,
      enableAchievements: enableAchievements,
      showCompletionPercentage: showCompletion,
    );
    await _saveSettings();
  }

  /// Update offline settings
  Future<void> updateOfflineSettings({
    bool? offlineMode,
    bool? autoDownloadTranslations,
    bool? autoDownloadAudio,
    DownloadQuality? downloadQuality,
    bool? wifiOnly,
    int? maxCacheSize,
    bool? smartDownload,
  }) async {
    state = state.copyWith(
      enableOfflineMode: offlineMode,
      autoDownloadTranslations: autoDownloadTranslations,
      autoDownloadAudio: autoDownloadAudio,
      downloadQuality: downloadQuality,
      downloadOnWifiOnly: wifiOnly,
      maxCacheSize: maxCacheSize,
      enableSmartDownload: smartDownload,
    );
    await _saveSettings();
  }

  /// Update reminder settings
  Future<void> updateReminderSettings({
    bool? enableReminders,
    bool? dailyReminder,
    bool? weeklyReminder,
    bool? customReminders,
    bool? quietHours,
    String? reminderSound,
    bool? enableVibration,
  }) async {
    state = state.copyWith(
      enableReminders: enableReminders,
      enableDailyReminder: dailyReminder,
      enableWeeklyReminder: weeklyReminder,
      enableCustomReminders: customReminders,
      enableQuietHours: quietHours,
      reminderSound: reminderSound,
      enableReminderVibration: enableVibration,
    );
    await _saveSettings();
  }

  /// Update memorization settings
  Future<void> updateMemorizationSettings({
    bool? memorizationMode,
    bool? hideTranslation,
    bool? enableTests,
    bool? enableProgress,
    bool? enableReminders,
    int? sessionDuration,
  }) async {
    state = state.copyWith(
      enableMemorizationMode: memorizationMode,
      hideTranslationInMemorization: hideTranslation,
      enableMemorizationTests: enableTests,
      enableMemorizationProgress: enableProgress,
      enableMemorizationReminders: enableReminders,
      memorizationSessionDuration: sessionDuration,
    );
    await _saveSettings();
  }

  /// Update study settings
  Future<void> updateStudySettings({
    bool? studyMode,
    bool? wordMeanings,
    bool? grammarAnalysis,
    bool? rootWords,
    bool? crossReferences,
    bool? showTafsir,
    List<String>? tafsirSources,
    bool? studyNotes,
    bool? highlighting,
  }) async {
    state = state.copyWith(
      enableStudyMode: studyMode,
      showWordMeanings: wordMeanings,
      showGrammarAnalysis: grammarAnalysis,
      showRootWords: rootWords,
      enableCrossReferences: crossReferences,
      showTafsir: showTafsir,
      selectedTafsirSources: tafsirSources,
      enableStudyNotes: studyNotes,
      enableHighlighting: highlighting,
    );
    await _saveSettings();
  }

  /// Update backup settings
  Future<void> updateBackupSettings({
    bool? autoBackup,
    bool? cloudBackup,
    bool? syncDevices,
    int? frequencyDays,
    bool? syncBookmarks,
    bool? syncProgress,
    bool? syncSettings,
    bool? syncNotes,
    bool? syncHighlights,
  }) async {
    state = state.copyWith(
      enableAutoBackup: autoBackup,
      backupToCloud: cloudBackup,
      syncAcrossDevices: syncDevices,
      backupFrequencyDays: frequencyDays,
      syncBookmarks: syncBookmarks,
      syncProgress: syncProgress,
      syncSettings: syncSettings,
      syncNotes: syncNotes,
      syncHighlights: syncHighlights,
    );
    await _saveSettings();
  }

  /// Update privacy settings
  Future<void> updatePrivacySettings({
    bool? privacyMode,
    bool? hideFromRecents,
    bool? requirePin,
    String? pinHash,
    bool? biometric,
    int? autoLockMinutes,
    bool? screenshotProtection,
    bool? incognitoMode,
  }) async {
    state = state.copyWith(
      enablePrivacyMode: privacyMode,
      hideFromRecents: hideFromRecents,
      requirePinForAccess: requirePin,
      pinHash: pinHash,
      enableBiometricAuth: biometric,
      autoLockMinutes: autoLockMinutes,
      enableScreenshotProtection: screenshotProtection,
      enableIncognitoMode: incognitoMode,
    );
    await _saveSettings();
  }

  /// Add downloaded reciter
  Future<void> addDownloadedReciter(String reciter) async {
    final reciters = List<String>.from(state.downloadedReciters);
    if (!reciters.contains(reciter)) {
      reciters.add(reciter);
      state = state.copyWith(downloadedReciters: reciters);
      await _saveSettings();
    }
  }

  /// Remove downloaded reciter
  Future<void> removeDownloadedReciter(String reciter) async {
    final reciters = List<String>.from(state.downloadedReciters);
    reciters.remove(reciter);
    state = state.copyWith(downloadedReciters: reciters);
    await _saveSettings();
  }

  /// Add to search history
  Future<void> addToSearchHistory(String query) async {
    final history = List<String>.from(state.searchHistory);
    history.remove(query); // Remove if exists to avoid duplicates
    history.insert(0, query); // Add to beginning
    
    // Limit history size
    if (history.length > state.maxSearchHistory) {
      history.removeRange(state.maxSearchHistory, history.length);
    }
    
    state = state.copyWith(searchHistory: history);
    await _saveSettings();
  }

  /// Clear search history
  Future<void> clearSearchHistory() async {
    state = state.copyWith(searchHistory: []);
    await _saveSettings();
  }

  /// Update reading progress
  Future<void> updateReadingProgress(String position) async {
    state = state.copyWith(
      lastReadPosition: position,
      lastReadDate: DateTime.now(),
    );
    await _saveSettings();
  }

  /// Update surah progress
  Future<void> updateSurahProgress(int surahNumber, double progress) async {
    final surahProgress = Map<int, double>.from(state.surahProgress);
    surahProgress[surahNumber] = progress;
    state = state.copyWith(surahProgress: surahProgress);
    await _saveSettings();
  }

  /// Reset to default settings
  Future<void> resetToDefaults() async {
    state = const QuranSettings();
    await _saveSettings();
  }

  /// Export settings as JSON string
  String exportSettings() {
    return json.encode(state.toJson());
  }

  /// Import settings from JSON string
  Future<bool> importSettings(String settingsJson) async {
    try {
      final Map<String, dynamic> data = json.decode(settingsJson);
      final importedSettings = QuranSettings.fromJson(data);
      state = importedSettings;
      await _saveSettings();
      return true;
    } catch (e) {
      print('Error importing quran settings: $e');
      return false;
    }
  }
}

/// Provider for quran settings
final quranSettingsProvider = StateNotifierProvider<QuranSettingsNotifier, QuranSettings>((ref) {
  return QuranSettingsNotifier();
});
