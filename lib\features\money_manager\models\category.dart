import 'package:flutter/material.dart';

/// Category model for Money Manager transactions
class Category {
  final String id;
  final String name;
  final String description;
  final CategoryType type;
  final String iconName;
  final Color color;
  final String? parentCategoryId;
  final bool isDefault;
  final bool isActive;
  final int sortOrder;
  final double? budgetLimit;
  final DateTime createdAt;
  final DateTime? modifiedAt;
  final Map<String, dynamic> metadata;

  const Category({
    required this.id,
    required this.name,
    this.description = '',
    required this.type,
    this.iconName = 'category',
    this.color = Colors.blue,
    this.parentCategoryId,
    this.isDefault = false,
    this.isActive = true,
    this.sortOrder = 0,
    this.budgetLimit,
    required this.createdAt,
    this.modifiedAt,
    this.metadata = const {},
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      type: CategoryType.values[json['type'] as int],
      iconName: json['icon_name'] as String? ?? 'category',
      color: Color(json['color'] as int? ?? Colors.blue.value),
      parentCategoryId: json['parent_category_id'] as String?,
      isDefault: json['is_default'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      sortOrder: json['sort_order'] as int? ?? 0,
      budgetLimit: (json['budget_limit'] as num?)?.toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      modifiedAt: json['modified_at'] != null 
          ? DateTime.parse(json['modified_at'] as String)
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'icon_name': iconName,
      'color': color.value,
      'parent_category_id': parentCategoryId,
      'is_default': isDefault,
      'is_active': isActive,
      'sort_order': sortOrder,
      'budget_limit': budgetLimit,
      'created_at': createdAt.toIso8601String(),
      'modified_at': modifiedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  Category copyWith({
    String? id,
    String? name,
    String? description,
    CategoryType? type,
    String? iconName,
    Color? color,
    String? parentCategoryId,
    bool? isDefault,
    bool? isActive,
    int? sortOrder,
    double? budgetLimit,
    DateTime? createdAt,
    DateTime? modifiedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      parentCategoryId: parentCategoryId ?? this.parentCategoryId,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      budgetLimit: budgetLimit ?? this.budgetLimit,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? DateTime.now(),
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isSubcategory => parentCategoryId != null;
  bool get hasBudgetLimit => budgetLimit != null && budgetLimit! > 0;
  
  String get formattedBudgetLimit {
    if (budgetLimit == null) return 'No limit';
    return '\$${budgetLimit!.toStringAsFixed(2)}';
  }

  IconData get icon {
    switch (iconName) {
      // Income categories
      case 'work':
        return Icons.work;
      case 'business':
        return Icons.business;
      case 'investment':
        return Icons.trending_up;
      case 'gift':
        return Icons.card_giftcard;
      case 'bonus':
        return Icons.star;
      case 'freelance':
        return Icons.laptop;
      case 'rental':
        return Icons.home;
      case 'dividend':
        return Icons.account_balance;
      
      // Expense categories
      case 'food':
        return Icons.restaurant;
      case 'groceries':
        return Icons.shopping_cart;
      case 'transport':
        return Icons.directions_car;
      case 'fuel':
        return Icons.local_gas_station;
      case 'entertainment':
        return Icons.movie;
      case 'shopping':
        return Icons.shopping_bag;
      case 'health':
        return Icons.local_hospital;
      case 'education':
        return Icons.school;
      case 'utilities':
        return Icons.electrical_services;
      case 'rent':
        return Icons.home;
      case 'insurance':
        return Icons.security;
      case 'phone':
        return Icons.phone;
      case 'internet':
        return Icons.wifi;
      case 'gym':
        return Icons.fitness_center;
      case 'travel':
        return Icons.flight;
      case 'clothing':
        return Icons.checkroom;
      case 'beauty':
        return Icons.face;
      case 'pet':
        return Icons.pets;
      case 'charity':
        return Icons.volunteer_activism;
      case 'tax':
        return Icons.receipt_long;
      case 'loan':
        return Icons.account_balance;
      case 'subscription':
        return Icons.subscriptions;
      case 'maintenance':
        return Icons.build;
      case 'office':
        return Icons.business_center;
      
      // General
      case 'other':
        return Icons.more_horiz;
      case 'transfer':
        return Icons.swap_horiz;
      default:
        return Icons.category;
    }
  }
}

enum CategoryType {
  income,
  expense,
}

extension CategoryTypeExtension on CategoryType {
  String get displayName {
    switch (this) {
      case CategoryType.income:
        return 'Income';
      case CategoryType.expense:
        return 'Expense';
    }
  }

  Color get defaultColor {
    switch (this) {
      case CategoryType.income:
        return Colors.green;
      case CategoryType.expense:
        return Colors.red;
    }
  }

  IconData get defaultIcon {
    switch (this) {
      case CategoryType.income:
        return Icons.add_circle;
      case CategoryType.expense:
        return Icons.remove_circle;
    }
  }
}

/// Predefined categories for quick setup
class DefaultCategories {
  static List<Category> get incomeCategories {
    final now = DateTime.now();
    return [
      Category(
        id: 'income_salary',
        name: 'Salary',
        description: 'Regular employment income',
        type: CategoryType.income,
        iconName: 'work',
        color: Colors.green,
        isDefault: true,
        sortOrder: 1,
        createdAt: now,
      ),
      Category(
        id: 'income_business',
        name: 'Business',
        description: 'Business income and profits',
        type: CategoryType.income,
        iconName: 'business',
        color: Colors.blue,
        isDefault: true,
        sortOrder: 2,
        createdAt: now,
      ),
      Category(
        id: 'income_investment',
        name: 'Investment',
        description: 'Investment returns and dividends',
        type: CategoryType.income,
        iconName: 'investment',
        color: Colors.purple,
        isDefault: true,
        sortOrder: 3,
        createdAt: now,
      ),
      Category(
        id: 'income_freelance',
        name: 'Freelance',
        description: 'Freelance and contract work',
        type: CategoryType.income,
        iconName: 'freelance',
        color: Colors.orange,
        isDefault: true,
        sortOrder: 4,
        createdAt: now,
      ),
      Category(
        id: 'income_gift',
        name: 'Gift',
        description: 'Gifts and bonuses received',
        type: CategoryType.income,
        iconName: 'gift',
        color: Colors.pink,
        isDefault: true,
        sortOrder: 5,
        createdAt: now,
      ),
      Category(
        id: 'income_other',
        name: 'Other Income',
        description: 'Other sources of income',
        type: CategoryType.income,
        iconName: 'other',
        color: Colors.grey,
        isDefault: true,
        sortOrder: 99,
        createdAt: now,
      ),
    ];
  }

  static List<Category> get expenseCategories {
    final now = DateTime.now();
    return [
      Category(
        id: 'expense_food',
        name: 'Food & Dining',
        description: 'Restaurants, takeout, and dining',
        type: CategoryType.expense,
        iconName: 'food',
        color: Colors.red,
        isDefault: true,
        sortOrder: 1,
        createdAt: now,
      ),
      Category(
        id: 'expense_groceries',
        name: 'Groceries',
        description: 'Grocery shopping and household items',
        type: CategoryType.expense,
        iconName: 'groceries',
        color: Colors.green,
        isDefault: true,
        sortOrder: 2,
        createdAt: now,
      ),
      Category(
        id: 'expense_transport',
        name: 'Transportation',
        description: 'Car, public transport, and travel',
        type: CategoryType.expense,
        iconName: 'transport',
        color: Colors.blue,
        isDefault: true,
        sortOrder: 3,
        createdAt: now,
      ),
      Category(
        id: 'expense_utilities',
        name: 'Utilities',
        description: 'Electricity, water, gas, internet',
        type: CategoryType.expense,
        iconName: 'utilities',
        color: Colors.orange,
        isDefault: true,
        sortOrder: 4,
        createdAt: now,
      ),
      Category(
        id: 'expense_entertainment',
        name: 'Entertainment',
        description: 'Movies, games, and leisure activities',
        type: CategoryType.expense,
        iconName: 'entertainment',
        color: Colors.purple,
        isDefault: true,
        sortOrder: 5,
        createdAt: now,
      ),
      Category(
        id: 'expense_shopping',
        name: 'Shopping',
        description: 'Clothing, electronics, and general shopping',
        type: CategoryType.expense,
        iconName: 'shopping',
        color: Colors.pink,
        isDefault: true,
        sortOrder: 6,
        createdAt: now,
      ),
      Category(
        id: 'expense_health',
        name: 'Healthcare',
        description: 'Medical expenses and health insurance',
        type: CategoryType.expense,
        iconName: 'health',
        color: Colors.teal,
        isDefault: true,
        sortOrder: 7,
        createdAt: now,
      ),
      Category(
        id: 'expense_education',
        name: 'Education',
        description: 'School, courses, and learning materials',
        type: CategoryType.expense,
        iconName: 'education',
        color: Colors.indigo,
        isDefault: true,
        sortOrder: 8,
        createdAt: now,
      ),
      Category(
        id: 'expense_other',
        name: 'Other Expenses',
        description: 'Miscellaneous expenses',
        type: CategoryType.expense,
        iconName: 'other',
        color: Colors.grey,
        isDefault: true,
        sortOrder: 99,
        createdAt: now,
      ),
    ];
  }

  static List<Category> get allDefaultCategories {
    return [...incomeCategories, ...expenseCategories];
  }
}
