import 'package:flutter/material.dart';

enum SupportedLanguage {
  english('en', 'English', 'English', TextDirection.ltr),
  arabic('ar', 'العربية', 'Arabic', TextDirection.rtl),
  spanish('es', 'Español', 'Spanish', TextDirection.ltr),
  french('fr', 'Français', 'French', TextDirection.ltr),
  german('de', 'Deutsch', 'German', TextDirection.ltr),
  chinese('zh', '中文', 'Chinese', TextDirection.ltr),
  japanese('ja', '日本語', 'Japanese', TextDirection.ltr),
  korean('ko', '한국어', 'Korean', TextDirection.ltr),
  russian('ru', 'Русский', 'Russian', TextDirection.ltr),
  portuguese('pt', 'Português', 'Portuguese', TextDirection.ltr),
  italian('it', 'Italiano', 'Italian', TextDirection.ltr),
  dutch('nl', 'Nederlands', 'Dutch', TextDirection.ltr),
  turkish('tr', 'Türkçe', 'Turkish', TextDirection.ltr),
  hindi('hi', 'हिन्दी', 'Hindi', TextDirection.ltr),
  urdu('ur', 'اردو', 'Urdu', TextDirection.rtl);

  const SupportedLanguage(this.code, this.nativeName, this.englishName, this.textDirection);

  final String code;
  final String nativeName;
  final String englishName;
  final TextDirection textDirection;

  Locale get locale => Locale(code);
  bool get isRTL => textDirection == TextDirection.rtl;
  bool get isLTR => textDirection == TextDirection.ltr;

  static SupportedLanguage fromCode(String code) {
    return SupportedLanguage.values.firstWhere(
      (lang) => lang.code == code,
      orElse: () => SupportedLanguage.english,
    );
  }

  static SupportedLanguage fromLocale(Locale locale) {
    return fromCode(locale.languageCode);
  }

  static List<SupportedLanguage> get rtlLanguages {
    return SupportedLanguage.values.where((lang) => lang.isRTL).toList();
  }

  static List<SupportedLanguage> get ltrLanguages {
    return SupportedLanguage.values.where((lang) => lang.isLTR).toList();
  }
}

class LocaleConfig {
  final SupportedLanguage language;
  final String? countryCode;
  final bool useSystemLocale;
  final bool fallbackToEnglish;
  final Map<String, String> customTranslations;

  const LocaleConfig({
    required this.language,
    this.countryCode,
    this.useSystemLocale = false,
    this.fallbackToEnglish = true,
    this.customTranslations = const {},
  });

  Locale get locale {
    if (countryCode != null) {
      return Locale(language.code, countryCode);
    }
    return language.locale;
  }

  TextDirection get textDirection => language.textDirection;
  bool get isRTL => language.isRTL;
  bool get isLTR => language.isLTR;

  LocaleConfig copyWith({
    SupportedLanguage? language,
    String? countryCode,
    bool? useSystemLocale,
    bool? fallbackToEnglish,
    Map<String, String>? customTranslations,
  }) {
    return LocaleConfig(
      language: language ?? this.language,
      countryCode: countryCode ?? this.countryCode,
      useSystemLocale: useSystemLocale ?? this.useSystemLocale,
      fallbackToEnglish: fallbackToEnglish ?? this.fallbackToEnglish,
      customTranslations: customTranslations ?? this.customTranslations,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language.code,
      'countryCode': countryCode,
      'useSystemLocale': useSystemLocale,
      'fallbackToEnglish': fallbackToEnglish,
      'customTranslations': customTranslations,
    };
  }

  factory LocaleConfig.fromJson(Map<String, dynamic> json) {
    return LocaleConfig(
      language: SupportedLanguage.fromCode(json['language'] ?? 'en'),
      countryCode: json['countryCode'],
      useSystemLocale: json['useSystemLocale'] ?? false,
      fallbackToEnglish: json['fallbackToEnglish'] ?? true,
      customTranslations: Map<String, String>.from(json['customTranslations'] ?? {}),
    );
  }

  factory LocaleConfig.system() {
    return const LocaleConfig(
      language: SupportedLanguage.english,
      useSystemLocale: true,
    );
  }

  factory LocaleConfig.english() {
    return const LocaleConfig(
      language: SupportedLanguage.english,
    );
  }

  factory LocaleConfig.arabic() {
    return const LocaleConfig(
      language: SupportedLanguage.arabic,
    );
  }

  @override
  String toString() {
    return 'LocaleConfig(language: ${language.code}, countryCode: $countryCode, isRTL: $isRTL)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocaleConfig &&
        other.language == language &&
        other.countryCode == countryCode &&
        other.useSystemLocale == useSystemLocale &&
        other.fallbackToEnglish == fallbackToEnglish;
  }

  @override
  int get hashCode {
    return Object.hash(language, countryCode, useSystemLocale, fallbackToEnglish);
  }
}

// Translation key constants
class TranslationKeys {
  // Common
  static const String appName = 'app.name';
  static const String ok = 'common.ok';
  static const String cancel = 'common.cancel';
  static const String save = 'common.save';
  static const String delete = 'common.delete';
  static const String edit = 'common.edit';
  static const String add = 'common.add';
  static const String search = 'common.search';
  static const String loading = 'common.loading';
  static const String error = 'common.error';
  static const String success = 'common.success';
  static const String retry = 'common.retry';
  static const String close = 'common.close';
  static const String back = 'common.back';
  static const String next = 'common.next';
  static const String previous = 'common.previous';
  static const String done = 'common.done';
  static const String yes = 'common.yes';
  static const String no = 'common.no';

  // Navigation
  static const String dashboard = 'navigation.dashboard';
  static const String notes = 'navigation.notes';
  static const String todos = 'navigation.todos';
  static const String voiceMemos = 'navigation.voice_memos';
  static const String dhikr = 'navigation.dhikr';
  static const String tools = 'navigation.tools';
  static const String money = 'navigation.money';
  static const String settings = 'navigation.settings';

  // Notes
  static const String notesTitle = 'notes.title';
  static const String createNote = 'notes.create';
  static const String editNote = 'notes.edit';
  static const String noteTitle = 'notes.note_title';
  static const String noteContent = 'notes.content';
  static const String noNotes = 'notes.no_notes';
  static const String searchNotes = 'notes.search';

  // Todos
  static const String todosTitle = 'todos.title';
  static const String createTodo = 'todos.create';
  static const String editTodo = 'todos.edit';
  static const String todoTitle = 'todos.todo_title';
  static const String todoDescription = 'todos.description';
  static const String dueDate = 'todos.due_date';
  static const String completed = 'todos.completed';
  static const String pending = 'todos.pending';
  static const String noTodos = 'todos.no_todos';

  // Voice Memos
  static const String voiceMemosTitle = 'voice_memos.title';
  static const String record = 'voice_memos.record';
  static const String stopRecording = 'voice_memos.stop_recording';
  static const String playback = 'voice_memos.playback';
  static const String transcription = 'voice_memos.transcription';
  static const String noVoiceMemos = 'voice_memos.no_memos';

  // Dhikr
  static const String dhikrTitle = 'dhikr.title';
  static const String dhikrCounter = 'dhikr.counter';
  static const String dhikrRoutines = 'dhikr.routines';
  static const String createRoutine = 'dhikr.create_routine';
  static const String startDhikr = 'dhikr.start';
  static const String resetCounter = 'dhikr.reset';

  // Settings
  static const String settingsTitle = 'settings.title';
  static const String language = 'settings.language';
  static const String theme = 'settings.theme';
  static const String notifications = 'settings.notifications';
  static const String privacy = 'settings.privacy';
  static const String about = 'settings.about';

  // Errors
  static const String networkError = 'errors.network';
  static const String serverError = 'errors.server';
  static const String validationError = 'errors.validation';
  static const String permissionError = 'errors.permission';
  static const String notFoundError = 'errors.not_found';
  static const String timeoutError = 'errors.timeout';
  static const String unknownError = 'errors.unknown';

  // Sync
  static const String syncTitle = 'sync.title';
  static const String syncing = 'sync.syncing';
  static const String syncComplete = 'sync.complete';
  static const String syncFailed = 'sync.failed';
  static const String offline = 'sync.offline';
  static const String online = 'sync.online';
  static const String conflicts = 'sync.conflicts';
  static const String resolveConflicts = 'sync.resolve_conflicts';

  // Accessibility
  static const String accessibilitySettings = 'accessibility.settings';
  static const String highContrast = 'accessibility.high_contrast';
  static const String largeText = 'accessibility.large_text';
  static const String reduceMotion = 'accessibility.reduce_motion';
  static const String screenReader = 'accessibility.screen_reader';

  // Time and dates
  static const String today = 'time.today';
  static const String yesterday = 'time.yesterday';
  static const String tomorrow = 'time.tomorrow';
  static const String thisWeek = 'time.this_week';
  static const String lastWeek = 'time.last_week';
  static const String thisMonth = 'time.this_month';
  static const String lastMonth = 'time.last_month';

  // Validation messages
  static const String requiredField = 'validation.required';
  static const String invalidEmail = 'validation.invalid_email';
  static const String passwordTooShort = 'validation.password_too_short';
  static const String passwordsDoNotMatch = 'validation.passwords_do_not_match';
  static const String invalidUrl = 'validation.invalid_url';
  static const String invalidPhoneNumber = 'validation.invalid_phone';

  // File operations
  static const String exportData = 'files.export_data';
  static const String importData = 'files.import_data';
  static const String backup = 'files.backup';
  static const String restore = 'files.restore';
  static const String fileNotFound = 'files.not_found';
  static const String fileTooLarge = 'files.too_large';
  static const String invalidFileFormat = 'files.invalid_format';
}
