import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category.dart';
import '../providers/category_provider.dart';
import '../../../shared/providers/user_provider.dart';

class CategoryEditorScreen extends ConsumerStatefulWidget {
  final MoneyCategory? category;
  final CategoryType? initialType;

  const CategoryEditorScreen({
    super.key,
    this.category,
    this.initialType,
  });

  @override
  ConsumerState<CategoryEditorScreen> createState() => _CategoryEditorScreenState();
}

class _CategoryEditorScreenState extends ConsumerState<CategoryEditorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _budgetLimitController = TextEditingController();
  final _alertThresholdController = TextEditingController();

  late CategoryType _selectedType;
  late IconData _selectedIcon;
  late Color _selectedColor;
  bool _enableBudgetAlert = false;
  double _alertThreshold = 0.8;

  final List<IconData> _availableIcons = [
    Icons.category,
    Icons.work,
    Icons.laptop,
    Icons.trending_up,
    Icons.attach_money,
    Icons.restaurant,
    Icons.directions_car,
    Icons.shopping_bag,
    Icons.movie,
    Icons.receipt_long,
    Icons.local_hospital,
    Icons.school,
    Icons.flight,
    Icons.spa,
    Icons.home,
    Icons.security,
    Icons.account_balance,
    Icons.card_giftcard,
    Icons.more_horiz,
    Icons.swap_horiz,
    Icons.savings,
    Icons.business,
    Icons.fitness_center,
    Icons.pets,
    Icons.child_care,
    Icons.local_gas_station,
    Icons.phone,
    Icons.wifi,
    Icons.electric_bolt,
    Icons.water_drop,
  ];

  final List<Color> _availableColors = [
    Colors.blue,
    Colors.green,
    Colors.red,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.pink,
    Colors.indigo,
    Colors.amber,
    Colors.cyan,
    Colors.lime,
    Colors.deepOrange,
    Colors.deepPurple,
    Colors.lightGreen,
    Colors.brown,
    Colors.blueGrey,
    Colors.grey,
  ];

  @override
  void initState() {
    super.initState();
    
    if (widget.category != null) {
      _loadCategoryData();
    } else {
      _selectedType = widget.initialType ?? CategoryType.expense;
      _selectedIcon = Icons.category;
      _selectedColor = Colors.blue;
      _alertThresholdController.text = '80';
    }
  }

  void _loadCategoryData() {
    final category = widget.category!;
    _nameController.text = category.name;
    _descriptionController.text = category.description;
    _selectedType = category.type;
    _selectedIcon = category.icon;
    _selectedColor = category.color;
    _budgetLimitController.text = category.budgetLimit > 0 ? category.budgetLimit.toString() : '';
    _enableBudgetAlert = category.enableBudgetAlert;
    _alertThreshold = category.alertThreshold;
    _alertThresholdController.text = (category.alertThreshold * 100).toStringAsFixed(0);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _budgetLimitController.dispose();
    _alertThresholdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.category == null ? 'Add Category' : 'Edit Category'),
        actions: [
          IconButton(
            onPressed: _saveCategory,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Preview Card
              Card(
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _selectedColor,
                    child: Icon(_selectedIcon, color: Colors.white),
                  ),
                  title: Text(
                    _nameController.text.isEmpty ? 'Category Name' : _nameController.text,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(
                    _descriptionController.text.isEmpty 
                        ? 'Category description' 
                        : _descriptionController.text,
                  ),
                  trailing: Chip(
                    label: Text(_selectedType.name.toUpperCase()),
                    backgroundColor: _getTypeColor(_selectedType),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Basic Information
              Text(
                'Basic Information',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Category Name',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.label),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a category name';
                  }
                  return null;
                },
                onChanged: (value) => setState(() {}),
              ),
              
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 2,
                onChanged: (value) => setState(() {}),
              ),
              
              const SizedBox(height: 16),
              
              // Category Type
              DropdownButtonFormField<CategoryType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Category Type',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items: CategoryType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Row(
                      children: [
                        Icon(_getTypeIcon(type), color: _getTypeColor(type)),
                        const SizedBox(width: 8),
                        Text(type.name.toUpperCase()),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              
              const SizedBox(height: 24),
              
              // Appearance
              Text(
                'Appearance',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              // Icon Selection
              Text(
                'Icon',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Container(
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: GridView.builder(
                  padding: const EdgeInsets.all(8),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 6,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: _availableIcons.length,
                  itemBuilder: (context, index) {
                    final icon = _availableIcons[index];
                    final isSelected = icon == _selectedIcon;
                    
                    return GestureDetector(
                      onTap: () => setState(() => _selectedIcon = icon),
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected ? _selectedColor : Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                          border: isSelected ? Border.all(color: _selectedColor, width: 2) : null,
                        ),
                        child: Icon(
                          icon,
                          color: isSelected ? Colors.white : Colors.grey[600],
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Color Selection
              Text(
                'Color',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _availableColors.map((color) {
                  final isSelected = color == _selectedColor;
                  return GestureDetector(
                    onTap: () => setState(() => _selectedColor = color),
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                        border: isSelected ? Border.all(color: Colors.black, width: 3) : null,
                      ),
                      child: isSelected ? const Icon(Icons.check, color: Colors.white) : null,
                    ),
                  );
                }).toList(),
              ),
              
              const SizedBox(height: 24),
              
              // Budget Settings (only for expense categories)
              if (_selectedType == CategoryType.expense) ...[
                Text(
                  'Budget Settings',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _budgetLimitController,
                  decoration: const InputDecoration(
                    labelText: 'Monthly Budget Limit (Optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.attach_money),
                    suffixText: 'USD',
                  ),
                  keyboardType: TextInputType.number,
                ),
                
                const SizedBox(height: 16),
                
                SwitchListTile(
                  title: const Text('Enable Budget Alerts'),
                  subtitle: const Text('Get notified when approaching budget limit'),
                  value: _enableBudgetAlert,
                  onChanged: (value) => setState(() => _enableBudgetAlert = value),
                ),
                
                if (_enableBudgetAlert) ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _alertThresholdController,
                    decoration: const InputDecoration(
                      labelText: 'Alert Threshold',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.warning),
                      suffixText: '%',
                      helperText: 'Alert when this percentage of budget is reached',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final threshold = double.tryParse(value);
                        if (threshold == null || threshold < 1 || threshold > 100) {
                          return 'Please enter a value between 1 and 100';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return Icons.trending_up;
      case CategoryType.expense:
        return Icons.trending_down;
      case CategoryType.transfer:
        return Icons.swap_horiz;
    }
  }

  Color _getTypeColor(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return Colors.green;
      case CategoryType.expense:
        return Colors.red;
      case CategoryType.transfer:
        return Colors.blue;
    }
  }

  void _saveCategory() async {
    if (_formKey.currentState!.validate()) {
      final userProfile = ref.read(userProfileProvider);
      if (userProfile == null) return;

      final budgetLimit = _budgetLimitController.text.isNotEmpty 
          ? double.tryParse(_budgetLimitController.text) ?? 0.0 
          : 0.0;
      
      final alertThreshold = _alertThresholdController.text.isNotEmpty 
          ? (double.tryParse(_alertThresholdController.text) ?? 80.0) / 100.0 
          : 0.8;

      if (widget.category == null) {
        // Create new category
        final category = MoneyCategory.create(
          name: _nameController.text,
          type: _selectedType,
          userId: userProfile.id.toString(),
          description: _descriptionController.text,
          icon: _selectedIcon,
          color: _selectedColor,
          budgetLimit: budgetLimit,
          enableBudgetAlert: _enableBudgetAlert,
          alertThreshold: alertThreshold,
        );

        await ref.read(categoriesProvider.notifier).addCategory(category);
      } else {
        // Update existing category
        final category = widget.category!;
        category.name = _nameController.text;
        category.description = _descriptionController.text;
        category.type = _selectedType;
        category.icon = _selectedIcon;
        category.color = _selectedColor;
        category.budgetLimit = budgetLimit;
        category.enableBudgetAlert = _enableBudgetAlert;
        category.alertThreshold = alertThreshold;
        category.updateTimestamp();

        await ref.read(categoriesProvider.notifier).updateCategory(category);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.category == null 
                ? 'Category created successfully' 
                : 'Category updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }
}
