import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/tool_definition.dart';
import '../models/spreadsheet_cell.dart';
import '../services/tool_storage_service.dart';
import 'tool_save_screen.dart';
import 'tool_run_screen.dart';

/// Screen showing all saved custom tools with CRUD operations
class ToolListScreen extends ConsumerStatefulWidget {
  const ToolListScreen({super.key});

  @override
  ConsumerState<ToolListScreen> createState() => _ToolListScreenState();
}

class _ToolListScreenState extends ConsumerState<ToolListScreen> {
  final _storageService = ToolStorageService();
  final _searchController = TextEditingController();
  
  List<ToolDefinition> _tools = [];
  List<ToolDefinition> _filteredTools = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedCategory = 'All';

  final List<String> _categories = [
    'All',
    'Custom',
    'Calculator',
    'Converter',
    'Finance',
    'Utility',
    'Education',
    'Health',
    'Productivity',
  ];

  @override
  void initState() {
    super.initState();
    _loadTools();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterTools();
    });
  }

  Future<void> _loadTools() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final tools = await _storageService.getUserTools('user_001'); // TODO: Get actual user ID
      setState(() {
        _tools = tools;
        _filterTools();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load tools: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterTools() {
    _filteredTools = _tools.where((tool) {
      final matchesSearch = _searchQuery.isEmpty ||
          tool.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          tool.description.toLowerCase().contains(_searchQuery.toLowerCase());
      
      final matchesCategory = _selectedCategory == 'All' ||
          tool.name.toLowerCase().contains(_selectedCategory.toLowerCase());
      
      return matchesSearch && matchesCategory;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Tools'),
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        actions: [
          IconButton(
            onPressed: _showToolStats,
            icon: const Icon(Icons.analytics),
            tooltip: 'Statistics',
          ),
          IconButton(
            onPressed: _loadTools,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Bar
          _buildSearchAndFilter(),
          
          // Tools List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredTools.isEmpty
                    ? _buildEmptyState()
                    : _buildToolsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go('/tools/create'),
        icon: const Icon(Icons.add),
        label: const Text('Create Tool'),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search tools...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Category Filter
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _categories.map((category) {
                final isSelected = _selectedCategory == category;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                        _filterTools();
                      });
                    },
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.build_circle_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != 'All'
                ? 'No tools match your search'
                : 'No tools created yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != 'All'
                ? 'Try adjusting your search or filters'
                : 'Create your first custom tool to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),

          ),
          const SizedBox(height: 24),
          if (_searchQuery.isEmpty && _selectedCategory == 'All')
            ElevatedButton.icon(
              onPressed: () => context.go('/tools/create'),
              icon: const Icon(Icons.add),
              label: const Text('Create Your First Tool'),
            ),
        ],
      ),
    );
  }

  Widget _buildToolsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredTools.length,
      itemBuilder: (context, index) {
        final tool = _filteredTools[index];
        return _buildToolCard(tool);
      },
    );
  }

  Widget _buildToolCard(ToolDefinition tool) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _runTool(tool),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Tool Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.build_circle,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Tool Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                tool.name,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (tool.isPasswordProtected)
                              Icon(
                                Icons.lock,
                                size: 16,
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                          ],
                        ),
                        Text(
                          tool.description,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  // Actions Menu
                  PopupMenuButton<String>(
                    onSelected: (action) => _handleToolAction(action, tool),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'run',
                        child: ListTile(
                          leading: Icon(Icons.play_arrow),
                          title: Text('Run'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Full Edit'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'quick_edit',
                        child: ListTile(
                          leading: Icon(Icons.edit_outlined),
                          title: Text('Quick Edit'),
                          subtitle: Text('Edit UI without rebuilding backend'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: ListTile(
                          leading: Icon(Icons.copy),
                          title: Text('Duplicate'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'export',
                        child: ListTile(
                          leading: Icon(Icons.download),
                          title: Text('Export'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text('Delete', style: TextStyle(color: Colors.red)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Tool Stats
              Row(
                children: [
                  _buildStatChip(
                    Icons.grid_on,
                    '${tool.getParsedBackendData().length} cells',
                    Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  _buildStatChip(
                    Icons.access_time,
                    _formatDate(tool.updatedAt),
                    Colors.green,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _handleToolAction(String action, ToolDefinition tool) {
    switch (action) {
      case 'run':
        _runTool(tool);
        break;
      case 'edit':
        _editTool(tool);
        break;
      case 'quick_edit':
        _quickEditTool(tool);
        break;
      case 'duplicate':
        _duplicateTool(tool);
        break;
      case 'export':
        _exportTool(tool);
        break;
      case 'delete':
        _deleteTool(tool);
        break;
    }
  }

  void _runTool(ToolDefinition tool) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ToolRunScreen(tool: tool),
      ),
    );
  }

  void _editTool(ToolDefinition tool) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ToolSaveScreen(
          backendData: _parseBackendData(tool.backendData),
          existingTool: tool,
          onSaved: (toolId) {
            _loadTools(); // Refresh the list
          },
        ),
      ),
    );
  }

  /// Quick edit tool - navigate directly to UI Builder without rebuilding backend
  void _quickEditTool(ToolDefinition tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.edit_outlined,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text('Quick Edit'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Edit "${tool.name}" without rebuilding the backend spreadsheet.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Text(
              'What would you like to edit?',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildQuickEditOption(
              context,
              'UI Interface',
              'Modify the user interface components',
              Icons.design_services,
              () {
                Navigator.of(context).pop();
                context.go('/tools/ui-builder/${tool.id}');
              },
            ),
            _buildQuickEditOption(
              context,
              'Tool Settings',
              'Update name, description, and category',
              Icons.settings,
              () {
                Navigator.of(context).pop();
                _showToolSettingsDialog(tool);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _editTool(tool); // Fall back to full edit
            },
            icon: const Icon(Icons.edit),
            label: const Text('Full Edit'),
          ),
        ],
      ),
    );
  }

  /// Build quick edit option
  Widget _buildQuickEditOption(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                size: 20,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// Show tool settings dialog for quick editing
  void _showToolSettingsDialog(ToolDefinition tool) {
    final nameController = TextEditingController(text: tool.name);
    final descriptionController = TextEditingController(text: tool.description);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tool Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Tool Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              nameController.dispose();
              descriptionController.dispose();
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              // TODO: Update tool settings
              nameController.dispose();
              descriptionController.dispose();
              Navigator.of(context).pop();

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Tool settings updated'),
                  backgroundColor: Colors.green,
                ),
              );

              _loadTools(); // Refresh the list
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Map<String, SpreadsheetCell> _parseBackendData(String backendData) {
    try {
      final jsonData = jsonDecode(backendData) as Map<String, dynamic>;
      final cellData = <String, SpreadsheetCell>{};

      for (final entry in jsonData.entries) {
        try {
          cellData[entry.key] = SpreadsheetCell.fromJson(entry.value as Map<String, dynamic>);
        } catch (e) {
          // Skip invalid entries
          continue;
        }
      }

      return cellData;
    } catch (e) {
      return {};
    }
  }

  Future<void> _duplicateTool(ToolDefinition tool) async {
    try {
      await _storageService.duplicateTool(tool.id!, 'user_001');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tool duplicated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        _loadTools();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to duplicate tool: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportTool(ToolDefinition tool) async {
    try {
      final exportData = await _storageService.exportTool(tool.id!);
      
      // TODO: Implement actual file export
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tool exported successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to export tool: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteTool(ToolDefinition tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tool'),
        content: Text('Are you sure you want to delete "${tool.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              try {
                await _storageService.deleteTool(tool.id!);
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Tool deleted successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadTools();
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete tool: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _showToolStats() async {
    try {
      final stats = await _storageService.getToolStats('user_001');
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Tool Statistics'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Total Tools: ${stats['totalTools']}'),
                Text('Recent Tools: ${stats['recentTools']}'),
                Text('Protected Tools: ${stats['protectedTools']}'),
                if (stats['oldestTool'] != null)
                  Text('Oldest Tool: ${_formatDate(stats['oldestTool'])}'),
                if (stats['newestTool'] != null)
                  Text('Newest Tool: ${_formatDate(stats['newestTool'])}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load statistics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
