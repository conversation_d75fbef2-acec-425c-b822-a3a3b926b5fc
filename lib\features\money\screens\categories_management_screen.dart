import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/base_screen.dart';
import '../models/category.dart';
import '../providers/category_provider.dart';
import 'category_editor_screen.dart';

class CategoriesManagementScreen extends ConsumerStatefulWidget {
  const CategoriesManagementScreen({super.key});

  @override
  ConsumerState<CategoriesManagementScreen> createState() => _CategoriesManagementScreenState();
}

class _CategoriesManagementScreenState extends ConsumerState<CategoriesManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MoneyBaseScreen(
      title: 'Categories Management',
      actions: [
        IconButton(
          onPressed: _addCategory,
          icon: const Icon(Icons.add),
          tooltip: 'Add Category',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.trending_up), text: 'Income'),
          Tab(icon: Icon(Icons.trending_down), text: 'Expense'),
          Tab(icon: Icon(Icons.swap_horiz), text: 'Transfer'),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoriesList(CategoryType.income),
          _buildCategoriesList(CategoryType.expense),
          _buildCategoriesList(CategoryType.transfer),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addCategory,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCategoriesList(CategoryType type) {
    return Consumer(
      builder: (context, ref, child) {
        final categories = ref.watch(categoriesByTypeProvider(type));
        final categoryStats = ref.watch(categoryTransactionStatsProvider);

        if (categories.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getTypeIcon(type),
                  size: 64,
                  color: Theme.of(context).colorScheme.outline,
                ),
                const SizedBox(height: 16),
                Text(
                  'No ${type.name} categories',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Add your first ${type.name} category to get started',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _addCategory(type),
                  icon: const Icon(Icons.add),
                  label: Text('Add ${type.name.capitalize()} Category'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            final stats = categoryStats[category.id];
            
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: category.color,
                  child: Icon(
                    category.icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                title: Text(
                  category.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (category.description.isNotEmpty)
                      Text(category.description),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        if (stats != null) ...[
                          Text(
                            '${stats['transactionCount']} transactions',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '\$${stats['totalAmount'].toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: type == CategoryType.income 
                                  ? Colors.green 
                                  : Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                        if (category.budgetLimit > 0 && type == CategoryType.expense) ...[
                          const SizedBox(width: 8),
                          Text(
                            'Budget: \$${category.budgetLimit.toStringAsFixed(0)}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ],
                    ),
                    if (category.budgetLimit > 0 && type == CategoryType.expense && stats != null) ...[
                      const SizedBox(height: 8),
                      _buildBudgetProgress(category, stats['totalAmount']),
                    ],
                  ],
                ),
                trailing: PopupMenuButton<String>(
                  onSelected: (value) => _handleCategoryAction(value, category),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'duplicate',
                      child: Row(
                        children: [
                          Icon(Icons.copy, color: Colors.orange),
                          SizedBox(width: 8),
                          Text('Duplicate'),
                        ],
                      ),
                    ),
                    if (!category.isDefault)
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete'),
                          ],
                        ),
                      ),
                  ],
                ),
                onTap: () => _editCategory(category),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildBudgetProgress(MoneyCategory category, double spent) {
    final percentage = category.budgetLimit > 0 ? (spent / category.budgetLimit).clamp(0.0, 1.0) : 0.0;
    final isOverBudget = spent > category.budgetLimit;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Spent: \$${spent.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              '${(percentage * 100).toStringAsFixed(0)}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isOverBudget ? Colors.red : null,
                fontWeight: isOverBudget ? FontWeight.bold : null,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            isOverBudget ? Colors.red : category.color,
          ),
        ),
      ],
    );
  }

  IconData _getTypeIcon(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return Icons.trending_up;
      case CategoryType.expense:
        return Icons.trending_down;
      case CategoryType.transfer:
        return Icons.swap_horiz;
    }
  }

  void _addCategory([CategoryType? type]) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CategoryEditorScreen(
          initialType: type ?? CategoryType.values[_tabController.index],
        ),
      ),
    );
  }

  void _editCategory(MoneyCategory category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CategoryEditorScreen(category: category),
      ),
    );
  }

  void _handleCategoryAction(String action, MoneyCategory category) {
    switch (action) {
      case 'edit':
        _editCategory(category);
        break;
      case 'duplicate':
        _duplicateCategory(category);
        break;
      case 'delete':
        _deleteCategory(category);
        break;
    }
  }

  void _duplicateCategory(MoneyCategory category) async {
    final duplicated = MoneyCategory.create(
      name: '${category.name} (Copy)',
      type: category.type,
      userId: category.userId,
      description: category.description,
      icon: category.icon,
      color: category.color,
      budgetLimit: category.budgetLimit,
      enableBudgetAlert: category.enableBudgetAlert,
      alertThreshold: category.alertThreshold,
    );

    await ref.read(categoriesProvider.notifier).addCategory(duplicated);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Category "${category.name}" duplicated'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _deleteCategory(MoneyCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"?\n\nThis action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(categoriesProvider.notifier).deleteCategory(category.id);
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Category "${category.name}" deleted'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
