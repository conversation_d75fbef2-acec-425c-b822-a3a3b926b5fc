import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/notes_provider.dart';

class NotesSearchBar extends ConsumerStatefulWidget {
  const NotesSearchBar({super.key});

  @override
  ConsumerState<NotesSearchBar> createState() => _NotesSearchBarState();
}

class _NotesSearchBarState extends ConsumerState<NotesSearchBar> {
  final TextEditingController _controller = TextEditingController();
  bool _isExpanded = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: TextField(
        controller: _controller,
        decoration: InputDecoration(
          hintText: 'Search notes, tags, or content...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _controller.text.isNotEmpty
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_isExpanded)
                      IconButton(
                        onPressed: _showAdvancedSearch,
                        icon: const Icon(Icons.tune),
                        tooltip: 'Advanced Search',
                      ),
                    IconButton(
                      onPressed: _clearSearch,
                      icon: const Icon(Icons.clear),
                      tooltip: 'Clear Search',
                    ),
                  ],
                )
              : IconButton(
                  onPressed: () {
                    setState(() => _isExpanded = !_isExpanded);
                  },
                  icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  tooltip: _isExpanded ? 'Collapse' : 'Advanced Search',
                ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          ref.read(searchQueryProvider.notifier).state = value;
          setState(() {});
        },
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            _addToSearchHistory(value);
          }
        },
      ),
    );
  }

  void _clearSearch() {
    _controller.clear();
    ref.read(searchQueryProvider.notifier).state = '';
    setState(() {});
  }

  void _showAdvancedSearch() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => const AdvancedSearchSheet(),
    );
  }

  void _addToSearchHistory(String query) {
    // TODO: Implement search history
  }
}

class AdvancedSearchSheet extends ConsumerStatefulWidget {
  const AdvancedSearchSheet({super.key});

  @override
  ConsumerState<AdvancedSearchSheet> createState() => _AdvancedSearchSheetState();
}

class _AdvancedSearchSheetState extends ConsumerState<AdvancedSearchSheet> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController();
  String? _selectedCategory;
  DateTimeRange? _dateRange;
  bool _pinnedOnly = false;
  bool _favoritesOnly = false;

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Advanced Search',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Title search
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Title contains',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.title),
            ),
          ),
          const SizedBox(height: 12),
          
          // Content search
          TextField(
            controller: _contentController,
            decoration: const InputDecoration(
              labelText: 'Content contains',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.text_fields),
            ),
          ),
          const SizedBox(height: 12),
          
          // Tags search
          TextField(
            controller: _tagsController,
            decoration: const InputDecoration(
              labelText: 'Tags (comma-separated)',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.local_offer),
            ),
          ),
          const SizedBox(height: 12),
          
          // Category filter
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: const InputDecoration(
              labelText: 'Category',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.category),
            ),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Categories')),
              ...ref.read(notesProvider.notifier).getAllCategories().map(
                (category) => DropdownMenuItem(
                  value: category,
                  child: Text(category),
                ),
              ),
            ],
            onChanged: (value) => setState(() => _selectedCategory = value),
          ),
          const SizedBox(height: 12),
          
          // Date range
          InkWell(
            onTap: _selectDateRange,
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'Date Range',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.date_range),
              ),
              child: Text(
                _dateRange == null
                    ? 'Any date'
                    : '${_formatDate(_dateRange!.start)} - ${_formatDate(_dateRange!.end)}',
              ),
            ),
          ),
          const SizedBox(height: 12),
          
          // Filters
          Row(
            children: [
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Pinned only'),
                  value: _pinnedOnly,
                  onChanged: (value) => setState(() => _pinnedOnly = value ?? false),
                  dense: true,
                ),
              ),
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Favorites only'),
                  value: _favoritesOnly,
                  onChanged: (value) => setState(() => _favoritesOnly = value ?? false),
                  dense: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _clearFilters,
                  child: const Text('Clear All'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _applyFilters,
                  child: const Text('Search'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _selectDateRange() async {
    final range = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
    );
    
    if (range != null) {
      setState(() => _dateRange = range);
    }
  }

  void _clearFilters() {
    setState(() {
      _titleController.clear();
      _contentController.clear();
      _tagsController.clear();
      _selectedCategory = null;
      _dateRange = null;
      _pinnedOnly = false;
      _favoritesOnly = false;
    });
  }

  void _applyFilters() {
    // TODO: Implement advanced search logic
    // For now, just use the basic search
    final query = [
      _titleController.text,
      _contentController.text,
      _tagsController.text,
    ].where((text) => text.isNotEmpty).join(' ');
    
    ref.read(searchQueryProvider.notifier).state = query;
    Navigator.of(context).pop();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
