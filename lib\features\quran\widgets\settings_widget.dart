import 'package:flutter/material.dart';
import '../models/quran_models.dart';

/// Widget for Quran reading preferences and settings
class SettingsWidget extends StatefulWidget {
  final QuranPreferences preferences;
  final Function(QuranPreferences preferences) onPreferencesChanged;

  const SettingsWidget({
    super.key,
    required this.preferences,
    required this.onPreferencesChanged,
  });

  @override
  State<SettingsWidget> createState() => _SettingsWidgetState();
}

class _SettingsWidgetState extends State<SettingsWidget> {
  late QuranPreferences _currentPreferences;

  @override
  void initState() {
    super.initState();
    _currentPreferences = widget.preferences;
  }

  void _updatePreferences(QuranPreferences newPreferences) {
    setState(() {
      _currentPreferences = newPreferences;
    });
    widget.onPreferencesChanged(newPreferences);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        _buildHeader(),
        
        // Settings content
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Text settings
                _buildSection(
                  'Text Settings',
                  Icons.text_fields,
                  [
                    _buildFontSizeSlider(),
                    const SizedBox(height: 16),
                    _buildFontFamilySelector(),
                    const SizedBox(height: 16),
                    _buildLineSpacingSlider(),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Display settings
                _buildSection(
                  'Display Settings',
                  Icons.visibility,
                  [
                    _buildShowTranslationSwitch(),
                    const SizedBox(height: 8),
                    _buildShowVerseNumbersSwitch(),
                    const SizedBox(height: 16),
                    _buildThemeSelector(),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Reading settings
                _buildSection(
                  'Reading Settings',
                  Icons.menu_book,
                  [
                    _buildNightModeSwitch(),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Preview
                _buildPreviewSection(),
              ],
            ),
          ),
        ),
        
        // Action buttons
        _buildActionButtons(),
      ],
    );
  }

  /// Build header
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.settings,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Reading Settings',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build section with title and content
  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  /// Build font size slider
  Widget _buildFontSizeSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Font Size',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${_currentPreferences.fontSize.toInt()}px',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        Slider(
          value: _currentPreferences.fontSize,
          min: 12,
          max: 32,
          divisions: 20,
          onChanged: (value) {
            _updatePreferences(_currentPreferences.copyWith(fontSize: value));
          },
        ),
      ],
    );
  }

  /// Build font family selector
  Widget _buildFontFamilySelector() {
    const fontFamilies = [
      'Amiri',
      'Noto Sans Arabic',
      'Scheherazade',
      'Default',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Font Family',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _currentPreferences.fontFamily,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: fontFamilies.map((font) {
            return DropdownMenuItem(
              value: font,
              child: Text(font),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updatePreferences(_currentPreferences.copyWith(fontFamily: value));
            }
          },
        ),
      ],
    );
  }

  /// Build line spacing slider
  Widget _buildLineSpacingSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Line Spacing',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${_currentPreferences.lineSpacing.toStringAsFixed(1)}x',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        Slider(
          value: _currentPreferences.lineSpacing,
          min: 1.0,
          max: 3.0,
          divisions: 20,
          onChanged: (value) {
            _updatePreferences(_currentPreferences.copyWith(lineSpacing: value));
          },
        ),
      ],
    );
  }

  /// Build show translation switch
  Widget _buildShowTranslationSwitch() {
    return SwitchListTile(
      title: const Text('Show Translation'),
      subtitle: const Text('Display English translation below Arabic text'),
      value: _currentPreferences.showTranslation,
      onChanged: (value) {
        _updatePreferences(_currentPreferences.copyWith(showTranslation: value));
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  /// Build show verse numbers switch
  Widget _buildShowVerseNumbersSwitch() {
    return SwitchListTile(
      title: const Text('Show Verse Numbers'),
      subtitle: const Text('Display verse numbers in the text'),
      value: _currentPreferences.showVerseNumbers,
      onChanged: (value) {
        _updatePreferences(_currentPreferences.copyWith(showVerseNumbers: value));
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  /// Build theme selector
  Widget _buildThemeSelector() {
    const themes = [
      {'value': 'light', 'name': 'Light'},
      {'value': 'dark', 'name': 'Dark'},
      {'value': 'sepia', 'name': 'Sepia'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Theme',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _currentPreferences.theme,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: themes.map((theme) {
            return DropdownMenuItem(
              value: theme['value'],
              child: Text(theme['name']!),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updatePreferences(_currentPreferences.copyWith(theme: value));
            }
          },
        ),
      ],
    );
  }

  /// Build night mode switch
  Widget _buildNightModeSwitch() {
    return SwitchListTile(
      title: const Text('Night Mode'),
      subtitle: const Text('Reduce eye strain during night reading'),
      value: _currentPreferences.enableNightMode,
      onChanged: (value) {
        _updatePreferences(_currentPreferences.copyWith(enableNightMode: value));
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  /// Build preview section
  Widget _buildPreviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.preview,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getPreviewBackgroundColor(),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Sample Arabic text
              Text(
                'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                style: TextStyle(
                  fontFamily: _currentPreferences.fontFamily,
                  fontSize: _currentPreferences.fontSize,
                  height: _currentPreferences.lineSpacing,
                  color: _getPreviewTextColor(),
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
              
              // Sample translation
              if (_currentPreferences.showTranslation) ...[
                const SizedBox(height: 8),
                Text(
                  'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: _getPreviewTextColor().withValues(alpha: 0.8),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Reset to defaults
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                _updatePreferences(const QuranPreferences());
              },
              icon: const Icon(Icons.restore),
              label: const Text('Reset to Defaults'),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Close button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.check),
              label: const Text('Done'),
            ),
          ),
        ],
      ),
    );
  }

  /// Get preview background color based on theme
  Color _getPreviewBackgroundColor() {
    switch (_currentPreferences.theme) {
      case 'dark':
        return Colors.grey[900]!;
      case 'sepia':
        return const Color(0xFFF4F1E8);
      default:
        return Theme.of(context).colorScheme.surface;
    }
  }

  /// Get preview text color based on theme
  Color _getPreviewTextColor() {
    switch (_currentPreferences.theme) {
      case 'dark':
        return Colors.white;
      case 'sepia':
        return const Color(0xFF5D4E37);
      default:
        return Theme.of(context).colorScheme.onSurface;
    }
  }
}
