import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/tools/providers/tool_builder_provider.dart';
import '../../features/tools/providers/tool_creation_provider.dart';
import '../../features/tools/providers/custom_tool_provider.dart';

class ToolsDependencies {
  static Future<void> initialize() async {
    // Initialize tool-related services
    // Any async initialization can be done here
  }

  static Future<void> dispose() async {
    // Clean up any resources if needed
  }

  static void register(ProviderContainer container) {
    // Register tool-related providers
    container.read(toolBuilderProvider);
    container.read(toolCreationProvider);
    container.read(customToolsProvider);
  }
}
