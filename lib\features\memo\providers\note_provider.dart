import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Notes List Provider
final notesProvider = StateNotifierProvider<NotesNotifier, List<Note>>((ref) {
  final userId = ref.watch(userProfileProvider.select((profile) => profile?.id.toString()));
  return NotesNotifier(userId);
});

class NotesNotifier extends StateNotifier<List<Note>> {
  final String? userId;

  NotesNotifier(this.userId) : super([]) {
    loadNotes();
  }

  Future<void> loadNotes() async {
    final notes = await DatabaseService.instance.getAllNotes(userId: userId);
    state = notes;
  }

  Future<Note> createNote({
    required String title,
    required String content,
    String color = 'default',
    bool isPinned = false,
    bool isFavorite = false,
    List<String>? tags,
  }) async {
    final note = Note.create(
      title: title,
      content: content,
      userId: userId ?? '',
      color: color,
      isPinned: isPinned,
      isFavorite: isFavorite,
      tags: tags,
    );

    final savedNote = await DatabaseService.instance.saveNote(note);
    await loadNotes(); // Refresh the list
    return savedNote;
  }

  Future<void> updateNote(Note note) async {
    await DatabaseService.instance.saveNote(note);
    await loadNotes(); // Refresh the list
  }

  Future<void> deleteNote(int noteId) async {
    await DatabaseService.instance.deleteNote(noteId);
    await loadNotes(); // Refresh the list
  }

  Future<void> togglePin(int noteId) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.togglePin();
    await updateNote(note);
  }

  Future<void> toggleFavorite(int noteId) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.toggleFavorite();
    await updateNote(note);
  }

  Future<void> addTag(int noteId, String tag) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.addTag(tag);
    await updateNote(note);
  }

  Future<void> removeTag(int noteId, String tag) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.removeTag(tag);
    await updateNote(note);
  }

  List<Note> get pinnedNotes => state.where((note) => note.isPinned).toList();
  List<Note> get unpinnedNotes => state.where((note) => !note.isPinned).toList();
  List<Note> get favoriteNotes => state.where((note) => note.isFavorite).toList();
}

// Search Provider
final noteSearchProvider = StateProvider<String>((ref) => '');

// Filtered Notes Provider
final filteredNotesProvider = Provider<List<Note>>((ref) {
  final notes = ref.watch(notesProvider);
  final searchTerm = ref.watch(noteSearchProvider);
  
  if (searchTerm.isEmpty) {
    return notes;
  }
  
  return notes.where((note) => note.containsSearchTerm(searchTerm)).toList();
});

// Selected Note Provider (for editing)
final selectedNoteProvider = StateProvider<Note?>((ref) => null);

// Note Colors Provider
final noteColorsProvider = Provider<List<String>>((ref) => [
  'default',
  'red',
  'orange',
  'yellow',
  'green',
  'blue',
  'purple',
  'pink',
  'brown',
  'grey',
]);

// Tags Provider
final noteTagsProvider = FutureProvider<List<String>>((ref) async {
  final userId = ref.watch(userProfileProvider.select((profile) => profile?.id.toString()));
  return await DatabaseService.instance.getAllTags(userId: userId);
});

// Selected Tag Filter Provider
final selectedTagFilterProvider = StateProvider<String?>((ref) => null);

// Notes by Tag Provider
final notesByTagProvider = Provider<List<Note>>((ref) {
  final notes = ref.watch(filteredNotesProvider);
  final selectedTag = ref.watch(selectedTagFilterProvider);
  
  if (selectedTag == null) {
    return notes;
  }
  
  return notes.where((note) => note.tags.contains(selectedTag)).toList();
});

// Note Statistics Provider
final noteStatsProvider = Provider<Map<String, int>>((ref) {
  final notes = ref.watch(notesProvider);
  
  return {
    'total': notes.length,
    'pinned': notes.where((note) => note.isPinned).length,
    'favorites': notes.where((note) => note.isFavorite).length,
    'withTags': notes.where((note) => note.tags.isNotEmpty).length,
  };
});
