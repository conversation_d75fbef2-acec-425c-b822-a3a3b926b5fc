import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/money_manager_service.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../models/category.dart';
import '../models/budget.dart';
import '../widgets/account_card.dart';
import '../widgets/transaction_list.dart';
import '../widgets/budget_overview.dart';
import '../widgets/quick_actions.dart';
import '../widgets/spending_chart.dart';
import 'add_transaction_screen.dart';
import 'accounts_screen.dart';
import 'budgets_screen.dart';
import 'reports_screen.dart';

/// Main Money Manager screen with comprehensive financial management
class MoneyManagerScreen extends ConsumerStatefulWidget {
  const MoneyManagerScreen({super.key});

  @override
  ConsumerState<MoneyManagerScreen> createState() => _MoneyManagerScreenState();
}

class _MoneyManagerScreenState extends ConsumerState<MoneyManagerScreen>
    with TickerProviderStateMixin {
  final MoneyManagerService _moneyService = MoneyManagerService();
  late TabController _tabController;
  
  bool _isLoading = true;
  List<Account> _accounts = [];
  List<Transaction> _transactions = [];
  List<Category> _categories = [];
  List<Budget> _budgets = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeMoneyManager();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeMoneyManager() async {
    try {
      await _moneyService.initialize();
      setState(() {
        _accounts = _moneyService.accounts;
        _transactions = _moneyService.transactions;
        _categories = _moneyService.categories;
        _budgets = _moneyService.budgets;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading Money Manager: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _refreshData() async {
    setState(() {
      _accounts = _moneyService.accounts;
      _transactions = _moneyService.transactions;
      _categories = _moneyService.categories;
      _budgets = _moneyService.budgets;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading Money Manager...'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Money Manager'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: 'Search Transactions',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('Export Data'),
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Import Data'),
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Settings'),
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'Overview',
            ),
            Tab(
              icon: Icon(Icons.account_balance_wallet),
              text: 'Accounts',
            ),
            Tab(
              icon: Icon(Icons.receipt_long),
              text: 'Transactions',
            ),
            Tab(
              icon: Icon(Icons.pie_chart),
              text: 'Budgets',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildAccountsTab(),
          _buildTransactionsTab(),
          _buildBudgetsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddTransactionDialog,
        icon: const Icon(Icons.add),
        label: const Text('Add Transaction'),
      ),
    );
  }

  Widget _buildOverviewTab() {
    final totalBalance = _moneyService.getTotalBalance();
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final monthlyIncome = _moneyService.getIncomeForPeriod(startOfMonth, now);
    final monthlyExpenses = _moneyService.getExpensesForPeriod(startOfMonth, now);

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Financial Summary Cards
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Total Balance',
                    '\$${totalBalance.toStringAsFixed(2)}',
                    totalBalance >= 0 ? Colors.green : Colors.red,
                    Icons.account_balance_wallet,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Monthly Income',
                    '\$${monthlyIncome.toStringAsFixed(2)}',
                    Colors.green,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'Monthly Expenses',
                    '\$${monthlyExpenses.toStringAsFixed(2)}',
                    Colors.red,
                    Icons.trending_down,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryCard(
                    'Net Income',
                    '\$${(monthlyIncome - monthlyExpenses).toStringAsFixed(2)}',
                    (monthlyIncome - monthlyExpenses) >= 0 ? Colors.green : Colors.red,
                    Icons.account_balance,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Quick Actions
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            QuickActions(
              onAddIncome: () => _showAddTransactionDialog(type: TransactionType.income),
              onAddExpense: () => _showAddTransactionDialog(type: TransactionType.expense),
              onTransfer: () => _showAddTransactionDialog(type: TransactionType.transfer),
              onViewReports: _openReports,
            ),

            const SizedBox(height: 24),

            // Budget Overview
            if (_budgets.isNotEmpty) ...[
              Text(
                'Budget Overview',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              BudgetOverview(budgets: _budgets),
              const SizedBox(height: 24),
            ],

            // Recent Transactions
            Text(
              'Recent Transactions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            TransactionList(
              transactions: _transactions.take(5).toList(),
              accounts: _accounts,
              categories: _categories,
              onTransactionTap: _editTransaction,
              showAccountInfo: true,
            ),

            const SizedBox(height: 16),

            // View All Transactions Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () => _tabController.animateTo(2),
                child: const Text('View All Transactions'),
              ),
            ),

            const SizedBox(height: 24),

            // Spending Chart
            Text(
              'Monthly Spending by Category',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            SpendingChart(
              categorySpending: _moneyService.getCategorySpending(startOfMonth, now),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountsTab() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Column(
        children: [
          // Account Summary
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total Balance',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '\$${_moneyService.getTotalBalance().toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_accounts.length} accounts',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),

          // Accounts List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _accounts.length,
              itemBuilder: (context, index) {
                final account = _accounts[index];
                return AccountCard(
                  account: account,
                  onTap: () => _viewAccountDetails(account),
                  onEdit: () => _editAccount(account),
                );
              },
            ),
          ),

          // Add Account Button
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _addAccount,
                icon: const Icon(Icons.add),
                label: const Text('Add Account'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Column(
        children: [
          // Transaction Summary
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Total Transactions',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSecondaryContainer,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_transactions.length}',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSecondaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: _showFilterDialog,
                  icon: const Icon(Icons.filter_list),
                  tooltip: 'Filter Transactions',
                ),
              ],
            ),
          ),

          // Transactions List
          Expanded(
            child: TransactionList(
              transactions: _transactions,
              accounts: _accounts,
              categories: _categories,
              onTransactionTap: _editTransaction,
              showAccountInfo: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetsTab() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Column(
        children: [
          // Budget Summary
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.tertiaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Active Budgets',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onTertiaryContainer,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_budgets.length}',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onTertiaryContainer,
                  ),
                ),
              ],
            ),
          ),

          // Budgets List
          Expanded(
            child: _budgets.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.pie_chart_outline,
                          size: 64,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No budgets yet',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Create your first budget to track spending',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _addBudget,
                          icon: const Icon(Icons.add),
                          label: const Text('Create Budget'),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _budgets.length,
                    itemBuilder: (context, index) {
                      final budget = _budgets[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: budget.color,
                            child: Icon(
                              budget.statusIcon,
                              color: Colors.white,
                            ),
                          ),
                          title: Text(budget.name),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(budget.description),
                              const SizedBox(height: 4),
                              LinearProgressIndicator(
                                value: budget.spentPercentage,
                                backgroundColor: budget.color.withValues(alpha: 0.2),
                                valueColor: AlwaysStoppedAnimation<Color>(budget.statusColor),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${budget.formattedSpent} of ${budget.formattedAmount}',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                          trailing: Text(
                            budget.statusText,
                            style: TextStyle(
                              color: budget.statusColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          onTap: () => _editBudget(budget),
                        ),
                      );
                    },
                  ),
          ),

          // Add Budget Button
          if (_budgets.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _addBudget,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Budget'),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Action methods
  void _showAddTransactionDialog({TransactionType? type}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddTransactionScreen(
          accounts: _accounts,
          categories: _categories,
          initialType: type,
          onSave: (transaction) async {
            await _moneyService.createTransaction(
              type: transaction.type,
              amount: transaction.amount,
              fromAccountId: transaction.fromAccountId,
              toAccountId: transaction.toAccountId,
              categoryId: transaction.categoryId,
              title: transaction.title,
              description: transaction.description,
              date: transaction.date,
              tags: transaction.tags,
            );
            await _refreshData();
          },
        ),
      ),
    );
  }

  void _editTransaction(Transaction transaction) {
    // Navigate to edit transaction screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit transaction functionality coming soon!')),
    );
  }

  void _viewAccountDetails(Account account) {
    // Navigate to account details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing details for ${account.name}')),
    );
  }

  void _editAccount(Account account) {
    // Navigate to edit account screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit account functionality coming soon!')),
    );
  }

  void _addAccount() {
    // Navigate to add account screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add account functionality coming soon!')),
    );
  }

  void _editBudget(Budget budget) {
    // Navigate to edit budget screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit budget functionality coming soon!')),
    );
  }

  void _addBudget() {
    // Navigate to add budget screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add budget functionality coming soon!')),
    );
  }

  void _openReports() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ReportsScreen(
          moneyService: _moneyService,
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Transactions'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'Enter search term...',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (query) {
            Navigator.of(context).pop();
            final results = _moneyService.searchTransactions(query);
            // Show search results
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Found ${results.length} transactions')),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Transactions'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.calendar_today),
                title: const Text('Date Range'),
                subtitle: const Text('Filter by date range'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showDateRangeFilter();
                },
              ),
              ListTile(
                leading: const Icon(Icons.category),
                title: const Text('Category'),
                subtitle: const Text('Filter by category'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showCategoryFilter();
                },
              ),
              ListTile(
                leading: const Icon(Icons.account_balance_wallet),
                title: const Text('Account'),
                subtitle: const Text('Filter by account'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showAccountFilter();
                },
              ),
              ListTile(
                leading: const Icon(Icons.attach_money),
                title: const Text('Amount Range'),
                subtitle: const Text('Filter by amount'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showAmountFilter();
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDateRangeFilter() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Date filter applied: ${picked.start.day}/${picked.start.month} - ${picked.end.day}/${picked.end.month}'),
        ),
      );
    }
  }

  void _showCategoryFilter() {
    final categories = _moneyService.categories;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Category'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return ListTile(
                title: Text(category.name),
                subtitle: Text(category.type.displayName),
                onTap: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Category filter applied: ${category.name}')),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showAccountFilter() {
    final accounts = _moneyService.accounts;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Account'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: accounts.length,
            itemBuilder: (context, index) {
              final account = accounts[index];
              return ListTile(
                title: Text(account.name),
                subtitle: Text(account.type.displayName),
                trailing: Text(account.formattedBalance),
                onTap: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Account filter applied: ${account.name}')),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showAmountFilter() {
    double minAmount = 0;
    double maxAmount = 1000;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Amount Range'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Min: \$${minAmount.toStringAsFixed(2)}'),
              Slider(
                value: minAmount,
                min: 0,
                max: 10000,
                divisions: 100,
                onChanged: (value) {
                  setState(() {
                    minAmount = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              Text('Max: \$${maxAmount.toStringAsFixed(2)}'),
              Slider(
                value: maxAmount,
                min: 0,
                max: 10000,
                divisions: 100,
                onChanged: (value) {
                  setState(() {
                    maxAmount = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Amount filter applied: \$${minAmount.toStringAsFixed(2)} - \$${maxAmount.toStringAsFixed(2)}'),
                  ),
                );
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportData();
        break;
      case 'import':
        _importData();
        break;
      case 'settings':
        _openSettings();
        break;
    }
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon!')),
    );
  }

  void _importData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality coming soon!')),
    );
  }

  void _openSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings functionality coming soon!')),
    );
  }
}
