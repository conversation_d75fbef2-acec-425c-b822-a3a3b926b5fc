import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/notification.dart';
import '../widgets/notification_widget.dart';
import '../providers/notification_provider.dart';

class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  @override
  ConsumerState<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _showOnlyUnread = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = ref.watch(unreadNotificationCountProvider);


    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => setState(() => _showOnlyUnread = !_showOnlyUnread),
            icon: Icon(_showOnlyUnread ? Icons.mark_email_read : Icons.filter_list),
            tooltip: _showOnlyUnread ? 'Show All' : 'Show Unread Only',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'mark_all_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read),
                    SizedBox(width: 8),
                    Text('Mark All Read'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_read',
                child: Row(
                  children: [
                    Icon(Icons.clear),
                    SizedBox(width: 8),
                    Text('Clear Read'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.delete_sweep, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Clear All', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'All',
              icon: const Icon(Icons.notifications, size: 16),
            ),
            Tab(
              text: 'Unread ($unreadCount)',
              icon: const Icon(Icons.mark_email_unread, size: 16),
            ),
            Tab(
              text: 'Scheduled',
              icon: const Icon(Icons.schedule, size: 16),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllNotificationsTab(),
          _buildUnreadNotificationsTab(),
          _buildScheduledNotificationsTab(),
        ],
      ),
    );
  }

  Widget _buildAllNotificationsTab() {
    return const NotificationsList(showOnlyUnread: false);
  }

  Widget _buildUnreadNotificationsTab() {
    return const NotificationsList(showOnlyUnread: true);
  }

  Widget _buildScheduledNotificationsTab() {
    final pendingNotifications = ref.watch(pendingNotificationsProvider);

    if (pendingNotifications.isEmpty) {
      return _buildEmptyScheduledState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: pendingNotifications.length,
      itemBuilder: (context, index) {
        final notification = pendingNotifications[index];
        return Card(
          child: ListTile(
            leading: _buildScheduledIcon(notification),
            title: Text(notification.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.body),
                const SizedBox(height: 4),
                Text(
                  'Scheduled: ${_formatScheduledTime(notification.scheduledAt)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue,
                  ),
                ),
                if (notification.isRepeating)
                  Text(
                    'Repeats every ${_formatRepeatInterval(notification.repeatInterval)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                    ),
                  ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (action) => _handleScheduledAction(notification, action),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'cancel',
                  child: Row(
                    children: [
                      Icon(Icons.cancel, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Cancel', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
            isThreeLine: true,
          ),
        );
      },
    );
  }

  Widget _buildEmptyScheduledState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No scheduled notifications',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Scheduled notifications will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduledIcon(notification) {
    IconData iconData;
    Color iconColor = notification.color ?? Colors.blue;

    switch (notification.type) {
      case NotificationType.todoReminder:
        iconData = Icons.task_alt;
        break;
      case NotificationType.dhikrReminder:
        iconData = Icons.self_improvement;
        break;
      case NotificationType.dailyReview:
        iconData = Icons.rate_review;
        break;
      case NotificationType.weeklyReport:
        iconData = Icons.analytics;
        break;
      default:
        iconData = Icons.schedule;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String _formatScheduledTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);

    if (difference.inDays > 0) {
      return 'in ${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return 'in ${difference.inHours} hours';
    } else if (difference.inMinutes > 0) {
      return 'in ${difference.inMinutes} minutes';
    } else if (difference.inMinutes < 0) {
      return 'overdue';
    } else {
      return 'now';
    }
  }

  String _formatRepeatInterval(Duration? interval) {
    if (interval == null) return '';
    
    if (interval.inDays >= 7) {
      final weeks = interval.inDays ~/ 7;
      return '$weeks week${weeks > 1 ? 's' : ''}';
    } else if (interval.inDays > 0) {
      return '${interval.inDays} day${interval.inDays > 1 ? 's' : ''}';
    } else if (interval.inHours > 0) {
      return '${interval.inHours} hour${interval.inHours > 1 ? 's' : ''}';
    } else {
      return '${interval.inMinutes} minute${interval.inMinutes > 1 ? 's' : ''}';
    }
  }

  void _handleMenuAction(String action) async {
    final actions = ref.read(notificationActionsProvider);
    
    switch (action) {
      case 'mark_all_read':
        await _markAllAsRead();
        break;
      case 'clear_read':
        await actions.clearRead();
        _showSnackBar('Read notifications cleared');
        break;
      case 'clear_all':
        await _confirmClearAll();
        break;
      case 'settings':
        _showNotificationSettings();
        break;
    }
  }

  void _handleScheduledAction(notification, String action) async {
    final actions = ref.read(notificationActionsProvider);
    
    switch (action) {
      case 'edit':
        _editScheduledNotification(notification);
        break;
      case 'cancel':
        await actions.cancel(notification.id);
        _showSnackBar('Notification cancelled');
        break;
    }
  }

  Future<void> _markAllAsRead() async {
    final notificationsAsync = ref.read(notificationsProvider);
    final notifications = notificationsAsync.value ?? [];
    final actions = ref.read(notificationActionsProvider);
    
    for (final notification in notifications) {
      if (!notification.isRead) {
        await actions.markAsRead(notification.id);
      }
    }
    
    _showSnackBar('All notifications marked as read');
  }

  Future<void> _confirmClearAll() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final actions = ref.read(notificationActionsProvider);
      await actions.clearAll();
      _showSnackBar('All notifications cleared');
    }
  }

  void _editScheduledNotification(notification) {
    // TODO: Implement notification editing
    _showSnackBar('Edit notification feature coming soon!');
  }

  void _showNotificationSettings() {
    // TODO: Navigate to notification settings
    _showSnackBar('Notification settings coming soon!');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
