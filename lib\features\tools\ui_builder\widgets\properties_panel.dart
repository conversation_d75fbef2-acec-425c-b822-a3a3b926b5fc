import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../providers/ui_builder_provider.dart';
import '../../spreadsheet/providers/simple_spreadsheet_provider.dart';

/// Properties panel for the UI Builder
class PropertiesPanel extends ConsumerStatefulWidget {
  final Function(String componentId, UIComponent updatedComponent)? onComponentUpdated;
  final VoidCallback? onCellPickerRequested;

  const PropertiesPanel({
    super.key,
    this.onComponentUpdated,
    this.onCellPickerRequested,
  });

  @override
  ConsumerState<PropertiesPanel> createState() => _PropertiesPanelState();
}

class _PropertiesPanelState extends ConsumerState<PropertiesPanel> {
  final Map<String, TextEditingController> _controllers = {};

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final uiBuilderState = ref.watch(uiBuilderProvider);
    final selectedComponent = uiBuilderState.selectedComponent;

    return Container(
      width: 320,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          left: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: selectedComponent != null
          ? _buildPropertiesContent(selectedComponent)
          : _buildEmptyState(),
    );
  }

  /// Build properties content for selected component
  Widget _buildPropertiesContent(UIComponent component) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        _buildHeader(component),
        
        // Properties list
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic properties
                _buildBasicProperties(component),
                
                const SizedBox(height: 24),
                
                // Data binding
                _buildDataBinding(component),
                
                const SizedBox(height: 24),
                
                // Styling
                _buildStyling(component),
                
                const SizedBox(height: 24),
                
                // Layout
                _buildLayout(component),
                
                const SizedBox(height: 24),
                
                // Component-specific properties
                _buildComponentSpecificProperties(component),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build empty state when no component is selected
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No Component Selected',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a component to edit its properties',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build panel header
  Widget _buildHeader(UIComponent component) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.tune,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Properties',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  component.title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _deleteComponent(component.id),
            icon: const Icon(Icons.delete_outline),
            tooltip: 'Delete Component',
            color: Theme.of(context).colorScheme.error,
          ),
        ],
      ),
    );
  }

  /// Build basic properties section
  Widget _buildBasicProperties(UIComponent component) {
    return _buildSection(
      'Basic Properties',
      [
        _buildTextProperty(
          'Title',
          component.title,
          (value) => _updateComponent(component, component.copyWith(title: value)),
        ),
        if (component.type == UIComponentType.textField)
          _buildTextProperty(
            'Placeholder',
            component.properties['placeholder'] ?? '',
            (value) => _updateComponentProperty(component, 'placeholder', value),
          ),
        if (component.type == UIComponentType.button)
          _buildTextProperty(
            'Button Text',
            component.properties['text'] ?? '',
            (value) => _updateComponentProperty(component, 'text', value),
          ),
        if (component.type == UIComponentType.label)
          _buildTextProperty(
            'Text',
            component.properties['text'] ?? '',
            (value) => _updateComponentProperty(component, 'text', value),
          ),
      ],
    );
  }

  /// Build data binding section
  Widget _buildDataBinding(UIComponent component) {
    return _buildSection(
      'Data Binding',
      [
        _buildCellBindingProperty(component),
        _buildBindingModeProperty(component),
      ],
    );
  }

  /// Build styling section
  Widget _buildStyling(UIComponent component) {
    return _buildSection(
      'Styling',
      [
        _buildColorProperty(
          'Background Color',
          component.properties['backgroundColor'] as String? ?? '#FFFFFF',
          (value) => _updateComponentProperty(component, 'backgroundColor', value),
        ),
        _buildColorProperty(
          'Text Color',
          component.properties['textColor'] as String? ?? '#000000',
          (value) => _updateComponentProperty(component, 'textColor', value),
        ),
        _buildSliderProperty(
          'Font Size',
          (component.properties['fontSize'] as num?)?.toDouble() ?? 14.0,
          8.0,
          32.0,
          (value) => _updateComponentProperty(component, 'fontSize', value),
        ),
      ],
    );
  }

  /// Build layout section
  Widget _buildLayout(UIComponent component) {
    return _buildSection(
      'Layout',
      [
        _buildSizeProperty(component),
        _buildPositionProperty(component),
        _buildPaddingProperty(component),
      ],
    );
  }

  /// Build component-specific properties
  Widget _buildComponentSpecificProperties(UIComponent component) {
    switch (component.type) {
      case UIComponentType.slider:
        return _buildSliderSpecificProperties(component);
      case UIComponentType.dropdown:
        return _buildDropdownSpecificProperties(component);
      case UIComponentType.image:
        return _buildImageSpecificProperties(component);
      default:
        return const SizedBox.shrink();
    }
  }

  /// Build section with title and properties
  Widget _buildSection(String title, List<Widget> properties) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 12),
        ...properties.map((prop) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: prop,
        )),
      ],
    );
  }

  /// Build text property
  Widget _buildTextProperty(String label, String value, Function(String) onChanged) {
    final key = '${label}_text';
    _controllers[key] ??= TextEditingController(text: value);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: _controllers[key],
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// Build cell binding property
  Widget _buildCellBindingProperty(UIComponent component) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Bind to Cell',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            IconButton(
              onPressed: widget.onCellPickerRequested,
              icon: const Icon(Icons.grid_on),
              tooltip: 'Pick Cell',
              iconSize: 20,
            ),
          ],
        ),
        const SizedBox(height: 4),
        TextField(
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'e.g., A1, B2:C5',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          controller: TextEditingController(text: component.cellBinding ?? ''),
          onChanged: (value) => _updateComponent(
            component,
            component.copyWith(cellBinding: value.isEmpty ? null : value),
          ),
        ),
      ],
    );
  }

  /// Build binding mode property
  Widget _buildBindingModeProperty(UIComponent component) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Binding Mode',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<BindingMode>(
          value: component.bindingMode,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: BindingMode.values.map((mode) {
            return DropdownMenuItem(
              value: mode,
              child: Text(_getBindingModeLabel(mode)),
            );
          }).toList(),
          onChanged: (mode) {
            if (mode != null) {
              _updateComponent(component, component.copyWith(bindingMode: mode));
            }
          },
        ),
      ],
    );
  }

  /// Build color property
  Widget _buildColorProperty(String label, String value, Function(String) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: _parseColor(value),
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: '#FFFFFF',
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                controller: TextEditingController(text: value),
                onChanged: onChanged,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build slider property
  Widget _buildSliderProperty(String label, double value, double min, double max, Function(double) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value.toStringAsFixed(1),
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Slider(
          value: value,
          min: min,
          max: max,
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// Build size property
  Widget _buildSizeProperty(UIComponent component) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Size',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Width',
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                controller: TextEditingController(text: component.size.width.toString()),
                onChanged: (value) {
                  final width = double.tryParse(value) ?? component.size.width;
                  _updateComponent(
                    component,
                    component.copyWith(size: Size(width, component.size.height)),
                  );
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Height',
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                controller: TextEditingController(text: component.size.height.toString()),
                onChanged: (value) {
                  final height = double.tryParse(value) ?? component.size.height;
                  _updateComponent(
                    component,
                    component.copyWith(size: Size(component.size.width, height)),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build position property
  Widget _buildPositionProperty(UIComponent component) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Position',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'X',
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                controller: TextEditingController(text: component.position.dx.toString()),
                onChanged: (value) {
                  final x = double.tryParse(value) ?? component.position.dx;
                  _updateComponent(
                    component,
                    component.copyWith(position: Offset(x, component.position.dy)),
                  );
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Y',
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                controller: TextEditingController(text: component.position.dy.toString()),
                onChanged: (value) {
                  final y = double.tryParse(value) ?? component.position.dy;
                  _updateComponent(
                    component,
                    component.copyWith(position: Offset(component.position.dx, y)),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build padding property
  Widget _buildPaddingProperty(UIComponent component) {
    final padding = component.properties['padding'] as double? ?? 8.0;
    
    return _buildSliderProperty(
      'Padding',
      padding,
      0.0,
      32.0,
      (value) => _updateComponentProperty(component, 'padding', value),
    );
  }

  /// Build slider-specific properties
  Widget _buildSliderSpecificProperties(UIComponent component) {
    return _buildSection(
      'Slider Properties',
      [
        _buildSliderProperty(
          'Minimum Value',
          (component.properties['min'] as num?)?.toDouble() ?? 0.0,
          -1000.0,
          1000.0,
          (value) => _updateComponentProperty(component, 'min', value),
        ),
        _buildSliderProperty(
          'Maximum Value',
          (component.properties['max'] as num?)?.toDouble() ?? 100.0,
          -1000.0,
          1000.0,
          (value) => _updateComponentProperty(component, 'max', value),
        ),
      ],
    );
  }

  /// Build dropdown-specific properties
  Widget _buildDropdownSpecificProperties(UIComponent component) {
    final options = List<String>.from(component.properties['options'] ?? []);
    
    return _buildSection(
      'Dropdown Properties',
      [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Options',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            ...options.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(
                          border: const OutlineInputBorder(),
                          labelText: 'Option ${index + 1}',
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        controller: TextEditingController(text: option),
                        onChanged: (value) {
                          final updatedOptions = List<String>.from(options);
                          updatedOptions[index] = value;
                          _updateComponentProperty(component, 'options', updatedOptions);
                        },
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        final updatedOptions = List<String>.from(options);
                        updatedOptions.removeAt(index);
                        _updateComponentProperty(component, 'options', updatedOptions);
                      },
                      icon: const Icon(Icons.remove_circle_outline),
                    ),
                  ],
                ),
              );
            }),
            ElevatedButton.icon(
              onPressed: () {
                final updatedOptions = List<String>.from(options);
                updatedOptions.add('New Option');
                _updateComponentProperty(component, 'options', updatedOptions);
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Option'),
            ),
          ],
        ),
      ],
    );
  }

  /// Build image-specific properties
  Widget _buildImageSpecificProperties(UIComponent component) {
    return _buildSection(
      'Image Properties',
      [
        _buildTextProperty(
          'Image URL',
          component.properties['url'] ?? '',
          (value) => _updateComponentProperty(component, 'url', value),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fit Mode',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            DropdownButtonFormField<String>(
              value: component.properties['fit'] ?? 'contain',
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: ['contain', 'cover', 'fill', 'fitWidth', 'fitHeight'].map((fit) {
                return DropdownMenuItem(
                  value: fit,
                  child: Text(fit),
                );
              }).toList(),
              onChanged: (fit) {
                if (fit != null) {
                  _updateComponentProperty(component, 'fit', fit);
                }
              },
            ),
          ],
        ),
      ],
    );
  }

  /// Update component
  void _updateComponent(UIComponent component, UIComponent updatedComponent) {
    ref.read(uiBuilderProvider.notifier).updateComponent(component.id, updatedComponent);
    widget.onComponentUpdated?.call(component.id, updatedComponent);
  }

  /// Update component property
  void _updateComponentProperty(UIComponent component, String key, dynamic value) {
    final updatedProperties = Map<String, dynamic>.from(component.properties);
    updatedProperties[key] = value;
    
    final updatedComponent = component.copyWith(properties: updatedProperties);
    _updateComponent(component, updatedComponent);
  }

  /// Delete component
  void _deleteComponent(String componentId) {
    ref.read(uiBuilderProvider.notifier).removeComponent(componentId);
  }

  /// Get binding mode label
  String _getBindingModeLabel(BindingMode mode) {
    switch (mode) {
      case BindingMode.readOnly:
        return 'Read Only';
      case BindingMode.writeOnly:
        return 'Write Only';
      case BindingMode.readWrite:
        return 'Read/Write';
    }
  }

  /// Parse color from hex string
  Color _parseColor(String hex) {
    try {
      return Color(int.parse(hex.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Colors.white;
    }
  }
}
