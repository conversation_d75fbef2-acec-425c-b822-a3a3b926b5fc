import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';

/// Service for loading and managing Quran data
class QuranService {
  static QuranService? _instance;
  static QuranService get instance => _instance ??= QuranService._();
  QuranService._();

  List<Surah>? _surahs;
  Map<int, List<Verse>>? _versesBySurah;
  bool _isLoaded = false;

  /// Get all surahs
  List<Surah> get surahs => _surahs ?? [];

  /// Get verses for a specific surah
  List<Verse> getVersesForSurah(int surahNumber) {
    return _versesBySurah?[surahNumber] ?? [];
  }

  /// Check if Quran data is loaded
  bool get isLoaded => _isLoaded;

  /// Load Quran data from the text file
  Future<void> loadQuranData() async {
    if (_isLoaded) return;

    try {
      // Load the Quran text file
      final String quranText = await rootBundle.loadString('Quran.txt');
      await _parseQuranData(quranText);
      _isLoaded = true;
    } catch (e) {
      print('Error loading Quran data: $e');
      // Load fallback data if file loading fails
      _loadFallbackData();
    }
  }

  /// Parse the Quran text data
  Future<void> _parseQuranData(String quranText) async {
    final lines = quranText.split('\n');
    final Map<int, SurahInfo> surahInfoMap = {};
    final Map<int, List<Verse>> versesBySurah = {};

    for (final line in lines) {
      if (line.trim().isEmpty) continue;

      final parts = line.split('|');
      if (parts.length != 3) continue;

      try {
        final surahNumber = int.parse(parts[0]);
        final verseNumber = int.parse(parts[1]);
        final arabicText = parts[2].trim();

        // Initialize surah info if not exists
        if (!surahInfoMap.containsKey(surahNumber)) {
          final surahName = _getSurahName(surahNumber);
          final surahNameEnglish = _getSurahNameEnglish(surahNumber);
          final revelationType = _getRevelationType(surahNumber);
          
          surahInfoMap[surahNumber] = SurahInfo(
            number: surahNumber,
            name: surahName,
            englishName: surahNameEnglish,
            revelationType: revelationType,
            verseCount: 0, // Will be updated later
          );
          versesBySurah[surahNumber] = [];
        }

        // Add verse
        final verse = Verse(
          number: verseNumber,
          arabicText: arabicText,
          surahNumber: surahNumber,
        );

        versesBySurah[surahNumber]!.add(verse);
        surahInfoMap[surahNumber] = surahInfoMap[surahNumber]!.copyWith(
          verseCount: versesBySurah[surahNumber]!.length,
        );
      } catch (e) {
        print('Error parsing line: $line - $e');
        continue;
      }
    }

    // Convert to final format
    _surahs = surahInfoMap.values
        .map((info) => Surah(
              info: info,
              verses: versesBySurah[info.number] ?? [],
            ))
        .toList()
      ..sort((a, b) => a.info.number.compareTo(b.info.number));

    _versesBySurah = versesBySurah;
  }

  /// Load fallback data if file loading fails
  void _loadFallbackData() {
    _surahs = [
      Surah(
        info: const SurahInfo(
          number: 1,
          name: 'الفاتحة',
          englishName: 'Al-Fatihah',
          revelationType: RevelationType.meccan,
          verseCount: 7,
        ),
        verses: [
          const Verse(
            number: 1,
            arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            surahNumber: 1,
          ),
          const Verse(
            number: 2,
            arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
            surahNumber: 1,
          ),
          const Verse(
            number: 3,
            arabicText: 'الرَّحْمَٰنِ الرَّحِيمِ',
            surahNumber: 1,
          ),
          const Verse(
            number: 4,
            arabicText: 'مَالِكِ يَوْمِ الدِّينِ',
            surahNumber: 1,
          ),
          const Verse(
            number: 5,
            arabicText: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ',
            surahNumber: 1,
          ),
          const Verse(
            number: 6,
            arabicText: 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ',
            surahNumber: 1,
          ),
          const Verse(
            number: 7,
            arabicText: 'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
            surahNumber: 1,
          ),
        ],
      ),
    ];

    _versesBySurah = {
      1: _surahs![0].verses,
    };

    _isLoaded = true;
  }

  /// Get surah name in Arabic
  String _getSurahName(int surahNumber) {
    final surahNames = {
      1: 'الفاتحة',
      2: 'البقرة',
      3: 'آل عمران',
      4: 'النساء',
      5: 'المائدة',
      6: 'الأنعام',
      7: 'الأعراف',
      8: 'الأنفال',
      9: 'التوبة',
      10: 'يونس',
      11: 'هود',
      12: 'يوسف',
      13: 'الرعد',
      14: 'إبراهيم',
      15: 'الحجر',
      16: 'النحل',
      17: 'الإسراء',
      18: 'الكهف',
      19: 'مريم',
      20: 'طه',
      21: 'الأنبياء',
      22: 'الحج',
      23: 'المؤمنون',
      24: 'النور',
      25: 'الفرقان',
      26: 'الشعراء',
      27: 'النمل',
      28: 'القصص',
      29: 'العنكبوت',
      30: 'الروم',
      31: 'لقمان',
      32: 'السجدة',
      33: 'الأحزاب',
      34: 'سبأ',
      35: 'فاطر',
      36: 'يس',
      37: 'الصافات',
      38: 'ص',
      39: 'الزمر',
      40: 'غافر',
      41: 'فصلت',
      42: 'الشورى',
      43: 'الزخرف',
      44: 'الدخان',
      45: 'الجاثية',
      46: 'الأحقاف',
      47: 'محمد',
      48: 'الفتح',
      49: 'الحجرات',
      50: 'ق',
      51: 'الذاريات',
      52: 'الطور',
      53: 'النجم',
      54: 'القمر',
      55: 'الرحمن',
      56: 'الواقعة',
      57: 'الحديد',
      58: 'المجادلة',
      59: 'الحشر',
      60: 'الممتحنة',
      61: 'الصف',
      62: 'الجمعة',
      63: 'المنافقون',
      64: 'التغابن',
      65: 'الطلاق',
      66: 'التحريم',
      67: 'الملك',
      68: 'القلم',
      69: 'الحاقة',
      70: 'المعارج',
      71: 'نوح',
      72: 'الجن',
      73: 'المزمل',
      74: 'المدثر',
      75: 'القيامة',
      76: 'الإنسان',
      77: 'المرسلات',
      78: 'النبأ',
      79: 'النازعات',
      80: 'عبس',
      81: 'التكوير',
      82: 'الانفطار',
      83: 'المطففين',
      84: 'الانشقاق',
      85: 'البروج',
      86: 'الطارق',
      87: 'الأعلى',
      88: 'الغاشية',
      89: 'الفجر',
      90: 'البلد',
      91: 'الشمس',
      92: 'الليل',
      93: 'الضحى',
      94: 'الشرح',
      95: 'التين',
      96: 'العلق',
      97: 'القدر',
      98: 'البينة',
      99: 'الزلزلة',
      100: 'العاديات',
      101: 'القارعة',
      102: 'التكاثر',
      103: 'العصر',
      104: 'الهمزة',
      105: 'الفيل',
      106: 'قريش',
      107: 'الماعون',
      108: 'الكوثر',
      109: 'الكافرون',
      110: 'النصر',
      111: 'المسد',
      112: 'الإخلاص',
      113: 'الفلق',
      114: 'الناس',
    };
    return surahNames[surahNumber] ?? 'سورة $surahNumber';
  }

  /// Get surah name in English
  String _getSurahNameEnglish(int surahNumber) {
    final surahNamesEnglish = {
      1: 'Al-Fatihah',
      2: 'Al-Baqarah',
      3: 'Ali \'Imran',
      4: 'An-Nisa',
      5: 'Al-Ma\'idah',
      6: 'Al-An\'am',
      7: 'Al-A\'raf',
      8: 'Al-Anfal',
      9: 'At-Tawbah',
      10: 'Yunus',
      // Add more as needed...
    };
    return surahNamesEnglish[surahNumber] ?? 'Surah $surahNumber';
  }

  /// Get revelation type (Meccan or Medinan)
  RevelationType _getRevelationType(int surahNumber) {
    // Simplified classification - in reality this would be more comprehensive
    final meccanSurahs = {1, 6, 7, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 50, 51, 52, 53, 54, 55, 56, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 111, 112, 113, 114};
    return meccanSurahs.contains(surahNumber) ? RevelationType.meccan : RevelationType.medinan;
  }

  /// Search verses by text
  List<SearchResult> searchVerses(String query) {
    if (!_isLoaded || query.trim().isEmpty) return [];

    final results = <SearchResult>[];
    final searchQuery = query.trim().toLowerCase();

    for (final surah in _surahs!) {
      for (final verse in surah.verses) {
        if (verse.arabicText.toLowerCase().contains(searchQuery)) {
          results.add(SearchResult(
            surah: surah.info,
            verse: verse,
            matchedText: verse.arabicText,
          ));
        }
      }
    }

    return results;
  }

  /// Get a specific verse
  Verse? getVerse(int surahNumber, int verseNumber) {
    final verses = getVersesForSurah(surahNumber);
    try {
      return verses.firstWhere((v) => v.number == verseNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get random verse
  Verse? getRandomVerse() {
    if (!_isLoaded || _surahs!.isEmpty) return null;

    final random = DateTime.now().millisecondsSinceEpoch % _surahs!.length;
    final surah = _surahs![random];
    if (surah.verses.isEmpty) return null;

    final verseIndex = DateTime.now().millisecondsSinceEpoch % surah.verses.length;
    return surah.verses[verseIndex];
  }
}
