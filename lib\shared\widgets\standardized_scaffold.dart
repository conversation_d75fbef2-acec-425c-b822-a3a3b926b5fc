import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/navigation/app_router.dart';
import 'responsive_layout.dart';

/// Navigation item for the standardized scaffold
class NavigationItem {
  final String label;
  final IconData icon;
  final IconData? selectedIcon;
  final String route;
  final List<NavigationItem>? children;

  const NavigationItem({
    required this.label,
    required this.icon,
    this.selectedIcon,
    required this.route,
    this.children,
  });
}

/// Standardized scaffold with responsive navigation
class StandardizedScaffold extends StatefulWidget {
  final Widget body;
  final String title;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final String currentRoute;
  final bool showBackButton;
  final PreferredSizeWidget? bottom;

  const StandardizedScaffold({
    super.key,
    required this.body,
    required this.title,
    required this.currentRoute,
    this.actions,
    this.floatingActionButton,
    this.showBackButton = false,
    this.bottom,
  });

  @override
  State<StandardizedScaffold> createState() => _StandardizedScaffoldState();
}

class _StandardizedScaffoldState extends State<StandardizedScaffold> {
  static const List<NavigationItem> _navigationItems = [
    NavigationItem(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      route: AppRoutes.dashboard,
    ),
    NavigationItem(
      label: 'Memo Suite',
      icon: Icons.note_outlined,
      selectedIcon: Icons.note,
      route: AppRoutes.memo,
    ),
    NavigationItem(
      label: 'Islami',
      icon: Icons.mosque_outlined,
      selectedIcon: Icons.mosque,
      route: AppRoutes.islami,
    ),
    NavigationItem(
      label: 'Tools',
      icon: Icons.build_outlined,
      selectedIcon: Icons.build,
      route: AppRoutes.tools,
    ),
    NavigationItem(
      label: 'Money Flow',
      icon: Icons.account_balance_wallet_outlined,
      selectedIcon: Icons.account_balance_wallet,
      route: AppRoutes.money,
    ),
    NavigationItem(
      label: 'Settings',
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings,
      route: AppRoutes.settings,
    ),
  ];

  int get _selectedIndex {
    for (int i = 0; i < _navigationItems.length; i++) {
      if (widget.currentRoute.startsWith(_navigationItems[i].route)) {
        return i;
      }
    }
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: (context, constraints) => _buildMobileLayout(),
      tablet: (context, constraints) => _buildTabletLayout(),
      desktop: (context, constraints) => _buildDesktopLayout(),
    );
  }

  /// Mobile layout with bottom navigation
  Widget _buildMobileLayout() {
    return Scaffold(
      appBar: _buildAppBar(),
      body: widget.body,
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// Tablet layout with drawer
  Widget _buildTabletLayout() {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: widget.body,
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// Desktop layout with sidebar
  Widget _buildDesktopLayout() {
    return Scaffold(
      body: Row(
        children: [
          _buildSidebar(),
          Expanded(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(child: widget.body),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// Build standardized app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.title),
      centerTitle: false,
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      leading: widget.showBackButton
          ? IconButton(
              onPressed: () => context.pop(),
              icon: const Icon(Icons.arrow_back),
            )
          : (ScreenSize.isDesktop(context) ? null : null),
      actions: widget.actions,
      bottom: widget.bottom,
    );
  }

  /// Build bottom navigation bar for mobile
  Widget _buildBottomNavigationBar() {
    // Show only main navigation items in bottom nav
    final mainItems = _navigationItems.take(5).toList();
    
    return NavigationBar(
      selectedIndex: _selectedIndex < mainItems.length ? _selectedIndex : 0,
      onDestinationSelected: (index) {
        if (index < mainItems.length) {
          context.go(mainItems[index].route);
        }
      },
      destinations: mainItems.map((item) {
        return NavigationDestination(
          icon: Icon(item.icon),
          selectedIcon: Icon(item.selectedIcon ?? item.icon),
          label: item.label,
        );
      }).toList(),
    );
  }

  /// Build drawer for tablet
  Widget _buildDrawer() {
    return NavigationDrawer(
      selectedIndex: _selectedIndex,
      onDestinationSelected: (index) {
        if (index < _navigationItems.length) {
          context.go(_navigationItems[index].route);
          Navigator.of(context).pop(); // Close drawer
        }
      },
      children: [
        // Header
        DrawerHeader(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.apps,
                size: 48,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 8),
              Text(
                'ShadowSuite',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Productivity Suite',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
        ),
        
        // Navigation items
        ..._navigationItems.map((item) {
          return NavigationDrawerDestination(
            icon: Icon(item.icon),
            selectedIcon: Icon(item.selectedIcon ?? item.icon),
            label: Text(item.label),
          );
        }),
      ],
    );
  }

  /// Build sidebar for desktop
  Widget _buildSidebar() {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            height: 80,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.apps,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'ShadowSuite',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Productivity Suite',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Navigation items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: _navigationItems.length,
              itemBuilder: (context, index) {
                final item = _navigationItems[index];
                final isSelected = index == _selectedIndex;
                
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  child: ListTile(
                    leading: Icon(
                      isSelected ? (item.selectedIcon ?? item.icon) : item.icon,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    title: Text(
                      item.label,
                      style: TextStyle(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    selected: isSelected,
                    selectedTileColor: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    onTap: () => context.go(item.route),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
