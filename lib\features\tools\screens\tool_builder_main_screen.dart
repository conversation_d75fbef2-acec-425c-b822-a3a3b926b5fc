import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/ui/master_layout.dart';
import '../../../core/navigation/app_router.dart';

import 'tool_builder_dashboard_tab.dart';
import 'tool_builder_simple_tools_tab.dart';
import 'tool_builder_creator_tab.dart';
import 'tool_builder_saved_tools_tab.dart';

/// Main Tool Builder screen with standardized 4-tab navigation
class ToolBuilderMainScreen extends ConsumerStatefulWidget {
  const ToolBuilderMainScreen({super.key});

  @override
  ConsumerState<ToolBuilderMainScreen> createState() => _ToolBuilderMainScreenState();
}

class _ToolBuilderMainScreenState extends ConsumerState<ToolBuilderMainScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MasterLayout(
      title: 'Tools Builder',
      currentRoute: AppRoutes.tools,
      actions: [
        IconButton(
          onPressed: _showHelp,
          icon: const Icon(Icons.help_outline),
          tooltip: 'Help',
        ),
        IconButton(
          onPressed: _showSettings,
          icon: const Icon(Icons.settings),
          tooltip: 'Settings',
        ),
      ],
      body: Column(
        children: [
          // Tab Bar
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Theme.of(context).colorScheme.primary,
              unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
              indicatorColor: Theme.of(context).colorScheme.primary,
              indicatorWeight: 3,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.dashboard_outlined),
                  text: 'Dashboard',
                ),
                Tab(
                  icon: Icon(Icons.calculate_outlined),
                  text: 'Simple Tools',
                ),
                Tab(
                  icon: Icon(Icons.build_outlined),
                  text: 'Tools Builder',
                ),
                Tab(
                  icon: Icon(Icons.folder_outlined),
                  text: 'Saved Tools',
                ),
              ],
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                ToolBuilderDashboardTab(),
                ToolBuilderSimpleToolsTab(),
                ToolBuilderCreatorTab(),
                ToolBuilderSavedToolsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help_outline, color: Colors.blue),
            SizedBox(width: 8),
            Text('Tools Builder Help'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Tool Builder Tabs:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 12),
              Text('📊 Dashboard - Overview and statistics'),
              SizedBox(height: 8),
              Text('🧮 Simple Tools - Pre-built calculators and converters'),
              SizedBox(height: 8),
              Text('🛠️ Tools Builder - Create custom tools with 4-step workflow'),
              SizedBox(height: 8),
              Text('💾 Saved Tools - Manage your created tools'),
              SizedBox(height: 16),
              Text(
                'Creating Tools:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 12),
              Text('1. Create - Start from scratch, import Excel, or use template'),
              SizedBox(height: 8),
              Text('2. Backend - Build spreadsheet logic with formulas'),
              SizedBox(height: 8),
              Text('3. UI Builder - Design the user interface'),
              SizedBox(height: 8),
              Text('4. Preview & Save - Test and save your tool'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.orange),
            SizedBox(width: 8),
            Text('Tools Builder Settings'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.auto_fix_high),
              title: Text('Auto-save'),
              subtitle: Text('Automatically save changes'),
              trailing: Switch(value: true, onChanged: null),
            ),
            ListTile(
              leading: Icon(Icons.grid_on),
              title: Text('Show grid'),
              subtitle: Text('Display grid lines in spreadsheet'),
              trailing: Switch(value: true, onChanged: null),
            ),
            ListTile(
              leading: Icon(Icons.functions),
              title: Text('Advanced formulas'),
              subtitle: Text('Enable advanced formula functions'),
              trailing: Switch(value: true, onChanged: null),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
