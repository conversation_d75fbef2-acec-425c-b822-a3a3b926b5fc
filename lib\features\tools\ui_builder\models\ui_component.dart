import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// UI component types available in the builder
enum UIComponentType {
  // Input Components
  textField,
  button,
  toggle,
  dropdown,
  slider,

  // Output Components
  label,
  displayText,
  calculatedValue,
  progressBar,
  gauge,

  // Excel-Specific Components
  dataTable,
  pivotTable,
  chartWidget,
  formulaDisplay,
  cellReference,

  // Layout Components
  image,
  divider,
  container,
  spacer,
}

/// UI component model for the builder
@immutable
class UIComponent {
  final String id;
  final UIComponentType type;
  final String title;
  final Map<String, dynamic> properties;
  final Offset position;
  final Size size;
  final String? cellBinding;
  final BindingMode bindingMode;
  final DateTime lastModified;

  const UIComponent({
    required this.id,
    required this.type,
    required this.title,
    this.properties = const {},
    this.position = Offset.zero,
    this.size = const Size(120, 40),
    this.cellBinding,
    this.bindingMode = BindingMode.readWrite,
    required this.lastModified,
  });

  /// Create component with default properties
  factory UIComponent.create(UIComponentType type) {
    final now = DateTime.now();
    final id = '${type.name}_${now.millisecondsSinceEpoch}';
    
    return UIComponent(
      id: id,
      type: type,
      title: _getDefaultTitle(type),
      properties: _getDefaultProperties(type),
      size: _getDefaultSize(type),
      lastModified: now,
    );
  }

  /// Get default title for component type
  static String _getDefaultTitle(UIComponentType type) {
    switch (type) {
      // Input Components
      case UIComponentType.textField:
        return 'Text Input';
      case UIComponentType.button:
        return 'Button';
      case UIComponentType.toggle:
        return 'Toggle';
      case UIComponentType.dropdown:
        return 'Dropdown';
      case UIComponentType.slider:
        return 'Slider';

      // Output Components
      case UIComponentType.label:
        return 'Label';
      case UIComponentType.displayText:
        return 'Display Text';
      case UIComponentType.calculatedValue:
        return 'Calculated Value';
      case UIComponentType.progressBar:
        return 'Progress Bar';
      case UIComponentType.gauge:
        return 'Gauge';

      // Excel-Specific Components
      case UIComponentType.dataTable:
        return 'Data Table';
      case UIComponentType.pivotTable:
        return 'Pivot Table';
      case UIComponentType.chartWidget:
        return 'Chart Widget';
      case UIComponentType.formulaDisplay:
        return 'Formula Display';
      case UIComponentType.cellReference:
        return 'Cell Reference';

      // Layout Components
      case UIComponentType.image:
        return 'Image';
      case UIComponentType.divider:
        return 'Divider';
      case UIComponentType.container:
        return 'Container';
      case UIComponentType.spacer:
        return 'Spacer';
    }
  }

  /// Get default properties for component type
  static Map<String, dynamic> _getDefaultProperties(UIComponentType type) {
    switch (type) {
      // Input Components
      case UIComponentType.textField:
        return {
          'placeholder': 'Enter text...',
          'defaultValue': '',
          'multiline': false,
          'enabled': true,
        };
      case UIComponentType.button:
        return {
          'text': 'Click Me',
          'action': 'none',
          'enabled': true,
          'style': 'elevated',
        };
      case UIComponentType.toggle:
        return {
          'defaultValue': false,
          'enabled': true,
        };
      case UIComponentType.dropdown:
        return {
          'options': ['Option 1', 'Option 2', 'Option 3'],
          'defaultValue': null,
          'enabled': true,
        };
      case UIComponentType.slider:
        return {
          'min': 0.0,
          'max': 100.0,
          'defaultValue': 50.0,
          'divisions': null,
          'enabled': true,
        };

      // Output Components
      case UIComponentType.label:
        return {
          'text': 'Label Text',
          'alignment': 'left',
          'fontSize': 14.0,
          'fontWeight': 'normal',
        };
      case UIComponentType.displayText:
        return {
          'text': 'Display Value',
          'alignment': 'center',
          'fontSize': 16.0,
          'fontWeight': 'bold',
          'color': '#000000',
        };
      case UIComponentType.calculatedValue:
        return {
          'formula': '=SUM(A1:A10)',
          'format': 'number',
          'decimals': 2,
          'prefix': '',
          'suffix': '',
        };
      case UIComponentType.progressBar:
        return {
          'value': 50.0,
          'min': 0.0,
          'max': 100.0,
          'showValue': true,
          'color': '#2196F3',
        };
      case UIComponentType.gauge:
        return {
          'value': 75.0,
          'min': 0.0,
          'max': 100.0,
          'segments': 3,
          'showValue': true,
        };

      // Excel-Specific Components
      case UIComponentType.dataTable:
        return {
          'dataRange': 'A1:E10',
          'showHeaders': true,
          'alternateRows': true,
          'sortable': true,
        };
      case UIComponentType.pivotTable:
        return {
          'sourceRange': 'A1:D100',
          'rowFields': [],
          'columnFields': [],
          'valueFields': [],
        };
      case UIComponentType.chartWidget:
        return {
          'chartType': 'column',
          'dataRange': 'A1:B10',
          'title': 'Chart Title',
          'showLegend': true,
        };
      case UIComponentType.formulaDisplay:
        return {
          'formula': '=A1+B1',
          'showFormula': true,
          'showResult': true,
        };
      case UIComponentType.cellReference:
        return {
          'cellAddress': 'A1',
          'showAddress': true,
          'showValue': true,
        };

      // Layout Components
      case UIComponentType.image:
        return {
          'url': '',
          'fit': 'contain',
          'placeholder': 'No Image',
        };
      case UIComponentType.divider:
        return {
          'thickness': 1.0,
          'color': '#E0E0E0',
          'orientation': 'horizontal',
        };
      case UIComponentType.container:
        return {
          'backgroundColor': '#FFFFFF',
          'borderColor': '#E0E0E0',
          'borderWidth': 1.0,
          'borderRadius': 8.0,
          'padding': 16.0,
        };
      case UIComponentType.spacer:
        return {
          'height': 20.0,
          'width': 20.0,
        };
    }
  }

  /// Get default size for component type
  static Size _getDefaultSize(UIComponentType type) {
    switch (type) {
      // Input Components
      case UIComponentType.textField:
        return const Size(200, 40);
      case UIComponentType.button:
        return const Size(120, 40);
      case UIComponentType.toggle:
        return const Size(60, 40);
      case UIComponentType.dropdown:
        return const Size(150, 40);
      case UIComponentType.slider:
        return const Size(200, 40);

      // Output Components
      case UIComponentType.label:
        return const Size(100, 30);
      case UIComponentType.displayText:
        return const Size(150, 40);
      case UIComponentType.calculatedValue:
        return const Size(120, 35);
      case UIComponentType.progressBar:
        return const Size(200, 20);
      case UIComponentType.gauge:
        return const Size(150, 150);

      // Excel-Specific Components
      case UIComponentType.dataTable:
        return const Size(400, 300);
      case UIComponentType.pivotTable:
        return const Size(350, 250);
      case UIComponentType.chartWidget:
        return const Size(300, 200);
      case UIComponentType.formulaDisplay:
        return const Size(250, 60);
      case UIComponentType.cellReference:
        return const Size(100, 30);

      // Layout Components
      case UIComponentType.image:
        return const Size(150, 150);
      case UIComponentType.divider:
        return const Size(200, 20);
      case UIComponentType.container:
        return const Size(200, 150);
      case UIComponentType.spacer:
        return const Size(50, 20);
    }
  }

  /// Copy with modifications
  UIComponent copyWith({
    String? id,
    UIComponentType? type,
    String? title,
    Map<String, dynamic>? properties,
    Offset? position,
    Size? size,
    String? cellBinding,
    BindingMode? bindingMode,
    DateTime? lastModified,
  }) {
    return UIComponent(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      properties: properties ?? this.properties,
      position: position ?? this.position,
      size: size ?? this.size,
      cellBinding: cellBinding ?? this.cellBinding,
      bindingMode: bindingMode ?? this.bindingMode,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'properties': properties,
      'position': {'x': position.dx, 'y': position.dy},
      'size': {'width': size.width, 'height': size.height},
      'cellBinding': cellBinding,
      'bindingMode': bindingMode.name,
      'lastModified': lastModified.toIso8601String(),
    };
  }

  /// Create from JSON
  factory UIComponent.fromJson(Map<String, dynamic> json) {
    final positionJson = json['position'] as Map<String, dynamic>;
    final sizeJson = json['size'] as Map<String, dynamic>;
    
    return UIComponent(
      id: json['id'] as String,
      type: UIComponentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => UIComponentType.label,
      ),
      title: json['title'] as String,
      properties: Map<String, dynamic>.from(json['properties'] as Map),
      position: Offset(
        (positionJson['x'] as num).toDouble(),
        (positionJson['y'] as num).toDouble(),
      ),
      size: Size(
        (sizeJson['width'] as num).toDouble(),
        (sizeJson['height'] as num).toDouble(),
      ),
      cellBinding: json['cellBinding'] as String?,
      bindingMode: BindingMode.values.firstWhere(
        (e) => e.name == json['bindingMode'],
        orElse: () => BindingMode.readWrite,
      ),
      lastModified: DateTime.parse(json['lastModified'] as String),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UIComponent &&
        other.id == id &&
        other.type == type &&
        other.title == title &&
        mapEquals(other.properties, properties) &&
        other.position == position &&
        other.size == size &&
        other.cellBinding == cellBinding &&
        other.bindingMode == bindingMode;
  }

  @override
  int get hashCode => Object.hash(
    id, type, title, properties, position, size, cellBinding, bindingMode,
  );

  @override
  String toString() => 'UIComponent($id: $type)';
}

/// Data binding modes
enum BindingMode {
  readOnly,
  writeOnly,
  readWrite,
}

/// Component layout modes
enum LayoutMode {
  grid,
  freeform,
  responsive,
}

/// Component alignment options
enum ComponentAlignment {
  left,
  center,
  right,
  justify,
}
