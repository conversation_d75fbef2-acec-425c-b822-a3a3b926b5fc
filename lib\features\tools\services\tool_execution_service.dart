import 'dart:async';
import '../models/custom_tool.dart';
import '../models/spreadsheet_cell.dart';
import '../../../core/formula/formula_engine.dart';
import 'tool_security_service.dart';

/// Service for executing and testing custom tools
class ToolExecutionService {
  static final ToolExecutionService _instance = ToolExecutionService._internal();
  factory ToolExecutionService() => _instance;
  ToolExecutionService._internal();

  final FormulaEngine _formulaEngine = FormulaEngine();
  final ToolSecurityService _securityService = ToolSecurityService();

  // Execution state
  final Map<String, ToolExecutionState> _executionStates = {};
  final Map<String, StreamController<ToolExecutionEvent>> _eventStreams = {};

  /// Execute a custom tool with input data
  Future<ToolExecutionResult> executeTool(
    CustomTool tool,
    Map<String, dynamic> inputData, {
    bool testMode = false,
  }) async {
    try {
      // Check security if not in test mode
      if (!testMode) {
        final isAuthenticated = await _securityService.authenticateForTool(tool.id.hashCode);
        if (!isAuthenticated) {
          return ToolExecutionResult(
            success: false,
            error: 'Authentication failed',
            executionTime: Duration.zero,
          );
        }
      }

      final startTime = DateTime.now();
      
      // Initialize execution state
      final state = ToolExecutionState(
        toolId: tool.id,
        startTime: startTime,
        status: ExecutionStatus.running,
        inputData: inputData,
      );
      _executionStates[tool.id] = state;
      _broadcastEvent(tool.id, ToolExecutionEvent.started(tool.id));

      // Load backend data (spreadsheet)
      final backendData = await _loadBackendData(tool);
      
      // Apply input data to backend
      final updatedBackend = _applyInputData(backendData, inputData);
      
      // Recalculate formulas
      final calculatedBackend = _formulaEngine.recalculateSheet(updatedBackend);
      
      // Extract output data based on UI layout
      final outputData = _extractOutputData(tool.uiLayout, calculatedBackend);
      
      // Update execution state
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      
      state.status = ExecutionStatus.completed;
      state.endTime = endTime;
      state.outputData = outputData;
      state.backendData = calculatedBackend;
      
      _broadcastEvent(tool.id, ToolExecutionEvent.completed(tool.id, outputData));

      return ToolExecutionResult(
        success: true,
        outputData: outputData,
        backendData: calculatedBackend,
        executionTime: executionTime,
        logs: state.logs,
      );
    } catch (e) {
      final state = _executionStates[tool.id];
      if (state != null) {
        state.status = ExecutionStatus.error;
        state.error = e.toString();
      }
      
      _broadcastEvent(tool.id, ToolExecutionEvent.error(tool.id, e.toString()));
      
      return ToolExecutionResult(
        success: false,
        error: e.toString(),
        executionTime: DateTime.now().difference(state?.startTime ?? DateTime.now()),
      );
    }
  }

  /// Test a tool with sample data
  Future<ToolTestResult> testTool(CustomTool tool) async {
    final testCases = _generateTestCases(tool);
    final results = <TestCaseResult>[];
    
    for (final testCase in testCases) {
      final startTime = DateTime.now();
      
      try {
        final result = await executeTool(tool, testCase.inputData, testMode: true);
        final endTime = DateTime.now();
        
        results.add(TestCaseResult(
          name: testCase.name,
          inputData: testCase.inputData,
          expectedOutput: testCase.expectedOutput,
          actualOutput: result.outputData ?? {},
          success: result.success,
          executionTime: endTime.difference(startTime),
          error: result.error,
        ));
      } catch (e) {
        results.add(TestCaseResult(
          name: testCase.name,
          inputData: testCase.inputData,
          expectedOutput: testCase.expectedOutput,
          actualOutput: {},
          success: false,
          executionTime: DateTime.now().difference(startTime),
          error: e.toString(),
        ));
      }
    }
    
    final successCount = results.where((r) => r.success).length;
    final totalCount = results.length;
    
    return ToolTestResult(
      toolId: tool.id,
      toolName: tool.name,
      testCases: results,
      successRate: totalCount > 0 ? successCount / totalCount : 0.0,
      totalExecutionTime: results.fold(
        Duration.zero,
        (sum, result) => sum + result.executionTime,
      ),
    );
  }

  /// Get execution state for a tool
  ToolExecutionState? getExecutionState(String toolId) {
    return _executionStates[toolId];
  }

  /// Get execution event stream for a tool
  Stream<ToolExecutionEvent> getExecutionStream(String toolId) {
    _eventStreams[toolId] ??= StreamController<ToolExecutionEvent>.broadcast();
    return _eventStreams[toolId]!.stream;
  }

  /// Stop tool execution
  Future<void> stopExecution(String toolId) async {
    final state = _executionStates[toolId];
    if (state != null && state.status == ExecutionStatus.running) {
      state.status = ExecutionStatus.cancelled;
      state.endTime = DateTime.now();
      _broadcastEvent(toolId, ToolExecutionEvent.cancelled(toolId));
    }
  }

  /// Clear execution history
  void clearExecutionHistory(String toolId) {
    _executionStates.remove(toolId);
    _eventStreams[toolId]?.close();
    _eventStreams.remove(toolId);
  }

  /// Load backend spreadsheet data
  Future<Map<String, SpreadsheetCell>> _loadBackendData(CustomTool tool) async {
    // In a real implementation, this would load from storage
    // For now, return empty data or sample data based on tool settings
    return {}; // Empty for now - would load from tool.settings['backendData']
  }

  /// Apply input data to backend cells
  Map<String, SpreadsheetCell> _applyInputData(
    Map<String, SpreadsheetCell> backendData,
    Map<String, dynamic> inputData,
  ) {
    final result = Map<String, SpreadsheetCell>.from(backendData);
    
    for (final entry in inputData.entries) {
      final cellAddress = entry.key;
      final value = entry.value;
      
      result[cellAddress] = SpreadsheetCell(
        address: cellAddress,
        value: value,
        type: _getValueType(value),
      );
    }
    
    return result;
  }

  /// Extract output data from tool UI layout
  Map<String, dynamic> _extractOutputData(
    Map<String, dynamic> uiLayout,
    Map<String, SpreadsheetCell> backendData,
  ) {
    final outputData = <String, dynamic>{};

    final components = uiLayout['components'] as List<dynamic>? ?? [];

    for (final componentData in components) {
      final component = componentData as Map<String, dynamic>;
      final type = component['type'] as String?;

      if (type == 'output') {
        // Extract output based on component properties
        final cellBinding = component['cellBinding'] as String?;
        if (cellBinding != null) {
          final cell = backendData[cellBinding];
          if (cell != null) {
            final componentId = component['id'] as String? ?? cellBinding;
            outputData[componentId] = cell.value;
          }
        }
      }
    }

    return outputData;
  }

  /// Generate test cases for a tool
  List<TestCase> _generateTestCases(CustomTool tool) {
    final testCases = <TestCase>[];

    // Find input components from UI layout
    final components = tool.uiLayout['components'] as List<dynamic>? ?? [];
    final inputComponents = components
        .cast<Map<String, dynamic>>()
        .where((c) => {
              'input',
              'dropdown',
              'slider'
            }.contains(c['type'] as String?))
        .toList();

    if (inputComponents.isEmpty) {
      // No inputs, create a basic test case
      testCases.add(TestCase(
        name: 'Basic Test',
        inputData: {},
        expectedOutput: {},
      ));
    } else {
      // Generate test cases with different input combinations
      testCases.add(TestCase(
        name: 'Zero Values Test',
        inputData: Map.fromEntries(
          inputComponents.map((c) {
            final cellBinding = c['cellBinding'] as String? ?? c['id'] as String? ?? 'A1';
            return MapEntry(cellBinding, 0);
          }),
        ),
        expectedOutput: {},
      ));

      testCases.add(TestCase(
        name: 'Positive Values Test',
        inputData: Map.fromEntries(
          inputComponents.map((c) {
            final cellBinding = c['cellBinding'] as String? ?? c['id'] as String? ?? 'A1';
            return MapEntry(cellBinding, 100);
          }),
        ),
        expectedOutput: {},
      ));

      testCases.add(TestCase(
        name: 'Negative Values Test',
        inputData: Map.fromEntries(
          inputComponents.map((c) {
            final cellBinding = c['cellBinding'] as String? ?? c['id'] as String? ?? 'A1';
            return MapEntry(cellBinding, -50);
          }),
        ),
        expectedOutput: {},
      ));

      testCases.add(TestCase(
        name: 'Mixed Values Test',
        inputData: Map.fromEntries(
          inputComponents.asMap().entries.map((entry) {
            final index = entry.key;
            final component = entry.value;
            final value = index % 2 == 0 ? 25 : 75;
            final cellBinding = component['cellBinding'] as String? ?? component['id'] as String? ?? 'A1';
            return MapEntry(cellBinding, value);
          }),
        ),
        expectedOutput: {},
      ));
    }

    return testCases;
  }

  /// Determine cell type based on value
  CellType _getValueType(dynamic value) {
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    return CellType.text;
  }

  /// Broadcast execution event
  void _broadcastEvent(String toolId, ToolExecutionEvent event) {
    final stream = _eventStreams[toolId];
    if (stream != null && !stream.isClosed) {
      stream.add(event);
    }
  }

  /// Dispose resources
  void dispose() {
    for (final stream in _eventStreams.values) {
      stream.close();
    }
    _eventStreams.clear();
    _executionStates.clear();
  }
}

/// Tool execution state
class ToolExecutionState {
  final String toolId;
  final DateTime startTime;
  final Map<String, dynamic> inputData;

  ExecutionStatus status;
  DateTime? endTime;
  Map<String, dynamic>? outputData;
  Map<String, SpreadsheetCell>? backendData;
  String? error;
  List<String> logs = [];

  ToolExecutionState({
    required this.toolId,
    required this.startTime,
    required this.status,
    required this.inputData,
  });

  Duration get executionTime {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }
}

/// Execution status enum
enum ExecutionStatus {
  running,
  completed,
  error,
  cancelled,
}

/// Tool execution result
class ToolExecutionResult {
  final bool success;
  final Map<String, dynamic>? outputData;
  final Map<String, SpreadsheetCell>? backendData;
  final Duration executionTime;
  final String? error;
  final List<String>? logs;

  const ToolExecutionResult({
    required this.success,
    this.outputData,
    this.backendData,
    required this.executionTime,
    this.error,
    this.logs,
  });
}

/// Tool test result
class ToolTestResult {
  final String toolId;
  final String toolName;
  final List<TestCaseResult> testCases;
  final double successRate;
  final Duration totalExecutionTime;

  const ToolTestResult({
    required this.toolId,
    required this.toolName,
    required this.testCases,
    required this.successRate,
    required this.totalExecutionTime,
  });

  bool get allTestsPassed => successRate == 1.0;
  int get passedCount => testCases.where((t) => t.success).length;
  int get failedCount => testCases.where((t) => !t.success).length;
}

/// Test case data
class TestCase {
  final String name;
  final Map<String, dynamic> inputData;
  final Map<String, dynamic> expectedOutput;

  const TestCase({
    required this.name,
    required this.inputData,
    required this.expectedOutput,
  });
}

/// Test case result
class TestCaseResult {
  final String name;
  final Map<String, dynamic> inputData;
  final Map<String, dynamic> expectedOutput;
  final Map<String, dynamic> actualOutput;
  final bool success;
  final Duration executionTime;
  final String? error;

  const TestCaseResult({
    required this.name,
    required this.inputData,
    required this.expectedOutput,
    required this.actualOutput,
    required this.success,
    required this.executionTime,
    this.error,
  });
}

/// Tool execution event
class ToolExecutionEvent {
  final String toolId;
  final ExecutionEventType type;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final String? message;

  const ToolExecutionEvent({
    required this.toolId,
    required this.type,
    required this.timestamp,
    this.data,
    this.message,
  });

  factory ToolExecutionEvent.started(String toolId) {
    return ToolExecutionEvent(
      toolId: toolId,
      type: ExecutionEventType.started,
      timestamp: DateTime.now(),
      message: 'Tool execution started',
    );
  }

  factory ToolExecutionEvent.completed(String toolId, Map<String, dynamic> outputData) {
    return ToolExecutionEvent(
      toolId: toolId,
      type: ExecutionEventType.completed,
      timestamp: DateTime.now(),
      data: outputData,
      message: 'Tool execution completed successfully',
    );
  }

  factory ToolExecutionEvent.error(String toolId, String error) {
    return ToolExecutionEvent(
      toolId: toolId,
      type: ExecutionEventType.error,
      timestamp: DateTime.now(),
      message: 'Tool execution failed: $error',
    );
  }

  factory ToolExecutionEvent.cancelled(String toolId) {
    return ToolExecutionEvent(
      toolId: toolId,
      type: ExecutionEventType.cancelled,
      timestamp: DateTime.now(),
      message: 'Tool execution cancelled',
    );
  }
}

/// Execution event types
enum ExecutionEventType {
  started,
  completed,
  error,
  cancelled,
  progress,
}
