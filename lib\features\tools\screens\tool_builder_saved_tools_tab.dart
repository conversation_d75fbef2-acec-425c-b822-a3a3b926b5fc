import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/custom_tool_provider.dart';
import '../models/custom_tool.dart';

/// Saved Tools tab for managing user-created tools
class ToolBuilderSavedToolsTab extends ConsumerStatefulWidget {
  const ToolBuilderSavedToolsTab({super.key});

  @override
  ConsumerState<ToolBuilderSavedToolsTab> createState() => _ToolBuilderSavedToolsTabState();
}

class _ToolBuilderSavedToolsTabState extends ConsumerState<ToolBuilderSavedToolsTab> {
  String _searchQuery = '';
  String _sortBy = 'name';
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    // Load saved tools
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(customToolsProvider.notifier).loadTools();
    });
  }

  @override
  Widget build(BuildContext context) {
    final toolsState = ref.watch(customToolsProvider);
    final filteredTools = _getFilteredTools(toolsState.tools);
    
    return Column(
      children: [
        // Search and Controls Bar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            children: [
              // Search Bar
              TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Search your tools...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
              ),
              const SizedBox(height: 12),
              
              // Controls Row
              Row(
                children: [
                  // Sort Dropdown
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _sortBy,
                      decoration: InputDecoration(
                        labelText: 'Sort by',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'name', child: Text('Name')),
                        DropdownMenuItem(value: 'created', child: Text('Date Created')),
                        DropdownMenuItem(value: 'modified', child: Text('Last Modified')),
                        DropdownMenuItem(value: 'usage', child: Text('Usage Count')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _sortBy = value!;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // View Toggle
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _isGridView = !_isGridView;
                      });
                    },
                    icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
                    tooltip: _isGridView ? 'List View' : 'Grid View',
                  ),
                  
                  // Refresh Button
                  IconButton(
                    onPressed: () {
                      ref.read(customToolsProvider.notifier).loadTools();
                    },
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Refresh',
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // Tools List/Grid
        Expanded(
          child: toolsState.isLoading
              ? const Center(child: CircularProgressIndicator())
              : toolsState.error != null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error_outline, size: 64, color: Colors.red),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading tools',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            toolsState.error!,
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              ref.read(customToolsProvider.notifier).loadTools();
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                  : filteredTools.isEmpty
                      ? _buildEmptyState()
                      : RefreshIndicator(
                          onRefresh: () => ref.read(customToolsProvider.notifier).loadTools(),
                          child: _isGridView
                              ? _buildGridView(filteredTools)
                              : _buildListView(filteredTools),
                        ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.build_circle_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty ? 'No tools created yet' : 'No tools found',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'Create your first tool using the Tools Builder tab'
                : 'Try adjusting your search criteria',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to Tools Builder tab
                DefaultTabController.of(context).animateTo(2);
              },
              icon: const Icon(Icons.add),
              label: const Text('Create Tool'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGridView(List<CustomTool> tools) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getCrossAxisCount(context),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: tools.length,
      itemBuilder: (context, index) {
        final tool = tools[index];
        return _buildToolCard(tool);
      },
    );
  }

  Widget _buildListView(List<CustomTool> tools) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tools.length,
      itemBuilder: (context, index) {
        final tool = tools[index];
        return _buildToolListTile(tool);
      },
    );
  }

  Widget _buildToolCard(CustomTool tool) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _runTool(tool),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with menu
              Row(
                children: [
                  Expanded(
                    child: Text(
                      tool.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (action) => _handleToolAction(action, tool),
                    itemBuilder: (context) => [
                      const PopupMenuItem(value: 'run', child: Text('Run Tool')),
                      const PopupMenuItem(value: 'edit', child: Text('Edit')),
                      const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
                      const PopupMenuItem(value: 'export', child: Text('Export')),
                      const PopupMenuItem(value: 'delete', child: Text('Delete')),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Description
              Text(
                tool.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const Spacer(),
              
              // Footer info
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      _formatDate(tool.updatedAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      tool.category,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToolListTile(CustomTool tool) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.build_circle,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        title: Text(
          tool.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(tool.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  'Modified: ${_formatDate(tool.updatedAt)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    tool.category,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (action) => _handleToolAction(action, tool),
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'run', child: Text('Run Tool')),
            const PopupMenuItem(value: 'edit', child: Text('Edit')),
            const PopupMenuItem(value: 'duplicate', child: Text('Duplicate')),
            const PopupMenuItem(value: 'export', child: Text('Export')),
            const PopupMenuItem(value: 'delete', child: Text('Delete')),
          ],
        ),
        onTap: () => _runTool(tool),
      ),
    );
  }

  List<CustomTool> _getFilteredTools(List<CustomTool> tools) {
    var filtered = tools.where((tool) {
      if (_searchQuery.isEmpty) return true;
      return tool.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             tool.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             tool.category.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    // Sort tools
    switch (_sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'created':
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'modified':
        filtered.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        break;
      case 'usage':
        // Sort by usage count (if available)
        filtered.sort((a, b) => (b.usageCount ?? 0).compareTo(a.usageCount ?? 0));
        break;
    }

    return filtered;
  }

  void _runTool(CustomTool tool) {
    // Navigate to tool execution screen
    // This will be implemented to run the tool through its custom UI
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Running ${tool.name}...')),
    );
  }

  void _handleToolAction(String action, CustomTool tool) {
    switch (action) {
      case 'run':
        _runTool(tool);
        break;
      case 'edit':
        _editTool(tool);
        break;
      case 'duplicate':
        _duplicateTool(tool);
        break;
      case 'export':
        _exportTool(tool);
        break;
      case 'delete':
        _deleteTool(tool);
        break;
    }
  }

  void _editTool(CustomTool tool) {
    // Navigate to Tools Builder tab with tool loaded for editing
    DefaultTabController.of(context).animateTo(2);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Editing ${tool.name}...')),
    );
  }

  void _duplicateTool(CustomTool tool) {
    ref.read(customToolsProvider.notifier).duplicateTool(tool);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${tool.name} duplicated successfully')),
    );
  }

  void _exportTool(CustomTool tool) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Exporting ${tool.name}...')),
    );
  }

  void _deleteTool(CustomTool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tool'),
        content: Text('Are you sure you want to delete "${tool.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(customToolsProvider.notifier).deleteTool(tool.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${tool.name} deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 7) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }
}
