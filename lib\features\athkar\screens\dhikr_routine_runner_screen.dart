import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/custom_dhikr_routine.dart';
import '../models/dhikr_session.dart';
import '../providers/dhikr_session_provider.dart';

class DhikrRoutineRunnerScreen extends ConsumerStatefulWidget {
  final CustomDhikrRoutine routine;
  final bool isPreview;

  const DhikrRoutineRunnerScreen({
    super.key,
    required this.routine,
    this.isPreview = false,
  });

  @override
  ConsumerState<DhikrRoutineRunnerScreen> createState() => _DhikrRoutineRunnerScreenState();
}

class _DhikrRoutineRunnerScreenState extends ConsumerState<DhikrRoutineRunnerScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  int _currentStepIndex = 0;
  int _currentCount = 0;
  bool _isRunning = false;
  bool _isPaused = false;
  DateTime? _startTime;
  Duration _totalDuration = Duration.zero;
  List<int> _stepProgress = [];
  bool _isCompleted = false;
  int _totalSteps = 0;

  @override
  void initState() {
    super.initState();
    
    _pageController = PageController();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));

    // Initialize progress tracking
    _stepProgress = List.filled(widget.routine.steps.length, 0);
    _totalSteps = widget.routine.steps.fold(0, (sum, step) => sum + step.targetCount);

    if (!widget.isPreview) {
      _startRoutine();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _startRoutine() {
    setState(() {
      _isRunning = true;
      _startTime = DateTime.now();
    });
  }

  void _pauseRoutine() {
    setState(() {
      _isPaused = !_isPaused;
    });
  }

  void _resetRoutine() {
    setState(() {
      _currentStepIndex = 0;
      _currentCount = 0;
      _isRunning = false;
      _isPaused = false;
      _startTime = null;
      _totalDuration = Duration.zero;
    });
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _nextStep() {
    if (_currentStepIndex < widget.routine.steps.length - 1) {
      setState(() {
        _currentStepIndex++;
        _currentCount = 0;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeRoutine();
    }
  }

  void _previousStep() {
    if (_currentStepIndex > 0) {
      setState(() {
        _currentStepIndex--;
        _currentCount = 0;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeRoutine() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Routine Completed!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('You have completed the "${widget.routine.name}" routine'),
            if (_startTime != null)
              Text('Time taken: ${_formatDuration(DateTime.now().difference(_startTime!))}'),
            const SizedBox(height: 16),
            const Text('May Allah accept your dhikr and grant you His blessings.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Finish'),
          ),
          if (widget.routine.repeatRoutine)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _resetRoutine();
                _startRoutine();
              },
              child: const Text('Repeat'),
            ),
        ],
      ),
    );
  }

  void _incrementCount() {
    if (!_isPaused && _isRunning) {
      HapticFeedback.lightImpact();
      _pulseController.forward().then((_) {
        _pulseController.reverse();
      });
      
      final currentStep = widget.routine.steps[_currentStepIndex];
      if (currentStep.type == DhikrStepType.dhikr) {
        setState(() {
          _currentCount++;
          _stepProgress[_currentStepIndex] = _currentCount;
        });

        if (_currentCount >= currentStep.targetCount) {
          Future.delayed(const Duration(milliseconds: 500), () {
            _nextStep();
          });
        }
      }
    }
  }

  double _getOverallProgress() {
    if (_totalSteps == 0) return 0.0;

    int completedSteps = 0;
    for (int i = 0; i < _stepProgress.length; i++) {
      completedSteps += _stepProgress[i];
    }

    return completedSteps / _totalSteps;
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isPreview ? 'Preview: ${widget.routine.name}' : widget.routine.name),
        backgroundColor: widget.routine.themeColor,
        foregroundColor: Colors.white,
        actions: [
          if (!widget.isPreview) ...[
            IconButton(
              onPressed: _pauseRoutine,
              icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
            ),
            IconButton(
              onPressed: _resetRoutine,
              icon: const Icon(Icons.refresh),
            ),
          ],
        ],
      ),
      body: Column(
        children: [
          // Enhanced Progress Indicator
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Overall routine progress
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Overall Progress',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: _getOverallProgress(),
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(widget.routine.themeColor),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${(_getOverallProgress() * 100).toInt()}%',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: widget.routine.themeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Step progress
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Step ${_currentStepIndex + 1} of ${widget.routine.steps.length}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: widget.routine.steps.isEmpty
                                ? 0.0
                                : (_currentStepIndex + 1) / widget.routine.steps.length,
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(widget.routine.themeColor.withValues(alpha: 0.7)),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: widget.routine.themeColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: widget.routine.themeColor.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        '${_currentStepIndex + 1}/${widget.routine.steps.length}',
                        style: TextStyle(
                          color: widget.routine.themeColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Steps Content
          Expanded(
            child: widget.routine.steps.isEmpty
                ? const Center(
                    child: Text('No steps in this routine'),
                  )
                : PageView.builder(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentStepIndex = index;
                        _currentCount = 0;
                      });
                    },
                    itemCount: widget.routine.steps.length,
                    itemBuilder: (context, index) {
                      final step = widget.routine.steps[index];
                      return _buildStepContent(step);
                    },
                  ),
          ),
          
          // Enhanced Navigation Controls
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Main Count Button (if not preview)
                if (!widget.isPreview) ...[
                  Container(
                    width: double.infinity,
                    height: 80,
                    margin: const EdgeInsets.only(bottom: 16),
                    child: ElevatedButton(
                      onPressed: _isPaused ? null : _incrementCount,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.routine.themeColor,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: Colors.grey[300],
                        disabledForegroundColor: Colors.grey[600],
                        elevation: 8,
                        shadowColor: widget.routine.themeColor.withValues(alpha: 0.4),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _isPaused ? Icons.pause_circle : Icons.touch_app,
                            size: 32,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            _isPaused ? 'Paused' : 'Tap to Count',
                            style: const TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                // Navigation Row
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 56,
                        margin: const EdgeInsets.only(right: 8),
                        child: ElevatedButton.icon(
                          onPressed: _currentStepIndex > 0 ? _previousStep : null,
                          icon: const Icon(Icons.arrow_back_ios, size: 20),
                          label: const Text(
                            'Previous',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[600],
                            foregroundColor: Colors.white,
                            disabledBackgroundColor: Colors.grey[300],
                            disabledForegroundColor: Colors.grey[500],
                            elevation: 4,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        height: 56,
                        margin: const EdgeInsets.only(left: 8),
                        child: ElevatedButton.icon(
                          onPressed: _currentStepIndex < widget.routine.steps.length - 1
                              ? _nextStep
                              : _completeRoutine,
                          icon: Icon(
                            _currentStepIndex < widget.routine.steps.length - 1
                                ? Icons.arrow_forward_ios
                                : Icons.check_circle,
                            size: 20,
                          ),
                          label: Text(
                            _currentStepIndex < widget.routine.steps.length - 1
                                ? 'Next'
                                : 'Complete',
                            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _currentStepIndex < widget.routine.steps.length - 1
                                ? widget.routine.themeColor
                                : Colors.green,
                            foregroundColor: Colors.white,
                            elevation: 4,
                            shadowColor: (_currentStepIndex < widget.routine.steps.length - 1
                                ? widget.routine.themeColor
                                : Colors.green).withValues(alpha: 0.3),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(DhikrStep step) {
    switch (step.type) {
      case DhikrStepType.dhikr:
        return _buildDhikrStep(step);
      case DhikrStepType.pause:
        return _buildPauseStep(step);
      case DhikrStepType.reminder:
        return _buildReminderStep(step);
    }
  }

  Widget _buildDhikrStep(DhikrStep step) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Dhikr Text
          Expanded(
            flex: 2,
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (step.arabicText.isNotEmpty) ...[
                      Text(
                        step.arabicText,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    if (step.transliteration.isNotEmpty) ...[
                      Text(
                        step.transliteration,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: widget.routine.themeColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                    ],
                    
                    if (step.translation.isNotEmpty)
                      Text(
                        step.translation,
                        style: Theme.of(context).textTheme.bodyLarge,
                        textAlign: TextAlign.center,
                      ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Counter
          Expanded(
            flex: 1,
            child: Column(
              children: [
                // Enhanced Progress Ring
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: step.color.withValues(alpha: 0.3),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: SizedBox(
                    width: 160,
                    height: 160,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        CircularProgressIndicator(
                          value: step.targetCount > 0 ? _currentCount / step.targetCount : 0.0,
                          strokeWidth: 12,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(step.color),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '$_currentCount',
                              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                fontWeight: FontWeight.w800,
                                color: step.color,
                                fontSize: 32,
                              ),
                            ),
                            Text(
                              'of ${step.targetCount}',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${((step.targetCount > 0 ? _currentCount / step.targetCount : 0.0) * 100).toInt()}%',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: step.color,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPauseStep(DhikrStep step) {
    return Center(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.pause_circle_outline,
                size: 64,
                color: step.color,
              ),
              const SizedBox(height: 16),
              Text(
                'Pause',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Take a ${step.duration.inSeconds} second break',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReminderStep(DhikrStep step) {
    return Center(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.notifications_active,
                size: 64,
                color: step.color,
              ),
              const SizedBox(height: 16),
              Text(
                'Reminder',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 8),
              Text(
                step.displayText,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
