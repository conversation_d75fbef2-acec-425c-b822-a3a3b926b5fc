import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/voice_memo.dart';

/// Service for handling voice recording functionality
class VoiceRecordingService {
  static final VoiceRecordingService _instance = VoiceRecordingService._internal();
  factory VoiceRecordingService() => _instance;
  VoiceRecordingService._internal();

  bool _isRecording = false;
  bool _isPaused = false;
  DateTime? _recordingStartTime;
  String? _currentRecordingPath;
  
  // Recording settings
  int _sampleRate = 44100;
  int _bitRate = 128000;
  String _audioFormat = 'wav';

  /// Check and request microphone permission with persistent storage
  Future<bool> checkMicrophonePermission() async {
    try {
      // On Windows, microphone permission is handled differently
      if (Platform.isWindows) {
        // For Windows, we assume permission is granted
        // In a real app, you might want to check Windows-specific permissions
        return true;
      }

      // Check if we've already granted permission (cached)
      final prefs = await SharedPreferences.getInstance();
      final cachedPermission = prefs.getBool('microphone_permission_granted');

      // For other platforms, use permission_handler
      final status = await Permission.microphone.status;

      if (status.isGranted) {
        // Cache the permission
        await prefs.setBool('microphone_permission_granted', true);
        return true;
      } else if (status.isDenied) {
        final result = await Permission.microphone.request();
        final granted = result.isGranted;

        // Cache the result
        await prefs.setBool('microphone_permission_granted', granted);

        return granted;
      } else if (status.isPermanentlyDenied) {
        // Clear cached permission
        await prefs.setBool('microphone_permission_granted', false);

        // Show dialog to open app settings
        await openAppSettings();
        return false;
      }

      // If cached permission exists and status is unknown, use cached value
      if (cachedPermission == true) {
        return true;
      }

      return false;
    } catch (e) {
      print('Error checking microphone permission: $e');
      return false;
    }
  }

  /// Start recording
  Future<bool> startRecording({String? fileName}) async {
    try {
      if (_isRecording) {
        print('Already recording');
        return false;
      }

      // Check permission first
      final hasPermission = await checkMicrophonePermission();
      if (!hasPermission) {
        throw Exception('Microphone permission not granted');
      }

      // Get recording directory
      final directory = await _getRecordingDirectory();
      
      // Generate filename if not provided
      fileName ??= 'recording_${DateTime.now().millisecondsSinceEpoch}.$_audioFormat';
      _currentRecordingPath = '${directory.path}/$fileName';

      // Start recording (platform-specific implementation)
      await _startPlatformRecording(_currentRecordingPath!);
      
      _isRecording = true;
      _isPaused = false;
      _recordingStartTime = DateTime.now();
      
      print('Recording started: $_currentRecordingPath');
      return true;
    } catch (e) {
      print('Error starting recording: $e');
      return false;
    }
  }

  /// Stop recording
  Future<String?> stopRecording() async {
    try {
      if (!_isRecording) {
        print('Not currently recording');
        return null;
      }

      // Stop platform-specific recording
      await _stopPlatformRecording();
      
      final recordingPath = _currentRecordingPath;
      
      _isRecording = false;
      _isPaused = false;
      _recordingStartTime = null;
      _currentRecordingPath = null;
      
      print('Recording stopped: $recordingPath');
      return recordingPath;
    } catch (e) {
      print('Error stopping recording: $e');
      return null;
    }
  }

  /// Pause recording
  Future<bool> pauseRecording() async {
    try {
      if (!_isRecording || _isPaused) {
        return false;
      }

      await _pausePlatformRecording();
      _isPaused = true;
      
      print('Recording paused');
      return true;
    } catch (e) {
      print('Error pausing recording: $e');
      return false;
    }
  }

  /// Resume recording
  Future<bool> resumeRecording() async {
    try {
      if (!_isRecording || !_isPaused) {
        return false;
      }

      await _resumePlatformRecording();
      _isPaused = false;
      
      print('Recording resumed');
      return true;
    } catch (e) {
      print('Error resuming recording: $e');
      return false;
    }
  }

  /// Get current recording duration
  Duration get currentRecordingDuration {
    if (_recordingStartTime == null) return Duration.zero;
    return DateTime.now().difference(_recordingStartTime!);
  }

  /// Get recording status
  bool get isRecording => _isRecording;
  bool get isPaused => _isPaused;
  bool get isActive => _isRecording && !_isPaused;

  /// Platform-specific recording implementations
  Future<void> _startPlatformRecording(String filePath) async {
    if (Platform.isWindows) {
      await _startWindowsRecording(filePath);
    } else {
      // For other platforms, use a different recording library
      await _startGenericRecording(filePath);
    }
  }

  Future<void> _stopPlatformRecording() async {
    if (Platform.isWindows) {
      await _stopWindowsRecording();
    } else {
      await _stopGenericRecording();
    }
  }

  Future<void> _pausePlatformRecording() async {
    if (Platform.isWindows) {
      await _pauseWindowsRecording();
    } else {
      await _pauseGenericRecording();
    }
  }

  Future<void> _resumePlatformRecording() async {
    if (Platform.isWindows) {
      await _resumeWindowsRecording();
    } else {
      await _resumeGenericRecording();
    }
  }

  /// Windows-specific recording implementation
  Future<void> _startWindowsRecording(String filePath) async {
    // For Windows, we'll use a simplified approach
    // In a real implementation, you'd use FFI or a Windows-specific plugin
    print('Starting Windows recording to: $filePath');
    
    // Create empty file for now (placeholder)
    final file = File(filePath);
    await file.create(recursive: true);
    await file.writeAsBytes(Uint8List(0));
  }

  Future<void> _stopWindowsRecording() async {
    print('Stopping Windows recording');
    // Implementation would stop the actual recording
  }

  Future<void> _pauseWindowsRecording() async {
    print('Pausing Windows recording');
    // Implementation would pause the recording
  }

  Future<void> _resumeWindowsRecording() async {
    print('Resuming Windows recording');
    // Implementation would resume the recording
  }

  /// Generic recording implementation for other platforms
  Future<void> _startGenericRecording(String filePath) async {
    print('Starting generic recording to: $filePath');
    // Use record package or similar for other platforms
  }

  Future<void> _stopGenericRecording() async {
    print('Stopping generic recording');
  }

  Future<void> _pauseGenericRecording() async {
    print('Pausing generic recording');
  }

  Future<void> _resumeGenericRecording() async {
    print('Resuming generic recording');
  }

  /// Get recording directory
  Future<Directory> _getRecordingDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final recordingDir = Directory('${appDir.path}/voice_recordings');
    
    if (!await recordingDir.exists()) {
      await recordingDir.create(recursive: true);
    }
    
    return recordingDir;
  }

  /// Update recording settings
  void updateSettings({
    int? sampleRate,
    int? bitRate,
    String? audioFormat,
  }) {
    _sampleRate = sampleRate ?? _sampleRate;
    _bitRate = bitRate ?? _bitRate;
    _audioFormat = audioFormat ?? _audioFormat;
  }

  /// Get current settings
  Map<String, dynamic> get currentSettings => {
    'sampleRate': _sampleRate,
    'bitRate': _bitRate,
    'audioFormat': _audioFormat,
  };

  /// Get available audio formats
  List<String> get availableFormats => ['wav', 'mp3', 'aac', 'm4a'];

  /// Get available sample rates
  List<int> get availableSampleRates => [8000, 16000, 22050, 44100, 48000];

  /// Get available bit rates
  List<int> get availableBitRates => [64000, 96000, 128000, 192000, 256000, 320000];

  /// Cleanup resources
  void dispose() {
    if (_isRecording) {
      stopRecording();
    }
  }
}
