import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr.dart';
import '../models/dhikr_session.dart';
import '../providers/dhikr_session_provider.dart';
import '../providers/athkar_display_preferences_provider.dart';
import '../models/athkar_display_preferences.dart';

class DhikrCounterScreen extends ConsumerStatefulWidget {
  final Dhikr dhikr;
  final DhikrSession? existingSession;

  const DhikrCounterScreen({
    super.key,
    required this.dhikr,
    this.existingSession,
  });

  @override
  ConsumerState<DhikrCounterScreen> createState() => _DhikrCounterScreenState();
}

class _DhikrCounterScreenState extends ConsumerState<DhikrCounterScreen>
    with TickerProviderStateMixin {
  late DhikrSession _session;
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize session
    if (widget.existingSession != null) {
      _session = widget.existingSession!;
    } else {
      _session = DhikrSession.create(
        dhikrId: widget.dhikr.id,
        targetCount: widget.dhikr.recommendedCount,
        userId: '', // Will be set by provider
      );
    }

    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: _session.progressPercentage,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // Start session if new
    if (widget.existingSession == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(dhikrSessionsProvider.notifier).startSession(
          widget.dhikr.id,
          widget.dhikr.recommendedCount,
        ).then((session) {
          setState(() {
            _session = session;
          });
        });
      });
    }

    _progressController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dhikr Counter'),
        actions: [
          IconButton(
            onPressed: _showOptions,
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Dhikr Text with Translation Preferences
              Expanded(
                flex: 2,
                child: Consumer(
                  builder: (context, ref, child) {
                    final preferences = ref.watch(athkarDisplayPreferencesProvider);
                    return Card(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: _buildDhikrTextWidgets(context, preferences),
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Progress Section
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    // Progress Ring
                    SizedBox(
                      width: 120,
                      height: 120,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Background Circle
                          CircularProgressIndicator(
                            value: 1.0,
                            strokeWidth: 8,
                            backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                            ),
                          ),
                          
                          // Progress Circle
                          AnimatedBuilder(
                            animation: _progressAnimation,
                            builder: (context, child) {
                              return CircularProgressIndicator(
                                value: _session.progressPercentage,
                                strokeWidth: 8,
                                backgroundColor: Colors.transparent,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context).colorScheme.primary,
                                ),
                              );
                            },
                          ),
                          
                          // Count Text
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                '${_session.currentCount}',
                                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                              Text(
                                'of ${_session.targetCount}',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Timer
                    Text(
                      _session.formattedDuration,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Enhanced Counter Button
              Expanded(
                flex: 2,
                child: Center(
                  child: AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: GestureDetector(
                          onTap: _incrementCount,
                          child: Container(
                            width: 280,
                            height: 280,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: _session.isCompleted
                                  ? const LinearGradient(
                                      colors: [Colors.green, Color(0xFF4CAF50)],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    )
                                  : LinearGradient(
                                      colors: [
                                        Theme.of(context).colorScheme.primary,
                                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                              boxShadow: [
                                BoxShadow(
                                  color: (_session.isCompleted ? Colors.green : Theme.of(context).colorScheme.primary)
                                      .withValues(alpha: 0.4),
                                  blurRadius: 30,
                                  spreadRadius: 8,
                                  offset: const Offset(0, 8),
                                ),
                                BoxShadow(
                                  color: (_session.isCompleted ? Colors.green : Theme.of(context).colorScheme.primary)
                                      .withValues(alpha: 0.2),
                                  blurRadius: 60,
                                  spreadRadius: 15,
                                  offset: const Offset(0, 15),
                                ),
                              ],
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 3,
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _session.isCompleted ? Icons.check_circle : Icons.touch_app,
                                  size: 100,
                                  color: Colors.white,
                                ),
                                const SizedBox(height: 12),
                                Text(
                                  _session.isCompleted ? 'Completed!' : 'Tap to Count',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 20,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                if (!_session.isCompleted) ...[
                                  const SizedBox(height: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      '${_session.currentCount} / ${_session.targetCount}',
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Enhanced Action Buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Primary Action Row
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 60,
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            child: ElevatedButton.icon(
                              onPressed: _session.currentCount > 0 ? _decrementCount : null,
                              icon: const Icon(Icons.remove_circle_outline, size: 24),
                              label: const Text(
                                'Undo',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                                disabledBackgroundColor: Colors.grey[300],
                                disabledForegroundColor: Colors.grey[600],
                                elevation: 4,
                                shadowColor: Colors.orange.withValues(alpha: 0.3),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Container(
                            height: 60,
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            child: ElevatedButton.icon(
                              onPressed: _resetSession,
                              icon: const Icon(Icons.refresh, size: 24),
                              label: const Text(
                                'Reset',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                elevation: 4,
                                shadowColor: Colors.blue.withValues(alpha: 0.3),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // Complete Button
                    SizedBox(
                      width: double.infinity,
                      height: 60,
                      child: ElevatedButton.icon(
                        onPressed: _session.isCompleted ? null : _completeSession,
                        icon: const Icon(Icons.check_circle, size: 24),
                        label: const Text(
                          'Complete Session',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          disabledBackgroundColor: Colors.grey[300],
                          disabledForegroundColor: Colors.grey[600],
                          elevation: 6,
                          shadowColor: Colors.green.withValues(alpha: 0.4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildDhikrTextWidgets(BuildContext context, AthkarDisplayPreferences? preferences) {
    final List<Widget> widgets = [];

    // Use default preferences if null
    final prefs = preferences ?? AthkarDisplayPreferences.createDefault('default');

    // Arabic Text
    if (prefs.shouldShowArabic) {
      widgets.add(
        Text(
          widget.dhikr.arabicText,
          style: TextStyle(
            fontSize: prefs.arabicFontSize,
            fontWeight: FontWeight.w700,
            fontFamily: prefs.arabicFontFamily == 'default' ? null : prefs.arabicFontFamily,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
          textDirection: prefs.enableRightToLeft ? TextDirection.rtl : TextDirection.ltr,
        ),
      );
    }

    // Transliteration
    if (prefs.shouldShowTransliteration && widget.dhikr.transliteration.isNotEmpty) {
      if (widgets.isNotEmpty) widgets.add(const SizedBox(height: 16));
      widgets.add(
        Text(
          widget.dhikr.transliteration,
          style: TextStyle(
            fontSize: prefs.transliterationFontSize,
            fontStyle: FontStyle.italic,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).colorScheme.primary,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    // Translation
    if (prefs.shouldShowTranslation && widget.dhikr.translation.isNotEmpty) {
      if (widgets.isNotEmpty) widgets.add(const SizedBox(height: 12));
      widgets.add(
        Text(
          widget.dhikr.translation,
          style: TextStyle(
            fontSize: prefs.translationFontSize,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    // Reference
    if (prefs.showReference && widget.dhikr.reference != null) {
      if (widgets.isNotEmpty) widgets.add(const SizedBox(height: 12));
      widgets.add(
        Text(
          widget.dhikr.reference!,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontStyle: FontStyle.italic,
            color: Theme.of(context).colorScheme.outline,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return widgets;
  }

  void _incrementCount() {
    if (!_session.isCompleted) {
      HapticFeedback.lightImpact();
      _pulseController.forward().then((_) {
        _pulseController.reverse();
      });

      setState(() {
        _session.increment();
      });

      ref.read(dhikrSessionsProvider.notifier).updateSession(_session);

      if (_session.isCompleted) {
        _showCompletionDialog();
      }
    }
  }

  void _decrementCount() {
    if (_session.currentCount > 0) {
      HapticFeedback.lightImpact();
      setState(() {
        _session.decrement();
      });
      ref.read(dhikrSessionsProvider.notifier).updateSession(_session);
    }
  }

  void _resetSession() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Session'),
        content: const Text('Are you sure you want to reset this dhikr session?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _session.reset();
              });
              ref.read(dhikrSessionsProvider.notifier).updateSession(_session);
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _completeSession() {
    setState(() {
      _session.complete();
    });
    ref.read(dhikrSessionsProvider.notifier).updateSession(_session);
    _showCompletionDialog();
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Dhikr Completed!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('You have completed ${_session.currentCount} dhikr'),
            Text('Time taken: ${_session.formattedDuration}'),
            const SizedBox(height: 16),
            const Text('May Allah accept your dhikr and grant you His blessings.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Finish'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startNewSession();
            },
            child: const Text('Start Again'),
          ),
        ],
      ),
    );
  }

  void _startNewSession() {
    ref.read(dhikrSessionsProvider.notifier).startSession(
      widget.dhikr.id,
      widget.dhikr.recommendedCount,
    ).then((session) {
      setState(() {
        _session = session;
      });
    });
  }

  void _showOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Edit Target Count'),
            onTap: () {
              Navigator.of(context).pop();
              _editTargetCount();
            },
          ),
          ListTile(
            leading: const Icon(Icons.volume_up),
            title: const Text('Audio Settings'),
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Implement audio settings
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share Progress'),
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Implement sharing
            },
          ),
        ],
      ),
    );
  }

  void _editTargetCount() {
    final controller = TextEditingController(text: _session.targetCount.toString());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Target Count'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Target Count',
            hintText: 'Enter target count',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final newTarget = int.tryParse(controller.text);
              if (newTarget != null && newTarget > 0) {
                setState(() {
                  _session.targetCount = newTarget;
                });
                ref.read(dhikrSessionsProvider.notifier).updateSession(_session);
              }
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
