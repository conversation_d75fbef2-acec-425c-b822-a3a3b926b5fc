import 'package:flutter/material.dart';
import '../services/money_manager_service.dart';

/// Placeholder budgets screen
class BudgetsScreen extends StatelessWidget {
  final MoneyManagerService moneyService;

  const BudgetsScreen({
    super.key,
    required this.moneyService,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Budgets'),
      ),
      body: const Center(
        child: Text('Budget management coming soon!'),
      ),
    );
  }
}
