import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Service for managing keyboard shortcuts in the spreadsheet
class KeyboardShortcutService {
  static final KeyboardShortcutService _instance = KeyboardShortcutService._internal();
  factory KeyboardShortcutService() => _instance;
  KeyboardShortcutService._internal();

  final Map<ShortcutActivator, VoidCallback> _shortcuts = {};
  final Map<String, ShortcutInfo> _shortcutInfo = {};

  /// Initialize default shortcuts
  void initializeShortcuts({
    VoidCallback? onCopy,
    VoidCallback? onPaste,
    VoidCallback? onCut,
    VoidCallback? onUndo,
    VoidCallback? onRedo,
    VoidCallback? onSelectAll,
    VoidCallback? onFind,
    VoidCallback? onReplace,
    VoidCallback? onSave,
    VoidCallback? onNew,
    VoidCallback? onOpen,
    VoidCallback? onPrint,
    VoidCallback? onBold,
    VoidCallback? onItalic,
    VoidCallback? onUnderline,
    VoidCallback? onInsertFunction,
    VoidCallback? onShowFormulas,
    VoidCallback? onRecalculate,
    VoidCallback? onGoToCell,
    VoidCallback? onInsertRow,
    VoidCallback? onInsertColumn,
    VoidCallback? onDeleteRow,
    VoidCallback? onDeleteColumn,
    VoidCallback? onFormatCells,
    VoidCallback? onAutoSum,
    VoidCallback? onFillDown,
    VoidCallback? onFillRight,
    VoidCallback? onClearContents,
    VoidCallback? onClearFormats,
    VoidCallback? onToggleGridlines,
    VoidCallback? onZoomIn,
    VoidCallback? onZoomOut,
    VoidCallback? onFullScreen,
    VoidCallback? onHelp,
  }) {
    _shortcuts.clear();
    _shortcutInfo.clear();

    // File operations
    _addShortcut('Ctrl+N', onNew, 'New workbook', ShortcutCategory.file);
    _addShortcut('Ctrl+O', onOpen, 'Open workbook', ShortcutCategory.file);
    _addShortcut('Ctrl+S', onSave, 'Save workbook', ShortcutCategory.file);
    _addShortcut('Ctrl+P', onPrint, 'Print', ShortcutCategory.file);

    // Edit operations
    _addShortcut('Ctrl+C', onCopy, 'Copy', ShortcutCategory.edit);
    _addShortcut('Ctrl+V', onPaste, 'Paste', ShortcutCategory.edit);
    _addShortcut('Ctrl+X', onCut, 'Cut', ShortcutCategory.edit);
    _addShortcut('Ctrl+Z', onUndo, 'Undo', ShortcutCategory.edit);
    _addShortcut('Ctrl+Y', onRedo, 'Redo', ShortcutCategory.edit);
    _addShortcut('Ctrl+A', onSelectAll, 'Select all', ShortcutCategory.edit);
    _addShortcut('Delete', onClearContents, 'Clear contents', ShortcutCategory.edit);
    _addShortcut('Ctrl+Delete', onClearFormats, 'Clear formats', ShortcutCategory.edit);

    // Navigation
    _addShortcut('Ctrl+F', onFind, 'Find', ShortcutCategory.navigation);
    _addShortcut('Ctrl+H', onReplace, 'Find and replace', ShortcutCategory.navigation);
    _addShortcut('Ctrl+G', onGoToCell, 'Go to cell', ShortcutCategory.navigation);
    _addShortcut('F11', onFullScreen, 'Toggle full screen', ShortcutCategory.navigation);

    // Formatting
    _addShortcut('Ctrl+B', onBold, 'Bold', ShortcutCategory.formatting);
    _addShortcut('Ctrl+I', onItalic, 'Italic', ShortcutCategory.formatting);
    _addShortcut('Ctrl+U', onUnderline, 'Underline', ShortcutCategory.formatting);
    _addShortcut('Ctrl+1', onFormatCells, 'Format cells', ShortcutCategory.formatting);

    // Formula operations
    _addShortcut('Shift+F3', onInsertFunction, 'Insert function', ShortcutCategory.formula);
    _addShortcut('Ctrl+`', onShowFormulas, 'Show formulas', ShortcutCategory.formula);
    _addShortcut('F9', onRecalculate, 'Recalculate', ShortcutCategory.formula);
    _addShortcut('Alt+=', onAutoSum, 'AutoSum', ShortcutCategory.formula);

    // Data operations
    _addShortcut('Ctrl+D', onFillDown, 'Fill down', ShortcutCategory.data);
    _addShortcut('Ctrl+R', onFillRight, 'Fill right', ShortcutCategory.data);
    _addShortcut('Ctrl+Shift+=', onInsertRow, 'Insert row', ShortcutCategory.data);
    _addShortcut('Ctrl+Shift+Plus', onInsertColumn, 'Insert column', ShortcutCategory.data);
    _addShortcut('Ctrl+-', onDeleteRow, 'Delete row', ShortcutCategory.data);
    _addShortcut('Ctrl+Shift+-', onDeleteColumn, 'Delete column', ShortcutCategory.data);

    // View operations
    _addShortcut('Ctrl+Plus', onZoomIn, 'Zoom in', ShortcutCategory.view);
    _addShortcut('Ctrl+-', onZoomOut, 'Zoom out', ShortcutCategory.view);
    _addShortcut('Ctrl+Shift+G', onToggleGridlines, 'Toggle gridlines', ShortcutCategory.view);

    // Help
    _addShortcut('F1', onHelp, 'Help', ShortcutCategory.help);
  }

  void _addShortcut(String shortcutString, VoidCallback? callback, String description, ShortcutCategory category) {
    if (callback == null) return;

    final activator = _parseShortcut(shortcutString);
    if (activator != null) {
      _shortcuts[activator] = callback;
      _shortcutInfo[shortcutString] = ShortcutInfo(
        shortcut: shortcutString,
        description: description,
        category: category,
      );
    }
  }

  ShortcutActivator? _parseShortcut(String shortcutString) {
    final parts = shortcutString.split('+');
    if (parts.isEmpty) return null;

    final modifiers = <LogicalKeyboardKey>[];
    LogicalKeyboardKey? key;

    for (final part in parts) {
      switch (part.toLowerCase()) {
        case 'ctrl':
          modifiers.add(LogicalKeyboardKey.control);
          break;
        case 'shift':
          modifiers.add(LogicalKeyboardKey.shift);
          break;
        case 'alt':
          modifiers.add(LogicalKeyboardKey.alt);
          break;
        case 'meta':
        case 'cmd':
          modifiers.add(LogicalKeyboardKey.meta);
          break;
        default:
          key = _parseKey(part);
          break;
      }
    }

    if (key == null) return null;

    if (modifiers.isEmpty) {
      return SingleActivator(key);
    } else {
      return SingleActivator(
        key,
        control: modifiers.contains(LogicalKeyboardKey.control),
        shift: modifiers.contains(LogicalKeyboardKey.shift),
        alt: modifiers.contains(LogicalKeyboardKey.alt),
        meta: modifiers.contains(LogicalKeyboardKey.meta),
      );
    }
  }

  LogicalKeyboardKey? _parseKey(String keyString) {
    switch (keyString.toLowerCase()) {
      case 'a': return LogicalKeyboardKey.keyA;
      case 'b': return LogicalKeyboardKey.keyB;
      case 'c': return LogicalKeyboardKey.keyC;
      case 'd': return LogicalKeyboardKey.keyD;
      case 'e': return LogicalKeyboardKey.keyE;
      case 'f': return LogicalKeyboardKey.keyF;
      case 'g': return LogicalKeyboardKey.keyG;
      case 'h': return LogicalKeyboardKey.keyH;
      case 'i': return LogicalKeyboardKey.keyI;
      case 'j': return LogicalKeyboardKey.keyJ;
      case 'k': return LogicalKeyboardKey.keyK;
      case 'l': return LogicalKeyboardKey.keyL;
      case 'm': return LogicalKeyboardKey.keyM;
      case 'n': return LogicalKeyboardKey.keyN;
      case 'o': return LogicalKeyboardKey.keyO;
      case 'p': return LogicalKeyboardKey.keyP;
      case 'q': return LogicalKeyboardKey.keyQ;
      case 'r': return LogicalKeyboardKey.keyR;
      case 's': return LogicalKeyboardKey.keyS;
      case 't': return LogicalKeyboardKey.keyT;
      case 'u': return LogicalKeyboardKey.keyU;
      case 'v': return LogicalKeyboardKey.keyV;
      case 'w': return LogicalKeyboardKey.keyW;
      case 'x': return LogicalKeyboardKey.keyX;
      case 'y': return LogicalKeyboardKey.keyY;
      case 'z': return LogicalKeyboardKey.keyZ;
      case '1': return LogicalKeyboardKey.digit1;
      case '2': return LogicalKeyboardKey.digit2;
      case '3': return LogicalKeyboardKey.digit3;
      case '4': return LogicalKeyboardKey.digit4;
      case '5': return LogicalKeyboardKey.digit5;
      case '6': return LogicalKeyboardKey.digit6;
      case '7': return LogicalKeyboardKey.digit7;
      case '8': return LogicalKeyboardKey.digit8;
      case '9': return LogicalKeyboardKey.digit9;
      case '0': return LogicalKeyboardKey.digit0;
      case 'f1': return LogicalKeyboardKey.f1;
      case 'f2': return LogicalKeyboardKey.f2;
      case 'f3': return LogicalKeyboardKey.f3;
      case 'f4': return LogicalKeyboardKey.f4;
      case 'f5': return LogicalKeyboardKey.f5;
      case 'f6': return LogicalKeyboardKey.f6;
      case 'f7': return LogicalKeyboardKey.f7;
      case 'f8': return LogicalKeyboardKey.f8;
      case 'f9': return LogicalKeyboardKey.f9;
      case 'f10': return LogicalKeyboardKey.f10;
      case 'f11': return LogicalKeyboardKey.f11;
      case 'f12': return LogicalKeyboardKey.f12;
      case 'enter': return LogicalKeyboardKey.enter;
      case 'space': return LogicalKeyboardKey.space;
      case 'tab': return LogicalKeyboardKey.tab;
      case 'escape': return LogicalKeyboardKey.escape;
      case 'delete': return LogicalKeyboardKey.delete;
      case 'backspace': return LogicalKeyboardKey.backspace;
      case 'insert': return LogicalKeyboardKey.insert;
      case 'home': return LogicalKeyboardKey.home;
      case 'end': return LogicalKeyboardKey.end;
      case 'pageup': return LogicalKeyboardKey.pageUp;
      case 'pagedown': return LogicalKeyboardKey.pageDown;
      case 'arrowup': return LogicalKeyboardKey.arrowUp;
      case 'arrowdown': return LogicalKeyboardKey.arrowDown;
      case 'arrowleft': return LogicalKeyboardKey.arrowLeft;
      case 'arrowright': return LogicalKeyboardKey.arrowRight;
      case '+': return LogicalKeyboardKey.equal;
      case 'plus': return LogicalKeyboardKey.equal;
      case '-': return LogicalKeyboardKey.minus;
      case '=': return LogicalKeyboardKey.equal;
      case '`': return LogicalKeyboardKey.backquote;
      default: return null;
    }
  }

  /// Get shortcuts map for use with Shortcuts widget
  Map<ShortcutActivator, Intent> getShortcutsMap() {
    final shortcutsMap = <ShortcutActivator, Intent>{};
    
    for (final entry in _shortcuts.entries) {
      shortcutsMap[entry.key] = CallbackIntent(entry.value);
    }
    
    return shortcutsMap;
  }

  /// Get actions map for use with Actions widget
  Map<Type, Action<Intent>> getActionsMap() {
    return {
      CallbackIntent: CallbackAction<CallbackIntent>(
        onInvoke: (intent) => intent.callback(),
      ),
    };
  }

  /// Get all shortcuts grouped by category
  Map<ShortcutCategory, List<ShortcutInfo>> getShortcutsByCategory() {
    final grouped = <ShortcutCategory, List<ShortcutInfo>>{};
    
    for (final info in _shortcutInfo.values) {
      grouped[info.category] ??= [];
      grouped[info.category]!.add(info);
    }
    
    return grouped;
  }

  /// Get platform-specific shortcut display string
  String getPlatformShortcutString(String shortcut) {
    if (Theme.of(NavigationService.navigatorKey.currentContext!).platform == TargetPlatform.macOS) {
      return shortcut
          .replaceAll('Ctrl+', '⌘')
          .replaceAll('Alt+', '⌥')
          .replaceAll('Shift+', '⇧');
    }
    return shortcut;
  }

  /// Show keyboard shortcuts help dialog
  void showShortcutsHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => KeyboardShortcutsDialog(
        shortcuts: getShortcutsByCategory(),
      ),
    );
  }
}

/// Intent for callback-based shortcuts
class CallbackIntent extends Intent {
  final VoidCallback callback;
  
  const CallbackIntent(this.callback);
}

/// Shortcut information
class ShortcutInfo {
  final String shortcut;
  final String description;
  final ShortcutCategory category;

  const ShortcutInfo({
    required this.shortcut,
    required this.description,
    required this.category,
  });
}

/// Shortcut categories
enum ShortcutCategory {
  file,
  edit,
  navigation,
  formatting,
  formula,
  data,
  view,
  help,
}

/// Extension for category display names
extension ShortcutCategoryExtension on ShortcutCategory {
  String get displayName {
    switch (this) {
      case ShortcutCategory.file:
        return 'File Operations';
      case ShortcutCategory.edit:
        return 'Edit Operations';
      case ShortcutCategory.navigation:
        return 'Navigation';
      case ShortcutCategory.formatting:
        return 'Formatting';
      case ShortcutCategory.formula:
        return 'Formulas';
      case ShortcutCategory.data:
        return 'Data Operations';
      case ShortcutCategory.view:
        return 'View Options';
      case ShortcutCategory.help:
        return 'Help';
    }
  }

  IconData get icon {
    switch (this) {
      case ShortcutCategory.file:
        return Icons.folder;
      case ShortcutCategory.edit:
        return Icons.edit;
      case ShortcutCategory.navigation:
        return Icons.navigation;
      case ShortcutCategory.formatting:
        return Icons.format_paint;
      case ShortcutCategory.formula:
        return Icons.functions;
      case ShortcutCategory.data:
        return Icons.data_object;
      case ShortcutCategory.view:
        return Icons.visibility;
      case ShortcutCategory.help:
        return Icons.help;
    }
  }
}

/// Keyboard shortcuts help dialog
class KeyboardShortcutsDialog extends StatelessWidget {
  final Map<ShortcutCategory, List<ShortcutInfo>> shortcuts;

  const KeyboardShortcutsDialog({
    super.key,
    required this.shortcuts,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.keyboard),
          SizedBox(width: 8),
          Text('Keyboard Shortcuts'),
        ],
      ),
      content: SizedBox(
        width: 600,
        height: 500,
        child: DefaultTabController(
          length: shortcuts.length,
          child: Column(
            children: [
              TabBar(
                isScrollable: true,
                tabs: shortcuts.keys.map((category) {
                  return Tab(
                    icon: Icon(category.icon),
                    text: category.displayName,
                  );
                }).toList(),
              ),
              Expanded(
                child: TabBarView(
                  children: shortcuts.entries.map((entry) {
                    return ListView.builder(
                      itemCount: entry.value.length,
                      itemBuilder: (context, index) {
                        final shortcut = entry.value[index];
                        return ListTile(
                          leading: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surfaceContainerHighest,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: Theme.of(context).colorScheme.outline,
                              ),
                            ),
                            child: Text(
                              KeyboardShortcutService().getPlatformShortcutString(shortcut.shortcut),
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          title: Text(shortcut.description),
                        );
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

/// Navigation service for global context access
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}
