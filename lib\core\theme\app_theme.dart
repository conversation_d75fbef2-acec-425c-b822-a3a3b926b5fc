import 'package:flutter/material.dart' hide ThemeMode;
import '../../features/settings/models/app_settings.dart';

class AppTheme {
  static const _lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF6750A4),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFEADDFF),
    onPrimaryContainer: Color(0xFF21005D),
    secondary: Color(0xFF625B71),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFE8DEF8),
    onSecondaryContainer: Color(0xFF1D192B),
    tertiary: Color(0xFF7D5260),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFFFD8E4),
    onTertiaryContainer: Color(0xFF31111D),
    error: Color(0xFFBA1A1A),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    outline: Color(0xFF79747E),
    // background: Color(0xFFFFFBFE), // deprecated
    // onBackground: Color(0xFF1C1B1F), // deprecated
    surface: Color(0xFFFFFBFE),
    onSurface: Color(0xFF1C1B1F),
    surfaceContainerHighest: Color(0xFFE7E0EC),
    onSurfaceVariant: Color(0xFF49454F),
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFFD0BCFF),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF6750A4),
    outlineVariant: Color(0xFFCAC4D0),
    scrim: Color(0xFF000000),
  );

  static const _darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFD0BCFF),
    onPrimary: Color(0xFF381E72),
    primaryContainer: Color(0xFF4F378B),
    onPrimaryContainer: Color(0xFFEADDFF),
    secondary: Color(0xFFCCC2DC),
    onSecondary: Color(0xFF332D41),
    secondaryContainer: Color(0xFF4A4458),
    onSecondaryContainer: Color(0xFFE8DEF8),
    tertiary: Color(0xFFEFB8C8),
    onTertiary: Color(0xFF492532),
    tertiaryContainer: Color(0xFF633B48),
    onTertiaryContainer: Color(0xFFFFD8E4),
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    outline: Color(0xFF938F99),
    // background: Color(0xFF1C1B1F), // deprecated
    // onBackground: Color(0xFFE6E1E5), // deprecated
    surface: Color(0xFF1C1B1F),
    onSurface: Color(0xFFE6E1E5),
    surfaceContainerHighest: Color(0xFF49454F),
    onSurfaceVariant: Color(0xFFCAC4D0),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF6750A4),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFFD0BCFF),
    outlineVariant: Color(0xFF49454F),
    scrim: Color(0xFF000000),
  );

  static ThemeData lightTheme(AppSettings settings) {
    final colorScheme = _lightColorScheme.copyWith(
      primary: settings.primaryColor,
      secondary: settings.accentColor,
    );

    return _buildTheme(colorScheme, settings);
  }

  static ThemeData darkTheme(AppSettings settings) {
    final colorScheme = _darkColorScheme.copyWith(
      primary: settings.primaryColor,
      secondary: settings.accentColor,
    );

    return _buildTheme(colorScheme, settings);
  }

  static ThemeData highContrastLightTheme(AppSettings settings) {
    final colorScheme = _lightColorScheme.copyWith(
      primary: Colors.black,
      onPrimary: Colors.white,
      secondary: Colors.black,
      onSecondary: Colors.white,
      surface: Colors.white,
      onSurface: Colors.black,
      // background: Colors.white, // deprecated
      // onBackground: Colors.black, // deprecated
      outline: Colors.black,
    );

    return _buildTheme(colorScheme, settings);
  }

  static ThemeData highContrastDarkTheme(AppSettings settings) {
    final colorScheme = _darkColorScheme.copyWith(
      primary: Colors.white,
      onPrimary: Colors.black,
      secondary: Colors.white,
      onSecondary: Colors.black,
      surface: Colors.black,
      onSurface: Colors.white,
      // background: Colors.black, // deprecated
      // onBackground: Colors.white, // deprecated
      outline: Colors.white,
    );

    return _buildTheme(colorScheme, settings);
  }

  static ThemeData _buildTheme(ColorScheme colorScheme, AppSettings settings) {
    final textTheme = _buildTextTheme(settings);
    
    return ThemeData(
      useMaterial3: settings.useMaterial3,
      colorScheme: colorScheme,
      textTheme: textTheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: settings.useMaterial3 ? 0 : 4,
        centerTitle: true,
      ),
      cardTheme: CardThemeData(
        elevation: settings.useMaterial3 ? 1 : 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: Size(88 * settings.buttonSize, 36 * settings.buttonSize),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          minimumSize: Size(88 * settings.buttonSize, 36 * settings.buttonSize),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          minimumSize: Size(88 * settings.buttonSize, 36 * settings.buttonSize),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          minimumSize: Size(64 * settings.buttonSize, 36 * settings.buttonSize),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        indicatorColor: colorScheme.secondaryContainer,
        labelTextStyle: WidgetStateProperty.all(
          textTheme.labelMedium?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
      ),
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: colorScheme.surface,
        selectedIconTheme: IconThemeData(
          color: colorScheme.onSecondaryContainer,
        ),
        unselectedIconTheme: IconThemeData(
          color: colorScheme.onSurfaceVariant,
        ),
        selectedLabelTextStyle: textTheme.labelMedium?.copyWith(
          color: colorScheme.onSurface,
        ),
        unselectedLabelTextStyle: textTheme.labelMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      ),
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceContainerHighest,
        selectedColor: colorScheme.secondaryContainer,
        labelStyle: textTheme.labelLarge,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: colorScheme.surface,
        titleTextStyle: textTheme.headlineSmall?.copyWith(
          color: colorScheme.onSurface,
        ),
        contentTextStyle: textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: colorScheme.surface,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: textTheme.bodyMedium?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        behavior: SnackBarBehavior.floating,
      ),
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: colorScheme.inverseSurface,
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: textTheme.bodySmall?.copyWith(
          color: colorScheme.onInverseSurface,
        ),
      ),
      // Animation settings
      pageTransitionsTheme: PageTransitionsTheme(
        builders: settings.animationsEnabled
            ? const {
                TargetPlatform.android: CupertinoPageTransitionsBuilder(),
                TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
                TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
                TargetPlatform.linux: FadeUpwardsPageTransitionsBuilder(),
              }
            : const {
                TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.iOS: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.macOS: FadeUpwardsPageTransitionsBuilder(),
                TargetPlatform.linux: FadeUpwardsPageTransitionsBuilder(),
              },
      ),
    );
  }

  static TextTheme _buildTextTheme(AppSettings settings) {
    final baseTextTheme = Typography.material2021().black;
    final fontSizeMultiplier = settings.fontSize / 14.0;
    final largeTextMultiplier = settings.largeTextMode ? 1.2 : 1.0;
    final totalMultiplier = fontSizeMultiplier * largeTextMultiplier;

    return baseTextTheme.copyWith(
      displayLarge: baseTextTheme.displayLarge?.copyWith(
        fontSize: (baseTextTheme.displayLarge?.fontSize ?? 57) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      displayMedium: baseTextTheme.displayMedium?.copyWith(
        fontSize: (baseTextTheme.displayMedium?.fontSize ?? 45) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      displaySmall: baseTextTheme.displaySmall?.copyWith(
        fontSize: (baseTextTheme.displaySmall?.fontSize ?? 36) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      headlineLarge: baseTextTheme.headlineLarge?.copyWith(
        fontSize: (baseTextTheme.headlineLarge?.fontSize ?? 32) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      headlineMedium: baseTextTheme.headlineMedium?.copyWith(
        fontSize: (baseTextTheme.headlineMedium?.fontSize ?? 28) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      headlineSmall: baseTextTheme.headlineSmall?.copyWith(
        fontSize: (baseTextTheme.headlineSmall?.fontSize ?? 24) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      titleLarge: baseTextTheme.titleLarge?.copyWith(
        fontSize: (baseTextTheme.titleLarge?.fontSize ?? 22) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      titleMedium: baseTextTheme.titleMedium?.copyWith(
        fontSize: (baseTextTheme.titleMedium?.fontSize ?? 16) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      titleSmall: baseTextTheme.titleSmall?.copyWith(
        fontSize: (baseTextTheme.titleSmall?.fontSize ?? 14) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      bodyLarge: baseTextTheme.bodyLarge?.copyWith(
        fontSize: (baseTextTheme.bodyLarge?.fontSize ?? 16) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      bodyMedium: baseTextTheme.bodyMedium?.copyWith(
        fontSize: (baseTextTheme.bodyMedium?.fontSize ?? 14) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      bodySmall: baseTextTheme.bodySmall?.copyWith(
        fontSize: (baseTextTheme.bodySmall?.fontSize ?? 12) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      labelLarge: baseTextTheme.labelLarge?.copyWith(
        fontSize: (baseTextTheme.labelLarge?.fontSize ?? 14) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      labelMedium: baseTextTheme.labelMedium?.copyWith(
        fontSize: (baseTextTheme.labelMedium?.fontSize ?? 12) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
      labelSmall: baseTextTheme.labelSmall?.copyWith(
        fontSize: (baseTextTheme.labelSmall?.fontSize ?? 11) * totalMultiplier,
        fontFamily: settings.useSystemFont ? null : settings.customFontFamily,
      ),
    );
  }

  // Predefined color schemes
  static const List<Color> predefinedColors = [
    Color(0xFF6750A4), // Purple (default)
    Color(0xFF1976D2), // Blue
    Color(0xFF388E3C), // Green
    Color(0xFFE64A19), // Orange
    Color(0xFFD32F2F), // Red
    Color(0xFF7B1FA2), // Deep Purple
    Color(0xFF303F9F), // Indigo
    Color(0xFF0097A7), // Cyan
    Color(0xFF689F38), // Light Green
    Color(0xFFFBC02D), // Yellow
    Color(0xFFFF8F00), // Amber
    Color(0xFF5D4037), // Brown
    Color(0xFF455A64), // Blue Grey
  ];

  // Helper methods
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  static Color getContrastingTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  static Color adjustColorForAccessibility(Color color, bool highContrast) {
    if (!highContrast) return color;

    final luminance = color.computeLuminance();
    if (luminance > 0.5) {
      // Light color, make it darker for better contrast
      return Color.lerp(color, Colors.black, 0.3) ?? color;
    } else {
      // Dark color, make it lighter for better contrast
      return Color.lerp(color, Colors.white, 0.3) ?? color;
    }
  }

  // Get theme based on settings
  static ThemeData getTheme(AppSettings settings, Brightness platformBrightness) {
    final brightness = _getBrightness(settings, platformBrightness);
    final isHighContrast = settings.highContrastMode;

    if (brightness == Brightness.light) {
      return isHighContrast
          ? highContrastLightTheme(settings)
          : lightTheme(settings);
    } else {
      return isHighContrast
          ? highContrastDarkTheme(settings)
          : darkTheme(settings);
    }
  }

  static Brightness _getBrightness(AppSettings settings, Brightness platformBrightness) {
    switch (settings.themeMode) {
      case ThemeMode.light:
        return Brightness.light;
      case ThemeMode.dark:
        return Brightness.dark;
      case ThemeMode.system:
        return platformBrightness;
    }
  }
}
