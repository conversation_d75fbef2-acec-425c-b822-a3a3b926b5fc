import 'package:flutter/material.dart';
import '../models/app_state.dart';

// Generic state builder widget
class StateBuilder<T> extends StatelessWidget {
  final AppState<T> state;
  final Widget Function(BuildContext context, T data) successBuilder;
  final Widget Function(BuildContext context, LoadingType? type, String? message)? loadingBuilder;
  final Widget Function(BuildContext context, String? message)? emptyBuilder;
  final Widget Function(BuildContext context, dynamic error, ErrorType? type, String? message, VoidCallback? onRetry)? errorBuilder;
  final Widget Function(BuildContext context, String? message, VoidCallback? onRetry)? offlineBuilder;
  final Widget Function(BuildContext context, String? message)? maintenanceBuilder;

  const StateBuilder({
    super.key,
    required this.state,
    required this.successBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.errorBuilder,
    this.offlineBuilder,
    this.maintenanceBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return state.when<Widget>(
      loading: (data, type, message) {
        if (loadingBuilder != null) {
          return loadingBuilder!(context, type, message);
        }
        return LoadingWidget(
          type: type ?? LoadingType.initial,
          message: message,
          hasData: data != null,
        );
      },
      success: (data, message) => successBuilder(context, data),
      empty: (message, metadata) {
        if (emptyBuilder != null) {
          return emptyBuilder!(context, message);
        }
        return EmptyWidget(
          message: message,
          metadata: metadata,
        );
      },
      error: (error, type, message, canRetry, onRetry) {
        if (errorBuilder != null) {
          return errorBuilder!(context, error, type, message, onRetry);
        }
        return ErrorWidget.withDetails(
          error: error,
          type: type ?? ErrorType.unknown,
          message: message,
          canRetry: canRetry,
          onRetry: onRetry,
        );
      },
      offline: (message, onRetry) {
        if (offlineBuilder != null) {
          return offlineBuilder!(context, message, onRetry);
        }
        return OfflineWidget(
          message: message,
          onRetry: onRetry,
        );
      },
      maintenance: (message, metadata) {
        if (maintenanceBuilder != null) {
          return maintenanceBuilder!(context, message);
        }
        return MaintenanceWidget(
          message: message,
          metadata: metadata,
        );
      },
    );
  }
}

// Loading widget
class LoadingWidget extends StatelessWidget {
  final LoadingType type;
  final String? message;
  final bool hasData;
  final Widget? child;

  const LoadingWidget({
    super.key,
    required this.type,
    this.message,
    this.hasData = false,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case LoadingType.initial:
        return _buildInitialLoading(context);
      case LoadingType.refresh:
        return _buildRefreshLoading(context);
      case LoadingType.loadMore:
        return _buildLoadMoreLoading(context);
      case LoadingType.action:
        return _buildActionLoading(context);
      case LoadingType.background:
        return child ?? _buildBackgroundLoading(context);
    }
  }

  Widget _buildInitialLoading(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRefreshLoading(BuildContext context) {
    return const Center(
      child: RefreshProgressIndicator(),
    );
  }

  Widget _buildLoadMoreLoading(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildActionLoading(BuildContext context) {
    return Container(
      color: Colors.black26,
      child: Center(
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                if (message != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    message!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackgroundLoading(BuildContext context) {
    return Stack(
      children: [
        if (child != null) child!,
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: LinearProgressIndicator(
            backgroundColor: Colors.transparent,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }
}

// Empty widget
class EmptyWidget extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final Widget? action;
  final Map<String, dynamic>? metadata;

  const EmptyWidget({
    super.key,
    this.message,
    this.icon,
    this.action,
    this.metadata,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.inbox_outlined,
              size: 64,
              color: colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              message ?? 'No data available',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'There\'s nothing to show here yet.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}

// Error widget
class ErrorWidget extends StatelessWidget {
  final dynamic error;
  final ErrorType type;
  final String? message;
  final bool canRetry;
  final VoidCallback? onRetry;

  const ErrorWidget.withDetails({
    super.key,
    required this.error,
    required this.type,
    this.message,
    this.canRetry = false,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getErrorIcon(),
              size: 64,
              color: colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _getErrorTitle(),
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message ?? _getErrorMessage(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (canRetry && onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getErrorIcon() {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.server:
        return Icons.cloud_off;
      case ErrorType.validation:
        return Icons.warning;
      case ErrorType.permission:
        return Icons.lock;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.timeout:
        return Icons.timer_off;
      case ErrorType.unknown:
      default:
        return Icons.error_outline;
    }
  }

  String _getErrorTitle() {
    switch (type) {
      case ErrorType.network:
        return 'Connection Error';
      case ErrorType.server:
        return 'Server Error';
      case ErrorType.validation:
        return 'Validation Error';
      case ErrorType.permission:
        return 'Permission Denied';
      case ErrorType.notFound:
        return 'Not Found';
      case ErrorType.timeout:
        return 'Request Timeout';
      case ErrorType.unknown:
      default:
        return 'Something went wrong';
    }
  }

  String _getErrorMessage() {
    switch (type) {
      case ErrorType.network:
        return 'Please check your internet connection and try again.';
      case ErrorType.server:
        return 'The server is currently unavailable. Please try again later.';
      case ErrorType.validation:
        return 'Please check your input and try again.';
      case ErrorType.permission:
        return 'You don\'t have permission to access this resource.';
      case ErrorType.notFound:
        return 'The requested resource could not be found.';
      case ErrorType.timeout:
        return 'The request took too long to complete. Please try again.';
      case ErrorType.unknown:
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

// Offline widget
class OfflineWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const OfflineWidget({
    super.key,
    this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_off,
              size: 64,
              color: colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'You\'re offline',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message ?? 'Please check your internet connection.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Maintenance widget
class MaintenanceWidget extends StatelessWidget {
  final String? message;
  final Map<String, dynamic>? metadata;

  const MaintenanceWidget({
    super.key,
    this.message,
    this.metadata,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.build,
              size: 64,
              color: colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Under Maintenance',
              style: theme.textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message ?? 'We\'re currently performing maintenance. Please check back later.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (metadata?['estimatedDuration'] != null) ...[
              const SizedBox(height: 16),
              Text(
                'Estimated duration: ${metadata!['estimatedDuration']}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// List state builder
class ListStateBuilder<T> extends StatelessWidget {
  final ListState<T> state;
  final Widget Function(BuildContext context, List<T> items) successBuilder;
  final Widget Function(BuildContext context, String? message)? emptyBuilder;
  final Widget Function(BuildContext context, LoadingType? type, String? message)? loadingBuilder;
  final Widget Function(BuildContext context, dynamic error, ErrorType? type, String? message, VoidCallback? onRetry)? errorBuilder;

  const ListStateBuilder({
    super.key,
    required this.state,
    required this.successBuilder,
    this.emptyBuilder,
    this.loadingBuilder,
    this.errorBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return StateBuilder<List<T>>(
      state: state,
      successBuilder: (context, data) {
        if (data.isEmpty) {
          if (emptyBuilder != null) {
            return emptyBuilder!(context, 'No items found');
          }
          return const EmptyWidget(
            message: 'No items found',
            icon: Icons.list_alt,
          );
        }
        return successBuilder(context, data);
      },
      loadingBuilder: loadingBuilder,
      errorBuilder: errorBuilder,
    );
  }
}

// Paginated list builder
class PaginatedListBuilder<T> extends StatelessWidget {
  final PaginatedState<T> state;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final VoidCallback? onLoadMore;
  final Widget? separator;
  final EdgeInsets? padding;

  const PaginatedListBuilder({
    super.key,
    required this.state,
    required this.itemBuilder,
    this.onLoadMore,
    this.separator,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ListStateBuilder<T>(
      state: state,
      successBuilder: (context, items) {
        return ListView.separated(
          padding: padding,
          itemCount: items.length + (state.hasMore ? 1 : 0),
          separatorBuilder: (context, index) => separator ?? const SizedBox(height: 8),
          itemBuilder: (context, index) {
            if (index >= items.length) {
              // Load more indicator
              if (state.isLoadingMore) {
                return const LoadingWidget(type: LoadingType.loadMore);
              } else if (state.hasMore && onLoadMore != null) {
                // Trigger load more
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  onLoadMore!();
                });
                return const LoadingWidget(type: LoadingType.loadMore);
              }
              return const SizedBox.shrink();
            }
            
            return itemBuilder(context, items[index], index);
          },
        );
      },
    );
  }
}
