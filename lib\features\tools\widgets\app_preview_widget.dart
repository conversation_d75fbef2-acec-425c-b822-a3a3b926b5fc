import 'package:flutter/material.dart';
import '../models/mobile_theme.dart';
import '../screens/enhanced_tool_builder_screen.dart';

/// App preview widget for the Tool Builder
class AppPreviewWidget extends StatefulWidget {
  final String appName;
  final String appDescription;
  final MobileTheme? theme;
  final BuildTarget buildTarget;
  final Map<String, dynamic> excelData;

  const AppPreviewWidget({
    super.key,
    required this.appName,
    required this.appDescription,
    this.theme,
    required this.buildTarget,
    required this.excelData,
  });

  @override
  State<AppPreviewWidget> createState() => _AppPreviewWidgetState();
}

class _AppPreviewWidgetState extends State<AppPreviewWidget> {
  bool _isDarkMode = false;
  DeviceType _selectedDevice = DeviceType.phone;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Preview controls
          Row(
            children: [
              Text(
                'App Preview',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              SegmentedButton<DeviceType>(
                segments: const [
                  ButtonSegment(
                    value: DeviceType.phone,
                    label: Text('Phone'),
                    icon: Icon(Icons.phone_android),
                  ),
                  ButtonSegment(
                    value: DeviceType.tablet,
                    label: Text('Tablet'),
                    icon: Icon(Icons.tablet),
                  ),
                  ButtonSegment(
                    value: DeviceType.desktop,
                    label: Text('Desktop'),
                    icon: Icon(Icons.desktop_windows),
                  ),
                ],
                selected: {_selectedDevice},
                onSelectionChanged: (Set<DeviceType> selection) {
                  setState(() {
                    _selectedDevice = selection.first;
                  });
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Dark mode toggle
          SwitchListTile(
            title: const Text('Dark Mode'),
            subtitle: const Text('Preview in dark theme'),
            value: _isDarkMode,
            onChanged: (value) {
              setState(() {
                _isDarkMode = value;
              });
            },
          ),

          const SizedBox(height: 24),

          // Device preview
          Center(
            child: _buildDevicePreview(),
          ),

          const SizedBox(height: 24),

          // App specifications
          _buildAppSpecs(),
        ],
      ),
    );
  }

  Widget _buildDevicePreview() {
    final deviceSize = _getDeviceSize();
    final colorScheme = widget.theme != null
        ? (_isDarkMode ? widget.theme!.darkColorScheme : widget.theme!.lightColorScheme)
        : (_isDarkMode 
            ? ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.dark)
            : ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.light));

    return Container(
      width: deviceSize.width,
      height: deviceSize.height,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(_selectedDevice == DeviceType.desktop ? 8 : 24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(_selectedDevice == DeviceType.desktop ? 0 : 8),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(_selectedDevice == DeviceType.desktop ? 8 : 16),
          child: Theme(
            data: ThemeData(
              colorScheme: colorScheme,
              useMaterial3: true,
            ),
            child: _buildAppContent(colorScheme),
          ),
        ),
      ),
    );
  }

  Widget _buildAppContent(ColorScheme colorScheme) {
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          widget.appName,
          style: TextStyle(color: colorScheme.onPrimary),
        ),
        backgroundColor: colorScheme.primary,
        elevation: widget.theme?.layoutConfig.elevation ?? 2,
        centerTitle: widget.theme?.type == MobileThemeType.ios,
      ),
      body: Column(
        children: [
          // Header section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: colorScheme.primaryContainer,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome to ${widget.appName}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.appDescription,
                  style: TextStyle(
                    color: colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),

          // Content section
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(widget.theme?.layoutConfig.screenPadding ?? 16),
              child: Column(
                children: [
                  // Feature cards
                  _buildFeatureCard(
                    colorScheme,
                    'Excel Integration',
                    'Powerful spreadsheet functionality',
                    Icons.table_chart,
                  ),
                  SizedBox(height: widget.theme?.layoutConfig.cardSpacing ?? 12),
                  _buildFeatureCard(
                    colorScheme,
                    'Data Analysis',
                    'Advanced formulas and calculations',
                    Icons.analytics,
                  ),
                  SizedBox(height: widget.theme?.layoutConfig.cardSpacing ?? 12),
                  _buildFeatureCard(
                    colorScheme,
                    'Mobile Optimized',
                    'Native look and feel',
                    Icons.phone_android,
                  ),

                  const Spacer(),

                  // Action button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            widget.theme?.layoutConfig.borderRadius ?? 12,
                          ),
                        ),
                      ),
                      child: const Text('Get Started'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigation(colorScheme),
    );
  }

  Widget _buildFeatureCard(ColorScheme colorScheme, String title, String subtitle, IconData icon) {
    return Card(
      elevation: widget.theme?.layoutConfig.elevation ?? 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          widget.theme?.layoutConfig.borderRadius ?? 12,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(widget.theme?.layoutConfig.screenPadding ?? 16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildBottomNavigation(ColorScheme colorScheme) {
    if (widget.theme?.layoutConfig.navigationStyle != NavigationStyle.bottomNav) {
      return null;
    }

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      backgroundColor: colorScheme.surface,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurfaceVariant,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.table_chart),
          label: 'Data',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'Reports',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: 'Settings',
        ),
      ],
    );
  }

  Widget _buildAppSpecs() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'App Specifications',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            _buildSpecRow('App Name', widget.appName),
            _buildSpecRow('Description', widget.appDescription),
            _buildSpecRow('Build Target', widget.buildTarget.name.toUpperCase()),
            _buildSpecRow('Theme Type', widget.theme?.type.name.toUpperCase() ?? 'DEFAULT'),
            _buildSpecRow('Theme Name', widget.theme?.name ?? 'Default Theme'),
            _buildSpecRow('Navigation', widget.theme?.layoutConfig.navigationStyle.name ?? 'Bottom Navigation'),
            _buildSpecRow('Excel Support', 'Full Formula Engine'),
            _buildSpecRow('Offline Support', 'Yes'),
            _buildSpecRow('Platform', _getPlatformString()),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Size _getDeviceSize() {
    switch (_selectedDevice) {
      case DeviceType.phone:
        return const Size(280, 500);
      case DeviceType.tablet:
        return const Size(400, 600);
      case DeviceType.desktop:
        return const Size(600, 400);
    }
  }

  String _getPlatformString() {
    switch (widget.buildTarget) {
      case BuildTarget.android:
        return 'Android APK';
      case BuildTarget.windows:
        return 'Windows EXE';
      case BuildTarget.both:
        return 'Android APK + Windows EXE';
    }
  }
}

enum DeviceType {
  phone,
  tablet,
  desktop,
}
