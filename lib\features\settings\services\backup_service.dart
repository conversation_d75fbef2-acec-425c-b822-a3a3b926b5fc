import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/models/user_profile.dart';

class BackupService {
  static const String _backupFileName = 'shadowsuite_backup';
  static const String _backupExtension = '.json';

  // Create a complete backup of all user data
  static Future<Map<String, dynamic>> createBackup(UserProfile userProfile) async {
    try {
      final db = DatabaseService.instance;
      
      // Get all data from database
      final backup = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'user_profile': userProfile.toJson(),
        'data': {
          // Memo data
          'notes': await _getNotesData(db, userProfile.id.toString()),
          'todos': await _getTodosData(db, userProfile.id.toString()),
          'voice_memos': await _getVoiceMemosData(db, userProfile.id.toString()),
          
          // Athkar data
          'dhikr_sessions': await _getDhikrSessionsData(db, userProfile.id.toString()),
          'custom_dhikr': await _getCustomDhikrData(db, userProfile.id.toString()),
          'quran_bookmarks': await _getQuranBookmarksData(db, userProfile.id.toString()),
          'prayer_times': await _getPrayerTimesData(db, userProfile.id.toString()),
          
          // Money data
          'accounts': await _getAccountsData(db, userProfile.id.toString()),
          'transactions': await _getTransactionsData(db, userProfile.id.toString()),
          'budgets': await _getBudgetsData(db, userProfile.id.toString()),
          'financial_goals': await _getFinancialGoalsData(db, userProfile.id.toString()),
          'categories': await _getCategoriesData(db, userProfile.id.toString()),
          
          // Tools data
          'calculation_history': await _getCalculationHistoryData(db, userProfile.id.toString()),
          'custom_tools': await _getCustomToolsData(db, userProfile.id.toString()),
          
          // Settings
          'app_settings': await _getAppSettingsData(db, userProfile.id.toString()),
        },
      };
      
      return backup;
    } catch (e) {
      throw Exception('Failed to create backup: $e');
    }
  }

  // Export backup to file
  static Future<String> exportBackup(Map<String, dynamic> backup) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${_backupFileName}_$timestamp$_backupExtension';
      final file = File('${directory.path}/$fileName');
      
      final jsonString = const JsonEncoder.withIndent('  ').convert(backup);
      await file.writeAsString(jsonString);
      
      return file.path;
    } catch (e) {
      throw Exception('Failed to export backup: $e');
    }
  }

  // Share backup file
  static Future<void> shareBackup(String filePath) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'ShadowSuite Backup',
        subject: 'My ShadowSuite Data Backup',
      );
    } catch (e) {
      throw Exception('Failed to share backup: $e');
    }
  }

  // Import backup from file
  static Future<Map<String, dynamic>> importBackup(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Backup file not found');
      }
      
      final jsonString = await file.readAsString();
      final backup = json.decode(jsonString) as Map<String, dynamic>;
      
      // Validate backup structure
      if (!_validateBackupStructure(backup)) {
        throw Exception('Invalid backup file format');
      }
      
      return backup;
    } catch (e) {
      throw Exception('Failed to import backup: $e');
    }
  }

  // Restore data from backup
  static Future<void> restoreBackup(Map<String, dynamic> backup, UserProfile userProfile) async {
    try {
      final db = DatabaseService.instance;
      final data = backup['data'] as Map<String, dynamic>;
      
      // Restore user profile
      if (backup.containsKey('user_profile')) {
        final profileData = backup['user_profile'] as Map<String, dynamic>;
        userProfile.name = profileData['name'] ?? userProfile.name;
        userProfile.languageCode = profileData['languageCode'] ?? userProfile.languageCode;
        userProfile.themeMode = profileData['themeMode'] ?? userProfile.themeMode;
        userProfile.syncEnabled = profileData['syncEnabled'] ?? userProfile.syncEnabled;
        userProfile.notificationsEnabled = profileData['notificationsEnabled'] ?? userProfile.notificationsEnabled;
        userProfile.memoSettings = profileData['memoSettings'] ?? userProfile.memoSettings;
        userProfile.athkarSettings = profileData['athkarSettings'] ?? userProfile.athkarSettings;
        userProfile.toolsSettings = profileData['toolsSettings'] ?? userProfile.toolsSettings;
        userProfile.moneySettings = profileData['moneySettings'] ?? userProfile.moneySettings;
        await db.updateUserProfile(userProfile);
      }
      
      // Restore each data type
      await _restoreNotesData(db, data['notes'], userProfile.id.toString());
      await _restoreTodosData(db, data['todos'], userProfile.id.toString());
      await _restoreVoiceMemosData(db, data['voice_memos'], userProfile.id.toString());
      await _restoreDhikrSessionsData(db, data['dhikr_sessions'], userProfile.id.toString());
      await _restoreCustomDhikrData(db, data['custom_dhikr'], userProfile.id.toString());
      await _restoreQuranBookmarksData(db, data['quran_bookmarks'], userProfile.id.toString());
      await _restorePrayerTimesData(db, data['prayer_times'], userProfile.id.toString());
      await _restoreAccountsData(db, data['accounts'], userProfile.id.toString());
      await _restoreTransactionsData(db, data['transactions'], userProfile.id.toString());
      await _restoreBudgetsData(db, data['budgets'], userProfile.id.toString());
      await _restoreFinancialGoalsData(db, data['financial_goals'], userProfile.id.toString());
      await _restoreCategoriesData(db, data['categories'], userProfile.id.toString());
      await _restoreCalculationHistoryData(db, data['calculation_history'], userProfile.id.toString());
      await _restoreCustomToolsData(db, data['custom_tools'], userProfile.id.toString());
      await _restoreAppSettingsData(db, data['app_settings'], userProfile.id.toString());
      
    } catch (e) {
      throw Exception('Failed to restore backup: $e');
    }
  }

  // Get backup file size
  static Future<int> getBackupSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  // List available backup files
  static Future<List<Map<String, dynamic>>> listBackupFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync()
          .where((file) => file.path.contains(_backupFileName) && file.path.endsWith(_backupExtension))
          .cast<File>()
          .toList();
      
      final backupFiles = <Map<String, dynamic>>[];
      
      for (final file in files) {
        final stat = await file.stat();
        final size = await file.length();
        
        backupFiles.add({
          'path': file.path,
          'name': file.path.split('/').last,
          'size': size,
          'created': stat.modified,
        });
      }
      
      // Sort by creation date (newest first)
      backupFiles.sort((a, b) => (b['created'] as DateTime).compareTo(a['created'] as DateTime));
      
      return backupFiles;
    } catch (e) {
      return [];
    }
  }

  // Delete backup file
  static Future<void> deleteBackupFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      throw Exception('Failed to delete backup file: $e');
    }
  }

  // Validate backup structure
  static bool _validateBackupStructure(Map<String, dynamic> backup) {
    return backup.containsKey('version') &&
           backup.containsKey('timestamp') &&
           backup.containsKey('data');
  }

  // Data extraction methods (simplified - in real app, implement actual database queries)
  static Future<List<Map<String, dynamic>>> _getNotesData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getTodosData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getVoiceMemosData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getDhikrSessionsData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getCustomDhikrData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getQuranBookmarksData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getPrayerTimesData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getAccountsData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getTransactionsData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getBudgetsData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getFinancialGoalsData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getCategoriesData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getCalculationHistoryData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<List<Map<String, dynamic>>> _getCustomToolsData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return [];
  }

  static Future<Map<String, dynamic>> _getAppSettingsData(DatabaseService db, String userId) async {
    // TODO: Implement actual database query
    return {};
  }

  // Data restoration methods (simplified - in real app, implement actual database operations)
  static Future<void> _restoreNotesData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreTodosData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreVoiceMemosData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreDhikrSessionsData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreCustomDhikrData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreQuranBookmarksData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restorePrayerTimesData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreAccountsData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreTransactionsData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreBudgetsData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreFinancialGoalsData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreCategoriesData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreCalculationHistoryData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreCustomToolsData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }

  static Future<void> _restoreAppSettingsData(DatabaseService db, dynamic data, String userId) async {
    // TODO: Implement actual database restoration
  }
}
