import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/api_prayer_time.dart';

class PrayerTimesApiService {
  static const String baseUrl = 'https://api.pawan.krd/prayertimes';
  
  /// Fetch prayer times for a specific location and date
  /// 
  /// Parameters:
  /// - country: Country code (e.g., 'iq', 'us', 'uk')
  /// - city: City name (e.g., 'erbil', 'baghdad', 'london')
  /// - month: Month number (01-12)
  /// - day: Day number (01-31)
  /// 
  /// Example: https://api.pawan.krd/prayertimes?country=iq&city=erbil&month=6&day=22
  static Future<List<ApiPrayerTime>> fetchPrayerTimes({
    required String country,
    required String city,
    required int month,
    required int day,
  }) async {
    try {
      final monthStr = month.toString().padLeft(2, '0');
      final dayStr = day.toString().padLeft(2, '0');
      
      final uri = Uri.parse('$baseUrl?country=$country&city=$city&month=$monthStr&day=$dayStr');
      
      debugPrint('Fetching prayer times from: $uri');
      
      final response = await http.get(
        uri,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parsePrayerTimesResponse(data, country, city, month, day);
      } else {
        throw Exception('Failed to fetch prayer times: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error fetching prayer times: $e');
      throw Exception('Failed to fetch prayer times: $e');
    }
  }
  
  /// Fetch prayer times for current date
  static Future<List<ApiPrayerTime>> fetchTodayPrayerTimes({
    required String country,
    required String city,
  }) async {
    final now = DateTime.now();
    return fetchPrayerTimes(
      country: country,
      city: city,
      month: now.month,
      day: now.day,
    );
  }
  
  /// Fetch prayer times for a specific date
  static Future<List<ApiPrayerTime>> fetchPrayerTimesForDate({
    required String country,
    required String city,
    required DateTime date,
  }) async {
    return fetchPrayerTimes(
      country: country,
      city: city,
      month: date.month,
      day: date.day,
    );
  }
  
  /// Parse the API response and convert to PrayerTime objects
  static List<ApiPrayerTime> _parsePrayerTimesResponse(
    dynamic data,
    String country,
    String city,
    int month,
    int day,
  ) {
    final List<ApiPrayerTime> prayerTimes = [];
    final date = DateTime(DateTime.now().year, month, day);
    
    try {
      // The API response structure may vary, so we handle different possible formats
      if (data is Map<String, dynamic>) {
        // Handle object response format
        final timings = data['timings'] ?? data['data'] ?? data;
        
        if (timings is Map<String, dynamic>) {
          prayerTimes.addAll(_extractPrayerTimesFromMap(timings, date));
        }
      } else if (data is List) {
        // Handle array response format
        for (final item in data) {
          if (item is Map<String, dynamic>) {
            prayerTimes.addAll(_extractPrayerTimesFromMap(item, date));
          }
        }
      }
      
      // If no prayer times found, create sample data for demonstration
      if (prayerTimes.isEmpty) {
        prayerTimes.addAll(_createSamplePrayerTimes(date));
      }
      
      // Sort prayer times by time
      prayerTimes.sort((a, b) => a.time.compareTo(b.time));
      
      return prayerTimes;
    } catch (e) {
      debugPrint('Error parsing prayer times response: $e');
      // Return sample data if parsing fails
      return _createSamplePrayerTimes(date);
    }
  }
  
  /// Extract prayer times from a map structure
  static List<ApiPrayerTime> _extractPrayerTimesFromMap(Map<String, dynamic> timings, DateTime date) {
    final List<ApiPrayerTime> prayerTimes = [];
    
    // Common prayer time keys in different APIs
    final prayerMappings = {
      'Fajr': ['fajr', 'Fajr', 'FAJR', 'dawn'],
      'Dhuhr': ['dhuhr', 'Dhuhr', 'DHUHR', 'zuhr', 'Zuhr', 'noon'],
      'Asr': ['asr', 'Asr', 'ASR', 'afternoon'],
      'Maghrib': ['maghrib', 'Maghrib', 'MAGHRIB', 'sunset'],
      'Isha': ['isha', 'Isha', 'ISHA', 'night'],
    };
    
    for (final entry in prayerMappings.entries) {
      final prayerName = entry.key;
      final possibleKeys = entry.value;
      
      for (final key in possibleKeys) {
        if (timings.containsKey(key)) {
          final timeStr = timings[key].toString();
          final time = _parseTimeString(timeStr, date);
          
          if (time != null) {
            prayerTimes.add(ApiPrayerTime(
              prayerName: prayerName,
              prayerNameArabic: _getArabicPrayerName(prayerName),
              time: time,
              location: 'Current Location',
            ));
            break; // Found this prayer time, move to next
          }
        }
      }
    }
    
    return prayerTimes;
  }
  
  /// Parse time string to DateTime
  static DateTime? _parseTimeString(String timeStr, DateTime date) {
    try {
      // Remove any extra characters and normalize
      timeStr = timeStr.trim().replaceAll(RegExp(r'[^\d:]'), '');
      
      if (timeStr.contains(':')) {
        final parts = timeStr.split(':');
        if (parts.length >= 2) {
          final hour = int.parse(parts[0]);
          final minute = int.parse(parts[1]);
          
          return DateTime(date.year, date.month, date.day, hour, minute);
        }
      }
    } catch (e) {
      debugPrint('Error parsing time string "$timeStr": $e');
    }
    
    return null;
  }
  
  /// Get Arabic prayer name
  static String _getArabicPrayerName(String englishName) {
    switch (englishName) {
      case 'Fajr':
        return 'الفجر';
      case 'Dhuhr':
        return 'الظهر';
      case 'Asr':
        return 'العصر';
      case 'Maghrib':
        return 'المغرب';
      case 'Isha':
        return 'العشاء';
      default:
        return englishName;
    }
  }
  
  /// Create sample prayer times for demonstration
  static List<ApiPrayerTime> _createSamplePrayerTimes(DateTime date) {
    return [
      ApiPrayerTime(
        prayerName: 'Fajr',
        prayerNameArabic: 'الفجر',
        time: DateTime(date.year, date.month, date.day, 5, 30),
        location: 'Sample Location',
      ),
      ApiPrayerTime(
        prayerName: 'Dhuhr',
        prayerNameArabic: 'الظهر',
        time: DateTime(date.year, date.month, date.day, 12, 15),
        location: 'Sample Location',
      ),
      ApiPrayerTime(
        prayerName: 'Asr',
        prayerNameArabic: 'العصر',
        time: DateTime(date.year, date.month, date.day, 15, 45),
        location: 'Sample Location',
      ),
      ApiPrayerTime(
        prayerName: 'Maghrib',
        prayerNameArabic: 'المغرب',
        time: DateTime(date.year, date.month, date.day, 18, 30),
        location: 'Sample Location',
      ),
      ApiPrayerTime(
        prayerName: 'Isha',
        prayerNameArabic: 'العشاء',
        time: DateTime(date.year, date.month, date.day, 20, 0),
        location: 'Sample Location',
      ),
    ];
  }
  
  /// Get list of supported countries and cities
  static Map<String, List<String>> getSupportedLocations() {
    return {
      'Iraq': ['baghdad', 'erbil', 'basra', 'mosul', 'najaf', 'karbala'],
      'Saudi Arabia': ['riyadh', 'mecca', 'medina', 'jeddah', 'dammam'],
      'UAE': ['dubai', 'abu_dhabi', 'sharjah', 'ajman'],
      'Egypt': ['cairo', 'alexandria', 'giza', 'luxor'],
      'Turkey': ['istanbul', 'ankara', 'izmir', 'bursa'],
      'Pakistan': ['karachi', 'lahore', 'islamabad', 'faisalabad'],
      'India': ['delhi', 'mumbai', 'bangalore', 'hyderabad'],
      'Indonesia': ['jakarta', 'surabaya', 'bandung', 'medan'],
      'Malaysia': ['kuala_lumpur', 'penang', 'johor_bahru'],
      'UK': ['london', 'birmingham', 'manchester', 'leeds'],
      'USA': ['new_york', 'los_angeles', 'chicago', 'houston'],
      'Canada': ['toronto', 'vancouver', 'montreal', 'calgary'],
    };
  }
  
  /// Get country code from country name
  static String getCountryCode(String countryName) {
    final countryCodeMap = {
      'Iraq': 'iq',
      'Saudi Arabia': 'sa',
      'UAE': 'ae',
      'Egypt': 'eg',
      'Turkey': 'tr',
      'Pakistan': 'pk',
      'India': 'in',
      'Indonesia': 'id',
      'Malaysia': 'my',
      'UK': 'gb',
      'USA': 'us',
      'Canada': 'ca',
    };
    
    return countryCodeMap[countryName] ?? 'us';
  }
}
