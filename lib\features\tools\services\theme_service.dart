import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Dynamic theme service for Tool Builder
class ThemeService extends StateNotifier<ToolBuilderTheme> {
  ThemeService() : super(ToolBuilderTheme.defaultTheme()) {
    _loadTheme();
  }

  static final provider = StateNotifierProvider<ThemeService, ToolBuilderTheme>(
    (ref) => ThemeService(),
  );

  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeData = prefs.getString('tool_builder_theme');
      
      if (themeData != null) {
        // TODO: Implement theme deserialization
        // For now, use default theme
      }
    } catch (e) {
      // Use default theme on error
    }
  }

  Future<void> updateTheme(ToolBuilderTheme newTheme) async {
    state = newTheme;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      // TODO: Implement theme serialization
      await prefs.setString('tool_builder_theme', newTheme.name);
    } catch (e) {
      // Handle error silently
    }
  }

  void updatePrimaryColor(Color color) {
    state = state.copyWith(primaryColor: color);
    _saveTheme();
  }

  void updateSecondaryColor(Color color) {
    state = state.copyWith(secondaryColor: color);
    _saveTheme();
  }

  void updateBackgroundColor(Color color) {
    state = state.copyWith(backgroundColor: color);
    _saveTheme();
  }

  void updateSurfaceColor(Color color) {
    state = state.copyWith(surfaceColor: color);
    _saveTheme();
  }

  void toggleDarkMode() {
    state = state.copyWith(isDarkMode: !state.isDarkMode);
    _saveTheme();
  }

  void updateBorderRadius(double radius) {
    state = state.copyWith(borderRadius: radius);
    _saveTheme();
  }

  void updateElevation(double elevation) {
    state = state.copyWith(elevation: elevation);
    _saveTheme();
  }

  void updateAnimationDuration(Duration duration) {
    state = state.copyWith(animationDuration: duration);
    _saveTheme();
  }

  void applyPresetTheme(String presetName) {
    switch (presetName) {
      case 'material':
        state = ToolBuilderTheme.materialTheme();
        break;
      case 'cupertino':
        state = ToolBuilderTheme.cupertinoTheme();
        break;
      case 'modern':
        state = ToolBuilderTheme.modernTheme();
        break;
      case 'dark':
        state = ToolBuilderTheme.darkTheme();
        break;
      case 'colorful':
        state = ToolBuilderTheme.colorfulTheme();
        break;
      default:
        state = ToolBuilderTheme.defaultTheme();
    }
    _saveTheme();
  }

  Future<void> _saveTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('tool_builder_theme', state.name);
    } catch (e) {
      // Handle error silently
    }
  }
}

/// Tool Builder theme configuration
class ToolBuilderTheme {
  final String name;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color surfaceColor;
  final Color onPrimaryColor;
  final Color onSecondaryColor;
  final Color onBackgroundColor;
  final Color onSurfaceColor;
  final Color errorColor;
  final Color successColor;
  final Color warningColor;
  final Color infoColor;
  final bool isDarkMode;
  final double borderRadius;
  final double elevation;
  final Duration animationDuration;
  final Map<String, dynamic> customProperties;

  const ToolBuilderTheme({
    required this.name,
    required this.primaryColor,
    required this.secondaryColor,
    required this.backgroundColor,
    required this.surfaceColor,
    required this.onPrimaryColor,
    required this.onSecondaryColor,
    required this.onBackgroundColor,
    required this.onSurfaceColor,
    required this.errorColor,
    required this.successColor,
    required this.warningColor,
    required this.infoColor,
    this.isDarkMode = false,
    this.borderRadius = 8.0,
    this.elevation = 2.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.customProperties = const {},
  });

  /// Default Material Design 3 theme
  factory ToolBuilderTheme.defaultTheme() {
    return const ToolBuilderTheme(
      name: 'default',
      primaryColor: Color(0xFF6750A4),
      secondaryColor: Color(0xFF625B71),
      backgroundColor: Color(0xFFFFFBFE),
      surfaceColor: Color(0xFFFFFBFE),
      onPrimaryColor: Color(0xFFFFFFFF),
      onSecondaryColor: Color(0xFFFFFFFF),
      onBackgroundColor: Color(0xFF1C1B1F),
      onSurfaceColor: Color(0xFF1C1B1F),
      errorColor: Color(0xFFBA1A1A),
      successColor: Color(0xFF006D3B),
      warningColor: Color(0xFFFF8F00),
      infoColor: Color(0xFF0061A4),
    );
  }

  /// Material Design theme
  factory ToolBuilderTheme.materialTheme() {
    return const ToolBuilderTheme(
      name: 'material',
      primaryColor: Color(0xFF2196F3),
      secondaryColor: Color(0xFF03DAC6),
      backgroundColor: Color(0xFFFFFFFF),
      surfaceColor: Color(0xFFFFFFFF),
      onPrimaryColor: Color(0xFFFFFFFF),
      onSecondaryColor: Color(0xFF000000),
      onBackgroundColor: Color(0xFF000000),
      onSurfaceColor: Color(0xFF000000),
      errorColor: Color(0xFFF44336),
      successColor: Color(0xFF4CAF50),
      warningColor: Color(0xFFFF9800),
      infoColor: Color(0xFF2196F3),
    );
  }

  /// Cupertino-style theme
  factory ToolBuilderTheme.cupertinoTheme() {
    return const ToolBuilderTheme(
      name: 'cupertino',
      primaryColor: Color(0xFF007AFF),
      secondaryColor: Color(0xFF5856D6),
      backgroundColor: Color(0xFFF2F2F7),
      surfaceColor: Color(0xFFFFFFFF),
      onPrimaryColor: Color(0xFFFFFFFF),
      onSecondaryColor: Color(0xFFFFFFFF),
      onBackgroundColor: Color(0xFF000000),
      onSurfaceColor: Color(0xFF000000),
      errorColor: Color(0xFFFF3B30),
      successColor: Color(0xFF34C759),
      warningColor: Color(0xFFFF9500),
      infoColor: Color(0xFF007AFF),
      borderRadius: 12.0,
    );
  }

  /// Modern gradient theme
  factory ToolBuilderTheme.modernTheme() {
    return const ToolBuilderTheme(
      name: 'modern',
      primaryColor: Color(0xFF667EEA),
      secondaryColor: Color(0xFF764BA2),
      backgroundColor: Color(0xFFF8FAFC),
      surfaceColor: Color(0xFFFFFFFF),
      onPrimaryColor: Color(0xFFFFFFFF),
      onSecondaryColor: Color(0xFFFFFFFF),
      onBackgroundColor: Color(0xFF1E293B),
      onSurfaceColor: Color(0xFF1E293B),
      errorColor: Color(0xFFEF4444),
      successColor: Color(0xFF10B981),
      warningColor: Color(0xFFF59E0B),
      infoColor: Color(0xFF3B82F6),
      borderRadius: 16.0,
      elevation: 4.0,
    );
  }

  /// Dark theme
  factory ToolBuilderTheme.darkTheme() {
    return const ToolBuilderTheme(
      name: 'dark',
      primaryColor: Color(0xFFBB86FC),
      secondaryColor: Color(0xFF03DAC6),
      backgroundColor: Color(0xFF121212),
      surfaceColor: Color(0xFF1E1E1E),
      onPrimaryColor: Color(0xFF000000),
      onSecondaryColor: Color(0xFF000000),
      onBackgroundColor: Color(0xFFFFFFFF),
      onSurfaceColor: Color(0xFFFFFFFF),
      errorColor: Color(0xFFCF6679),
      successColor: Color(0xFF4CAF50),
      warningColor: Color(0xFFFFB74D),
      infoColor: Color(0xFF64B5F6),
      isDarkMode: true,
    );
  }

  /// Colorful vibrant theme
  factory ToolBuilderTheme.colorfulTheme() {
    return const ToolBuilderTheme(
      name: 'colorful',
      primaryColor: Color(0xFFE91E63),
      secondaryColor: Color(0xFF9C27B0),
      backgroundColor: Color(0xFFFFF3E0),
      surfaceColor: Color(0xFFFFFFFF),
      onPrimaryColor: Color(0xFFFFFFFF),
      onSecondaryColor: Color(0xFFFFFFFF),
      onBackgroundColor: Color(0xFF3E2723),
      onSurfaceColor: Color(0xFF3E2723),
      errorColor: Color(0xFFD32F2F),
      successColor: Color(0xFF388E3C),
      warningColor: Color(0xFFF57C00),
      infoColor: Color(0xFF1976D2),
      borderRadius: 20.0,
      elevation: 6.0,
    );
  }

  /// Create a copy with updated values
  ToolBuilderTheme copyWith({
    String? name,
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? surfaceColor,
    Color? onPrimaryColor,
    Color? onSecondaryColor,
    Color? onBackgroundColor,
    Color? onSurfaceColor,
    Color? errorColor,
    Color? successColor,
    Color? warningColor,
    Color? infoColor,
    bool? isDarkMode,
    double? borderRadius,
    double? elevation,
    Duration? animationDuration,
    Map<String, dynamic>? customProperties,
  }) {
    return ToolBuilderTheme(
      name: name ?? this.name,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      onPrimaryColor: onPrimaryColor ?? this.onPrimaryColor,
      onSecondaryColor: onSecondaryColor ?? this.onSecondaryColor,
      onBackgroundColor: onBackgroundColor ?? this.onBackgroundColor,
      onSurfaceColor: onSurfaceColor ?? this.onSurfaceColor,
      errorColor: errorColor ?? this.errorColor,
      successColor: successColor ?? this.successColor,
      warningColor: warningColor ?? this.warningColor,
      infoColor: infoColor ?? this.infoColor,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      borderRadius: borderRadius ?? this.borderRadius,
      elevation: elevation ?? this.elevation,
      animationDuration: animationDuration ?? this.animationDuration,
      customProperties: customProperties ?? this.customProperties,
    );
  }

  /// Convert to Flutter ThemeData
  ThemeData toThemeData() {
    final colorScheme = ColorScheme(
      brightness: isDarkMode ? Brightness.dark : Brightness.light,
      primary: primaryColor,
      onPrimary: onPrimaryColor,
      secondary: secondaryColor,
      onSecondary: onSecondaryColor,
      error: errorColor,
      onError: isDarkMode ? Colors.black : Colors.white,
      surface: surfaceColor,
      onSurface: onSurfaceColor,
    );

    return ThemeData(
      colorScheme: colorScheme,
      useMaterial3: true,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          elevation: elevation,
        ),
      ),
      cardTheme: CardThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        elevation: elevation,
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ToolBuilderTheme &&
        other.name == name &&
        other.primaryColor == primaryColor &&
        other.isDarkMode == isDarkMode;
  }

  @override
  int get hashCode => Object.hash(name, primaryColor, isDarkMode);
}
