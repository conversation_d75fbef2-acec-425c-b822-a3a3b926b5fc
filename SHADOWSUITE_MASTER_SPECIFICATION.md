# **SHADOWSUITE MASTER SPECIFICATION**
*Complete Implementation Guide for Cross-Platform Productivity Suite*

## **PROJECT OVERVIEW**

**ShadowSuite** is a comprehensive cross-platform productivity application built with Flutter, featuring four integrated mini-applications with full responsive design and Excel-compatible functionality.

### **Core Architecture**
- **Framework**: Flutter 3.24+ with Dart 3.5+
- **State Management**: Riverpod for all state management
- **Database**: SQLite with proper migrations and relationships
- **Navigation**: GoRouter with nested routing
- **Responsive Design**: Custom breakpoints (Mobile: <768px, Tablet: 768-1024px, Desktop: >1024px)
- **Design System**: Material Design 3 with custom theming

### **Cross-Platform Targets**
- **Primary**: Windows Desktop, Android Mobile
- **Secondary**: Web, iOS, macOS, Linux

---

## **UI/UX ARCHITECTURE**

### **Responsive Navigation System**
- **Desktop (>1024px)**: Sidebar navigation with collapsible mini-sidebar
- **Tablet (768-1024px)**: Collapsible sidebar or bottom navigation
- **Mobile (<768px)**: Bottom navigation bar or drawer

### **Layout Components**
- **StandardizedScaffold**: Base scaffold with responsive navigation
- **ResponsiveContainer**: Adaptive container with breakpoint-aware sizing
- **StandardCard**: Consistent card design across all mini-apps
- **ResponsiveGrid**: Adaptive grid layout for different screen sizes

### **Header System**
- **Mini Top Header**: Compact header (56px height) with:
  - App logo/title
  - Search functionality
  - User profile menu
  - Settings access
  - Theme toggle

### **Color System & Theming**
- **Material Design 3** color system
- **Light/Dark mode** support
- **Custom accent colors** with user customization
- **Appearance customization** with granular controls

---

## **MINI-APPLICATIONS SPECIFICATION**

## **1. MEMO SUITE**
*Complete productivity suite for notes, todos, and voice memos*

### **Features Overview**
- **Rich Text Notes** with formatting, categories, search
- **Advanced Todo Management** with priorities, dependencies, progress tracking
- **Voice Memos** with recording, playback, transcription
- **Cross-referencing** between notes, todos, and voice memos
- **Real-time sync** and offline support

### **Database Schema**
```sql
-- Notes Table
CREATE TABLE notes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  content TEXT,
  category_id INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  is_favorite BOOLEAN DEFAULT FALSE,
  tags TEXT, -- JSON array
  FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Todos Table
CREATE TABLE todos (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  description TEXT,
  priority INTEGER DEFAULT 1, -- 1=Low, 2=Medium, 3=High, 4=Critical
  status INTEGER DEFAULT 0, -- 0=Pending, 1=In Progress, 2=Completed
  due_date DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  category_id INTEGER,
  parent_todo_id INTEGER, -- For subtasks
  progress INTEGER DEFAULT 0, -- 0-100
  FOREIGN KEY (category_id) REFERENCES categories(id),
  FOREIGN KEY (parent_todo_id) REFERENCES todos(id)
);

-- Voice Memos Table
CREATE TABLE voice_memos (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  file_path TEXT NOT NULL,
  duration INTEGER, -- in seconds
  transcription TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  category_id INTEGER,
  FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Categories Table (shared across all memo types)
CREATE TABLE categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  color TEXT, -- Hex color code
  icon TEXT, -- Icon name
  type TEXT -- 'note', 'todo', 'voice', 'all'
);
```

### **Screen Specifications**

#### **Memo Dashboard Screen**
- **Layout**: Responsive grid with overview cards
- **Components**:
  - Quick stats (total notes, pending todos, voice memos)
  - Recent items list
  - Quick action buttons (New Note, New Todo, Record Voice)
  - Search bar with filters
  - Category filter chips

#### **Notes Management**
- **Notes List Screen**: Grid/list view with search, filter, sort
- **Note Editor Screen**: Rich text editor with:
  - Formatting toolbar (bold, italic, lists, links)
  - Category selection
  - Tag management
  - Auto-save functionality
  - Export options (PDF, TXT, MD)

#### **Todo Management**
- **Todo List Screen**: Kanban board or list view with:
  - Priority indicators
  - Progress bars
  - Due date indicators
  - Drag-and-drop reordering
- **Todo Editor Screen**: Comprehensive form with:
  - Title and description
  - Priority selection
  - Due date picker
  - Category assignment
  - Subtask creation
  - Progress tracking

#### **Voice Memo Management**
- **Voice Memo List**: Grid view with playback controls
- **Recording Screen**: Full-screen recorder with:
  - Waveform visualization
  - Recording controls (record, pause, stop)
  - Real-time duration display
  - Quality settings
- **Playback Screen**: Audio player with:
  - Waveform scrubbing
  - Speed controls
  - Transcription display (if available)

### **State Management (Riverpod Providers)**
```dart
// Notes Providers
final notesProvider = StateNotifierProvider<NotesNotifier, List<Note>>();
final noteProvider = StateNotifierProvider.family<NoteNotifier, Note?, int?>();
final noteCategoriesProvider = StateNotifierProvider<CategoriesNotifier, List<Category>>();

// Todos Providers
final todosProvider = StateNotifierProvider<TodosNotifier, List<Todo>>();
final todoProvider = StateNotifierProvider.family<TodoNotifier, Todo?, int?>();
final todoStatsProvider = Provider<TodoStats>();

// Voice Memos Providers
final voiceMemosProvider = StateNotifierProvider<VoiceMemosNotifier, List<VoiceMemo>>();
final recordingProvider = StateNotifierProvider<RecordingNotifier, RecordingState>();
```

---

## **2. ISLAMIC APP (ATHKAR + QURAN)**
*Complete Islamic companion with Quran, Athkar, and Prayer Times*

### **Features Overview**
- **Complete Quran** with Arabic text, multiple translations, search
- **Athkar/Dhikr** with counters, progress tracking, custom dhikr
- **Prayer Times** with location-based calculations and notifications
- **Bookmarks and Favorites** across all Islamic content
- **Audio recitation** support (future enhancement)

### **Database Schema**
```sql
-- Quran Verses Table
CREATE TABLE quran_verses (
  id INTEGER PRIMARY KEY,
  surah_number INTEGER NOT NULL,
  verse_number INTEGER NOT NULL,
  arabic_text TEXT NOT NULL,
  translation_en TEXT,
  translation_ur TEXT,
  transliteration TEXT,
  juz_number INTEGER,
  page_number INTEGER
);

-- Surahs Table
CREATE TABLE surahs (
  id INTEGER PRIMARY KEY,
  number INTEGER NOT NULL UNIQUE,
  name_arabic TEXT NOT NULL,
  name_english TEXT NOT NULL,
  verses_count INTEGER NOT NULL,
  revelation_place TEXT, -- 'Mecca' or 'Medina'
  revelation_order INTEGER
);

-- Athkar Table
CREATE TABLE athkar (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  arabic_text TEXT NOT NULL,
  translation TEXT,
  transliteration TEXT,
  category TEXT, -- 'morning', 'evening', 'after_prayer', 'custom'
  repetitions INTEGER DEFAULT 1,
  source TEXT, -- Hadith reference
  is_custom BOOLEAN DEFAULT FALSE
);

-- Dhikr Sessions Table
CREATE TABLE dhikr_sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  athkar_id INTEGER NOT NULL,
  current_count INTEGER DEFAULT 0,
  target_count INTEGER NOT NULL,
  started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  completed_at DATETIME,
  is_completed BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (athkar_id) REFERENCES athkar(id)
);

-- Prayer Times Table
CREATE TABLE prayer_times (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  date TEXT NOT NULL, -- YYYY-MM-DD format
  fajr TEXT NOT NULL,
  sunrise TEXT NOT NULL,
  dhuhr TEXT NOT NULL,
  asr TEXT NOT NULL,
  maghrib TEXT NOT NULL,
  isha TEXT NOT NULL,
  latitude REAL,
  longitude REAL,
  timezone TEXT
);

-- Bookmarks Table
CREATE TABLE bookmarks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type TEXT NOT NULL, -- 'verse', 'surah', 'athkar'
  reference_id INTEGER NOT NULL,
  title TEXT,
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### **Screen Specifications**

#### **Islamic Dashboard**
- **Prayer Times Widget**: Current and next prayer times
- **Daily Athkar Progress**: Morning/evening athkar completion
- **Quick Access**: Quran, Athkar, Prayer Times
- **Islamic Calendar**: Hijri date display

#### **Quran Reader**
- **Surah List**: Complete list with Arabic/English names
- **Verse Display**: Arabic text with translation options
- **Search Functionality**: Search by Arabic text or translation
- **Reading Features**:
  - Adjustable font sizes
  - Night mode
  - Bookmark verses
  - Share verses
  - Copy to clipboard

#### **Athkar Management**
- **Athkar Categories**: Morning, Evening, After Prayer, Custom
- **Dhikr Counter**: Touch-based counter with haptic feedback
- **Progress Tracking**: Daily/weekly athkar completion
- **Custom Athkar**: User-created dhikr with custom repetitions

#### **Prayer Times**
- **Location-based Calculation**: GPS or manual location entry
- **Calculation Methods**: Multiple calculation methods support
- **Notifications**: Configurable prayer time alerts
- **Qibla Direction**: Compass pointing to Mecca

---

## **3. MONEY FLOW**
*Comprehensive financial management and budgeting*

### **Features Overview**
- **Account Management** with real balance tracking
- **Transaction Recording** with categories, receipts, search
- **Budget Creation** and monitoring with alerts
- **Financial Reports** and charts with real data visualization
- **Multi-currency Support** with exchange rates
- **Goal Setting** and progress tracking

### **Database Schema**
```sql
-- Accounts Table
CREATE TABLE accounts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  type TEXT NOT NULL, -- 'checking', 'savings', 'credit', 'cash', 'investment'
  balance REAL DEFAULT 0.0,
  currency TEXT DEFAULT 'USD',
  bank_name TEXT,
  account_number TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Categories Table
CREATE TABLE transaction_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  type TEXT NOT NULL, -- 'income', 'expense'
  color TEXT,
  icon TEXT,
  parent_category_id INTEGER,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (parent_category_id) REFERENCES transaction_categories(id)
);

-- Transactions Table
CREATE TABLE transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  account_id INTEGER NOT NULL,
  category_id INTEGER,
  amount REAL NOT NULL,
  type TEXT NOT NULL, -- 'income', 'expense', 'transfer'
  description TEXT,
  date DATETIME NOT NULL,
  receipt_path TEXT,
  tags TEXT, -- JSON array
  is_recurring BOOLEAN DEFAULT FALSE,
  recurring_pattern TEXT, -- JSON for recurring rules
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (account_id) REFERENCES accounts(id),
  FOREIGN KEY (category_id) REFERENCES transaction_categories(id)
);

-- Budgets Table
CREATE TABLE budgets (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  category_id INTEGER,
  amount REAL NOT NULL,
  period TEXT NOT NULL, -- 'monthly', 'weekly', 'yearly'
  start_date DATETIME NOT NULL,
  end_date DATETIME,
  is_active BOOLEAN DEFAULT TRUE,
  alert_threshold REAL DEFAULT 0.8, -- Alert at 80% of budget
  FOREIGN KEY (category_id) REFERENCES transaction_categories(id)
);

-- Financial Goals Table
CREATE TABLE financial_goals (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  target_amount REAL NOT NULL,
  current_amount REAL DEFAULT 0.0,
  target_date DATETIME,
  category TEXT, -- 'savings', 'debt_payoff', 'investment'
  description TEXT,
  is_completed BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### **Screen Specifications**

#### **Money Flow Dashboard**
- **Account Overview**: Total balance across all accounts
- **Recent Transactions**: Latest 10 transactions with quick actions
- **Budget Status**: Current month budget progress
- **Quick Actions**: Add Transaction, Transfer Money, View Reports

#### **Accounts Management**
- **Accounts List**: All accounts with balances and quick actions
- **Account Details**: Transaction history, balance trends
- **Add/Edit Account**: Form with account type, bank details

#### **Transaction Management**
- **Transaction List**: Filterable list with search and categories
- **Add Transaction**: Comprehensive form with:
  - Amount and type selection
  - Account and category selection
  - Date picker
  - Receipt photo capture
  - Tags and notes
- **Transaction Details**: Full transaction view with edit/delete options

#### **Budget Management**
- **Budget Overview**: All budgets with progress indicators
- **Budget Creation**: Form with category, amount, period selection
- **Budget Tracking**: Visual progress with spending alerts

#### **Reports and Charts**
- **Spending Analysis**: Pie charts by category
- **Income vs Expenses**: Monthly comparison charts
- **Account Trends**: Balance history over time
- **Custom Reports**: Date range and category filtering

---

## **4. TOOLS BUILDER**
*Excel-compatible spreadsheet engine with UI builder*

### **Features Overview**
- **Excel-Compatible Spreadsheet**: Full formula support (25+ functions)
- **File I/O**: Import/export actual Excel files (.xlsx)
- **UI Builder**: Drag-drop interface for creating custom tools
- **Formula Engine**: Real-time calculations with Excel-like syntax
- **Tool Templates**: Pre-built calculators, converters, forms
- **Data Binding**: Connect UI components to spreadsheet cells

### **Core Components**

#### **Spreadsheet Engine**
```dart
// Core spreadsheet functionality
class SpreadsheetEngine {
  Map<String, SpreadsheetCell> cells = {};
  FormulaEngine formulaEngine = FormulaEngine();
  
  // Excel-compatible functions (25+ functions)
  List<String> supportedFunctions = [
    'SUM', 'AVERAGE', 'COUNT', 'MAX', 'MIN',
    'IF', 'AND', 'OR', 'NOT',
    'VLOOKUP', 'HLOOKUP', 'INDEX', 'MATCH',
    'CONCATENATE', 'LEFT', 'RIGHT', 'MID', 'LEN',
    'ROUND', 'ROUNDUP', 'ROUNDDOWN',
    'TODAY', 'NOW', 'DATE', 'TIME',
    'PMT', 'PV', 'FV', 'RATE', 'NPER'
  ];
}

// Cell data structure
class SpreadsheetCell {
  String address; // A1, B2, etc.
  dynamic value;
  String? formula;
  CellFormat format;
  List<String> dependencies = [];
  List<String> dependents = [];
}

// Cell formatting
class CellFormat {
  Color? backgroundColor;
  Color? textColor;
  FontWeight? fontWeight;
  double? fontSize;
  TextAlign? textAlign;
  String? numberFormat; // Currency, percentage, etc.
  BorderStyle? border;
}
```

#### **Formula Engine**
```dart
class FormulaEngine {
  // Parse and evaluate Excel-like formulas
  dynamic evaluateFormula(String formula, Map<String, dynamic> context);
  
  // Function implementations
  double sum(List<double> values);
  double average(List<double> values);
  int count(List<dynamic> values);
  dynamic vlookup(dynamic lookupValue, List<List<dynamic>> tableArray, int colIndex);
  String concatenate(List<String> values);
  // ... 25+ Excel functions
}
```

#### **UI Builder Components**
```dart
// Draggable UI components
enum UIComponentType {
  textField, numberField, dropdown, checkbox,
  button, label, chart, table, image, container
}

class UIComponent {
  String id;
  UIComponentType type;
  Map<String, dynamic> properties;
  String? boundCell; // Spreadsheet cell binding
  List<UIComponent> children = []; // For containers
}

// UI Builder state
class UIBuilderState {
  List<UIComponent> components = [];
  UIComponent? selectedComponent;
  bool isPreviewMode = false;
  Map<String, dynamic> dataBindings = {};
}
```

### **Screen Specifications**

#### **Tools Builder Dashboard**
- **My Tools**: Grid of created tools with preview thumbnails
- **Templates**: Pre-built tool templates (calculators, converters)
- **Recent Tools**: Recently edited tools
- **Quick Actions**: Create New Tool, Import Excel, Browse Templates

#### **Spreadsheet Editor**
- **Grid Interface**: Excel-like grid with:
  - Column headers (A, B, C...)
  - Row numbers (1, 2, 3...)
  - Cell selection and editing
  - Formula bar
  - Formatting toolbar
- **Function Library**: Searchable list of available functions
- **Data Validation**: Cell input validation rules

#### **UI Builder Interface**
- **Component Palette**: Draggable UI components
- **Design Canvas**: Visual design area with grid snapping
- **Properties Panel**: Component property editor
- **Data Binding Panel**: Connect components to spreadsheet cells
- **Preview Mode**: Test the created tool

#### **Tool Preview/Runtime**
- **Interactive Tool**: Fully functional tool with real calculations
- **Data Input**: Form fields bound to spreadsheet cells
- **Real-time Results**: Automatic calculation updates
- **Export Options**: Save as standalone app or share

### **File I/O Specifications**
```dart
// Excel file import/export
class ExcelService {
  Future<Spreadsheet> importExcel(String filePath);
  Future<void> exportExcel(Spreadsheet spreadsheet, String filePath);
  
  // Support for:
  // - .xlsx files (Excel 2007+)
  // - Multiple worksheets
  // - Cell formatting
  // - Formulas (converted to compatible format)
  // - Charts (basic support)
}
```

---

## **TECHNICAL IMPLEMENTATION DETAILS**

### **State Management Architecture**
```dart
// Global app state
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>();

// Feature-specific providers
final memoStateProvider = StateNotifierProvider<MemoStateNotifier, MemoState>();
final islamicStateProvider = StateNotifierProvider<IslamicStateNotifier, IslamicState>();
final moneyStateProvider = StateNotifierProvider<MoneyStateNotifier, MoneyState>();
final toolsStateProvider = StateNotifierProvider<ToolsStateNotifier, ToolsState>();

// Settings and preferences
final settingsProvider = StateNotifierProvider<SettingsNotifier, AppSettings>();
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeData>();
```

### **Database Management**
```dart
// SQLite database service
class DatabaseService {
  static Database? _database;
  
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'shadowsuite.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _upgradeDatabase,
    );
  }
  
  // Migration system for database updates
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion);
}
```

### **Responsive Design System**
```dart
// Breakpoint definitions
class ResponsiveBreakpoints {
  static const double mobile = 768;
  static const double tablet = 1024;
  static const double desktop = 1440;
  
  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < mobile;
  
  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= mobile &&
      MediaQuery.of(context).size.width < tablet;
  
  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= tablet;
}

// Responsive spacing
class ResponsiveSpacing {
  static double xs(BuildContext context) => ResponsiveBreakpoints.isMobile(context) ? 4 : 8;
  static double sm(BuildContext context) => ResponsiveBreakpoints.isMobile(context) ? 8 : 12;
  static double md(BuildContext context) => ResponsiveBreakpoints.isMobile(context) ? 12 : 16;
  static double lg(BuildContext context) => ResponsiveBreakpoints.isMobile(context) ? 16 : 24;
  static double xl(BuildContext context) => ResponsiveBreakpoints.isMobile(context) ? 24 : 32;
}
```

### **Navigation System**
```dart
// GoRouter configuration
final appRouter = GoRouter(
  initialLocation: '/',
  routes: [
    // Dashboard
    GoRoute(path: '/', builder: (context, state) => const DashboardScreen()),
    
    // Memo Suite
    GoRoute(
      path: '/memo',
      builder: (context, state) => const MemoScreen(),
      routes: [
        GoRoute(path: '/notes', builder: (context, state) => const NotesScreen()),
        GoRoute(path: '/todos', builder: (context, state) => const TodosScreen()),
        GoRoute(path: '/voice', builder: (context, state) => const VoiceMemosScreen()),
      ],
    ),
    
    // Islamic App
    GoRoute(
      path: '/islamic',
      builder: (context, state) => const IslamicScreen(),
      routes: [
        GoRoute(path: '/quran', builder: (context, state) => const QuranScreen()),
        GoRoute(path: '/athkar', builder: (context, state) => const AthkarScreen()),
        GoRoute(path: '/prayer-times', builder: (context, state) => const PrayerTimesScreen()),
      ],
    ),
    
    // Money Flow
    GoRoute(
      path: '/money',
      builder: (context, state) => const MoneyScreen(),
      routes: [
        GoRoute(path: '/accounts', builder: (context, state) => const AccountsScreen()),
        GoRoute(path: '/transactions', builder: (context, state) => const TransactionsScreen()),
        GoRoute(path: '/budgets', builder: (context, state) => const BudgetsScreen()),
        GoRoute(path: '/reports', builder: (context, state) => const ReportsScreen()),
      ],
    ),
    
    // Tools Builder
    GoRoute(
      path: '/tools',
      builder: (context, state) => const ToolsScreen(),
      routes: [
        GoRoute(path: '/spreadsheet', builder: (context, state) => const SpreadsheetScreen()),
        GoRoute(path: '/ui-builder', builder: (context, state) => const UIBuilderScreen()),
        GoRoute(path: '/templates', builder: (context, state) => const TemplatesScreen()),
      ],
    ),
    
    // Settings
    GoRoute(path: '/settings', builder: (context, state) => const SettingsScreen()),
  ],
);
```

---

## **IMPLEMENTATION PHASES**

### **Phase 1: Project Setup & Core Framework (Days 1-2)**
1. Flutter project initialization
2. Dependency setup (Riverpod, GoRouter, SQLite, etc.)
3. Basic responsive framework
4. Navigation system
5. Database schema creation
6. Basic theming system

### **Phase 2: Memo Suite Implementation (Days 3-5)**
1. Database models and services
2. Notes functionality (CRUD operations)
3. Todo management system
4. Voice memo recording and playback
5. Search and filtering
6. Category management

### **Phase 3: Islamic App Implementation (Days 6-8)**
1. Quran database integration
2. Quran reader with search
3. Athkar management and dhikr counter
4. Prayer times calculation
5. Bookmarks and favorites
6. Islamic calendar integration

### **Phase 4: Money Flow Implementation (Days 9-11)**
1. Account management system
2. Transaction recording and categorization
3. Budget creation and tracking
4. Financial reports and charts
5. Multi-currency support
6. Goal setting and tracking

### **Phase 5: Tools Builder Implementation (Days 12-15)**
1. Spreadsheet engine with formula support
2. Excel file import/export
3. UI builder with drag-drop interface
4. Data binding system
5. Tool templates and gallery
6. Preview and runtime system

### **Phase 6: Settings & Customization (Days 16-17)**
1. Comprehensive settings system
2. Appearance customization
3. User preferences
4. Data backup and restore
5. Export/import functionality

### **Phase 7: Testing & Optimization (Days 18-20)**
1. Comprehensive testing of all features
2. Performance optimization
3. Responsive design testing
4. Cross-platform compatibility
5. Final bug fixes and polish

---

## **QUALITY STANDARDS**

### **Code Quality**
- **Zero compilation errors** before proceeding to next phase
- **Zero analyzer warnings** in production code
- **Comprehensive error handling** with user-friendly messages
- **Clean architecture** with proper separation of concerns
- **Documented code** with clear comments and documentation

### **Testing Requirements**
- **Unit tests** for all business logic
- **Widget tests** for UI components
- **Integration tests** for complete user flows
- **Performance tests** for large datasets
- **Cross-platform testing** on multiple devices

### **Performance Standards**
- **App startup time** < 3 seconds
- **Screen transitions** < 300ms
- **Database operations** < 100ms for simple queries
- **Excel file import** < 5 seconds for files up to 10MB
- **Memory usage** < 200MB on mobile devices

### **User Experience Standards**
- **Responsive design** works on all screen sizes
- **Accessibility** compliance with WCAG guidelines
- **Offline functionality** for core features
- **Data persistence** across app restarts
- **Intuitive navigation** with clear user flows

---

## **DELIVERABLES**

1. **Complete ShadowSuite Application** with all four mini-apps
2. **Responsive Design** tested on mobile, tablet, and desktop
3. **Excel-Compatible Spreadsheet Engine** with 25+ functions
4. **Comprehensive Documentation** including user guides
5. **Production-Ready Codebase** with zero errors/warnings
6. **Cross-Platform Builds** for Windows, Android, and Web
7. **Performance Benchmarks** and optimization reports
8. **Testing Suite** with comprehensive coverage

---

## **DETAILED COMPONENT SPECIFICATIONS**

### **StandardizedScaffold Widget**
```dart
class StandardizedScaffold extends ConsumerWidget {
  final String title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showBackButton;
  final String? currentRoute;

  // Responsive navigation logic
  // Mobile: Bottom navigation or drawer
  // Tablet: Collapsible sidebar
  // Desktop: Full sidebar with mini-sidebar option
}
```

### **ResponsiveContainer Widget**
```dart
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? mobilePadding;
  final EdgeInsets? tabletPadding;
  final EdgeInsets? desktopPadding;
  final double? mobileMaxWidth;
  final double? tabletMaxWidth;
  final double? desktopMaxWidth;

  // Automatically adjusts padding and max width based on screen size
}
```

### **StandardCard Widget**
```dart
class StandardCard extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final EdgeInsets? padding;
  final bool isClickable;
  final VoidCallback? onTap;

  // Consistent card design with Material Design 3 styling
}
```

---

## **EXCEL FUNCTION IMPLEMENTATIONS**

### **Mathematical Functions**
```dart
// SUM function
double sum(List<dynamic> values) {
  return values.where((v) => v is num).fold(0.0, (sum, v) => sum + v.toDouble());
}

// AVERAGE function
double average(List<dynamic> values) {
  final numbers = values.where((v) => v is num).toList();
  return numbers.isEmpty ? 0.0 : sum(numbers) / numbers.length;
}

// ROUND function
double round(double value, int digits) {
  final factor = pow(10, digits);
  return (value * factor).round() / factor;
}
```

### **Logical Functions**
```dart
// IF function
dynamic ifFunction(bool condition, dynamic trueValue, dynamic falseValue) {
  return condition ? trueValue : falseValue;
}

// AND function
bool andFunction(List<dynamic> values) {
  return values.every((v) => v == true || v == 1 || v == "TRUE");
}

// OR function
bool orFunction(List<dynamic> values) {
  return values.any((v) => v == true || v == 1 || v == "TRUE");
}
```

### **Lookup Functions**
```dart
// VLOOKUP function
dynamic vlookup(dynamic lookupValue, List<List<dynamic>> tableArray, int colIndex, [bool exactMatch = false]) {
  for (var row in tableArray) {
    if (row.isNotEmpty && row[0] == lookupValue) {
      return colIndex <= row.length ? row[colIndex - 1] : null;
    }
  }
  return exactMatch ? null : "#N/A";
}

// INDEX function
dynamic index(List<List<dynamic>> array, int rowIndex, int colIndex) {
  if (rowIndex <= array.length && colIndex <= array[rowIndex - 1].length) {
    return array[rowIndex - 1][colIndex - 1];
  }
  return "#REF!";
}
```

### **Text Functions**
```dart
// CONCATENATE function
String concatenate(List<dynamic> values) {
  return values.map((v) => v.toString()).join('');
}

// LEFT function
String left(String text, int numChars) {
  return text.length >= numChars ? text.substring(0, numChars) : text;
}

// MID function
String mid(String text, int startNum, int numChars) {
  final start = startNum - 1; // Excel uses 1-based indexing
  return text.substring(start, (start + numChars).clamp(0, text.length));
}
```

### **Date/Time Functions**
```dart
// TODAY function
DateTime today() => DateTime.now();

// DATE function
DateTime dateFunction(int year, int month, int day) {
  return DateTime(year, month, day);
}

// NOW function
DateTime now() => DateTime.now();
```

### **Financial Functions**
```dart
// PMT function (Payment calculation)
double pmt(double rate, int nper, double pv, [double fv = 0, bool type = false]) {
  if (rate == 0) return -(pv + fv) / nper;

  final pvif = pow(1 + rate, nper);
  final payment = rate / (pvif - 1) * -(pv * pvif + fv);

  return type ? payment / (1 + rate) : payment;
}

// PV function (Present Value)
double pv(double rate, int nper, double pmt, [double fv = 0, bool type = false]) {
  if (rate == 0) return -pmt * nper - fv;

  final pvif = pow(1 + rate, nper);
  final presentValue = (pmt * (1 - 1 / pvif) / rate + fv / pvif) * -1;

  return type ? presentValue * (1 + rate) : presentValue;
}
```

---

## **DATABASE MIGRATION SYSTEM**

### **Migration Framework**
```dart
class DatabaseMigration {
  final int version;
  final String description;
  final List<String> upQueries;
  final List<String> downQueries;

  DatabaseMigration({
    required this.version,
    required this.description,
    required this.upQueries,
    required this.downQueries,
  });
}

class MigrationManager {
  static final List<DatabaseMigration> migrations = [
    DatabaseMigration(
      version: 1,
      description: "Initial database schema",
      upQueries: [
        "CREATE TABLE notes (...)",
        "CREATE TABLE todos (...)",
        // ... all initial tables
      ],
      downQueries: [
        "DROP TABLE IF EXISTS notes",
        "DROP TABLE IF EXISTS todos",
        // ... cleanup queries
      ],
    ),
    // Future migrations...
  ];

  static Future<void> runMigrations(Database db, int oldVersion, int newVersion) async {
    for (var migration in migrations) {
      if (migration.version > oldVersion && migration.version <= newVersion) {
        for (var query in migration.upQueries) {
          await db.execute(query);
        }
      }
    }
  }
}
```

---

## **ERROR HANDLING SYSTEM**

### **Custom Exception Classes**
```dart
abstract class ShadowSuiteException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const ShadowSuiteException(this.message, {this.code, this.originalError});
}

class DatabaseException extends ShadowSuiteException {
  const DatabaseException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

class FileOperationException extends ShadowSuiteException {
  const FileOperationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

class ValidationException extends ShadowSuiteException {
  const ValidationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}
```

### **Global Error Handler**
```dart
class ErrorHandler {
  static void handleError(dynamic error, StackTrace stackTrace) {
    // Log error for debugging
    debugPrint('Error: $error');
    debugPrint('Stack trace: $stackTrace');

    // Show user-friendly message
    String userMessage = _getUserFriendlyMessage(error);

    // Report to crash analytics (if enabled)
    _reportError(error, stackTrace);

    // Show error to user
    _showErrorToUser(userMessage);
  }

  static String _getUserFriendlyMessage(dynamic error) {
    if (error is DatabaseException) {
      return "There was a problem saving your data. Please try again.";
    } else if (error is FileOperationException) {
      return "There was a problem with the file operation. Please check file permissions.";
    } else if (error is ValidationException) {
      return error.message; // Validation messages are already user-friendly
    } else {
      return "An unexpected error occurred. Please try again.";
    }
  }
}
```

---

## **PERFORMANCE OPTIMIZATION STRATEGIES**

### **Database Optimization**
```dart
class DatabaseOptimizer {
  // Batch operations for better performance
  static Future<void> batchInsert<T>(List<T> items, Future<void> Function(T) insertFunction) async {
    const batchSize = 100;
    for (int i = 0; i < items.length; i += batchSize) {
      final batch = items.skip(i).take(batchSize);
      await Future.wait(batch.map(insertFunction));
    }
  }

  // Lazy loading for large datasets
  static Stream<List<T>> lazyLoad<T>(
    Future<List<T>> Function(int offset, int limit) loadFunction,
    {int pageSize = 50}
  ) async* {
    int offset = 0;
    List<T> batch;

    do {
      batch = await loadFunction(offset, pageSize);
      if (batch.isNotEmpty) {
        yield batch;
        offset += pageSize;
      }
    } while (batch.length == pageSize);
  }
}
```

### **Memory Management**
```dart
class MemoryManager {
  // Image caching with size limits
  static final Map<String, ui.Image> _imageCache = {};
  static const int maxCacheSize = 50;

  static Future<ui.Image> getCachedImage(String path) async {
    if (_imageCache.containsKey(path)) {
      return _imageCache[path]!;
    }

    if (_imageCache.length >= maxCacheSize) {
      _imageCache.remove(_imageCache.keys.first);
    }

    final image = await _loadImage(path);
    _imageCache[path] = image;
    return image;
  }

  // Dispose of large objects when not needed
  static void disposeResources() {
    _imageCache.clear();
    // Clear other caches...
  }
}
```

---

## **ACCESSIBILITY IMPLEMENTATION**

### **Accessibility Standards**
```dart
class AccessibilityHelper {
  // Semantic labels for screen readers
  static Widget makeAccessible(Widget child, {
    required String label,
    String? hint,
    bool isButton = false,
    bool isHeader = false,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: isButton,
      header: isHeader,
      child: child,
    );
  }

  // High contrast color checking
  static bool hasGoodContrast(Color foreground, Color background) {
    final luminance1 = foreground.computeLuminance();
    final luminance2 = background.computeLuminance();
    final ratio = (max(luminance1, luminance2) + 0.05) / (min(luminance1, luminance2) + 0.05);
    return ratio >= 4.5; // WCAG AA standard
  }

  // Focus management
  static void announceFocus(String message) {
    SemanticsService.announce(message, TextDirection.ltr);
  }
}
```

### **Keyboard Navigation**
```dart
class KeyboardNavigationHelper {
  static Widget makeKeyboardNavigable(Widget child, {
    required VoidCallback onActivate,
    FocusNode? focusNode,
  }) {
    return Focus(
      focusNode: focusNode,
      onKey: (node, event) {
        if (event is RawKeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            onActivate();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: child,
    );
  }
}
```

---

This master specification serves as the single source of truth for the complete ShadowSuite implementation. Every feature, screen, and component must be built according to these specifications to ensure consistency, quality, and completeness.
