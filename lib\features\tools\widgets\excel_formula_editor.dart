import 'package:flutter/material.dart';
import '../services/excel_engine_service.dart';
import '../models/excel_function.dart';

/// Excel formula editor with autocomplete
class ExcelFormulaEditor extends StatefulWidget {
  final ExcelEngineService excelEngine;
  final String selectedCell;
  final Function(String) onCellSelected;

  const ExcelFormulaEditor({
    super.key,
    required this.excelEngine,
    required this.selectedCell,
    required this.onCellSelected,
  });

  @override
  State<ExcelFormulaEditor> createState() => _ExcelFormulaEditorState();
}

class _ExcelFormulaEditorState extends State<ExcelFormulaEditor> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();
  List<AutocompleteSuggestion> _suggestions = [];
  bool _showSuggestions = false;
  int _selectedSuggestionIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _updateFormulaBar();
  }

  @override
  void didUpdateWidget(ExcelFormulaEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedCell != widget.selectedCell) {
      _updateFormulaBar();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _updateFormulaBar() {
    final formula = widget.excelEngine.getCellFormula(widget.selectedCell);
    final value = widget.excelEngine.getCellValue(widget.selectedCell);
    
    if (formula != null) {
      _controller.text = formula;
    } else {
      _controller.text = value?.toString() ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Cell reference and value display
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                widget.selectedCell,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  widget.excelEngine.getCellValue(widget.selectedCell)?.toString() ?? '',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Formula input
        Stack(
          children: [
            TextField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                labelText: 'Formula',
                hintText: 'Enter formula or value...',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.functions),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.check),
                      onPressed: _applyFormula,
                      tooltip: 'Apply',
                    ),
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearFormula,
                      tooltip: 'Clear',
                    ),
                  ],
                ),
              ),
              onChanged: _onTextChanged,
              onSubmitted: (_) => _applyFormula(),
              style: const TextStyle(fontFamily: 'monospace'),
            ),

            // Autocomplete suggestions
            if (_showSuggestions && _suggestions.isNotEmpty)
              Positioned(
                top: 60,
                left: 0,
                right: 0,
                child: _buildSuggestionsPanel(),
              ),
          ],
        ),

        const SizedBox(height: 12),

        // Formula validation
        _buildValidationPanel(),

        const SizedBox(height: 12),

        // Quick function buttons
        _buildQuickFunctions(),
      ],
    );
  }

  Widget _buildSuggestionsPanel() {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 200),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: _suggestions.length,
          itemBuilder: (context, index) {
            final suggestion = _suggestions[index];
            final isSelected = index == _selectedSuggestionIndex;

            return ListTile(
              dense: true,
              selected: isSelected,
              leading: Icon(_getSuggestionIcon(suggestion.type)),
              title: Text(
                suggestion.displayText,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
              subtitle: Text(suggestion.description),
              onTap: () => _applySuggestion(suggestion),
            );
          },
        ),
      ),
    );
  }

  Widget _buildValidationPanel() {
    final formula = _controller.text;
    if (formula.isEmpty || !formula.startsWith('=')) {
      return const SizedBox.shrink();
    }

    final validation = widget.excelEngine.validateFormula(formula);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: validation.isValid 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: validation.isValid ? Colors.green : Colors.red,
        ),
      ),
      child: Row(
        children: [
          Icon(
            validation.isValid ? Icons.check_circle : Icons.error,
            color: validation.isValid ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              validation.isValid 
                  ? 'Formula is valid'
                  : validation.error ?? 'Invalid formula',
              style: TextStyle(
                color: validation.isValid ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFunctions() {
    final quickFunctions = [
      'SUM', 'AVERAGE', 'MAX', 'MIN', 'COUNT',
      'IF', 'CONCATENATE', 'TODAY', 'NOW'
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: quickFunctions.map((functionName) {
        return ActionChip(
          label: Text(functionName),
          onPressed: () => _insertQuickFunction(functionName),
          avatar: const Icon(Icons.functions, size: 16),
        );
      }).toList(),
    );
  }

  IconData _getSuggestionIcon(SuggestionType type) {
    switch (type) {
      case SuggestionType.function:
        return Icons.functions;
      case SuggestionType.cellReference:
        return Icons.grid_on;
      case SuggestionType.range:
        return Icons.select_all;
      case SuggestionType.constant:
        return Icons.numbers;
    }
  }

  void _onTextChanged(String text) {
    setState(() {
      _suggestions = widget.excelEngine.getAutocompleteSuggestions(text);
      _showSuggestions = _suggestions.isNotEmpty && _focusNode.hasFocus;
      _selectedSuggestionIndex = 0;
    });
  }

  void _applySuggestion(AutocompleteSuggestion suggestion) {
    final currentText = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;
    
    // Find the word being typed
    int wordStart = cursorPosition;
    while (wordStart > 0 && !RegExp(r'[^A-Za-z0-9_]').hasMatch(currentText[wordStart - 1])) {
      wordStart--;
    }

    final newText = currentText.substring(0, wordStart) + 
                   suggestion.insertText + 
                   currentText.substring(cursorPosition);

    _controller.text = newText;
    _controller.selection = TextSelection.collapsed(
      offset: wordStart + suggestion.insertText.length,
    );

    setState(() {
      _showSuggestions = false;
    });
  }

  void _insertQuickFunction(String functionName) {
    final function = ExcelFunctionLibrary.getFunction(functionName);
    if (function != null) {
      final insertText = '=${function.name}()';
      _controller.text = insertText;
      _controller.selection = TextSelection.collapsed(
        offset: insertText.length - 1,
      );
    }
  }

  void _applyFormula() {
    final formula = _controller.text;
    widget.excelEngine.setCellValue(widget.selectedCell, formula);
    
    setState(() {
      _showSuggestions = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Applied formula to ${widget.selectedCell}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _clearFormula() {
    _controller.clear();
    widget.excelEngine.setCellValue(widget.selectedCell, '');
    
    setState(() {
      _showSuggestions = false;
    });
  }
}
