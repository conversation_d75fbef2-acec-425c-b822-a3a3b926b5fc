import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../providers/ui_builder_provider.dart';
import 'component_renderer.dart';

/// UI Builder canvas for designing interfaces
class UICanvas extends ConsumerStatefulWidget {
  final Function(UIComponent component, Offset position)? onComponentDropped;
  final Function(String componentId)? onComponentSelected;
  final Function(String componentId, Offset newPosition)? onComponentMoved;
  final Function(String componentId, Size newSize)? onComponentResized;

  const UICanvas({
    super.key,
    this.onComponentDropped,
    this.onComponentSelected,
    this.onComponentMoved,
    this.onComponentResized,
  });

  @override
  ConsumerState<UICanvas> createState() => _UICanvasState();
}

class _UICanvasState extends ConsumerState<UICanvas> {
  final GlobalKey _canvasKey = GlobalKey();
  Offset? _dragStartPosition;
  String? _draggedComponentId;

  @override
  Widget build(BuildContext context) {
    final uiBuilderState = ref.watch(uiBuilderProvider);
    
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Stack(
          children: [
            // Grid background
            if (uiBuilderState.layoutMode == LayoutMode.grid)
              _buildGridBackground(uiBuilderState.gridSize),
            
            // Canvas area
            Positioned.fill(
              child: DragTarget<UIComponentType>(
                key: _canvasKey,
                onAcceptWithDetails: (details) => _handleComponentDrop(details),
                builder: (context, candidateData, rejectedData) {
                  return GestureDetector(
                    onTap: () => _clearSelection(),
                    child: CustomPaint(
                      painter: _CanvasPainter(
                        components: uiBuilderState.components.values.toList(),
                        selectedComponentId: uiBuilderState.selectedComponentId,
                        isPreviewMode: uiBuilderState.isPreviewMode,
                        gridSize: uiBuilderState.gridSize,
                        showGrid: uiBuilderState.layoutMode == LayoutMode.grid,
                      ),
                      child: Stack(
                        children: [
                          // Render components
                          ...uiBuilderState.components.values.map((component) {
                            return _buildComponentWidget(component, uiBuilderState);
                          }),
                          
                          // Selection overlay
                          if (uiBuilderState.selectedComponent != null && !uiBuilderState.isPreviewMode)
                            _buildSelectionOverlay(uiBuilderState.selectedComponent!),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Canvas toolbar
            Positioned(
              top: 16,
              right: 16,
              child: _buildCanvasToolbar(uiBuilderState),
            ),
          ],
        ),
      ),
    );
  }

  /// Build grid background
  Widget _buildGridBackground(double gridSize) {
    return CustomPaint(
      painter: _GridPainter(
        gridSize: gridSize,
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
      ),
      child: Container(),
    );
  }

  /// Build component widget
  Widget _buildComponentWidget(UIComponent component, UIBuilderState state) {
    final isSelected = component.id == state.selectedComponentId;
    final isPreviewMode = state.isPreviewMode;
    
    return Positioned(
      left: component.position.dx,
      top: component.position.dy,
      child: GestureDetector(
        onTap: () => _selectComponent(component.id),
        onPanStart: isPreviewMode ? null : (details) => _startDrag(component.id, details),
        onPanUpdate: isPreviewMode ? null : (details) => _updateDrag(details),
        onPanEnd: isPreviewMode ? null : (details) => _endDrag(),
        child: Container(
          width: component.size.width,
          height: component.size.height,
          decoration: BoxDecoration(
            border: isSelected && !isPreviewMode
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: ComponentRenderer(
            component: component,
            isPreviewMode: isPreviewMode,
            onValueChanged: (value) => _handleComponentValueChanged(component, value),
          ),
        ),
      ),
    );
  }

  /// Build selection overlay with resize handles
  Widget _buildSelectionOverlay(UIComponent component) {
    return Positioned(
      left: component.position.dx - 4,
      top: component.position.dy - 4,
      child: Container(
        width: component.size.width + 8,
        height: component.size.height + 8,
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        child: Stack(
          children: [
            // Resize handles
            ..._buildResizeHandles(component),
          ],
        ),
      ),
    );
  }

  /// Build resize handles
  List<Widget> _buildResizeHandles(UIComponent component) {
    const handleSize = 8.0;
    final handles = <Widget>[];
    
    // Corner handles
    final positions = [
      const Offset(-4, -4), // Top-left
      Offset(component.size.width - 4, -4), // Top-right
      Offset(-4, component.size.height - 4), // Bottom-left
      Offset(component.size.width - 4, component.size.height - 4), // Bottom-right
    ];
    
    for (int i = 0; i < positions.length; i++) {
      handles.add(
        Positioned(
          left: positions[i].dx,
          top: positions[i].dy,
          child: GestureDetector(
            onPanUpdate: (details) => _handleResize(component, details, i),
            child: Container(
              width: handleSize,
              height: handleSize,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                border: Border.all(
                  color: Theme.of(context).colorScheme.surface,
                  width: 1,
                ),
              ),
            ),
          ),
        ),
      );
    }
    
    return handles;
  }

  /// Build canvas toolbar
  Widget _buildCanvasToolbar(UIBuilderState state) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Layout mode toggle
          SegmentedButton<LayoutMode>(
            segments: const [
              ButtonSegment(
                value: LayoutMode.grid,
                icon: Icon(Icons.grid_on, size: 16),
                tooltip: 'Grid Mode',
              ),
              ButtonSegment(
                value: LayoutMode.freeform,
                icon: Icon(Icons.crop_free, size: 16),
                tooltip: 'Free-form Mode',
              ),
            ],
            selected: {state.layoutMode},
            onSelectionChanged: (modes) {
              if (modes.isNotEmpty) {
                ref.read(uiBuilderProvider.notifier).setLayoutMode(modes.first);
              }
            },
          ),
          
          const SizedBox(width: 8),
          
          // Preview mode toggle
          IconButton(
            onPressed: () => ref.read(uiBuilderProvider.notifier).togglePreviewMode(),
            icon: Icon(state.isPreviewMode ? Icons.edit : Icons.preview),
            tooltip: state.isPreviewMode ? 'Edit Mode' : 'Preview Mode',
            style: IconButton.styleFrom(
              backgroundColor: state.isPreviewMode 
                  ? Theme.of(context).colorScheme.primaryContainer
                  : null,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Snap to grid toggle
          if (state.layoutMode == LayoutMode.grid)
            IconButton(
              onPressed: () => ref.read(uiBuilderProvider.notifier).toggleSnapToGrid(),
              icon: Icon(state.snapToGrid ? Icons.grid_4x4 : Icons.grid_off),
              tooltip: 'Snap to Grid',
              style: IconButton.styleFrom(
                backgroundColor: state.snapToGrid 
                    ? Theme.of(context).colorScheme.primaryContainer
                    : null,
              ),
            ),
        ],
      ),
    );
  }

  /// Handle component drop from palette
  void _handleComponentDrop(DragTargetDetails<UIComponentType> details) {
    final renderBox = _canvasKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;
    
    final localPosition = renderBox.globalToLocal(details.offset);
    final component = UIComponent.create(details.data);
    
    ref.read(uiBuilderProvider.notifier).addComponent(component, position: localPosition);
    widget.onComponentDropped?.call(component, localPosition);
  }

  /// Select component
  void _selectComponent(String componentId) {
    ref.read(uiBuilderProvider.notifier).selectComponent(componentId);
    widget.onComponentSelected?.call(componentId);
  }

  /// Clear selection
  void _clearSelection() {
    ref.read(uiBuilderProvider.notifier).clearSelection();
  }

  /// Start dragging component
  void _startDrag(String componentId, DragStartDetails details) {
    _draggedComponentId = componentId;
    _dragStartPosition = details.localPosition;
  }

  /// Update drag position
  void _updateDrag(DragUpdateDetails details) {
    if (_draggedComponentId == null || _dragStartPosition == null) return;
    
    final component = ref.read(uiBuilderProvider).components[_draggedComponentId!];
    if (component == null) return;
    
    final newPosition = component.position + details.delta;
    ref.read(uiBuilderProvider.notifier).moveComponent(_draggedComponentId!, newPosition);
    widget.onComponentMoved?.call(_draggedComponentId!, newPosition);
  }

  /// End dragging
  void _endDrag() {
    _draggedComponentId = null;
    _dragStartPosition = null;
  }

  /// Handle component resize
  void _handleResize(UIComponent component, DragUpdateDetails details, int handleIndex) {
    Size newSize = component.size;
    
    switch (handleIndex) {
      case 0: // Top-left
        newSize = Size(
          component.size.width - details.delta.dx,
          component.size.height - details.delta.dy,
        );
        break;
      case 1: // Top-right
        newSize = Size(
          component.size.width + details.delta.dx,
          component.size.height - details.delta.dy,
        );
        break;
      case 2: // Bottom-left
        newSize = Size(
          component.size.width - details.delta.dx,
          component.size.height + details.delta.dy,
        );
        break;
      case 3: // Bottom-right
        newSize = Size(
          component.size.width + details.delta.dx,
          component.size.height + details.delta.dy,
        );
        break;
    }
    
    // Ensure minimum size
    newSize = Size(
      newSize.width.clamp(20, double.infinity),
      newSize.height.clamp(20, double.infinity),
    );
    
    ref.read(uiBuilderProvider.notifier).resizeComponent(component.id, newSize);
    widget.onComponentResized?.call(component.id, newSize);
  }

  /// Handle component value changes
  void _handleComponentValueChanged(UIComponent component, dynamic value) {
    // Update bound spreadsheet cell if applicable
    if (component.cellBinding != null) {
      // This would integrate with the spreadsheet provider
      // ref.read(simpleSpreadsheetProvider.notifier).updateCell(worksheetId, component.cellBinding!, value);
    }
  }
}

/// Custom painter for canvas grid
class _GridPainter extends CustomPainter {
  final double gridSize;
  final Color color;

  _GridPainter({required this.gridSize, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    // Draw vertical lines
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    // Draw horizontal lines
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter for canvas
class _CanvasPainter extends CustomPainter {
  final List<UIComponent> components;
  final String? selectedComponentId;
  final bool isPreviewMode;
  final double gridSize;
  final bool showGrid;

  _CanvasPainter({
    required this.components,
    this.selectedComponentId,
    required this.isPreviewMode,
    required this.gridSize,
    required this.showGrid,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Additional canvas painting logic can be added here
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
