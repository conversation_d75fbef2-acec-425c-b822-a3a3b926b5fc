import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note.dart';

// Notes provider
final notesProvider = StateNotifierProvider<NotesNotifier, List<TextNote>>((ref) {
  return NotesNotifier();
});

// Filtered notes providers
final pinnedNotesProvider = Provider<List<TextNote>>((ref) {
  final notes = ref.watch(notesProvider);
  return notes.where((note) => note.isPinned && !note.isArchived).toList();
});

final archivedNotesProvider = Provider<List<TextNote>>((ref) {
  final notes = ref.watch(notesProvider);
  return notes.where((note) => note.isArchived).toList();
});

final favoriteNotesProvider = Provider<List<TextNote>>((ref) {
  final notes = ref.watch(notesProvider);
  return notes.where((note) => note.isFavorite && !note.isArchived).toList();
});

final activeNotesProvider = Provider<List<TextNote>>((ref) {
  final notes = ref.watch(notesProvider);
  return notes.where((note) => !note.isArchived).toList();
});

// Category filter provider
final selectedCategoryProvider = StateProvider<String?>((ref) => null);

final filteredNotesProvider = Provider<List<TextNote>>((ref) {
  final notes = ref.watch(activeNotesProvider);
  final selectedCategory = ref.watch(selectedCategoryProvider);

  if (selectedCategory == null || selectedCategory.isEmpty) {
    return notes;
  }

  return notes.where((note) => note.category == selectedCategory).toList();
});

// Search provider
final searchQueryProvider = StateProvider<String>((ref) => '');

final searchResultsProvider = Provider<List<TextNote>>((ref) {
  final notes = ref.watch(filteredNotesProvider);
  final query = ref.watch(searchQueryProvider);

  if (query.isEmpty) return notes;

  return notes.where((note) => note.matchesSearch(query)).toList();
});

// Notes statistics provider
final notesStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final allNotes = ref.watch(notesProvider);
  final activeNotes = ref.watch(activeNotesProvider);
  final pinnedNotes = ref.watch(pinnedNotesProvider);
  final favoriteNotes = ref.watch(favoriteNotesProvider);
  final archivedNotes = ref.watch(archivedNotesProvider);
  
  final categories = <String, int>{};
  for (final note in activeNotes) {
    categories[note.category] = (categories[note.category] ?? 0) + 1;
  }
  
  final totalWords = activeNotes.fold<int>(0, (sum, note) => sum + note.wordCount);
  final totalCharacters = activeNotes.fold<int>(0, (sum, note) => sum + note.characterCount);
  
  return {
    'total': allNotes.length,
    'active': activeNotes.length,
    'pinned': pinnedNotes.length,
    'favorites': favoriteNotes.length,
    'archived': archivedNotes.length,
    'categories': categories,
    'totalWords': totalWords,
    'totalCharacters': totalCharacters,
    'averageWordsPerNote': activeNotes.isNotEmpty ? (totalWords / activeNotes.length).round() : 0,
  };
});

// Note templates provider
final noteTemplatesProvider = StateNotifierProvider<NoteTemplatesNotifier, List<NoteTemplate>>((ref) {
  return NoteTemplatesNotifier();
});

class NotesNotifier extends StateNotifier<List<TextNote>> {
  NotesNotifier() : super([]) {
    loadNotes();
  }

  Future<void> loadNotes() async {
    try {
      // For now, start with empty list - will implement database methods later
      state = [];
    } catch (e) {
      debugPrint('Error loading notes: $e');
    }
  }

  Future<void> addNote(TextNote note) async {
    try {
      // For now, just add to state - will implement database save later
      state = [...state, note];
    } catch (e) {
      debugPrint('Error adding note: $e');
      rethrow;
    }
  }

  Future<void> updateNote(TextNote note) async {
    try {
      // For now, just update in state - will implement database update later
      state = state.map((n) => n.id == note.id ? note : n).toList();
    } catch (e) {
      debugPrint('Error updating note: $e');
      rethrow;
    }
  }

  Future<void> deleteNote(int noteId) async {
    try {
      // For now, just remove from state - will implement database delete later
      state = state.where((note) => note.id != noteId).toList();
    } catch (e) {
      debugPrint('Error deleting note: $e');
      rethrow;
    }
  }

  Future<void> togglePin(int noteId) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.togglePin();
    await updateNote(note);
  }

  Future<void> toggleArchive(int noteId) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.toggleArchive();
    await updateNote(note);
  }

  Future<void> toggleFavorite(int noteId) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.toggleFavorite();
    await updateNote(note);
  }

  Future<void> duplicateNote(int noteId) async {
    final originalNote = state.firstWhere((n) => n.id == noteId);
    final duplicatedNote = TextNote.create(
      title: '${originalNote.title} (Copy)',
      content: originalNote.content,
      category: originalNote.category,
      userId: originalNote.userId,
      tags: originalNote.tags,
      color: originalNote.color,
      formattingData: originalNote.formattingData,
    );
    await addNote(duplicatedNote);
  }

  Future<void> moveToCategory(int noteId, String category) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.category = category;
    note.updatedAt = DateTime.now();
    await updateNote(note);
  }

  Future<void> addTagToNote(int noteId, String tag) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.addTag(tag);
    await updateNote(note);
  }

  Future<void> removeTagFromNote(int noteId, String tag) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.removeTag(tag);
    await updateNote(note);
  }

  Future<void> setNoteColor(int noteId, String color) async {
    final note = state.firstWhere((n) => n.id == noteId);
    note.color = color;
    note.updatedAt = DateTime.now();
    await updateNote(note);
  }

  Future<void> bulkDelete(List<int> noteIds) async {
    try {
      // For now, just remove from state - will implement database delete later
      state = state.where((note) => !noteIds.contains(note.id)).toList();
    } catch (e) {
      debugPrint('Error bulk deleting notes: $e');
      rethrow;
    }
  }

  Future<void> bulkArchive(List<int> noteIds) async {
    try {
      state = state.map((note) {
        if (noteIds.contains(note.id)) {
          note.isArchived = true;
          note.updatedAt = DateTime.now();
        }
        return note;
      }).toList();
    } catch (e) {
      debugPrint('Error bulk archiving notes: $e');
      rethrow;
    }
  }

  Future<void> bulkMoveToCategory(List<int> noteIds, String category) async {
    try {
      state = state.map((note) {
        if (noteIds.contains(note.id)) {
          note.category = category;
          note.updatedAt = DateTime.now();
        }
        return note;
      }).toList();
    } catch (e) {
      debugPrint('Error bulk moving notes: $e');
      rethrow;
    }
  }

  List<String> getAllTags() {
    final allTags = <String>{};
    for (final note in state) {
      allTags.addAll(note.getTagsList());
    }
    return allTags.toList()..sort();
  }

  List<String> getAllCategories() {
    final categories = state.map((note) => note.category).toSet().toList();
    categories.sort();
    return categories;
  }

  Future<String> exportNotes(List<int> noteIds, String format) async {
    final notesToExport = state.where((note) => noteIds.contains(note.id)).toList();

    switch (format.toLowerCase()) {
      case 'txt':
        return _exportAsText(notesToExport);
      case 'md':
        return _exportAsMarkdown(notesToExport);
      case 'json':
        return _exportAsJson(notesToExport);
      default:
        throw ArgumentError('Unsupported export format: $format');
    }
  }

  String _exportAsText(List<TextNote> notes) {
    final buffer = StringBuffer();
    for (final note in notes) {
      buffer.writeln('Title: ${note.title}');
      buffer.writeln('Category: ${note.category}');
      buffer.writeln('Created: ${note.formattedCreatedAt}');
      buffer.writeln('Tags: ${note.getTagsList().join(', ')}');
      buffer.writeln('---');
      buffer.writeln(note.content);
      buffer.writeln('\n${'=' * 50}\n');
    }
    return buffer.toString();
  }

  String _exportAsMarkdown(List<TextNote> notes) {
    final buffer = StringBuffer();
    for (final note in notes) {
      buffer.writeln('# ${note.title}');
      buffer.writeln('**Category:** ${note.category}');
      buffer.writeln('**Created:** ${note.formattedCreatedAt}');
      if (note.getTagsList().isNotEmpty) {
        buffer.writeln('**Tags:** ${note.getTagsList().map((tag) => '#$tag').join(' ')}');
      }
      buffer.writeln();
      buffer.writeln(note.content);
      buffer.writeln('\n---\n');
    }
    return buffer.toString();
  }

  String _exportAsJson(List<TextNote> notes) {
    final notesData = notes.map((note) => {
      'id': note.id,
      'title': note.title,
      'content': note.content,
      'category': note.category,
      'createdAt': note.createdAt.toIso8601String(),
      'updatedAt': note.updatedAt.toIso8601String(),
      'tags': note.getTagsList(),
      'isPinned': note.isPinned,
      'isFavorite': note.isFavorite,
      'color': note.color,
      'wordCount': note.wordCount,
      'characterCount': note.characterCount,
    }).toList();

    // In a real implementation, this would use dart:convert
    return notesData.toString();
  }

  Future<void> importNotes(String content, String format) async {
    // TODO: Implement note import functionality based on format
    debugPrint('Importing notes from $format format');
  }
}

class NoteTemplatesNotifier extends StateNotifier<List<NoteTemplate>> {
  NoteTemplatesNotifier() : super([]) {
    loadTemplates();
  }

  Future<void> loadTemplates() async {
    try {
      // For now, start with empty list - will implement database methods later
      state = [];
    } catch (e) {
      debugPrint('Error loading note templates: $e');
    }
  }

  Future<void> addTemplate(NoteTemplate template) async {
    try {
      // For now, just add to state - will implement database save later
      state = [...state, template];
    } catch (e) {
      debugPrint('Error adding note template: $e');
      rethrow;
    }
  }

  Future<void> updateTemplate(NoteTemplate template) async {
    try {
      // For now, just update in state - will implement database update later
      state = state.map((t) => t.id == template.id ? template : t).toList();
    } catch (e) {
      debugPrint('Error updating note template: $e');
      rethrow;
    }
  }

  Future<void> deleteTemplate(int templateId) async {
    try {
      // For now, just remove from state - will implement database delete later
      state = state.where((template) => template.id != templateId).toList();
    } catch (e) {
      debugPrint('Error deleting note template: $e');
      rethrow;
    }
  }
}
