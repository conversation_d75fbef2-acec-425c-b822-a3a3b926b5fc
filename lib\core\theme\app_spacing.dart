import 'package:flutter/material.dart';

class AppSpacing {
  // Base spacing unit (8dp)
  static const double _unit = 8.0;

  // Spacing values
  static const double xs = _unit * 0.5; // 4dp
  static const double sm = _unit; // 8dp
  static const double md = _unit * 2; // 16dp
  static const double lg = _unit * 3; // 24dp
  static const double xl = _unit * 4; // 32dp
  static const double xxl = _unit * 6; // 48dp
  static const double xxxl = _unit * 8; // 64dp

  // Padding
  static const EdgeInsets paddingXS = EdgeInsets.all(xs);
  static const EdgeInsets paddingSM = EdgeInsets.all(sm);
  static const EdgeInsets paddingMD = EdgeInsets.all(md);
  static const EdgeInsets paddingLG = EdgeInsets.all(lg);
  static const EdgeInsets paddingXL = EdgeInsets.all(xl);
  static const EdgeInsets paddingXXL = EdgeInsets.all(xxl);

  // Horizontal padding
  static const EdgeInsets horizontalXS = EdgeInsets.symmetric(horizontal: xs);
  static const EdgeInsets horizontalSM = EdgeInsets.symmetric(horizontal: sm);
  static const EdgeInsets horizontalMD = EdgeInsets.symmetric(horizontal: md);
  static const EdgeInsets horizontalLG = EdgeInsets.symmetric(horizontal: lg);
  static const EdgeInsets horizontalXL = EdgeInsets.symmetric(horizontal: xl);

  // Vertical padding
  static const EdgeInsets verticalXS = EdgeInsets.symmetric(vertical: xs);
  static const EdgeInsets verticalSM = EdgeInsets.symmetric(vertical: sm);
  static const EdgeInsets verticalMD = EdgeInsets.symmetric(vertical: md);
  static const EdgeInsets verticalLG = EdgeInsets.symmetric(vertical: lg);
  static const EdgeInsets verticalXL = EdgeInsets.symmetric(vertical: xl);

  // Screen padding
  static const EdgeInsets screenPadding = EdgeInsets.all(md);
  static const EdgeInsets screenPaddingHorizontal = EdgeInsets.symmetric(horizontal: md);
  static const EdgeInsets screenPaddingVertical = EdgeInsets.symmetric(vertical: md);

  // Card padding
  static const EdgeInsets cardPadding = EdgeInsets.all(md);
  static const EdgeInsets cardPaddingLarge = EdgeInsets.all(lg);

  // List item padding
  static const EdgeInsets listItemPadding = EdgeInsets.symmetric(horizontal: md, vertical: sm);
  static const EdgeInsets listItemPaddingLarge = EdgeInsets.symmetric(horizontal: md, vertical: md);

  // SizedBox spacing
  static const SizedBox spaceXS = SizedBox(height: xs, width: xs);
  static const SizedBox spaceSM = SizedBox(height: sm, width: sm);
  static const SizedBox spaceMD = SizedBox(height: md, width: md);
  static const SizedBox spaceLG = SizedBox(height: lg, width: lg);
  static const SizedBox spaceXL = SizedBox(height: xl, width: xl);
  static const SizedBox spaceXXL = SizedBox(height: xxl, width: xxl);

  // Horizontal spacing
  static const SizedBox hSpaceXS = SizedBox(width: xs);
  static const SizedBox hSpaceSM = SizedBox(width: sm);
  static const SizedBox hSpaceMD = SizedBox(width: md);
  static const SizedBox hSpaceLG = SizedBox(width: lg);
  static const SizedBox hSpaceXL = SizedBox(width: xl);

  // Vertical spacing
  static const SizedBox vSpaceXS = SizedBox(height: xs);
  static const SizedBox vSpaceSM = SizedBox(height: sm);
  static const SizedBox vSpaceMD = SizedBox(height: md);
  static const SizedBox vSpaceLG = SizedBox(height: lg);
  static const SizedBox vSpaceXL = SizedBox(height: xl);
  static const SizedBox vSpaceXXL = SizedBox(height: xxl);

  // Border radius
  static const double radiusXS = 4.0;
  static const double radiusSM = 8.0;
  static const double radiusMD = 12.0;
  static const double radiusLG = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusXXL = 24.0;
  static const double radiusCircle = 999.0;

  // BorderRadius
  static const BorderRadius borderRadiusXS = BorderRadius.all(Radius.circular(radiusXS));
  static const BorderRadius borderRadiusSM = BorderRadius.all(Radius.circular(radiusSM));
  static const BorderRadius borderRadiusMD = BorderRadius.all(Radius.circular(radiusMD));
  static const BorderRadius borderRadiusLG = BorderRadius.all(Radius.circular(radiusLG));
  static const BorderRadius borderRadiusXL = BorderRadius.all(Radius.circular(radiusXL));
  static const BorderRadius borderRadiusXXL = BorderRadius.all(Radius.circular(radiusXXL));

  // Elevation
  static const double elevationNone = 0.0;
  static const double elevationXS = 1.0;
  static const double elevationSM = 2.0;
  static const double elevationMD = 4.0;
  static const double elevationLG = 8.0;
  static const double elevationXL = 12.0;
  static const double elevationXXL = 16.0;

  // Icon sizes
  static const double iconXS = 16.0;
  static const double iconSM = 20.0;
  static const double iconMD = 24.0;
  static const double iconLG = 32.0;
  static const double iconXL = 48.0;
  static const double iconXXL = 64.0;

  // Button heights
  static const double buttonHeightSM = 32.0;
  static const double buttonHeightMD = 40.0;
  static const double buttonHeightLG = 48.0;
  static const double buttonHeightXL = 56.0;

  // App bar height
  static const double appBarHeight = 56.0;
  static const double appBarHeightLarge = 64.0;

  // Bottom navigation height
  static const double bottomNavHeight = 80.0;

  // Floating action button size
  static const double fabSize = 56.0;
  static const double fabSizeSmall = 40.0;
  static const double fabSizeLarge = 64.0;

  // Helper methods
  static EdgeInsets symmetric({double? horizontal, double? vertical}) {
    return EdgeInsets.symmetric(
      horizontal: horizontal ?? 0,
      vertical: vertical ?? 0,
    );
  }

  static EdgeInsets onlyPadding({
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return EdgeInsets.only(
      left: left ?? 0,
      top: top ?? 0,
      right: right ?? 0,
      bottom: bottom ?? 0,
    );
  }

  static SizedBox height(double height) => SizedBox(height: height);
  static SizedBox width(double width) => SizedBox(width: width);

  static BorderRadius circular(double radius) => BorderRadius.circular(radius);

  static BorderRadius only({
    double? topLeft,
    double? topRight,
    double? bottomLeft,
    double? bottomRight,
  }) {
    return BorderRadius.only(
      topLeft: Radius.circular(topLeft ?? 0),
      topRight: Radius.circular(topRight ?? 0),
      bottomLeft: Radius.circular(bottomLeft ?? 0),
      bottomRight: Radius.circular(bottomRight ?? 0),
    );
  }

  // Responsive spacing based on screen size
  static double responsiveSpacing(BuildContext context, {
    double mobile = md,
    double tablet = lg,
    double desktop = xl,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return mobile;
    } else if (screenWidth < 1200) {
      return tablet;
    } else {
      return desktop;
    }
  }

  static EdgeInsets responsivePadding(BuildContext context, {
    EdgeInsets mobile = paddingMD,
    EdgeInsets tablet = paddingLG,
    EdgeInsets desktop = paddingXL,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return mobile;
    } else if (screenWidth < 1200) {
      return tablet;
    } else {
      return desktop;
    }
  }

  // Grid spacing
  static double gridSpacing(BuildContext context) {
    return responsiveSpacing(context, mobile: sm, tablet: md, desktop: lg);
  }

  // Safe area padding
  static EdgeInsets safeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: mediaQuery.padding.left,
      right: mediaQuery.padding.right,
    );
  }

  // Keyboard padding
  static EdgeInsets keyboardPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(bottom: mediaQuery.viewInsets.bottom);
  }
}

// Layout breakpoints
class AppBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1200;
  static const double desktop = 1920;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < tablet;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tablet;
  }

  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }

  static bool isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= tablet;
  }
}

// Layout helpers
class AppLayout {
  static Widget responsive({
    required BuildContext context,
    Widget? mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    if (AppBreakpoints.isDesktop(context) && desktop != null) {
      return desktop;
    } else if (AppBreakpoints.isTablet(context) && tablet != null) {
      return tablet;
    } else {
      return mobile ?? const SizedBox.shrink();
    }
  }

  static int responsiveColumns(BuildContext context, {
    int mobile = 1,
    int tablet = 2,
    int desktop = 3,
  }) {
    if (AppBreakpoints.isDesktop(context)) {
      return desktop;
    } else if (AppBreakpoints.isTablet(context)) {
      return tablet;
    } else {
      return mobile;
    }
  }

  static double responsiveWidth(BuildContext context, {
    double mobile = 1.0,
    double tablet = 0.8,
    double desktop = 0.6,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (AppBreakpoints.isDesktop(context)) {
      return screenWidth * desktop;
    } else if (AppBreakpoints.isTablet(context)) {
      return screenWidth * tablet;
    } else {
      return screenWidth * mobile;
    }
  }
}
