import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note.dart';
import '../providers/notes_provider.dart';
import '../widgets/note_card.dart';
import '../widgets/notes_search_bar.dart';
import '../widgets/category_filter_chips.dart';
import 'note_editor_screen.dart';

class NotesScreen extends ConsumerStatefulWidget {
  const NotesScreen({super.key});

  @override
  ConsumerState<NotesScreen> createState() => _NotesScreenState();
}

class _NotesScreenState extends ConsumerState<NotesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isSelectionMode = false;
  final Set<int> _selectedNotes = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final stats = ref.watch(notesStatsProvider);
    
    return Scaffold(
      appBar: _buildAppBar(stats),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllNotesTab(),
                _buildPinnedNotesTab(),
                _buildFavoritesTab(),
                _buildArchivedNotesTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar(Map<String, dynamic> stats) {
    if (_isSelectionMode) {
      return AppBar(
        title: Text('${_selectedNotes.length} selected'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: _exitSelectionMode,
          icon: const Icon(Icons.close),
        ),
        actions: [
          IconButton(
            onPressed: _selectedNotes.isNotEmpty ? _bulkPin : null,
            icon: const Icon(Icons.push_pin),
            tooltip: 'Pin Selected',
          ),
          IconButton(
            onPressed: _selectedNotes.isNotEmpty ? _bulkArchive : null,
            icon: const Icon(Icons.archive),
            tooltip: 'Archive Selected',
          ),
          IconButton(
            onPressed: _selectedNotes.isNotEmpty ? _bulkDelete : null,
            icon: const Icon(Icons.delete),
            tooltip: 'Delete Selected',
          ),
          PopupMenuButton<String>(
            onSelected: _handleBulkAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export Selected'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'move_category',
                child: Row(
                  children: [
                    Icon(Icons.folder),
                    SizedBox(width: 8),
                    Text('Move to Category'),
                  ],
                ),
              ),
            ],
          ),
        ],
      );
    }

    return AppBar(
      title: const Text('Notes'),
      backgroundColor: Colors.deepPurple,
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          onPressed: _showStatsDialog,
          icon: const Icon(Icons.analytics),
          tooltip: 'Statistics',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'templates',
              child: Row(
                children: [
                  Icon(Icons.description_outlined),
                  SizedBox(width: 8),
                  Text('Templates'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: Row(
                children: [
                  Icon(Icons.upload),
                  SizedBox(width: 8),
                  Text('Import Notes'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export_all',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('Export All'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings),
                  SizedBox(width: 8),
                  Text('Settings'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: const Column(
        children: [
          NotesSearchBar(),
          SizedBox(height: 12),
          CategoryFilterChips(),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    final stats = ref.watch(notesStatsProvider);
    
    return TabBar(
      controller: _tabController,
      labelColor: Colors.deepPurple,
      unselectedLabelColor: Colors.grey,
      indicatorColor: Colors.deepPurple,
      tabs: [
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.note, size: 16),
              const SizedBox(width: 4),
              Text('All (${stats['active']})'),
            ],
          ),
        ),
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.push_pin, size: 16),
              const SizedBox(width: 4),
              Text('Pinned (${stats['pinned']})'),
            ],
          ),
        ),
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.favorite, size: 16),
              const SizedBox(width: 4),
              Text('Favorites (${stats['favorites']})'),
            ],
          ),
        ),
        Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.archive, size: 16),
              const SizedBox(width: 4),
              Text('Archived (${stats['archived']})'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAllNotesTab() {
    final notes = ref.watch(searchResultsProvider);
    return _buildNotesList(notes);
  }

  Widget _buildPinnedNotesTab() {
    final notes = ref.watch(pinnedNotesProvider);
    return _buildNotesList(notes);
  }

  Widget _buildFavoritesTab() {
    final notes = ref.watch(favoriteNotesProvider);
    return _buildNotesList(notes);
  }

  Widget _buildArchivedNotesTab() {
    final notes = ref.watch(archivedNotesProvider);
    return _buildNotesList(notes);
  }

  Widget _buildNotesList(List<TextNote> notes) {
    if (notes.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notes.length,
      itemBuilder: (context, index) {
        final note = notes[index];
        return NoteCard(
          note: note,
          isSelected: _selectedNotes.contains(note.id),
          isSelectionMode: _isSelectionMode,
          onTap: () => _handleNoteTap(note),
          onLongPress: () => _handleNoteLongPress(note),
          onSelectionChanged: (selected) => _handleNoteSelection(note.id, selected),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.note_add,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No notes yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to create your first note',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _createNewNote,
      backgroundColor: Colors.deepPurple,
      foregroundColor: Colors.white,
      child: const Icon(Icons.add),
    );
  }

  void _handleNoteTap(TextNote note) {
    if (_isSelectionMode) {
      _handleNoteSelection(note.id, !_selectedNotes.contains(note.id));
    } else {
      _openNoteEditor(note);
    }
  }

  void _handleNoteLongPress(TextNote note) {
    if (!_isSelectionMode) {
      setState(() {
        _isSelectionMode = true;
        _selectedNotes.add(note.id);
      });
    }
  }

  void _handleNoteSelection(int noteId, bool selected) {
    setState(() {
      if (selected) {
        _selectedNotes.add(noteId);
      } else {
        _selectedNotes.remove(noteId);
      }
      
      if (_selectedNotes.isEmpty) {
        _isSelectionMode = false;
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedNotes.clear();
    });
  }

  void _createNewNote() {
    final newNote = TextNote.create(
      title: '',
      content: '',
      category: NoteCategory.personal.displayName,
      userId: 'current_user', // TODO: Get from auth service
    );
    
    _openNoteEditor(newNote, isNew: true);
  }

  void _openNoteEditor(TextNote note, {bool isNew = false}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => NoteEditorScreen(
          note: note,
          isNew: isNew,
        ),
      ),
    );
  }

  void _bulkPin() async {
    try {
      for (final noteId in _selectedNotes) {
        await ref.read(notesProvider.notifier).togglePin(noteId);
      }
      _exitSelectionMode();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Notes pinned')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error pinning notes: $e')),
        );
      }
    }
  }

  void _bulkArchive() async {
    try {
      await ref.read(notesProvider.notifier).bulkArchive(_selectedNotes.toList());
      _exitSelectionMode();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Notes archived')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error archiving notes: $e')),
        );
      }
    }
  }

  void _bulkDelete() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notes'),
        content: Text('Are you sure you want to delete ${_selectedNotes.length} notes?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(notesProvider.notifier).bulkDelete(_selectedNotes.toList());
        _exitSelectionMode();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Notes deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting notes: $e')),
          );
        }
      }
    }
  }

  void _handleBulkAction(String action) {
    switch (action) {
      case 'export':
        _exportSelectedNotes();
        break;
      case 'move_category':
        _showMoveToCategoryDialog();
        break;
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'templates':
        _showTemplatesDialog();
        break;
      case 'import':
        _importNotes();
        break;
      case 'export_all':
        _exportAllNotes();
        break;
      case 'settings':
        _showSettingsDialog();
        break;
    }
  }

  void _showStatsDialog() {
    final stats = ref.read(notesStatsProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notes Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Notes: ${stats['total']}'),
            Text('Active Notes: ${stats['active']}'),
            Text('Pinned Notes: ${stats['pinned']}'),
            Text('Favorite Notes: ${stats['favorites']}'),
            Text('Archived Notes: ${stats['archived']}'),
            const SizedBox(height: 16),
            Text('Total Words: ${stats['totalWords']}'),
            Text('Total Characters: ${stats['totalCharacters']}'),
            Text('Average Words per Note: ${stats['averageWordsPerNote']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportSelectedNotes() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export feature coming soon!')),
    );
  }

  void _showMoveToCategoryDialog() {
    // TODO: Implement move to category dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Move to category feature coming soon!')),
    );
  }

  void _showTemplatesDialog() {
    // TODO: Implement templates dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Templates feature coming soon!')),
    );
  }

  void _importNotes() {
    // TODO: Implement import functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import feature coming soon!')),
    );
  }

  void _exportAllNotes() {
    // TODO: Implement export all functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export all feature coming soon!')),
    );
  }

  void _showSettingsDialog() {
    // TODO: Implement settings dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings feature coming soon!')),
    );
  }
}
