import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/transaction.dart';
import '../models/category.dart';
import '../providers/transaction_provider.dart';
import '../providers/category_provider.dart';

/// Charts and visualizations screen
class ChartsScreen extends ConsumerStatefulWidget {
  const ChartsScreen({super.key});

  @override
  ConsumerState<ChartsScreen> createState() => _ChartsScreenState();
}

class _ChartsScreenState extends ConsumerState<ChartsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Default to last 30 days
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: now.subtract(const Duration(days: 30)),
      end: now,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StandardizedScaffold(
      title: 'Financial Charts',
      currentRoute: '/money-flow/charts',
      showBackButton: true,
      actions: [
        IconButton(
          onPressed: _selectDateRange,
          icon: const Icon(Icons.date_range),
          tooltip: 'Select Date Range',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.pie_chart), text: 'Categories'),
          Tab(icon: Icon(Icons.bar_chart), text: 'Monthly'),
          Tab(icon: Icon(Icons.show_chart), text: 'Trends'),
          Tab(icon: Icon(Icons.donut_small), text: 'Accounts'),
        ],
      ),
      body: Column(
        children: [
          // Date range display
          _buildDateRangeBar(),
          
          // Charts content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCategoryPieChart(),
                _buildMonthlyBarChart(),
                _buildTrendsLineChart(),
                _buildAccountsDonutChart(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build date range bar
  Widget _buildDateRangeBar() {
    return Container(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.date_range,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            _getDateRangeText(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('Change'),
          ),
        ],
      ),
    );
  }

  /// Build category pie chart
  Widget _buildCategoryPieChart() {
    final transactions = _getFilteredTransactions();
    final categoryData = _getCategoryChartData(transactions);
    
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Expenses by Category',
            subtitle: 'Distribution of spending across categories',
          ),
          
          if (categoryData.isEmpty)
            EmptyState(
              icon: Icons.pie_chart,
              title: 'No Data Available',
              message: 'No expense transactions found for the selected period',
            )
          else
            StandardCard(
              child: Column(
                children: [
                  // Pie chart
                  SizedBox(
                    height: 300,
                    child: PieChart(
                      PieChartData(
                        sections: categoryData.map((data) {
                          return PieChartSectionData(
                            value: data['amount'],
                            title: '${data['percentage'].toStringAsFixed(1)}%',
                            color: data['color'],
                            radius: 100,
                            titleStyle: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          );
                        }).toList(),
                        sectionsSpace: 2,
                        centerSpaceRadius: 40,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Legend
                  Wrap(
                    spacing: 16,
                    runSpacing: 8,
                    children: categoryData.map((data) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: data['color'],
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${data['name']}: \$${data['amount'].toStringAsFixed(0)}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build monthly bar chart
  Widget _buildMonthlyBarChart() {
    final transactions = _getFilteredTransactions();
    final monthlyData = _getMonthlyChartData(transactions);
    
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Monthly Income vs Expenses',
            subtitle: 'Compare income and expenses by month',
          ),
          
          StandardCard(
            child: Container(
              height: 300,
              padding: const EdgeInsets.all(16),
              child: monthlyData.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.bar_chart,
                            size: 48,
                            color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No Data Available',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    )
                  : BarChart(
                      BarChartData(
                        alignment: BarChartAlignment.spaceAround,
                        maxY: _getMaxValue(monthlyData) * 1.2,
                        barTouchData: BarTouchData(
                          touchTooltipData: BarTouchTooltipData(
                            getTooltipColor: (group) => Colors.blueGrey,
                            getTooltipItem: (group, groupIndex, rod, rodIndex) {
                              final month = monthlyData[groupIndex]['month'];
                              final value = rod.toY;
                              final type = rodIndex == 0 ? 'Income' : 'Expenses';
                              return BarTooltipItem(
                                '$month\n$type: \$${value.toStringAsFixed(0)}',
                                const TextStyle(color: Colors.white),
                              );
                            },
                          ),
                        ),
                        titlesData: FlTitlesData(
                          show: true,
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                if (value.toInt() < monthlyData.length) {
                                  return Text(
                                    monthlyData[value.toInt()]['month'],
                                    style: const TextStyle(fontSize: 12),
                                  );
                                }
                                return const Text('');
                              },
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '\$${(value / 1000).toStringAsFixed(0)}k',
                                  style: const TextStyle(fontSize: 10),
                                );
                              },
                            ),
                          ),
                          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                        ),
                        borderData: FlBorderData(show: false),
                        barGroups: monthlyData.asMap().entries.map((entry) {
                          final index = entry.key;
                          final data = entry.value;
                          return BarChartGroupData(
                            x: index,
                            barRods: [
                              BarChartRodData(
                                toY: data['income'],
                                color: Colors.green,
                                width: 16,
                              ),
                              BarChartRodData(
                                toY: data['expenses'],
                                color: Colors.red,
                                width: 16,
                              ),
                            ],
                          );
                        }).toList(),
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build trends line chart
  Widget _buildTrendsLineChart() {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Spending Trends',
            subtitle: 'Track spending patterns over time',
          ),
          
          StandardCard(
            child: Container(
              height: 300,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.show_chart,
                      size: 64,
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Trend Analysis Coming Soon',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Advanced trend analysis with line charts will be implemented',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build accounts donut chart
  Widget _buildAccountsDonutChart() {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Account Distribution',
            subtitle: 'Transaction distribution across accounts',
          ),
          
          StandardCard(
            child: Container(
              height: 300,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.donut_small,
                      size: 64,
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Account Distribution Coming Soon',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Donut charts showing account-wise transaction distribution',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get filtered transactions
  List<Transaction> _getFilteredTransactions() {
    final allTransactions = ref.watch(transactionsProvider);
    
    if (_selectedDateRange == null) return allTransactions;
    
    return allTransactions.where((transaction) {
      final transactionDate = transaction.date;
      return transactionDate.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
             transactionDate.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
    }).toList();
  }

  /// Get category chart data
  List<Map<String, dynamic>> _getCategoryChartData(List<Transaction> transactions) {
    final Map<String, double> categoryTotals = {};
    final categories = ref.read(categoriesProvider);
    
    // Calculate totals for expense transactions only
    for (final transaction in transactions) {
      if (transaction.type == TransactionType.expense) {
        final category = categories.firstWhere(
          (cat) => cat.name == transaction.category,
          orElse: () => MoneyCategory()..name = 'Unknown',
        );
        
        categoryTotals[category.name] = (categoryTotals[category.name] ?? 0) + transaction.amount;
      }
    }
    
    if (categoryTotals.isEmpty) return [];
    
    final total = categoryTotals.values.reduce((a, b) => a + b);
    final colors = [
      Colors.blue, Colors.green, Colors.orange, Colors.purple,
      Colors.red, Colors.teal, Colors.amber, Colors.indigo,
      Colors.pink, Colors.cyan, Colors.lime, Colors.brown,
    ];
    
    return categoryTotals.entries.map((entry) {
      final index = categoryTotals.keys.toList().indexOf(entry.key);
      return {
        'name': entry.key,
        'amount': entry.value,
        'percentage': (entry.value / total) * 100,
        'color': colors[index % colors.length],
      };
    }).toList();
  }

  /// Get monthly chart data
  List<Map<String, dynamic>> _getMonthlyChartData(List<Transaction> transactions) {
    final Map<String, Map<String, dynamic>> monthlyTotals = {};

    for (final transaction in transactions) {
      final monthKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      final monthName = _getMonthName(transaction.date.month);

      if (!monthlyTotals.containsKey(monthKey)) {
        monthlyTotals[monthKey] = {
          'month': monthName,
          'income': 0.0,
          'expenses': 0.0,
        };
      }
      
      if (transaction.type == TransactionType.income) {
        monthlyTotals[monthKey]!['income'] = monthlyTotals[monthKey]!['income']! + transaction.amount;
      } else if (transaction.type == TransactionType.expense) {
        monthlyTotals[monthKey]!['expenses'] = monthlyTotals[monthKey]!['expenses']! + transaction.amount;
      }
    }
    
    // Sort by month and return
    final sortedKeys = monthlyTotals.keys.toList()..sort();
    return sortedKeys.map((key) => monthlyTotals[key]!).toList();
  }

  /// Get maximum value for bar chart scaling
  double _getMaxValue(List<Map<String, dynamic>> monthlyData) {
    double max = 0;
    for (final data in monthlyData) {
      final income = data['income'] as double;
      final expenses = data['expenses'] as double;
      if (income > max) max = income;
      if (expenses > max) max = expenses;
    }
    return max;
  }

  /// Get month name from number
  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  /// Get date range text
  String _getDateRangeText() {
    if (_selectedDateRange == null) return 'All time';
    
    final start = _selectedDateRange!.start;
    final end = _selectedDateRange!.end;
    
    return '${start.day}/${start.month}/${start.year} - ${end.day}/${end.month}/${end.year}';
  }

  /// Select date range
  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }
}
