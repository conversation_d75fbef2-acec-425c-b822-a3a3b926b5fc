import 'todo.dart';

class TodoAnalytics {
  final List<Todo> todos;
  final DateTime startDate;
  final DateTime endDate;

  TodoAnalytics({
    required this.todos,
    required this.startDate,
    required this.endDate,
  });

  // Completion metrics
  int get totalTodos => todos.length;
  int get completedTodos => todos.where((t) => t.isCompleted).length;
  int get pendingTodos => todos.where((t) => t.status == TodoStatus.pending).length;
  int get inProgressTodos => todos.where((t) => t.status == TodoStatus.inProgress).length;
  int get overdueTodos => todos.where((t) => t.isOverdue).length;
  int get cancelledTodos => todos.where((t) => t.status == TodoStatus.cancelled).length;

  double get completionRate => totalTodos > 0 ? completedTodos / totalTodos : 0.0;
  double get overdueRate => totalTodos > 0 ? overdueTodos / totalTodos : 0.0;

  // Priority distribution
  Map<TodoPriority, int> get priorityDistribution {
    final distribution = <TodoPriority, int>{};
    for (final priority in TodoPriority.values) {
      distribution[priority] = todos.where((t) => t.priority == priority).length;
    }
    return distribution;
  }

  // Category distribution
  Map<String, int> get categoryDistribution {
    final distribution = <String, int>{};
    for (final todo in todos) {
      distribution[todo.category] = (distribution[todo.category] ?? 0) + 1;
    }
    return distribution;
  }

  // Time tracking
  int get totalEstimatedMinutes => todos.fold(0, (sum, t) => sum + t.estimatedMinutes);
  int get totalActualMinutes => todos.fold(0, (sum, t) => sum + t.actualMinutes);
  
  double get averageTimeEfficiency {
    final todosWithTime = todos.where((t) => t.estimatedMinutes > 0 && t.actualMinutes > 0);
    if (todosWithTime.isEmpty) return 1.0;
    
    final totalEfficiency = todosWithTime.fold(0.0, (sum, t) => sum + t.timeEfficiency);
    return totalEfficiency / todosWithTime.length;
  }

  // Productivity trends
  Map<DateTime, int> get dailyCompletions {
    final completions = <DateTime, int>{};
    final completedTodos = todos.where((t) => t.completedAt != null);
    
    for (final todo in completedTodos) {
      final date = DateTime(
        todo.completedAt!.year,
        todo.completedAt!.month,
        todo.completedAt!.day,
      );
      completions[date] = (completions[date] ?? 0) + 1;
    }
    return completions;
  }

  Map<int, int> get weeklyCompletions {
    final completions = <int, int>{};
    final completedTodos = todos.where((t) => t.completedAt != null);
    
    for (final todo in completedTodos) {
      final weekOfYear = _getWeekOfYear(todo.completedAt!);
      completions[weekOfYear] = (completions[weekOfYear] ?? 0) + 1;
    }
    return completions;
  }

  // Streak tracking
  int get currentStreak {
    final today = DateTime.now();
    final dailyCompletions = this.dailyCompletions;
    int streak = 0;
    
    for (int i = 0; i < 365; i++) {
      final date = DateTime(today.year, today.month, today.day - i);
      if (dailyCompletions.containsKey(date) && dailyCompletions[date]! > 0) {
        streak++;
      } else {
        break;
      }
    }
    return streak;
  }

  int get longestStreak {
    final dailyCompletions = this.dailyCompletions;
    final sortedDates = dailyCompletions.keys.toList()..sort();
    
    int maxStreak = 0;
    int currentStreak = 0;
    DateTime? lastDate;
    
    for (final date in sortedDates) {
      if (dailyCompletions[date]! > 0) {
        if (lastDate == null || date.difference(lastDate).inDays == 1) {
          currentStreak++;
        } else {
          currentStreak = 1;
        }
        maxStreak = maxStreak > currentStreak ? maxStreak : currentStreak;
        lastDate = date;
      }
    }
    return maxStreak;
  }

  // Performance insights
  List<String> get insights {
    final insights = <String>[];
    
    // Completion rate insights
    if (completionRate >= 0.8) {
      insights.add('Excellent completion rate! You\'re very productive.');
    } else if (completionRate >= 0.6) {
      insights.add('Good completion rate. Consider breaking down larger tasks.');
    } else if (completionRate >= 0.4) {
      insights.add('Room for improvement. Try setting more realistic goals.');
    } else {
      insights.add('Low completion rate. Consider reviewing your task planning.');
    }

    // Overdue insights
    if (overdueRate > 0.3) {
      insights.add('Many overdue tasks. Consider better time estimation.');
    } else if (overdueRate > 0.1) {
      insights.add('Some overdue tasks. Review your scheduling.');
    }

    // Time efficiency insights
    if (averageTimeEfficiency > 1.2) {
      insights.add('You often finish tasks faster than estimated!');
    } else if (averageTimeEfficiency < 0.8) {
      insights.add('Tasks often take longer than estimated. Adjust your planning.');
    }

    // Priority insights
    final highPriorityCount = priorityDistribution[TodoPriority.high] ?? 0;
    final urgentCount = priorityDistribution[TodoPriority.urgent] ?? 0;
    final totalHighPriority = highPriorityCount + urgentCount;
    
    if (totalHighPriority > totalTodos * 0.5) {
      insights.add('Too many high-priority tasks. Consider better prioritization.');
    }

    // Streak insights
    if (currentStreak >= 7) {
      insights.add('Amazing! You\'re on a ${currentStreak}-day completion streak!');
    } else if (currentStreak >= 3) {
      insights.add('Great momentum with a ${currentStreak}-day streak!');
    }

    return insights;
  }

  // Helper methods
  int _getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil();
  }

  // Productivity score (0-100)
  double get productivityScore {
    double score = 0;
    
    // Completion rate (40% weight)
    score += completionRate * 40;
    
    // Time efficiency (20% weight)
    final efficiencyScore = averageTimeEfficiency.clamp(0.5, 1.5);
    score += ((efficiencyScore - 0.5) / 1.0) * 20;
    
    // Overdue penalty (20% weight)
    score += (1 - overdueRate) * 20;
    
    // Streak bonus (20% weight)
    final streakScore = (currentStreak / 30).clamp(0.0, 1.0);
    score += streakScore * 20;
    
    return score.clamp(0.0, 100.0);
  }

  // Recommendations
  List<String> get recommendations {
    final recommendations = <String>[];
    
    if (completionRate < 0.6) {
      recommendations.add('Break down large tasks into smaller, manageable subtasks');
      recommendations.add('Set more realistic deadlines for your tasks');
    }
    
    if (overdueRate > 0.2) {
      recommendations.add('Review and adjust your time estimates');
      recommendations.add('Consider using time-blocking for better scheduling');
    }
    
    if (averageTimeEfficiency < 0.8) {
      recommendations.add('Add buffer time to your estimates');
      recommendations.add('Track time spent on similar tasks for better planning');
    }
    
    if (currentStreak == 0) {
      recommendations.add('Start with completing one small task today');
      recommendations.add('Set a daily goal to build momentum');
    }
    
    final highPriorityRatio = (priorityDistribution[TodoPriority.high] ?? 0) + 
                             (priorityDistribution[TodoPriority.urgent] ?? 0);
    if (highPriorityRatio > totalTodos * 0.5) {
      recommendations.add('Review your priorities - not everything can be urgent');
      recommendations.add('Use the Eisenhower Matrix for better prioritization');
    }
    
    return recommendations;
  }

  // Export data for charts/reports
  Map<String, dynamic> toChartData() {
    return {
      'completionRate': completionRate,
      'overdueRate': overdueRate,
      'productivityScore': productivityScore,
      'priorityDistribution': priorityDistribution.map((k, v) => MapEntry(k.name, v)),
      'categoryDistribution': categoryDistribution,
      'dailyCompletions': dailyCompletions.map((k, v) => MapEntry(k.toIso8601String(), v)),
      'weeklyCompletions': weeklyCompletions,
      'timeEfficiency': averageTimeEfficiency,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'totalTodos': totalTodos,
      'completedTodos': completedTodos,
      'pendingTodos': pendingTodos,
      'overdueTodos': overdueTodos,
    };
  }
}
