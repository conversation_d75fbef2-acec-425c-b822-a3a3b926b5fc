import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/athkar_display_preferences.dart';

// Athkar Display Preferences Provider
final athkarDisplayPreferencesProvider = StateNotifierProvider<AthkarDisplayPreferencesNotifier, AthkarDisplayPreferences?>((ref) {
  return AthkarDisplayPreferencesNotifier();
});

class AthkarDisplayPreferencesNotifier extends StateNotifier<AthkarDisplayPreferences?> {
  static const String _preferencesKey = 'athkar_display_preferences';
  
  AthkarDisplayPreferencesNotifier() : super(null) {
    loadPreferences();
  }

  Future<void> loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = prefs.getString(_preferencesKey);
      
      if (preferencesJson != null) {
        final preferencesMap = json.decode(preferencesJson) as Map<String, dynamic>;
        state = AthkarDisplayPreferences.fromJson(preferencesMap);
      } else {
        // Create default preferences
        state = AthkarDisplayPreferences.createDefault('default_user');
        await savePreferences();
      }
    } catch (e) {
      // If loading fails, create default preferences
      state = AthkarDisplayPreferences.createDefault('default_user');
      await savePreferences();
    }
  }

  Future<void> savePreferences() async {
    if (state == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = json.encode(state!.toJson());
      await prefs.setString(_preferencesKey, preferencesJson);
    } catch (e) {
      // Handle save error silently for now
    }
  }

  Future<void> updateDisplayMode(AthkarDisplayMode mode) async {
    if (state == null) return;
    
    state!.updatePreferences(displayMode: mode);
    state = state; // Trigger rebuild
    await savePreferences();
  }

  Future<void> updateLanguage(String languageCode) async {
    if (state == null) return;
    
    state!.updatePreferences(preferredLanguage: languageCode);
    state = state; // Trigger rebuild
    await savePreferences();
  }

  Future<void> updateFontSizes({
    double? arabicFontSize,
    double? transliterationFontSize,
    double? translationFontSize,
  }) async {
    if (state == null) return;
    
    state!.updatePreferences(
      arabicFontSize: arabicFontSize,
      transliterationFontSize: transliterationFontSize,
      translationFontSize: translationFontSize,
    );
    state = state; // Trigger rebuild
    await savePreferences();
  }

  Future<void> updateArabicFont(String fontFamily) async {
    if (state == null) return;
    
    state!.updatePreferences(arabicFontFamily: fontFamily);
    state = state; // Trigger rebuild
    await savePreferences();
  }

  Future<void> updateDisplayOptions({
    bool? showReference,
    bool? showRecommendedCount,
    bool? enableRightToLeft,
  }) async {
    if (state == null) return;
    
    state!.updatePreferences(
      showReference: showReference,
      showRecommendedCount: showRecommendedCount,
      enableRightToLeft: enableRightToLeft,
    );
    state = state; // Trigger rebuild
    await savePreferences();
  }

  Future<void> resetToDefaults() async {
    state = AthkarDisplayPreferences.createDefault('default_user');
    await savePreferences();
  }
}

// Helper providers for specific preference values
final displayModeProvider = Provider<AthkarDisplayMode>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.displayMode ?? AthkarDisplayMode.arabicWithBoth;
});

final preferredLanguageProvider = Provider<String>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.preferredLanguage ?? 'en';
});

final arabicFontSizeProvider = Provider<double>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.arabicFontSize ?? 24.0;
});

final transliterationFontSizeProvider = Provider<double>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.transliterationFontSize ?? 18.0;
});

final translationFontSizeProvider = Provider<double>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.translationFontSize ?? 16.0;
});

final arabicFontFamilyProvider = Provider<String>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.arabicFontFamily ?? 'default';
});

final showReferenceProvider = Provider<bool>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.showReference ?? true;
});

final showRecommendedCountProvider = Provider<bool>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.showRecommendedCount ?? true;
});

final enableRightToLeftProvider = Provider<bool>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.enableRightToLeft ?? true;
});

// Helper providers for display logic
final shouldShowArabicProvider = Provider<bool>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.shouldShowArabic ?? true;
});

final shouldShowTransliterationProvider = Provider<bool>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.shouldShowTransliteration ?? true;
});

final shouldShowTranslationProvider = Provider<bool>((ref) {
  final preferences = ref.watch(athkarDisplayPreferencesProvider);
  return preferences?.shouldShowTranslation ?? true;
});
