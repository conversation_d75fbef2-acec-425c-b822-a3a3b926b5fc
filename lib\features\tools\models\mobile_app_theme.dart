import 'package:flutter/material.dart';

/// Mobile app theme system for Tool Builder
class MobileAppTheme {
  final String id;
  final String name;
  final String description;
  final MobileAppThemeType type;
  final ColorScheme colorScheme;
  final TextTheme textTheme;
  final AppBarTheme appBarTheme;
  final BottomNavigationBarThemeData bottomNavTheme;
  final NavigationDrawerThemeData drawerTheme;
  final CardThemeData cardTheme;
  final ButtonThemeData buttonTheme;
  final InputDecorationTheme inputTheme;
  final Map<String, dynamic> customProperties;

  const MobileAppTheme({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.colorScheme,
    required this.textTheme,
    required this.appBarTheme,
    required this.bottomNavTheme,
    required this.drawerTheme,
    required this.cardTheme,
    required this.buttonTheme,
    required this.inputTheme,
    this.customProperties = const {},
  });

  /// Convert to Flutter ThemeData
  ThemeData toThemeData() {
    return ThemeData(
      colorScheme: colorScheme,
      textTheme: textTheme,
      appBarTheme: appBarTheme,
      bottomNavigationBarTheme: bottomNavTheme,
      navigationDrawerTheme: drawerTheme,
      cardTheme: cardTheme,
      buttonTheme: buttonTheme,
      inputDecorationTheme: inputTheme,
      useMaterial3: true,
    );
  }

  /// Create copy with modifications
  MobileAppTheme copyWith({
    String? id,
    String? name,
    String? description,
    MobileAppThemeType? type,
    ColorScheme? colorScheme,
    TextTheme? textTheme,
    AppBarTheme? appBarTheme,
    BottomNavigationBarThemeData? bottomNavTheme,
    NavigationDrawerThemeData? drawerTheme,
    CardThemeData? cardTheme,
    ButtonThemeData? buttonTheme,
    InputDecorationTheme? inputTheme,
    Map<String, dynamic>? customProperties,
  }) {
    return MobileAppTheme(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      colorScheme: colorScheme ?? this.colorScheme,
      textTheme: textTheme ?? this.textTheme,
      appBarTheme: appBarTheme ?? this.appBarTheme,
      bottomNavTheme: bottomNavTheme ?? this.bottomNavTheme,
      drawerTheme: drawerTheme ?? this.drawerTheme,
      cardTheme: cardTheme ?? this.cardTheme,
      buttonTheme: buttonTheme ?? this.buttonTheme,
      inputTheme: inputTheme ?? this.inputTheme,
      customProperties: customProperties ?? this.customProperties,
    );
  }
}

/// Types of mobile app themes
enum MobileAppThemeType {
  android,
  ios,
  web,
  custom,
}

/// Mobile app layout templates
enum MobileAppLayoutType {
  singlePage,
  tabbed,
  drawer,
  bottomNav,
  masterDetail,
  onboarding,
  dashboard,
  form,
  list,
  grid,
}

/// Mobile app component types for UI Builder
enum MobileComponentType {
  // Layout Components
  scaffold,
  appBar,
  drawer,
  bottomNavBar,
  tabBar,
  container,
  column,
  row,
  stack,
  
  // Navigation Components
  navigationRail,
  bottomSheet,
  modalBottomSheet,
  dialog,
  snackBar,
  
  // Input Components
  textField,
  dropdownButton,
  checkbox,
  radioButton,
  slider,
  switchWidget,
  datePicker,
  timePicker,
  
  // Display Components
  text,
  richText,
  image,
  icon,
  avatar,
  chip,
  badge,
  progressIndicator,
  
  // Interactive Components
  button,
  iconButton,
  floatingActionButton,
  card,
  listTile,
  expansionTile,
  
  // Data Components
  dataTable,
  chart,
  graph,
  
  // Custom Components
  formulaOutput,
  calculatorDisplay,
  spreadsheetCell,
}

/// Mobile app component data
class MobileComponentData {
  final String id;
  final MobileComponentType type;
  final String label;
  final Map<String, dynamic> properties;
  final Map<String, dynamic> style;
  final List<MobileComponentData> children;
  final String? cellBinding;
  final String? formula;

  const MobileComponentData({
    required this.id,
    required this.type,
    required this.label,
    this.properties = const {},
    this.style = const {},
    this.children = const [],
    this.cellBinding,
    this.formula,
  });

  MobileComponentData copyWith({
    String? id,
    MobileComponentType? type,
    String? label,
    Map<String, dynamic>? properties,
    Map<String, dynamic>? style,
    List<MobileComponentData>? children,
    String? cellBinding,
    String? formula,
  }) {
    return MobileComponentData(
      id: id ?? this.id,
      type: type ?? this.type,
      label: label ?? this.label,
      properties: properties ?? this.properties,
      style: style ?? this.style,
      children: children ?? this.children,
      cellBinding: cellBinding ?? this.cellBinding,
      formula: formula ?? this.formula,
    );
  }
}

/// Mobile app template
class MobileAppTemplate {
  final String id;
  final String name;
  final String description;
  final MobileAppLayoutType layoutType;
  final MobileAppThemeType themeType;
  final List<MobileComponentData> components;
  final Map<String, dynamic> configuration;
  final String previewImagePath;

  const MobileAppTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.layoutType,
    required this.themeType,
    required this.components,
    this.configuration = const {},
    this.previewImagePath = '',
  });
}

/// Mobile app preview configuration
class MobileAppPreview {
  final String deviceType; // 'android', 'ios', 'web'
  final Size screenSize;
  final double devicePixelRatio;
  final bool showStatusBar;
  final bool showNavigationBar;
  final EdgeInsets safeAreaInsets;

  const MobileAppPreview({
    required this.deviceType,
    required this.screenSize,
    this.devicePixelRatio = 2.0,
    this.showStatusBar = true,
    this.showNavigationBar = true,
    this.safeAreaInsets = const EdgeInsets.only(top: 44, bottom: 34),
  });

  /// Common device presets
  static const MobileAppPreview androidPhone = MobileAppPreview(
    deviceType: 'android',
    screenSize: Size(360, 640),
    devicePixelRatio: 3.0,
    safeAreaInsets: EdgeInsets.only(top: 24, bottom: 0),
  );

  static const MobileAppPreview iphone = MobileAppPreview(
    deviceType: 'ios',
    screenSize: Size(375, 667),
    devicePixelRatio: 2.0,
    safeAreaInsets: EdgeInsets.only(top: 44, bottom: 34),
  );

  static const MobileAppPreview tablet = MobileAppPreview(
    deviceType: 'android',
    screenSize: Size(768, 1024),
    devicePixelRatio: 2.0,
    safeAreaInsets: EdgeInsets.only(top: 24, bottom: 0),
  );

  static const MobileAppPreview desktop = MobileAppPreview(
    deviceType: 'web',
    screenSize: Size(1200, 800),
    devicePixelRatio: 1.0,
    showStatusBar: false,
    showNavigationBar: false,
    safeAreaInsets: EdgeInsets.zero,
  );
}
