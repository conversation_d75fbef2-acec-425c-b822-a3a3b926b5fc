import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/simple_spreadsheet_provider.dart';

/// Sheet tabs widget for multi-sheet support
class SheetTabsWidget extends ConsumerStatefulWidget {
  final Function(String)? onSheetSelected;
  final Function(String)? onSheetRenamed;
  final Function(String)? onSheetDeleted;
  final VoidCallback? onSheetAdded;

  const SheetTabsWidget({
    super.key,
    this.onSheetSelected,
    this.onSheetRenamed,
    this.onSheetDeleted,
    this.onSheetAdded,
  });

  @override
  ConsumerState<SheetTabsWidget> createState() => _SheetTabsWidgetState();
}

class _SheetTabsWidgetState extends ConsumerState<SheetTabsWidget> {
  String? _editingSheetId;
  final TextEditingController _renameController = TextEditingController();

  @override
  void dispose() {
    _renameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final spreadsheetState = ref.watch(simpleSpreadsheetProvider);
    final worksheets = spreadsheetState.worksheets.values.toList();

    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Sheet tabs
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: worksheets.length,
              itemBuilder: (context, index) {
                final worksheet = worksheets[index];
                final isActive = worksheet.id == spreadsheetState.activeWorksheetId;
                final isEditing = _editingSheetId == worksheet.id;

                return _buildSheetTab(worksheet, isActive, isEditing);
              },
            ),
          ),

          // Add sheet button
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: IconButton(
              onPressed: _addSheet,
              icon: const Icon(Icons.add, size: 16),
              tooltip: 'Add Sheet',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSheetTab(dynamic worksheet, bool isActive, bool isEditing) {
    return GestureDetector(
      onTap: isEditing ? null : () => _selectSheet(worksheet.id),
      onDoubleTap: () => _startRenaming(worksheet.id, worksheet.name),
      child: Container(
        constraints: const BoxConstraints(minWidth: 80, maxWidth: 150),
        decoration: BoxDecoration(
          color: isActive
              ? Theme.of(context).colorScheme.surface
              : Colors.transparent,
          border: Border(
            right: BorderSide(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
            top: isActive
                ? BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : BorderSide.none,
          ),
        ),
        child: Row(
          children: [
            // Sheet content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: isEditing
                    ? _buildRenameField()
                    : _buildSheetLabel(worksheet.name, isActive),
              ),
            ),

            // Context menu
            if (isActive && !isEditing) ...[
              PopupMenuButton<String>(
                padding: EdgeInsets.zero,
                iconSize: 16,
                icon: Icon(
                  Icons.more_vert,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                onSelected: (action) => _handleSheetAction(action, worksheet.id, worksheet.name),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'rename',
                    child: ListTile(
                      leading: Icon(Icons.edit, size: 16),
                      title: Text('Rename'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'duplicate',
                    child: ListTile(
                      leading: Icon(Icons.copy, size: 16),
                      title: Text('Duplicate'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, size: 16),
                      title: Text('Delete'),
                      dense: true,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSheetLabel(String name, bool isActive) {
    return Text(
      name,
      style: TextStyle(
        fontSize: 12,
        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
        color: isActive
            ? Theme.of(context).colorScheme.onSurface
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildRenameField() {
    return TextField(
      controller: _renameController,
      style: const TextStyle(fontSize: 12),
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        isDense: true,
      ),
      autofocus: true,
      onSubmitted: _finishRenaming,
      onTapOutside: (_) => _cancelRenaming(),
    );
  }

  void _selectSheet(String sheetId) {
    ref.read(simpleSpreadsheetProvider.notifier).setActiveWorksheet(sheetId);
    widget.onSheetSelected?.call(sheetId);
  }

  void _addSheet() {
    final spreadsheetState = ref.read(simpleSpreadsheetProvider);
    final sheetCount = spreadsheetState.worksheetCount;
    final newSheetName = 'Sheet${sheetCount + 1}';
    
    ref.read(simpleSpreadsheetProvider.notifier).addWorksheet(newSheetName);
    widget.onSheetAdded?.call();
  }

  void _startRenaming(String sheetId, String currentName) {
    setState(() {
      _editingSheetId = sheetId;
      _renameController.text = currentName;
    });
  }

  void _finishRenaming(String newName) {
    if (_editingSheetId != null && newName.trim().isNotEmpty) {
      ref.read(simpleSpreadsheetProvider.notifier).renameWorksheet(_editingSheetId!, newName.trim());
      widget.onSheetRenamed?.call(_editingSheetId!);
    }
    _cancelRenaming();
  }

  void _cancelRenaming() {
    setState(() {
      _editingSheetId = null;
      _renameController.clear();
    });
  }

  void _handleSheetAction(String action, String sheetId, String sheetName) {
    switch (action) {
      case 'rename':
        _startRenaming(sheetId, sheetName);
        break;
      case 'duplicate':
        _duplicateSheet(sheetId, sheetName);
        break;
      case 'delete':
        _confirmDeleteSheet(sheetId, sheetName);
        break;
    }
  }

  void _duplicateSheet(String sheetId, String sheetName) {
    final newSheetName = '$sheetName (Copy)';
    ref.read(simpleSpreadsheetProvider.notifier).addWorksheet(newSheetName);
    
    // TODO: Copy sheet content
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sheet "$newSheetName" created'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _confirmDeleteSheet(String sheetId, String sheetName) {
    final spreadsheetState = ref.read(simpleSpreadsheetProvider);
    
    // Prevent deleting the last sheet
    if (spreadsheetState.worksheetCount <= 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot delete the last sheet'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Sheet'),
        content: Text('Are you sure you want to delete "$sheetName"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(simpleSpreadsheetProvider.notifier).removeWorksheet(sheetId);
              widget.onSheetDeleted?.call(sheetId);
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Sheet "$sheetName" deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

/// Sheet navigation helper widget
class SheetNavigationWidget extends ConsumerWidget {
  const SheetNavigationWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final spreadsheetState = ref.watch(simpleSpreadsheetProvider);
    final worksheets = spreadsheetState.worksheets.values.toList();
    final activeWorksheet = worksheets.where(
      (w) => w.id == spreadsheetState.activeWorksheetId,
    ).firstOrNull;

    if (activeWorksheet == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.table_chart,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Current Sheet: ${activeWorksheet.name}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          Text(
            '${worksheets.length} sheet${worksheets.length == 1 ? '' : 's'}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
