import 'package:flutter/material.dart';

/// Budget model for Money Manager
class Budget {
  final String id;
  final String name;
  final String description;
  final BudgetType type;
  final double amount;
  final double spent;
  final List<String> categoryIds;
  final List<String> accountIds;
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final bool enableAlerts;
  final double alertThreshold; // Percentage (0.0 to 1.0)
  final Color color;
  final DateTime createdAt;
  final DateTime? modifiedAt;
  final Map<String, dynamic> metadata;

  const Budget({
    required this.id,
    required this.name,
    this.description = '',
    required this.type,
    required this.amount,
    this.spent = 0.0,
    this.categoryIds = const [],
    this.accountIds = const [],
    required this.period,
    required this.startDate,
    required this.endDate,
    this.isActive = true,
    this.enableAlerts = true,
    this.alertThreshold = 0.8,
    this.color = Colors.blue,
    required this.createdAt,
    this.modifiedAt,
    this.metadata = const {},
  });

  factory Budget.fromJson(Map<String, dynamic> json) {
    return Budget(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      type: BudgetType.values[json['type'] as int],
      amount: (json['amount'] as num).toDouble(),
      spent: (json['spent'] as num?)?.toDouble() ?? 0.0,
      categoryIds: (json['category_ids'] as List<dynamic>?)?.map((id) => id as String).toList() ?? [],
      accountIds: (json['account_ids'] as List<dynamic>?)?.map((id) => id as String).toList() ?? [],
      period: BudgetPeriod.values[json['period'] as int],
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      isActive: json['is_active'] as bool? ?? true,
      enableAlerts: json['enable_alerts'] as bool? ?? true,
      alertThreshold: (json['alert_threshold'] as num?)?.toDouble() ?? 0.8,
      color: Color(json['color'] as int? ?? Colors.blue.value),
      createdAt: DateTime.parse(json['created_at'] as String),
      modifiedAt: json['modified_at'] != null 
          ? DateTime.parse(json['modified_at'] as String)
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'amount': amount,
      'spent': spent,
      'category_ids': categoryIds,
      'account_ids': accountIds,
      'period': period.index,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'is_active': isActive,
      'enable_alerts': enableAlerts,
      'alert_threshold': alertThreshold,
      'color': color.value,
      'created_at': createdAt.toIso8601String(),
      'modified_at': modifiedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  Budget copyWith({
    String? id,
    String? name,
    String? description,
    BudgetType? type,
    double? amount,
    double? spent,
    List<String>? categoryIds,
    List<String>? accountIds,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    bool? enableAlerts,
    double? alertThreshold,
    Color? color,
    DateTime? createdAt,
    DateTime? modifiedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Budget(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      spent: spent ?? this.spent,
      categoryIds: categoryIds ?? this.categoryIds,
      accountIds: accountIds ?? this.accountIds,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      enableAlerts: enableAlerts ?? this.enableAlerts,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? DateTime.now(),
      metadata: metadata ?? this.metadata,
    );
  }

  // Calculated properties
  double get remaining => amount - spent;
  double get spentPercentage => amount > 0 ? (spent / amount).clamp(0.0, 1.0) : 0.0;
  double get remainingPercentage => 1.0 - spentPercentage;
  
  bool get isOverBudget => spent > amount;
  bool get isNearLimit => spentPercentage >= alertThreshold;
  bool get isActivePeriod => DateTime.now().isBefore(endDate) && DateTime.now().isAfter(startDate);
  
  int get daysRemaining {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return 0;
    return endDate.difference(now).inDays;
  }
  
  int get totalDays => endDate.difference(startDate).inDays;
  
  double get dailyBudget => totalDays > 0 ? amount / totalDays : 0.0;
  double get dailySpent => totalDays > 0 ? spent / (totalDays - daysRemaining + 1) : 0.0;
  
  String get formattedAmount => '\$${amount.toStringAsFixed(2)}';
  String get formattedSpent => '\$${spent.toStringAsFixed(2)}';
  String get formattedRemaining => '\$${remaining.toStringAsFixed(2)}';
  
  BudgetStatus get status {
    if (!isActive) return BudgetStatus.inactive;
    if (!isActivePeriod) return BudgetStatus.inactive;
    if (isOverBudget) return BudgetStatus.overBudget;
    if (isNearLimit) return BudgetStatus.nearLimit;
    return BudgetStatus.onTrack;
  }
  
  Color get statusColor {
    switch (status) {
      case BudgetStatus.onTrack:
        return Colors.green;
      case BudgetStatus.nearLimit:
        return Colors.orange;
      case BudgetStatus.overBudget:
        return Colors.red;
      case BudgetStatus.inactive:
        return Colors.grey;
    }
  }
  
  String get statusText {
    switch (status) {
      case BudgetStatus.onTrack:
        return 'On Track';
      case BudgetStatus.nearLimit:
        return 'Near Limit';
      case BudgetStatus.overBudget:
        return 'Over Budget';
      case BudgetStatus.inactive:
        return 'Inactive';
    }
  }
  
  IconData get statusIcon {
    switch (status) {
      case BudgetStatus.onTrack:
        return Icons.check_circle;
      case BudgetStatus.nearLimit:
        return Icons.warning;
      case BudgetStatus.overBudget:
        return Icons.error;
      case BudgetStatus.inactive:
        return Icons.pause_circle;
    }
  }
}

enum BudgetType {
  category,
  account,
  total,
}

enum BudgetPeriod {
  weekly,
  monthly,
  quarterly,
  yearly,
  custom,
}

enum BudgetStatus {
  onTrack,
  nearLimit,
  overBudget,
  inactive,
}

extension BudgetTypeExtension on BudgetType {
  String get displayName {
    switch (this) {
      case BudgetType.category:
        return 'Category Budget';
      case BudgetType.account:
        return 'Account Budget';
      case BudgetType.total:
        return 'Total Budget';
    }
  }

  String get description {
    switch (this) {
      case BudgetType.category:
        return 'Budget for specific categories';
      case BudgetType.account:
        return 'Budget for specific accounts';
      case BudgetType.total:
        return 'Overall spending budget';
    }
  }

  IconData get icon {
    switch (this) {
      case BudgetType.category:
        return Icons.category;
      case BudgetType.account:
        return Icons.account_balance_wallet;
      case BudgetType.total:
        return Icons.account_balance;
    }
  }
}

extension BudgetPeriodExtension on BudgetPeriod {
  String get displayName {
    switch (this) {
      case BudgetPeriod.weekly:
        return 'Weekly';
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.quarterly:
        return 'Quarterly';
      case BudgetPeriod.yearly:
        return 'Yearly';
      case BudgetPeriod.custom:
        return 'Custom';
    }
  }

  Duration get duration {
    switch (this) {
      case BudgetPeriod.weekly:
        return const Duration(days: 7);
      case BudgetPeriod.monthly:
        return const Duration(days: 30);
      case BudgetPeriod.quarterly:
        return const Duration(days: 90);
      case BudgetPeriod.yearly:
        return const Duration(days: 365);
      case BudgetPeriod.custom:
        return const Duration(days: 30); // Default fallback
    }
  }

  DateTime getEndDate(DateTime startDate) {
    switch (this) {
      case BudgetPeriod.weekly:
        return startDate.add(const Duration(days: 7));
      case BudgetPeriod.monthly:
        return DateTime(startDate.year, startDate.month + 1, startDate.day);
      case BudgetPeriod.quarterly:
        return DateTime(startDate.year, startDate.month + 3, startDate.day);
      case BudgetPeriod.yearly:
        return DateTime(startDate.year + 1, startDate.month, startDate.day);
      case BudgetPeriod.custom:
        return startDate.add(const Duration(days: 30)); // Default fallback
    }
  }
}
