enum GoalType { savings, debt, investment, purchase, emergency }

enum GoalStatus { active, completed, paused, cancelled }

class FinancialGoal {
  int id = 0;
  String name = '';
  String description = '';
  GoalType type = GoalType.savings;
  double targetAmount = 0.0;
  double currentAmount = 0.0;
  DateTime targetDate = DateTime.now();
  GoalStatus status = GoalStatus.active;
  double monthlyContribution = 0.0;
  bool autoContribute = false;
  int? linkedAccountId;
  String? imageUrl;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  FinancialGoal();

  FinancialGoal.create({
    required this.name,
    required this.type,
    required this.targetAmount,
    required this.targetDate,
    required this.userId,
    this.description = '',
    this.currentAmount = 0.0,
    this.monthlyContribution = 0.0,
    this.autoContribute = false,
    this.linkedAccountId,
    this.imageUrl,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  String get typeDisplayName {
    switch (type) {
      case GoalType.savings:
        return 'Savings Goal';
      case GoalType.debt:
        return 'Debt Payoff';
      case GoalType.investment:
        return 'Investment Goal';
      case GoalType.purchase:
        return 'Purchase Goal';
      case GoalType.emergency:
        return 'Emergency Fund';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case GoalStatus.active:
        return 'Active';
      case GoalStatus.completed:
        return 'Completed';
      case GoalStatus.paused:
        return 'Paused';
      case GoalStatus.cancelled:
        return 'Cancelled';
    }
  }

  double get progressPercentage {
    if (targetAmount == 0) return 0.0;
    return (currentAmount / targetAmount).clamp(0.0, 1.0);
  }

  double get remainingAmount {
    return (targetAmount - currentAmount).clamp(0.0, double.infinity);
  }

  String get formattedTargetAmount {
    return '\$${targetAmount.toStringAsFixed(2)}';
  }

  String get formattedCurrentAmount {
    return '\$${currentAmount.toStringAsFixed(2)}';
  }

  String get formattedRemainingAmount {
    return '\$${remainingAmount.toStringAsFixed(2)}';
  }

  String get formattedTargetDate {
    return '${targetDate.day}/${targetDate.month}/${targetDate.year}';
  }

  bool get isCompleted {
    return currentAmount >= targetAmount || status == GoalStatus.completed;
  }

  bool get isOverdue {
    return DateTime.now().isAfter(targetDate) && !isCompleted;
  }

  int get daysRemaining {
    if (isCompleted) return 0;
    final remaining = targetDate.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }

  int get monthsRemaining {
    if (isCompleted) return 0;
    final now = DateTime.now();
    final months = (targetDate.year - now.year) * 12 + (targetDate.month - now.month);
    return months > 0 ? months : 0;
  }

  double get requiredMonthlyContribution {
    if (isCompleted || monthsRemaining <= 0) return 0.0;
    return remainingAmount / monthsRemaining;
  }

  bool get isOnTrack {
    if (isCompleted) return true;
    if (monthlyContribution == 0) return false;
    
    final projectedAmount = currentAmount + (monthlyContribution * monthsRemaining);
    return projectedAmount >= targetAmount;
  }

  DateTime get projectedCompletionDate {
    if (isCompleted) return DateTime.now();
    if (monthlyContribution <= 0) return targetDate.add(const Duration(days: 365 * 10)); // Far future
    
    final monthsNeeded = remainingAmount / monthlyContribution;
    return DateTime.now().add(Duration(days: (monthsNeeded * 30).round()));
  }

  void addContribution(double amount) {
    currentAmount += amount;
    if (currentAmount >= targetAmount && status == GoalStatus.active) {
      status = GoalStatus.completed;
    }
    updateTimestamp();
  }

  void removeContribution(double amount) {
    currentAmount = (currentAmount - amount).clamp(0.0, double.infinity);
    if (status == GoalStatus.completed && currentAmount < targetAmount) {
      status = GoalStatus.active;
    }
    updateTimestamp();
  }

  void updateGoal({
    String? name,
    String? description,
    GoalType? type,
    double? targetAmount,
    DateTime? targetDate,
    double? monthlyContribution,
    bool? autoContribute,
    int? linkedAccountId,
    String? imageUrl,
    GoalStatus? status,
  }) {
    if (name != null) this.name = name;
    if (description != null) this.description = description;
    if (type != null) this.type = type;
    if (targetAmount != null) this.targetAmount = targetAmount;
    if (targetDate != null) this.targetDate = targetDate;
    if (monthlyContribution != null) this.monthlyContribution = monthlyContribution;
    if (autoContribute != null) this.autoContribute = autoContribute;
    if (linkedAccountId != null) this.linkedAccountId = linkedAccountId;
    if (imageUrl != null) this.imageUrl = imageUrl;
    if (status != null) this.status = status;
    
    updateTimestamp();
  }

  void completeGoal() {
    status = GoalStatus.completed;
    currentAmount = targetAmount;
    updateTimestamp();
  }

  void pauseGoal() {
    status = GoalStatus.paused;
    updateTimestamp();
  }

  void resumeGoal() {
    if (status == GoalStatus.paused) {
      status = GoalStatus.active;
      updateTimestamp();
    }
  }

  void cancelGoal() {
    status = GoalStatus.cancelled;
    updateTimestamp();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'targetAmount': targetAmount,
      'currentAmount': currentAmount,
      'targetDate': targetDate.toIso8601String(),
      'status': status.name,
      'monthlyContribution': monthlyContribution,
      'autoContribute': autoContribute,
      'linkedAccountId': linkedAccountId,
      'imageUrl': imageUrl,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory FinancialGoal.fromJson(Map<String, dynamic> json) {
    final goal = FinancialGoal();
    goal.id = json['id'] ?? 0;
    goal.name = json['name'] ?? '';
    goal.description = json['description'] ?? '';
    goal.type = GoalType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => GoalType.savings,
    );
    goal.targetAmount = (json['targetAmount'] ?? 0.0).toDouble();
    goal.currentAmount = (json['currentAmount'] ?? 0.0).toDouble();
    goal.targetDate = DateTime.parse(json['targetDate']);
    goal.status = GoalStatus.values.firstWhere(
      (s) => s.name == json['status'],
      orElse: () => GoalStatus.active,
    );
    goal.monthlyContribution = (json['monthlyContribution'] ?? 0.0).toDouble();
    goal.autoContribute = json['autoContribute'] ?? false;
    goal.linkedAccountId = json['linkedAccountId'];
    goal.imageUrl = json['imageUrl'];
    goal.userId = json['userId'] ?? '';
    goal.createdAt = DateTime.parse(json['createdAt']);
    goal.updatedAt = DateTime.parse(json['updatedAt']);
    goal.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    goal.isDeleted = json['isDeleted'] ?? false;
    goal.syncId = json['syncId'];
    return goal;
  }
}
