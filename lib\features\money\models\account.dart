import 'package:flutter/material.dart';

enum AccountType {
  checking,
  savings,
  credit,
  investment,
  cash,
  loan,
  other
}

class Account {
  int id = 0;
  String name = '';
  String description = '';
  AccountType type = AccountType.checking;
  double balance = 0.0;
  String currency = 'USD';
  String? bankName;
  String? accountNumber;
  IconData icon = Icons.account_balance_wallet;
  Color color = Colors.blue;
  bool isActive = true;
  bool includeInTotal = true;
  bool allowNegativeBalance = false;
  String userId = '';
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Account();

  Account.create({
    required this.name,
    required this.type,
    required this.userId,
    this.description = '',
    this.balance = 0.0,
    this.currency = 'USD',
    this.bankName,
    this.accountNumber,
    this.icon = Icons.account_balance_wallet,
    this.color = Colors.blue,
    this.isActive = true,
    this.includeInTotal = true,
    this.allowNegativeBalance = false,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  String get typeDisplayName {
    switch (type) {
      case AccountType.checking:
        return 'Checking Account';
      case AccountType.savings:
        return 'Savings Account';
      case AccountType.credit:
        return 'Credit Card';
      case AccountType.investment:
        return 'Investment Account';
      case AccountType.cash:
        return 'Cash';
      case AccountType.loan:
        return 'Loan Account';
      case AccountType.other:
        return 'Other';
    }
  }

  String get formattedBalance {
    return _getCurrencyFormatter();
  }

  String get maskedAccountNumber {
    if (accountNumber == null || accountNumber!.length < 4) {
      return accountNumber ?? '';
    }
    final lastFour = accountNumber!.substring(accountNumber!.length - 4);
    return '****$lastFour';
  }

  bool get isDebtAccount {
    return type == AccountType.credit || type == AccountType.loan;
  }

  void updateBalance(double newBalance) {
    balance = newBalance;
    updateTimestamp();
  }

  void addToBalance(double amount) {
    balance += amount;
    updateTimestamp();
  }

  void subtractFromBalance(double amount) {
    balance -= amount;
    updateTimestamp();
  }

  void updateAccount({
    String? name,
    String? description,
    AccountType? type,
    String? currency,
    String? bankName,
    String? accountNumber,
    IconData? icon,
    Color? color,
    bool? isActive,
    bool? includeInTotal,
    bool? allowNegativeBalance,
  }) {
    if (name != null) this.name = name;
    if (description != null) this.description = description;
    if (type != null) this.type = type;
    if (currency != null) this.currency = currency;
    if (bankName != null) this.bankName = bankName;
    if (accountNumber != null) this.accountNumber = accountNumber;
    if (icon != null) this.icon = icon;
    if (color != null) this.color = color;
    if (isActive != null) this.isActive = isActive;
    if (includeInTotal != null) this.includeInTotal = includeInTotal;
    if (allowNegativeBalance != null) this.allowNegativeBalance = allowNegativeBalance;
    
    updateTimestamp();
  }

  String _getCurrencyFormatter() {
    // Enhanced currency formatting with Arab countries support
    switch (currency) {
      case 'USD':
        return '\$${balance.toStringAsFixed(2)}';
      case 'EUR':
        return '€${balance.toStringAsFixed(2)}';
      case 'GBP':
        return '£${balance.toStringAsFixed(2)}';
      case 'JPY':
        return '¥${balance.toStringAsFixed(0)}';
      case 'SAR':
        return '${balance.toStringAsFixed(2)} ر.س';
      case 'AED':
        return '${balance.toStringAsFixed(2)} د.إ';
      case 'KWD':
        return '${balance.toStringAsFixed(3)} د.ك';
      case 'QAR':
        return '${balance.toStringAsFixed(2)} ر.ق';
      case 'BHD':
        return '${balance.toStringAsFixed(3)} د.ب';
      case 'OMR':
        return '${balance.toStringAsFixed(3)} ر.ع';
      case 'JOD':
        return '${balance.toStringAsFixed(3)} د.أ';
      case 'LBP':
        return '${balance.toStringAsFixed(0)} ل.ل';
      case 'SYP':
        return '${balance.toStringAsFixed(0)} ل.س';
      case 'IQD':
        return '${balance.toStringAsFixed(0)} د.ع';
      case 'EGP':
        return '${balance.toStringAsFixed(2)} ج.م';
      case 'LYD':
        return '${balance.toStringAsFixed(3)} د.ل';
      case 'TND':
        return '${balance.toStringAsFixed(3)} د.ت';
      case 'DZD':
        return '${balance.toStringAsFixed(2)} د.ج';
      case 'MAD':
        return '${balance.toStringAsFixed(2)} د.م';
      case 'MRU':
        return '${balance.toStringAsFixed(2)} أ.م';
      case 'SDG':
        return '${balance.toStringAsFixed(2)} ج.س';
      case 'SOS':
        return '${balance.toStringAsFixed(2)} ش.ص';
      case 'DJF':
        return '${balance.toStringAsFixed(0)} ف.ج';
      case 'KMF':
        return '${balance.toStringAsFixed(0)} ف.ق';
      default:
        return '$currency ${balance.toStringAsFixed(2)}';
    }
  }

  /// Get list of available currencies including Arab countries
  static List<Map<String, String>> getAvailableCurrencies() {
    return [
      // Major currencies
      {'code': 'USD', 'name': 'US Dollar', 'symbol': '\$'},
      {'code': 'EUR', 'name': 'Euro', 'symbol': '€'},
      {'code': 'GBP', 'name': 'British Pound', 'symbol': '£'},
      {'code': 'JPY', 'name': 'Japanese Yen', 'symbol': '¥'},

      // Arab countries currencies
      {'code': 'SAR', 'name': 'Saudi Riyal', 'symbol': 'ر.س'},
      {'code': 'AED', 'name': 'UAE Dirham', 'symbol': 'د.إ'},
      {'code': 'KWD', 'name': 'Kuwaiti Dinar', 'symbol': 'د.ك'},
      {'code': 'QAR', 'name': 'Qatari Riyal', 'symbol': 'ر.ق'},
      {'code': 'BHD', 'name': 'Bahraini Dinar', 'symbol': 'د.ب'},
      {'code': 'OMR', 'name': 'Omani Rial', 'symbol': 'ر.ع'},
      {'code': 'JOD', 'name': 'Jordanian Dinar', 'symbol': 'د.أ'},
      {'code': 'LBP', 'name': 'Lebanese Pound', 'symbol': 'ل.ل'},
      {'code': 'SYP', 'name': 'Syrian Pound', 'symbol': 'ل.س'},
      {'code': 'IQD', 'name': 'Iraqi Dinar', 'symbol': 'د.ع'},
      {'code': 'EGP', 'name': 'Egyptian Pound', 'symbol': 'ج.م'},
      {'code': 'LYD', 'name': 'Libyan Dinar', 'symbol': 'د.ل'},
      {'code': 'TND', 'name': 'Tunisian Dinar', 'symbol': 'د.ت'},
      {'code': 'DZD', 'name': 'Algerian Dinar', 'symbol': 'د.ج'},
      {'code': 'MAD', 'name': 'Moroccan Dirham', 'symbol': 'د.م'},
      {'code': 'MRU', 'name': 'Mauritanian Ouguiya', 'symbol': 'أ.م'},
      {'code': 'SDG', 'name': 'Sudanese Pound', 'symbol': 'ج.س'},
      {'code': 'SOS', 'name': 'Somali Shilling', 'symbol': 'ش.ص'},
      {'code': 'DJF', 'name': 'Djiboutian Franc', 'symbol': 'ف.ج'},
      {'code': 'KMF', 'name': 'Comorian Franc', 'symbol': 'ف.ق'},

      // Other common currencies
      {'code': 'CAD', 'name': 'Canadian Dollar', 'symbol': 'C\$'},
      {'code': 'AUD', 'name': 'Australian Dollar', 'symbol': 'A\$'},
      {'code': 'CHF', 'name': 'Swiss Franc', 'symbol': 'CHF'},
      {'code': 'CNY', 'name': 'Chinese Yuan', 'symbol': '¥'},
      {'code': 'INR', 'name': 'Indian Rupee', 'symbol': '₹'},
      {'code': 'TRY', 'name': 'Turkish Lira', 'symbol': '₺'},
    ];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'balance': balance,
      'currency': currency,
      'bankName': bankName,
      'accountNumber': accountNumber,
      'icon': icon.codePoint,
      'color': color.toARGB32(),
      'isActive': isActive,
      'includeInTotal': includeInTotal,
      'allowNegativeBalance': allowNegativeBalance,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Account.fromJson(Map<String, dynamic> json) {
    final account = Account();
    account.id = json['id'] ?? 0;
    account.name = json['name'] ?? '';
    account.description = json['description'] ?? '';
    account.type = AccountType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => AccountType.checking,
    );
    account.balance = (json['balance'] ?? 0.0).toDouble();
    account.currency = json['currency'] ?? 'USD';
    account.bankName = json['bankName'];
    account.accountNumber = json['accountNumber'];
    account.icon = _getIconFromCodePoint(json['icon'] ?? Icons.account_balance_wallet.codePoint);
    account.color = Color(json['color'] ?? Colors.blue.toARGB32());
    account.isActive = json['isActive'] ?? true;
    account.includeInTotal = json['includeInTotal'] ?? true;
    account.allowNegativeBalance = json['allowNegativeBalance'] ?? false;
    account.userId = json['userId'] ?? '';
    account.createdAt = DateTime.parse(json['createdAt']);
    account.updatedAt = DateTime.parse(json['updatedAt']);
    account.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    account.isDeleted = json['isDeleted'] ?? false;
    account.syncId = json['syncId'];
    return account;
  }

  static IconData _getIconFromCodePoint(int codePoint) {
    // Map of common account icon code points to their IconData constants
    const iconMap = {
      0xe047: Icons.account_balance_wallet, // account_balance_wallet
      0xe0c8: Icons.credit_card, // credit_card
      0xe227: Icons.savings, // savings
      0xe0c7: Icons.account_balance, // account_balance
      0xe263: Icons.payments, // payments
      0xe57c: Icons.monetization_on, // monetization_on
    };

    return iconMap[codePoint] ?? Icons.account_balance_wallet;
  }
}
