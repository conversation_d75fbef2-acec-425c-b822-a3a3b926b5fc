import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quran_models.dart';
import '../services/quran_service.dart';

/// Detailed Surah reading screen
class SurahDetailScreen extends ConsumerStatefulWidget {
  final Surah surah;
  final int? startVerse;

  const SurahDetailScreen({
    super.key,
    required this.surah,
    this.startVerse,
  });

  @override
  ConsumerState<SurahDetailScreen> createState() => _SurahDetailScreenState();
}

class _SurahDetailScreenState extends ConsumerState<SurahDetailScreen> {
  final QuranService _quranService = QuranService();
  final ScrollController _scrollController = ScrollController();
  List<Verse> _verses = [];
  bool _isLoading = true;
  QuranSettings _settings = const QuranSettings();

  @override
  void initState() {
    super.initState();
    _loadSurahData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadSurahData() async {
    try {
      final verses = await _quranService.getVerses(widget.surah.number);
      final settings = _quranService.getSettings();
      
      setState(() {
        _verses = verses;
        _settings = settings;
        _isLoading = false;
      });

      // Scroll to start verse if specified
      if (widget.startVerse != null && widget.startVerse! > 1) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToVerse(widget.startVerse!);
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading Surah: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _scrollToVerse(int verseNumber) {
    // Calculate approximate position based on verse number
    final position = (verseNumber - 1) * 150.0; // Approximate height per verse
    _scrollController.animateTo(
      position,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.surah.nameEnglish),
            Text(
              widget.surah.nameArabic,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontFamily: 'Amiri',
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: _showBookmarkDialog,
            tooltip: 'Bookmark',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
            tooltip: 'Reading Settings',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('Share'),
                ),
              ),
              const PopupMenuItem(
                value: 'audio',
                child: ListTile(
                  leading: Icon(Icons.play_circle),
                  title: Text('Audio Recitation'),
                ),
              ),
              const PopupMenuItem(
                value: 'translation',
                child: ListTile(
                  leading: Icon(Icons.translate),
                  title: Text('Change Translation'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading verses...'),
                ],
              ),
            )
          : Column(
              children: [
                // Surah header
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        widget.surah.nameArabic,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontFamily: 'Amiri',
                          fontWeight: FontWeight.bold,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${widget.surah.nameTransliteration} • ${widget.surah.revelationType} • ${widget.surah.versesCount} verses',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),

                // Bismillah (except for At-Tawbah)
                if (widget.surah.number != 9 && widget.surah.number != 1)
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Text(
                      'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontFamily: 'Amiri',
                        height: 1.8,
                      ),
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.rtl,
                    ),
                  ),

                // Verses
                Expanded(
                  child: _verses.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(16),
                          itemCount: _verses.length,
                          itemBuilder: (context, index) {
                            final verse = _verses[index];
                            return _buildVerseTile(verse, index);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildVerseTile(Verse verse, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Verse number
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    verse.number.toString(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.bookmark_border),
                onPressed: () => _bookmarkVerse(verse),
                iconSize: 20,
              ),
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: () => _shareVerse(verse),
                iconSize: 20,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Arabic text
          Text(
            verse.textArabic,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontFamily: 'Amiri',
              fontSize: _settings.arabicFontSize,
              height: _settings.lineSpacing,
            ),
            textAlign: TextAlign.right,
            textDirection: TextDirection.rtl,
          ),
          
          if (_settings.showTransliteration && verse.textTransliteration.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              verse.textTransliteration,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: _settings.translationFontSize,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
          
          if (_settings.showTranslation && verse.textTranslation.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              verse.textTranslation,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: _settings.translationFontSize,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No Verses Available',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'This Surah\'s verses are not yet loaded',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showBookmarkDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bookmark Surah'),
        content: const Text('Add this Surah to your bookmarks?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add bookmark logic
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Surah bookmarked!')),
              );
            },
            child: const Text('Bookmark'),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reading Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Reading settings will be available in the next update.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareSurah();
        break;
      case 'audio':
        _playAudio();
        break;
      case 'translation':
        _changeTranslation();
        break;
    }
  }

  void _shareSurah() {
    // Share Surah logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon!')),
    );
  }

  void _playAudio() {
    // Audio playback logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Audio recitation coming soon!')),
    );
  }

  void _changeTranslation() {
    // Translation change logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Translation options coming soon!')),
    );
  }

  void _bookmarkVerse(Verse verse) {
    // Bookmark verse logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Verse ${verse.number} bookmarked!')),
    );
  }

  void _shareVerse(Verse verse) {
    // Share verse logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Verse ${verse.number} shared!')),
    );
  }
}
