# ShadowSuite Build Setup Guide

This guide provides step-by-step instructions for setting up the development environment and building ShadowSuite for different platforms.

## Prerequisites

### General Requirements
- Flutter SDK 3.16.0 or later
- Dart SDK 3.2.0 or later
- Git for version control
- A code editor (VS Code recommended)

### Platform-Specific Requirements

#### Windows Desktop
- Windows 10 version 1903 or later
- Visual Studio 2022 or Visual Studio Build Tools 2022
- CMake 3.23 or later
- Ninja build system

#### Android
- Android Studio or Android SDK Command-line Tools
- Android SDK API level 21 (Android 5.0) or later
- Java Development Kit (JDK) 11 or later

#### Web
- Chrome browser for testing
- Web server for deployment (optional)

## Windows Desktop Build Setup

### 1. Install Visual Studio 2022

Download and install Visual Studio 2022 Community (free) from:
https://visualstudio.microsoft.com/downloads/

**Required Workloads:**
- Desktop development with C++
- Game development with C++ (for CMake tools)

**Individual Components (ensure these are selected):**
- MSVC v143 - VS 2022 C++ x64/x86 build tools
- Windows 11 SDK (latest version)
- CMake tools for Visual Studio
- Git for Windows

### 2. Upgrade CMake to 3.23+

Flutter requires CMake 3.23 or later for Windows desktop builds.

**Option A: Through Visual Studio Installer**
1. Open Visual Studio Installer
2. Click "Modify" on your VS 2022 installation
3. Go to Individual Components
4. Search for "CMake"
5. Select "CMake tools for Visual Studio" (latest version)
6. Click "Modify" to install

**Option B: Download CMake Directly**
1. Go to https://cmake.org/download/
2. Download the latest Windows x64 Installer
3. Install CMake and add it to your system PATH
4. Verify installation: `cmake --version`

### 3. Install Ninja Build System

```bash
# Using Chocolatey (recommended)
choco install ninja

# Or download manually from:
# https://github.com/ninja-build/ninja/releases
# Extract ninja.exe to a folder in your PATH
```

### 4. Configure Flutter for Windows

```bash
# Enable Windows desktop support
flutter config --enable-windows-desktop

# Verify Windows toolchain
flutter doctor -v
```

**Expected Output:**
```
[√] Visual Studio - develop for Windows (Visual Studio Community 2022 17.x.x)
    • Visual Studio at C:\Program Files\Microsoft Visual Studio\2022\Community
    • Visual Studio Community 2022 version 17.x.x
    • Windows 10 SDK version 10.0.xxxxx.x
```

### 5. Fix Common Windows Build Issues

**Issue: CMake version too old**
```
Error: CMake 3.23 or higher is required. You are running version 3.xx.x
```
**Solution:** Follow step 2 above to upgrade CMake.

**Issue: Build path contains spaces**
```
Error: The path contains spaces which are not supported by the build system.
```
**Solution:** Move your project to a path without spaces, e.g., `C:\dev\shadowsuite`

**Issue: Long path names**
```
Error: The filename or extension is too long.
```
**Solution:** 
1. Enable long path support in Windows:
   - Run `gpedit.msc` as administrator
   - Navigate to: Computer Configuration > Administrative Templates > System > Filesystem
   - Enable "Enable Win32 long paths"
2. Or move project closer to root: `C:\shadowsuite`

## Android Build Setup

### 1. Install Android Studio

Download from: https://developer.android.com/studio

**Required SDK Components:**
- Android SDK Platform-Tools
- Android SDK Build-Tools (latest)
- Android SDK Platform API 34 (or latest)
- Android SDK Command-line Tools

### 2. Configure Android SDK

```bash
# Set environment variables (add to your system PATH)
ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
ANDROID_SDK_ROOT=%ANDROID_HOME%

# Add to PATH:
%ANDROID_HOME%\platform-tools
%ANDROID_HOME%\cmdline-tools\latest\bin
%ANDROID_HOME%\emulator
```

### 3. Accept Android Licenses

```bash
flutter doctor --android-licenses
```

### 4. Fix Android Build Path Issues

**Issue: Path contains spaces**
If your project path contains spaces, Android builds may fail.

**Solution:** Move project to a path without spaces:
```bash
# Good paths:
C:\dev\shadowsuite
C:\projects\shadowsuite
D:\shadowsuite

# Bad paths:
C:\Users\<USER>\Documents\shadowsuite
C:\My Projects\shadowsuite
```

## Building ShadowSuite

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url> shadowsuite
cd shadowsuite

# Get dependencies
flutter pub get

# Generate code (if needed)
flutter packages pub run build_runner build
```

### 2. Build for Different Platforms

#### Web (Development)
```bash
flutter run -d chrome
```

#### Web (Production)
```bash
flutter build web --release
# Output: build/web/
```

#### Windows Desktop (Development)
```bash
flutter run -d windows
```

#### Windows Desktop (Production)
```bash
flutter build windows --release
# Output: build/windows/x64/runner/Release/
```

#### Android (Development)
```bash
flutter run -d android
```

#### Android APK (Production)
```bash
flutter build apk --release
# Output: build/app/outputs/flutter-apk/app-release.apk
```

#### Android App Bundle (Production)
```bash
flutter build appbundle --release
# Output: build/app/outputs/bundle/release/app-release.aab
```

### 3. Build Verification

After building, verify the outputs:

**Windows:**
- Executable: `build/windows/x64/runner/Release/shadowsuite.exe`
- Required DLLs are included in the same directory
- Test by running the executable directly

**Android:**
- APK: `build/app/outputs/flutter-apk/app-release.apk`
- Install on device: `flutter install --release`
- Test all features work correctly

**Web:**
- Static files: `build/web/`
- Test by serving locally: `python -m http.server 8000` (from build/web/)

## Troubleshooting

### Common Build Errors

**Error: "Could not find a command line tool"**
```bash
# Install missing tools
flutter doctor
# Follow the suggestions to install missing components
```

**Error: "Gradle build failed"**
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk --release
```

**Error: "CMake Error"**
```bash
# Ensure CMake 3.23+ is installed and in PATH
cmake --version
# Should show 3.23.0 or later
```

**Error: "Visual Studio not found"**
```bash
# Verify Visual Studio installation
flutter doctor -v
# Install required workloads if missing
```

### Performance Optimization

**For faster builds:**
```bash
# Use debug builds during development
flutter run --debug

# Use profile builds for performance testing
flutter run --profile

# Only use release builds for final testing/distribution
flutter build <platform> --release
```

**For smaller APK size:**
```bash
# Build separate APKs per architecture
flutter build apk --release --split-per-abi

# Use app bundle for Play Store
flutter build appbundle --release
```

## Continuous Integration

### GitHub Actions Example

Create `.github/workflows/build.yml`:

```yaml
name: Build ShadowSuite

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    - run: flutter pub get
    - run: flutter build windows --release

  build-android:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '11'
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    - run: flutter pub get
    - run: flutter build apk --release

  build-web:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    - run: flutter pub get
    - run: flutter build web --release
```

## Distribution

### Windows
- Package the entire `build/windows/x64/runner/Release/` folder
- Create installer using tools like Inno Setup or NSIS
- Consider code signing for production releases

### Android
- Upload APK/AAB to Google Play Store
- Test on multiple devices and Android versions
- Follow Google Play Store guidelines

### Web
- Deploy to web hosting service (Firebase Hosting, Netlify, etc.)
- Configure proper MIME types for Flutter web assets
- Set up HTTPS for production

## Support

For build issues:
1. Check `flutter doctor -v` output
2. Verify all prerequisites are installed
3. Check the troubleshooting section above
4. Search Flutter GitHub issues for similar problems
5. Create an issue with detailed error logs if needed
