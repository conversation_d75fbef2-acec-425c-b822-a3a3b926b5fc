import 'package:flutter/material.dart';

class TextNote {
  int id;
  String title;
  String content;
  String category;
  DateTime createdAt;
  DateTime updatedAt;
  bool isPinned;
  bool isArchived;
  bool isFavorite;
  String tags; // Comma-separated tags
  String color; // Hex color code
  String attachments; // JSON array of attachment paths
  String userId;
  String formattingData; // JSON data for rich text formatting
  int wordCount;
  int characterCount;
  String templateId; // Reference to note template

  TextNote({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.createdAt,
    required this.updatedAt,
    required this.isPinned,
    required this.isArchived,
    required this.isFavorite,
    required this.tags,
    required this.color,
    required this.attachments,
    required this.userId,
    required this.formattingData,
    required this.wordCount,
    required this.characterCount,
    required this.templateId,
  });

  TextNote.create({
    required this.title,
    required this.content,
    required this.category,
    required this.userId,
    this.isPinned = false,
    this.isArchived = false,
    this.isFavorite = false,
    this.tags = '',
    this.color = '#FFFFFF',
    this.attachments = '[]',
    this.formattingData = '{}',
    this.templateId = '',
  }) : id = DateTime.now().millisecondsSinceEpoch,
       createdAt = DateTime.now(),
       updatedAt = DateTime.now(),
       wordCount = 0,
       characterCount = 0 {
    _updateCounts();
  }
  
  void _updateCounts() {
    wordCount = content.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
    characterCount = content.length;
  }
  
  void updateContent(String newContent) {
    content = newContent;
    updatedAt = DateTime.now();
    _updateCounts();
  }
  
  void updateTitle(String newTitle) {
    title = newTitle;
    updatedAt = DateTime.now();
  }
  
  void addTag(String tag) {
    final currentTags = getTagsList();
    if (!currentTags.contains(tag)) {
      currentTags.add(tag);
      tags = currentTags.join(',');
      updatedAt = DateTime.now();
    }
  }
  
  void removeTag(String tag) {
    final currentTags = getTagsList();
    currentTags.remove(tag);
    tags = currentTags.join(',');
    updatedAt = DateTime.now();
  }
  
  List<String> getTagsList() {
    if (tags.isEmpty) return [];
    return tags.split(',').map((tag) => tag.trim()).where((tag) => tag.isNotEmpty).toList();
  }
  
  Color getColor() {
    try {
      return Color(int.parse(color.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Colors.white;
    }
  }
  
  void setColor(Color newColor) {
    final colorValue = ((newColor.r * 255).round() << 16 | (newColor.g * 255).round() << 8 | (newColor.b * 255).round() | (newColor.a * 255).round() << 24);
    color = '#${colorValue.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
    updatedAt = DateTime.now();
  }
  
  List<String> getAttachments() {
    try {
      // In a real implementation, this would parse JSON
      return [];
    } catch (e) {
      return [];
    }
  }
  
  void addAttachment(String attachmentPath) {
    final currentAttachments = getAttachments();
    currentAttachments.add(attachmentPath);
    // In a real implementation, this would serialize to JSON
    attachments = '[]';
    updatedAt = DateTime.now();
  }
  
  void removeAttachment(String attachmentPath) {
    final currentAttachments = getAttachments();
    currentAttachments.remove(attachmentPath);
    // In a real implementation, this would serialize to JSON
    attachments = '[]';
    updatedAt = DateTime.now();
  }
  
  Map<String, dynamic> getFormattingData() {
    try {
      // In a real implementation, this would parse JSON
      return {};
    } catch (e) {
      return {};
    }
  }
  
  void setFormattingData(Map<String, dynamic> data) {
    // In a real implementation, this would serialize to JSON
    formattingData = '{}';
    updatedAt = DateTime.now();
  }
  
  void togglePin() {
    isPinned = !isPinned;
    updatedAt = DateTime.now();
  }
  
  void toggleArchive() {
    isArchived = !isArchived;
    updatedAt = DateTime.now();
  }
  
  void toggleFavorite() {
    isFavorite = !isFavorite;
    updatedAt = DateTime.now();
  }
  
  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 7) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }
  
  String get formattedUpdatedAt {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    
    if (difference.inDays > 7) {
      return 'Updated ${updatedAt.day}/${updatedAt.month}/${updatedAt.year}';
    } else if (difference.inDays > 0) {
      return 'Updated ${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return 'Updated ${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return 'Updated ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Updated just now';
    }
  }
  
  String get preview {
    final plainText = content.replaceAll(RegExp(r'<[^>]*>'), ''); // Remove HTML tags
    if (plainText.length <= 100) return plainText;
    return '${plainText.substring(0, 100)}...';
  }
  
  bool matchesSearch(String query) {
    final lowerQuery = query.toLowerCase();
    return title.toLowerCase().contains(lowerQuery) ||
           content.toLowerCase().contains(lowerQuery) ||
           category.toLowerCase().contains(lowerQuery) ||
           getTagsList().any((tag) => tag.toLowerCase().contains(lowerQuery));
  }
  
  TextNote copyWith({
    String? title,
    String? content,
    String? category,
    bool? isPinned,
    bool? isArchived,
    bool? isFavorite,
    String? tags,
    String? color,
    String? templateId,
  }) {
    return TextNote.create(
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      userId: userId,
      isPinned: isPinned ?? this.isPinned,
      isArchived: isArchived ?? this.isArchived,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
      color: color ?? this.color,
      templateId: templateId ?? this.templateId,
    );
  }
}

enum NoteCategory {
  personal,
  work,
  study,
  ideas,
  reminders,
  recipes,
  travel,
  health,
  finance,
  projects,
  custom;
  
  String get displayName {
    switch (this) {
      case NoteCategory.personal:
        return 'Personal';
      case NoteCategory.work:
        return 'Work';
      case NoteCategory.study:
        return 'Study';
      case NoteCategory.ideas:
        return 'Ideas';
      case NoteCategory.reminders:
        return 'Reminders';
      case NoteCategory.recipes:
        return 'Recipes';
      case NoteCategory.travel:
        return 'Travel';
      case NoteCategory.health:
        return 'Health';
      case NoteCategory.finance:
        return 'Finance';
      case NoteCategory.projects:
        return 'Projects';
      case NoteCategory.custom:
        return 'Custom';
    }
  }
  
  IconData get icon {
    switch (this) {
      case NoteCategory.personal:
        return Icons.person;
      case NoteCategory.work:
        return Icons.work;
      case NoteCategory.study:
        return Icons.school;
      case NoteCategory.ideas:
        return Icons.lightbulb;
      case NoteCategory.reminders:
        return Icons.alarm;
      case NoteCategory.recipes:
        return Icons.restaurant;
      case NoteCategory.travel:
        return Icons.flight;
      case NoteCategory.health:
        return Icons.health_and_safety;
      case NoteCategory.finance:
        return Icons.attach_money;
      case NoteCategory.projects:
        return Icons.folder_special;
      case NoteCategory.custom:
        return Icons.category;
    }
  }
  
  Color get color {
    switch (this) {
      case NoteCategory.personal:
        return Colors.blue;
      case NoteCategory.work:
        return Colors.orange;
      case NoteCategory.study:
        return Colors.green;
      case NoteCategory.ideas:
        return Colors.yellow;
      case NoteCategory.reminders:
        return Colors.red;
      case NoteCategory.recipes:
        return Colors.brown;
      case NoteCategory.travel:
        return Colors.cyan;
      case NoteCategory.health:
        return Colors.pink;
      case NoteCategory.finance:
        return Colors.teal;
      case NoteCategory.projects:
        return Colors.purple;
      case NoteCategory.custom:
        return Colors.grey;
    }
  }
}

class NoteTemplate {
  int id;
  String name;
  String description;
  String content;
  String category;
  String formattingData;
  DateTime createdAt;
  String userId;

  NoteTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.content,
    required this.category,
    required this.formattingData,
    required this.createdAt,
    required this.userId,
  });

  NoteTemplate.create({
    required this.name,
    required this.description,
    required this.content,
    required this.category,
    required this.userId,
    this.formattingData = '{}',
  }) : id = DateTime.now().millisecondsSinceEpoch,
       createdAt = DateTime.now();
}
