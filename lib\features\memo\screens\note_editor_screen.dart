import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/note.dart';
import '../providers/note_provider.dart';
import '../../../shared/providers/user_provider.dart';

class NoteEditorScreen extends ConsumerStatefulWidget {
  final int? noteId;

  const NoteEditorScreen({super.key, this.noteId});

  @override
  ConsumerState<NoteEditorScreen> createState() => _NoteEditorScreenState();
}

class _NoteEditorScreenState extends ConsumerState<NoteEditorScreen> {
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagController = TextEditingController();
  
  Note? _currentNote;
  String _selectedColor = 'default';
  bool _isPinned = false;
  bool _isFavorite = false;
  List<String> _tags = [];
  bool _isLoading = false;
  bool _hasChanges = false;

  final List<Map<String, dynamic>> _colors = [
    {'name': 'default', 'color': Colors.white, 'label': 'Default'},
    {'name': 'red', 'color': Colors.red[100], 'label': 'Red'},
    {'name': 'orange', 'color': Colors.orange[100], 'label': 'Orange'},
    {'name': 'yellow', 'color': Colors.yellow[100], 'label': 'Yellow'},
    {'name': 'green', 'color': Colors.green[100], 'label': 'Green'},
    {'name': 'blue', 'color': Colors.blue[100], 'label': 'Blue'},
    {'name': 'purple', 'color': Colors.purple[100], 'label': 'Purple'},
    {'name': 'pink', 'color': Colors.pink[100], 'label': 'Pink'},
  ];

  @override
  void initState() {
    super.initState();
    _loadNote();
    _titleController.addListener(_onTextChanged);
    _contentController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  void _loadNote() {
    if (widget.noteId != null) {
      final notes = ref.read(notesProvider);
      _currentNote = notes.firstWhere(
        (note) => note.id == widget.noteId,
        orElse: () => Note(),
      );
      
      if (_currentNote != null && _currentNote!.id != 0) {
        _titleController.text = _currentNote!.title;
        _contentController.text = _currentNote!.content;
        _selectedColor = _currentNote!.color;
        _isPinned = _currentNote!.isPinned;
        _isFavorite = _currentNote!.isFavorite;
        _tags = List.from(_currentNote!.tags);
      }
    }
  }

  Future<void> _saveNote() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = ref.read(userProfileProvider)?.id.toString() ?? '';
      
      if (_currentNote != null && _currentNote!.id != 0) {
        // Update existing note
        _currentNote!.updateContent(
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          color: _selectedColor,
          isPinned: _isPinned,
          isFavorite: _isFavorite,
          tags: _tags,
        );
        await ref.read(notesProvider.notifier).updateNote(_currentNote!);
      } else {
        // Create new note
        await ref.read(notesProvider.notifier).createNote(
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          color: _selectedColor,
          isPinned: _isPinned,
          isFavorite: _isFavorite,
          tags: _tags,
        );
      }

      setState(() {
        _hasChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note saved successfully'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving note: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
        _hasChanges = true;
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
      _hasChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final selectedColorData = _colors.firstWhere(
      (color) => color['name'] == _selectedColor,
      orElse: () => _colors.first,
    );

    return Scaffold(
      backgroundColor: selectedColorData['color'],
      appBar: AppBar(
        title: Text(widget.noteId != null ? 'Edit Note' : 'New Note'),
        backgroundColor: selectedColorData['color'],
        actions: [
          IconButton(
            icon: Icon(
              _isPinned ? Icons.push_pin : Icons.push_pin_outlined,
              color: _isPinned ? Colors.orange : null,
            ),
            onPressed: () {
              setState(() {
                _isPinned = !_isPinned;
                _hasChanges = true;
              });
            },
          ),
          IconButton(
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? Colors.red : null,
            ),
            onPressed: () {
              setState(() {
                _isFavorite = !_isFavorite;
                _hasChanges = true;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.palette),
            onSelected: (color) {
              setState(() {
                _selectedColor = color;
                _hasChanges = true;
              });
            },
            itemBuilder: (context) => _colors.map((colorData) {
              return PopupMenuItem<String>(
                value: colorData['name'],
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: colorData['color'],
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(colorData['label']),
                  ],
                ),
              );
            }).toList(),
          ),
          if (_hasChanges)
            IconButton(
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              onPressed: _isLoading ? null : _saveNote,
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Title Field
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                hintText: 'Note title...',
                border: InputBorder.none,
              ),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              maxLines: null,
            ),
            const SizedBox(height: 16),

            // Content Field
            Expanded(
              child: TextField(
                controller: _contentController,
                decoration: const InputDecoration(
                  hintText: 'Start writing...',
                  border: InputBorder.none,
                ),
                style: Theme.of(context).textTheme.bodyLarge,
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
              ),
            ),

            // Tags Section
            if (_tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _tags.map((tag) {
                  return Chip(
                    label: Text(tag),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeTag(tag),
                  );
                }).toList(),
              ),
            ],

            // Add Tag Field
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _tagController,
                    decoration: const InputDecoration(
                      hintText: 'Add tag...',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    onSubmitted: (_) => _addTag(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: _addTag,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
