import 'package:flutter/material.dart';
import '../services/money_manager_service.dart';

/// Placeholder accounts screen
class AccountsScreen extends StatelessWidget {
  final MoneyManagerService moneyService;

  const AccountsScreen({
    super.key,
    required this.moneyService,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
      ),
      body: const Center(
        child: Text('Accounts management coming soon!'),
      ),
    );
  }
}
