import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/tool_creation_provider.dart';
import '../../spreadsheet/widgets/simple_spreadsheet_widget.dart';
import '../../spreadsheet/providers/simple_spreadsheet_provider.dart';

/// Page B - Backend: Excel-like spreadsheet with formula engine
class BackendPage extends ConsumerStatefulWidget {
  const BackendPage({super.key});

  @override
  ConsumerState<BackendPage> createState() => _BackendPageState();
}

class _BackendPageState extends ConsumerState<BackendPage> {
  bool _isImporting = false;
  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    // Initialize with sample data if no existing data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final spreadsheetState = ref.read(simpleSpreadsheetProvider);
      if (spreadsheetState.worksheets.isEmpty) {
        _loadSampleData();
      }
    });
  }



  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Status bar
        if (_isImporting || _isExporting) _buildStatusBar(),

        // Main spreadsheet widget
        Expanded(
          child: SimpleSpreadsheetWidget(
            onCellValueChanged: _onCellValueChanged,
            onCellSelected: _onCellSelected,
            onImportExcel: _importExcelFile,
            onExportExcel: _exportExcelFile,
            onLoadSample: _loadSampleData,
          ),
        ),
      ],
    );
  }

  /// Build status bar for import/export operations
  Widget _buildStatusBar() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            _isImporting ? 'Importing Excel file...' : 'Exporting Excel file...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle cell value changes
  void _onCellValueChanged(String worksheetId, String address, dynamic value) {
    // Update the tool creation state with spreadsheet data
    final spreadsheetState = ref.read(simpleSpreadsheetProvider);
    ref.read(toolCreationProvider.notifier).updateBackendData({
      'spreadsheetData': spreadsheetState.toJson(),
      'lastUpdated': DateTime.now().toIso8601String(),
    });
  }

  /// Handle cell selection
  void _onCellSelected(String worksheetId, String address) {
    // Handle cell selection if needed
  }


  /// Import CSV file (simplified)
  Future<void> _importExcelFile() async {
    if (_isImporting) return;

    setState(() {
      _isImporting = true;
    });

    try {
      // For now, just show a message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('CSV import feature coming soon!'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Import failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isImporting = false;
        });
      }
    }
  }

  /// Export CSV file (simplified)
  Future<void> _exportExcelFile() async {
    if (_isExporting) return;

    setState(() {
      _isExporting = true;
    });

    try {
      // For now, just show a message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('CSV export feature coming soon!'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }





  /// Load sample data for demonstration
  void _loadSampleData() {
    // Create sample data using the simplified spreadsheet system
    final spreadsheetNotifier = ref.read(simpleSpreadsheetProvider.notifier);

    // Clear existing data
    spreadsheetNotifier.clear();

    // Add sample worksheet
    spreadsheetNotifier.addWorksheet('Sample Data');
    final worksheetId = ref.read(simpleSpreadsheetProvider).activeWorksheetId;

    // Add sample data
    final sampleData = [
      ['Product', 'Price', 'Quantity', 'Total'],
      ['Widget A', 10.50, 5, '=B2*C2'],
      ['Widget B', 15.75, 3, '=B3*C3'],
      ['Widget C', 8.25, 7, '=B4*C4'],
      ['Total', '', '', '=SUM(D2:D4)'],
    ];

    for (int row = 0; row < sampleData.length; row++) {
      for (int col = 0; col < sampleData[row].length; col++) {
        final address = String.fromCharCode(65 + col) + (row + 1).toString();
        final value = sampleData[row][col];
        spreadsheetNotifier.updateCell(worksheetId, address, value);
      }
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Sample data loaded successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
