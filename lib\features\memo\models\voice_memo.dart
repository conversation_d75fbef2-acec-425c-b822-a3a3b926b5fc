import 'package:flutter/material.dart';

enum RecordingQuality { low, medium, high }
enum RecordingFormat { aac, mp3, wav }

class VoiceMemo {
  int id = 0;
  String title = '';
  String? description;
  String filePath = '';
  int durationMs = 0;
  double fileSizeBytes = 0;
  String audioFormat = 'aac';
  List<String> tags = [];
  bool isFavorite = false;
  String? transcription;
  Color color = Colors.blue;
  String userId = '';
  String category = 'general';

  // Recording metadata
  RecordingQuality quality = RecordingQuality.medium;
  RecordingFormat format = RecordingFormat.aac;
  double? recordingLatitude;
  double? recordingLongitude;
  String? recordingLocation;

  // Playback state
  bool isPlaying = false;
  Duration currentPosition = Duration.zero;
  double playbackSpeed = 1.0;
  bool isLooping = false;

  // Sharing and export
  bool isShared = false;
  List<String> sharedWith = [];
  String? exportedPath;

  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  VoiceMemo();

  VoiceMemo.create({
    required this.title,
    required this.filePath,
    required this.durationMs,
    required this.fileSizeBytes,
    required this.userId,
    this.description,
    this.audioFormat = 'aac',
    this.isFavorite = false,
    this.color = Colors.blue,
    this.quality = RecordingQuality.medium,
    this.format = RecordingFormat.aac,
    this.category = 'general',
    this.recordingLatitude,
    this.recordingLongitude,
    this.recordingLocation,
    List<String>? tags,
  }) {
    this.tags = tags ?? [];
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void updateMemo({
    String? title,
    String? description,
    bool? isFavorite,
    List<String>? tags,
    String? transcription,
    Color? color,
    String? category,
    String? recordingLocation,
  }) {
    if (title != null) this.title = title;
    if (description != null) this.description = description;
    if (isFavorite != null) this.isFavorite = isFavorite;
    if (tags != null) this.tags = tags;
    if (transcription != null) this.transcription = transcription;
    if (color != null) this.color = color;
    if (category != null) this.category = category;
    if (recordingLocation != null) this.recordingLocation = recordingLocation;

    updateTimestamp();
  }

  // Playback control methods
  void setPlaybackSpeed(double speed) {
    playbackSpeed = speed.clamp(0.5, 2.0);
    updateTimestamp();
  }

  void toggleLoop() {
    isLooping = !isLooping;
    updateTimestamp();
  }

  void updatePlaybackPosition(Duration position) {
    currentPosition = position;
  }

  void startPlayback() {
    isPlaying = true;
  }

  void pausePlayback() {
    isPlaying = false;
  }

  void stopPlayback() {
    isPlaying = false;
    currentPosition = Duration.zero;
  }

  // Sharing methods
  void shareWith(String userId) {
    if (!sharedWith.contains(userId)) {
      sharedWith.add(userId);
      isShared = true;
      updateTimestamp();
    }
  }

  void unshareWith(String userId) {
    if (sharedWith.remove(userId)) {
      isShared = sharedWith.isNotEmpty;
      updateTimestamp();
    }
  }

  void setExportPath(String path) {
    exportedPath = path;
    updateTimestamp();
  }

  // Location methods
  void setRecordingLocation(double latitude, double longitude, String? locationName) {
    recordingLatitude = latitude;
    recordingLongitude = longitude;
    recordingLocation = locationName;
    updateTimestamp();
  }

  bool get hasLocation => recordingLatitude != null && recordingLongitude != null;

  void addTag(String tag) {
    if (!tags.contains(tag)) {
      tags.add(tag);
      updateTimestamp();
    }
  }

  void removeTag(String tag) {
    if (tags.remove(tag)) {
      updateTimestamp();
    }
  }

  void toggleFavorite() {
    isFavorite = !isFavorite;
    updateTimestamp();
  }

  String get formattedDuration {
    final duration = Duration(milliseconds: durationMs);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  String get formattedFileSize {
    if (fileSizeBytes < 1024) {
      return '${fileSizeBytes.toStringAsFixed(0)} B';
    } else if (fileSizeBytes < 1024 * 1024) {
      return '${(fileSizeBytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  bool containsSearchTerm(String searchTerm) {
    final term = searchTerm.toLowerCase();
    return title.toLowerCase().contains(term) ||
           (description?.toLowerCase().contains(term) ?? false) ||
           (transcription?.toLowerCase().contains(term) ?? false) ||
           tags.any((tag) => tag.toLowerCase().contains(term));
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'filePath': filePath,
      'durationMs': durationMs,
      'fileSizeBytes': fileSizeBytes,
      'audioFormat': audioFormat,
      'tags': tags,
      'isFavorite': isFavorite,
      'transcription': transcription,
      'color': color.toARGB32(),
      'quality': quality.name,
      'format': format.name,
      'category': category,
      'recordingLatitude': recordingLatitude,
      'recordingLongitude': recordingLongitude,
      'recordingLocation': recordingLocation,
      'playbackSpeed': playbackSpeed,
      'isLooping': isLooping,
      'isShared': isShared,
      'sharedWith': sharedWith,
      'exportedPath': exportedPath,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory VoiceMemo.fromJson(Map<String, dynamic> json) {
    final memo = VoiceMemo();
    memo.id = json['id'] ?? 0;
    memo.title = json['title'] ?? '';
    memo.description = json['description'];
    memo.filePath = json['filePath'] ?? '';
    memo.durationMs = json['durationMs'] ?? 0;
    memo.fileSizeBytes = (json['fileSizeBytes'] ?? 0).toDouble();
    memo.audioFormat = json['audioFormat'] ?? 'aac';
    memo.tags = List<String>.from(json['tags'] ?? []);
    memo.isFavorite = json['isFavorite'] ?? false;
    memo.transcription = json['transcription'];
    memo.color = Color(json['color'] ?? Colors.blue.toARGB32());
    memo.quality = RecordingQuality.values.firstWhere(
      (q) => q.name == json['quality'],
      orElse: () => RecordingQuality.medium,
    );
    memo.format = RecordingFormat.values.firstWhere(
      (f) => f.name == json['format'],
      orElse: () => RecordingFormat.aac,
    );
    memo.category = json['category'] ?? 'general';
    memo.recordingLatitude = json['recordingLatitude']?.toDouble();
    memo.recordingLongitude = json['recordingLongitude']?.toDouble();
    memo.recordingLocation = json['recordingLocation'];
    memo.playbackSpeed = (json['playbackSpeed'] ?? 1.0).toDouble();
    memo.isLooping = json['isLooping'] ?? false;
    memo.isShared = json['isShared'] ?? false;
    memo.sharedWith = List<String>.from(json['sharedWith'] ?? []);
    memo.exportedPath = json['exportedPath'];
    memo.userId = json['userId'] ?? '';
    memo.createdAt = DateTime.parse(json['createdAt']);
    memo.updatedAt = DateTime.parse(json['updatedAt']);
    memo.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    memo.isDeleted = json['isDeleted'] ?? false;
    memo.syncId = json['syncId'];
    return memo;
  }
}
