import 'package:flutter/material.dart';

/// Theme customization model for app-wide theming
class ThemeCustomization {
  final String id;
  final String name;
  final String description;
  final ColorScheme lightColorScheme;
  final ColorScheme darkColorScheme;
  final TextTheme textTheme;
  final AppBarTheme appBarTheme;
  final CardTheme cardTheme;
  final ElevatedButtonThemeData elevatedButtonTheme;
  final OutlinedButtonThemeData outlinedButtonTheme;
  final TextButtonThemeData textButtonTheme;
  final InputDecorationTheme inputDecorationTheme;
  final FloatingActionButtonThemeData fabTheme;
  final NavigationBarThemeData navigationBarTheme;
  final NavigationRailThemeData navigationRailTheme;
  final DrawerThemeData drawerTheme;
  final BottomNavigationBarThemeData bottomNavTheme;
  final TabBarTheme tabBarTheme;
  final ChipThemeData chipTheme;
  final DialogTheme dialogTheme;
  final SnackBarThemeData snackBarTheme;
  final TooltipThemeData tooltipTheme;
  final PopupMenuThemeData popupMenuTheme;
  final ListTileThemeData listTileTheme;
  final ExpansionTileThemeData expansionTileTheme;
  final SwitchThemeData switchTheme;
  final CheckboxThemeData checkboxTheme;
  final RadioThemeData radioTheme;
  final SliderThemeData sliderTheme;
  final ProgressIndicatorThemeData progressIndicatorTheme;
  final DividerThemeData dividerTheme;
  final bool isCustom;
  final DateTime createdAt;
  final DateTime? modifiedAt;

  const ThemeCustomization({
    required this.id,
    required this.name,
    required this.description,
    required this.lightColorScheme,
    required this.darkColorScheme,
    required this.textTheme,
    required this.appBarTheme,
    required this.cardTheme,
    required this.elevatedButtonTheme,
    required this.outlinedButtonTheme,
    required this.textButtonTheme,
    required this.inputDecorationTheme,
    required this.fabTheme,
    required this.navigationBarTheme,
    required this.navigationRailTheme,
    required this.drawerTheme,
    required this.bottomNavTheme,
    required this.tabBarTheme,
    required this.chipTheme,
    required this.dialogTheme,
    required this.snackBarTheme,
    required this.tooltipTheme,
    required this.popupMenuTheme,
    required this.listTileTheme,
    required this.expansionTileTheme,
    required this.switchTheme,
    required this.checkboxTheme,
    required this.radioTheme,
    required this.sliderTheme,
    required this.progressIndicatorTheme,
    required this.dividerTheme,
    this.isCustom = false,
    required this.createdAt,
    this.modifiedAt,
  });

  factory ThemeCustomization.fromJson(Map<String, dynamic> json) {
    return ThemeCustomization(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      lightColorScheme: _colorSchemeFromJson(json['light_color_scheme'] as Map<String, dynamic>),
      darkColorScheme: _colorSchemeFromJson(json['dark_color_scheme'] as Map<String, dynamic>),
      textTheme: _textThemeFromJson(json['text_theme'] as Map<String, dynamic>),
      appBarTheme: _appBarThemeFromJson(json['app_bar_theme'] as Map<String, dynamic>),
      cardTheme: _cardThemeFromJson(json['card_theme'] as Map<String, dynamic>),
      elevatedButtonTheme: _elevatedButtonThemeFromJson(json['elevated_button_theme'] as Map<String, dynamic>),
      outlinedButtonTheme: _outlinedButtonThemeFromJson(json['outlined_button_theme'] as Map<String, dynamic>),
      textButtonTheme: _textButtonThemeFromJson(json['text_button_theme'] as Map<String, dynamic>),
      inputDecorationTheme: _inputDecorationThemeFromJson(json['input_decoration_theme'] as Map<String, dynamic>),
      fabTheme: _fabThemeFromJson(json['fab_theme'] as Map<String, dynamic>),
      navigationBarTheme: _navigationBarThemeFromJson(json['navigation_bar_theme'] as Map<String, dynamic>),
      navigationRailTheme: _navigationRailThemeFromJson(json['navigation_rail_theme'] as Map<String, dynamic>),
      drawerTheme: _drawerThemeFromJson(json['drawer_theme'] as Map<String, dynamic>),
      bottomNavTheme: _bottomNavThemeFromJson(json['bottom_nav_theme'] as Map<String, dynamic>),
      tabBarTheme: _tabBarThemeFromJson(json['tab_bar_theme'] as Map<String, dynamic>),
      chipTheme: _chipThemeFromJson(json['chip_theme'] as Map<String, dynamic>),
      dialogTheme: _dialogThemeFromJson(json['dialog_theme'] as Map<String, dynamic>),
      snackBarTheme: _snackBarThemeFromJson(json['snack_bar_theme'] as Map<String, dynamic>),
      tooltipTheme: _tooltipThemeFromJson(json['tooltip_theme'] as Map<String, dynamic>),
      popupMenuTheme: _popupMenuThemeFromJson(json['popup_menu_theme'] as Map<String, dynamic>),
      listTileTheme: _listTileThemeFromJson(json['list_tile_theme'] as Map<String, dynamic>),
      expansionTileTheme: _expansionTileThemeFromJson(json['expansion_tile_theme'] as Map<String, dynamic>),
      switchTheme: _switchThemeFromJson(json['switch_theme'] as Map<String, dynamic>),
      checkboxTheme: _checkboxThemeFromJson(json['checkbox_theme'] as Map<String, dynamic>),
      radioTheme: _radioThemeFromJson(json['radio_theme'] as Map<String, dynamic>),
      sliderTheme: _sliderThemeFromJson(json['slider_theme'] as Map<String, dynamic>),
      progressIndicatorTheme: _progressIndicatorThemeFromJson(json['progress_indicator_theme'] as Map<String, dynamic>),
      dividerTheme: _dividerThemeFromJson(json['divider_theme'] as Map<String, dynamic>),
      isCustom: json['is_custom'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      modifiedAt: json['modified_at'] != null ? DateTime.parse(json['modified_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'light_color_scheme': _colorSchemeToJson(lightColorScheme),
      'dark_color_scheme': _colorSchemeToJson(darkColorScheme),
      'text_theme': _textThemeToJson(textTheme),
      'app_bar_theme': _appBarThemeToJson(appBarTheme),
      'card_theme': _cardThemeToJson(cardTheme),
      'elevated_button_theme': _elevatedButtonThemeToJson(elevatedButtonTheme),
      'outlined_button_theme': _outlinedButtonThemeToJson(outlinedButtonTheme),
      'text_button_theme': _textButtonThemeToJson(textButtonTheme),
      'input_decoration_theme': _inputDecorationThemeToJson(inputDecorationTheme),
      'fab_theme': _fabThemeToJson(fabTheme),
      'navigation_bar_theme': _navigationBarThemeToJson(navigationBarTheme),
      'navigation_rail_theme': _navigationRailThemeToJson(navigationRailTheme),
      'drawer_theme': _drawerThemeToJson(drawerTheme),
      'bottom_nav_theme': _bottomNavThemeToJson(bottomNavTheme),
      'tab_bar_theme': _tabBarThemeToJson(tabBarTheme),
      'chip_theme': _chipThemeToJson(chipTheme),
      'dialog_theme': _dialogThemeToJson(dialogTheme),
      'snack_bar_theme': _snackBarThemeToJson(snackBarTheme),
      'tooltip_theme': _tooltipThemeToJson(tooltipTheme),
      'popup_menu_theme': _popupMenuThemeToJson(popupMenuTheme),
      'list_tile_theme': _listTileThemeToJson(listTileTheme),
      'expansion_tile_theme': _expansionTileThemeToJson(expansionTileTheme),
      'switch_theme': _switchThemeToJson(switchTheme),
      'checkbox_theme': _checkboxThemeToJson(checkboxTheme),
      'radio_theme': _radioThemeToJson(radioTheme),
      'slider_theme': _sliderThemeToJson(sliderTheme),
      'progress_indicator_theme': _progressIndicatorThemeToJson(progressIndicatorTheme),
      'divider_theme': _dividerThemeToJson(dividerTheme),
      'is_custom': isCustom,
      'created_at': createdAt.toIso8601String(),
      'modified_at': modifiedAt?.toIso8601String(),
    };
  }

  ThemeData toThemeData({required bool isDark}) {
    final colorScheme = isDark ? darkColorScheme : lightColorScheme;
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: textTheme,
      appBarTheme: appBarTheme,
      cardTheme: cardTheme as CardThemeData?,
      elevatedButtonTheme: elevatedButtonTheme,
      outlinedButtonTheme: outlinedButtonTheme,
      textButtonTheme: textButtonTheme,
      inputDecorationTheme: inputDecorationTheme,
      floatingActionButtonTheme: fabTheme,
      navigationBarTheme: navigationBarTheme,
      navigationRailTheme: navigationRailTheme,
      drawerTheme: drawerTheme,
      bottomNavigationBarTheme: bottomNavTheme,
      tabBarTheme: tabBarTheme as TabBarThemeData?,
      chipTheme: chipTheme,
      dialogTheme: dialogTheme as DialogThemeData?,
      snackBarTheme: snackBarTheme,
      tooltipTheme: tooltipTheme,
      popupMenuTheme: popupMenuTheme,
      listTileTheme: listTileTheme,
      expansionTileTheme: expansionTileTheme,
      switchTheme: switchTheme,
      checkboxTheme: checkboxTheme,
      radioTheme: radioTheme,
      sliderTheme: sliderTheme,
      progressIndicatorTheme: progressIndicatorTheme,
      dividerTheme: dividerTheme,
    );
  }

  ThemeCustomization copyWith({
    String? id,
    String? name,
    String? description,
    ColorScheme? lightColorScheme,
    ColorScheme? darkColorScheme,
    TextTheme? textTheme,
    AppBarTheme? appBarTheme,
    CardTheme? cardTheme,
    ElevatedButtonThemeData? elevatedButtonTheme,
    OutlinedButtonThemeData? outlinedButtonTheme,
    TextButtonThemeData? textButtonTheme,
    InputDecorationTheme? inputDecorationTheme,
    FloatingActionButtonThemeData? fabTheme,
    NavigationBarThemeData? navigationBarTheme,
    NavigationRailThemeData? navigationRailTheme,
    DrawerThemeData? drawerTheme,
    BottomNavigationBarThemeData? bottomNavTheme,
    TabBarTheme? tabBarTheme,
    ChipThemeData? chipTheme,
    DialogTheme? dialogTheme,
    SnackBarThemeData? snackBarTheme,
    TooltipThemeData? tooltipTheme,
    PopupMenuThemeData? popupMenuTheme,
    ListTileThemeData? listTileTheme,
    ExpansionTileThemeData? expansionTileTheme,
    SwitchThemeData? switchTheme,
    CheckboxThemeData? checkboxTheme,
    RadioThemeData? radioTheme,
    SliderThemeData? sliderTheme,
    ProgressIndicatorThemeData? progressIndicatorTheme,
    DividerThemeData? dividerTheme,
    bool? isCustom,
    DateTime? createdAt,
    DateTime? modifiedAt,
  }) {
    return ThemeCustomization(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      lightColorScheme: lightColorScheme ?? this.lightColorScheme,
      darkColorScheme: darkColorScheme ?? this.darkColorScheme,
      textTheme: textTheme ?? this.textTheme,
      appBarTheme: appBarTheme ?? this.appBarTheme,
      cardTheme: cardTheme ?? this.cardTheme,
      elevatedButtonTheme: elevatedButtonTheme ?? this.elevatedButtonTheme,
      outlinedButtonTheme: outlinedButtonTheme ?? this.outlinedButtonTheme,
      textButtonTheme: textButtonTheme ?? this.textButtonTheme,
      inputDecorationTheme: inputDecorationTheme ?? this.inputDecorationTheme,
      fabTheme: fabTheme ?? this.fabTheme,
      navigationBarTheme: navigationBarTheme ?? this.navigationBarTheme,
      navigationRailTheme: navigationRailTheme ?? this.navigationRailTheme,
      drawerTheme: drawerTheme ?? this.drawerTheme,
      bottomNavTheme: bottomNavTheme ?? this.bottomNavTheme,
      tabBarTheme: tabBarTheme ?? this.tabBarTheme,
      chipTheme: chipTheme ?? this.chipTheme,
      dialogTheme: dialogTheme ?? this.dialogTheme,
      snackBarTheme: snackBarTheme ?? this.snackBarTheme,
      tooltipTheme: tooltipTheme ?? this.tooltipTheme,
      popupMenuTheme: popupMenuTheme ?? this.popupMenuTheme,
      listTileTheme: listTileTheme ?? this.listTileTheme,
      expansionTileTheme: expansionTileTheme ?? this.expansionTileTheme,
      switchTheme: switchTheme ?? this.switchTheme,
      checkboxTheme: checkboxTheme ?? this.checkboxTheme,
      radioTheme: radioTheme ?? this.radioTheme,
      sliderTheme: sliderTheme ?? this.sliderTheme,
      progressIndicatorTheme: progressIndicatorTheme ?? this.progressIndicatorTheme,
      dividerTheme: dividerTheme ?? this.dividerTheme,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? DateTime.now(),
    );
  }

  // Helper methods for JSON serialization
  static ColorScheme _colorSchemeFromJson(Map<String, dynamic> json) {
    return ColorScheme(
      brightness: Brightness.values[json['brightness'] as int],
      primary: Color(json['primary'] as int),
      onPrimary: Color(json['on_primary'] as int),
      secondary: Color(json['secondary'] as int),
      onSecondary: Color(json['on_secondary'] as int),
      error: Color(json['error'] as int),
      onError: Color(json['on_error'] as int),
      surface: Color(json['surface'] as int),
      onSurface: Color(json['on_surface'] as int),
    );
  }

  static Map<String, dynamic> _colorSchemeToJson(ColorScheme scheme) {
    return {
      'brightness': scheme.brightness.index,
      'primary': scheme.primary.value,
      'on_primary': scheme.onPrimary.value,
      'secondary': scheme.secondary.value,
      'on_secondary': scheme.onSecondary.value,
      'error': scheme.error.value,
      'on_error': scheme.onError.value,
      'surface': scheme.surface.value,
      'on_surface': scheme.onSurface.value,
    };
  }

  // Placeholder implementations for other theme serialization methods
  static TextTheme _textThemeFromJson(Map<String, dynamic> json) => const TextTheme();
  static Map<String, dynamic> _textThemeToJson(TextTheme theme) => {};
  static AppBarTheme _appBarThemeFromJson(Map<String, dynamic> json) => const AppBarTheme();
  static Map<String, dynamic> _appBarThemeToJson(AppBarTheme theme) => {};
  static CardTheme _cardThemeFromJson(Map<String, dynamic> json) => const CardTheme();
  static Map<String, dynamic> _cardThemeToJson(CardTheme theme) => {};
  static ElevatedButtonThemeData _elevatedButtonThemeFromJson(Map<String, dynamic> json) => const ElevatedButtonThemeData();
  static Map<String, dynamic> _elevatedButtonThemeToJson(ElevatedButtonThemeData theme) => {};
  static OutlinedButtonThemeData _outlinedButtonThemeFromJson(Map<String, dynamic> json) => const OutlinedButtonThemeData();
  static Map<String, dynamic> _outlinedButtonThemeToJson(OutlinedButtonThemeData theme) => {};
  static TextButtonThemeData _textButtonThemeFromJson(Map<String, dynamic> json) => const TextButtonThemeData();
  static Map<String, dynamic> _textButtonThemeToJson(TextButtonThemeData theme) => {};
  static InputDecorationTheme _inputDecorationThemeFromJson(Map<String, dynamic> json) => const InputDecorationTheme();
  static Map<String, dynamic> _inputDecorationThemeToJson(InputDecorationTheme theme) => {};
  static FloatingActionButtonThemeData _fabThemeFromJson(Map<String, dynamic> json) => const FloatingActionButtonThemeData();
  static Map<String, dynamic> _fabThemeToJson(FloatingActionButtonThemeData theme) => {};
  static NavigationBarThemeData _navigationBarThemeFromJson(Map<String, dynamic> json) => const NavigationBarThemeData();
  static Map<String, dynamic> _navigationBarThemeToJson(NavigationBarThemeData theme) => {};
  static NavigationRailThemeData _navigationRailThemeFromJson(Map<String, dynamic> json) => const NavigationRailThemeData();
  static Map<String, dynamic> _navigationRailThemeToJson(NavigationRailThemeData theme) => {};
  static DrawerThemeData _drawerThemeFromJson(Map<String, dynamic> json) => const DrawerThemeData();
  static Map<String, dynamic> _drawerThemeToJson(DrawerThemeData theme) => {};
  static BottomNavigationBarThemeData _bottomNavThemeFromJson(Map<String, dynamic> json) => const BottomNavigationBarThemeData();
  static Map<String, dynamic> _bottomNavThemeToJson(BottomNavigationBarThemeData theme) => {};
  static TabBarTheme _tabBarThemeFromJson(Map<String, dynamic> json) => const TabBarTheme();
  static Map<String, dynamic> _tabBarThemeToJson(TabBarTheme theme) => {};
  static ChipThemeData _chipThemeFromJson(Map<String, dynamic> json) => const ChipThemeData();
  static Map<String, dynamic> _chipThemeToJson(ChipThemeData theme) => {};
  static DialogTheme _dialogThemeFromJson(Map<String, dynamic> json) => const DialogTheme();
  static Map<String, dynamic> _dialogThemeToJson(DialogTheme theme) => {};
  static SnackBarThemeData _snackBarThemeFromJson(Map<String, dynamic> json) => const SnackBarThemeData();
  static Map<String, dynamic> _snackBarThemeToJson(SnackBarThemeData theme) => {};
  static TooltipThemeData _tooltipThemeFromJson(Map<String, dynamic> json) => const TooltipThemeData();
  static Map<String, dynamic> _tooltipThemeToJson(TooltipThemeData theme) => {};
  static PopupMenuThemeData _popupMenuThemeFromJson(Map<String, dynamic> json) => const PopupMenuThemeData();
  static Map<String, dynamic> _popupMenuThemeToJson(PopupMenuThemeData theme) => {};
  static ListTileThemeData _listTileThemeFromJson(Map<String, dynamic> json) => const ListTileThemeData();
  static Map<String, dynamic> _listTileThemeToJson(ListTileThemeData theme) => {};
  static ExpansionTileThemeData _expansionTileThemeFromJson(Map<String, dynamic> json) => const ExpansionTileThemeData();
  static Map<String, dynamic> _expansionTileThemeToJson(ExpansionTileThemeData theme) => {};
  static SwitchThemeData _switchThemeFromJson(Map<String, dynamic> json) => const SwitchThemeData();
  static Map<String, dynamic> _switchThemeToJson(SwitchThemeData theme) => {};
  static CheckboxThemeData _checkboxThemeFromJson(Map<String, dynamic> json) => const CheckboxThemeData();
  static Map<String, dynamic> _checkboxThemeToJson(CheckboxThemeData theme) => {};
  static RadioThemeData _radioThemeFromJson(Map<String, dynamic> json) => const RadioThemeData();
  static Map<String, dynamic> _radioThemeToJson(RadioThemeData theme) => {};
  static SliderThemeData _sliderThemeFromJson(Map<String, dynamic> json) => const SliderThemeData();
  static Map<String, dynamic> _sliderThemeToJson(SliderThemeData theme) => {};
  static ProgressIndicatorThemeData _progressIndicatorThemeFromJson(Map<String, dynamic> json) => const ProgressIndicatorThemeData();
  static Map<String, dynamic> _progressIndicatorThemeToJson(ProgressIndicatorThemeData theme) => {};
  static DividerThemeData _dividerThemeFromJson(Map<String, dynamic> json) => const DividerThemeData();
  static Map<String, dynamic> _dividerThemeToJson(DividerThemeData theme) => {};
}
