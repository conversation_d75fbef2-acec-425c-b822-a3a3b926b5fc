import 'dart:convert';
import 'dart:io';
import 'package:excel/excel.dart';
import '../models/spreadsheet_cell.dart';
import '../models/ui_component.dart';

/// Service for parsing Excel files and converting them to Tool Builder format
class ExcelParserService {
  static final ExcelParserService _instance = ExcelParserService._internal();
  factory ExcelParserService() => _instance;
  ExcelParserService._internal();

  /// Parse an Excel file and convert to spreadsheet data
  Future<Map<String, dynamic>> parseExcelFile(String filePath) async {
    try {
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      final result = <String, dynamic>{
        'sheets': <String, Map<String, SpreadsheetCell>>{},
        'metadata': {
          'fileName': filePath.split('/').last,
          'sheetNames': excel.tables.keys.toList(),
          'parsedAt': DateTime.now().toIso8601String(),
        },
      };

      // Parse each sheet
      for (final sheetName in excel.tables.keys) {
        final sheet = excel.tables[sheetName];
        if (sheet == null) continue;

        final cells = <String, SpreadsheetCell>{};

        // Iterate through all cells in the sheet
        for (int row = 0; row < sheet.maxRows; row++) {
          for (int col = 0; col < sheet.maxColumns; col++) {
            final cell = sheet.cell(CellIndex.indexByColumnRow(
              columnIndex: col,
              rowIndex: row,
            ));

            if (cell.value != null) {
              final address = SpreadsheetUtils.getCellAddress(row + 1, col + 1);
              final cellValue = _extractCellValue(cell);
              final cellType = _determineCellType(cellValue);

              cells[address] = SpreadsheetCell(
                address: address,
                value: cellValue,
                formula: cell.value is FormulaCellValue ? (cell.value as FormulaCellValue).formula : null,
                type: cellType,
              );
            }
          }
        }

        result['sheets'][sheetName] = cells;
      }

      return result;
    } catch (e) {
      throw Exception('Failed to parse Excel file: $e');
    }
  }

  /// Suggest UI components based on Excel data structure
  List<UIComponent> suggestUIFromHeaders(List<String> headers) {
    final components = <UIComponent>[];
    int componentIndex = 0;

    for (int i = 0; i < headers.length; i++) {
      final header = headers[i].toLowerCase().trim();
      final cellAddress = SpreadsheetUtils.getCellAddress(2, i + 1); // Data row

      // Determine component type based on header content
      ComponentType componentType;
      Map<String, dynamic> properties = {};

      if (_isInputField(header)) {
        componentType = ComponentType.textField;
        properties = {
          'placeholder': 'Enter ${headers[i]}',
          'required': true,
        };
      } else if (_isOutputField(header)) {
        componentType = ComponentType.outputField;
        properties = {
          'readonly': true,
          'format': _getOutputFormat(header),
        };
      } else if (_isToggleField(header)) {
        componentType = ComponentType.toggle;
        properties = {
          'defaultValue': false,
        };
      } else if (_isDropdownField(header)) {
        componentType = ComponentType.dropdown;
        properties = {
          'options': _getDropdownOptions(header),
        };
      } else {
        componentType = ComponentType.label;
        properties = {
          'text': headers[i],
        };
      }

      components.add(UIComponent(
        id: 'component_$componentIndex',
        type: componentType,
        label: headers[i],
        bindToCell: cellAddress,
        properties: properties,
        position: ComponentPosition(
          x: 20,
          y: 20 + (componentIndex * 80),
          width: 200,
          height: 40,
        ),
      ));

      componentIndex++;
    }

    return components;
  }

  /// Convert Excel data to spreadsheet cell format
  Map<String, SpreadsheetCell> convertToSpreadsheetData(Map<String, dynamic> excelData) {
    final cells = <String, SpreadsheetCell>{};

    try {
      final sheets = excelData['sheets'] as Map<String, dynamic>;
      
      // For now, use the first sheet
      if (sheets.isNotEmpty) {
        final firstSheetName = sheets.keys.first;
        final sheetData = sheets[firstSheetName] as Map<String, SpreadsheetCell>;
        cells.addAll(sheetData);
      }

      return cells;
    } catch (e) {
      return cells;
    }
  }

  /// Extract headers from Excel data
  List<String> extractHeaders(Map<String, dynamic> excelData) {
    try {
      final sheets = excelData['sheets'] as Map<String, dynamic>;
      if (sheets.isEmpty) return [];

      final firstSheetName = sheets.keys.first;
      final sheetData = sheets[firstSheetName] as Map<String, SpreadsheetCell>;

      final headers = <String>[];
      
      // Look for headers in the first row
      for (int col = 1; col <= 26; col++) { // A-Z columns
        final address = SpreadsheetUtils.getCellAddress(1, col);
        final cell = sheetData[address];
        
        if (cell != null && cell.value != null) {
          headers.add(cell.value.toString());
        } else {
          break; // Stop when we hit an empty cell
        }
      }

      return headers;
    } catch (e) {
      return [];
    }
  }

  /// Generate a complete tool definition from Excel file
  Future<Map<String, dynamic>> generateToolFromExcel(
    String filePath,
    String toolName,
    String toolDescription,
  ) async {
    try {
      final excelData = await parseExcelFile(filePath);
      final headers = extractHeaders(excelData);
      final spreadsheetData = convertToSpreadsheetData(excelData);
      final suggestedComponents = suggestUIFromHeaders(headers);

      return {
        'backendData': jsonEncode({
          'cells': spreadsheetData.map((key, value) => MapEntry(key, value.toJson())),
          'sheets': ['Sheet1'],
          'activeSheet': 'Sheet1',
        }),
        'uiLayout': jsonEncode({
          'components': suggestedComponents.map((c) => c.toJson()).toList(),
          'layout': 'vertical',
          'theme': 'default',
        }),
        'metadata': {
          'generatedFrom': filePath.split('/').last,
          'headers': headers,
          'cellCount': spreadsheetData.length,
        },
      };
    } catch (e) {
      throw Exception('Failed to generate tool from Excel: $e');
    }
  }

  /// Private helper methods

  dynamic _extractCellValue(dynamic cell) {
    if (cell.value == null) return null;

    // Handle different cell value types
    if (cell.value is TextCellValue) {
      return (cell.value as TextCellValue).value;
    } else if (cell.value is IntCellValue) {
      return (cell.value as IntCellValue).value;
    } else if (cell.value is DoubleCellValue) {
      return (cell.value as DoubleCellValue).value;
    } else if (cell.value is BoolCellValue) {
      return (cell.value as BoolCellValue).value;
    } else if (cell.value is DateCellValue) {
      return (cell.value as DateCellValue).asDateTimeLocal();
    } else if (cell.value is TimeCellValue) {
      final timeValue = cell.value as TimeCellValue;
      return '${timeValue.hour}:${timeValue.minute}:${timeValue.second}';
    } else if (cell.value is FormulaCellValue) {
      return (cell.value as FormulaCellValue).formula;
    }

    return cell.value.toString();
  }

  CellType _determineCellType(dynamic value) {
    if (value == null) return CellType.text;
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    if (value is String && value.startsWith('=')) return CellType.formula;
    return CellType.text;
  }

  bool _isInputField(String header) {
    final inputKeywords = [
      'input', 'enter', 'name', 'email', 'phone', 'address',
      'age', 'quantity', 'amount', 'value', 'price', 'cost'
    ];
    return inputKeywords.any((keyword) => header.contains(keyword));
  }

  bool _isOutputField(String header) {
    final outputKeywords = [
      'total', 'result', 'output', 'calculated', 'sum',
      'average', 'percentage', 'final', 'balance'
    ];
    return outputKeywords.any((keyword) => header.contains(keyword));
  }

  bool _isToggleField(String header) {
    final toggleKeywords = [
      'enabled', 'active', 'visible', 'checked', 'selected',
      'yes/no', 'true/false', 'on/off'
    ];
    return toggleKeywords.any((keyword) => header.contains(keyword));
  }

  bool _isDropdownField(String header) {
    final dropdownKeywords = [
      'category', 'type', 'status', 'priority', 'level',
      'grade', 'rating', 'option', 'choice'
    ];
    return dropdownKeywords.any((keyword) => header.contains(keyword));
  }

  String _getOutputFormat(String header) {
    if (header.contains('currency') || header.contains('price') || header.contains('cost')) {
      return 'currency';
    } else if (header.contains('percentage') || header.contains('%')) {
      return 'percentage';
    } else if (header.contains('date')) {
      return 'date';
    } else if (header.contains('time')) {
      return 'time';
    }
    return 'text';
  }

  List<String> _getDropdownOptions(String header) {
    // Provide default options based on common field types
    if (header.contains('priority')) {
      return ['Low', 'Medium', 'High', 'Critical'];
    } else if (header.contains('status')) {
      return ['Active', 'Inactive', 'Pending', 'Completed'];
    } else if (header.contains('category')) {
      return ['Category 1', 'Category 2', 'Category 3'];
    } else if (header.contains('type')) {
      return ['Type A', 'Type B', 'Type C'];
    }
    return ['Option 1', 'Option 2', 'Option 3'];
  }
}
