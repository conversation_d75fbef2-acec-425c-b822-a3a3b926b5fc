import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/quran_provider.dart';
import '../models/quran_models.dart';

/// Authentic Quran reader screen with real Arabic text
class AuthenticQuranReaderScreen extends ConsumerStatefulWidget {
  const AuthenticQuranReaderScreen({super.key});

  @override
  ConsumerState<AuthenticQuranReaderScreen> createState() => _AuthenticQuranReaderScreenState();
}

class _AuthenticQuranReaderScreenState extends ConsumerState<AuthenticQuranReaderScreen> {
  int _currentSurah = 1;
  int _currentVerse = 1;
  bool _isSearchMode = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quranLoadingAsync = ref.watch(quranDataLoadingProvider);
    final allSurahs = ref.watch(allSurahsProvider);
    final searchResults = ref.watch(quranSearchResultsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('القرآن الكريم'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        actions: [
          IconButton(
            onPressed: () {
              setState(() {
                _isSearchMode = !_isSearchMode;
              });
            },
            icon: Icon(_isSearchMode ? Icons.close : Icons.search),
          ),
          IconButton(
            onPressed: _showSurahSelector,
            icon: const Icon(Icons.list),
          ),
        ],
      ),
      body: quranLoadingAsync.when(
        data: (isLoaded) {
          if (!isLoaded) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading Quran data...'),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Search bar
              if (_isSearchMode) _buildSearchBar(),
              
              // Content
              Expanded(
                child: _isSearchMode
                    ? _buildSearchResults(searchResults)
                    : _buildQuranReader(allSurahs),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading Quran: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(quranDataLoadingProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: 'Search in Quran...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: (value) {
          ref.read(quranSearchProvider.notifier).state = value;
        },
      ),
    );
  }

  Widget _buildSearchResults(AsyncValue<List<SearchResult>> searchResultsAsync) {
    return searchResultsAsync.when(
      data: (results) {
        if (results.isEmpty) {
          return const Center(
            child: Text('No results found'),
          );
        }

        return ListView.builder(
          itemCount: results.length,
          itemBuilder: (context, index) {
            final result = results[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: ListTile(
                title: Text(
                  '${result.surah.name} - آية ${result.verse.number}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(
                  result.verse.arabicText,
                  style: const TextStyle(
                    fontSize: 18,
                    fontFamily: 'Amiri',
                  ),
                  textDirection: TextDirection.rtl,
                ),
                onTap: () {
                  setState(() {
                    _currentSurah = result.surah.number;
                    _currentVerse = result.verse.number;
                    _isSearchMode = false;
                  });
                },
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildQuranReader(List<Surah> surahs) {
    if (surahs.isEmpty) {
      return const Center(child: Text('No Quran data available'));
    }

    final currentSurah = surahs.firstWhere(
      (s) => s.info.number == _currentSurah,
      orElse: () => surahs.first,
    );

    return Column(
      children: [
        // Surah header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
          ),
          child: Column(
            children: [
              Text(
                currentSurah.info.name,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Amiri',
                ),
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 4),
              Text(
                '${currentSurah.info.englishName} - ${currentSurah.info.verseCount} verses',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),

        // Verses
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: currentSurah.verses.length,
            itemBuilder: (context, index) {
              final verse = currentSurah.verses[index];
              final isCurrentVerse = verse.number == _currentVerse;

              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isCurrentVerse
                      ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
                      : null,
                  borderRadius: BorderRadius.circular(12),
                  border: isCurrentVerse
                      ? Border.all(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : null,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Verse number
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${verse.number}',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => _toggleBookmark(verse),
                          icon: Icon(
                            _isBookmarked(verse) ? Icons.bookmark : Icons.bookmark_border,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    // Arabic text
                    Text(
                      verse.arabicText,
                      style: const TextStyle(
                        fontSize: 24,
                        height: 2.0,
                        fontFamily: 'Amiri',
                      ),
                      textDirection: TextDirection.rtl,
                      textAlign: TextAlign.justify,
                    ),
                  ],
                ),
              );
            },
          ),
        ),

        // Navigation controls
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: _canGoPrevious() ? _previousVerse : null,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Previous'),
              ),
              ElevatedButton.icon(
                onPressed: _canGoNext(surahs) ? _nextVerse : null,
                icon: const Icon(Icons.arrow_forward),
                label: const Text('Next'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showSurahSelector() {
    final surahs = ref.read(allSurahsProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Surah'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: surahs.length,
            itemBuilder: (context, index) {
              final surah = surahs[index];
              return ListTile(
                leading: CircleAvatar(
                  child: Text('${surah.info.number}'),
                ),
                title: Text(surah.info.name),
                subtitle: Text('${surah.info.englishName} - ${surah.info.verseCount} verses'),
                onTap: () {
                  setState(() {
                    _currentSurah = surah.info.number;
                    _currentVerse = 1;
                  });
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  bool _canGoPrevious() {
    return _currentVerse > 1 || _currentSurah > 1;
  }

  bool _canGoNext(List<Surah> surahs) {
    final currentSurah = surahs.firstWhere((s) => s.info.number == _currentSurah);
    return _currentVerse < currentSurah.verses.length || _currentSurah < surahs.length;
  }

  void _previousVerse() {
    final surahs = ref.read(allSurahsProvider);
    if (_currentVerse > 1) {
      setState(() {
        _currentVerse--;
      });
    } else if (_currentSurah > 1) {
      final previousSurah = surahs.firstWhere((s) => s.info.number == _currentSurah - 1);
      setState(() {
        _currentSurah--;
        _currentVerse = previousSurah.verses.length;
      });
    }
  }

  void _nextVerse() {
    final surahs = ref.read(allSurahsProvider);
    final currentSurah = surahs.firstWhere((s) => s.info.number == _currentSurah);
    
    if (_currentVerse < currentSurah.verses.length) {
      setState(() {
        _currentVerse++;
      });
    } else if (_currentSurah < surahs.length) {
      setState(() {
        _currentSurah++;
        _currentVerse = 1;
      });
    }
  }

  bool _isBookmarked(Verse verse) {
    final bookmarks = ref.read(quranBookmarksProvider);
    return bookmarks.any((b) => 
      b.surahNumber == verse.surahNumber && b.verseNumber == verse.number
    );
  }

  void _toggleBookmark(Verse verse) {
    final bookmarksNotifier = ref.read(quranBookmarksProvider.notifier);
    final isBookmarked = _isBookmarked(verse);
    
    if (isBookmarked) {
      bookmarksNotifier.deleteBookmark(verse.surahNumber, verse.number);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Bookmark removed')),
      );
    } else {
      final surahs = ref.read(allSurahsProvider);
      final surah = surahs.firstWhere((s) => s.info.number == verse.surahNumber);
      
      final bookmark = QuranBookmark(
        surahNumber: verse.surahNumber,
        verseNumber: verse.number,
        surahName: surah.info.name,
        verseText: verse.arabicText,
        createdAt: DateTime.now(),
      );
      
      bookmarksNotifier.addBookmark(bookmark);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Bookmark added')),
      );
    }
  }
}
