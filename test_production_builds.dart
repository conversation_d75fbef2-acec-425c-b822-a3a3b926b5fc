// Production Build Validation Test
import 'dart:io';

void main() async {
  print('=== ShadowSuite Production Build Validation ===');
  print('Testing build artifacts and functionality...\n');
  
  try {
    // Test 1: Verify build artifacts exist
    print('Test 1: Build Artifacts Verification');
    await testBuildArtifacts();
    print('✅ Build artifacts verification passed\n');
    
    // Test 2: Verify file sizes are reasonable
    print('Test 2: Build Size Validation');
    await testBuildSizes();
    print('✅ Build size validation passed\n');
    
    // Test 3: Verify Windows executable properties
    print('Test 3: Windows Executable Validation');
    await testWindowsExecutable();
    print('✅ Windows executable validation passed\n');
    
    // Test 4: Verify Android APK properties
    print('Test 4: Android APK Validation');
    await testAndroidAPK();
    print('✅ Android APK validation passed\n');
    
    // Test 5: Verify all required DLLs are present
    print('Test 5: Dependencies Validation');
    await testDependencies();
    print('✅ Dependencies validation passed\n');
    
    print('🎉 ALL PRODUCTION BUILD TESTS PASSED!');
    print('ShadowSuite is ready for production deployment.');
    print('\n📦 Build Artifacts:');
    print('   Windows: build\\windows\\x64\\runner\\Debug\\shadowsuite.exe');
    print('   Android: build\\app\\outputs\\flutter-apk\\app-debug.apk');
    
  } catch (e, stackTrace) {
    print('❌ Production build test failed: $e');
    print('Stack trace: $stackTrace');
    exit(1);
  }
}

Future<void> testBuildArtifacts() async {
  // Check Windows executable
  final windowsExe = File('build/windows/x64/runner/Debug/shadowsuite.exe');
  assert(windowsExe.existsSync(), 'Windows executable not found');
  
  // Check Android APK
  final androidApk = File('build/app/outputs/flutter-apk/app-debug.apk');
  assert(androidApk.existsSync(), 'Android APK not found');
  
  // Check Windows data folder
  final windowsData = Directory('build/windows/x64/runner/Debug/data');
  assert(windowsData.existsSync(), 'Windows data folder not found');
  
  print('   ✓ Windows executable exists');
  print('   ✓ Android APK exists');
  print('   ✓ Windows data folder exists');
}

Future<void> testBuildSizes() async {
  // Check Windows executable size (should be reasonable, not empty)
  final windowsExe = File('build/windows/x64/runner/Debug/shadowsuite.exe');
  final windowsSize = windowsExe.lengthSync();
  assert(windowsSize > 1000000, 'Windows executable too small: $windowsSize bytes'); // At least 1MB
  
  // Check Android APK size
  final androidApk = File('build/app/outputs/flutter-apk/app-debug.apk');
  final androidSize = androidApk.lengthSync();
  assert(androidSize > 10000000, 'Android APK too small: $androidSize bytes'); // At least 10MB
  
  print('   ✓ Windows executable size: ${(windowsSize / 1024 / 1024).toStringAsFixed(1)} MB');
  print('   ✓ Android APK size: ${(androidSize / 1024 / 1024).toStringAsFixed(1)} MB');
}

Future<void> testWindowsExecutable() async {
  final windowsExe = File('build/windows/x64/runner/Debug/shadowsuite.exe');
  
  // Check if it's a valid PE executable (starts with MZ)
  final bytes = windowsExe.readAsBytesSync();
  assert(bytes.length > 2, 'Executable file is too small');
  assert(bytes[0] == 0x4D && bytes[1] == 0x5A, 'Not a valid Windows executable (missing MZ header)');
  
  // Check for Flutter DLL
  final flutterDll = File('build/windows/x64/runner/Debug/flutter_windows.dll');
  assert(flutterDll.existsSync(), 'Flutter Windows DLL not found');
  
  print('   ✓ Valid Windows PE executable');
  print('   ✓ Flutter Windows DLL present');
}

Future<void> testAndroidAPK() async {
  final androidApk = File('build/app/outputs/flutter-apk/app-debug.apk');
  
  // Check if it's a valid ZIP/APK file (starts with PK)
  final bytes = androidApk.readAsBytesSync();
  assert(bytes.length > 2, 'APK file is too small');
  assert(bytes[0] == 0x50 && bytes[1] == 0x4B, 'Not a valid APK file (missing PK header)');
  
  // Check SHA1 file exists
  final sha1File = File('build/app/outputs/flutter-apk/app-debug.apk.sha1');
  assert(sha1File.existsSync(), 'APK SHA1 file not found');
  
  print('   ✓ Valid Android APK file');
  print('   ✓ APK SHA1 checksum present');
}

Future<void> testDependencies() async {
  final debugDir = Directory('build/windows/x64/runner/Debug');
  final requiredDlls = [
    'flutter_windows.dll',
    'audioplayers_windows_plugin.dll',
    'connectivity_plus_plugin.dll',
    'file_selector_windows_plugin.dll',
    'local_auth_windows_plugin.dll',
    'permission_handler_windows_plugin.dll',
    'share_plus_plugin.dll',
    'url_launcher_windows_plugin.dll',
  ];
  
  for (final dll in requiredDlls) {
    final dllFile = File('${debugDir.path}/$dll');
    assert(dllFile.existsSync(), 'Required DLL not found: $dll');
  }
  
  // Check data folder has Flutter assets
  final dataDir = Directory('build/windows/x64/runner/Debug/data');
  final flutterAssets = Directory('${dataDir.path}/flutter_assets');
  assert(flutterAssets.existsSync(), 'Flutter assets not found');
  
  print('   ✓ All required DLLs present (${requiredDlls.length} files)');
  print('   ✓ Flutter assets present');
}
