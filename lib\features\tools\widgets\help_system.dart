import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Comprehensive help system with tooltips, tutorials, and guidance
class HelpSystem {
  static final HelpSystem _instance = HelpSystem._internal();
  factory HelpSystem() => _instance;
  HelpSystem._internal();

  /// Show contextual help for a specific feature
  static void showContextualHelp(
    BuildContext context,
    String feature, {
    Widget? customContent,
  }) {
    showDialog(
      context: context,
      builder: (context) => HelpDialog(
        feature: feature,
        customContent: customContent,
      ),
    );
  }

  /// Show quick tour of the Tool Builder
  static void showQuickTour(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const QuickTourDialog(),
    );
  }

  /// Show keyboard shortcuts
  static void showKeyboardShortcuts(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const KeyboardShortcutsDialog(),
    );
  }

  /// Get help content for a specific feature
  static HelpContent getHelpContent(String feature) {
    return _helpContent[feature] ?? HelpContent.empty();
  }

  /// All help content organized by feature
  static final Map<String, HelpContent> _helpContent = {
    'component_palette': HelpContent(
      title: 'Component Palette',
      description: 'Drag and drop components to build your app interface',
      steps: [
        'Browse available components in the left panel',
        'Drag a component to the canvas to add it',
        'Components are organized by category (Input, Output, Layout, etc.)',
        'Each component has specific properties you can customize',
      ],
      tips: [
        'Use Ctrl+Z to undo component placement',
        'Double-click a component to quickly add it to the center',
        'Right-click components for additional options',
      ],
    ),
    'canvas': HelpContent(
      title: 'Design Canvas',
      description: 'The main area where you design your app layout',
      steps: [
        'Drop components from the palette onto the canvas',
        'Click components to select and edit them',
        'Drag components to reposition them',
        'Use the handles to resize selected components',
      ],
      tips: [
        'Hold Shift while dragging to constrain movement',
        'Use the grid for precise alignment',
        'Press Delete to remove selected components',
      ],
    ),
    'properties_panel': HelpContent(
      title: 'Properties Panel',
      description: 'Customize the selected component\'s appearance and behavior',
      steps: [
        'Select a component to see its properties',
        'Use the Component tab for basic settings',
        'Switch to Style tab for visual customization',
        'Theme tab controls overall app appearance',
      ],
      tips: [
        'Changes are applied immediately',
        'Use the Global tab for app-wide settings',
        'Color pickers support hex codes and visual selection',
      ],
    ),
    'formula_engine': HelpContent(
      title: 'Formula Engine',
      description: 'Connect your components to Excel-like calculations',
      steps: [
        'Bind components to spreadsheet cells',
        'Use formulas like =SUM(A1:A10) for calculations',
        'Output components display formula results',
        'Input components update cell values',
      ],
      tips: [
        'Over 300 Excel functions are supported',
        'Use cell references like A1, B2, etc.',
        'Formulas update automatically when inputs change',
      ],
    ),
    'mobile_themes': HelpContent(
      title: 'Mobile App Themes',
      description: 'Choose themes that match your target platform',
      steps: [
        'Select from Material Android, iOS, Web, or Dark themes',
        'Preview your app on different device sizes',
        'Customize colors to match your brand',
        'Export generates platform-appropriate code',
      ],
      tips: [
        'Material theme works best for Android apps',
        'iOS theme follows Apple design guidelines',
        'Web theme is optimized for browser deployment',
      ],
    ),
    'export_system': HelpContent(
      title: 'App Export',
      description: 'Generate production-ready Flutter applications',
      steps: [
        'Complete your app design and testing',
        'Choose export format (Flutter project, APK config, Web)',
        'Configure app name and metadata',
        'Download the generated project files',
      ],
      tips: [
        'Test your app thoroughly before exporting',
        'Generated code is clean and maintainable',
        'Include debug info for development builds',
      ],
    ),
  };
}

/// Help content data structure
class HelpContent {
  final String title;
  final String description;
  final List<String> steps;
  final List<String> tips;
  final String? videoUrl;
  final List<String>? relatedFeatures;

  const HelpContent({
    required this.title,
    required this.description,
    this.steps = const [],
    this.tips = const [],
    this.videoUrl,
    this.relatedFeatures,
  });

  factory HelpContent.empty() {
    return const HelpContent(
      title: 'Help Not Available',
      description: 'No help content found for this feature.',
    );
  }
}

/// Help dialog widget
class HelpDialog extends StatelessWidget {
  final String feature;
  final Widget? customContent;

  const HelpDialog({
    super.key,
    required this.feature,
    this.customContent,
  });

  @override
  Widget build(BuildContext context) {
    final content = HelpSystem.getHelpContent(feature);
    
    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.help,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      content.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: customContent ?? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Description
                    Text(
                      content.description,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    
                    if (content.steps.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      Text(
                        'How to use:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...content.steps.asMap().entries.map((entry) => 
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.primary,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    '${entry.key + 1}',
                                    style: TextStyle(
                                      color: Theme.of(context).colorScheme.onPrimary,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  entry.value,
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    if (content.tips.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.lightbulb,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Pro Tips',
                                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            ...content.tips.map((tip) => 
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 2),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Icon(
                                      Icons.arrow_right,
                                      size: 16,
                                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                                    ),
                                    const SizedBox(width: 4),
                                    Expanded(
                                      child: Text(
                                        tip,
                                        style: Theme.of(context).textTheme.bodySmall,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(12)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () => HelpSystem.showQuickTour(context),
                    icon: const Icon(Icons.tour),
                    label: const Text('Quick Tour'),
                  ),
                  Row(
                    children: [
                      TextButton.icon(
                        onPressed: () => HelpSystem.showKeyboardShortcuts(context),
                        icon: const Icon(Icons.keyboard),
                        label: const Text('Shortcuts'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Got it'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Quick tour dialog
class QuickTourDialog extends StatefulWidget {
  const QuickTourDialog({super.key});

  @override
  State<QuickTourDialog> createState() => _QuickTourDialogState();
}

class _QuickTourDialogState extends State<QuickTourDialog> {
  int _currentStep = 0;
  final PageController _pageController = PageController();

  final List<TourStep> _tourSteps = [
    TourStep(
      title: 'Welcome to Tool Builder',
      description: 'Create professional mobile and desktop apps from spreadsheets with drag-and-drop simplicity.',
      icon: Icons.rocket_launch,
    ),
    TourStep(
      title: 'Component Palette',
      description: 'Drag components from the left panel to build your app interface. Choose from input fields, buttons, charts, and more.',
      icon: Icons.widgets,
    ),
    TourStep(
      title: 'Design Canvas',
      description: 'Drop components here and arrange them to create your app layout. Click to select, drag to move, resize with handles.',
      icon: Icons.design_services,
    ),
    TourStep(
      title: 'Properties Panel',
      description: 'Customize selected components with the properties panel. Change colors, text, behavior, and more.',
      icon: Icons.settings,
    ),
    TourStep(
      title: 'Excel Integration',
      description: 'Bind components to spreadsheet cells and use 300+ Excel functions for powerful calculations.',
      icon: Icons.functions,
    ),
    TourStep(
      title: 'Mobile Themes',
      description: 'Choose from Android, iOS, Web, or Dark themes. Preview on different devices and export production-ready code.',
      icon: Icons.phone_android,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        height: 400,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primaryContainer,
                  ],
                ),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.tour,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Quick Tour',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${_currentStep + 1} of ${_tourSteps.length}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) => setState(() => _currentStep = index),
                itemCount: _tourSteps.length,
                itemBuilder: (context, index) {
                  final step = _tourSteps[index];
                  return Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primaryContainer,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            step.icon,
                            size: 40,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const SizedBox(height: 24),
                        Text(
                          step.title,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          step.description,
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Progress indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  _tourSteps.length,
                  (index) => Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: index == _currentStep
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ),
            ),

            // Navigation
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Skip'),
                  ),
                  Row(
                    children: [
                      if (_currentStep > 0)
                        TextButton(
                          onPressed: () {
                            _pageController.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          },
                          child: const Text('Previous'),
                        ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          if (_currentStep < _tourSteps.length - 1) {
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          } else {
                            Navigator.of(context).pop();
                          }
                        },
                        child: Text(_currentStep < _tourSteps.length - 1 ? 'Next' : 'Finish'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Keyboard shortcuts dialog
class KeyboardShortcutsDialog extends StatelessWidget {
  const KeyboardShortcutsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final shortcuts = [
      KeyboardShortcut('Ctrl + Z', 'Undo last action'),
      KeyboardShortcut('Ctrl + Y', 'Redo last action'),
      KeyboardShortcut('Ctrl + S', 'Save project'),
      KeyboardShortcut('Ctrl + C', 'Copy selected component'),
      KeyboardShortcut('Ctrl + V', 'Paste component'),
      KeyboardShortcut('Delete', 'Delete selected component'),
      KeyboardShortcut('Ctrl + D', 'Duplicate selected component'),
      KeyboardShortcut('F1', 'Show help'),
      KeyboardShortcut('F5', 'Preview mode'),
      KeyboardShortcut('Ctrl + E', 'Export app'),
      KeyboardShortcut('Ctrl + G', 'Toggle grid'),
      KeyboardShortcut('Ctrl + Shift + P', 'Show properties panel'),
    ];

    return Dialog(
      child: Container(
        width: 400,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.keyboard,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Keyboard Shortcuts',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            // Shortcuts list
            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                padding: const EdgeInsets.all(20),
                itemCount: shortcuts.length,
                itemBuilder: (context, index) {
                  final shortcut = shortcuts[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          child: Text(
                            shortcut.keys,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontFamily: 'monospace',
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            shortcut.description,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Tour step data
class TourStep {
  final String title;
  final String description;
  final IconData icon;

  const TourStep({
    required this.title,
    required this.description,
    required this.icon,
  });
}

/// Keyboard shortcut data
class KeyboardShortcut {
  final String keys;
  final String description;

  const KeyboardShortcut(this.keys, this.description);
}

/// Enhanced tooltip widget with rich content
class EnhancedTooltip extends StatelessWidget {
  final String message;
  final String? description;
  final Widget child;
  final List<String>? tips;

  const EnhancedTooltip({
    super.key,
    required this.message,
    this.description,
    required this.child,
    this.tips,
  });

  @override
  Widget build(BuildContext context) {
    if (description == null && tips == null) {
      return Tooltip(
        message: message,
        child: child,
      );
    }

    return Tooltip(
      richMessage: WidgetSpan(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 300),
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              if (description != null) ...[
                const SizedBox(height: 4),
                Text(
                  description!,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
              if (tips != null && tips!.isNotEmpty) ...[
                const SizedBox(height: 8),
                ...tips!.map((tip) => Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '• ',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white70,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          tip,
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white70,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ],
          ),
        ),
      ),
      child: child,
    );
  }
}
