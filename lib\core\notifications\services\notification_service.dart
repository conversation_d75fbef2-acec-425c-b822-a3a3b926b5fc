import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static const String _notificationsKey = 'app_notifications';
  static const String _settingsKey = 'notification_settings';

  final List<AppNotification> _notifications = [];
  final StreamController<List<AppNotification>> _notificationsController = 
      StreamController<List<AppNotification>>.broadcast();
  final StreamController<AppNotification> _newNotificationController = 
      StreamController<AppNotification>.broadcast();

  Timer? _schedulerTimer;
  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Notification settings
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _badgeEnabled = true;
  TimeOfDay? _quietHoursStart;
  TimeOfDay? _quietHoursEnd;

  // Getters
  Stream<List<AppNotification>> get notificationsStream => _notificationsController.stream;
  Stream<AppNotification> get newNotificationStream => _newNotificationController.stream;
  List<AppNotification> get notifications => List.unmodifiable(_notifications);
  List<AppNotification> get pendingNotifications => 
      _notifications.where((n) => n.isPending).toList();
  List<AppNotification> get unreadNotifications => 
      _notifications.where((n) => n.isSent && !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;
  bool get notificationsEnabled => _notificationsEnabled;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadNotifications();
      await _loadSettings();
      _startScheduler();
      _isInitialized = true;
      debugPrint('NotificationService initialized');
    } catch (e) {
      debugPrint('Error initializing NotificationService: $e');
    }
  }

  // Schedule a notification
  Future<void> scheduleNotification(AppNotification notification) async {
    try {
      _notifications.add(notification);
      await _saveNotifications();
      _notificationsController.add(_notifications);
      debugPrint('Notification scheduled: ${notification.title}');
    } catch (e) {
      debugPrint('Error scheduling notification: $e');
    }
  }

  // Cancel a notification
  Future<void> cancelNotification(int notificationId) async {
    try {
      _notifications.removeWhere((n) => n.id == notificationId);
      await _saveNotifications();
      _notificationsController.add(_notifications);
      debugPrint('Notification cancelled: $notificationId');
    } catch (e) {
      debugPrint('Error cancelling notification: $e');
    }
  }

  // Update notification status
  Future<void> updateNotificationStatus(int notificationId, NotificationStatus status) async {
    try {
      final notification = _notifications.firstWhere((n) => n.id == notificationId);
      
      switch (status) {
        case NotificationStatus.sent:
          notification.markAsSent();
          break;
        case NotificationStatus.read:
          notification.markAsRead();
          break;
        case NotificationStatus.dismissed:
          notification.markAsDismissed();
          break;
        case NotificationStatus.failed:
          notification.markAsFailed();
          break;
        default:
          break;
      }

      await _saveNotifications();
      _notificationsController.add(_notifications);
    } catch (e) {
      debugPrint('Error updating notification status: $e');
    }
  }

  // Handle notification action
  Future<void> handleNotificationAction(int notificationId, String actionId) async {
    try {
      final notification = _notifications.firstWhere((n) => n.id == notificationId);
      
      switch (actionId) {
        case 'complete':
          await _handleCompleteAction(notification);
          break;
        case 'snooze':
          await _handleSnoozeAction(notification);
          break;
        case 'start':
          await _handleStartAction(notification);
          break;
        case 'skip':
          await _handleSkipAction(notification);
          break;
        default:
          debugPrint('Unknown action: $actionId');
      }
    } catch (e) {
      debugPrint('Error handling notification action: $e');
    }
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      _notifications.clear();
      await _saveNotifications();
      _notificationsController.add(_notifications);
    } catch (e) {
      debugPrint('Error clearing notifications: $e');
    }
  }

  // Clear read notifications
  Future<void> clearReadNotifications() async {
    try {
      _notifications.removeWhere((n) => n.isRead);
      await _saveNotifications();
      _notificationsController.add(_notifications);
    } catch (e) {
      debugPrint('Error clearing read notifications: $e');
    }
  }

  // Update settings
  Future<void> updateSettings({
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? badgeEnabled,
    TimeOfDay? quietHoursStart,
    TimeOfDay? quietHoursEnd,
  }) async {
    try {
      if (notificationsEnabled != null) _notificationsEnabled = notificationsEnabled;
      if (soundEnabled != null) _soundEnabled = soundEnabled;
      if (vibrationEnabled != null) _vibrationEnabled = vibrationEnabled;
      if (badgeEnabled != null) _badgeEnabled = badgeEnabled;
      if (quietHoursStart != null) _quietHoursStart = quietHoursStart;
      if (quietHoursEnd != null) _quietHoursEnd = quietHoursEnd;

      await _saveSettings();
    } catch (e) {
      debugPrint('Error updating notification settings: $e');
    }
  }

  // Check if currently in quiet hours
  bool get isInQuietHours {
    if (_quietHoursStart == null || _quietHoursEnd == null) return false;
    
    final now = TimeOfDay.now();
    final start = _quietHoursStart!;
    final end = _quietHoursEnd!;
    
    // Handle overnight quiet hours
    if (start.hour > end.hour || (start.hour == end.hour && start.minute > end.minute)) {
      return (now.hour > start.hour || (now.hour == start.hour && now.minute >= start.minute)) ||
             (now.hour < end.hour || (now.hour == end.hour && now.minute <= end.minute));
    } else {
      return (now.hour > start.hour || (now.hour == start.hour && now.minute >= start.minute)) &&
             (now.hour < end.hour || (now.hour == end.hour && now.minute <= end.minute));
    }
  }

  // Private methods
  void _startScheduler() {
    _schedulerTimer?.cancel();
    _schedulerTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _checkPendingNotifications();
    });
  }

  void _checkPendingNotifications() {
    if (!_notificationsEnabled) return;

    final now = DateTime.now();
    final dueNotifications = _notifications.where((n) => 
        n.isPending && n.scheduledAt.isBefore(now)).toList();

    for (final notification in dueNotifications) {
      _sendNotification(notification);
    }
  }

  void _sendNotification(AppNotification notification) {
    if (isInQuietHours && notification.priority != NotificationPriority.urgent) {
      return; // Skip non-urgent notifications during quiet hours
    }

    try {
      notification.markAsSent();
      _newNotificationController.add(notification);
      
      // Handle repeating notifications
      if (notification.canRepeat) {
        notification.scheduleNext();
      }

      _saveNotifications();
      _notificationsController.add(_notifications);
      
      debugPrint('Notification sent: ${notification.title}');
    } catch (e) {
      notification.markAsFailed();
      debugPrint('Error sending notification: $e');
    }
  }

  Future<void> _handleCompleteAction(AppNotification notification) async {
    // TODO: Integrate with todo service to mark todo as complete
    notification.markAsRead();
    await _saveNotifications();
    debugPrint('Todo marked as complete from notification');
  }

  Future<void> _handleSnoozeAction(AppNotification notification) async {
    notification.scheduledAt = DateTime.now().add(const Duration(minutes: 15));
    notification.status = NotificationStatus.pending;
    await _saveNotifications();
    debugPrint('Notification snoozed for 15 minutes');
  }

  Future<void> _handleStartAction(AppNotification notification) async {
    // TODO: Integrate with dhikr service to start routine
    notification.markAsRead();
    await _saveNotifications();
    debugPrint('Dhikr routine started from notification');
  }

  Future<void> _handleSkipAction(AppNotification notification) async {
    notification.markAsDismissed();
    await _saveNotifications();
    debugPrint('Dhikr routine skipped from notification');
  }

  Future<void> _loadNotifications() async {
    try {
      final notificationsJson = _prefs?.getString(_notificationsKey);
      if (notificationsJson != null) {
        final List<dynamic> notificationsList = json.decode(notificationsJson);
        _notifications.clear();
        _notifications.addAll(
          notificationsList.map((json) => AppNotification.fromJson(json))
        );
        _notificationsController.add(_notifications);
      }
    } catch (e) {
      debugPrint('Error loading notifications: $e');
    }
  }

  Future<void> _saveNotifications() async {
    try {
      final notificationsJson = json.encode(
        _notifications.map((n) => n.toJson()).toList()
      );
      await _prefs?.setString(_notificationsKey, notificationsJson);
    } catch (e) {
      debugPrint('Error saving notifications: $e');
    }
  }

  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs?.getString(_settingsKey);
      if (settingsJson != null) {
        final Map<String, dynamic> settings = json.decode(settingsJson);
        _notificationsEnabled = settings['notificationsEnabled'] ?? true;
        _soundEnabled = settings['soundEnabled'] ?? true;
        _vibrationEnabled = settings['vibrationEnabled'] ?? true;
        _badgeEnabled = settings['badgeEnabled'] ?? true;
        
        if (settings['quietHoursStart'] != null) {
          final parts = settings['quietHoursStart'].split(':');
          _quietHoursStart = TimeOfDay(
            hour: int.parse(parts[0]), 
            minute: int.parse(parts[1])
          );
        }
        
        if (settings['quietHoursEnd'] != null) {
          final parts = settings['quietHoursEnd'].split(':');
          _quietHoursEnd = TimeOfDay(
            hour: int.parse(parts[0]), 
            minute: int.parse(parts[1])
          );
        }
      }
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final settings = {
        'notificationsEnabled': _notificationsEnabled,
        'soundEnabled': _soundEnabled,
        'vibrationEnabled': _vibrationEnabled,
        'badgeEnabled': _badgeEnabled,
        'quietHoursStart': _quietHoursStart != null 
            ? '${_quietHoursStart!.hour}:${_quietHoursStart!.minute}' 
            : null,
        'quietHoursEnd': _quietHoursEnd != null 
            ? '${_quietHoursEnd!.hour}:${_quietHoursEnd!.minute}' 
            : null,
      };
      
      final settingsJson = json.encode(settings);
      await _prefs?.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving notification settings: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _schedulerTimer?.cancel();
    _notificationsController.close();
    _newNotificationController.close();
  }
}
