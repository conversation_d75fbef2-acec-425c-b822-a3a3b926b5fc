import '../models/spreadsheet_cell.dart';

/// Service for managing named ranges and variables in spreadsheets
class NamedRangesService {
  static final NamedRangesService _instance = NamedRangesService._internal();
  factory NamedRangesService() => _instance;
  NamedRangesService._internal();

  final Map<String, NamedRange> _namedRanges = {};
  final Map<String, Variable> _variables = {};

  /// Add a named range
  void addNamedRange(NamedRange namedRange) {
    _namedRanges[namedRange.name] = namedRange;
  }

  /// Remove a named range
  void removeNamedRange(String name) {
    _namedRanges.remove(name);
  }

  /// Get a named range by name
  NamedRange? getNamedRange(String name) {
    return _namedRanges[name];
  }

  /// Get all named ranges
  List<NamedRange> getAllNamedRanges() {
    return _namedRanges.values.toList();
  }

  /// Add a variable
  void addVariable(Variable variable) {
    _variables[variable.name] = variable;
  }

  /// Remove a variable
  void removeVariable(String name) {
    _variables.remove(name);
  }

  /// Get a variable by name
  Variable? getVariable(String name) {
    return _variables[name];
  }

  /// Get all variables
  List<Variable> getAllVariables() {
    return _variables.values.toList();
  }

  /// Update variable value
  void updateVariableValue(String name, dynamic value) {
    final variable = _variables[name];
    if (variable != null) {
      _variables[name] = variable.copyWith(value: value);
    }
  }

  /// Resolve named range to cell addresses
  List<String> resolveNamedRange(String name) {
    final namedRange = _namedRanges[name];
    if (namedRange == null) return [];

    return _expandRange(namedRange.range);
  }

  /// Resolve variable to value
  dynamic resolveVariable(String name) {
    final variable = _variables[name];
    return variable?.value;
  }

  /// Check if name is a named range
  bool isNamedRange(String name) {
    return _namedRanges.containsKey(name);
  }

  /// Check if name is a variable
  bool isVariable(String name) {
    return _variables.containsKey(name);
  }

  /// Validate named range name
  bool isValidName(String name) {
    // Name must start with letter or underscore
    if (!RegExp(r'^[a-zA-Z_]').hasMatch(name)) return false;
    
    // Name can only contain letters, numbers, underscores, and periods
    if (!RegExp(r'^[a-zA-Z_][a-zA-Z0-9_.]*$').hasMatch(name)) return false;
    
    // Name cannot be a cell reference
    if (RegExp(r'^[A-Z]+\d+$').hasMatch(name)) return false;
    
    // Name cannot be a reserved word
    final reservedWords = [
      'TRUE', 'FALSE', 'NULL', 'AND', 'OR', 'NOT', 'IF', 'SUM', 'AVERAGE',
      'MIN', 'MAX', 'COUNT', 'ROUND', 'ABS', 'SQRT', 'POWER', 'EXP', 'LOG'
    ];
    if (reservedWords.contains(name.toUpperCase())) return false;
    
    return true;
  }

  /// Get suggestions for auto-completion
  List<String> getNameSuggestions(String prefix) {
    final suggestions = <String>[];
    
    // Add named ranges
    for (final name in _namedRanges.keys) {
      if (name.toLowerCase().startsWith(prefix.toLowerCase())) {
        suggestions.add(name);
      }
    }
    
    // Add variables
    for (final name in _variables.keys) {
      if (name.toLowerCase().startsWith(prefix.toLowerCase())) {
        suggestions.add(name);
      }
    }
    
    return suggestions;
  }

  /// Replace named ranges and variables in formula
  String replaceNamesInFormula(String formula) {
    String result = formula;
    
    // Replace named ranges
    for (final entry in _namedRanges.entries) {
      final name = entry.key;
      final namedRange = entry.value;
      result = result.replaceAll(name, namedRange.range);
    }
    
    // Replace variables
    for (final entry in _variables.entries) {
      final name = entry.key;
      final variable = entry.value;
      result = result.replaceAll(name, variable.value.toString());
    }
    
    return result;
  }

  /// Find dependencies on named ranges and variables
  List<String> findDependencies(String formula) {
    final dependencies = <String>[];
    
    // Check for named ranges
    for (final name in _namedRanges.keys) {
      if (formula.contains(name)) {
        dependencies.add(name);
      }
    }
    
    // Check for variables
    for (final name in _variables.keys) {
      if (formula.contains(name)) {
        dependencies.add(name);
      }
    }
    
    return dependencies;
  }

  /// Export named ranges and variables to JSON
  Map<String, dynamic> exportToJson() {
    return {
      'namedRanges': _namedRanges.map((key, value) => MapEntry(key, value.toJson())),
      'variables': _variables.map((key, value) => MapEntry(key, value.toJson())),
    };
  }

  /// Import named ranges and variables from JSON
  void importFromJson(Map<String, dynamic> json) {
    _namedRanges.clear();
    _variables.clear();
    
    final namedRangesJson = json['namedRanges'] as Map<String, dynamic>? ?? {};
    for (final entry in namedRangesJson.entries) {
      final namedRange = NamedRange.fromJson(entry.value);
      _namedRanges[entry.key] = namedRange;
    }
    
    final variablesJson = json['variables'] as Map<String, dynamic>? ?? {};
    for (final entry in variablesJson.entries) {
      final variable = Variable.fromJson(entry.value);
      _variables[entry.key] = variable;
    }
  }

  /// Create named range from selection
  NamedRange createNamedRangeFromSelection(
    String name,
    String startCell,
    String endCell, {
    String? description,
  }) {
    final range = startCell == endCell ? startCell : '$startCell:$endCell';
    return NamedRange(
      name: name,
      range: range,
      description: description ?? 'Named range for $range',
      createdAt: DateTime.now(),
    );
  }

  /// Create variable with initial value
  Variable createVariable(
    String name,
    dynamic value, {
    VariableType? type,
    String? description,
  }) {
    return Variable(
      name: name,
      value: value,
      type: type ?? _inferVariableType(value),
      description: description ?? 'Variable $name',
      createdAt: DateTime.now(),
    );
  }

  VariableType _inferVariableType(dynamic value) {
    if (value is bool) return VariableType.boolean;
    if (value is int || value is double) return VariableType.number;
    if (value is DateTime) return VariableType.date;
    return VariableType.text;
  }

  List<String> _expandRange(String range) {
    if (!range.contains(':')) {
      return [range]; // Single cell
    }

    final parts = range.split(':');
    if (parts.length != 2) return [];

    final startAddress = parts[0];
    final endAddress = parts[1];

    final startPos = SpreadsheetUtils.parseCellAddress(startAddress);
    final endPos = SpreadsheetUtils.parseCellAddress(endAddress);

    final cells = <String>[];

    for (int row = startPos['row']!; row <= endPos['row']!; row++) {
      for (int col = startPos['column']!; col <= endPos['column']!; col++) {
        cells.add(SpreadsheetUtils.getCellAddress(row, col));
      }
    }

    return cells;
  }

  /// Get usage statistics
  NamedRangeStatistics getStatistics() {
    final namedRangesByType = <String, int>{};
    final variablesByType = <VariableType, int>{};

    for (final namedRange in _namedRanges.values) {
      final type = namedRange.range.contains(':') ? 'Range' : 'Single Cell';
      namedRangesByType[type] = (namedRangesByType[type] ?? 0) + 1;
    }

    for (final variable in _variables.values) {
      variablesByType[variable.type] = (variablesByType[variable.type] ?? 0) + 1;
    }

    return NamedRangeStatistics(
      totalNamedRanges: _namedRanges.length,
      totalVariables: _variables.length,
      namedRangesByType: namedRangesByType,
      variablesByType: variablesByType,
    );
  }
}

/// Named range model
class NamedRange {
  final String name;
  final String range;
  final String description;
  final DateTime createdAt;
  final DateTime? lastUsed;

  const NamedRange({
    required this.name,
    required this.range,
    required this.description,
    required this.createdAt,
    this.lastUsed,
  });

  NamedRange copyWith({
    String? name,
    String? range,
    String? description,
    DateTime? createdAt,
    DateTime? lastUsed,
  }) {
    return NamedRange(
      name: name ?? this.name,
      range: range ?? this.range,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'range': range,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed?.toIso8601String(),
    };
  }

  factory NamedRange.fromJson(Map<String, dynamic> json) {
    return NamedRange(
      name: json['name'],
      range: json['range'],
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      lastUsed: json['lastUsed'] != null ? DateTime.parse(json['lastUsed']) : null,
    );
  }
}

/// Variable model
class Variable {
  final String name;
  final dynamic value;
  final VariableType type;
  final String description;
  final DateTime createdAt;
  final DateTime? lastModified;

  const Variable({
    required this.name,
    required this.value,
    required this.type,
    required this.description,
    required this.createdAt,
    this.lastModified,
  });

  Variable copyWith({
    String? name,
    dynamic value,
    VariableType? type,
    String? description,
    DateTime? createdAt,
    DateTime? lastModified,
  }) {
    return Variable(
      name: name ?? this.name,
      value: value ?? this.value,
      type: type ?? this.type,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
      'type': type.name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified?.toIso8601String(),
    };
  }

  factory Variable.fromJson(Map<String, dynamic> json) {
    return Variable(
      name: json['name'],
      value: json['value'],
      type: VariableType.values.firstWhere((e) => e.name == json['type']),
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      lastModified: json['lastModified'] != null ? DateTime.parse(json['lastModified']) : null,
    );
  }
}

/// Variable types
enum VariableType {
  text,
  number,
  boolean,
  date,
}

/// Statistics for named ranges and variables
class NamedRangeStatistics {
  final int totalNamedRanges;
  final int totalVariables;
  final Map<String, int> namedRangesByType;
  final Map<VariableType, int> variablesByType;

  const NamedRangeStatistics({
    required this.totalNamedRanges,
    required this.totalVariables,
    required this.namedRangesByType,
    required this.variablesByType,
  });
}
