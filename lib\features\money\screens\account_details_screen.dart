import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/account.dart';
import '../models/transaction.dart';
import '../providers/transaction_provider.dart';
import '../providers/account_provider.dart';
import '../../../shared/providers/user_provider.dart';
import 'transaction_form_screen.dart';
import 'account_form_screen.dart';

class AccountDetailsScreen extends ConsumerStatefulWidget {
  final Account account;

  const AccountDetailsScreen({
    super.key,
    required this.account,
  });

  @override
  ConsumerState<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends ConsumerState<AccountDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = '30 days';
  final List<String> _periods = ['7 days', '30 days', '90 days', '1 year', 'All time'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StandardizedScaffold(
      title: widget.account.name,
      currentRoute: '/money-flow/account/${widget.account.id}',
      showBackButton: true,
      actions: [
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('Edit Account'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'transfer',
              child: Row(
                children: [
                  Icon(Icons.swap_horiz, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('Transfer Money'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, color: Colors.green),
                  SizedBox(width: 8),
                  Text('Export Transactions'),
                ],
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
          Tab(icon: Icon(Icons.list), text: 'Transactions'),
          Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildTransactionsTab(),
          _buildAnalyticsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addTransaction,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return Consumer(
      builder: (context, ref, child) {
        final accountTransactions = ref.watch(transactionsByAccountProvider(widget.account.id));
        final accountStats = _calculateAccountStats(accountTransactions);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Account Summary Card
              StandardCard(
                child: Column(
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: widget.account.color,
                          child: Icon(
                            widget.account.icon,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.account.name,
                                style: Theme.of(context).textTheme.headlineSmall,
                              ),
                              Text(
                                widget.account.typeDisplayName,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.outline,
                                ),
                              ),
                              if (widget.account.description.isNotEmpty)
                                Text(
                                  widget.account.description,
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Current Balance',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.account.formattedBalance,
                      style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        color: widget.account.balance >= 0 ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Quick Stats
              ResponsiveGrid(
                mobileColumns: 1,
                tabletColumns: 2,
                desktopColumns: 4,
                spacing: ResponsiveSpacing.sm(context),
                runSpacing: ResponsiveSpacing.sm(context),
                children: [
                  InfoCard(
                    icon: Icons.trending_up,
                    title: 'Total Income',
                    value: widget.account.currency == 'USD'
                        ? '\$${accountStats['totalIncome'].toStringAsFixed(2)}'
                        : '${widget.account.currency} ${accountStats['totalIncome'].toStringAsFixed(2)}',
                    iconColor: Colors.green,
                  ),
                  InfoCard(
                    icon: Icons.trending_down,
                    title: 'Total Expenses',
                    value: widget.account.currency == 'USD'
                        ? '\$${accountStats['totalExpenses'].toStringAsFixed(2)}'
                        : '${widget.account.currency} ${accountStats['totalExpenses'].toStringAsFixed(2)}',
                    iconColor: Colors.red,
                  ),
                  InfoCard(
                    icon: Icons.receipt,
                    title: 'Transactions',
                    value: '${accountStats['transactionCount']}',
                    iconColor: Colors.blue,
                  ),
                  InfoCard(
                    icon: Icons.analytics,
                    title: 'Avg Transaction',
                    value: widget.account.currency == 'USD'
                        ? '\$${accountStats['averageTransaction'].toStringAsFixed(2)}'
                        : '${widget.account.currency} ${accountStats['averageTransaction'].toStringAsFixed(2)}',
                    iconColor: Colors.purple,
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Recent Transactions
              SectionHeader(
                title: 'Recent Transactions',
                subtitle: 'Latest account activity',
                action: TextButton(
                  onPressed: () => _tabController.animateTo(1),
                  child: const Text('View All'),
                ),
              ),
              
              const SizedBox(height: 8),
              
              if (accountTransactions.isEmpty) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      children: [
                        Icon(
                          Icons.receipt_long,
                          size: 64,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No transactions yet',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Add your first transaction to get started',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _addTransaction,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Transaction'),
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                ...accountTransactions.take(5).map((transaction) {
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: transaction.type == TransactionType.income 
                            ? Colors.green 
                            : Colors.red,
                        child: Icon(
                          transaction.type == TransactionType.income 
                              ? Icons.add 
                              : Icons.remove,
                          color: Colors.white,
                        ),
                      ),
                      title: Text(transaction.description),
                      subtitle: Text(
                        '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
                      ),
                      trailing: Text(
                        '${transaction.type == TransactionType.income ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}',
                        style: TextStyle(
                          color: transaction.type == TransactionType.income 
                              ? Colors.green 
                              : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onTap: () => _editTransaction(transaction),
                    ),
                  );
                }),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildTransactionsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final accountTransactions = ref.watch(transactionsByAccountProvider(widget.account.id));
        final filteredTransactions = _filterTransactionsByPeriod(accountTransactions);

        return Column(
          children: [
            // Period Filter
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Text('Period: '),
                  DropdownButton<String>(
                    value: _selectedPeriod,
                    items: _periods.map((period) {
                      return DropdownMenuItem(
                        value: period,
                        child: Text(period),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedPeriod = value;
                        });
                      }
                    },
                  ),
                  const Spacer(),
                  Text('${filteredTransactions.length} transactions'),
                ],
              ),
            ),
            
            // Transactions List
            Expanded(
              child: filteredTransactions.isEmpty
                  ? const Center(
                      child: Text('No transactions in selected period'),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: filteredTransactions.length,
                      itemBuilder: (context, index) {
                        final transaction = filteredTransactions[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: transaction.type == TransactionType.income 
                                  ? Colors.green 
                                  : Colors.red,
                              child: Icon(
                                transaction.type == TransactionType.income 
                                    ? Icons.add 
                                    : Icons.remove,
                                color: Colors.white,
                              ),
                            ),
                            title: Text(transaction.description),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
                                ),
                                if (transaction.notes != null && transaction.notes!.isNotEmpty)
                                  Text(
                                    transaction.notes!,
                                    style: Theme.of(context).textTheme.bodySmall,
                                  ),
                              ],
                            ),
                            trailing: Text(
                              '${transaction.type == TransactionType.income ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}',
                              style: TextStyle(
                                color: transaction.type == TransactionType.income 
                                    ? Colors.green 
                                    : Colors.red,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            onTap: () => _editTransaction(transaction),
                          ),
                        );
                      },
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAnalyticsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final accountTransactions = ref.watch(transactionsByAccountProvider(widget.account.id));
        
        if (accountTransactions.isEmpty) {
          return const Center(
            child: Text('No data available for analytics'),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Balance Trend',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SizedBox(
                    height: 200,
                    child: _buildBalanceTrendChart(accountTransactions),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              Text(
                'Monthly Summary',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              // Monthly breakdown
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Monthly Breakdown',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildMonthlyBreakdown(accountTransactions),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }



  Widget _buildBalanceTrendChart(List<Transaction> transactions) {
    if (transactions.isEmpty) {
      return const Center(child: Text('No data available'));
    }

    // Sort transactions by date
    final sortedTransactions = List<Transaction>.from(transactions)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Calculate running balance
    double runningBalance = 0;
    final spots = <FlSpot>[];
    
    for (int i = 0; i < sortedTransactions.length; i++) {
      final transaction = sortedTransactions[i];
      if (transaction.type == TransactionType.income) {
        runningBalance += transaction.amount;
      } else {
        runningBalance -= transaction.amount;
      }
      spots.add(FlSpot(i.toDouble(), runningBalance));
    }

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: false),
        titlesData: const FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: widget.account.color,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: widget.account.color.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _calculateAccountStats(List<Transaction> transactions) {
    double totalIncome = 0;
    double totalExpenses = 0;
    
    for (final transaction in transactions) {
      if (transaction.type == TransactionType.income) {
        totalIncome += transaction.amount;
      } else {
        totalExpenses += transaction.amount;
      }
    }
    
    final averageTransaction = transactions.isNotEmpty 
        ? (totalIncome + totalExpenses) / transactions.length 
        : 0.0;

    return {
      'totalIncome': totalIncome,
      'totalExpenses': totalExpenses,
      'transactionCount': transactions.length,
      'averageTransaction': averageTransaction,
    };
  }

  List<Transaction> _filterTransactionsByPeriod(List<Transaction> transactions) {
    final now = DateTime.now();
    DateTime startDate;
    
    switch (_selectedPeriod) {
      case '7 days':
        startDate = now.subtract(const Duration(days: 7));
        break;
      case '30 days':
        startDate = now.subtract(const Duration(days: 30));
        break;
      case '90 days':
        startDate = now.subtract(const Duration(days: 90));
        break;
      case '1 year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      case 'All time':
      default:
        return transactions;
    }
    
    return transactions.where((t) => t.date.isAfter(startDate)).toList();
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _editAccount();
        break;
      case 'transfer':
        _transferMoney();
        break;
      case 'export':
        _exportTransactions();
        break;
    }
  }

  Widget _buildMonthlyBreakdown(List<Transaction> transactions) {
    // Group transactions by month
    final monthlyData = <String, Map<String, double>>{};

    for (final transaction in transactions) {
      final monthKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyData[monthKey] ??= {'income': 0.0, 'expense': 0.0};

      if (transaction.type == TransactionType.income) {
        monthlyData[monthKey]!['income'] = monthlyData[monthKey]!['income']! + transaction.amount;
      } else {
        monthlyData[monthKey]!['expense'] = monthlyData[monthKey]!['expense']! + transaction.amount;
      }
    }

    // Sort by month (most recent first)
    final sortedMonths = monthlyData.keys.toList()..sort((a, b) => b.compareTo(a));

    if (sortedMonths.isEmpty) {
      return const Center(
        child: Text('No transaction data available'),
      );
    }

    return Column(
      children: sortedMonths.take(6).map((monthKey) {
        final data = monthlyData[monthKey]!;
        final income = data['income']!;
        final expense = data['expense']!;
        final net = income - expense;

        // Format month display
        final parts = monthKey.split('-');
        final year = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final monthName = [
          '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ][month];
        final displayMonth = '$monthName $year';

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    displayMonth,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    '+\$${income.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
                Expanded(
                  child: Text(
                    '-\$${expense.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
                Expanded(
                  child: Text(
                    '${net >= 0 ? '+' : ''}\$${net.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: net >= 0 ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.error,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  void _editAccount() {
    // Navigate to account editor
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AccountFormScreen(account: widget.account),
      ),
    );
  }

  void _transferMoney() {
    _showTransferDialog();
  }

  void _showTransferDialog() {
    final accounts = ref.read(accountsProvider).where((a) => a.id != widget.account.id).toList();

    if (accounts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No other accounts available for transfer')),
      );
      return;
    }

    Account? selectedAccount;
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transfer Money'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<Account>(
              decoration: const InputDecoration(
                labelText: 'To Account',
                border: OutlineInputBorder(),
              ),
              items: accounts.map((account) => DropdownMenuItem(
                value: account,
                child: Text(account.name),
              )).toList(),
              onChanged: (account) => selectedAccount = account,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                border: OutlineInputBorder(),
                prefixText: '\$',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optional)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (selectedAccount != null && amountController.text.isNotEmpty) {
                final amount = double.tryParse(amountController.text);
                if (amount != null && amount > 0) {
                  _performTransfer(selectedAccount!, amount, descriptionController.text);
                  Navigator.of(context).pop();
                }
              }
            },
            child: const Text('Transfer'),
          ),
        ],
      ),
    );
  }

  void _performTransfer(Account toAccount, double amount, String description) async {
    try {
      final userProfile = ref.read(userProfileProvider);
      if (userProfile == null) return;

      // Create transfer transactions
      final fromTransaction = Transaction.create(
        accountId: widget.account.id,
        amount: amount,
        type: TransactionType.expense,
        category: 'Transfer',
        description: description.isEmpty ? 'Transfer to ${toAccount.name}' : description,
        userId: userProfile.id.toString(),
        transactionDate: DateTime.now(),
        toAccountId: toAccount.id,
      );

      final toTransaction = Transaction.create(
        accountId: toAccount.id,
        amount: amount,
        type: TransactionType.income,
        category: 'Transfer',
        description: description.isEmpty ? 'Transfer from ${widget.account.name}' : description,
        userId: userProfile.id.toString(),
        transactionDate: DateTime.now(),
        toAccountId: widget.account.id,
      );

      // Add transactions
      await ref.read(transactionsProvider.notifier).addTransaction(fromTransaction);
      await ref.read(transactionsProvider.notifier).addTransaction(toTransaction);

      // Update account balances
      await ref.read(accountsProvider.notifier).updateAccountBalance(
        widget.account.id,
        widget.account.balance - amount
      );
      await ref.read(accountsProvider.notifier).updateAccountBalance(
        toAccount.id,
        toAccount.balance + amount
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Transfer of \$${amount.toStringAsFixed(2)} completed')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Transfer failed: $e')),
        );
      }
    }
  }

  void _exportTransactions() {
    // Export transactions
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon!')),
    );
  }

  void _addTransaction() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TransactionFormScreen(),
      ),
    );
  }

  void _editTransaction(Transaction transaction) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TransactionFormScreen(
          transaction: transaction,
        ),
      ),
    );
  }
}
