import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/transaction.dart';

import '../providers/transaction_provider.dart';
import '../providers/category_provider.dart';

class FinancialCharts extends ConsumerWidget {
  const FinancialCharts({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Expense by Category Pie Chart
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Expenses by Category',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: ExpensePie<PERSON>hart(),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Monthly Trend Line Chart
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Monthly Trend',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: MonthlyTrendChart(),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Income vs Expenses Bar Chart
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Income vs Expenses',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: IncomeExpenseBarChart(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class ExpensePieChart extends ConsumerWidget {
  const ExpensePieChart({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactions = ref.watch(transactionsProvider);
    final categories = ref.watch(categoriesProvider);
    
    // Calculate expense data by category
    final expenseData = <String, double>{};
    final categoryColors = <String, Color>{};
    
    for (final transaction in transactions.where((t) => t.type == TransactionType.expense)) {
      expenseData[transaction.category] = (expenseData[transaction.category] ?? 0) + transaction.amount;
    }
    
    // Get category colors
    for (final category in categories) {
      categoryColors[category.name] = category.color;
    }
    
    if (expenseData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.pie_chart_outline,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No expense data available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }
    
    // Create a simple pie chart representation
    return CustomPieChart(
      data: expenseData,
      colors: categoryColors,
    );
  }
}

class MonthlyTrendChart extends ConsumerWidget {
  const MonthlyTrendChart({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactions = ref.watch(transactionsProvider);
    
    // Calculate monthly data for the last 6 months
    final monthlyData = <String, Map<String, double>>{};
    final now = DateTime.now();
    
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.month}/${month.year}';
      monthlyData[monthKey] = {'income': 0.0, 'expense': 0.0};
    }
    
    for (final transaction in transactions) {
      final monthKey = '${transaction.transactionDate.month}/${transaction.transactionDate.year}';
      if (monthlyData.containsKey(monthKey)) {
        if (transaction.type == TransactionType.income) {
          monthlyData[monthKey]!['income'] = monthlyData[monthKey]!['income']! + transaction.amount;
        } else if (transaction.type == TransactionType.expense) {
          monthlyData[monthKey]!['expense'] = monthlyData[monthKey]!['expense']! + transaction.amount;
        }
      }
    }
    
    if (monthlyData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No trend data available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }
    
    return CustomLineChart(data: monthlyData);
  }
}

class IncomeExpenseBarChart extends ConsumerWidget {
  const IncomeExpenseBarChart({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactions = ref.watch(transactionsProvider);
    
    // Calculate data for the last 6 months
    final monthlyData = <String, Map<String, double>>{};
    final now = DateTime.now();
    
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.month}/${month.year}';
      monthlyData[monthKey] = {'income': 0.0, 'expense': 0.0};
    }
    
    for (final transaction in transactions) {
      final monthKey = '${transaction.transactionDate.month}/${transaction.transactionDate.year}';
      if (monthlyData.containsKey(monthKey)) {
        if (transaction.type == TransactionType.income) {
          monthlyData[monthKey]!['income'] = monthlyData[monthKey]!['income']! + transaction.amount;
        } else if (transaction.type == TransactionType.expense) {
          monthlyData[monthKey]!['expense'] = monthlyData[monthKey]!['expense']! + transaction.amount;
        }
      }
    }
    
    if (monthlyData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No comparison data available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }
    
    return CustomBarChart(data: monthlyData);
  }
}

// Custom chart widgets (simplified implementations)
class CustomPieChart extends StatelessWidget {
  final Map<String, double> data;
  final Map<String, Color> colors;

  const CustomPieChart({
    super.key,
    required this.data,
    required this.colors,
  });

  @override
  Widget build(BuildContext context) {
    final total = data.values.fold(0.0, (sum, value) => sum + value);
    
    return Column(
      children: [
        Expanded(
          child: Center(
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: SweepGradient(
                  colors: data.keys.map((key) => colors[key] ?? Colors.grey).toList(),
                ),
              ),
              child: Center(
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  child: Center(
                    child: Text(
                      '\$${total.toStringAsFixed(0)}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: data.entries.map((entry) {
            final percentage = (entry.value / total * 100).toStringAsFixed(1);
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: colors[entry.key] ?? Colors.grey,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '${entry.key} ($percentage%)',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }
}

class CustomLineChart extends StatelessWidget {
  final Map<String, Map<String, double>> data;

  const CustomLineChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: CustomPaint(
              painter: LineChartPainter(data, Theme.of(context).colorScheme),
              size: const Size(double.infinity, double.infinity),
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 2,
                  color: Colors.green,
                ),
                const SizedBox(width: 4),
                const Text('Income'),
              ],
            ),
            Row(
              children: [
                Container(
                  width: 12,
                  height: 2,
                  color: Colors.red,
                ),
                const SizedBox(width: 4),
                const Text('Expenses'),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

class CustomBarChart extends StatelessWidget {
  final Map<String, Map<String, double>> data;

  const CustomBarChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: CustomPaint(
              painter: BarChartPainter(data, Theme.of(context).colorScheme),
              size: const Size(double.infinity, double.infinity),
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  color: Colors.green,
                ),
                const SizedBox(width: 4),
                const Text('Income'),
              ],
            ),
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  color: Colors.red,
                ),
                const SizedBox(width: 4),
                const Text('Expenses'),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

// Custom painters for charts
class LineChartPainter extends CustomPainter {
  final Map<String, Map<String, double>> data;
  final ColorScheme colorScheme;

  LineChartPainter(this.data, this.colorScheme);

  @override
  void paint(Canvas canvas, Size size) {
    // Simplified line chart implementation
    final paint = Paint()
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw income line
    paint.color = Colors.green;
    _drawLine(canvas, size, 'income', paint);

    // Draw expense line
    paint.color = Colors.red;
    _drawLine(canvas, size, 'expense', paint);
  }

  void _drawLine(Canvas canvas, Size size, String type, Paint paint) {
    final values = data.values.map((d) => d[type] ?? 0.0).toList();
    if (values.isEmpty) return;

    final maxValue = values.reduce((a, b) => a > b ? a : b);
    if (maxValue == 0) return;

    final path = Path();
    for (int i = 0; i < values.length; i++) {
      final x = (i / (values.length - 1)) * size.width;
      final y = size.height - (values[i] / maxValue) * size.height;
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class BarChartPainter extends CustomPainter {
  final Map<String, Map<String, double>> data;
  final ColorScheme colorScheme;

  BarChartPainter(this.data, this.colorScheme);

  @override
  void paint(Canvas canvas, Size size) {
    // Simplified bar chart implementation
    final entries = data.entries.toList();
    if (entries.isEmpty) return;

    final barWidth = size.width / (entries.length * 2);
    final maxValue = entries
        .expand((e) => e.value.values)
        .fold(0.0, (max, value) => value > max ? value : max);

    if (maxValue == 0) return;

    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      final x = i * barWidth * 2;
      
      // Income bar
      final incomeHeight = (entry.value['income'] ?? 0) / maxValue * size.height;
      canvas.drawRect(
        Rect.fromLTWH(x, size.height - incomeHeight, barWidth * 0.8, incomeHeight),
        Paint()..color = Colors.green,
      );
      
      // Expense bar
      final expenseHeight = (entry.value['expense'] ?? 0) / maxValue * size.height;
      canvas.drawRect(
        Rect.fromLTWH(x + barWidth, size.height - expenseHeight, barWidth * 0.8, expenseHeight),
        Paint()..color = Colors.red,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
