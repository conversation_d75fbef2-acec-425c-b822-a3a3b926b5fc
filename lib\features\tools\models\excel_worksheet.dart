import 'spreadsheet_cell.dart';

/// Model representing an Excel worksheet with cells and metadata
class ExcelWorksheet {
  final String name;
  final Map<String, SpreadsheetCell> cells;
  final int maxRow;
  final int maxColumn;
  final bool isVisible;
  final String? tabColor;
  final Map<String, dynamic> metadata;

  const ExcelWorksheet({
    required this.name,
    required this.cells,
    this.maxRow = 0,
    this.maxColumn = 0,
    this.isVisible = true,
    this.tabColor,
    Map<String, dynamic>? metadata,
  }) : metadata = metadata ?? const {};

  /// Get cell by address (e.g., "A1")
  SpreadsheetCell? getCell(String address) {
    return cells[address];
  }

  /// Set cell value
  ExcelWorksheet setCell(String address, SpreadsheetCell cell) {
    final newCells = Map<String, SpreadsheetCell>.from(cells);
    newCells[address] = cell;
    
    final coords = _parseCellAddress(address);
    final newMaxRow = coords['row']! > maxRow ? coords['row']! : maxRow;
    final newMaxColumn = coords['col']! > maxColumn ? coords['col']! : maxColumn;
    
    return copyWith(
      cells: newCells,
      maxRow: newMaxRow,
      maxColumn: newMaxColumn,
    );
  }

  /// Remove cell
  ExcelWorksheet removeCell(String address) {
    final newCells = Map<String, SpreadsheetCell>.from(cells);
    newCells.remove(address);
    
    return copyWith(cells: newCells);
  }

  /// Get all cells in a range
  Map<String, SpreadsheetCell> getCellsInRange(String startCell, String endCell) {
    final startCoords = _parseCellAddress(startCell);
    final endCoords = _parseCellAddress(endCell);
    
    final minRow = startCoords['row']! < endCoords['row']! ? startCoords['row']! : endCoords['row']!;
    final maxRow = startCoords['row']! > endCoords['row']! ? startCoords['row']! : endCoords['row']!;
    final minCol = startCoords['col']! < endCoords['col']! ? startCoords['col']! : endCoords['col']!;
    final maxCol = startCoords['col']! > endCoords['col']! ? startCoords['col']! : endCoords['col']!;
    
    final rangeCells = <String, SpreadsheetCell>{};
    
    for (int row = minRow; row <= maxRow; row++) {
      for (int col = minCol; col <= maxCol; col++) {
        final address = _getCellAddress(row, col);
        final cell = cells[address];
        if (cell != null) {
          rangeCells[address] = cell;
        }
      }
    }
    
    return rangeCells;
  }

  /// Get used range (cells with data)
  Map<String, int> get usedRange {
    if (cells.isEmpty) {
      return {'minRow': 0, 'maxRow': 0, 'minCol': 0, 'maxCol': 0};
    }
    
    int minRow = maxRow;
    int maxRowUsed = 0;
    int minCol = maxColumn;
    int maxColUsed = 0;
    
    for (final address in cells.keys) {
      final coords = _parseCellAddress(address);
      final row = coords['row']!;
      final col = coords['col']!;
      
      if (row < minRow) minRow = row;
      if (row > maxRowUsed) maxRowUsed = row;
      if (col < minCol) minCol = col;
      if (col > maxColUsed) maxColUsed = col;
    }
    
    return {
      'minRow': minRow,
      'maxRow': maxRowUsed,
      'minCol': minCol,
      'maxCol': maxColUsed,
    };
  }

  /// Get number of cells with data
  int get cellCount => cells.length;

  /// Get number of formulas
  int get formulaCount => cells.values.where((cell) => cell.hasFormula).length;

  /// Check if worksheet has any data
  bool get hasData => cells.isNotEmpty;

  /// Get all formulas in the worksheet
  Map<String, String> get formulas {
    final formulaMap = <String, String>{};
    for (final entry in cells.entries) {
      if (entry.value.hasFormula && entry.value.formula != null) {
        formulaMap[entry.key] = entry.value.formula!;
      }
    }
    return formulaMap;
  }

  /// Parse cell address like "A1" to row/column coordinates
  Map<String, int> _parseCellAddress(String address) {
    final regex = RegExp(r'^([A-Z]+)(\d+)$');
    final match = regex.firstMatch(address);
    
    if (match != null) {
      final colStr = match.group(1)!;
      final rowStr = match.group(2)!;
      
      int col = 0;
      for (int i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.codeUnitAt(i) - 65 + 1);
      }
      
      return {
        'row': int.parse(rowStr),
        'col': col,
      };
    }
    
    return {'row': 1, 'col': 1};
  }

  /// Convert row/column coordinates to cell address
  String _getCellAddress(int row, int col) {
    String colStr = '';
    int tempCol = col;
    
    while (tempCol > 0) {
      tempCol--;
      colStr = String.fromCharCode(65 + (tempCol % 26)) + colStr;
      tempCol ~/= 26;
    }
    
    return '$colStr$row';
  }

  /// Create a copy with updated values
  ExcelWorksheet copyWith({
    String? name,
    Map<String, SpreadsheetCell>? cells,
    int? maxRow,
    int? maxColumn,
    bool? isVisible,
    String? tabColor,
    Map<String, dynamic>? metadata,
  }) {
    return ExcelWorksheet(
      name: name ?? this.name,
      cells: cells ?? this.cells,
      maxRow: maxRow ?? this.maxRow,
      maxColumn: maxColumn ?? this.maxColumn,
      isVisible: isVisible ?? this.isVisible,
      tabColor: tabColor ?? this.tabColor,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'cells': cells.map((key, value) => MapEntry(key, value.toJson())),
      'maxRow': maxRow,
      'maxColumn': maxColumn,
      'isVisible': isVisible,
      'tabColor': tabColor,
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory ExcelWorksheet.fromJson(Map<String, dynamic> json) {
    final cellsJson = json['cells'] as Map<String, dynamic>? ?? {};
    final cells = <String, SpreadsheetCell>{};
    
    for (final entry in cellsJson.entries) {
      try {
        cells[entry.key] = SpreadsheetCell.fromJson(entry.value as Map<String, dynamic>);
      } catch (e) {
        // Skip invalid cells
        continue;
      }
    }
    
    return ExcelWorksheet(
      name: json['name'] ?? '',
      cells: cells,
      maxRow: json['maxRow'] ?? 0,
      maxColumn: json['maxColumn'] ?? 0,
      isVisible: json['isVisible'] ?? true,
      tabColor: json['tabColor'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Get worksheet statistics
  Map<String, dynamic> getStatistics() {
    final usedRangeData = usedRange;
    return {
      'name': name,
      'cellCount': cellCount,
      'formulaCount': formulaCount,
      'hasData': hasData,
      'maxRow': maxRow,
      'maxColumn': maxColumn,
      'usedRange': usedRangeData,
      'isVisible': isVisible,
      'tabColor': tabColor,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExcelWorksheet &&
        other.name == name &&
        other.cellCount == cellCount;
  }

  @override
  int get hashCode {
    return Object.hash(name, cellCount);
  }

  @override
  String toString() {
    return 'ExcelWorksheet(name: $name, cells: $cellCount)';
  }
}
