import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/backup_sync_service.dart';
import '../../shared/providers/user_provider.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(AuthState.initial) {
    _initialize();
  }

  final BackupSyncService _syncService = BackupSyncService.instance;
  SupabaseClient get _supabase => Supabase.instance.client;
  
  User? get currentUser => _supabase.auth.currentUser;
  bool get isAuthenticated => currentUser != null;
  String? _lastError;
  
  String? get lastError => _lastError;

  Future<void> _initialize() async {
    state = AuthState.loading;
    
    try {
      // Listen to auth state changes
      _supabase.auth.onAuthStateChange.listen((data) {
        final event = data.event;
        final user = data.session?.user;
        
        switch (event) {
          case AuthChangeEvent.signedIn:
            state = AuthState.authenticated;
            _onSignedIn(user);
            break;
          case AuthChangeEvent.signedOut:
            state = AuthState.unauthenticated;
            _onSignedOut();
            break;
          case AuthChangeEvent.userUpdated:
            // Handle user profile updates
            break;
          default:
            break;
        }
      });

      // Check initial auth state
      final session = _supabase.auth.currentSession;
      if (session?.user != null) {
        state = AuthState.authenticated;
        await _onSignedIn(session!.user);
      } else {
        state = AuthState.unauthenticated;
      }
    } catch (e) {
      _lastError = e.toString();
      state = AuthState.error;
    }
  }

  /// Sign in with email and password
  Future<bool> signInWithEmail(String email, String password) async {
    state = AuthState.loading;
    _lastError = null;

    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        state = AuthState.authenticated;
        return true;
      } else {
        _lastError = 'Sign in failed';
        state = AuthState.unauthenticated;
        return false;
      }
    } on AuthException catch (e) {
      _lastError = e.message;
      state = AuthState.error;
      return false;
    } catch (e) {
      _lastError = 'An unexpected error occurred';
      state = AuthState.error;
      return false;
    }
  }

  /// Sign up with email and password
  Future<bool> signUpWithEmail(String email, String password, {String? name}) async {
    state = AuthState.loading;
    _lastError = null;

    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: name != null ? {'name': name} : null,
      );

      if (response.user != null) {
        // User created successfully
        if (response.session != null) {
          // User is immediately signed in
          state = AuthState.authenticated;
        } else {
          // User needs to verify email
          state = AuthState.unauthenticated;
        }
        return true;
      } else {
        _lastError = 'Sign up failed';
        state = AuthState.unauthenticated;
        return false;
      }
    } on AuthException catch (e) {
      _lastError = e.message;
      state = AuthState.error;
      return false;
    } catch (e) {
      _lastError = 'An unexpected error occurred';
      state = AuthState.error;
      return false;
    }
  }

  /// Sign in with Google (if configured)
  Future<bool> signInWithGoogle() async {
    state = AuthState.loading;
    _lastError = null;

    try {
      final response = await _supabase.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'io.supabase.shadowsuite://login-callback/',
      );

      // OAuth flow will be handled by the auth state listener
      return true;
    } on AuthException catch (e) {
      _lastError = e.message;
      state = AuthState.error;
      return false;
    } catch (e) {
      _lastError = 'An unexpected error occurred';
      state = AuthState.error;
      return false;
    }
  }

  /// Sign in with Apple (if configured)
  Future<bool> signInWithApple() async {
    state = AuthState.loading;
    _lastError = null;

    try {
      final response = await _supabase.auth.signInWithOAuth(
        OAuthProvider.apple,
        redirectTo: 'io.supabase.shadowsuite://login-callback/',
      );

      // OAuth flow will be handled by the auth state listener
      return true;
    } on AuthException catch (e) {
      _lastError = e.message;
      state = AuthState.error;
      return false;
    } catch (e) {
      _lastError = 'An unexpected error occurred';
      state = AuthState.error;
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    state = AuthState.loading;
    
    try {
      await _supabase.auth.signOut();
      state = AuthState.unauthenticated;
    } catch (e) {
      _lastError = e.toString();
      state = AuthState.error;
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    _lastError = null;

    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: 'io.supabase.shadowsuite://reset-password/',
      );
      return true;
    } on AuthException catch (e) {
      _lastError = e.message;
      return false;
    } catch (e) {
      _lastError = 'An unexpected error occurred';
      return false;
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? email,
    String? password,
    Map<String, dynamic>? data,
  }) async {
    _lastError = null;

    try {
      final updates = UserAttributes(
        email: email,
        password: password,
        data: data,
      );

      final response = await _supabase.auth.updateUser(updates);
      return response.user != null;
    } on AuthException catch (e) {
      _lastError = e.message;
      return false;
    } catch (e) {
      _lastError = 'An unexpected error occurred';
      return false;
    }
  }

  /// Delete user account
  Future<bool> deleteAccount() async {
    _lastError = null;

    try {
      // First, delete user data from our tables
      await _deleteUserData();
      
      // Then delete the auth user
      // Note: This requires a custom function in Supabase
      await _supabase.rpc('delete_user');
      
      state = AuthState.unauthenticated;
      return true;
    } catch (e) {
      _lastError = e.toString();
      return false;
    }
  }

  /// Get user metadata
  Map<String, dynamic>? get userMetadata => currentUser?.userMetadata;

  /// Get user email
  String? get userEmail => currentUser?.email;

  /// Get user ID
  String? get userId => currentUser?.id;

  /// Check if email is verified
  bool get isEmailVerified => currentUser?.emailConfirmedAt != null;

  /// Resend email verification
  Future<bool> resendEmailVerification() async {
    if (currentUser?.email == null) return false;

    try {
      await _supabase.auth.resend(
        type: OtpType.signup,
        email: currentUser!.email!,
      );
      return true;
    } catch (e) {
      _lastError = e.toString();
      return false;
    }
  }

  // Private methods

  Future<void> _onSignedIn(User? user) async {
    if (user == null) return;

    try {
      // Initialize sync service
      await _syncService.initialize();
      
      // Create or update user profile in local database
      // This will be handled by the user provider
      
      // Optionally perform initial sync
      if (await _syncService.isAutoSyncEnabled()) {
        _syncService.performFullSync();
      }
    } catch (e) {
      // Handle error but don't prevent sign in
      print('Error in post-sign-in setup: $e');
    }
  }

  Future<void> _onSignedOut() async {
    try {
      // Clear any cached data if needed
      // The user provider will handle local profile cleanup
    } catch (e) {
      print('Error in post-sign-out cleanup: $e');
    }
  }

  Future<void> _deleteUserData() async {
    if (userId == null) return;

    try {
      // Delete user data from all tables
      await _supabase.from('notes').delete().eq('user_id', userId!);
      await _supabase.from('todos').delete().eq('user_id', userId!);
      await _supabase.from('voice_memos').delete().eq('user_id', userId!);
      await _supabase.from('accounts').delete().eq('user_id', userId!);
      await _supabase.from('transactions').delete().eq('user_id', userId!);
      await _supabase.from('categories').delete().eq('user_id', userId!);
      await _supabase.from('user_profiles').delete().eq('user_id', userId!);
    } catch (e) {
      print('Error deleting user data: $e');
      rethrow;
    }
  }
}

// Providers
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

final currentUserProvider = Provider<User?>((ref) {
  final authNotifier = ref.read(authProvider.notifier);
  ref.watch(authProvider); // Watch for auth state changes
  return authNotifier.currentUser;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState == AuthState.authenticated;
});

final userEmailProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.email;
});

final userIdProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.id;
});

final isEmailVerifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.emailConfirmedAt != null;
});
