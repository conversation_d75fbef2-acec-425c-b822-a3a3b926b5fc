import 'package:flutter/material.dart';
import '../models/quran_models.dart';

/// Widget for displaying and managing Quran bookmarks
class BookmarksWidget extends StatefulWidget {
  final List<QuranBookmark> bookmarks;
  final Function(QuranBookmark bookmark) onBookmarkSelected;
  final Function(QuranBookmark bookmark) onBookmarkDeleted;

  const BookmarksWidget({
    super.key,
    required this.bookmarks,
    required this.onBookmarkSelected,
    required this.onBookmarkDeleted,
  });

  @override
  State<BookmarksWidget> createState() => _BookmarksWidgetState();
}

class _BookmarksWidgetState extends State<BookmarksWidget> {
  String _sortBy = 'recent'; // 'recent', 'surah', 'alphabetical'
  List<QuranBookmark> _sortedBookmarks = [];

  @override
  void initState() {
    super.initState();
    _sortBookmarks();
  }

  @override
  void didUpdateWidget(BookmarksWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.bookmarks != widget.bookmarks) {
      _sortBookmarks();
    }
  }

  void _sortBookmarks() {
    _sortedBookmarks = List.from(widget.bookmarks);
    
    switch (_sortBy) {
      case 'recent':
        _sortedBookmarks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'surah':
        _sortedBookmarks.sort((a, b) {
          final surahCompare = a.verse.surahNumber.compareTo(b.verse.surahNumber);
          if (surahCompare != 0) return surahCompare;
          return a.verse.verseNumber.compareTo(b.verse.verseNumber);
        });
        break;
      case 'alphabetical':
        _sortedBookmarks.sort((a, b) => a.verse.arabicText.compareTo(b.verse.arabicText));
        break;
    }
    
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header with sort options
        _buildHeader(),
        
        // Bookmarks list
        Expanded(child: _buildBookmarksList()),
      ],
    );
  }

  /// Build header with sort options
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Title and count
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bookmarks',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${widget.bookmarks.length} bookmark${widget.bookmarks.length == 1 ? '' : 's'}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          
          // Sort dropdown
          DropdownButton<String>(
            value: _sortBy,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sortBy = value;
                });
                _sortBookmarks();
              }
            },
            items: const [
              DropdownMenuItem(
                value: 'recent',
                child: Text('Most Recent'),
              ),
              DropdownMenuItem(
                value: 'surah',
                child: Text('By Surah'),
              ),
              DropdownMenuItem(
                value: 'alphabetical',
                child: Text('Alphabetical'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build bookmarks list
  Widget _buildBookmarksList() {
    if (widget.bookmarks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bookmark_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No Bookmarks Yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Bookmark verses while reading to save them here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _sortedBookmarks.length,
      itemBuilder: (context, index) {
        final bookmark = _sortedBookmarks[index];
        return _buildBookmarkCard(bookmark);
      },
    );
  }

  /// Build individual bookmark card
  Widget _buildBookmarkCard(QuranBookmark bookmark) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: InkWell(
        onTap: () => widget.onBookmarkSelected(bookmark),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with verse reference and actions
              Row(
                children: [
                  // Verse address
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      bookmark.verse.address,
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // Date
                  Text(
                    _formatDate(bookmark.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Actions
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    onSelected: (value) => _handleBookmarkAction(value, bookmark),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Edit Note'),
                          dense: true,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'copy',
                        child: ListTile(
                          leading: Icon(Icons.copy),
                          title: Text('Copy Verse'),
                          dense: true,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: ListTile(
                          leading: Icon(Icons.share),
                          title: Text('Share'),
                          dense: true,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text('Delete', style: TextStyle(color: Colors.red)),
                          dense: true,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Arabic text (truncated)
              Text(
                bookmark.verse.arabicText,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontFamily: 'Amiri',
                  height: 1.6,
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              // Note (if exists)
              if (bookmark.note != null && bookmark.note!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.note,
                            size: 16,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Note',
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Text(
                        bookmark.note!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Tags (if any)
              if (bookmark.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 6,
                  runSpacing: 4,
                  children: bookmark.tags.map((tag) => Chip(
                    label: Text(
                      tag,
                      style: Theme.of(context).textTheme.labelSmall,
                    ),
                    backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                    side: BorderSide.none,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                  )).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks == 1 ? '' : 's'} ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  /// Handle bookmark actions
  void _handleBookmarkAction(String action, QuranBookmark bookmark) {
    switch (action) {
      case 'edit':
        _showEditNoteDialog(bookmark);
        break;
      case 'copy':
        // TODO: Implement copy to clipboard
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Verse copied to clipboard')),
        );
        break;
      case 'share':
        // TODO: Implement share functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Share functionality coming soon')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(bookmark);
        break;
    }
  }

  /// Show edit note dialog
  void _showEditNoteDialog(QuranBookmark bookmark) {
    final TextEditingController noteController = TextEditingController();
    noteController.text = bookmark.note ?? '';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Note for ${bookmark.verse.address}'),
        content: TextField(
          controller: noteController,
          decoration: const InputDecoration(
            hintText: 'Enter your note...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Update bookmark note
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Note updated')),
              );
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(QuranBookmark bookmark) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Bookmark'),
        content: Text('Are you sure you want to delete the bookmark for ${bookmark.verse.address}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.onBookmarkDeleted(bookmark);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
