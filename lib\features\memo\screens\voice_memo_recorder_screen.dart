import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'dart:math';
import '../models/voice_memo.dart';
import '../providers/voice_memo_provider.dart';
import '../../../core/services/audio_service.dart' as core_audio;

class VoiceMemoRecorderScreen extends ConsumerStatefulWidget {
  final VoiceMemo? existingMemo;

  const VoiceMemoRecorderScreen({
    super.key,
    this.existingMemo,
  });

  @override
  ConsumerState<VoiceMemoRecorderScreen> createState() => _VoiceMemoRecorderScreenState();
}

class _VoiceMemoRecorderScreenState extends ConsumerState<VoiceMemoRecorderScreen>
    with TickerProviderStateMixin {
  final core_audio.AudioService _audioService = core_audio.AudioService();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  
  core_audio.RecordingState _recordingState = core_audio.RecordingState.idle;
  core_audio.PlaybackState _playbackState = core_audio.PlaybackState.idle;
  Duration _recordingDuration = Duration.zero;
  Duration _playbackDuration = Duration.zero;
  Duration _playbackPosition = Duration.zero;
  double _amplitude = 0.0;
  
  Color _selectedColor = Colors.blue;
  RecordingQuality _selectedQuality = RecordingQuality.medium;
  String? _recordedFilePath;
  
  List<double> _waveformData = [];
  StreamSubscription? _recordingStateSubscription;
  StreamSubscription? _playbackStateSubscription;
  StreamSubscription? _recordingDurationSubscription;
  StreamSubscription? _playbackPositionSubscription;
  StreamSubscription? _playbackDurationSubscription;
  StreamSubscription? _amplitudeSubscription;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    
    _initializeAudioService();
    _setupStreamListeners();
    
    if (widget.existingMemo != null) {
      _loadExistingMemo();
    }
  }

  void _loadExistingMemo() {
    final memo = widget.existingMemo!;
    _titleController.text = memo.title;
    _descriptionController.text = memo.description ?? '';
    _selectedColor = memo.color;
    _selectedQuality = memo.quality;
    _recordedFilePath = memo.filePath;
    
    if (_recordedFilePath != null && _recordedFilePath!.isNotEmpty) {
      _loadAudioDuration();
    }
  }

  Future<void> _initializeAudioService() async {
    await _audioService.initialize();
  }

  void _setupStreamListeners() {
    _recordingStateSubscription = _audioService.recordingStateStream.listen((state) {
      setState(() {
        _recordingState = state;
      });
      
      if (state == core_audio.RecordingState.recording) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    });

    _playbackStateSubscription = _audioService.playbackStateStream.listen((state) {
      setState(() {
        _playbackState = state;
      });
    });

    _recordingDurationSubscription = _audioService.recordingDurationStream.listen((duration) {
      setState(() {
        _recordingDuration = duration;
      });
    });

    _playbackPositionSubscription = _audioService.playbackPositionStream.listen((position) {
      setState(() {
        _playbackPosition = position;
      });
    });

    _playbackDurationSubscription = _audioService.playbackDurationStream.listen((duration) {
      setState(() {
        _playbackDuration = duration;
      });
    });

    _amplitudeSubscription = _audioService.amplitudeStream.listen((amplitude) {
      setState(() {
        _amplitude = amplitude;
        _addWaveformData(amplitude);
      });
    });
  }

  void _addWaveformData(double amplitude) {
    _waveformData.add(amplitude);
    if (_waveformData.length > 100) {
      _waveformData.removeAt(0);
    }
  }

  Future<void> _loadAudioDuration() async {
    if (_recordedFilePath != null) {
      final duration = await _audioService.getAudioDuration(_recordedFilePath!);
      if (duration != null) {
        setState(() {
          _playbackDuration = duration;
        });
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    
    _recordingStateSubscription?.cancel();
    _playbackStateSubscription?.cancel();
    _recordingDurationSubscription?.cancel();
    _playbackPositionSubscription?.cancel();
    _playbackDurationSubscription?.cancel();
    _amplitudeSubscription?.cancel();
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingMemo == null ? 'Record Voice Memo' : 'Edit Voice Memo'),
        backgroundColor: _selectedColor,
        foregroundColor: Colors.white,
        actions: [
          if (_recordedFilePath != null)
            IconButton(
              onPressed: _saveVoiceMemo,
              icon: const Icon(Icons.save),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Recording Controls
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Waveform Visualization
                    Container(
                      height: 100,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: _buildWaveform(),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Duration Display
                    Text(
                      _formatDuration(_recordingState == core_audio.RecordingState.idle
                          ? _playbackDuration
                          : _recordingDuration),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _selectedColor,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Control Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // Delete/Reset Button
                        if (_recordedFilePath != null || _recordingState != core_audio.RecordingState.idle)
                          IconButton(
                            onPressed: _resetRecording,
                            icon: const Icon(Icons.delete),
                            iconSize: 32,
                            color: Colors.red,
                          ),
                        
                        // Record/Stop Button
                        AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _recordingState == core_audio.RecordingState.recording
                                  ? _pulseAnimation.value
                                  : 1.0,
                              child: Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: _getRecordButtonColor(),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: _getRecordButtonColor().withValues(alpha: 0.3),
                                      blurRadius: 10,
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                                child: IconButton(
                                  onPressed: _toggleRecording,
                                  icon: Icon(
                                    _getRecordButtonIcon(),
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        
                        // Play/Pause Button
                        if (_recordedFilePath != null)
                          IconButton(
                            onPressed: _togglePlayback,
                            icon: Icon(
                              _playbackState == core_audio.PlaybackState.playing
                                  ? Icons.pause
                                  : Icons.play_arrow,
                            ),
                            iconSize: 32,
                            color: _selectedColor,
                          ),
                      ],
                    ),
                    
                    // Playback Progress
                    if (_recordedFilePath != null && _playbackDuration.inMilliseconds > 0) ...[
                      const SizedBox(height: 16),
                      SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          thumbColor: _selectedColor,
                          activeTrackColor: _selectedColor,
                          inactiveTrackColor: _selectedColor.withValues(alpha: 0.3),
                        ),
                        child: Slider(
                          value: _playbackPosition.inMilliseconds.toDouble(),
                          max: _playbackDuration.inMilliseconds.toDouble(),
                          onChanged: (value) {
                            _audioService.seekTo(Duration(milliseconds: value.toInt()));
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Memo Details
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Memo Details',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    TextField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title',
                        border: OutlineInputBorder(),
                        hintText: 'Enter memo title',
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description (optional)',
                        border: OutlineInputBorder(),
                        hintText: 'Add description or notes',
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Settings',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    // Color Selection
                    Text(
                      'Color',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        Colors.blue,
                        Colors.green,
                        Colors.red,
                        Colors.orange,
                        Colors.purple,
                        Colors.teal,
                        Colors.indigo,
                        Colors.brown,
                      ].map((color) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedColor = color;
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                              border: _selectedColor == color
                                  ? Border.all(color: Colors.black, width: 3)
                                  : null,
                            ),
                            child: _selectedColor == color
                                ? const Icon(Icons.check, color: Colors.white)
                                : null,
                          ),
                        );
                      }).toList(),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Quality Selection
                    Text(
                      'Recording Quality',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SegmentedButton<RecordingQuality>(
                      segments: const [
                        ButtonSegment(
                          value: RecordingQuality.low,
                          label: Text('Low'),
                        ),
                        ButtonSegment(
                          value: RecordingQuality.medium,
                          label: Text('Medium'),
                        ),
                        ButtonSegment(
                          value: RecordingQuality.high,
                          label: Text('High'),
                        ),
                      ],
                      selected: {_selectedQuality},
                      onSelectionChanged: (Set<RecordingQuality> selection) {
                        setState(() {
                          _selectedQuality = selection.first;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Save Button
            if (_recordedFilePath != null)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _saveVoiceMemo,
                  icon: const Icon(Icons.save),
                  label: const Text('Save Voice Memo'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _selectedColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaveform() {
    if (_waveformData.isEmpty) {
      return const Center(
        child: Text(
          'Tap record to start',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return CustomPaint(
      painter: WaveformPainter(
        waveformData: _waveformData,
        color: _selectedColor,
        amplitude: _amplitude,
      ),
      size: const Size(double.infinity, 100),
    );
  }

  Color _getRecordButtonColor() {
    switch (_recordingState) {
      case core_audio.RecordingState.recording:
        return Colors.red;
      case core_audio.RecordingState.paused:
        return Colors.orange;
      default:
        return _selectedColor;
    }
  }

  IconData _getRecordButtonIcon() {
    switch (_recordingState) {
      case core_audio.RecordingState.recording:
        return Icons.stop;
      case core_audio.RecordingState.paused:
        return Icons.fiber_manual_record;
      default:
        return Icons.fiber_manual_record;
    }
  }

  Future<void> _toggleRecording() async {
    switch (_recordingState) {
      case core_audio.RecordingState.idle:
        await _startRecording();
        break;
      case core_audio.RecordingState.recording:
        await _stopRecording();
        break;
      case core_audio.RecordingState.paused:
        await _audioService.resumeRecording();
        break;
      case core_audio.RecordingState.stopped:
        await _startRecording();
        break;
    }
  }

  Future<void> _startRecording() async {
    final hasPermission = await _audioService.hasPermissions();
    if (!hasPermission) {
      final granted = await _audioService.requestPermissions();
      if (!granted) {
        _showPermissionDialog();
        return;
      }
    }

    final bitRate = _getBitRateForQuality(_selectedQuality);
    final success = await _audioService.startRecording(bitRate: bitRate);
    
    if (!success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to start recording')),
      );
    }
  }

  Future<void> _stopRecording() async {
    final filePath = await _audioService.stopRecording();
    if (filePath != null) {
      setState(() {
        _recordedFilePath = filePath;
      });
      await _loadAudioDuration();
    }
  }

  Future<void> _togglePlayback() async {
    if (_recordedFilePath == null) return;

    switch (_playbackState) {
      case core_audio.PlaybackState.idle:
      case core_audio.PlaybackState.stopped:
        await _audioService.playAudio(_recordedFilePath!);
        break;
      case core_audio.PlaybackState.playing:
        await _audioService.pausePlayback();
        break;
      case core_audio.PlaybackState.paused:
        await _audioService.resumePlayback();
        break;
    }
  }

  void _resetRecording() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Recording'),
        content: const Text('Are you sure you want to delete this recording?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performReset();
            },
            child: const Text('Delete'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  Future<void> _performReset() async {
    await _audioService.stopRecording();
    await _audioService.stopPlayback();
    
    if (_recordedFilePath != null) {
      await _audioService.deleteAudioFile(_recordedFilePath!);
    }
    
    setState(() {
      _recordedFilePath = null;
      _recordingDuration = Duration.zero;
      _playbackDuration = Duration.zero;
      _playbackPosition = Duration.zero;
      _waveformData.clear();
    });
  }

  Future<void> _saveVoiceMemo() async {
    if (_recordedFilePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No recording to save')),
      );
      return;
    }

    final title = _titleController.text.trim();
    if (title.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title')),
      );
      return;
    }

    final fileSize = await _audioService.getFileSize(_recordedFilePath!);
    final duration = _playbackDuration.inMilliseconds > 0 
        ? _playbackDuration 
        : _recordingDuration;

    if (widget.existingMemo != null) {
      // Update existing memo
      widget.existingMemo!.updateMemo(
        title: title,
        description: _descriptionController.text.trim(),
        color: _selectedColor,
      );
      
      ref.read(voiceMemosProvider.notifier).updateMemo(widget.existingMemo!);
    } else {
      // Create new memo
      final memo = VoiceMemo.create(
        title: title,
        filePath: _recordedFilePath!,
        durationMs: duration.inMilliseconds,
        fileSizeBytes: fileSize.toDouble(),
        userId: '', // Will be set by provider
        description: _descriptionController.text.trim(),
        color: _selectedColor,
        quality: _selectedQuality,
      );
      
      ref.read(voiceMemosProvider.notifier).addMemo(memo);
    }

    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.existingMemo != null 
              ? 'Voice memo updated!' 
              : 'Voice memo saved!',
        ),
      ),
    );
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Microphone Permission Required'),
        content: const Text(
          'This app needs microphone permission to record voice memos. '
          'Please grant permission in your device settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _audioService.requestPermissions();
            },
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );
  }

  int _getBitRateForQuality(RecordingQuality quality) {
    switch (quality) {
      case RecordingQuality.low:
        return 64000;
      case RecordingQuality.medium:
        return 128000;
      case RecordingQuality.high:
        return 256000;
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

class WaveformPainter extends CustomPainter {
  final List<double> waveformData;
  final Color color;
  final double amplitude;

  WaveformPainter({
    required this.waveformData,
    required this.color,
    required this.amplitude,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final centerY = size.height / 2;
    final stepX = size.width / max(waveformData.length, 1);

    for (int i = 0; i < waveformData.length; i++) {
      final x = i * stepX;
      final normalizedAmplitude = (waveformData[i] / 100).clamp(0.0, 1.0);
      final y = centerY + (normalizedAmplitude * centerY * 0.8 * (i % 2 == 0 ? 1 : -1));
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
