import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/money_settings.dart';
import '../providers/money_settings_provider.dart';

/// Money Flow settings and configuration screen
class MoneySettingsScreen extends ConsumerStatefulWidget {
  const MoneySettingsScreen({super.key});

  @override
  ConsumerState<MoneySettingsScreen> createState() => _MoneySettingsScreenState();
}

class _MoneySettingsScreenState extends ConsumerState<MoneySettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(moneySettingsProvider);
    
    return StandardizedScaffold(
      title: 'Money Flow Settings',
      currentRoute: '/money-flow/settings',
      showBackButton: true,
      body: SingleChildScrollView(
        padding: ResponsiveBreakpoints.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Currency Settings
            SectionHeader(
              title: 'Currency Settings',
              subtitle: 'Configure your preferred currency and formatting',
            ),
            
            StandardCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Primary Currency
                  ListTile(
                    leading: const Icon(Icons.attach_money),
                    title: const Text('Primary Currency'),
                    subtitle: Text(settings.primaryCurrency),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _showCurrencyPicker(context, settings),
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Currency Symbol Position
                  ListTile(
                    leading: const Icon(Icons.format_align_left),
                    title: const Text('Currency Symbol Position'),
                    subtitle: Text(settings.currencySymbolPosition.name),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _showSymbolPositionPicker(context, settings),
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Decimal Places
                  ListTile(
                    leading: const Icon(Icons.format_list_numbered),
                    title: const Text('Decimal Places'),
                    subtitle: Text('${settings.decimalPlaces} places'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _showDecimalPlacesPicker(context, settings),
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Thousand Separator
                  SwitchListTile(
                    secondary: const Icon(Icons.format_list_numbered),
                    title: const Text('Use Thousand Separator'),
                    subtitle: const Text('Add commas to large numbers'),
                    value: settings.useThousandSeparator,
                    onChanged: (value) {
                      ref.read(moneySettingsProvider.notifier).updateSettings(
                        settings.copyWith(useThousandSeparator: value),
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: ResponsiveSpacing.lg(context)),
            
            // Default Settings
            SectionHeader(
              title: 'Default Settings',
              subtitle: 'Set default values for new transactions',
            ),
            
            StandardCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Default Account
                  ListTile(
                    leading: const Icon(Icons.account_balance_wallet),
                    title: const Text('Default Account'),
                    subtitle: Text(settings.defaultAccountId.isEmpty 
                        ? 'None selected' 
                        : 'Account selected'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _showDefaultAccountPicker(context, settings),
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Auto-categorize
                  SwitchListTile(
                    secondary: const Icon(Icons.auto_awesome),
                    title: const Text('Auto-categorize Transactions'),
                    subtitle: const Text('Suggest categories based on description'),
                    value: settings.autoCategorize,
                    onChanged: (value) {
                      ref.read(moneySettingsProvider.notifier).updateSettings(
                        settings.copyWith(autoCategorize: value),
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: ResponsiveSpacing.lg(context)),
            
            // Notifications
            SectionHeader(
              title: 'Notifications',
              subtitle: 'Configure alerts and reminders',
            ),
            
            StandardCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Budget Alerts
                  SwitchListTile(
                    secondary: const Icon(Icons.notifications),
                    title: const Text('Budget Alerts'),
                    subtitle: const Text('Get notified when approaching budget limits'),
                    value: settings.enableBudgetAlerts,
                    onChanged: (value) {
                      ref.read(moneySettingsProvider.notifier).updateSettings(
                        settings.copyWith(enableBudgetAlerts: value),
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Transaction Reminders
                  SwitchListTile(
                    secondary: const Icon(Icons.schedule),
                    title: const Text('Transaction Reminders'),
                    subtitle: const Text('Remind to log daily transactions'),
                    value: settings.enableTransactionReminders,
                    onChanged: (value) {
                      ref.read(moneySettingsProvider.notifier).updateSettings(
                        settings.copyWith(enableTransactionReminders: value),
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Low Balance Alerts
                  SwitchListTile(
                    secondary: const Icon(Icons.warning),
                    title: const Text('Low Balance Alerts'),
                    subtitle: const Text('Alert when account balance is low'),
                    value: settings.enableLowBalanceAlerts,
                    onChanged: (value) {
                      ref.read(moneySettingsProvider.notifier).updateSettings(
                        settings.copyWith(enableLowBalanceAlerts: value),
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: ResponsiveSpacing.lg(context)),
            
            // Data Management
            SectionHeader(
              title: 'Data Management',
              subtitle: 'Backup, restore, and privacy settings',
            ),
            
            StandardCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Export Data
                  ListTile(
                    leading: const Icon(Icons.download),
                    title: const Text('Export Data'),
                    subtitle: const Text('Download your financial data'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _exportData(context),
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Import Data
                  ListTile(
                    leading: const Icon(Icons.upload),
                    title: const Text('Import Data'),
                    subtitle: const Text('Import transactions from file'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => _importData(context),
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Clear All Data
                  ListTile(
                    leading: const Icon(Icons.delete_forever, color: Colors.red),
                    title: const Text('Clear All Data', style: TextStyle(color: Colors.red)),
                    subtitle: const Text('Permanently delete all financial data'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.red),
                    onTap: () => _clearAllData(context),
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: ResponsiveSpacing.lg(context)),
            
            // Privacy & Security
            SectionHeader(
              title: 'Privacy & Security',
              subtitle: 'Protect your financial information',
            ),
            
            StandardCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Require PIN
                  SwitchListTile(
                    secondary: const Icon(Icons.lock),
                    title: const Text('Require PIN'),
                    subtitle: const Text('Require PIN to access Money Flow'),
                    value: settings.requirePin,
                    onChanged: (value) {
                      if (value) {
                        _setupPin(context, settings);
                      } else {
                        ref.read(moneySettingsProvider.notifier).updateSettings(
                          settings.copyWith(requirePin: false),
                        );
                      }
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const Divider(),
                  
                  // Hide Amounts
                  SwitchListTile(
                    secondary: const Icon(Icons.visibility_off),
                    title: const Text('Hide Amounts in Overview'),
                    subtitle: const Text('Show *** instead of actual amounts'),
                    value: settings.hideAmountsInOverview,
                    onChanged: (value) {
                      ref.read(moneySettingsProvider.notifier).updateSettings(
                        settings.copyWith(hideAmountsInOverview: value),
                      );
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            
            SizedBox(height: ResponsiveSpacing.xl(context)),
          ],
        ),
      ),
    );
  }

  /// Show currency picker
  void _showCurrencyPicker(BuildContext context, MoneySettings settings) {
    final currencies = [
      'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY',
      'INR', 'BRL', 'RUB', 'KRW', 'SGD', 'HKD', 'NOK', 'SEK',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: currencies.length,
            itemBuilder: (context, index) {
              final currency = currencies[index];
              return ListTile(
                title: Text(currency),
                trailing: settings.primaryCurrency == currency
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  ref.read(moneySettingsProvider.notifier).updateSettings(
                    settings.copyWith(primaryCurrency: currency),
                  );
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// Show symbol position picker
  void _showSymbolPositionPicker(BuildContext context, MoneySettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Currency Symbol Position'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: CurrencySymbolPosition.values.map((position) {
            return ListTile(
              title: Text(position.name),
              subtitle: Text(_getSymbolPositionExample(position)),
              trailing: settings.currencySymbolPosition == position
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(moneySettingsProvider.notifier).updateSettings(
                  settings.copyWith(currencySymbolPosition: position),
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show decimal places picker
  void _showDecimalPlacesPicker(BuildContext context, MoneySettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Decimal Places'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [0, 1, 2, 3, 4].map((places) {
            return ListTile(
              title: Text('$places decimal places'),
              subtitle: Text(_getDecimalExample(places)),
              trailing: settings.decimalPlaces == places
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(moneySettingsProvider.notifier).updateSettings(
                  settings.copyWith(decimalPlaces: places),
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show default account picker
  void _showDefaultAccountPicker(BuildContext context, MoneySettings settings) {
    // TODO: Implement account picker
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Account picker will be implemented')),
    );
  }

  /// Export data
  void _exportData(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality will be implemented')),
    );
  }

  /// Import data
  void _importData(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality will be implemented')),
    );
  }

  /// Clear all data
  void _clearAllData(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'Are you sure you want to permanently delete all your financial data? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement clear all data
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Clear data functionality will be implemented')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  /// Setup PIN
  void _setupPin(BuildContext context, MoneySettings settings) {
    // TODO: Implement PIN setup
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('PIN setup will be implemented')),
    );
  }

  /// Get symbol position example
  String _getSymbolPositionExample(CurrencySymbolPosition position) {
    switch (position) {
      case CurrencySymbolPosition.before:
        return '\$100.00';
      case CurrencySymbolPosition.after:
        return '100.00\$';
      case CurrencySymbolPosition.beforeWithSpace:
        return '\$ 100.00';
      case CurrencySymbolPosition.afterWithSpace:
        return '100.00 \$';
    }
  }

  /// Get decimal example
  String _getDecimalExample(int places) {
    final amount = 1234.5678;
    return '\$${amount.toStringAsFixed(places)}';
  }
}
