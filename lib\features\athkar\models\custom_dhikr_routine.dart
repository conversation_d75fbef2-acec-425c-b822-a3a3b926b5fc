import 'package:flutter/material.dart';

enum DhikrStepType { dhikr, pause, reminder }

class DhikrStep {
  int id = 0;
  DhikrStepType type = DhikrStepType.dhikr;
  String arabicText = '';
  String transliteration = '';
  String translation = '';
  int targetCount = 1;
  int currentCount = 0;
  Duration duration = const Duration(seconds: 30); // For pauses
  String reminderText = ''; // For reminders
  Color color = Colors.blue;
  bool isCompleted = false;
  
  // Audio settings
  bool playSound = true;
  String? audioPath;
  double volume = 0.7;
  
  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();

  DhikrStep();

  DhikrStep.create({
    required this.type,
    this.arabicText = '',
    this.transliteration = '',
    this.translation = '',
    this.targetCount = 1,
    this.duration = const Duration(seconds: 30),
    this.reminderText = '',
    this.color = Colors.blue,
    this.playSound = true,
    this.volume = 0.7,
  }) {
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void incrementCount() {
    if (currentCount < targetCount) {
      currentCount++;
      updateTimestamp();
    }
    if (currentCount >= targetCount) {
      isCompleted = true;
    }
  }

  void resetCount() {
    currentCount = 0;
    isCompleted = false;
    updateTimestamp();
  }

  double get progressPercentage {
    if (targetCount == 0) return 0.0;
    return (currentCount / targetCount).clamp(0.0, 1.0);
  }

  String get displayText {
    switch (type) {
      case DhikrStepType.dhikr:
        return arabicText.isNotEmpty ? arabicText : transliteration;
      case DhikrStepType.pause:
        return 'Pause for ${duration.inSeconds} seconds';
      case DhikrStepType.reminder:
        return reminderText;
    }
  }

  String get typeDisplayName {
    switch (type) {
      case DhikrStepType.dhikr:
        return 'Dhikr';
      case DhikrStepType.pause:
        return 'Pause';
      case DhikrStepType.reminder:
        return 'Reminder';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'targetCount': targetCount,
      'currentCount': currentCount,
      'duration': duration.inSeconds,
      'reminderText': reminderText,
      'color': (color.r * 255).round() << 16 | (color.g * 255).round() << 8 | (color.b * 255).round() | (color.a * 255).round() << 24,
      'isCompleted': isCompleted,
      'playSound': playSound,
      'audioPath': audioPath,
      'volume': volume,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory DhikrStep.fromJson(Map<String, dynamic> json) {
    final step = DhikrStep();
    step.id = json['id'] ?? 0;
    step.type = DhikrStepType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => DhikrStepType.dhikr,
    );
    step.arabicText = json['arabicText'] ?? '';
    step.transliteration = json['transliteration'] ?? '';
    step.translation = json['translation'] ?? '';
    step.targetCount = json['targetCount'] ?? 1;
    step.currentCount = json['currentCount'] ?? 0;
    step.duration = Duration(seconds: json['duration'] ?? 30);
    step.reminderText = json['reminderText'] ?? '';
    step.color = Color(json['color'] ?? ((Colors.blue.r * 255).round() << 16 | (Colors.blue.g * 255).round() << 8 | (Colors.blue.b * 255).round() | (Colors.blue.a * 255).round() << 24));
    step.isCompleted = json['isCompleted'] ?? false;
    step.playSound = json['playSound'] ?? true;
    step.audioPath = json['audioPath'];
    step.volume = (json['volume'] ?? 0.7).toDouble();
    step.createdAt = DateTime.parse(json['createdAt']);
    step.updatedAt = DateTime.parse(json['updatedAt']);
    return step;
  }
}

class CustomDhikrRoutine {
  int id = 0;
  String name = '';
  String description = '';
  List<DhikrStep> steps = [];
  Color themeColor = Colors.blue;
  bool isActive = true;
  bool repeatRoutine = false;
  int currentStepIndex = 0;
  
  // Reminder settings
  bool enableReminders = false;
  List<TimeOfDay> reminderTimes = [];
  List<int> reminderDays = []; // 1-7 for Monday-Sunday
  
  // Statistics
  int totalCompletions = 0;
  DateTime? lastCompletedAt;
  Duration totalTimeSpent = Duration.zero;
  
  // User and sync
  String userId = '';
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  CustomDhikrRoutine();

  CustomDhikrRoutine.create({
    required this.name,
    required this.userId,
    this.description = '',
    this.themeColor = Colors.blue,
    this.enableReminders = false,
    this.repeatRoutine = false,
    List<DhikrStep>? steps,
  }) {
    this.steps = steps ?? [];
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  DhikrStep? get currentStep {
    if (currentStepIndex >= 0 && currentStepIndex < steps.length) {
      return steps[currentStepIndex];
    }
    return null;
  }

  bool get isCompleted {
    return steps.every((step) => step.isCompleted);
  }

  double get overallProgress {
    if (steps.isEmpty) return 0.0;
    final completedSteps = steps.where((step) => step.isCompleted).length;
    return completedSteps / steps.length;
  }

  int get totalDhikrCount {
    return steps
        .where((step) => step.type == DhikrStepType.dhikr)
        .fold(0, (sum, step) => sum + step.targetCount);
  }

  int get completedDhikrCount {
    return steps
        .where((step) => step.type == DhikrStepType.dhikr)
        .fold(0, (sum, step) => sum + step.currentCount);
  }

  Duration get estimatedDuration {
    Duration total = Duration.zero;
    for (final step in steps) {
      switch (step.type) {
        case DhikrStepType.dhikr:
          // Estimate 2 seconds per dhikr
          total += Duration(seconds: step.targetCount * 2);
          break;
        case DhikrStepType.pause:
          total += step.duration;
          break;
        case DhikrStepType.reminder:
          // Estimate 5 seconds for reminder
          total += const Duration(seconds: 5);
          break;
      }
    }
    return total;
  }

  void addStep(DhikrStep step) {
    step.id = steps.length + 1;
    steps.add(step);
    updateTimestamp();
  }

  void removeStep(int stepId) {
    steps.removeWhere((step) => step.id == stepId);
    _reorderStepIds();
    updateTimestamp();
  }

  void reorderSteps(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final step = steps.removeAt(oldIndex);
    steps.insert(newIndex, step);
    _reorderStepIds();
    updateTimestamp();
  }

  void _reorderStepIds() {
    for (int i = 0; i < steps.length; i++) {
      steps[i].id = i + 1;
    }
  }

  bool moveToNextStep() {
    if (currentStepIndex < steps.length - 1) {
      currentStepIndex++;
      updateTimestamp();
      return true;
    } else if (repeatRoutine && !isCompleted) {
      resetRoutine();
      return true;
    }
    return false;
  }

  void moveToPreviousStep() {
    if (currentStepIndex > 0) {
      currentStepIndex--;
      updateTimestamp();
    }
  }

  void resetRoutine() {
    currentStepIndex = 0;
    for (final step in steps) {
      step.resetCount();
    }
    updateTimestamp();
  }

  void completeRoutine() {
    totalCompletions++;
    lastCompletedAt = DateTime.now();
    updateTimestamp();
    
    if (repeatRoutine) {
      resetRoutine();
    }
  }

  void addReminderTime(TimeOfDay time) {
    if (!reminderTimes.contains(time)) {
      reminderTimes.add(time);
      updateTimestamp();
    }
  }

  void removeReminderTime(TimeOfDay time) {
    reminderTimes.remove(time);
    updateTimestamp();
  }

  void toggleReminderDay(int day) {
    if (reminderDays.contains(day)) {
      reminderDays.remove(day);
    } else {
      reminderDays.add(day);
    }
    updateTimestamp();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'steps': steps.map((step) => step.toJson()).toList(),
      'themeColor': (themeColor.r * 255).round() << 16 | (themeColor.g * 255).round() << 8 | (themeColor.b * 255).round() | (themeColor.a * 255).round() << 24,
      'isActive': isActive,
      'repeatRoutine': repeatRoutine,
      'currentStepIndex': currentStepIndex,
      'enableReminders': enableReminders,
      'reminderTimes': reminderTimes.map((time) => '${time.hour}:${time.minute}').toList(),
      'reminderDays': reminderDays,
      'totalCompletions': totalCompletions,
      'lastCompletedAt': lastCompletedAt?.toIso8601String(),
      'totalTimeSpent': totalTimeSpent.inSeconds,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory CustomDhikrRoutine.fromJson(Map<String, dynamic> json) {
    final routine = CustomDhikrRoutine();
    routine.id = json['id'] ?? 0;
    routine.name = json['name'] ?? '';
    routine.description = json['description'] ?? '';
    routine.steps = (json['steps'] as List<dynamic>?)
        ?.map((stepJson) => DhikrStep.fromJson(stepJson))
        .toList() ?? [];
    routine.themeColor = Color(json['themeColor'] ?? ((Colors.blue.r * 255).round() << 16 | (Colors.blue.g * 255).round() << 8 | (Colors.blue.b * 255).round() | (Colors.blue.a * 255).round() << 24));
    routine.isActive = json['isActive'] ?? true;
    routine.repeatRoutine = json['repeatRoutine'] ?? false;
    routine.currentStepIndex = json['currentStepIndex'] ?? 0;
    routine.enableReminders = json['enableReminders'] ?? false;
    routine.reminderTimes = (json['reminderTimes'] as List<dynamic>?)
        ?.map((timeStr) {
          final parts = timeStr.split(':');
          return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
        })
        .toList() ?? [];
    routine.reminderDays = List<int>.from(json['reminderDays'] ?? []);
    routine.totalCompletions = json['totalCompletions'] ?? 0;
    routine.lastCompletedAt = json['lastCompletedAt'] != null 
        ? DateTime.parse(json['lastCompletedAt']) 
        : null;
    routine.totalTimeSpent = Duration(seconds: json['totalTimeSpent'] ?? 0);
    routine.userId = json['userId'] ?? '';
    routine.createdAt = DateTime.parse(json['createdAt']);
    routine.updatedAt = DateTime.parse(json['updatedAt']);
    routine.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    routine.isDeleted = json['isDeleted'] ?? false;
    routine.syncId = json['syncId'];
    return routine;
  }
}
