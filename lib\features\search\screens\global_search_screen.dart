import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/services/search_service.dart';

class GlobalSearchScreen extends ConsumerStatefulWidget {
  const GlobalSearchScreen({super.key});

  @override
  ConsumerState<GlobalSearchScreen> createState() => _GlobalSearchScreenState();
}

class _GlobalSearchScreenState extends ConsumerState<GlobalSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  SearchCategory _selectedCategory = SearchCategory.all;
  String _currentQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _searchFocusNode.requestFocus();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text<PERSON>ield(
          controller: _searchController,
          focusNode: _searchFocusNode,
          decoration: const InputDecoration(
            hintText: 'Search across all your data...',
            border: InputBorder.none,
            hintStyle: TextStyle(color: Colors.grey),
          ),
          style: const TextStyle(fontSize: 18),
          onChanged: _onSearchChanged,
          onSubmitted: _onSearchSubmitted,
        ),
        actions: [
          if (_searchController.text.isNotEmpty)
            IconButton(
              onPressed: _clearSearch,
              icon: const Icon(Icons.clear),
            ),
        ],
      ),
      body: Column(
        children: [
          // Category Filter
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: SearchCategory.values.length,
              itemBuilder: (context, index) {
                final category = SearchCategory.values[index];
                final isSelected = category == _selectedCategory;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category.name.toUpperCase()),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                      if (_currentQuery.isNotEmpty) {
                        _performSearch(_currentQuery);
                      }
                    },
                    selectedColor: Theme.of(context).colorScheme.primary,
                    labelStyle: TextStyle(
                      color: isSelected ? Colors.white : null,
                      fontWeight: isSelected ? FontWeight.bold : null,
                    ),
                  ),
                );
              },
            ),
          ),
          
          const Divider(height: 1),
          
          // Search Results
          Expanded(
            child: _buildSearchContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchContent() {
    if (_currentQuery.isEmpty) {
      return _buildSearchSuggestions();
    }

    if (_isSearching) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Consumer(
      builder: (context, ref, child) {
        final searchResultsAsync = ref.watch(searchResultsProvider({
          'query': _currentQuery,
          'category': _selectedCategory,
          'limit': 50,
        }));

        return searchResultsAsync.when(
          data: (results) => _buildSearchResults(results),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error),
        );
      },
    );
  }

  Widget _buildSearchSuggestions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search Tips
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Search Tips',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text('• Search across notes, todos, voice memos, and financial data'),
                  const Text('• Use filters to narrow down results by category'),
                  const Text('• Search is case-insensitive and matches partial words'),
                  const Text('• Recent and relevant results appear first'),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Quick Search Categories
          Text(
            'Quick Search',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 12),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickSearchChip('Recent notes', Icons.note, 'notes'),
              _buildQuickSearchChip('Pending todos', Icons.check_box_outline_blank, 'todos pending'),
              _buildQuickSearchChip('Voice memos', Icons.mic, 'voice memos'),
              _buildQuickSearchChip('Expenses', Icons.trending_down, 'expenses'),
              _buildQuickSearchChip('Income', Icons.trending_up, 'income'),
              _buildQuickSearchChip('Accounts', Icons.account_balance_wallet, 'accounts'),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Search Categories
          Text(
            'Search Categories',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 12),
          
          ...SearchCategory.values.where((c) => c != SearchCategory.all).map((category) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Icon(
                    _getCategoryIcon(category),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                title: Text(category.name.toUpperCase()),
                subtitle: Text(_getCategoryDescription(category)),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  setState(() {
                    _selectedCategory = category;
                  });
                  _searchFocusNode.requestFocus();
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSearchResults(List<SearchResult> results) {
    if (results.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search terms or filters',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: results.length,
      itemBuilder: (context, index) {
        final result = results[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getCategoryColor(result.category),
              child: Icon(
                _getCategoryIcon(result.category),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(
              result.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.subtitle,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(result.category),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        result.categoryDisplayName.toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (result.date != null) ...[
                      const SizedBox(width: 8),
                      Text(
                        _formatDate(result.date!),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _navigateToResult(result),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Search Error',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'An error occurred while searching. Please try again.',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              if (_currentQuery.isNotEmpty) {
                _performSearch(_currentQuery);
              }
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSearchChip(String label, IconData icon, String query) {
    return ActionChip(
      avatar: Icon(icon, size: 18),
      label: Text(label),
      onPressed: () {
        _searchController.text = query;
        _onSearchSubmitted(query);
      },
    );
  }

  IconData _getCategoryIcon(SearchCategory category) {
    switch (category) {
      case SearchCategory.all:
        return Icons.search;
      case SearchCategory.notes:
        return Icons.note;
      case SearchCategory.todos:
        return Icons.check_box;
      case SearchCategory.voiceMemos:
        return Icons.mic;
      case SearchCategory.accounts:
        return Icons.account_balance_wallet;
      case SearchCategory.transactions:
        return Icons.receipt;
      case SearchCategory.categories:
        return Icons.category;
      case SearchCategory.athkar:
        return Icons.mosque;
      case SearchCategory.quran:
        return Icons.menu_book;
      case SearchCategory.tools:
        return Icons.build;
    }
  }

  Color _getCategoryColor(SearchCategory category) {
    switch (category) {
      case SearchCategory.all:
        return Colors.blue;
      case SearchCategory.notes:
        return Colors.orange;
      case SearchCategory.todos:
        return Colors.green;
      case SearchCategory.voiceMemos:
        return Colors.purple;
      case SearchCategory.accounts:
        return Colors.teal;
      case SearchCategory.transactions:
        return Colors.indigo;
      case SearchCategory.categories:
        return Colors.pink;
      case SearchCategory.athkar:
        return Colors.deepPurple;
      case SearchCategory.quran:
        return Colors.brown;
      case SearchCategory.tools:
        return Colors.grey;
    }
  }

  String _getCategoryDescription(SearchCategory category) {
    switch (category) {
      case SearchCategory.all:
        return 'Search everything';
      case SearchCategory.notes:
        return 'Search your notes and documents';
      case SearchCategory.todos:
        return 'Search tasks and reminders';
      case SearchCategory.voiceMemos:
        return 'Search voice recordings';
      case SearchCategory.accounts:
        return 'Search financial accounts';
      case SearchCategory.transactions:
        return 'Search financial transactions';
      case SearchCategory.categories:
        return 'Search expense categories';
      case SearchCategory.athkar:
        return 'Search dhikr and prayers';
      case SearchCategory.quran:
        return 'Search Quran verses';
      case SearchCategory.tools:
        return 'Search tools and calculators';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _currentQuery = query;
    });
    
    // Debounce search to avoid too many API calls
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_currentQuery == query && query.isNotEmpty) {
        _performSearch(query);
      }
    });
  }

  void _onSearchSubmitted(String query) {
    setState(() {
      _currentQuery = query;
    });
    _performSearch(query);
  }

  void _performSearch(String query) {
    setState(() {
      _isSearching = true;
    });
    
    // The search will be performed by the provider
    // Reset loading state after a delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _currentQuery = '';
      _isSearching = false;
    });
    _searchFocusNode.requestFocus();
  }

  void _navigateToResult(SearchResult result) {
    // Navigate to the specific result
    context.go(result.route);
  }
}
