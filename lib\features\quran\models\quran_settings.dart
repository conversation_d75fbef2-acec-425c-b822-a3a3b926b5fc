import 'package:flutter/material.dart';

/// Quran reading mode options
enum QuranReadingMode {
  page,
  verse,
  continuous,
  mushaf,
}

/// Translation language options
enum TranslationLanguage {
  english,
  urdu,
  french,
  spanish,
  german,
  turkish,
  indonesian,
  malay,
  bengali,
  hindi,
}

/// Arabic font options
enum ArabicFont {
  uthmanic,
  naskh,
  kufi,
  thuluth,
  nastaliq,
  amiri,
  scheherazade,
  lateef,
}

/// Tajweed highlighting options
enum TajweedStyle {
  none,
  basic,
  advanced,
  colorCoded,
  detailed,
}

/// Audio recitation speed
enum RecitationSpeed {
  slow,
  normal,
  fast,
}

/// Download quality options
enum DownloadQuality {
  low,
  medium,
  high,
  lossless,
}

/// Bookmark category options
enum BookmarkCategory {
  favorites,
  toRead,
  memorization,
  study,
  reflection,
  custom,
}

/// Quran Reader application settings
@immutable
class QuranSettings {
  // Display Settings
  final QuranReadingMode readingMode;
  final ArabicFont arabicFont;
  final double arabicFontSize;
  final double translationFontSize;
  final String translationFontFamily;
  final bool showTranslation;
  final bool showTransliteration;
  final List<TranslationLanguage> selectedTranslations;
  final bool enableDarkMode;
  final bool enableNightMode;
  final Color primaryColor;
  final Color backgroundColor;
  final Color textColor;

  // Tajweed Settings
  final TajweedStyle tajweedStyle;
  final bool enableTajweedHighlighting;
  final bool showTajweedRules;
  final Map<String, Color> tajweedColors;
  final bool enableTajweedAudio;
  final bool showTajweedLegend;

  // Reading Settings
  final bool enableAutoScroll;
  final double autoScrollSpeed;
  final bool highlightCurrentVerse;
  final bool showVerseNumbers;
  final bool showSurahHeaders;
  final bool showJuzMarkers;
  final bool showHizbMarkers;
  final bool showSajdahMarkers;
  final double lineSpacing;
  final double wordSpacing;
  final bool enableFullscreen;
  final bool keepScreenOn;
  final bool enableGestures;
  final bool enableZoom;

  // Audio Settings
  final String selectedReciter;
  final List<String> downloadedReciters;
  final RecitationSpeed recitationSpeed;
  final bool enableAudioHighlighting;
  final bool enableRepeatMode;
  final int repeatCount;
  final bool enableAutoPlay;
  final bool enableContinuousPlay;
  final double audioVolume;
  final bool enableAudioBookmarks;
  final DownloadQuality audioQuality;

  // Search Settings
  final bool enableAdvancedSearch;
  final bool searchInTranslation;
  final bool searchInTransliteration;
  final bool enableRootWordSearch;
  final bool enableTopicSearch;
  final bool caseSensitiveSearch;
  final bool wholeWordSearch;
  final List<String> searchHistory;
  final int maxSearchHistory;

  // Bookmark Settings
  final bool enableBookmarks;
  final List<BookmarkCategory> bookmarkCategories;
  final bool enableBookmarkNotes;
  final bool enableBookmarkTags;
  final bool enableBookmarkSharing;
  final bool autoBookmarkLastRead;
  final String defaultBookmarkCategory;

  // Progress Tracking
  final bool trackReadingProgress;
  final bool showProgressStats;
  final bool enableReadingGoals;
  final int dailyReadingGoal;
  final bool enableStreaks;
  final bool enableAchievements;
  final bool showCompletionPercentage;
  final Map<int, double> surahProgress;
  final DateTime? lastReadDate;
  final String lastReadPosition;

  // Offline & Download Settings
  final bool enableOfflineMode;
  final bool autoDownloadTranslations;
  final bool autoDownloadAudio;
  final List<String> downloadedTranslations;
  final List<String> downloadedAudioSurahs;
  final DownloadQuality downloadQuality;
  final bool downloadOnWifiOnly;
  final int maxCacheSize;
  final bool enableSmartDownload;

  // Notification Settings
  final bool enableReminders;
  final bool enableDailyReminder;
  final TimeOfDay? dailyReminderTime;
  final bool enableWeeklyReminder;
  final int weeklyReminderDay;
  final TimeOfDay? weeklyReminderTime;
  final bool enableCustomReminders;
  final List<TimeOfDay> customReminderTimes;
  final bool enableQuietHours;
  final TimeOfDay? quietHoursStart;
  final TimeOfDay? quietHoursEnd;
  final String reminderSound;
  final bool enableReminderVibration;

  // Memorization Settings
  final bool enableMemorizationMode;
  final bool hideTranslationInMemorization;
  final bool enableMemorizationTests;
  final bool enableMemorizationProgress;
  final List<String> memorizationSurahs;
  final Map<String, int> memorizationScores;
  final bool enableMemorizationReminders;
  final int memorizationSessionDuration;

  // Study Settings
  final bool enableStudyMode;
  final bool showWordMeanings;
  final bool showGrammarAnalysis;
  final bool showRootWords;
  final bool enableCrossReferences;
  final bool showTafsir;
  final List<String> selectedTafsirSources;
  final bool enableStudyNotes;
  final bool enableHighlighting;
  final Map<String, Color> highlightColors;

  // Backup & Sync Settings
  final bool enableAutoBackup;
  final bool backupToCloud;
  final bool syncAcrossDevices;
  final int backupFrequencyDays;
  final String lastBackupDate;
  final bool syncBookmarks;
  final bool syncProgress;
  final bool syncSettings;
  final bool syncNotes;
  final bool syncHighlights;

  // Privacy & Security Settings
  final bool enablePrivacyMode;
  final bool hideFromRecents;
  final bool requirePinForAccess;
  final String pinHash;
  final bool enableBiometricAuth;
  final int autoLockMinutes;
  final bool enableScreenshotProtection;
  final bool enableIncognitoMode;

  // Advanced Settings
  final bool enableDebugMode;
  final bool enableBetaFeatures;
  final bool enableAnalytics;
  final bool enableCrashReporting;
  final bool enablePerformanceMonitoring;
  final bool enableExperimentalFeatures;
  final String preferredMushafType;
  final bool enableRightToLeftLayout;
  final bool enableAdvancedTypography;

  const QuranSettings({
    // Display Settings
    this.readingMode = QuranReadingMode.page,
    this.arabicFont = ArabicFont.uthmanic,
    this.arabicFontSize = 20.0,
    this.translationFontSize = 16.0,
    this.translationFontFamily = 'System',
    this.showTranslation = true,
    this.showTransliteration = false,
    this.selectedTranslations = const [TranslationLanguage.english],
    this.enableDarkMode = false,
    this.enableNightMode = false,
    this.primaryColor = Colors.green,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black,

    // Tajweed Settings
    this.tajweedStyle = TajweedStyle.basic,
    this.enableTajweedHighlighting = true,
    this.showTajweedRules = false,
    this.tajweedColors = const {},
    this.enableTajweedAudio = false,
    this.showTajweedLegend = false,

    // Reading Settings
    this.enableAutoScroll = false,
    this.autoScrollSpeed = 1.0,
    this.highlightCurrentVerse = true,
    this.showVerseNumbers = true,
    this.showSurahHeaders = true,
    this.showJuzMarkers = true,
    this.showHizbMarkers = false,
    this.showSajdahMarkers = true,
    this.lineSpacing = 1.5,
    this.wordSpacing = 1.0,
    this.enableFullscreen = false,
    this.keepScreenOn = false,
    this.enableGestures = true,
    this.enableZoom = true,

    // Audio Settings
    this.selectedReciter = 'Abdul Rahman Al-Sudais',
    this.downloadedReciters = const [],
    this.recitationSpeed = RecitationSpeed.normal,
    this.enableAudioHighlighting = true,
    this.enableRepeatMode = false,
    this.repeatCount = 3,
    this.enableAutoPlay = false,
    this.enableContinuousPlay = false,
    this.audioVolume = 0.8,
    this.enableAudioBookmarks = true,
    this.audioQuality = DownloadQuality.medium,

    // Search Settings
    this.enableAdvancedSearch = true,
    this.searchInTranslation = true,
    this.searchInTransliteration = false,
    this.enableRootWordSearch = false,
    this.enableTopicSearch = true,
    this.caseSensitiveSearch = false,
    this.wholeWordSearch = false,
    this.searchHistory = const [],
    this.maxSearchHistory = 50,

    // Bookmark Settings
    this.enableBookmarks = true,
    this.bookmarkCategories = const [BookmarkCategory.favorites],
    this.enableBookmarkNotes = true,
    this.enableBookmarkTags = true,
    this.enableBookmarkSharing = false,
    this.autoBookmarkLastRead = true,
    this.defaultBookmarkCategory = 'favorites',

    // Progress Tracking
    this.trackReadingProgress = true,
    this.showProgressStats = true,
    this.enableReadingGoals = false,
    this.dailyReadingGoal = 5,
    this.enableStreaks = true,
    this.enableAchievements = true,
    this.showCompletionPercentage = true,
    this.surahProgress = const {},
    this.lastReadDate,
    this.lastReadPosition = '',

    // Offline & Download Settings
    this.enableOfflineMode = true,
    this.autoDownloadTranslations = false,
    this.autoDownloadAudio = false,
    this.downloadedTranslations = const [],
    this.downloadedAudioSurahs = const [],
    this.downloadQuality = DownloadQuality.medium,
    this.downloadOnWifiOnly = true,
    this.maxCacheSize = 500,
    this.enableSmartDownload = true,

    // Notification Settings
    this.enableReminders = false,
    this.enableDailyReminder = false,
    this.dailyReminderTime,
    this.enableWeeklyReminder = false,
    this.weeklyReminderDay = 5,
    this.weeklyReminderTime,
    this.enableCustomReminders = false,
    this.customReminderTimes = const [],
    this.enableQuietHours = false,
    this.quietHoursStart,
    this.quietHoursEnd,
    this.reminderSound = 'default',
    this.enableReminderVibration = true,

    // Memorization Settings
    this.enableMemorizationMode = false,
    this.hideTranslationInMemorization = true,
    this.enableMemorizationTests = false,
    this.enableMemorizationProgress = true,
    this.memorizationSurahs = const [],
    this.memorizationScores = const {},
    this.enableMemorizationReminders = false,
    this.memorizationSessionDuration = 30,

    // Study Settings
    this.enableStudyMode = false,
    this.showWordMeanings = false,
    this.showGrammarAnalysis = false,
    this.showRootWords = false,
    this.enableCrossReferences = false,
    this.showTafsir = false,
    this.selectedTafsirSources = const [],
    this.enableStudyNotes = true,
    this.enableHighlighting = true,
    this.highlightColors = const {},

    // Backup & Sync Settings
    this.enableAutoBackup = true,
    this.backupToCloud = false,
    this.syncAcrossDevices = false,
    this.backupFrequencyDays = 7,
    this.lastBackupDate = '',
    this.syncBookmarks = true,
    this.syncProgress = true,
    this.syncSettings = true,
    this.syncNotes = true,
    this.syncHighlights = true,

    // Privacy & Security Settings
    this.enablePrivacyMode = false,
    this.hideFromRecents = false,
    this.requirePinForAccess = false,
    this.pinHash = '',
    this.enableBiometricAuth = false,
    this.autoLockMinutes = 5,
    this.enableScreenshotProtection = false,
    this.enableIncognitoMode = false,

    // Advanced Settings
    this.enableDebugMode = false,
    this.enableBetaFeatures = false,
    this.enableAnalytics = true,
    this.enableCrashReporting = true,
    this.enablePerformanceMonitoring = false,
    this.enableExperimentalFeatures = false,
    this.preferredMushafType = 'Madinah',
    this.enableRightToLeftLayout = true,
    this.enableAdvancedTypography = true,
  });

  /// Create a copy with modified values
  QuranSettings copyWith({
    QuranReadingMode? readingMode,
    ArabicFont? arabicFont,
    double? arabicFontSize,
    double? translationFontSize,
    String? translationFontFamily,
    bool? showTranslation,
    bool? showTransliteration,
    List<TranslationLanguage>? selectedTranslations,
    bool? enableDarkMode,
    bool? enableNightMode,
    Color? primaryColor,
    Color? backgroundColor,
    Color? textColor,
    TajweedStyle? tajweedStyle,
    bool? enableTajweedHighlighting,
    bool? showTajweedRules,
    Map<String, Color>? tajweedColors,
    bool? enableTajweedAudio,
    bool? showTajweedLegend,
    bool? enableAutoScroll,
    double? autoScrollSpeed,
    bool? highlightCurrentVerse,
    bool? showVerseNumbers,
    bool? showSurahHeaders,
    bool? showJuzMarkers,
    bool? showHizbMarkers,
    bool? showSajdahMarkers,
    double? lineSpacing,
    double? wordSpacing,
    bool? enableFullscreen,
    bool? keepScreenOn,
    bool? enableGestures,
    bool? enableZoom,
    String? selectedReciter,
    List<String>? downloadedReciters,
    RecitationSpeed? recitationSpeed,
    bool? enableAudioHighlighting,
    bool? enableRepeatMode,
    int? repeatCount,
    bool? enableAutoPlay,
    bool? enableContinuousPlay,
    double? audioVolume,
    bool? enableAudioBookmarks,
    DownloadQuality? audioQuality,
    bool? enableAdvancedSearch,
    bool? searchInTranslation,
    bool? searchInTransliteration,
    bool? enableRootWordSearch,
    bool? enableTopicSearch,
    bool? caseSensitiveSearch,
    bool? wholeWordSearch,
    List<String>? searchHistory,
    int? maxSearchHistory,
    bool? enableBookmarks,
    List<BookmarkCategory>? bookmarkCategories,
    bool? enableBookmarkNotes,
    bool? enableBookmarkTags,
    bool? enableBookmarkSharing,
    bool? autoBookmarkLastRead,
    String? defaultBookmarkCategory,
    bool? trackReadingProgress,
    bool? showProgressStats,
    bool? enableReadingGoals,
    int? dailyReadingGoal,
    bool? enableStreaks,
    bool? enableAchievements,
    bool? showCompletionPercentage,
    Map<int, double>? surahProgress,
    DateTime? lastReadDate,
    String? lastReadPosition,
    bool? enableOfflineMode,
    bool? autoDownloadTranslations,
    bool? autoDownloadAudio,
    List<String>? downloadedTranslations,
    List<String>? downloadedAudioSurahs,
    DownloadQuality? downloadQuality,
    bool? downloadOnWifiOnly,
    int? maxCacheSize,
    bool? enableSmartDownload,
    bool? enableReminders,
    bool? enableDailyReminder,
    TimeOfDay? dailyReminderTime,
    bool? enableWeeklyReminder,
    int? weeklyReminderDay,
    TimeOfDay? weeklyReminderTime,
    bool? enableCustomReminders,
    List<TimeOfDay>? customReminderTimes,
    bool? enableQuietHours,
    TimeOfDay? quietHoursStart,
    TimeOfDay? quietHoursEnd,
    String? reminderSound,
    bool? enableReminderVibration,
    bool? enableMemorizationMode,
    bool? hideTranslationInMemorization,
    bool? enableMemorizationTests,
    bool? enableMemorizationProgress,
    List<String>? memorizationSurahs,
    Map<String, int>? memorizationScores,
    bool? enableMemorizationReminders,
    int? memorizationSessionDuration,
    bool? enableStudyMode,
    bool? showWordMeanings,
    bool? showGrammarAnalysis,
    bool? showRootWords,
    bool? enableCrossReferences,
    bool? showTafsir,
    List<String>? selectedTafsirSources,
    bool? enableStudyNotes,
    bool? enableHighlighting,
    Map<String, Color>? highlightColors,
    bool? enableAutoBackup,
    bool? backupToCloud,
    bool? syncAcrossDevices,
    int? backupFrequencyDays,
    String? lastBackupDate,
    bool? syncBookmarks,
    bool? syncProgress,
    bool? syncSettings,
    bool? syncNotes,
    bool? syncHighlights,
    bool? enablePrivacyMode,
    bool? hideFromRecents,
    bool? requirePinForAccess,
    String? pinHash,
    bool? enableBiometricAuth,
    int? autoLockMinutes,
    bool? enableScreenshotProtection,
    bool? enableIncognitoMode,
    bool? enableDebugMode,
    bool? enableBetaFeatures,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    bool? enablePerformanceMonitoring,
    bool? enableExperimentalFeatures,
    String? preferredMushafType,
    bool? enableRightToLeftLayout,
    bool? enableAdvancedTypography,
  }) {
    return QuranSettings(
      readingMode: readingMode ?? this.readingMode,
      arabicFont: arabicFont ?? this.arabicFont,
      arabicFontSize: arabicFontSize ?? this.arabicFontSize,
      translationFontSize: translationFontSize ?? this.translationFontSize,
      translationFontFamily: translationFontFamily ?? this.translationFontFamily,
      showTranslation: showTranslation ?? this.showTranslation,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      selectedTranslations: selectedTranslations ?? this.selectedTranslations,
      enableDarkMode: enableDarkMode ?? this.enableDarkMode,
      enableNightMode: enableNightMode ?? this.enableNightMode,
      primaryColor: primaryColor ?? this.primaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      tajweedStyle: tajweedStyle ?? this.tajweedStyle,
      enableTajweedHighlighting: enableTajweedHighlighting ?? this.enableTajweedHighlighting,
      showTajweedRules: showTajweedRules ?? this.showTajweedRules,
      tajweedColors: tajweedColors ?? this.tajweedColors,
      enableTajweedAudio: enableTajweedAudio ?? this.enableTajweedAudio,
      showTajweedLegend: showTajweedLegend ?? this.showTajweedLegend,
      enableAutoScroll: enableAutoScroll ?? this.enableAutoScroll,
      autoScrollSpeed: autoScrollSpeed ?? this.autoScrollSpeed,
      highlightCurrentVerse: highlightCurrentVerse ?? this.highlightCurrentVerse,
      showVerseNumbers: showVerseNumbers ?? this.showVerseNumbers,
      showSurahHeaders: showSurahHeaders ?? this.showSurahHeaders,
      showJuzMarkers: showJuzMarkers ?? this.showJuzMarkers,
      showHizbMarkers: showHizbMarkers ?? this.showHizbMarkers,
      showSajdahMarkers: showSajdahMarkers ?? this.showSajdahMarkers,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      enableFullscreen: enableFullscreen ?? this.enableFullscreen,
      keepScreenOn: keepScreenOn ?? this.keepScreenOn,
      enableGestures: enableGestures ?? this.enableGestures,
      enableZoom: enableZoom ?? this.enableZoom,
      selectedReciter: selectedReciter ?? this.selectedReciter,
      downloadedReciters: downloadedReciters ?? this.downloadedReciters,
      recitationSpeed: recitationSpeed ?? this.recitationSpeed,
      enableAudioHighlighting: enableAudioHighlighting ?? this.enableAudioHighlighting,
      enableRepeatMode: enableRepeatMode ?? this.enableRepeatMode,
      repeatCount: repeatCount ?? this.repeatCount,
      enableAutoPlay: enableAutoPlay ?? this.enableAutoPlay,
      enableContinuousPlay: enableContinuousPlay ?? this.enableContinuousPlay,
      audioVolume: audioVolume ?? this.audioVolume,
      enableAudioBookmarks: enableAudioBookmarks ?? this.enableAudioBookmarks,
      audioQuality: audioQuality ?? this.audioQuality,
      enableAdvancedSearch: enableAdvancedSearch ?? this.enableAdvancedSearch,
      searchInTranslation: searchInTranslation ?? this.searchInTranslation,
      searchInTransliteration: searchInTransliteration ?? this.searchInTransliteration,
      enableRootWordSearch: enableRootWordSearch ?? this.enableRootWordSearch,
      enableTopicSearch: enableTopicSearch ?? this.enableTopicSearch,
      caseSensitiveSearch: caseSensitiveSearch ?? this.caseSensitiveSearch,
      wholeWordSearch: wholeWordSearch ?? this.wholeWordSearch,
      searchHistory: searchHistory ?? this.searchHistory,
      maxSearchHistory: maxSearchHistory ?? this.maxSearchHistory,
      enableBookmarks: enableBookmarks ?? this.enableBookmarks,
      bookmarkCategories: bookmarkCategories ?? this.bookmarkCategories,
      enableBookmarkNotes: enableBookmarkNotes ?? this.enableBookmarkNotes,
      enableBookmarkTags: enableBookmarkTags ?? this.enableBookmarkTags,
      enableBookmarkSharing: enableBookmarkSharing ?? this.enableBookmarkSharing,
      autoBookmarkLastRead: autoBookmarkLastRead ?? this.autoBookmarkLastRead,
      defaultBookmarkCategory: defaultBookmarkCategory ?? this.defaultBookmarkCategory,
      trackReadingProgress: trackReadingProgress ?? this.trackReadingProgress,
      showProgressStats: showProgressStats ?? this.showProgressStats,
      enableReadingGoals: enableReadingGoals ?? this.enableReadingGoals,
      dailyReadingGoal: dailyReadingGoal ?? this.dailyReadingGoal,
      enableStreaks: enableStreaks ?? this.enableStreaks,
      enableAchievements: enableAchievements ?? this.enableAchievements,
      showCompletionPercentage: showCompletionPercentage ?? this.showCompletionPercentage,
      surahProgress: surahProgress ?? this.surahProgress,
      lastReadDate: lastReadDate ?? this.lastReadDate,
      lastReadPosition: lastReadPosition ?? this.lastReadPosition,
      enableOfflineMode: enableOfflineMode ?? this.enableOfflineMode,
      autoDownloadTranslations: autoDownloadTranslations ?? this.autoDownloadTranslations,
      autoDownloadAudio: autoDownloadAudio ?? this.autoDownloadAudio,
      downloadedTranslations: downloadedTranslations ?? this.downloadedTranslations,
      downloadedAudioSurahs: downloadedAudioSurahs ?? this.downloadedAudioSurahs,
      downloadQuality: downloadQuality ?? this.downloadQuality,
      downloadOnWifiOnly: downloadOnWifiOnly ?? this.downloadOnWifiOnly,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      enableSmartDownload: enableSmartDownload ?? this.enableSmartDownload,
      enableReminders: enableReminders ?? this.enableReminders,
      enableDailyReminder: enableDailyReminder ?? this.enableDailyReminder,
      dailyReminderTime: dailyReminderTime ?? this.dailyReminderTime,
      enableWeeklyReminder: enableWeeklyReminder ?? this.enableWeeklyReminder,
      weeklyReminderDay: weeklyReminderDay ?? this.weeklyReminderDay,
      weeklyReminderTime: weeklyReminderTime ?? this.weeklyReminderTime,
      enableCustomReminders: enableCustomReminders ?? this.enableCustomReminders,
      customReminderTimes: customReminderTimes ?? this.customReminderTimes,
      enableQuietHours: enableQuietHours ?? this.enableQuietHours,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      reminderSound: reminderSound ?? this.reminderSound,
      enableReminderVibration: enableReminderVibration ?? this.enableReminderVibration,
      enableMemorizationMode: enableMemorizationMode ?? this.enableMemorizationMode,
      hideTranslationInMemorization: hideTranslationInMemorization ?? this.hideTranslationInMemorization,
      enableMemorizationTests: enableMemorizationTests ?? this.enableMemorizationTests,
      enableMemorizationProgress: enableMemorizationProgress ?? this.enableMemorizationProgress,
      memorizationSurahs: memorizationSurahs ?? this.memorizationSurahs,
      memorizationScores: memorizationScores ?? this.memorizationScores,
      enableMemorizationReminders: enableMemorizationReminders ?? this.enableMemorizationReminders,
      memorizationSessionDuration: memorizationSessionDuration ?? this.memorizationSessionDuration,
      enableStudyMode: enableStudyMode ?? this.enableStudyMode,
      showWordMeanings: showWordMeanings ?? this.showWordMeanings,
      showGrammarAnalysis: showGrammarAnalysis ?? this.showGrammarAnalysis,
      showRootWords: showRootWords ?? this.showRootWords,
      enableCrossReferences: enableCrossReferences ?? this.enableCrossReferences,
      showTafsir: showTafsir ?? this.showTafsir,
      selectedTafsirSources: selectedTafsirSources ?? this.selectedTafsirSources,
      enableStudyNotes: enableStudyNotes ?? this.enableStudyNotes,
      enableHighlighting: enableHighlighting ?? this.enableHighlighting,
      highlightColors: highlightColors ?? this.highlightColors,
      enableAutoBackup: enableAutoBackup ?? this.enableAutoBackup,
      backupToCloud: backupToCloud ?? this.backupToCloud,
      syncAcrossDevices: syncAcrossDevices ?? this.syncAcrossDevices,
      backupFrequencyDays: backupFrequencyDays ?? this.backupFrequencyDays,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
      syncBookmarks: syncBookmarks ?? this.syncBookmarks,
      syncProgress: syncProgress ?? this.syncProgress,
      syncSettings: syncSettings ?? this.syncSettings,
      syncNotes: syncNotes ?? this.syncNotes,
      syncHighlights: syncHighlights ?? this.syncHighlights,
      enablePrivacyMode: enablePrivacyMode ?? this.enablePrivacyMode,
      hideFromRecents: hideFromRecents ?? this.hideFromRecents,
      requirePinForAccess: requirePinForAccess ?? this.requirePinForAccess,
      pinHash: pinHash ?? this.pinHash,
      enableBiometricAuth: enableBiometricAuth ?? this.enableBiometricAuth,
      autoLockMinutes: autoLockMinutes ?? this.autoLockMinutes,
      enableScreenshotProtection: enableScreenshotProtection ?? this.enableScreenshotProtection,
      enableIncognitoMode: enableIncognitoMode ?? this.enableIncognitoMode,
      enableDebugMode: enableDebugMode ?? this.enableDebugMode,
      enableBetaFeatures: enableBetaFeatures ?? this.enableBetaFeatures,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      enablePerformanceMonitoring: enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
      enableExperimentalFeatures: enableExperimentalFeatures ?? this.enableExperimentalFeatures,
      preferredMushafType: preferredMushafType ?? this.preferredMushafType,
      enableRightToLeftLayout: enableRightToLeftLayout ?? this.enableRightToLeftLayout,
      enableAdvancedTypography: enableAdvancedTypography ?? this.enableAdvancedTypography,
    );
  }

  /// Get font size for Arabic text
  double getArabicFontSizeValue() => arabicFontSize;

  /// Get font size for translation text
  double getTranslationFontSizeValue() => translationFontSize;

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'readingMode': readingMode.index,
      'arabicFont': arabicFont.index,
      'arabicFontSize': arabicFontSize,
      'translationFontSize': translationFontSize,
      'translationFontFamily': translationFontFamily,
      'showTranslation': showTranslation,
      'showTransliteration': showTransliteration,
      'selectedTranslations': selectedTranslations.map((t) => t.index).toList(),
      'enableDarkMode': enableDarkMode,
      'enableNightMode': enableNightMode,
      'primaryColor': primaryColor.value,
      'backgroundColor': backgroundColor.value,
      'textColor': textColor.value,
      'tajweedStyle': tajweedStyle.index,
      'enableTajweedHighlighting': enableTajweedHighlighting,
      'showTajweedRules': showTajweedRules,
      'tajweedColors': tajweedColors.map((k, v) => MapEntry(k, v.value)),
      'enableTajweedAudio': enableTajweedAudio,
      'showTajweedLegend': showTajweedLegend,
      'enableAutoScroll': enableAutoScroll,
      'autoScrollSpeed': autoScrollSpeed,
      'highlightCurrentVerse': highlightCurrentVerse,
      'showVerseNumbers': showVerseNumbers,
      'showSurahHeaders': showSurahHeaders,
      'showJuzMarkers': showJuzMarkers,
      'showHizbMarkers': showHizbMarkers,
      'showSajdahMarkers': showSajdahMarkers,
      'lineSpacing': lineSpacing,
      'wordSpacing': wordSpacing,
      'enableFullscreen': enableFullscreen,
      'keepScreenOn': keepScreenOn,
      'enableGestures': enableGestures,
      'enableZoom': enableZoom,
      'selectedReciter': selectedReciter,
      'downloadedReciters': downloadedReciters,
      'recitationSpeed': recitationSpeed.index,
      'enableAudioHighlighting': enableAudioHighlighting,
      'enableRepeatMode': enableRepeatMode,
      'repeatCount': repeatCount,
      'enableAutoPlay': enableAutoPlay,
      'enableContinuousPlay': enableContinuousPlay,
      'audioVolume': audioVolume,
      'enableAudioBookmarks': enableAudioBookmarks,
      'audioQuality': audioQuality.index,
      'enableAdvancedSearch': enableAdvancedSearch,
      'searchInTranslation': searchInTranslation,
      'searchInTransliteration': searchInTransliteration,
      'enableRootWordSearch': enableRootWordSearch,
      'enableTopicSearch': enableTopicSearch,
      'caseSensitiveSearch': caseSensitiveSearch,
      'wholeWordSearch': wholeWordSearch,
      'searchHistory': searchHistory,
      'maxSearchHistory': maxSearchHistory,
      'enableBookmarks': enableBookmarks,
      'bookmarkCategories': bookmarkCategories.map((c) => c.index).toList(),
      'enableBookmarkNotes': enableBookmarkNotes,
      'enableBookmarkTags': enableBookmarkTags,
      'enableBookmarkSharing': enableBookmarkSharing,
      'autoBookmarkLastRead': autoBookmarkLastRead,
      'defaultBookmarkCategory': defaultBookmarkCategory,
      'trackReadingProgress': trackReadingProgress,
      'showProgressStats': showProgressStats,
      'enableReadingGoals': enableReadingGoals,
      'dailyReadingGoal': dailyReadingGoal,
      'enableStreaks': enableStreaks,
      'enableAchievements': enableAchievements,
      'showCompletionPercentage': showCompletionPercentage,
      'surahProgress': surahProgress,
      'lastReadDate': lastReadDate?.toIso8601String(),
      'lastReadPosition': lastReadPosition,
      'enableOfflineMode': enableOfflineMode,
      'autoDownloadTranslations': autoDownloadTranslations,
      'autoDownloadAudio': autoDownloadAudio,
      'downloadedTranslations': downloadedTranslations,
      'downloadedAudioSurahs': downloadedAudioSurahs,
      'downloadQuality': downloadQuality.index,
      'downloadOnWifiOnly': downloadOnWifiOnly,
      'maxCacheSize': maxCacheSize,
      'enableSmartDownload': enableSmartDownload,
      'enableReminders': enableReminders,
      'enableDailyReminder': enableDailyReminder,
      'dailyReminderTime': dailyReminderTime != null
          ? '${dailyReminderTime!.hour}:${dailyReminderTime!.minute}'
          : null,
      'enableWeeklyReminder': enableWeeklyReminder,
      'weeklyReminderDay': weeklyReminderDay,
      'weeklyReminderTime': weeklyReminderTime != null
          ? '${weeklyReminderTime!.hour}:${weeklyReminderTime!.minute}'
          : null,
      'enableCustomReminders': enableCustomReminders,
      'customReminderTimes': customReminderTimes.map((t) => '${t.hour}:${t.minute}').toList(),
      'enableQuietHours': enableQuietHours,
      'quietHoursStart': quietHoursStart != null
          ? '${quietHoursStart!.hour}:${quietHoursStart!.minute}'
          : null,
      'quietHoursEnd': quietHoursEnd != null
          ? '${quietHoursEnd!.hour}:${quietHoursEnd!.minute}'
          : null,
      'reminderSound': reminderSound,
      'enableReminderVibration': enableReminderVibration,
      'enableMemorizationMode': enableMemorizationMode,
      'hideTranslationInMemorization': hideTranslationInMemorization,
      'enableMemorizationTests': enableMemorizationTests,
      'enableMemorizationProgress': enableMemorizationProgress,
      'memorizationSurahs': memorizationSurahs,
      'memorizationScores': memorizationScores,
      'enableMemorizationReminders': enableMemorizationReminders,
      'memorizationSessionDuration': memorizationSessionDuration,
      'enableStudyMode': enableStudyMode,
      'showWordMeanings': showWordMeanings,
      'showGrammarAnalysis': showGrammarAnalysis,
      'showRootWords': showRootWords,
      'enableCrossReferences': enableCrossReferences,
      'showTafsir': showTafsir,
      'selectedTafsirSources': selectedTafsirSources,
      'enableStudyNotes': enableStudyNotes,
      'enableHighlighting': enableHighlighting,
      'highlightColors': highlightColors.map((k, v) => MapEntry(k, v.value)),
      'enableAutoBackup': enableAutoBackup,
      'backupToCloud': backupToCloud,
      'syncAcrossDevices': syncAcrossDevices,
      'backupFrequencyDays': backupFrequencyDays,
      'lastBackupDate': lastBackupDate,
      'syncBookmarks': syncBookmarks,
      'syncProgress': syncProgress,
      'syncSettings': syncSettings,
      'syncNotes': syncNotes,
      'syncHighlights': syncHighlights,
      'enablePrivacyMode': enablePrivacyMode,
      'hideFromRecents': hideFromRecents,
      'requirePinForAccess': requirePinForAccess,
      'pinHash': pinHash,
      'enableBiometricAuth': enableBiometricAuth,
      'autoLockMinutes': autoLockMinutes,
      'enableScreenshotProtection': enableScreenshotProtection,
      'enableIncognitoMode': enableIncognitoMode,
      'enableDebugMode': enableDebugMode,
      'enableBetaFeatures': enableBetaFeatures,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'enablePerformanceMonitoring': enablePerformanceMonitoring,
      'enableExperimentalFeatures': enableExperimentalFeatures,
      'preferredMushafType': preferredMushafType,
      'enableRightToLeftLayout': enableRightToLeftLayout,
      'enableAdvancedTypography': enableAdvancedTypography,
    };
  }

  /// Create from JSON
  factory QuranSettings.fromJson(Map<String, dynamic> json) {
    TimeOfDay? parseTimeOfDay(String? timeString) {
      if (timeString == null) return null;
      final parts = timeString.split(':');
      if (parts.length != 2) return null;
      final hour = int.tryParse(parts[0]);
      final minute = int.tryParse(parts[1]);
      if (hour == null || minute == null) return null;
      return TimeOfDay(hour: hour, minute: minute);
    }

    return QuranSettings(
      readingMode: QuranReadingMode.values[json['readingMode'] ?? 0],
      arabicFont: ArabicFont.values[json['arabicFont'] ?? 0],
      arabicFontSize: (json['arabicFontSize'] as num?)?.toDouble() ?? 20.0,
      translationFontSize: (json['translationFontSize'] as num?)?.toDouble() ?? 16.0,
      translationFontFamily: json['translationFontFamily'] ?? 'System',
      showTranslation: json['showTranslation'] ?? true,
      showTransliteration: json['showTransliteration'] ?? false,
      selectedTranslations: (json['selectedTranslations'] as List<dynamic>?)
          ?.map((i) => TranslationLanguage.values[i])
          .toList() ?? [TranslationLanguage.english],
      enableDarkMode: json['enableDarkMode'] ?? false,
      enableNightMode: json['enableNightMode'] ?? false,
      primaryColor: Color(json['primaryColor'] ?? Colors.green.value),
      backgroundColor: Color(json['backgroundColor'] ?? Colors.white.value),
      textColor: Color(json['textColor'] ?? Colors.black.value),
      tajweedStyle: TajweedStyle.values[json['tajweedStyle'] ?? 1],
      enableTajweedHighlighting: json['enableTajweedHighlighting'] ?? true,
      showTajweedRules: json['showTajweedRules'] ?? false,
      tajweedColors: (json['tajweedColors'] as Map<String, dynamic>?)
          ?.map((k, v) => MapEntry(k, Color(v))) ?? {},
      enableTajweedAudio: json['enableTajweedAudio'] ?? false,
      showTajweedLegend: json['showTajweedLegend'] ?? false,
      enableAutoScroll: json['enableAutoScroll'] ?? false,
      autoScrollSpeed: (json['autoScrollSpeed'] as num?)?.toDouble() ?? 1.0,
      highlightCurrentVerse: json['highlightCurrentVerse'] ?? true,
      showVerseNumbers: json['showVerseNumbers'] ?? true,
      showSurahHeaders: json['showSurahHeaders'] ?? true,
      showJuzMarkers: json['showJuzMarkers'] ?? true,
      showHizbMarkers: json['showHizbMarkers'] ?? false,
      showSajdahMarkers: json['showSajdahMarkers'] ?? true,
      lineSpacing: (json['lineSpacing'] as num?)?.toDouble() ?? 1.5,
      wordSpacing: (json['wordSpacing'] as num?)?.toDouble() ?? 1.0,
      enableFullscreen: json['enableFullscreen'] ?? false,
      keepScreenOn: json['keepScreenOn'] ?? false,
      enableGestures: json['enableGestures'] ?? true,
      enableZoom: json['enableZoom'] ?? true,
      selectedReciter: json['selectedReciter'] ?? 'Abdul Rahman Al-Sudais',
      downloadedReciters: List<String>.from(json['downloadedReciters'] ?? []),
      recitationSpeed: RecitationSpeed.values[json['recitationSpeed'] ?? 1],
      enableAudioHighlighting: json['enableAudioHighlighting'] ?? true,
      enableRepeatMode: json['enableRepeatMode'] ?? false,
      repeatCount: json['repeatCount'] ?? 3,
      enableAutoPlay: json['enableAutoPlay'] ?? false,
      enableContinuousPlay: json['enableContinuousPlay'] ?? false,
      audioVolume: (json['audioVolume'] as num?)?.toDouble() ?? 0.8,
      enableAudioBookmarks: json['enableAudioBookmarks'] ?? true,
      audioQuality: DownloadQuality.values[json['audioQuality'] ?? 1],
      enableAdvancedSearch: json['enableAdvancedSearch'] ?? true,
      searchInTranslation: json['searchInTranslation'] ?? true,
      searchInTransliteration: json['searchInTransliteration'] ?? false,
      enableRootWordSearch: json['enableRootWordSearch'] ?? false,
      enableTopicSearch: json['enableTopicSearch'] ?? true,
      caseSensitiveSearch: json['caseSensitiveSearch'] ?? false,
      wholeWordSearch: json['wholeWordSearch'] ?? false,
      searchHistory: List<String>.from(json['searchHistory'] ?? []),
      maxSearchHistory: json['maxSearchHistory'] ?? 50,
      enableBookmarks: json['enableBookmarks'] ?? true,
      bookmarkCategories: (json['bookmarkCategories'] as List<dynamic>?)
          ?.map((i) => BookmarkCategory.values[i])
          .toList() ?? [BookmarkCategory.favorites],
      enableBookmarkNotes: json['enableBookmarkNotes'] ?? true,
      enableBookmarkTags: json['enableBookmarkTags'] ?? true,
      enableBookmarkSharing: json['enableBookmarkSharing'] ?? false,
      autoBookmarkLastRead: json['autoBookmarkLastRead'] ?? true,
      defaultBookmarkCategory: json['defaultBookmarkCategory'] ?? 'favorites',
      trackReadingProgress: json['trackReadingProgress'] ?? true,
      showProgressStats: json['showProgressStats'] ?? true,
      enableReadingGoals: json['enableReadingGoals'] ?? false,
      dailyReadingGoal: json['dailyReadingGoal'] ?? 5,
      enableStreaks: json['enableStreaks'] ?? true,
      enableAchievements: json['enableAchievements'] ?? true,
      showCompletionPercentage: json['showCompletionPercentage'] ?? true,
      surahProgress: Map<int, double>.from(json['surahProgress'] ?? {}),
      lastReadDate: json['lastReadDate'] != null
          ? DateTime.parse(json['lastReadDate'])
          : null,
      lastReadPosition: json['lastReadPosition'] ?? '',
      enableOfflineMode: json['enableOfflineMode'] ?? true,
      autoDownloadTranslations: json['autoDownloadTranslations'] ?? false,
      autoDownloadAudio: json['autoDownloadAudio'] ?? false,
      downloadedTranslations: List<String>.from(json['downloadedTranslations'] ?? []),
      downloadedAudioSurahs: List<String>.from(json['downloadedAudioSurahs'] ?? []),
      downloadQuality: DownloadQuality.values[json['downloadQuality'] ?? 1],
      downloadOnWifiOnly: json['downloadOnWifiOnly'] ?? true,
      maxCacheSize: json['maxCacheSize'] ?? 500,
      enableSmartDownload: json['enableSmartDownload'] ?? true,
      enableReminders: json['enableReminders'] ?? false,
      enableDailyReminder: json['enableDailyReminder'] ?? false,
      dailyReminderTime: parseTimeOfDay(json['dailyReminderTime']),
      enableWeeklyReminder: json['enableWeeklyReminder'] ?? false,
      weeklyReminderDay: json['weeklyReminderDay'] ?? 5,
      weeklyReminderTime: parseTimeOfDay(json['weeklyReminderTime']),
      enableCustomReminders: json['enableCustomReminders'] ?? false,
      customReminderTimes: (json['customReminderTimes'] as List<dynamic>?)
          ?.map((t) => parseTimeOfDay(t))
          .where((t) => t != null)
          .cast<TimeOfDay>()
          .toList() ?? [],
      enableQuietHours: json['enableQuietHours'] ?? false,
      quietHoursStart: parseTimeOfDay(json['quietHoursStart']),
      quietHoursEnd: parseTimeOfDay(json['quietHoursEnd']),
      reminderSound: json['reminderSound'] ?? 'default',
      enableReminderVibration: json['enableReminderVibration'] ?? true,
      enableMemorizationMode: json['enableMemorizationMode'] ?? false,
      hideTranslationInMemorization: json['hideTranslationInMemorization'] ?? true,
      enableMemorizationTests: json['enableMemorizationTests'] ?? false,
      enableMemorizationProgress: json['enableMemorizationProgress'] ?? true,
      memorizationSurahs: List<String>.from(json['memorizationSurahs'] ?? []),
      memorizationScores: Map<String, int>.from(json['memorizationScores'] ?? {}),
      enableMemorizationReminders: json['enableMemorizationReminders'] ?? false,
      memorizationSessionDuration: json['memorizationSessionDuration'] ?? 30,
      enableStudyMode: json['enableStudyMode'] ?? false,
      showWordMeanings: json['showWordMeanings'] ?? false,
      showGrammarAnalysis: json['showGrammarAnalysis'] ?? false,
      showRootWords: json['showRootWords'] ?? false,
      enableCrossReferences: json['enableCrossReferences'] ?? false,
      showTafsir: json['showTafsir'] ?? false,
      selectedTafsirSources: List<String>.from(json['selectedTafsirSources'] ?? []),
      enableStudyNotes: json['enableStudyNotes'] ?? true,
      enableHighlighting: json['enableHighlighting'] ?? true,
      highlightColors: (json['highlightColors'] as Map<String, dynamic>?)
          ?.map((k, v) => MapEntry(k, Color(v))) ?? {},
      enableAutoBackup: json['enableAutoBackup'] ?? true,
      backupToCloud: json['backupToCloud'] ?? false,
      syncAcrossDevices: json['syncAcrossDevices'] ?? false,
      backupFrequencyDays: json['backupFrequencyDays'] ?? 7,
      lastBackupDate: json['lastBackupDate'] ?? '',
      syncBookmarks: json['syncBookmarks'] ?? true,
      syncProgress: json['syncProgress'] ?? true,
      syncSettings: json['syncSettings'] ?? true,
      syncNotes: json['syncNotes'] ?? true,
      syncHighlights: json['syncHighlights'] ?? true,
      enablePrivacyMode: json['enablePrivacyMode'] ?? false,
      hideFromRecents: json['hideFromRecents'] ?? false,
      requirePinForAccess: json['requirePinForAccess'] ?? false,
      pinHash: json['pinHash'] ?? '',
      enableBiometricAuth: json['enableBiometricAuth'] ?? false,
      autoLockMinutes: json['autoLockMinutes'] ?? 5,
      enableScreenshotProtection: json['enableScreenshotProtection'] ?? false,
      enableIncognitoMode: json['enableIncognitoMode'] ?? false,
      enableDebugMode: json['enableDebugMode'] ?? false,
      enableBetaFeatures: json['enableBetaFeatures'] ?? false,
      enableAnalytics: json['enableAnalytics'] ?? true,
      enableCrashReporting: json['enableCrashReporting'] ?? true,
      enablePerformanceMonitoring: json['enablePerformanceMonitoring'] ?? false,
      enableExperimentalFeatures: json['enableExperimentalFeatures'] ?? false,
      preferredMushafType: json['preferredMushafType'] ?? 'Madinah',
      enableRightToLeftLayout: json['enableRightToLeftLayout'] ?? true,
      enableAdvancedTypography: json['enableAdvancedTypography'] ?? true,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranSettings &&
        other.readingMode == readingMode &&
        other.arabicFont == arabicFont &&
        other.arabicFontSize == arabicFontSize &&
        other.showTranslation == showTranslation &&
        other.enableDarkMode == enableDarkMode;
    // Note: Truncated for brevity - in real implementation, compare all fields
  }

  @override
  int get hashCode => Object.hashAll([
        readingMode,
        arabicFont,
        arabicFontSize,
        showTranslation,
        enableDarkMode,
        // Note: Truncated for brevity - in real implementation, hash all fields
      ]);

  @override
  String toString() => 'QuranSettings(readingMode: $readingMode, arabicFont: $arabicFont)';
}