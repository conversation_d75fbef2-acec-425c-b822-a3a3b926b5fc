import 'dart:math' as math;
import '../models/excel_function.dart';

/// Excel engine service for formula calculation and autocomplete
class ExcelEngineService {
  static final ExcelEngineService _instance = ExcelEngineService._internal();
  factory ExcelEngineService() => _instance;
  ExcelEngineService._internal();

  final Map<String, dynamic> _cells = {};
  final Map<String, String> _formulas = {};
  final List<String> _calculationOrder = [];

  /// Get cell value
  dynamic getCellValue(String cellRef) {
    return _cells[cellRef.toUpperCase()];
  }

  /// Set cell value
  void setCellValue(String cellRef, dynamic value) {
    final upperRef = cellRef.toUpperCase();
    _cells[upperRef] = value;
    
    // If it's a formula, store it separately
    if (value is String && value.startsWith('=')) {
      _formulas[upperRef] = value;
      _recalculateFormulas();
    } else {
      _formulas.remove(upperRef);
    }
  }

  /// Get cell formula
  String? getCellFormula(String cellRef) {
    return _formulas[cellRef.toUpperCase()];
  }

  /// Calculate formula
  dynamic calculateFormula(String formula) {
    try {
      if (!formula.startsWith('=')) {
        return formula;
      }

      final expression = formula.substring(1); // Remove '=' prefix
      return _evaluateExpression(expression);
    } catch (e) {
      return '#ERROR!';
    }
  }

  /// Get autocomplete suggestions
  List<AutocompleteSuggestion> getAutocompleteSuggestions(String input) {
    final suggestions = <AutocompleteSuggestion>[];
    
    if (input.isEmpty) return suggestions;

    final upperInput = input.toUpperCase();

    // Function suggestions
    for (final function in ExcelFunctionLibrary.allFunctions) {
      if (function.name.startsWith(upperInput)) {
        suggestions.add(AutocompleteSuggestion(
          text: function.name,
          displayText: function.fullSyntax,
          description: function.description,
          type: SuggestionType.function,
          insertText: '${function.name}(',
        ));
      }
    }

    // Cell reference suggestions
    final cellPattern = RegExp(r'^[A-Z]+\d*$');
    if (cellPattern.hasMatch(upperInput)) {
      // Generate nearby cell suggestions
      final match = RegExp(r'^([A-Z]+)(\d*)$').firstMatch(upperInput);
      if (match != null) {
        final column = match.group(1)!;
        final rowStr = match.group(2)!;
        
        if (rowStr.isNotEmpty) {
          final row = int.tryParse(rowStr);
          if (row != null) {
            // Suggest nearby rows
            for (int i = math.max(1, row - 2); i <= row + 2; i++) {
              final cellRef = '$column$i';
              if (cellRef.startsWith(upperInput)) {
                suggestions.add(AutocompleteSuggestion(
                  text: cellRef,
                  displayText: cellRef,
                  description: 'Cell reference',
                  type: SuggestionType.cellReference,
                  insertText: cellRef,
                ));
              }
            }
          }
        } else {
          // Suggest rows for the column
          for (int i = 1; i <= 10; i++) {
            final cellRef = '$column$i';
            suggestions.add(AutocompleteSuggestion(
              text: cellRef,
              displayText: cellRef,
              description: 'Cell reference',
              type: SuggestionType.cellReference,
              insertText: cellRef,
            ));
          }
        }
      }
    }

    // Range suggestions
    if (input.contains(':')) {
      suggestions.add(AutocompleteSuggestion(
        text: input,
        displayText: input,
        description: 'Cell range',
        type: SuggestionType.range,
        insertText: input,
      ));
    }

    return suggestions..sort((a, b) => a.text.compareTo(b.text));
  }

  /// Validate formula syntax
  FormulaValidationResult validateFormula(String formula) {
    try {
      if (!formula.startsWith('=')) {
        return FormulaValidationResult(
          isValid: false,
          error: 'Formula must start with =',
        );
      }

      final expression = formula.substring(1);
      
      // Check for balanced parentheses
      int openParens = 0;
      for (int i = 0; i < expression.length; i++) {
        if (expression[i] == '(') {
          openParens++;
        } else if (expression[i] == ')') {
          openParens--;
          if (openParens < 0) {
            return FormulaValidationResult(
              isValid: false,
              error: 'Unmatched closing parenthesis at position ${i + 1}',
            );
          }
        }
      }

      if (openParens > 0) {
        return FormulaValidationResult(
          isValid: false,
          error: 'Missing closing parenthesis',
        );
      }

      // Try to evaluate the expression
      _evaluateExpression(expression);

      return FormulaValidationResult(isValid: true);
    } catch (e) {
      return FormulaValidationResult(
        isValid: false,
        error: e.toString(),
      );
    }
  }

  /// Get function help
  ExcelFunction? getFunctionHelp(String functionName) {
    return ExcelFunctionLibrary.getFunction(functionName);
  }

  /// Private methods
  dynamic _evaluateExpression(String expression) {
    // Remove whitespace
    expression = expression.replaceAll(' ', '');

    // Handle function calls
    final functionPattern = RegExp(r'([A-Z]+)\(([^)]*)\)');
    final functionMatches = functionPattern.allMatches(expression);

    for (final match in functionMatches) {
      final functionName = match.group(1)!;
      final argsString = match.group(2)!;
      final result = _evaluateFunction(functionName, argsString);
      expression = expression.replaceFirst(match.group(0)!, result.toString());
    }

    // Handle cell references
    final cellPattern = RegExp(r'[A-Z]+\d+');
    final cellMatches = cellPattern.allMatches(expression);

    for (final match in cellMatches) {
      final cellRef = match.group(0)!;
      final value = getCellValue(cellRef) ?? 0;
      expression = expression.replaceFirst(cellRef, value.toString());
    }

    // Evaluate mathematical expression
    return _evaluateMathExpression(expression);
  }

  dynamic _evaluateFunction(String functionName, String argsString) {
    final args = _parseArguments(argsString);

    switch (functionName.toUpperCase()) {
      case 'SUM':
        return _sum(args);
      case 'AVERAGE':
        return _average(args);
      case 'MAX':
        return _max(args);
      case 'MIN':
        return _min(args);
      case 'COUNT':
        return _count(args);
      case 'IF':
        return _if(args);
      case 'AND':
        return _and(args);
      case 'OR':
        return _or(args);
      case 'CONCATENATE':
        return _concatenate(args);
      case 'LEFT':
        return _left(args);
      case 'RIGHT':
        return _right(args);
      case 'LEN':
        return _len(args);
      case 'TODAY':
        return _today();
      case 'NOW':
        return _now();
      case 'YEAR':
        return _year(args);
      default:
        throw Exception('Unknown function: $functionName');
    }
  }

  List<dynamic> _parseArguments(String argsString) {
    if (argsString.isEmpty) return [];

    final args = <dynamic>[];
    final parts = argsString.split(',');

    for (final part in parts) {
      final trimmed = part.trim();
      
      // Handle ranges
      if (trimmed.contains(':')) {
        args.addAll(_expandRange(trimmed));
      }
      // Handle cell references
      else if (RegExp(r'^[A-Z]+\d+$').hasMatch(trimmed)) {
        args.add(getCellValue(trimmed) ?? 0);
      }
      // Handle numbers
      else if (double.tryParse(trimmed) != null) {
        args.add(double.parse(trimmed));
      }
      // Handle strings
      else if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
        args.add(trimmed.substring(1, trimmed.length - 1));
      }
      // Handle boolean
      else if (trimmed.toUpperCase() == 'TRUE') {
        args.add(true);
      } else if (trimmed.toUpperCase() == 'FALSE') {
        args.add(false);
      }
      // Default to string
      else {
        args.add(trimmed);
      }
    }

    return args;
  }

  List<dynamic> _expandRange(String range) {
    final parts = range.split(':');
    if (parts.length != 2) return [];

    final start = parts[0].trim();
    final end = parts[1].trim();

    // Simple implementation for A1:A10 style ranges
    final startMatch = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(start);
    final endMatch = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(end);

    if (startMatch == null || endMatch == null) return [];

    final startCol = startMatch.group(1)!;
    final startRow = int.parse(startMatch.group(2)!);
    final endCol = endMatch.group(1)!;
    final endRow = int.parse(endMatch.group(2)!);

    final values = <dynamic>[];

    if (startCol == endCol) {
      // Same column, different rows
      for (int row = startRow; row <= endRow; row++) {
        final cellRef = '$startCol$row';
        values.add(getCellValue(cellRef) ?? 0);
      }
    }

    return values;
  }

  double _evaluateMathExpression(String expression) {
    // Simple math expression evaluator
    // This is a simplified implementation
    try {
      // Handle basic operations
      if (expression.contains('+')) {
        final parts = expression.split('+');
        return parts.map((p) => double.tryParse(p.trim()) ?? 0).reduce((a, b) => a + b);
      } else if (expression.contains('-')) {
        final parts = expression.split('-');
        if (parts.length == 2) {
          return (double.tryParse(parts[0].trim()) ?? 0) - (double.tryParse(parts[1].trim()) ?? 0);
        }
      } else if (expression.contains('*')) {
        final parts = expression.split('*');
        return parts.map((p) => double.tryParse(p.trim()) ?? 0).reduce((a, b) => a * b);
      } else if (expression.contains('/')) {
        final parts = expression.split('/');
        if (parts.length == 2) {
          final divisor = double.tryParse(parts[1].trim()) ?? 1;
          if (divisor == 0) throw Exception('Division by zero');
          return (double.tryParse(parts[0].trim()) ?? 0) / divisor;
        }
      }

      return double.tryParse(expression) ?? 0;
    } catch (e) {
      throw Exception('Invalid mathematical expression');
    }
  }

  void _recalculateFormulas() {
    // Recalculate all formulas in dependency order
    for (final entry in _formulas.entries) {
      final cellRef = entry.key;
      final formula = entry.value;
      try {
        final result = calculateFormula(formula);
        _cells[cellRef] = result;
      } catch (e) {
        _cells[cellRef] = '#ERROR!';
      }
    }
  }

  // Excel function implementations
  double _sum(List<dynamic> args) {
    return args.where((arg) => arg is num).map((arg) => arg.toDouble()).fold(0.0, (a, b) => a + b);
  }

  double _average(List<dynamic> args) {
    final numbers = args.where((arg) => arg is num).map((arg) => arg.toDouble()).toList();
    if (numbers.isEmpty) return 0;
    return numbers.fold(0.0, (a, b) => a + b) / numbers.length;
  }

  double _max(List<dynamic> args) {
    final numbers = args.where((arg) => arg is num).map((arg) => arg.toDouble()).toList();
    if (numbers.isEmpty) return 0;
    return numbers.reduce(math.max);
  }

  double _min(List<dynamic> args) {
    final numbers = args.where((arg) => arg is num).map((arg) => arg.toDouble()).toList();
    if (numbers.isEmpty) return 0;
    return numbers.reduce(math.min);
  }

  int _count(List<dynamic> args) {
    return args.where((arg) => arg is num).length;
  }

  dynamic _if(List<dynamic> args) {
    if (args.length < 2) throw Exception('IF function requires at least 2 arguments');
    final condition = args[0];
    final valueIfTrue = args[1];
    final valueIfFalse = args.length > 2 ? args[2] : false;

    bool isTrue = false;
    if (condition is bool) {
      isTrue = condition;
    } else if (condition is num) {
      isTrue = condition != 0;
    } else if (condition is String) {
      isTrue = condition.isNotEmpty;
    }

    return isTrue ? valueIfTrue : valueIfFalse;
  }

  bool _and(List<dynamic> args) {
    return args.every((arg) {
      if (arg is bool) return arg;
      if (arg is num) return arg != 0;
      if (arg is String) return arg.isNotEmpty;
      return false;
    });
  }

  bool _or(List<dynamic> args) {
    return args.any((arg) {
      if (arg is bool) return arg;
      if (arg is num) return arg != 0;
      if (arg is String) return arg.isNotEmpty;
      return false;
    });
  }

  String _concatenate(List<dynamic> args) {
    return args.map((arg) => arg.toString()).join('');
  }

  String _left(List<dynamic> args) {
    if (args.isEmpty) throw Exception('LEFT function requires at least 1 argument');
    final text = args[0].toString();
    final numChars = args.length > 1 ? (args[1] as num).toInt() : 1;
    return text.substring(0, math.min(numChars, text.length));
  }

  String _right(List<dynamic> args) {
    if (args.isEmpty) throw Exception('RIGHT function requires at least 1 argument');
    final text = args[0].toString();
    final numChars = args.length > 1 ? (args[1] as num).toInt() : 1;
    final startIndex = math.max(0, text.length - numChars);
    return text.substring(startIndex);
  }

  int _len(List<dynamic> args) {
    if (args.isEmpty) throw Exception('LEN function requires 1 argument');
    return args[0].toString().length;
  }

  double _today() {
    final now = DateTime.now();
    final epoch = DateTime(1900, 1, 1);
    return now.difference(epoch).inDays.toDouble() + 2; // Excel epoch adjustment
  }

  double _now() {
    final now = DateTime.now();
    final epoch = DateTime(1900, 1, 1);
    final days = now.difference(epoch).inDays.toDouble() + 2;
    final timeOfDay = (now.hour * 3600 + now.minute * 60 + now.second) / 86400;
    return days + timeOfDay;
  }

  int _year(List<dynamic> args) {
    if (args.isEmpty) throw Exception('YEAR function requires 1 argument');
    final serialNumber = (args[0] as num).toDouble();
    final epoch = DateTime(1900, 1, 1);
    final date = epoch.add(Duration(days: (serialNumber - 2).toInt()));
    return date.year;
  }
}

/// Autocomplete suggestion model
class AutocompleteSuggestion {
  final String text;
  final String displayText;
  final String description;
  final SuggestionType type;
  final String insertText;

  const AutocompleteSuggestion({
    required this.text,
    required this.displayText,
    required this.description,
    required this.type,
    required this.insertText,
  });
}

enum SuggestionType {
  function,
  cellReference,
  range,
  constant,
}

/// Formula validation result
class FormulaValidationResult {
  final bool isValid;
  final String? error;

  const FormulaValidationResult({
    required this.isValid,
    this.error,
  });
}
