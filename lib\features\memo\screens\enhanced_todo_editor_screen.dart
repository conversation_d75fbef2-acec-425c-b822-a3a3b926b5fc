import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/todo.dart';
import '../providers/todo_provider.dart';

class EnhancedTodoEditorScreen extends ConsumerStatefulWidget {
  final Todo? todo;
  final int? parentTodoId;

  const EnhancedTodoEditorScreen({
    super.key,
    this.todo,
    this.parentTodoId,
  });

  @override
  ConsumerState<EnhancedTodoEditorScreen> createState() => _EnhancedTodoEditorScreenState();
}

class _EnhancedTodoEditorScreenState extends ConsumerState<EnhancedTodoEditorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _locationController;
  late TextEditingController _urlController;
  late TextEditingController _estimatedMinutesController;
  
  // Form state
  DateTime? _dueDate;
  DateTime? _startDate;
  DateTime? _reminderTime;
  TodoPriority _priority = TodoPriority.medium;
  String _category = 'general';
  List<String> _tags = [];
  List<int> _dependsOnIds = [];
  double _progress = 0.0;
  
  bool get _isEditing => widget.todo != null;
  bool get _isSubtask => widget.parentTodoId != null;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeControllers();
    _loadTodoData();
  }

  void _initializeControllers() {
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();
    _locationController = TextEditingController();
    _urlController = TextEditingController();
    _estimatedMinutesController = TextEditingController();
  }

  void _loadTodoData() {
    if (widget.todo != null) {
      final todo = widget.todo!;
      _titleController.text = todo.title;
      _descriptionController.text = todo.description;
      _locationController.text = todo.location ?? '';
      _urlController.text = todo.url ?? '';
      _estimatedMinutesController.text = todo.estimatedMinutes.toString();
      _dueDate = todo.dueDate;
      _startDate = todo.startDate;
      _reminderTime = todo.reminderTime;
      _priority = todo.priority;
      _category = todo.category;
      _tags = List.from(todo.tags);
      _dependsOnIds = List.from(todo.dependsOnIds);
      _progress = todo.progress;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _urlController.dispose();
    _estimatedMinutesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Todo' : (_isSubtask ? 'New Subtask' : 'New Todo')),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveTodo,
            child: const Text('Save', style: TextStyle(color: Colors.white)),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Basic', icon: Icon(Icons.edit, size: 16)),
            Tab(text: 'Schedule', icon: Icon(Icons.schedule, size: 16)),
            Tab(text: 'Details', icon: Icon(Icons.info, size: 16)),
            Tab(text: 'Progress', icon: Icon(Icons.trending_up, size: 16)),
          ],
        ),
      ),
      body: Form(
        key: _formKey,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildBasicTab(),
            _buildScheduleTab(),
            _buildDetailsTab(),
            _buildProgressTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Title *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.title),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Title is required';
              }
              return null;
            },
            autofocus: !_isEditing,
          ),
          const SizedBox(height: 16),
          
          // Description
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.description),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          
          // Priority
          DropdownButtonFormField<TodoPriority>(
            value: _priority,
            decoration: const InputDecoration(
              labelText: 'Priority',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.flag),
            ),
            items: TodoPriority.values.map((priority) {
              return DropdownMenuItem(
                value: priority,
                child: Row(
                  children: [
                    Icon(
                      Icons.circle,
                      size: 12,
                      color: _getPriorityColor(priority),
                    ),
                    const SizedBox(width: 8),
                    Text(_getPriorityName(priority)),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) => setState(() => _priority = value!),
          ),
          const SizedBox(height: 16),
          
          // Category
          DropdownButtonFormField<String>(
            value: _category,
            decoration: const InputDecoration(
              labelText: 'Category',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.category),
            ),
            items: ref.watch(todoCategoriesProvider).map((category) {
              return DropdownMenuItem(
                value: category,
                child: Text(category),
              );
            }).toList(),
            onChanged: (value) => setState(() => _category = value!),
          ),
          const SizedBox(height: 16),
          
          // Tags
          _buildTagsSection(),
        ],
      ),
    );
  }

  Widget _buildScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Start Date
          ListTile(
            leading: const Icon(Icons.play_arrow),
            title: const Text('Start Date'),
            subtitle: Text(_startDate?.toString().split(' ')[0] ?? 'Not set'),
            trailing: const Icon(Icons.calendar_today),
            onTap: () => _selectStartDate(),
          ),
          const Divider(),
          
          // Due Date
          ListTile(
            leading: const Icon(Icons.event),
            title: const Text('Due Date'),
            subtitle: Text(_dueDate?.toString().split(' ')[0] ?? 'Not set'),
            trailing: const Icon(Icons.calendar_today),
            onTap: () => _selectDueDate(),
          ),
          const Divider(),
          
          // Reminder
          ListTile(
            leading: const Icon(Icons.alarm),
            title: const Text('Reminder'),
            subtitle: Text(_reminderTime?.toString() ?? 'Not set'),
            trailing: const Icon(Icons.access_time),
            onTap: () => _selectReminderTime(),
          ),
          const Divider(),
          
          // Estimated Time
          TextFormField(
            controller: _estimatedMinutesController,
            decoration: const InputDecoration(
              labelText: 'Estimated Time (minutes)',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.timer),
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Location
          TextFormField(
            controller: _locationController,
            decoration: const InputDecoration(
              labelText: 'Location',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.location_on),
            ),
          ),
          const SizedBox(height: 16),
          
          // URL
          TextFormField(
            controller: _urlController,
            decoration: const InputDecoration(
              labelText: 'URL/Link',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.link),
            ),
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 16),
          
          // Dependencies
          _buildDependenciesSection(),
          const SizedBox(height: 16),
          
          // Subtasks (if editing)
          if (_isEditing) _buildSubtasksSection(),
        ],
      ),
    );
  }

  Widget _buildProgressTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress Slider
          Text('Progress: ${(_progress * 100).round()}%'),
          Slider(
            value: _progress,
            onChanged: (value) => setState(() => _progress = value),
            divisions: 10,
            label: '${(_progress * 100).round()}%',
          ),
          const SizedBox(height: 16),
          
          // Progress visualization
          LinearProgressIndicator(
            value: _progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              _progress < 0.3 ? Colors.red :
              _progress < 0.7 ? Colors.orange : Colors.green,
            ),
          ),
          const SizedBox(height: 24),
          
          // Time tracking (if editing)
          if (_isEditing && widget.todo != null) ...[
            _buildTimeTrackingSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Tags', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            ..._tags.map((tag) => Chip(
              label: Text(tag),
              onDeleted: () => setState(() => _tags.remove(tag)),
            )),
            ActionChip(
              label: const Text('+ Add Tag'),
              onPressed: _addTag,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDependenciesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Dependencies', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        // TODO: Implement dependency selection
        const Text('Dependency management coming soon!'),
      ],
    );
  }

  Widget _buildSubtasksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Subtasks', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        // TODO: Implement subtask management
        const Text('Subtask management coming soon!'),
      ],
    );
  }

  Widget _buildTimeTrackingSection() {
    final todo = widget.todo!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Time Tracking', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Text('Estimated'),
                      Text('${todo.estimatedMinutes} min', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Text('Actual'),
                      Text('${todo.actualMinutes} min', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper methods
  Color _getPriorityColor(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.low:
        return Colors.green;
      case TodoPriority.medium:
        return Colors.orange;
      case TodoPriority.high:
        return Colors.red;
      case TodoPriority.urgent:
        return Colors.purple;
    }
  }

  String _getPriorityName(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.low:
        return 'Low';
      case TodoPriority.medium:
        return 'Medium';
      case TodoPriority.high:
        return 'High';
      case TodoPriority.urgent:
        return 'Urgent';
    }
  }

  void _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() => _startDate = date);
    }
  }

  void _selectDueDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() => _dueDate = date);
    }
  }

  void _selectReminderTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _reminderTime ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_reminderTime ?? DateTime.now()),
      );
      if (time != null) {
        setState(() {
          _reminderTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        });
      }
    }
  }

  void _addTag() async {
    final tag = await showDialog<String>(
      context: context,
      builder: (context) => _TagDialog(),
    );
    if (tag != null && tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() => _tags.add(tag));
    }
  }

  void _saveTodo() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      if (_isEditing) {
        // Update existing todo
        widget.todo!.updateTodo(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          dueDate: _dueDate,
          startDate: _startDate,
          priority: _priority,
          category: _category,
          reminderTime: _reminderTime,
          tags: _tags,
          progress: _progress,
          estimatedMinutes: int.tryParse(_estimatedMinutesController.text) ?? 0,
          location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
          url: _urlController.text.trim().isEmpty ? null : _urlController.text.trim(),
        );
        await ref.read(todosProvider.notifier).updateTodo(widget.todo!);
      } else {
        // Create new todo
        await ref.read(todosProvider.notifier).createTodo(
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          dueDate: _dueDate,
          startDate: _startDate,
          priority: _priority,
          category: _category,
          reminderTime: _reminderTime,
          tags: _tags,
          parentTodoId: widget.parentTodoId,
          estimatedMinutes: int.tryParse(_estimatedMinutesController.text) ?? 0,
          location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
          url: _urlController.text.trim().isEmpty ? null : _urlController.text.trim(),
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_isEditing ? 'Todo updated' : 'Todo created')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }
}

class _TagDialog extends StatefulWidget {
  @override
  State<_TagDialog> createState() => _TagDialogState();
}

class _TagDialogState extends State<_TagDialog> {
  final _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Tag'),
      content: TextField(
        controller: _controller,
        decoration: const InputDecoration(
          labelText: 'Tag name',
          border: OutlineInputBorder(),
        ),
        autofocus: true,
        onSubmitted: (value) => Navigator.of(context).pop(value),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(_controller.text),
          child: const Text('Add'),
        ),
      ],
    );
  }
}
