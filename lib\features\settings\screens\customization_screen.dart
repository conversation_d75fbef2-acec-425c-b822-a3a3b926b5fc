import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/customization_service.dart';
import '../models/theme_customization.dart';
import '../models/app_layout_config.dart';
import '../widgets/theme_editor_widget.dart';
import '../widgets/layout_editor_widget.dart';

/// Comprehensive customization screen for themes and layouts
class CustomizationScreen extends ConsumerStatefulWidget {
  const CustomizationScreen({super.key});

  @override
  ConsumerState<CustomizationScreen> createState() => _CustomizationScreenState();
}

class _CustomizationScreenState extends ConsumerState<CustomizationScreen>
    with TickerProviderStateMixin {
  final CustomizationService _customizationService = CustomizationService();
  late TabController _tabController;
  
  bool _isLoading = true;
  List<ThemeCustomization> _themes = [];
  List<AppLayoutConfig> _layouts = [];
  ThemeCustomization? _currentTheme;
  AppLayoutConfig? _currentLayout;
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeCustomization();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeCustomization() async {
    try {
      await _customizationService.initialize();
      setState(() {
        _themes = _customizationService.themes;
        _layouts = _customizationService.layouts;
        _currentTheme = _customizationService.currentTheme;
        _currentLayout = _customizationService.currentLayout;
        _isDarkMode = _customizationService.isDarkMode;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading customization: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading customization options...'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Customization'),
        actions: [
          IconButton(
            icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: _toggleDarkMode,
            tooltip: 'Toggle Dark Mode',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_theme',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('Export Themes'),
                ),
              ),
              const PopupMenuItem(
                value: 'import_theme',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Import Themes'),
                ),
              ),
              const PopupMenuItem(
                value: 'export_layout',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('Export Layouts'),
                ),
              ),
              const PopupMenuItem(
                value: 'import_layout',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Import Layouts'),
                ),
              ),
              const PopupMenuItem(
                value: 'reset',
                child: ListTile(
                  leading: Icon(Icons.restore),
                  title: Text('Reset to Defaults'),
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.palette),
              text: 'Themes',
            ),
            Tab(
              icon: Icon(Icons.view_quilt),
              text: 'Layouts',
            ),
            Tab(
              icon: Icon(Icons.tune),
              text: 'Advanced',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildThemesTab(),
          _buildLayoutsTab(),
          _buildAdvancedTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateDialog,
        icon: const Icon(Icons.add),
        label: const Text('Create Custom'),
      ),
    );
  }

  Widget _buildThemesTab() {
    return Column(
      children: [
        // Current theme preview
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.palette,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Current Theme',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                _currentTheme?.name ?? 'No theme selected',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (_currentTheme != null) ...[
                const SizedBox(height: 4),
                Text(
                  _currentTheme!.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 12),
                _buildColorPreview(_currentTheme!),
              ],
            ],
          ),
        ),

        // Theme list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _themes.length,
            itemBuilder: (context, index) {
              final theme = _themes[index];
              return _buildThemeCard(theme);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLayoutsTab() {
    return Column(
      children: [
        // Current layout preview
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.secondaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.view_quilt,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Current Layout',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                _currentLayout?.name ?? 'No layout selected',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (_currentLayout != null) ...[
                const SizedBox(height: 4),
                Text(
                  _currentLayout!.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 12),
                _buildLayoutPreview(_currentLayout!),
              ],
            ],
          ),
        ),

        // Layout list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _layouts.length,
            itemBuilder: (context, index) {
              final layout = _layouts[index];
              return _buildLayoutCard(layout);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Advanced Settings',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Dark mode toggle
          Card(
            child: SwitchListTile(
              title: const Text('Dark Mode'),
              subtitle: const Text('Use dark theme across the app'),
              value: _isDarkMode,
              onChanged: (value) => _setDarkMode(value),
              secondary: Icon(_isDarkMode ? Icons.dark_mode : Icons.light_mode),
            ),
          ),

          const SizedBox(height: 16),

          // Theme editor
          Card(
            child: ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Theme Editor'),
              subtitle: const Text('Create and edit custom themes'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _openThemeEditor,
            ),
          ),

          const SizedBox(height: 8),

          // Layout editor
          Card(
            child: ListTile(
              leading: const Icon(Icons.design_services),
              title: const Text('Layout Editor'),
              subtitle: const Text('Customize app layout and spacing'),
              trailing: const Icon(Icons.chevron_right),
              onTap: _openLayoutEditor,
            ),
          ),

          const SizedBox(height: 16),

          Text(
            'Import/Export',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          // Export themes
          Card(
            child: ListTile(
              leading: const Icon(Icons.upload),
              title: const Text('Export Themes'),
              subtitle: const Text('Save your themes to a file'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _handleMenuAction('export_theme'),
            ),
          ),

          const SizedBox(height: 8),

          // Import themes
          Card(
            child: ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Import Themes'),
              subtitle: const Text('Load themes from a file'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _handleMenuAction('import_theme'),
            ),
          ),

          const SizedBox(height: 8),

          // Reset to defaults
          Card(
            child: ListTile(
              leading: const Icon(Icons.restore),
              title: const Text('Reset to Defaults'),
              subtitle: const Text('Restore original themes and layouts'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _handleMenuAction('reset'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeCard(ThemeCustomization theme) {
    final isSelected = _currentTheme?.id == theme.id;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: () => _applyTheme(theme.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          theme.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isSelected ? Theme.of(context).colorScheme.primary : null,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          theme.description,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  if (theme.isCustom)
                    PopupMenuButton<String>(
                      onSelected: (action) => _handleThemeAction(action, theme),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('Edit'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: ListTile(
                            leading: Icon(Icons.copy),
                            title: Text('Duplicate'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete),
                            title: Text('Delete'),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              const SizedBox(height: 12),
              _buildColorPreview(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLayoutCard(AppLayoutConfig layout) {
    final isSelected = _currentLayout?.id == layout.id;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: () => _applyLayout(layout.id),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          layout.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isSelected ? Theme.of(context).colorScheme.primary : null,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          layout.description,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  if (!layout.isDefault)
                    PopupMenuButton<String>(
                      onSelected: (action) => _handleLayoutAction(action, layout),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit),
                            title: Text('Edit'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: ListTile(
                            leading: Icon(Icons.copy),
                            title: Text('Duplicate'),
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete),
                            title: Text('Delete'),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              const SizedBox(height: 12),
              _buildLayoutPreview(layout),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildColorPreview(ThemeCustomization theme) {
    final colorScheme = _isDarkMode ? theme.darkColorScheme : theme.lightColorScheme;
    
    return Row(
      children: [
        _buildColorCircle(colorScheme.primary, 'Primary'),
        const SizedBox(width: 8),
        _buildColorCircle(colorScheme.secondary, 'Secondary'),
        const SizedBox(width: 8),
        _buildColorCircle(colorScheme.surface, 'Surface'),
        const SizedBox(width: 8),
        _buildColorCircle(colorScheme.error, 'Error'),
      ],
    );
  }

  Widget _buildColorCircle(Color color, String label) {
    return Tooltip(
      message: label,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
    );
  }

  Widget _buildLayoutPreview(AppLayoutConfig layout) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Sidebar representation
          Container(
            width: layout.sidebarWidth / 8,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                bottomLeft: Radius.circular(8),
              ),
            ),
          ),
          // Content area
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(layout.contentPadding / 4),
              child: Column(
                children: [
                  // Header
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  SizedBox(height: layout.cardSpacing / 2),
                  // Content cards
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(layout.cardBorderRadius.topLeft.x / 3),
                            ),
                          ),
                        ),
                        SizedBox(width: layout.cardSpacing / 2),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(layout.cardBorderRadius.topLeft.x / 3),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleDarkMode() async {
    await _customizationService.toggleDarkMode();
    setState(() {
      _isDarkMode = _customizationService.isDarkMode;
    });
  }

  Future<void> _setDarkMode(bool isDark) async {
    await _customizationService.setDarkMode(isDark);
    setState(() {
      _isDarkMode = _customizationService.isDarkMode;
    });
  }

  Future<void> _applyTheme(String themeId) async {
    await _customizationService.applyTheme(themeId);
    setState(() {
      _currentTheme = _customizationService.currentTheme;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Theme applied successfully!')),
      );
    }
  }

  Future<void> _applyLayout(String layoutId) async {
    await _customizationService.applyLayout(layoutId);
    setState(() {
      _currentLayout = _customizationService.currentLayout;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Layout applied successfully!')),
      );
    }
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Custom'),
        content: const Text('What would you like to create?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _openThemeEditor();
            },
            child: const Text('Custom Theme'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _openLayoutEditor();
            },
            child: const Text('Custom Layout'),
          ),
        ],
      ),
    );
  }

  void _openThemeEditor() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ThemeEditorWidget(
          theme: _currentTheme,
          onSave: (theme) async {
            await _customizationService.createCustomTheme(
              name: theme.name,
              description: theme.description,
              lightColorScheme: theme.lightColorScheme,
              darkColorScheme: theme.darkColorScheme,
            );
            await _initializeCustomization();
          },
        ),
      ),
    );
  }

  void _openLayoutEditor() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LayoutEditorWidget(
          layout: _currentLayout,
          onSave: (layout) async {
            await _customizationService.createCustomLayout(
              name: layout.name,
              description: layout.description,
              baseLayout: layout,
            );
            await _initializeCustomization();
          },
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_theme':
        _exportThemes();
        break;
      case 'import_theme':
        _importThemes();
        break;
      case 'export_layout':
        _exportLayouts();
        break;
      case 'import_layout':
        _importLayouts();
        break;
      case 'reset':
        _resetToDefaults();
        break;
    }
  }

  void _handleThemeAction(String action, ThemeCustomization theme) {
    switch (action) {
      case 'edit':
        _openThemeEditor();
        break;
      case 'duplicate':
        // Implement theme duplication
        break;
      case 'delete':
        _deleteTheme(theme.id);
        break;
    }
  }

  void _handleLayoutAction(String action, AppLayoutConfig layout) {
    switch (action) {
      case 'edit':
        _openLayoutEditor();
        break;
      case 'duplicate':
        // Implement layout duplication
        break;
      case 'delete':
        _deleteLayout(layout.id);
        break;
    }
  }

  Future<void> _deleteTheme(String themeId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Theme'),
        content: const Text('Are you sure you want to delete this custom theme?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _customizationService.deleteCustomTheme(themeId);
      await _initializeCustomization();
    }
  }

  Future<void> _deleteLayout(String layoutId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Layout'),
        content: const Text('Are you sure you want to delete this custom layout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _customizationService.deleteCustomLayout(layoutId);
      await _initializeCustomization();
    }
  }

  void _exportThemes() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Theme export functionality coming soon!')),
    );
  }

  void _importThemes() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Theme import functionality coming soon!')),
    );
  }

  void _exportLayouts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Layout export functionality coming soon!')),
    );
  }

  void _importLayouts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Layout import functionality coming soon!')),
    );
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset to Defaults'),
        content: const Text('This will remove all custom themes and layouts. Are you sure?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _customizationService.resetToDefaults();
      await _initializeCustomization();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Reset to defaults successfully!')),
        );
      }
    }
  }
}
