import 'package:flutter/material.dart';
import '../models/custom_dhikr_routine.dart';

class AuthenticAthkar {
  static List<CustomDhikrRoutine> getPreBuiltRoutines() {
    return [
      _createMorningAthkar(),
      _createEvening<PERSON><PERSON>kar(),
      _createAfter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
      _createTasbihFatimah(),
      _createIstikhara(),
      _createSleepAthkar(),
      _createTravelAthkar(),
    ];
  }

  static CustomDhikrRoutine _createMorningAthkar() {
    return CustomDhikrRoutine.create(
      name: 'Morning Athkar (أذكار الصباح)',
      description: 'Authentic morning remembrances from Quran and Sunnah',
      themeColor: Colors.orange,
      steps: [
        // Ayat al-Kursi
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ ۚ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ ۗ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ ۚ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ ۖ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِمَا شَاءَ ۚ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ ۖ وَلَا يَئُودُهُ حِفْظُهُمَا ۚ وَهُوَ الْعَلِيُّ الْعَظِيمُ',
          transliteration: 'Allahu la ilaha illa huwa al-hayyu al-qayyum...',
          translation: 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence...',
          targetCount: 1,
          color: Colors.orange,
        ),
        
        // Surah Al-Ikhlas
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُنْ لَهُ كُفُوًا أَحَدٌ',
          transliteration: 'Qul huwa Allahu ahad, Allahu as-samad, lam yalid wa lam yulad, wa lam yakun lahu kufuwan ahad',
          translation: 'Say: He is Allah, the One! Allah, the Eternal, Absolute; He begets not, nor is He begotten; And there is none like unto Him.',
          targetCount: 3,
          color: Colors.orange,
        ),
        
        // Surah Al-Falaq
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ * مِنْ شَرِّ مَا خَلَقَ * وَمِنْ شَرِّ غَاسِقٍ إِذَا وَقَبَ * وَمِنْ شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ * وَمِنْ شَرِّ حَاسِدٍ إِذَا حَسَدَ',
          transliteration: 'Qul a\'udhu bi rabbi al-falaq, min sharri ma khalaq...',
          translation: 'Say: I seek refuge with the Lord of the Dawn, From the mischief of created things...',
          targetCount: 3,
          color: Colors.orange,
        ),
        
        // Surah An-Nas
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ * مَلِكِ النَّاسِ * إِلَٰهِ النَّاسِ * مِنْ شَرِّ الْوَسْوَاسِ الْخَنَّاسِ * الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ * مِنَ الْجِنَّةِ وَالنَّاسِ',
          transliteration: 'Qul a\'udhu bi rabbi an-nas, maliki an-nas, ilahi an-nas...',
          translation: 'Say: I seek refuge with the Lord and Cherisher of Mankind, The King of Mankind, The God of Mankind...',
          targetCount: 3,
          color: Colors.orange,
        ),
        
        // Subhan Allah wa bihamdihi
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
          transliteration: 'Subhan Allahi wa bihamdihi',
          translation: 'Glory is to Allah and praise is to Him',
          targetCount: 100,
          color: Colors.orange,
        ),
      ],
      enableReminders: true,
      userId: 'default_user',
    );
  }

  static CustomDhikrRoutine _createEveningAthkar() {
    return CustomDhikrRoutine.create(
      name: 'Evening Athkar (أذكار المساء)',
      description: 'Authentic evening remembrances from Quran and Sunnah',
      themeColor: Colors.indigo,
      steps: [
        // Ayat al-Kursi
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ ۚ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ ۗ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ ۚ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ ۖ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِمَا شَاءَ ۚ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ ۖ وَلَا يَئُودُهُ حِفْظُهُمَا ۚ وَهُوَ الْعَلِيُّ الْعَظِيمُ',
          transliteration: 'Allahu la ilaha illa huwa al-hayyu al-qayyum...',
          translation: 'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence...',
          targetCount: 1,
          color: Colors.indigo,
        ),
        
        // Three Quls
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُنْ لَهُ كُفُوًا أَحَدٌ',
          transliteration: 'Qul huwa Allahu ahad...',
          translation: 'Say: He is Allah, the One!...',
          targetCount: 3,
          color: Colors.indigo,
        ),
        
        // Astaghfirullah
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ وَأَتُوبُ إِلَيْهِ',
          transliteration: 'Astaghfirullaha al-\'azeem alladhi la ilaha illa huwa al-hayyu al-qayyumu wa atubu ilayh',
          translation: 'I seek forgiveness from Allah the Mighty, whom there is none worthy of worship except Him, the Living, the Eternal, and I repent to Him',
          targetCount: 3,
          color: Colors.indigo,
        ),
      ],
      enableReminders: true,
      userId: 'default_user',
    );
  }

  static CustomDhikrRoutine _createAfterPrayerAthkar() {
    return CustomDhikrRoutine.create(
      name: 'After Prayer Athkar (أذكار بعد الصلاة)',
      description: 'Remembrances to recite after each obligatory prayer',
      themeColor: Colors.green,
      steps: [
        // Astaghfirullah
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'أَسْتَغْفِرُ اللَّهَ',
          transliteration: 'Astaghfirullah',
          translation: 'I seek forgiveness from Allah',
          targetCount: 3,
          color: Colors.green,
        ),
        
        // Allahumma anta as-salam
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُمَّ أَنْتَ السَّلَامُ وَمِنْكَ السَّلَامُ تَبَارَكْتَ يَا ذَا الْجَلَالِ وَالْإِكْرَامِ',
          transliteration: 'Allahumma anta as-salamu wa minka as-salamu tabarakta ya dhal-jalali wal-ikram',
          translation: 'O Allah, You are Peace and from You comes peace. Blessed are You, O Owner of majesty and honor',
          targetCount: 1,
          color: Colors.green,
        ),
        
        // Subhan Allah
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'سُبْحَانَ اللَّهِ',
          transliteration: 'Subhan Allah',
          translation: 'Glory be to Allah',
          targetCount: 33,
          color: Colors.green,
        ),
        
        // Alhamdulillah
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'الْحَمْدُ لِلَّهِ',
          transliteration: 'Alhamdulillah',
          translation: 'All praise is due to Allah',
          targetCount: 33,
          color: Colors.green,
        ),
        
        // Allahu Akbar
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُ أَكْبَرُ',
          transliteration: 'Allahu Akbar',
          translation: 'Allah is the Greatest',
          targetCount: 34,
          color: Colors.green,
        ),
      ],
      userId: 'default_user',
    );
  }

  static CustomDhikrRoutine _createTasbihFatimah() {
    return CustomDhikrRoutine.create(
      name: 'Tasbih Fatimah (تسبيح فاطمة)',
      description: 'The dhikr taught by Prophet Muhammad (ﷺ) to his daughter Fatimah',
      themeColor: Colors.purple,
      steps: [
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'سُبْحَانَ اللَّهِ',
          transliteration: 'Subhan Allah',
          translation: 'Glory be to Allah',
          targetCount: 33,
          color: Colors.purple,
        ),
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'الْحَمْدُ لِلَّهِ',
          transliteration: 'Alhamdulillah',
          translation: 'All praise is due to Allah',
          targetCount: 33,
          color: Colors.purple,
        ),
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُ أَكْبَرُ',
          transliteration: 'Allahu Akbar',
          translation: 'Allah is the Greatest',
          targetCount: 34,
          color: Colors.purple,
        ),
      ],
      userId: 'default_user',
    );
  }

  static CustomDhikrRoutine _createIstikhara() {
    return CustomDhikrRoutine.create(
      name: 'Istikhara (الاستخارة)',
      description: 'Prayer for seeking guidance from Allah',
      themeColor: Colors.teal,
      steps: [
        DhikrStep.create(
          type: DhikrStepType.reminder,
          reminderText: 'Pray 2 rakats of voluntary prayer first, then recite the Istikhara dua',
          color: Colors.teal,
        ),
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُمَّ إِنِّي أَسْتَخِيرُكَ بِعِلْمِكَ وَأَسْتَقْدِرُكَ بِقُدْرَتِكَ وَأَسْأَلُكَ مِنْ فَضْلِكَ الْعَظِيمِ فَإِنَّكَ تَقْدِرُ وَلَا أَقْدِرُ وَتَعْلَمُ وَلَا أَعْلَمُ وَأَنْتَ عَلَّامُ الْغُيُوبِ',
          transliteration: 'Allahumma inni astakhiruka bi\'ilmika wa astaqdiruka bi qudratika...',
          translation: 'O Allah, I seek Your guidance through Your knowledge, and I seek ability through Your power...',
          targetCount: 1,
          color: Colors.teal,
        ),
      ],
      userId: 'default_user',
    );
  }

  static CustomDhikrRoutine _createSleepAthkar() {
    return CustomDhikrRoutine.create(
      name: 'Sleep Athkar (أذكار النوم)',
      description: 'Remembrances before going to sleep',
      themeColor: Colors.blueGrey,
      steps: [
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
          transliteration: 'Bismika Allahumma amutu wa ahya',
          translation: 'In Your name, O Allah, I die and I live',
          targetCount: 1,
          color: Colors.blueGrey,
        ),
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'سُبْحَانَ اللَّهِ',
          transliteration: 'Subhan Allah',
          translation: 'Glory be to Allah',
          targetCount: 33,
          color: Colors.blueGrey,
        ),
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'الْحَمْدُ لِلَّهِ',
          transliteration: 'Alhamdulillah',
          translation: 'All praise is due to Allah',
          targetCount: 33,
          color: Colors.blueGrey,
        ),
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُ أَكْبَرُ',
          transliteration: 'Allahu Akbar',
          translation: 'Allah is the Greatest',
          targetCount: 34,
          color: Colors.blueGrey,
        ),
      ],
      enableReminders: true,
      userId: 'default_user',
    );
  }

  static CustomDhikrRoutine _createTravelAthkar() {
    return CustomDhikrRoutine.create(
      name: 'Travel Athkar (أذكار السفر)',
      description: 'Remembrances for traveling',
      themeColor: Colors.brown,
      steps: [
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'سُبْحَانَ الَّذِي سَخَّرَ لَنَا هَٰذَا وَمَا كُنَّا لَهُ مُقْرِنِينَ * وَإِنَّا إِلَىٰ رَبِّنَا لَمُنقَلِبُونَ',
          transliteration: 'Subhan alladhi sakhkhara lana hadha wa ma kunna lahu muqrinin, wa inna ila rabbina lamunqalibun',
          translation: 'Glory be to Him who has subjected this to us, and we could never have it (by our efforts). And to our Lord, surely, must we return!',
          targetCount: 1,
          color: Colors.brown,
        ),
        DhikrStep.create(
          type: DhikrStepType.dhikr,
          arabicText: 'اللَّهُمَّ إِنَّا نَسْأَلُكَ فِي سَفَرِنَا هَٰذَا الْبِرَّ وَالتَّقْوَىٰ وَمِنَ الْعَمَلِ مَا تَرْضَىٰ',
          transliteration: 'Allahumma inna nas\'aluka fi safarina hadha al-birra wat-taqwa wa min al-\'amali ma tarda',
          translation: 'O Allah, we ask You on this our journey for goodness and piety, and for works that are pleasing to You',
          targetCount: 1,
          color: Colors.brown,
        ),
      ],
      userId: 'default_user',
    );
  }
}
