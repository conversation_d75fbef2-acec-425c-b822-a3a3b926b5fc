import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';

/// Components panel for the UI Builder
class ComponentsPanel extends ConsumerStatefulWidget {
  final Function(UIComponent component)? onComponentDragStart;
  final VoidCallback? onComponentSelected;

  const ComponentsPanel({
    super.key,
    this.onComponentDragStart,
    this.onComponentSelected,
  });

  @override
  ConsumerState<ComponentsPanel> createState() => _ComponentsPanelState();
}

class _ComponentsPanelState extends ConsumerState<ComponentsPanel> {
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),
          
          // Search bar
          _buildSearchBar(),
          
          // Components list
          Expanded(child: _buildComponentsList()),
        ],
      ),
    );
  }

  /// Build panel header
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.widgets,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Components',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build search bar
  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search components...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  /// Build components list
  Widget _buildComponentsList() {
    if (_searchQuery.isNotEmpty) {
      // Show filtered results when searching
      final filteredComponents = _getFilteredComponents();
      return ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filteredComponents.length,
        itemBuilder: (context, index) {
          final componentType = filteredComponents[index];
          return _buildComponentItem(componentType);
        },
      );
    }

    // Show categorized components when not searching
    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      children: [
        _buildCategorySection('Input Components', [
          UIComponentType.textField,
          UIComponentType.button,
          UIComponentType.toggle,
          UIComponentType.dropdown,
          UIComponentType.slider,
        ]),

        _buildCategorySection('Output Components', [
          UIComponentType.label,
          UIComponentType.displayText,
          UIComponentType.calculatedValue,
          UIComponentType.progressBar,
          UIComponentType.gauge,
        ]),

        _buildCategorySection('Excel Components', [
          UIComponentType.dataTable,
          UIComponentType.pivotTable,
          UIComponentType.chartWidget,
          UIComponentType.formulaDisplay,
          UIComponentType.cellReference,
        ]),

        _buildCategorySection('Layout Components', [
          UIComponentType.image,
          UIComponentType.divider,
          UIComponentType.container,
          UIComponentType.spacer,
        ]),
      ],
    );
  }

  /// Build category section
  Widget _buildCategorySection(String title, List<UIComponentType> components) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        ...components.map((type) => _buildComponentItem(type)),
        const SizedBox(height: 8),
      ],
    );
  }

  /// Get filtered components based on search
  List<UIComponentType> _getFilteredComponents() {
    if (_searchQuery.isEmpty) {
      return UIComponentType.values;
    }
    
    return UIComponentType.values.where((type) {
      final name = _getComponentDisplayName(type).toLowerCase();
      final description = _getComponentDescription(type).toLowerCase();
      return name.contains(_searchQuery) || description.contains(_searchQuery);
    }).toList();
  }

  /// Build individual component item
  Widget _buildComponentItem(UIComponentType type) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Draggable<UIComponentType>(
        data: type,
        feedback: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: _buildComponentCard(type, isDragging: true),
        ),
        childWhenDragging: _buildComponentCard(type, isGhost: true),
        onDragStarted: () {
          final component = UIComponent.create(type);
          widget.onComponentDragStart?.call(component);
        },
        child: _buildComponentCard(type),
      ),
    );
  }

  /// Build component card
  Widget _buildComponentCard(UIComponentType type, {bool isDragging = false, bool isGhost = false}) {
    return Container(
      width: isDragging ? 240 : null,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isGhost 
            ? Theme.of(context).colorScheme.surface.withValues(alpha: 0.5)
            : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
        boxShadow: isDragging ? [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Row(
        children: [
          // Component icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getComponentIcon(type),
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Component info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getComponentDisplayName(type),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getComponentDescription(type),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get component display name
  String _getComponentDisplayName(UIComponentType type) {
    switch (type) {
      // Input Components
      case UIComponentType.textField:
        return 'Text Field';
      case UIComponentType.button:
        return 'Button';
      case UIComponentType.toggle:
        return 'Toggle Switch';
      case UIComponentType.dropdown:
        return 'Dropdown';
      case UIComponentType.slider:
        return 'Slider';

      // Output Components
      case UIComponentType.label:
        return 'Label';
      case UIComponentType.displayText:
        return 'Display Text';
      case UIComponentType.calculatedValue:
        return 'Calculated Value';
      case UIComponentType.progressBar:
        return 'Progress Bar';
      case UIComponentType.gauge:
        return 'Gauge';

      // Excel-Specific Components
      case UIComponentType.dataTable:
        return 'Data Table';
      case UIComponentType.pivotTable:
        return 'Pivot Table';
      case UIComponentType.chartWidget:
        return 'Chart Widget';
      case UIComponentType.formulaDisplay:
        return 'Formula Display';
      case UIComponentType.cellReference:
        return 'Cell Reference';

      // Layout Components
      case UIComponentType.image:
        return 'Image';
      case UIComponentType.divider:
        return 'Divider';
      case UIComponentType.container:
        return 'Container';
      case UIComponentType.spacer:
        return 'Spacer';
    }
  }

  /// Get component description
  String _getComponentDescription(UIComponentType type) {
    switch (type) {
      // Input Components
      case UIComponentType.textField:
        return 'Input field for text entry with placeholder support';
      case UIComponentType.button:
        return 'Clickable button with customizable actions';
      case UIComponentType.toggle:
        return 'Boolean switch for on/off values';
      case UIComponentType.dropdown:
        return 'Selection dropdown with custom options';
      case UIComponentType.slider:
        return 'Numeric range input with visual slider';

      // Output Components
      case UIComponentType.label:
        return 'Read-only text display element';
      case UIComponentType.displayText:
        return 'Formatted text display with styling options';
      case UIComponentType.calculatedValue:
        return 'Display calculated values from spreadsheet formulas';
      case UIComponentType.progressBar:
        return 'Visual progress indicator with percentage display';
      case UIComponentType.gauge:
        return 'Circular gauge for displaying metrics and KPIs';

      // Excel-Specific Components
      case UIComponentType.dataTable:
        return 'Interactive data table with sorting and filtering';
      case UIComponentType.pivotTable:
        return 'Dynamic pivot table for data analysis';
      case UIComponentType.chartWidget:
        return 'Interactive charts (bar, line, pie, etc.)';
      case UIComponentType.formulaDisplay:
        return 'Display Excel formulas and their results';
      case UIComponentType.cellReference:
        return 'Reference and display specific cell values';

      // Layout Components
      case UIComponentType.image:
        return 'Image display with URL or file upload';
      case UIComponentType.divider:
        return 'Visual separator for layout organization';
      case UIComponentType.container:
        return 'Container for grouping and styling components';
      case UIComponentType.spacer:
        return 'Empty space for layout spacing';
    }
  }

  /// Get component icon
  IconData _getComponentIcon(UIComponentType type) {
    switch (type) {
      // Input Components
      case UIComponentType.textField:
        return Icons.text_fields;
      case UIComponentType.button:
        return Icons.smart_button;
      case UIComponentType.toggle:
        return Icons.toggle_on;
      case UIComponentType.dropdown:
        return Icons.arrow_drop_down_circle;
      case UIComponentType.slider:
        return Icons.tune;

      // Output Components
      case UIComponentType.label:
        return Icons.label;
      case UIComponentType.displayText:
        return Icons.text_format;
      case UIComponentType.calculatedValue:
        return Icons.calculate;
      case UIComponentType.progressBar:
        return Icons.linear_scale;
      case UIComponentType.gauge:
        return Icons.speed;

      // Excel-Specific Components
      case UIComponentType.dataTable:
        return Icons.table_chart;
      case UIComponentType.pivotTable:
        return Icons.pivot_table_chart;
      case UIComponentType.chartWidget:
        return Icons.bar_chart;
      case UIComponentType.formulaDisplay:
        return Icons.functions;
      case UIComponentType.cellReference:
        return Icons.grid_on;

      // Layout Components
      case UIComponentType.image:
        return Icons.image;
      case UIComponentType.divider:
        return Icons.horizontal_rule;
      case UIComponentType.container:
        return Icons.crop_square;
      case UIComponentType.spacer:
        return Icons.space_bar;
    }
  }
}
