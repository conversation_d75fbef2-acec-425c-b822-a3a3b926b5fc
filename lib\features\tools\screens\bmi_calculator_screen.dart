import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math' as math;
import '../models/calculation_history.dart';
import '../providers/calculation_history_provider.dart';

enum UnitSystem { metric, imperial }
enum Gender { male, female }

class BMICalculatorScreen extends ConsumerStatefulWidget {
  const BMICalculatorScreen({super.key});

  @override
  ConsumerState<BMICalculatorScreen> createState() => _BMICalculatorScreenState();
}

class _BMICalculatorScreenState extends ConsumerState<BMICalculatorScreen> {
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();
  final _feetController = TextEditingController();
  final _inchesController = TextEditingController();
  final _ageController = TextEditingController();
  
  UnitSystem _unitSystem = UnitSystem.metric;
  Gender _gender = Gender.male;
  double _bmi = 0;
  String _bmiCategory = '';
  Color _categoryColor = Colors.grey;
  double _idealWeightMin = 0;
  double _idealWeightMax = 0;
  double _bmr = 0;

  @override
  void initState() {
    super.initState();
    _weightController.addListener(_calculateBMI);
    _heightController.addListener(_calculateBMI);
    _feetController.addListener(_calculateBMI);
    _inchesController.addListener(_calculateBMI);
    _ageController.addListener(_calculateBMI);
  }

  @override
  void dispose() {
    _weightController.dispose();
    _heightController.dispose();
    _feetController.dispose();
    _inchesController.dispose();
    _ageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BMI Calculator'),
        actions: [
          IconButton(
            onPressed: _saveCalculation,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Unit System Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Unit System',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    SegmentedButton<UnitSystem>(
                      segments: const [
                        ButtonSegment(
                          value: UnitSystem.metric,
                          label: Text('Metric (kg/cm)'),
                        ),
                        ButtonSegment(
                          value: UnitSystem.imperial,
                          label: Text('Imperial (lbs/ft)'),
                        ),
                      ],
                      selected: {_unitSystem},
                      onSelectionChanged: (Set<UnitSystem> selection) {
                        setState(() {
                          _unitSystem = selection.first;
                          _clearInputs();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Personal Information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Personal Information',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    // Gender Selection
                    Row(
                      children: [
                        const Text('Gender: '),
                        const SizedBox(width: 16),
                        SegmentedButton<Gender>(
                          segments: const [
                            ButtonSegment(
                              value: Gender.male,
                              label: Text('Male'),
                              icon: Icon(Icons.male),
                            ),
                            ButtonSegment(
                              value: Gender.female,
                              label: Text('Female'),
                              icon: Icon(Icons.female),
                            ),
                          ],
                          selected: {_gender},
                          onSelectionChanged: (Set<Gender> selection) {
                            setState(() {
                              _gender = selection.first;
                              _calculateBMI();
                            });
                          },
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Age Input
                    TextFormField(
                      controller: _ageController,
                      decoration: const InputDecoration(
                        labelText: 'Age',
                        hintText: 'Enter your age',
                        suffixText: 'years',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Measurements
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Measurements',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    // Weight Input
                    TextFormField(
                      controller: _weightController,
                      decoration: InputDecoration(
                        labelText: 'Weight',
                        hintText: _unitSystem == UnitSystem.metric 
                            ? 'Enter weight in kg' 
                            : 'Enter weight in lbs',
                        suffixText: _unitSystem == UnitSystem.metric ? 'kg' : 'lbs',
                        border: const OutlineInputBorder(),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Height Input
                    if (_unitSystem == UnitSystem.metric) ...[
                      TextFormField(
                        controller: _heightController,
                        decoration: const InputDecoration(
                          labelText: 'Height',
                          hintText: 'Enter height in cm',
                          suffixText: 'cm',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                        ],
                      ),
                    ] else ...[
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _feetController,
                              decoration: const InputDecoration(
                                labelText: 'Feet',
                                hintText: 'ft',
                                suffixText: 'ft',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _inchesController,
                              decoration: const InputDecoration(
                                labelText: 'Inches',
                                hintText: 'in',
                                suffixText: 'in',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Results
            if (_bmi > 0) ...[
              Card(
                color: _categoryColor.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Text(
                        'Your BMI',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      // BMI Value
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: _categoryColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Text(
                              _bmi.toStringAsFixed(1),
                              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: _categoryColor,
                              ),
                            ),
                            Text(
                              _bmiCategory,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: _categoryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // BMI Scale
                      _buildBMIScale(),
                      
                      const SizedBox(height: 16),
                      
                      // Additional Information
                      if (_idealWeightMin > 0) ...[
                        Text(
                          'Ideal Weight Range',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Text(
                          '${_idealWeightMin.toStringAsFixed(1)} - ${_idealWeightMax.toStringAsFixed(1)} ${_unitSystem == UnitSystem.metric ? 'kg' : 'lbs'}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                      ],
                      
                      if (_bmr > 0) ...[
                        Text(
                          'Basal Metabolic Rate (BMR)',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Text(
                          '${_bmr.toStringAsFixed(0)} calories/day',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Health Tips
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Health Tips',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ..._getHealthTips().map((tip) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text('• $tip'),
                      )),
                    ],
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _clearInputs,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _bmi > 0 ? _shareResults : null,
                    icon: const Icon(Icons.share),
                    label: const Text('Share'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBMIScale() {
    return Column(
      children: [
        const Text('BMI Scale'),
        const SizedBox(height: 8),
        Container(
          height: 20,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            gradient: const LinearGradient(
              colors: [
                Colors.blue,    // Underweight
                Colors.green,   // Normal
                Colors.orange,  // Overweight
                Colors.red,     // Obese
              ],
              stops: [0.0, 0.33, 0.66, 1.0],
            ),
          ),
        ),
        const SizedBox(height: 4),
        const Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('16', style: TextStyle(fontSize: 12)),
            Text('18.5', style: TextStyle(fontSize: 12)),
            Text('25', style: TextStyle(fontSize: 12)),
            Text('30', style: TextStyle(fontSize: 12)),
            Text('40', style: TextStyle(fontSize: 12)),
          ],
        ),
      ],
    );
  }

  void _calculateBMI() {
    double weight = double.tryParse(_weightController.text) ?? 0;
    double height = 0;
    
    if (_unitSystem == UnitSystem.metric) {
      height = double.tryParse(_heightController.text) ?? 0;
      height = height / 100; // Convert cm to meters
    } else {
      final feet = double.tryParse(_feetController.text) ?? 0;
      final inches = double.tryParse(_inchesController.text) ?? 0;
      height = (feet * 12 + inches) * 0.0254; // Convert to meters
      weight = weight * 0.453592; // Convert lbs to kg
    }
    
    if (weight > 0 && height > 0) {
      setState(() {
        _bmi = weight / (height * height);
        _updateBMICategory();
        _calculateIdealWeight(height);
        _calculateBMR(weight, height);
      });
    } else {
      setState(() {
        _bmi = 0;
        _bmiCategory = '';
        _categoryColor = Colors.grey;
      });
    }
  }

  void _updateBMICategory() {
    if (_bmi < 16) {
      _bmiCategory = 'Severely Underweight';
      _categoryColor = Colors.blue[800]!;
    } else if (_bmi < 18.5) {
      _bmiCategory = 'Underweight';
      _categoryColor = Colors.blue;
    } else if (_bmi < 25) {
      _bmiCategory = 'Normal Weight';
      _categoryColor = Colors.green;
    } else if (_bmi < 30) {
      _bmiCategory = 'Overweight';
      _categoryColor = Colors.orange;
    } else if (_bmi < 35) {
      _bmiCategory = 'Moderately Obese';
      _categoryColor = Colors.red;
    } else {
      _bmiCategory = 'Severely Obese';
      _categoryColor = Colors.red[800]!;
    }
  }

  void _calculateIdealWeight(double heightInMeters) {
    // Using BMI range 18.5 - 24.9 for ideal weight
    _idealWeightMin = 18.5 * heightInMeters * heightInMeters;
    _idealWeightMax = 24.9 * heightInMeters * heightInMeters;
    
    if (_unitSystem == UnitSystem.imperial) {
      _idealWeightMin = _idealWeightMin / 0.453592; // Convert to lbs
      _idealWeightMax = _idealWeightMax / 0.453592;
    }
  }

  void _calculateBMR(double weightInKg, double heightInMeters) {
    final age = double.tryParse(_ageController.text) ?? 25;
    final heightInCm = heightInMeters * 100;
    
    // Mifflin-St Jeor Equation
    if (_gender == Gender.male) {
      _bmr = 10 * weightInKg + 6.25 * heightInCm - 5 * age + 5;
    } else {
      _bmr = 10 * weightInKg + 6.25 * heightInCm - 5 * age - 161;
    }
  }

  List<String> _getHealthTips() {
    if (_bmi < 18.5) {
      return [
        'Consider consulting a healthcare provider',
        'Focus on nutrient-dense foods',
        'Include strength training exercises',
        'Eat frequent, smaller meals',
      ];
    } else if (_bmi < 25) {
      return [
        'Maintain your current healthy weight',
        'Continue regular physical activity',
        'Eat a balanced diet',
        'Stay hydrated',
      ];
    } else if (_bmi < 30) {
      return [
        'Aim for gradual weight loss (1-2 lbs/week)',
        'Increase physical activity',
        'Focus on portion control',
        'Choose whole foods over processed',
      ];
    } else {
      return [
        'Consult with a healthcare provider',
        'Consider a structured weight loss program',
        'Start with low-impact exercises',
        'Focus on sustainable lifestyle changes',
      ];
    }
  }

  void _clearInputs() {
    setState(() {
      _weightController.clear();
      _heightController.clear();
      _feetController.clear();
      _inchesController.clear();
      _ageController.clear();
      _bmi = 0;
      _bmiCategory = '';
      _categoryColor = Colors.grey;
      _idealWeightMin = 0;
      _idealWeightMax = 0;
      _bmr = 0;
    });
  }

  void _saveCalculation() {
    if (_bmi <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your measurements first')),
      );
      return;
    }

    final weight = _weightController.text;
    final height = _unitSystem == UnitSystem.metric 
        ? _heightController.text 
        : '${_feetController.text}\'${_inchesController.text}"';
    
    final expression = 'BMI: ${weight}${_unitSystem == UnitSystem.metric ? 'kg' : 'lbs'}, '
        '${height}${_unitSystem == UnitSystem.metric ? 'cm' : ''}, '
        '${_gender.name}, age ${_ageController.text}';
    
    final result = 'BMI: ${_bmi.toStringAsFixed(1)} ($_bmiCategory)';
    
    final calculation = CalculationHistory.create(
      type: CalculationType.bmi,
      expression: expression,
      result: result,
      userId: '',
    );
    
    ref.read(calculationHistoryProvider.notifier).addCalculation(calculation);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('BMI calculation saved to history')),
    );
  }

  void _shareResults() {
    final shareText = '''
BMI Calculator Results:
BMI: ${_bmi.toStringAsFixed(1)}
Category: $_bmiCategory
Ideal Weight: ${_idealWeightMin.toStringAsFixed(1)} - ${_idealWeightMax.toStringAsFixed(1)} ${_unitSystem == UnitSystem.metric ? 'kg' : 'lbs'}
BMR: ${_bmr.toStringAsFixed(0)} calories/day

Calculated with ShadowSuite
''';

    Clipboard.setData(ClipboardData(text: shareText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Results copied to clipboard')),
    );
  }
}
