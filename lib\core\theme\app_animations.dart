import 'package:flutter/material.dart';

class AppAnimations {
  // Animation Durations
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration extraSlow = Duration(milliseconds: 800);

  // Animation Curves
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve easeOut = Curves.easeOut;
  static const Curve easeIn = Curves.easeIn;
  static const Curve bounceOut = Curves.bounceOut;
  static const Curve elasticOut = Curves.elasticOut;
  static const Curve fastOutSlowIn = Curves.fastOutSlowIn;

  // Page Transitions
  static Widget slideTransition({
    required Widget child,
    required Animation<double> animation,
    SlideDirection direction = SlideDirection.right,
  }) {
    Offset begin;
    switch (direction) {
      case SlideDirection.up:
        begin = const Offset(0.0, 1.0);
        break;
      case SlideDirection.down:
        begin = const Offset(0.0, -1.0);
        break;
      case SlideDirection.left:
        begin = const Offset(1.0, 0.0);
        break;
      case SlideDirection.right:
        begin = const Offset(-1.0, 0.0);
        break;
    }

    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: fastOutSlowIn,
      )),
      child: child,
    );
  }

  static Widget fadeTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  static Widget scaleTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: elasticOut,
      )),
      child: child,
    );
  }

  static Widget fadeSlideTransition({
    required Widget child,
    required Animation<double> animation,
    SlideDirection direction = SlideDirection.up,
  }) {
    return FadeTransition(
      opacity: animation,
      child: slideTransition(
        child: child,
        animation: animation,
        direction: direction,
      ),
    );
  }

  // Custom Page Route
  static PageRouteBuilder<T> createRoute<T>({
    required Widget page,
    PageTransitionType type = PageTransitionType.fadeSlide,
    SlideDirection direction = SlideDirection.right,
    Duration duration = medium,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        switch (type) {
          case PageTransitionType.fade:
            return fadeTransition(child: child, animation: animation);
          case PageTransitionType.slide:
            return slideTransition(child: child, animation: animation, direction: direction);
          case PageTransitionType.scale:
            return scaleTransition(child: child, animation: animation);
          case PageTransitionType.fadeSlide:
            return fadeSlideTransition(child: child, animation: animation, direction: direction);
        }
      },
    );
  }

  // Animated Widgets
  static Widget animatedContainer({
    required Widget child,
    Duration duration = medium,
    Curve curve = easeInOut,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? color,
    Decoration? decoration,
    double? width,
    double? height,
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: curve,
      padding: padding,
      margin: margin,
      color: color,
      decoration: decoration,
      width: width,
      height: height,
      child: child,
    );
  }

  static Widget animatedOpacity({
    required Widget child,
    required bool visible,
    Duration duration = medium,
    Curve curve = easeInOut,
  }) {
    return AnimatedOpacity(
      opacity: visible ? 1.0 : 0.0,
      duration: duration,
      curve: curve,
      child: child,
    );
  }

  static Widget animatedScale({
    required Widget child,
    required bool scaled,
    Duration duration = medium,
    Curve curve = elasticOut,
    double scale = 1.1,
  }) {
    return AnimatedScale(
      scale: scaled ? scale : 1.0,
      duration: duration,
      curve: curve,
      child: child,
    );
  }

  static Widget animatedRotation({
    required Widget child,
    required bool rotated,
    Duration duration = medium,
    Curve curve = easeInOut,
    double turns = 0.25,
  }) {
    return AnimatedRotation(
      turns: rotated ? turns : 0.0,
      duration: duration,
      curve: curve,
      child: child,
    );
  }

  // Staggered Animations
  static Widget staggeredList({
    required List<Widget> children,
    Duration delay = const Duration(milliseconds: 100),
    Duration duration = medium,
    Curve curve = easeOut,
  }) {
    return Column(
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return TweenAnimationBuilder<double>(
          duration: duration + (delay * index),
          tween: Tween(begin: 0.0, end: 1.0),
          curve: curve,
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: child,
        );
      }).toList(),
    );
  }

  // Loading Animations
  static Widget pulseAnimation({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1000),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: minScale, end: maxScale),
      curve: Curves.easeInOut,
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
      onEnd: () {
        // Reverse animation would be handled by the parent widget
      },
      child: child,
    );
  }

  static Widget shimmerEffect({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: -1.0, end: 2.0),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor ?? Colors.grey[300]!,
                highlightColor ?? Colors.grey[100]!,
                baseColor ?? Colors.grey[300]!,
              ],
              stops: [
                (value - 0.3).clamp(0.0, 1.0),
                value.clamp(0.0, 1.0),
                (value + 0.3).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: child,
    );
  }

  // Hero Animations
  static Widget heroAnimation({
    required String tag,
    required Widget child,
    Duration duration = medium,
  }) {
    return Hero(
      tag: tag,
      flightShuttleBuilder: (context, animation, direction, fromContext, toContext) {
        return ScaleTransition(
          scale: animation,
          child: child,
        );
      },
      child: child,
    );
  }
}

enum SlideDirection { up, down, left, right }

enum PageTransitionType { fade, slide, scale, fadeSlide }

// Custom Animation Controller Mixin
mixin AnimationControllerMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin<T> {
  late AnimationController animationController;
  late Animation<double> animation;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: AppAnimations.medium,
      vsync: this,
    );
    animation = CurvedAnimation(
      parent: animationController,
      curve: AppAnimations.easeInOut,
    );
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  void startAnimation() => animationController.forward();
  void reverseAnimation() => animationController.reverse();
  void resetAnimation() => animationController.reset();
  void repeatAnimation() => animationController.repeat();
  void stopAnimation() => animationController.stop();
}
