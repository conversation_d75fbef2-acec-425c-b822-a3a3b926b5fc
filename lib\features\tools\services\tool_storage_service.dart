import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/tool_definition.dart';

/// Service for managing Tool Builder data persistence
class ToolStorageService {
  static final ToolStorageService _instance = ToolStorageService._internal();
  factory ToolStorageService() => _instance;
  ToolStorageService._internal();
  static const String _toolsKey = 'tool_builder_tools';
  static const String _toolCounterKey = 'tool_builder_counter';
  static const String _autoSaveKey = 'tool_builder_autosave';
  static const String _settingsKey = 'tool_builder_settings';

  // Auto-save functionality
  Timer? _autoSaveTimer;
  final Map<String, dynamic> _pendingChanges = {};
  bool _autoSaveEnabled = true;

  /// Save a tool to persistent storage
  Future<void> saveTool(ToolDefinition tool) async {
    try {
      final tools = await getUserTools(tool.userId);
      
      // Generate ID if new tool
      ToolDefinition toolToSave = tool;
      if (tool.id == null) {
        final counter = await _getNextId();
        toolToSave = tool.copyWith(id: counter);
      } else {
        // Update existing tool
        tools.removeWhere((t) => t.id == tool.id);
      }
      
      tools.add(toolToSave);
      await _saveToolsList(tools);
    } catch (e) {
      throw Exception('Failed to save tool: $e');
    }
  }

  /// Get all tools for a specific user
  Future<List<ToolDefinition>> getUserTools(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final toolsData = prefs.getString(_toolsKey);
      if (toolsData == null) return [];

      final toolsList = jsonDecode(toolsData) as List;
      return toolsList
          .map((json) => ToolDefinition.fromJson(json, userId))
          .where((tool) => tool.userId == userId && !tool.isDeleted)
          .toList()
        ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    } catch (e) {
      return [];
    }
  }

  /// Get a specific tool by ID
  Future<ToolDefinition?> getTool(int toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final toolsData = prefs.getString(_toolsKey);
      if (toolsData == null) return null;

      final toolsList = jsonDecode(toolsData) as List;
      for (final json in toolsList) {
        final tool = ToolDefinition.fromJson(json, json['userId']);
        if (tool.id == toolId && !tool.isDeleted) {
          return tool;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Delete a tool (soft delete)
  Future<void> deleteTool(int toolId) async {
    try {
      final tool = await getTool(toolId);
      if (tool == null) return;

      final updatedTool = tool.copyWith(
        isDeleted: true,
        updatedAt: DateTime.now(),
      );

      await saveTool(updatedTool);
    } catch (e) {
      throw Exception('Failed to delete tool: $e');
    }
  }

  /// Duplicate a tool
  Future<ToolDefinition> duplicateTool(int toolId, String userId) async {
    try {
      final originalTool = await getTool(toolId);
      if (originalTool == null) {
        throw Exception('Tool not found');
      }

      final duplicatedTool = ToolDefinition.create(
        name: '${originalTool.name} (Copy)',
        description: originalTool.description,
        userId: userId,
        backendData: originalTool.backendData,
        uiLayout: originalTool.uiLayout,
        isPasswordProtected: false, // Remove password protection on copy
      );

      await saveTool(duplicatedTool);
      return duplicatedTool;
    } catch (e) {
      throw Exception('Failed to duplicate tool: $e');
    }
  }

  /// Export a tool as JSON string
  Future<String> exportTool(int toolId) async {
    try {
      final tool = await getTool(toolId);
      if (tool == null) {
        throw Exception('Tool not found');
      }

      final exportData = {
        'version': '1.0',
        'exportedAt': DateTime.now().toIso8601String(),
        'tool': tool.toJson(),
      };

      return jsonEncode(exportData);
    } catch (e) {
      throw Exception('Failed to export tool: $e');
    }
  }

  /// Import a tool from JSON string
  Future<ToolDefinition> importTool(String jsonData, String userId) async {
    try {
      final data = jsonDecode(jsonData);
      
      // Handle different import formats
      Map<String, dynamic> toolData;
      if (data.containsKey('tool')) {
        // New format with metadata
        toolData = data['tool'];
      } else {
        // Direct tool data
        toolData = data;
      }

      final tool = ToolDefinition.fromJson(toolData, userId);
      
      // Ensure unique name
      final existingTools = await getUserTools(userId);
      String uniqueName = tool.name;
      int counter = 1;
      while (existingTools.any((t) => t.name == uniqueName)) {
        uniqueName = '${tool.name} ($counter)';
        counter++;
      }

      final importedTool = tool.copyWith(
        name: uniqueName,
        id: null, // Will be assigned new ID
      );

      await saveTool(importedTool);
      return importedTool;
    } catch (e) {
      throw Exception('Failed to import tool: $e');
    }
  }

  /// Search tools by name or description
  Future<List<ToolDefinition>> searchTools(String userId, String query) async {
    try {
      final tools = await getUserTools(userId);
      final lowercaseQuery = query.toLowerCase();

      return tools.where((tool) {
        return tool.name.toLowerCase().contains(lowercaseQuery) ||
               tool.description.toLowerCase().contains(lowercaseQuery);
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get tool statistics for a user
  Future<Map<String, dynamic>> getToolStats(String userId) async {
    try {
      final tools = await getUserTools(userId);
      
      return {
        'totalTools': tools.length,
        'recentTools': tools.where((t) => 
          DateTime.now().difference(t.updatedAt).inDays <= 7
        ).length,
        'protectedTools': tools.where((t) => t.isPasswordProtected).length,
        'oldestTool': tools.isNotEmpty 
          ? tools.reduce((a, b) => a.createdAt.isBefore(b.createdAt) ? a : b).createdAt
          : null,
        'newestTool': tools.isNotEmpty 
          ? tools.reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b).createdAt
          : null,
      };
    } catch (e) {
      return {
        'totalTools': 0,
        'recentTools': 0,
        'protectedTools': 0,
        'oldestTool': null,
        'newestTool': null,
      };
    }
  }

  /// Clear all tools for a user (for testing/reset)
  Future<void> clearUserTools(String userId) async {
    try {
      final allTools = await _getAllTools();
      final filteredTools = allTools.where((tool) => 
        ToolDefinition.fromJson(tool, tool['userId']).userId != userId
      ).toList();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_toolsKey, jsonEncode(filteredTools));
    } catch (e) {
      throw Exception('Failed to clear user tools: $e');
    }
  }

  /// Private helper methods

  Future<List<Map<String, dynamic>>> _getAllTools() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final toolsData = prefs.getString(_toolsKey);
      if (toolsData == null) return [];
      return List<Map<String, dynamic>>.from(jsonDecode(toolsData));
    } catch (e) {
      return [];
    }
  }

  Future<void> _saveToolsList(List<ToolDefinition> tools) async {
    final prefs = await SharedPreferences.getInstance();
    final toolsJson = tools.map((tool) => tool.toJson()).toList();
    await prefs.setString(_toolsKey, jsonEncode(toolsJson));
  }

  Future<int> _getNextId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final counterData = prefs.getString(_toolCounterKey);
      int counter = counterData != null ? int.parse(counterData) : 1;
      counter++;
      await prefs.setString(_toolCounterKey, counter.toString());
      return counter;
    } catch (e) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_toolCounterKey, '1');
      return 1;
    }
  }

  /// Backup and restore functionality

  Future<String> createBackup(String userId) async {
    try {
      final tools = await getUserTools(userId);
      final backupData = {
        'version': '1.0',
        'userId': userId,
        'createdAt': DateTime.now().toIso8601String(),
        'tools': tools.map((tool) => tool.toJson()).toList(),
      };
      
      return jsonEncode(backupData);
    } catch (e) {
      throw Exception('Failed to create backup: $e');
    }
  }

  Future<void> restoreFromBackup(String backupData, String userId) async {
    try {
      final data = jsonDecode(backupData);
      final tools = data['tools'] as List;

      for (final toolData in tools) {
        final tool = ToolDefinition.fromJson(toolData, userId);
        await saveTool(tool.copyWith(id: null)); // Assign new IDs
      }
    } catch (e) {
      throw Exception('Failed to restore from backup: $e');
    }
  }

  /// Auto-save functionality

  /// Enable auto-save with specified interval
  void enableAutoSave({Duration interval = const Duration(seconds: 30)}) {
    _autoSaveEnabled = true;
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(interval, (_) => _performAutoSave());
  }

  /// Disable auto-save
  void disableAutoSave() {
    _autoSaveEnabled = false;
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
  }

  /// Queue changes for auto-save
  void queueAutoSave(String key, dynamic data) {
    if (!_autoSaveEnabled) return;
    _pendingChanges[key] = data;
  }

  /// Perform auto-save of pending changes
  Future<void> _performAutoSave() async {
    if (_pendingChanges.isEmpty) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      for (final entry in _pendingChanges.entries) {
        await prefs.setString(_autoSaveKey + '_' + entry.key, jsonEncode(entry.value));
      }

      _pendingChanges.clear();
    } catch (e) {
      // Auto-save failed, but don't throw - just log
      print('Auto-save failed: $e');
    }
  }

  /// Restore auto-saved data
  Future<Map<String, dynamic>> restoreAutoSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_autoSaveKey));
      final restoredData = <String, dynamic>{};

      for (final key in keys) {
        final data = prefs.getString(key);
        if (data != null) {
          final cleanKey = key.replaceFirst(_autoSaveKey + '_', '');
          restoredData[cleanKey] = jsonDecode(data);
        }
      }

      return restoredData;
    } catch (e) {
      return {};
    }
  }

  /// Clear auto-saved data
  Future<void> clearAutoSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_autoSaveKey));

      for (final key in keys) {
        await prefs.remove(key);
      }

      _pendingChanges.clear();
    } catch (e) {
      print('Failed to clear auto-saved data: $e');
    }
  }

  /// Save tool builder settings
  Future<void> saveSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_settingsKey, jsonEncode(settings));
    } catch (e) {
      throw Exception('Failed to save settings: $e');
    }
  }

  /// Load tool builder settings
  Future<Map<String, dynamic>> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsData = prefs.getString(_settingsKey);
      if (settingsData == null) return {};
      return Map<String, dynamic>.from(jsonDecode(settingsData));
    } catch (e) {
      return {};
    }
  }

  /// Dispose resources
  void dispose() {
    _autoSaveTimer?.cancel();
    _pendingChanges.clear();
  }
}
