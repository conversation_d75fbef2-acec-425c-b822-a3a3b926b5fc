import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';
import '../models/dhikr_models.dart';

/// Service for managing Quran data and operations
class QuranService {
  static final QuranService _instance = QuranService._internal();
  factory QuranService() => _instance;
  QuranService._internal();

  List<Surah> _surahs = [];
  List<QuranBookmark> _bookmarks = [];
  List<ReadingProgress> _readingProgress = [];
  QuranSettings _settings = const QuranSettings();

  bool _isInitialized = false;

  /// Initialize the Quran service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadQuranData();
      await _loadBookmarks();
      await _loadReadingProgress();
      await _loadSettings();
      _isInitialized = true;
    } catch (e) {
      print('Error initializing Quran service: $e');
      // Initialize with default data if loading fails
      await _initializeDefaultData();
      _isInitialized = true;
    }
  }

  /// Load Quran data from file or assets
  Future<void> _loadQuranData() async {
    try {
      // First try to load from external Quran.txt file
      final file = File('Quran.txt');
      if (await file.exists()) {
        final content = await file.readAsString(encoding: utf8);
        _parseQuranText(content);
        return;
      }
    } catch (e) {
      print('Could not load external Quran.txt: $e');
    }

    try {
      // Fallback to assets
      final content = await rootBundle.loadString('assets/data/quran.json');
      final data = json.decode(content) as Map<String, dynamic>;
      _surahs = (data['surahs'] as List<dynamic>)
          .map((s) => Surah.fromJson(s as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Could not load Quran from assets: $e');
      // Initialize with basic Surah data
      await _initializeBasicSurahs();
    }
  }

  /// Parse Quran text file format
  void _parseQuranText(String content) {
    final lines = content.split('\n');
    final Map<int, List<String>> surahVerses = {};
    
    for (final line in lines) {
      if (line.trim().isEmpty) continue;
      
      // Expected format: surah_number|verse_number|arabic_text|translation
      final parts = line.split('|');
      if (parts.length >= 3) {
        final surahNum = int.tryParse(parts[0]) ?? 1;
        final verseNum = int.tryParse(parts[1]) ?? 1;
        final arabicText = parts[2];
        final translation = parts.length > 3 ? parts[3] : '';
        
        if (!surahVerses.containsKey(surahNum)) {
          surahVerses[surahNum] = [];
        }
        
        // Store verse data
        surahVerses[surahNum]!.add('$verseNum|$arabicText|$translation');
      }
    }

    // Convert to Surah objects
    _surahs = [];
    for (int i = 1; i <= 114; i++) {
      final verses = surahVerses[i] ?? [];
      final surahInfo = _getSurahInfo(i);

      final parsedVerses = verses.map((verseData) {
        final parts = verseData.split('|');
        return Verse(
          number: int.parse(parts[0]),
          textArabic: parts[1],
          textTransliteration: '',
          textTranslation: parts.length > 2 ? parts[2] : '',
          surahNumber: i,
        );
      }).toList();

      _surahs.add(Surah(
        number: i,
        nameArabic: surahInfo['arabic']!,
        nameEnglish: surahInfo['english']!,
        nameTransliteration: surahInfo['transliteration']!,
        versesCount: verses.length,
        revelationType: surahInfo['type']!,
        revelationOrder: int.parse(surahInfo['order']!),
        verses: parsedVerses,
      ));
    }
  }

  /// Initialize basic Surah information
  Future<void> _initializeBasicSurahs() async {
    _surahs = [];
    for (int i = 1; i <= 114; i++) {
      final surahInfo = _getSurahInfo(i);
      _surahs.add(Surah(
        number: i,
        nameArabic: surahInfo['arabic']!,
        nameEnglish: surahInfo['english']!,
        nameTransliteration: surahInfo['transliteration']!,
        versesCount: int.parse(surahInfo['verses']!),
        revelationType: surahInfo['type']!,
        revelationOrder: int.parse(surahInfo['order']!),
        verses: [], // Will be loaded on demand
      ));
    }
  }

  /// Get basic Surah information
  Map<String, String> _getSurahInfo(int surahNumber) {
    final surahData = {
      1: {'arabic': 'الفاتحة', 'english': 'Al-Fatihah', 'transliteration': 'Al-Faatiha', 'verses': '7', 'type': 'Meccan', 'order': '5'},
      2: {'arabic': 'البقرة', 'english': 'Al-Baqarah', 'transliteration': 'Al-Baqara', 'verses': '286', 'type': 'Medinan', 'order': '87'},
      3: {'arabic': 'آل عمران', 'english': 'Ali \'Imran', 'transliteration': 'Aal-i-Imraan', 'verses': '200', 'type': 'Medinan', 'order': '89'},
      4: {'arabic': 'النساء', 'english': 'An-Nisa', 'transliteration': 'An-Nisaa', 'verses': '176', 'type': 'Medinan', 'order': '92'},
      5: {'arabic': 'المائدة', 'english': 'Al-Ma\'idah', 'transliteration': 'Al-Maaida', 'verses': '120', 'type': 'Medinan', 'order': '112'},
      // Add more surahs as needed - this is a basic implementation
    };

    return surahData[surahNumber] ?? {
      'arabic': 'سورة $surahNumber',
      'english': 'Surah $surahNumber',
      'transliteration': 'Surah $surahNumber',
      'verses': '1',
      'type': 'Meccan',
      'order': '$surahNumber'
    };
  }

  /// Initialize with default data
  Future<void> _initializeDefaultData() async {
    await _initializeBasicSurahs();
    _bookmarks = [];
    _readingProgress = [];
    _settings = const QuranSettings();
  }

  /// Load bookmarks from storage
  Future<void> _loadBookmarks() async {
    // Implementation for loading bookmarks from local storage
    _bookmarks = [];
  }

  /// Load reading progress from storage
  Future<void> _loadReadingProgress() async {
    // Implementation for loading reading progress from local storage
    _readingProgress = [];
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    // Implementation for loading settings from local storage
    _settings = const QuranSettings();
  }

  /// Get all Surahs
  List<Surah> getSurahs() => List.unmodifiable(_surahs);

  /// Get specific Surah by number
  Surah? getSurah(int surahNumber) {
    try {
      return _surahs.firstWhere((s) => s.number == surahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get verses for a specific Surah
  Future<List<Verse>> getVerses(int surahNumber) async {
    final surah = getSurah(surahNumber);
    if (surah == null) return [];

    if (surah.verses.isNotEmpty) {
      return surah.verses;
    }

    // Load verses on demand if not already loaded
    return await _loadSurahVerses(surahNumber);
  }

  /// Load verses for a specific Surah
  Future<List<Verse>> _loadSurahVerses(int surahNumber) async {
    // Implementation for loading specific Surah verses
    // This would typically load from a database or API
    return [];
  }

  /// Search in Quran
  Future<List<QuranSearchResult>> search(String query) async {
    if (query.trim().isEmpty) return [];

    final results = <QuranSearchResult>[];
    final searchQuery = query.toLowerCase();

    for (final surah in _surahs) {
      final verses = await getVerses(surah.number);
      for (final verse in verses) {
        if (verse.textArabic.toLowerCase().contains(searchQuery) ||
            verse.textTranslation.toLowerCase().contains(searchQuery) ||
            verse.textTransliteration.toLowerCase().contains(searchQuery)) {
          
          results.add(QuranSearchResult(
            verse: verse,
            surah: surah,
            highlightedText: _highlightText(verse.textTranslation, query),
            relevanceScore: _calculateRelevance(verse, query),
          ));
        }
      }
    }

    // Sort by relevance
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    return results;
  }

  /// Highlight search terms in text
  String _highlightText(String text, String query) {
    if (query.trim().isEmpty) return text;
    
    final regex = RegExp(RegExp.escape(query), caseSensitive: false);
    return text.replaceAll(regex, '<mark>$query</mark>');
  }

  /// Calculate search relevance score
  double _calculateRelevance(Verse verse, String query) {
    double score = 0.0;
    final queryLower = query.toLowerCase();
    
    // Exact matches get higher scores
    if (verse.textTranslation.toLowerCase().contains(queryLower)) {
      score += 10.0;
    }
    
    // Word matches
    final words = queryLower.split(' ');
    for (final word in words) {
      if (verse.textTranslation.toLowerCase().contains(word)) {
        score += 5.0;
      }
    }
    
    return score;
  }

  /// Add bookmark
  Future<void> addBookmark(QuranBookmark bookmark) async {
    _bookmarks.add(bookmark);
    await _saveBookmarks();
  }

  /// Remove bookmark
  Future<void> removeBookmark(String bookmarkId) async {
    _bookmarks.removeWhere((b) => b.id == bookmarkId);
    await _saveBookmarks();
  }

  /// Get all bookmarks
  List<QuranBookmark> getBookmarks() => List.unmodifiable(_bookmarks);

  /// Update reading progress
  Future<void> updateReadingProgress(int surahNumber, int verseNumber) async {
    final existingIndex = _readingProgress.indexWhere(
      (p) => p.surahNumber == surahNumber,
    );

    final progress = ReadingProgress(
      id: 'progress_$surahNumber',
      surahNumber: surahNumber,
      verseNumber: verseNumber,
      lastReadAt: DateTime.now(),
      totalReadingTime: existingIndex >= 0 
          ? _readingProgress[existingIndex].totalReadingTime + 60
          : 60,
      sessionCount: existingIndex >= 0 
          ? _readingProgress[existingIndex].sessionCount + 1
          : 1,
      completionPercentage: _calculateCompletionPercentage(surahNumber, verseNumber),
    );

    if (existingIndex >= 0) {
      _readingProgress[existingIndex] = progress;
    } else {
      _readingProgress.add(progress);
    }

    await _saveReadingProgress();
  }

  /// Calculate completion percentage for a Surah
  double _calculateCompletionPercentage(int surahNumber, int verseNumber) {
    final surah = getSurah(surahNumber);
    if (surah == null) return 0.0;
    
    return (verseNumber / surah.versesCount) * 100.0;
  }

  /// Get reading progress
  List<ReadingProgress> getReadingProgress() => List.unmodifiable(_readingProgress);

  /// Update settings
  Future<void> updateSettings(QuranSettings settings) async {
    _settings = settings;
    await _saveSettings();
  }

  /// Get current settings
  QuranSettings getSettings() => _settings;

  /// Save bookmarks to storage
  Future<void> _saveBookmarks() async {
    // Implementation for saving bookmarks to local storage
  }

  /// Save reading progress to storage
  Future<void> _saveReadingProgress() async {
    // Implementation for saving reading progress to local storage
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    // Implementation for saving settings to local storage
  }

  /// Get random verse
  Future<Verse?> getRandomVerse() async {
    if (_surahs.isEmpty) return null;
    
    final randomSurah = _surahs[DateTime.now().millisecond % _surahs.length];
    final verses = await getVerses(randomSurah.number);
    
    if (verses.isEmpty) return null;
    
    return verses[DateTime.now().microsecond % verses.length];
  }

  /// Get verse of the day
  Future<Verse?> getVerseOfTheDay() async {
    // Use date as seed for consistent daily verse
    final today = DateTime.now();
    final daysSinceEpoch = today.difference(DateTime(2024, 1, 1)).inDays;

    if (_surahs.isEmpty) return null;

    final surahIndex = daysSinceEpoch % _surahs.length;
    final surah = _surahs[surahIndex];
    final verses = await getVerses(surah.number);

    if (verses.isEmpty) return null;

    final verseIndex = daysSinceEpoch % verses.length;
    return verses[verseIndex];
  }
}

/// Service for managing Dhikr data and operations
class DhikrService {
  static final DhikrService _instance = DhikrService._internal();
  factory DhikrService() => _instance;
  DhikrService._internal();

  List<Dhikr> _dhikrs = [];
  List<DhikrCategory> _categories = [];
  List<DhikrSession> _sessions = [];
  List<DhikrReminder> _reminders = [];
  DhikrStatistics _statistics = const DhikrStatistics();

  bool _isInitialized = false;

  /// Initialize the Dhikr service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCategories();
      await _loadDhikrs();
      await _loadSessions();
      await _loadReminders();
      await _loadStatistics();
      _isInitialized = true;
    } catch (e) {
      print('Error initializing Dhikr service: $e');
      await _initializeDefaultData();
      _isInitialized = true;
    }
  }

  /// Initialize with default data
  Future<void> _initializeDefaultData() async {
    _categories = [
      const DhikrCategory(
        id: 'morning',
        name: 'Morning Adhkar',
        nameArabic: 'أذكار الصباح',
        description: 'Remembrances to recite in the morning',
        icon: 'wb_sunny',
        color: '#FF9800',
        isDefault: true,
      ),
      const DhikrCategory(
        id: 'evening',
        name: 'Evening Adhkar',
        nameArabic: 'أذكار المساء',
        description: 'Remembrances to recite in the evening',
        icon: 'brightness_3',
        color: '#3F51B5',
        isDefault: true,
      ),
      const DhikrCategory(
        id: 'prayer',
        name: 'After Prayer',
        nameArabic: 'أذكار بعد الصلاة',
        description: 'Remembrances after completing prayers',
        icon: 'mosque',
        color: '#4CAF50',
        isDefault: true,
      ),
      const DhikrCategory(
        id: 'general',
        name: 'General Dhikr',
        nameArabic: 'أذكار عامة',
        description: 'General remembrances for any time',
        icon: 'favorite',
        color: '#E91E63',
        isDefault: true,
      ),
    ];

    _dhikrs = [
      const Dhikr(
        id: 'subhanallah',
        textArabic: 'سُبْحَانَ اللَّهِ',
        textTransliteration: 'Subhan Allah',
        textTranslation: 'Glory be to Allah',
        category: 'general',
        recommendedCount: 33,
        source: 'Hadith',
        benefits: 'Purifies the heart and brings peace',
        tags: ['tasbih', 'glorification'],
      ),
      const Dhikr(
        id: 'alhamdulillah',
        textArabic: 'الْحَمْدُ لِلَّهِ',
        textTransliteration: 'Alhamdulillah',
        textTranslation: 'All praise is due to Allah',
        category: 'general',
        recommendedCount: 33,
        source: 'Hadith',
        benefits: 'Increases gratitude and blessings',
        tags: ['praise', 'gratitude'],
      ),
      const Dhikr(
        id: 'allahu_akbar',
        textArabic: 'اللَّهُ أَكْبَرُ',
        textTransliteration: 'Allahu Akbar',
        textTranslation: 'Allah is the Greatest',
        category: 'general',
        recommendedCount: 34,
        source: 'Hadith',
        benefits: 'Strengthens faith and submission',
        tags: ['takbir', 'greatness'],
      ),
      const Dhikr(
        id: 'la_ilaha_illa_allah',
        textArabic: 'لَا إِلَٰهَ إِلَّا اللَّهُ',
        textTransliteration: 'La ilaha illa Allah',
        textTranslation: 'There is no god but Allah',
        category: 'general',
        recommendedCount: 100,
        source: 'Quran & Hadith',
        benefits: 'The greatest dhikr, brings immense reward',
        tags: ['tawhid', 'declaration'],
      ),
    ];

    _sessions = [];
    _reminders = [];
    _statistics = const DhikrStatistics();
  }

  /// Load categories from storage
  Future<void> _loadCategories() async {
    // Implementation for loading categories from local storage
  }

  /// Load dhikrs from storage
  Future<void> _loadDhikrs() async {
    // Implementation for loading dhikrs from local storage
  }

  /// Load sessions from storage
  Future<void> _loadSessions() async {
    // Implementation for loading sessions from local storage
  }

  /// Load reminders from storage
  Future<void> _loadReminders() async {
    // Implementation for loading reminders from local storage
  }

  /// Load statistics from storage
  Future<void> _loadStatistics() async {
    // Implementation for loading statistics from local storage
  }

  /// Get all categories
  List<DhikrCategory> getCategories() => List.unmodifiable(_categories);

  /// Get dhikrs by category
  List<Dhikr> getDhikrsByCategory(String categoryId) {
    return _dhikrs.where((d) => d.category == categoryId).toList();
  }

  /// Get all dhikrs
  List<Dhikr> getAllDhikrs() => List.unmodifiable(_dhikrs);

  /// Get favorite dhikrs
  List<Dhikr> getFavoriteDhikrs() {
    return _dhikrs.where((d) => d.isFavorite).toList();
  }

  /// Start dhikr session
  Future<DhikrSession> startSession(String dhikrId, int targetCount) async {
    final session = DhikrSession(
      id: 'session_${DateTime.now().millisecondsSinceEpoch}',
      dhikrId: dhikrId,
      targetCount: targetCount,
      startTime: DateTime.now(),
    );

    _sessions.add(session);
    await _saveSessions();
    return session;
  }

  /// Update session count
  Future<void> updateSessionCount(String sessionId) async {
    final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
    if (sessionIndex >= 0) {
      final session = _sessions[sessionIndex];
      final updatedSession = session.copyWith(
        currentCount: session.currentCount + 1,
        recitationTimes: [...session.recitationTimes, DateTime.now()],
        isCompleted: session.currentCount + 1 >= session.targetCount,
        endTime: session.currentCount + 1 >= session.targetCount ? DateTime.now() : null,
      );

      _sessions[sessionIndex] = updatedSession;
      await _saveSessions();
      await _updateStatistics(session.dhikrId);
    }
  }

  /// Complete session
  Future<void> completeSession(String sessionId) async {
    final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
    if (sessionIndex >= 0) {
      final session = _sessions[sessionIndex];
      final updatedSession = session.copyWith(
        isCompleted: true,
        endTime: DateTime.now(),
      );

      _sessions[sessionIndex] = updatedSession;
      await _saveSessions();
    }
  }

  /// Get active session
  DhikrSession? getActiveSession() {
    try {
      return _sessions.firstWhere((s) => !s.isCompleted);
    } catch (e) {
      return null;
    }
  }

  /// Get session history
  List<DhikrSession> getSessionHistory() {
    return _sessions.where((s) => s.isCompleted).toList()
      ..sort((a, b) => b.startTime.compareTo(a.startTime));
  }

  /// Toggle favorite
  Future<void> toggleFavorite(String dhikrId) async {
    final dhikrIndex = _dhikrs.indexWhere((d) => d.id == dhikrId);
    if (dhikrIndex >= 0) {
      final dhikr = _dhikrs[dhikrIndex];
      _dhikrs[dhikrIndex] = dhikr.copyWith(isFavorite: !dhikr.isFavorite);
      await _saveDhikrs();
    }
  }

  /// Update statistics
  Future<void> _updateStatistics(String dhikrId) async {
    // Implementation for updating statistics
    await _saveStatistics();
  }

  /// Get statistics
  DhikrStatistics getStatistics() => _statistics;

  /// Save methods
  Future<void> _saveCategories() async {
    // Implementation for saving categories to local storage
  }

  Future<void> _saveDhikrs() async {
    // Implementation for saving dhikrs to local storage
  }

  Future<void> _saveSessions() async {
    // Implementation for saving sessions to local storage
  }

  Future<void> _saveReminders() async {
    // Implementation for saving reminders to local storage
  }

  Future<void> _saveStatistics() async {
    // Implementation for saving statistics to local storage
  }
}
