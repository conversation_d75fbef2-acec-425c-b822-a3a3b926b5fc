import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/locale_config.dart';
import '../services/translation_service.dart';

// Translation service provider
final translationServiceProvider = Provider<TranslationService>((ref) {
  return TranslationService();
});

// Current locale stream provider
final localeProvider = StreamProvider<LocaleConfig>((ref) {
  final service = ref.watch(translationServiceProvider);
  return service.localeStream;
});

// Current locale config provider
final currentLocaleProvider = Provider<LocaleConfig>((ref) {
  final localeAsync = ref.watch(localeProvider);
  return localeAsync.when(
    data: (locale) => locale,
    loading: () => LocaleConfig.english(),
    error: (_, __) => LocaleConfig.english(),
  );
});

// Current language provider
final currentLanguageProvider = Provider<SupportedLanguage>((ref) {
  final locale = ref.watch(currentLocaleProvider);
  return locale.language;
});

// Text direction provider
final textDirectionProvider = Provider<TextDirection>((ref) {
  final locale = ref.watch(currentLocaleProvider);
  return locale.textDirection;
});

// Is RTL provider
final isRTLProvider = Provider<bool>((ref) {
  final locale = ref.watch(currentLocaleProvider);
  return locale.isRTL;
});

// Translation actions provider
final translationActionsProvider = Provider<TranslationActions>((ref) {
  final service = ref.watch(translationServiceProvider);
  return TranslationActions(service);
});

// Translation helper class
class TranslationActions {
  final TranslationService _service;

  TranslationActions(this._service);

  // Locale management
  Future<void> setLanguage(SupportedLanguage language, {String? countryCode}) async {
    await _service.setLocale(language, countryCode: countryCode);
  }

  Future<void> useSystemLocale() async {
    await _service.useSystemLocale();
  }

  // Translation methods
  String translate(String key, {Map<String, dynamic>? params}) {
    return _service.translate(key, params: params);
  }

  String translatePlural(String key, int count, {Map<String, dynamic>? params}) {
    return _service.translatePlural(key, count, params: params);
  }

  // Custom translations
  Future<void> addCustomTranslation(String key, String value) async {
    await _service.addCustomTranslation(key, value);
  }

  Future<void> removeCustomTranslation(String key) async {
    await _service.removeCustomTranslation(key);
  }

  Future<void> clearCustomTranslations() async {
    await _service.clearCustomTranslations();
  }

  // Utility methods
  bool hasTranslation(String key, [String? languageCode]) {
    return _service.hasTranslation(key, languageCode);
  }

  Map<String, String> getAvailableTranslations(String key) {
    return _service.getAvailableTranslations(key);
  }

  Set<String> getAllTranslationKeys() {
    return _service.getAllTranslationKeys();
  }

  // Import/Export
  Map<String, dynamic> exportTranslations() {
    return _service.exportTranslations();
  }

  Future<void> importTranslations(Map<String, dynamic> data) async {
    await _service.importTranslations(data);
  }

  // Quick language switching
  Future<void> switchToEnglish() async {
    await setLanguage(SupportedLanguage.english);
  }

  Future<void> switchToArabic() async {
    await setLanguage(SupportedLanguage.arabic);
  }

  Future<void> switchToSpanish() async {
    await setLanguage(SupportedLanguage.spanish);
  }

  Future<void> switchToFrench() async {
    await setLanguage(SupportedLanguage.french);
  }

  Future<void> switchToGerman() async {
    await setLanguage(SupportedLanguage.german);
  }

  // Getters
  LocaleConfig get currentConfig => _service.currentConfig;
  Locale get currentLocale => _service.currentLocale;
  TextDirection get textDirection => _service.textDirection;
  bool get isRTL => _service.isRTL;
  bool get isLTR => _service.isLTR;
  SupportedLanguage get currentLanguage => _service.currentLanguage;
}

// Localized text widget
class LocalizedText extends ConsumerWidget {
  final String translationKey;
  final Map<String, dynamic>? params;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final String? fallback;

  const LocalizedText(
    this.translationKey, {
    super.key,
    this.params,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(translationActionsProvider);
    final text = actions.translate(translationKey, params: params);
    
    return Text(
      text.isEmpty ? (fallback ?? translationKey) : text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

// Localized plural text widget
class LocalizedPluralText extends ConsumerWidget {
  final String translationKey;
  final int count;
  final Map<String, dynamic>? params;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const LocalizedPluralText(
    this.translationKey,
    this.count, {
    super.key,
    this.params,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(translationActionsProvider);
    final text = actions.translatePlural(translationKey, count, params: params);
    
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

// Language selector widget
class LanguageSelector extends ConsumerWidget {
  final bool showNativeNames;
  final bool showFlags;
  final List<SupportedLanguage>? supportedLanguages;

  const LanguageSelector({
    super.key,
    this.showNativeNames = true,
    this.showFlags = false,
    this.supportedLanguages,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(translationActionsProvider);
    final currentLanguage = ref.watch(currentLanguageProvider);
    final languages = supportedLanguages ?? SupportedLanguage.values;

    return DropdownButton<SupportedLanguage>(
      value: currentLanguage,
      onChanged: (language) {
        if (language != null) {
          actions.setLanguage(language);
        }
      },
      items: languages.map((language) {
        return DropdownMenuItem<SupportedLanguage>(
          value: language,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showFlags) ...[
                // You could add flag icons here
                const SizedBox(width: 8),
              ],
              Text(
                showNativeNames ? language.nativeName : language.englishName,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

// RTL wrapper widget
class RTLWrapper extends ConsumerWidget {
  final Widget child;

  const RTLWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textDirection = ref.watch(textDirectionProvider);
    
    return Directionality(
      textDirection: textDirection,
      child: child,
    );
  }
}

// Localized app bar
class LocalizedAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String titleKey;
  final Map<String, dynamic>? titleParams;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;

  const LocalizedAppBar({
    super.key,
    required this.titleKey,
    this.titleParams,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(translationActionsProvider);
    
    return AppBar(
      title: Text(actions.translate(titleKey, params: titleParams)),
      actions: this.actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Localized button
class LocalizedButton extends ConsumerWidget {
  final String textKey;
  final Map<String, dynamic>? textParams;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final Widget? icon;

  const LocalizedButton({
    super.key,
    required this.textKey,
    this.textParams,
    this.onPressed,
    this.style,
    this.icon,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(translationActionsProvider);
    final text = actions.translate(textKey, params: textParams);
    
    if (icon != null) {
      return ElevatedButton.icon(
        onPressed: onPressed,
        icon: icon!,
        label: Text(text),
        style: style,
      );
    } else {
      return ElevatedButton(
        onPressed: onPressed,
        style: style,
        child: Text(text),
      );
    }
  }
}

// Localized snack bar helper
class LocalizedSnackBar {
  static void show(
    BuildContext context,
    WidgetRef ref,
    String messageKey, {
    Map<String, dynamic>? params,
    Duration duration = const Duration(seconds: 4),
    SnackBarAction? action,
  }) {
    final actions = ref.read(translationActionsProvider);
    final message = actions.translate(messageKey, params: params);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration,
        action: action,
      ),
    );
  }
}

// Initialize translation service provider
final initializeTranslationServiceProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(translationServiceProvider);
  await service.initialize();
});
