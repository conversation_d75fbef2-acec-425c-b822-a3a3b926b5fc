import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../models/memo_settings.dart';
import '../providers/memo_settings_provider.dart';

/// Memo Suite settings screen
class MemoSettingsScreen extends ConsumerStatefulWidget {
  const MemoSettingsScreen({super.key});

  @override
  ConsumerState<MemoSettingsScreen> createState() => _MemoSettingsScreenState();
}

class _MemoSettingsScreenState extends ConsumerState<MemoSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(memoSettingsProvider);

    return StandardizedScaffold(
      title: 'Memo Suite Settings',
      currentRoute: '/memo/settings',
      showBackButton: true,
      actions: [
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.upload),
                title: Text('Import Settings'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'reset',
              child: ListTile(
                leading: Icon(Icons.restore, color: Colors.red),
                title: Text('Reset to Defaults', style: TextStyle(color: Colors.red)),
                dense: true,
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(icon: Icon(Icons.display_settings), text: 'Display'),
          Tab(icon: Icon(Icons.note), text: 'Notes'),
          Tab(icon: Icon(Icons.check_box), text: 'Todos'),
          Tab(icon: Icon(Icons.mic), text: 'Voice'),
          Tab(icon: Icon(Icons.security), text: 'Security'),
          Tab(icon: Icon(Icons.more_horiz), text: 'Advanced'),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDisplaySettings(settings),
          _buildNoteSettings(settings),
          _buildTodoSettings(settings),
          _buildVoiceSettings(settings),
          _buildSecuritySettings(settings),
          _buildAdvancedSettings(settings),
        ],
      ),
    );
  }

  /// Build display settings tab
  Widget _buildDisplaySettings(MemoSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'View Mode',
            subtitle: 'How notes are displayed',
          ),

          StandardCard(
            child: Column(
              children: [
                // Note View Mode
                ListTile(
                  leading: const Icon(Icons.view_list),
                  title: const Text('Note View Mode'),
                  subtitle: Text(_getNoteViewModeText(settings.noteViewMode)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showNoteViewModePicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Show Note Preview
                SwitchListTile(
                  secondary: const Icon(Icons.preview),
                  title: const Text('Show Note Preview'),
                  subtitle: const Text('Display content preview in list'),
                  value: settings.showNotePreview,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateDisplaySettings(
                      showNotePreview: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Show Word Count
                SwitchListTile(
                  secondary: const Icon(Icons.text_fields),
                  title: const Text('Show Word Count'),
                  subtitle: const Text('Display word count in editor'),
                  value: settings.showWordCount,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateDisplaySettings(
                      showWordCount: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Show Character Count
                SwitchListTile(
                  secondary: const Icon(Icons.format_size),
                  title: const Text('Show Character Count'),
                  subtitle: const Text('Display character count in editor'),
                  value: settings.showCharacterCount,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateDisplaySettings(
                      showCharacterCount: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Text Formatting',
            subtitle: 'Font and text appearance',
          ),

          StandardCard(
            child: Column(
              children: [
                // Font Size
                ListTile(
                  leading: const Icon(Icons.format_size),
                  title: const Text('Font Size'),
                  subtitle: Text('${settings.fontSize.toInt()}pt'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.fontSize,
                  min: 12.0,
                  max: 24.0,
                  divisions: 12,
                  label: '${settings.fontSize.toInt()}pt',
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateFontSize(value);
                  },
                ),

                const Divider(),

                // Font Family
                ListTile(
                  leading: const Icon(Icons.font_download),
                  title: const Text('Font Family'),
                  subtitle: Text(settings.fontFamily),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showFontFamilyPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Use System Font
                SwitchListTile(
                  secondary: const Icon(Icons.phone_android),
                  title: const Text('Use System Font'),
                  subtitle: const Text('Use device default font'),
                  value: settings.useSystemFont,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(useSystemFont: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Editor Features',
            subtitle: 'Advanced editing options',
          ),

          StandardCard(
            child: Column(
              children: [
                // Markdown Preview
                SwitchListTile(
                  secondary: const Icon(Icons.preview),
                  title: const Text('Markdown Preview'),
                  subtitle: const Text('Enable live markdown preview'),
                  value: settings.enableMarkdownPreview,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateDisplaySettings(
                      enableMarkdownPreview: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Syntax Highlighting
                SwitchListTile(
                  secondary: const Icon(Icons.highlight),
                  title: const Text('Syntax Highlighting'),
                  subtitle: const Text('Highlight code blocks'),
                  value: settings.enableSyntaxHighlighting,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateDisplaySettings(
                      enableSyntaxHighlighting: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Show Line Numbers
                SwitchListTile(
                  secondary: const Icon(Icons.format_list_numbered),
                  title: const Text('Show Line Numbers'),
                  subtitle: const Text('Display line numbers in editor'),
                  value: settings.showLineNumbers,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateDisplaySettings(
                      showLineNumbers: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Word Wrap
                SwitchListTile(
                  secondary: const Icon(Icons.wrap_text),
                  title: const Text('Word Wrap'),
                  subtitle: const Text('Wrap long lines'),
                  value: settings.enableWordWrap,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateDisplaySettings(
                      enableWordWrap: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build note settings tab
  Widget _buildNoteSettings(MemoSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Note Organization',
            subtitle: 'How notes are sorted and organized',
          ),

          StandardCard(
            child: Column(
              children: [
                // Sort By
                ListTile(
                  leading: const Icon(Icons.sort),
                  title: const Text('Sort Notes By'),
                  subtitle: Text(_getNoteSortByText(settings.noteSortBy)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showNoteSortByPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Sort Order
                SwitchListTile(
                  secondary: const Icon(Icons.sort_by_alpha),
                  title: const Text('Ascending Order'),
                  subtitle: const Text('Sort in ascending order'),
                  value: settings.noteSortAscending,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateNoteSorting(
                      settings.noteSortBy,
                      value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Default Category
                ListTile(
                  leading: const Icon(Icons.category),
                  title: const Text('Default Category'),
                  subtitle: Text(settings.defaultNoteCategory),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showDefaultCategoryPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Auto Save',
            subtitle: 'Automatic saving options',
          ),

          StandardCard(
            child: Column(
              children: [
                // Auto Save
                SwitchListTile(
                  secondary: const Icon(Icons.save),
                  title: const Text('Auto Save Notes'),
                  subtitle: const Text('Automatically save changes'),
                  value: settings.autoSaveNotes,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateAutoSave(value);
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                if (settings.autoSaveNotes) ...[
                  const Divider(),

                  // Auto Save Interval
                  ListTile(
                    leading: const Icon(Icons.timer),
                    title: const Text('Auto Save Interval'),
                    subtitle: Text('${settings.autoSaveIntervalSeconds} seconds'),
                    contentPadding: EdgeInsets.zero,
                  ),
                  Slider(
                    value: settings.autoSaveIntervalSeconds.toDouble(),
                    min: 10.0,
                    max: 300.0,
                    divisions: 29,
                    label: '${settings.autoSaveIntervalSeconds}s',
                    onChanged: (value) {
                      ref.read(memoSettingsProvider.notifier).updateAutoSave(
                        true,
                        value.toInt(),
                      );
                    },
                  ),
                ],
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Text Features',
            subtitle: 'Writing assistance features',
          ),

          StandardCard(
            child: Column(
              children: [
                // Spell Check
                SwitchListTile(
                  secondary: const Icon(Icons.spellcheck),
                  title: const Text('Spell Check'),
                  subtitle: const Text('Check spelling while typing'),
                  value: settings.enableSpellCheck,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(enableSpellCheck: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Auto Correct
                SwitchListTile(
                  secondary: const Icon(Icons.auto_fix_high),
                  title: const Text('Auto Correct'),
                  subtitle: const Text('Automatically correct typos'),
                  value: settings.enableAutoCorrect,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(enableAutoCorrect: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build todo settings tab
  Widget _buildTodoSettings(MemoSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Todo Organization',
            subtitle: 'How todos are sorted and displayed',
          ),

          StandardCard(
            child: Column(
              children: [
                // Sort By
                ListTile(
                  leading: const Icon(Icons.sort),
                  title: const Text('Sort Todos By'),
                  subtitle: Text(_getTodoSortByText(settings.todoSortBy)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showTodoSortByPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Sort Order
                SwitchListTile(
                  secondary: const Icon(Icons.sort_by_alpha),
                  title: const Text('Ascending Order'),
                  subtitle: const Text('Sort in ascending order'),
                  value: settings.todoSortAscending,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateTodoSorting(
                      settings.todoSortBy,
                      value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Show Completed
                SwitchListTile(
                  secondary: const Icon(Icons.check_circle),
                  title: const Text('Show Completed Todos'),
                  subtitle: const Text('Display completed items'),
                  value: settings.showCompletedTodos,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(showCompletedTodos: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Notifications',
            subtitle: 'Todo reminders and alerts',
          ),

          StandardCard(
            child: Column(
              children: [
                // Enable Notifications
                SwitchListTile(
                  secondary: const Icon(Icons.notifications),
                  title: const Text('Todo Notifications'),
                  subtitle: const Text('Enable due date reminders'),
                  value: settings.enableTodoNotifications,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateNotificationSettings(
                      enableDueDateReminders: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                if (settings.enableTodoNotifications) ...[
                  const Divider(),

                  // Reminder Time
                  ListTile(
                    leading: const Icon(Icons.schedule),
                    title: const Text('Reminder Time'),
                    subtitle: Text('${settings.reminderMinutesBefore} minutes before'),
                    contentPadding: EdgeInsets.zero,
                  ),
                  Slider(
                    value: settings.reminderMinutesBefore.toDouble(),
                    min: 5.0,
                    max: 120.0,
                    divisions: 23,
                    label: '${settings.reminderMinutesBefore}m',
                    onChanged: (value) {
                      ref.read(memoSettingsProvider.notifier).updateSettings(
                        settings.copyWith(reminderMinutesBefore: value.toInt()),
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build voice settings tab
  Widget _buildVoiceSettings(MemoSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Recording Quality',
            subtitle: 'Audio recording settings',
          ),

          StandardCard(
            child: Column(
              children: [
                // Voice Quality
                ListTile(
                  leading: const Icon(Icons.high_quality),
                  title: const Text('Recording Quality'),
                  subtitle: Text(_getVoiceQualityText(settings.voiceMemoQuality)),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showVoiceQualityPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Audio Format
                ListTile(
                  leading: const Icon(Icons.audiotrack),
                  title: const Text('Audio Format'),
                  subtitle: Text(settings.audioFormat.toUpperCase()),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showAudioFormatPicker(settings),
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Max Recording Time
                ListTile(
                  leading: const Icon(Icons.timer),
                  title: const Text('Max Recording Time'),
                  subtitle: Text('${settings.maxRecordingMinutes} minutes'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.maxRecordingMinutes.toDouble(),
                  min: 5.0,
                  max: 180.0,
                  divisions: 35,
                  label: '${settings.maxRecordingMinutes}m',
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(maxRecordingMinutes: value.toInt()),
                    );
                  },
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Advanced Features',
            subtitle: 'Enhanced recording options',
          ),

          StandardCard(
            child: Column(
              children: [
                // Auto Transcription
                SwitchListTile(
                  secondary: const Icon(Icons.transcribe),
                  title: const Text('Auto Transcription'),
                  subtitle: const Text('Convert speech to text'),
                  value: settings.enableAutoTranscription,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(enableAutoTranscription: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Noise Reduction
                SwitchListTile(
                  secondary: const Icon(Icons.noise_control_off),
                  title: const Text('Noise Reduction'),
                  subtitle: const Text('Reduce background noise'),
                  value: settings.enableNoiseReduction,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(enableNoiseReduction: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Auto Stop
                SwitchListTile(
                  secondary: const Icon(Icons.stop),
                  title: const Text('Auto Stop Recording'),
                  subtitle: const Text('Stop when silent'),
                  value: settings.enableAutoStop,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(enableAutoStop: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build security settings tab
  Widget _buildSecuritySettings(MemoSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Access Control',
            subtitle: 'Protect your memos',
          ),

          StandardCard(
            child: Column(
              children: [
                // Require PIN
                SwitchListTile(
                  secondary: const Icon(Icons.lock),
                  title: const Text('Require PIN'),
                  subtitle: const Text('PIN required to access memos'),
                  value: settings.requirePinForAccess,
                  onChanged: (value) {
                    if (value) {
                      _setupPin(settings);
                    } else {
                      ref.read(memoSettingsProvider.notifier).updateSecuritySettings(
                        requirePin: false,
                      );
                    }
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Biometric Auth
                SwitchListTile(
                  secondary: const Icon(Icons.fingerprint),
                  title: const Text('Biometric Authentication'),
                  subtitle: const Text('Use fingerprint/face unlock'),
                  value: settings.enableBiometricAuth,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSecuritySettings(
                      enableBiometric: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Auto Lock
                ListTile(
                  leading: const Icon(Icons.timer_off),
                  title: const Text('Auto Lock'),
                  subtitle: Text('${settings.autoLockMinutes} minutes'),
                  contentPadding: EdgeInsets.zero,
                ),
                Slider(
                  value: settings.autoLockMinutes.toDouble(),
                  min: 1.0,
                  max: 30.0,
                  divisions: 29,
                  label: '${settings.autoLockMinutes}m',
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSecuritySettings(
                      autoLockMinutes: value.toInt(),
                    );
                  },
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Privacy',
            subtitle: 'Content protection',
          ),

          StandardCard(
            child: Column(
              children: [
                // Hide in Recents
                SwitchListTile(
                  secondary: const Icon(Icons.visibility_off),
                  title: const Text('Hide in Recent Apps'),
                  subtitle: const Text('Hide content in app switcher'),
                  value: settings.hideContentInRecents,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSecuritySettings(
                      requirePin: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Screenshot Protection
                SwitchListTile(
                  secondary: const Icon(Icons.screenshot_monitor),
                  title: const Text('Screenshot Protection'),
                  subtitle: const Text('Prevent screenshots'),
                  value: settings.enableScreenshotProtection,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSettings(
                      settings.copyWith(enableScreenshotProtection: value),
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Encryption
                SwitchListTile(
                  secondary: const Icon(Icons.enhanced_encryption),
                  title: const Text('Enable Encryption'),
                  subtitle: const Text('Encrypt memo data'),
                  value: settings.enableEncryption,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateSecuritySettings(
                      enableEncryption: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build advanced settings tab
  Widget _buildAdvancedSettings(MemoSettings settings) {
    return SingleChildScrollView(
      padding: ResponsiveBreakpoints.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionHeader(
            title: 'Backup & Sync',
            subtitle: 'Data management',
          ),

          StandardCard(
            child: Column(
              children: [
                // Auto Backup
                SwitchListTile(
                  secondary: const Icon(Icons.backup),
                  title: const Text('Auto Backup'),
                  subtitle: const Text('Automatically backup data'),
                  value: settings.enableAutoBackup,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateBackupSettings(
                      enableAutoBackup: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Cloud Sync
                SwitchListTile(
                  secondary: const Icon(Icons.cloud_sync),
                  title: const Text('Cloud Sync'),
                  subtitle: const Text('Sync across devices'),
                  value: settings.syncAcrossDevices,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateBackupSettings(
                      syncOnWifiOnly: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          SizedBox(height: ResponsiveSpacing.lg(context)),

          SectionHeader(
            title: 'Developer Options',
            subtitle: 'Advanced features',
          ),

          StandardCard(
            child: Column(
              children: [
                // Debug Mode
                SwitchListTile(
                  secondary: const Icon(Icons.bug_report),
                  title: const Text('Debug Mode'),
                  subtitle: const Text('Enable debug features'),
                  value: settings.enableDebugMode,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateAdvancedSettings(
                      enableDebugMode: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Beta Features
                SwitchListTile(
                  secondary: const Icon(Icons.science),
                  title: const Text('Beta Features'),
                  subtitle: const Text('Enable experimental features'),
                  value: settings.enableBetaFeatures,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateAdvancedSettings(
                      enableBetaFeatures: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),

                const Divider(),

                // Analytics
                SwitchListTile(
                  secondary: const Icon(Icons.analytics),
                  title: const Text('Analytics'),
                  subtitle: const Text('Help improve the app'),
                  value: settings.enableAnalytics,
                  onChanged: (value) {
                    ref.read(memoSettingsProvider.notifier).updateAdvancedSettings(
                      enableAnalytics: value,
                    );
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Handle menu actions
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportSettings();
        break;
      case 'import':
        _importSettings();
        break;
      case 'reset':
        _resetSettings();
        break;
    }
  }

  /// Export settings
  void _exportSettings() {
    final settingsJson = ref.read(memoSettingsProvider.notifier).exportSettings();
    // TODO: Implement actual export functionality
    print('Exported settings: $settingsJson');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Settings exported successfully')),
    );
  }

  /// Import settings
  void _importSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality will be implemented')),
    );
  }

  /// Reset settings
  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(memoSettingsProvider.notifier).resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings reset to defaults')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  /// Get note view mode text
  String _getNoteViewModeText(NoteViewMode mode) {
    switch (mode) {
      case NoteViewMode.list:
        return 'List View';
      case NoteViewMode.grid:
        return 'Grid View';
      case NoteViewMode.compact:
        return 'Compact View';
    }
  }

  /// Get note sort by text
  String _getNoteSortByText(NoteSortBy sortBy) {
    switch (sortBy) {
      case NoteSortBy.dateCreated:
        return 'Date Created';
      case NoteSortBy.dateModified:
        return 'Date Modified';
      case NoteSortBy.title:
        return 'Title';
      case NoteSortBy.category:
        return 'Category';
      case NoteSortBy.priority:
        return 'Priority';
    }
  }

  /// Get todo sort by text
  String _getTodoSortByText(TodoSortBy sortBy) {
    switch (sortBy) {
      case TodoSortBy.dueDate:
        return 'Due Date';
      case TodoSortBy.priority:
        return 'Priority';
      case TodoSortBy.dateCreated:
        return 'Date Created';
      case TodoSortBy.title:
        return 'Title';
      case TodoSortBy.category:
        return 'Category';
    }
  }

  /// Get voice quality text
  String _getVoiceQualityText(VoiceMemoQuality quality) {
    switch (quality) {
      case VoiceMemoQuality.low:
        return 'Low Quality';
      case VoiceMemoQuality.medium:
        return 'Medium Quality';
      case VoiceMemoQuality.high:
        return 'High Quality';
      case VoiceMemoQuality.lossless:
        return 'Lossless Quality';
    }
  }

  /// Show note view mode picker
  void _showNoteViewModePicker(MemoSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Note View Mode'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: NoteViewMode.values.map((mode) {
            return ListTile(
              title: Text(_getNoteViewModeText(mode)),
              trailing: settings.noteViewMode == mode
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(memoSettingsProvider.notifier).updateNoteViewMode(mode);
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show font family picker
  void _showFontFamilyPicker(MemoSettings settings) {
    final fonts = ['System', 'Roboto', 'Open Sans', 'Lato', 'Montserrat', 'Source Sans Pro'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Font Family'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: fonts.length,
            itemBuilder: (context, index) {
              final font = fonts[index];
              return ListTile(
                title: Text(font),
                trailing: settings.fontFamily == font
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () {
                  ref.read(memoSettingsProvider.notifier).updateFontFamily(font);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// Show note sort by picker
  void _showNoteSortByPicker(MemoSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Notes By'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: NoteSortBy.values.map((sortBy) {
            return ListTile(
              title: Text(_getNoteSortByText(sortBy)),
              trailing: settings.noteSortBy == sortBy
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(memoSettingsProvider.notifier).updateNoteSorting(
                  sortBy,
                  settings.noteSortAscending,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show default category picker
  void _showDefaultCategoryPicker(MemoSettings settings) {
    final categories = ['General', 'Work', 'Personal', 'Ideas', 'Projects'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Default Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: categories.map((category) {
            return ListTile(
              title: Text(category),
              trailing: settings.defaultNoteCategory == category
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(memoSettingsProvider.notifier).updateSettings(
                  settings.copyWith(defaultNoteCategory: category),
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show todo sort by picker
  void _showTodoSortByPicker(MemoSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Todos By'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: TodoSortBy.values.map((sortBy) {
            return ListTile(
              title: Text(_getTodoSortByText(sortBy)),
              trailing: settings.todoSortBy == sortBy
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(memoSettingsProvider.notifier).updateTodoSorting(
                  sortBy,
                  settings.todoSortAscending,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show voice quality picker
  void _showVoiceQualityPicker(MemoSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Recording Quality'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: VoiceMemoQuality.values.map((quality) {
            return ListTile(
              title: Text(_getVoiceQualityText(quality)),
              trailing: settings.voiceMemoQuality == quality
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(memoSettingsProvider.notifier).updateVoiceMemoQuality(quality);
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Show audio format picker
  void _showAudioFormatPicker(MemoSettings settings) {
    final formats = ['aac', 'mp3', 'wav', 'flac'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audio Format'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: formats.map((format) {
            return ListTile(
              title: Text(format.toUpperCase()),
              trailing: settings.audioFormat == format
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                ref.read(memoSettingsProvider.notifier).updateSettings(
                  settings.copyWith(audioFormat: format),
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Setup PIN
  void _setupPin(MemoSettings settings) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('PIN setup will be implemented')),
    );
  }
}