import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_component.dart';
import '../providers/ui_builder_provider.dart';
import '../widgets/components_panel.dart';
import '../widgets/properties_panel.dart';
import '../widgets/ui_canvas.dart';

/// UI Builder page for creating user interfaces
class UIBuilderPage extends ConsumerStatefulWidget {
  const UIBuilderPage({super.key});

  @override
  ConsumerState<UIBuilderPage> createState() => _UIBuilderPageState();
}

class _UIBuilderPageState extends ConsumerState<UIBuilderPage> {

  @override
  Widget build(BuildContext context) {
    final uiBuilderState = ref.watch(uiBuilderProvider);
    
    return Scaffold(
      appBar: _buildAppBar(uiBuilderState),
      body: Column(
        children: [
          // Toolbar
          _buildToolbar(uiBuilderState),
          
          // Main content
          Expanded(
            child: Row(
              children: [
                // Components panel (left)
                ComponentsPanel(
                  onComponentDragStart: _handleComponentDragStart,
                  onComponentSelected: null,
                ),
                
                // Canvas (center)
                UICanvas(
                  onComponentDropped: _handleComponentDropped,
                  onComponentSelected: (componentId) {
                    // Selection is automatically handled by the provider
                  },
                  onComponentMoved: _handleComponentMoved,
                  onComponentResized: _handleComponentResized,
                ),
                
                // Properties panel (right)
                PropertiesPanel(
                  onComponentUpdated: _handleComponentUpdated,
                  onCellPickerRequested: _showCellPickerDialog,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar(UIBuilderState state) {
    return AppBar(
      title: const Text('UI Builder'),
      actions: [
        // Undo
        IconButton(
          onPressed: state.canUndo ? () => ref.read(uiBuilderProvider.notifier).undo() : null,
          icon: const Icon(Icons.undo),
          tooltip: 'Undo',
        ),
        
        // Redo
        IconButton(
          onPressed: state.canRedo ? () => ref.read(uiBuilderProvider.notifier).redo() : null,
          icon: const Icon(Icons.redo),
          tooltip: 'Redo',
        ),
        
        const SizedBox(width: 8),
        
        // Save
        IconButton(
          onPressed: _saveProject,
          icon: const Icon(Icons.save),
          tooltip: 'Save Project',
        ),
        
        // Load
        IconButton(
          onPressed: _loadProject,
          icon: const Icon(Icons.folder_open),
          tooltip: 'Load Project',
        ),
        
        const SizedBox(width: 8),
        
        // Export
        PopupMenuButton<String>(
          icon: const Icon(Icons.download),
          tooltip: 'Export',
          onSelected: _handleExport,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'html',
              child: ListTile(
                leading: Icon(Icons.web),
                title: Text('Export as HTML'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'flutter',
              child: ListTile(
                leading: Icon(Icons.code),
                title: Text('Export as Flutter Code'),
                dense: true,
              ),
            ),
            const PopupMenuItem(
              value: 'json',
              child: ListTile(
                leading: Icon(Icons.data_object),
                title: Text('Export as JSON'),
                dense: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build toolbar
  Widget _buildToolbar(UIBuilderState state) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Edit/Preview mode toggle
          SegmentedButton<bool>(
            segments: const [
              ButtonSegment<bool>(
                value: false,
                label: Text('Edit'),
                icon: Icon(Icons.edit),
              ),
              ButtonSegment<bool>(
                value: true,
                label: Text('Preview'),
                icon: Icon(Icons.preview),
              ),
            ],
            selected: {state.isPreviewMode},
            onSelectionChanged: (Set<bool> selection) {
              ref.read(uiBuilderProvider.notifier).setPreviewMode(selection.first);
            },
          ),

          const SizedBox(width: 16),

          // Component count
          Text(
            '${state.componentCount} components',
            style: Theme.of(context).textTheme.bodyMedium,
          ),

          const SizedBox(width: 16),

          // Layout mode toggle
          SegmentedButton<LayoutMode>(
            segments: const [
              ButtonSegment<LayoutMode>(
                value: LayoutMode.freeform,
                label: Text('Free'),
                icon: Icon(Icons.open_with),
              ),
              ButtonSegment<LayoutMode>(
                value: LayoutMode.grid,
                label: Text('Grid'),
                icon: Icon(Icons.grid_on),
              ),
            ],
            selected: {state.layoutMode},
            onSelectionChanged: (Set<LayoutMode> selection) {
              ref.read(uiBuilderProvider.notifier).setLayoutMode(selection.first);
            },
          ),

          const SizedBox(width: 16),

          // Grid size (if grid mode)
          if (state.layoutMode == LayoutMode.grid) ...[
            Text(
              'Grid: ${state.gridSize.toInt()}px',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 100,
              child: Slider(
                value: state.gridSize,
                min: 4,
                max: 32,
                divisions: 7,
                onChanged: (value) {
                  ref.read(uiBuilderProvider.notifier).setGridSize(value);
                },
              ),
            ),
          ],

          const Spacer(),

          // Mode indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: state.isPreviewMode
                ? Theme.of(context).colorScheme.primaryContainer
                : Theme.of(context).colorScheme.secondaryContainer,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: state.isPreviewMode
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.secondary,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  state.isPreviewMode ? Icons.preview : Icons.edit,
                  size: 16,
                  color: state.isPreviewMode
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 4),
                Text(
                  state.isPreviewMode ? 'PREVIEW MODE' : 'EDIT MODE',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: state.isPreviewMode
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Test mode button
          ElevatedButton.icon(
            onPressed: _enterTestMode,
            icon: const Icon(Icons.play_arrow),
            label: const Text('Test'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            ),
          ),

          const SizedBox(width: 8),

          // Clear all
          ElevatedButton.icon(
            onPressed: _clearAll,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.errorContainer,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle component drag start
  void _handleComponentDragStart(UIComponent component) {
    // Optional: Show visual feedback during drag
  }

  /// Handle component dropped on canvas
  void _handleComponentDropped(UIComponent component, Offset position) {
    // Component is automatically added by the canvas
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added ${component.title} to canvas'),
        duration: const Duration(seconds: 2),
      ),
    );
  }



  /// Handle component moved
  void _handleComponentMoved(String componentId, Offset newPosition) {
    // Position is automatically updated by the provider
  }

  /// Handle component resized
  void _handleComponentResized(String componentId, Size newSize) {
    // Size is automatically updated by the provider
  }

  /// Handle component updated
  void _handleComponentUpdated(String componentId, UIComponent updatedComponent) {
    // Component is automatically updated by the provider
  }

  /// Show cell picker dialog
  void _showCellPickerDialog() {
    showDialog(
      context: context,
      builder: (context) => _CellPickerDialog(
        onCellSelected: (cellAddress) {
          final selectedComponent = ref.read(uiBuilderProvider).selectedComponent;
          if (selectedComponent != null) {
            final updatedComponent = selectedComponent.copyWith(cellBinding: cellAddress);
            ref.read(uiBuilderProvider.notifier).updateComponent(
              selectedComponent.id,
              updatedComponent,
            );
          }
        },
      ),
    );
  }

  /// Save project
  void _saveProject() {
    final state = ref.read(uiBuilderProvider);
    final projectData = state.toJson();

    // TODO: Implement actual project saving
    debugPrint('Saving project: ${projectData.keys.length} components');

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Project saved successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Load project
  void _loadProject() {
    // For now, just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Load project feature coming soon!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Handle export
  void _handleExport(String format) {
    switch (format) {
      case 'html':
        _exportAsHTML();
        break;
      case 'flutter':
        _exportAsFlutter();
        break;
      case 'json':
        _exportAsJSON();
        break;
    }
  }

  /// Export as HTML
  void _exportAsHTML() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('HTML export feature coming soon!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Export as Flutter code
  void _exportAsFlutter() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Flutter code export feature coming soon!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Export as JSON
  void _exportAsJSON() {
    final state = ref.read(uiBuilderProvider);
    final jsonData = state.toJson();

    // TODO: Implement actual JSON export
    debugPrint('Exporting JSON: ${jsonData.keys.length} components');

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('JSON export feature coming soon!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Enter test mode
  void _enterTestMode() {
    showDialog(
      context: context,
      builder: (context) => _TestModeDialog(),
    );
  }

  /// Clear all components
  void _clearAll() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Components'),
        content: const Text('Are you sure you want to remove all components? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(uiBuilderProvider.notifier).clearAll();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All components cleared'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

/// Test mode dialog for testing UI functionality
class _TestModeDialog extends ConsumerWidget {
  const _TestModeDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.play_arrow,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Test Mode - Interactive Preview',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'Test your UI with real interactions and data binding',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),

            // Test controls
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      // Inject test data
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Test data injected')),
                      );
                    },
                    icon: const Icon(Icons.data_object),
                    label: const Text('Inject Test Data'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Reset test
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Test reset')),
                      );
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Reset'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Interactive preview area
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text('Interactive UI Preview Area'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Cell picker dialog for selecting spreadsheet cells
class _CellPickerDialog extends ConsumerWidget {
  final Function(String cellAddress) onCellSelected;

  const _CellPickerDialog({required this.onCellSelected});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.table_chart,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Bind to Cell',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'Select a cell to bind data to your component',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),

            // Cell grid with data preview
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 10,
                  childAspectRatio: 1.5,
                ),
                itemCount: 100, // 10x10 grid
                itemBuilder: (context, index) {
                  final row = (index ~/ 10) + 1;
                  final col = (index % 10) + 1;
                  final cellAddress = '${String.fromCharCode(64 + col)}$row';

                  // Sample data for demonstration
                  final sampleData = _getSampleCellData(cellAddress);

                  return GestureDetector(
                    onTap: () {
                      onCellSelected(cellAddress);
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Bound to cell $cellAddress'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    },
                    child: Container(
                      margin: const EdgeInsets.all(1),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: sampleData.isNotEmpty
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey,
                        ),
                        color: sampleData.isNotEmpty
                          ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
                          : Colors.grey[50],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            cellAddress,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: sampleData.isNotEmpty
                                ? Theme.of(context).colorScheme.primary
                                : Colors.grey[600],
                            ),
                          ),
                          if (sampleData.isNotEmpty)
                            Text(
                              sampleData,
                              style: const TextStyle(fontSize: 8),
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    // Open backend spreadsheet view
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Opening backend spreadsheet view...'),
                        backgroundColor: Colors.blue,
                      ),
                    );
                  },
                  icon: const Icon(Icons.open_in_new),
                  label: const Text('Select from Backend'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Get sample data for demonstration
  String _getSampleCellData(String cellAddress) {
    // Sample data for demonstration
    final sampleData = {
      'A1': 'Revenue',
      'B1': '2024',
      'A2': 'Q1',
      'B2': '15000',
      'A3': 'Q2',
      'B3': '18000',
      'A4': 'Q3',
      'B4': '22000',
      'C1': 'Growth',
      'C2': '12%',
      'C3': '20%',
      'C4': '22%',
    };
    return sampleData[cellAddress] ?? '';
  }
}
