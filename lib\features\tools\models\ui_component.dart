import 'dart:convert';

/// UI Component model for the visual UI builder
class UIComponent {
  final String id;
  final ComponentType type;
  final String label;
  final String? bindToCell;
  final Map<String, dynamic> properties;
  final ComponentStyle style;
  final ComponentPosition position;
  final ComponentBehavior behavior;
  final int zIndex;
  final bool isSelected;
  final bool isResizing;

  const UIComponent({
    required this.id,
    required this.type,
    required this.label,
    this.bindToCell,
    required this.properties,
    this.style = const ComponentStyle(),
    this.position = const ComponentPosition(),
    this.behavior = const ComponentBehavior(),
    this.zIndex = 0,
    this.isSelected = false,
    this.isResizing = false,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'label': label,
      'bindToCell': bindToCell,
      'properties': properties,
      'style': style.toJson(),
      'position': position.toJson(),
      'behavior': behavior.toJson(),
      'zIndex': zIndex,
      'isSelected': isSelected,
      'isResizing': isResizing,
    };
  }

  /// Create from JSON
  factory UIComponent.fromJson(Map<String, dynamic> json) {
    return UIComponent(
      id: json['id'],
      type: ComponentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ComponentType.label,
      ),
      label: json['label'],
      bindToCell: json['bindToCell'],
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
      style: ComponentStyle.fromJson(json['style'] ?? {}),
      position: ComponentPosition.fromJson(json['position'] ?? {}),
      behavior: ComponentBehavior.fromJson(json['behavior'] ?? {}),
      zIndex: json['zIndex'] ?? 0,
      isSelected: json['isSelected'] ?? false,
      isResizing: json['isResizing'] ?? false,
    );
  }

  /// Create a copy with updated values
  UIComponent copyWith({
    String? id,
    ComponentType? type,
    String? label,
    String? bindToCell,
    Map<String, dynamic>? properties,
    ComponentStyle? style,
    ComponentPosition? position,
    ComponentBehavior? behavior,
    int? zIndex,
    bool? isSelected,
    bool? isResizing,
  }) {
    return UIComponent(
      id: id ?? this.id,
      type: type ?? this.type,
      label: label ?? this.label,
      bindToCell: bindToCell ?? this.bindToCell,
      properties: properties ?? this.properties,
      style: style ?? this.style,
      position: position ?? this.position,
      behavior: behavior ?? this.behavior,
      zIndex: zIndex ?? this.zIndex,
      isSelected: isSelected ?? this.isSelected,
      isResizing: isResizing ?? this.isResizing,
    );
  }

  /// Get property value with default
  T getProperty<T>(String key, T defaultValue) {
    return properties[key] as T? ?? defaultValue;
  }

  /// Update a property
  UIComponent updateProperty(String key, dynamic value) {
    final newProperties = Map<String, dynamic>.from(properties);
    newProperties[key] = value;
    return copyWith(properties: newProperties);
  }

  /// Check if component is bound to a cell
  bool get isBoundToCell => bindToCell != null && bindToCell!.isNotEmpty;

  /// Get component icon based on type
  String get iconName {
    switch (type) {
      // Input components
      case ComponentType.textField:
        return 'text_fields';
      case ComponentType.textArea:
        return 'notes';
      case ComponentType.dropdown:
        return 'arrow_drop_down';
      case ComponentType.slider:
        return 'tune';
      case ComponentType.datePicker:
        return 'calendar_today';
      case ComponentType.colorPicker:
        return 'palette';

      // Output components
      case ComponentType.outputField:
        return 'output';
      case ComponentType.label:
        return 'label';
      case ComponentType.progressBar:
        return 'linear_scale';
      case ComponentType.gauge:
        return 'speed';

      // Chart components
      case ComponentType.lineChart:
        return 'show_chart';
      case ComponentType.barChart:
        return 'bar_chart';
      case ComponentType.pieChart:
        return 'pie_chart';
      case ComponentType.scatterChart:
        return 'scatter_plot';
      case ComponentType.chart:
        return 'bar_chart';

      // Layout components
      case ComponentType.container:
        return 'crop_square';
      case ComponentType.card:
        return 'credit_card';
      case ComponentType.divider:
        return 'horizontal_rule';
      case ComponentType.spacer:
        return 'space_bar';

      // Action components
      case ComponentType.button:
        return 'smart_button';
      case ComponentType.iconButton:
        return 'radio_button_unchecked';
      case ComponentType.checkbox:
        return 'check_box';
      case ComponentType.toggle:
        return 'toggle_on';
      case ComponentType.radioButton:
        return 'radio_button_checked';

      // Media components
      case ComponentType.image:
        return 'image';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UIComponent &&
        other.id == id &&
        other.type == type &&
        other.label == label &&
        other.bindToCell == bindToCell;
  }

  @override
  int get hashCode {
    return Object.hash(id, type, label, bindToCell);
  }

  @override
  String toString() {
    return 'UIComponent(id: $id, type: $type, label: $label, bindToCell: $bindToCell)';
  }
}

/// Available UI component types
enum ComponentType {
  // Input components
  textField,
  textArea,
  dropdown,
  slider,
  datePicker,
  colorPicker,

  // Output components
  outputField,
  label,
  progressBar,
  gauge,

  // Chart components
  lineChart,
  barChart,
  pieChart,
  scatterChart,
  chart, // Keep for backward compatibility

  // Layout components
  container,
  card,
  divider,
  spacer,

  // Action components
  button,
  iconButton,
  checkbox,
  toggle,
  radioButton,

  // Media components
  image,
}

/// Component styling information
class ComponentStyle {
  final String? backgroundColor;
  final String? textColor;
  final String? borderColor;
  final double? borderWidth;
  final double? borderRadius;
  final String? fontSize;
  final bool bold;
  final bool italic;
  final double? padding;
  final double? margin;

  const ComponentStyle({
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.fontSize,
    this.bold = false,
    this.italic = false,
    this.padding,
    this.margin,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'backgroundColor': backgroundColor,
      'textColor': textColor,
      'borderColor': borderColor,
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'fontSize': fontSize,
      'bold': bold,
      'italic': italic,
      'padding': padding,
      'margin': margin,
    };
  }

  /// Create from JSON
  factory ComponentStyle.fromJson(Map<String, dynamic> json) {
    return ComponentStyle(
      backgroundColor: json['backgroundColor'],
      textColor: json['textColor'],
      borderColor: json['borderColor'],
      borderWidth: json['borderWidth']?.toDouble(),
      borderRadius: json['borderRadius']?.toDouble(),
      fontSize: json['fontSize'],
      bold: json['bold'] ?? false,
      italic: json['italic'] ?? false,
      padding: json['padding']?.toDouble(),
      margin: json['margin']?.toDouble(),
    );
  }

  /// Create a copy with updated values
  ComponentStyle copyWith({
    String? backgroundColor,
    String? textColor,
    String? borderColor,
    double? borderWidth,
    double? borderRadius,
    String? fontSize,
    bool? bold,
    bool? italic,
    double? padding,
    double? margin,
  }) {
    return ComponentStyle(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      fontSize: fontSize ?? this.fontSize,
      bold: bold ?? this.bold,
      italic: italic ?? this.italic,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
    );
  }
}

/// Component position and size information
class ComponentPosition {
  final double x;
  final double y;
  final double width;
  final double height;
  final int zIndex;

  const ComponentPosition({
    this.x = 0,
    this.y = 0,
    this.width = 100,
    this.height = 40,
    this.zIndex = 0,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'zIndex': zIndex,
    };
  }

  /// Create from JSON
  factory ComponentPosition.fromJson(Map<String, dynamic> json) {
    return ComponentPosition(
      x: json['x']?.toDouble() ?? 0,
      y: json['y']?.toDouble() ?? 0,
      width: json['width']?.toDouble() ?? 100,
      height: json['height']?.toDouble() ?? 40,
      zIndex: json['zIndex'] ?? 0,
    );
  }

  /// Create a copy with updated values
  ComponentPosition copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    int? zIndex,
  }) {
    return ComponentPosition(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      zIndex: zIndex ?? this.zIndex,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ComponentPosition &&
        other.x == x &&
        other.y == y &&
        other.width == width &&
        other.height == height &&
        other.zIndex == zIndex;
  }

  @override
  int get hashCode {
    return Object.hash(x, y, width, height, zIndex);
  }
}

/// Component behavior and interaction settings
class ComponentBehavior {
  final bool isEnabled;
  final bool isVisible;
  final bool isDraggable;
  final bool isResizable;
  final String? onClickAction;
  final String? onHoverAction;
  final String? onFocusAction;
  final Map<String, dynamic> validationRules;

  const ComponentBehavior({
    this.isEnabled = true,
    this.isVisible = true,
    this.isDraggable = true,
    this.isResizable = true,
    this.onClickAction,
    this.onHoverAction,
    this.onFocusAction,
    this.validationRules = const {},
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'isEnabled': isEnabled,
      'isVisible': isVisible,
      'isDraggable': isDraggable,
      'isResizable': isResizable,
      'onClickAction': onClickAction,
      'onHoverAction': onHoverAction,
      'onFocusAction': onFocusAction,
      'validationRules': validationRules,
    };
  }

  /// Create from JSON
  factory ComponentBehavior.fromJson(Map<String, dynamic> json) {
    return ComponentBehavior(
      isEnabled: json['isEnabled'] ?? true,
      isVisible: json['isVisible'] ?? true,
      isDraggable: json['isDraggable'] ?? true,
      isResizable: json['isResizable'] ?? true,
      onClickAction: json['onClickAction'],
      onHoverAction: json['onHoverAction'],
      onFocusAction: json['onFocusAction'],
      validationRules: json['validationRules'] ?? {},
    );
  }

  /// Create a copy with updated values
  ComponentBehavior copyWith({
    bool? isEnabled,
    bool? isVisible,
    bool? isDraggable,
    bool? isResizable,
    String? onClickAction,
    String? onHoverAction,
    String? onFocusAction,
    Map<String, dynamic>? validationRules,
  }) {
    return ComponentBehavior(
      isEnabled: isEnabled ?? this.isEnabled,
      isVisible: isVisible ?? this.isVisible,
      isDraggable: isDraggable ?? this.isDraggable,
      isResizable: isResizable ?? this.isResizable,
      onClickAction: onClickAction ?? this.onClickAction,
      onHoverAction: onHoverAction ?? this.onHoverAction,
      onFocusAction: onFocusAction ?? this.onFocusAction,
      validationRules: validationRules ?? this.validationRules,
    );
  }
}
