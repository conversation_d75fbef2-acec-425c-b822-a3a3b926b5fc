import 'package:flutter/material.dart';
import 'lib/core/services/production_readiness_service.dart';
import 'lib/core/services/data_persistence_service.dart';
import 'lib/core/database/database_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('=== ShadowSuite Production Readiness Test ===');
  print('Starting comprehensive production readiness check...\n');
  
  try {
    // Initialize database
    await DatabaseService.instance.initialize();
    
    // Run production readiness check
    final readinessService = ProductionReadinessService.instance;
    final report = await readinessService.generateDetailedReport();
    
    print(report);
    
    // Run data persistence check
    print('\n=== Data Persistence Verification ===');
    final persistenceService = DataPersistenceService.instance;
    final persistenceReport = await persistenceService.generatePersistenceReport();
    
    print(persistenceReport);
    
    // Check if ready for production
    final isReady = await readinessService.isReadyForProduction();
    
    print('\n=== Final Assessment ===');
    if (isReady) {
      print('🎉 ShadowSuite is READY for production deployment!');
      print('All systems are functioning correctly.');
    } else {
      print('⚠️  ShadowSuite is NOT ready for production.');
      print('Please address the issues identified above.');
    }
    
  } catch (e) {
    print('❌ Error during production readiness test: $e');
  }
}
