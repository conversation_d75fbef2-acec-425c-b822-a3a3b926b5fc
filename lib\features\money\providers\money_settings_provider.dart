import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/money_settings.dart';

/// Money settings notifier
class MoneySettingsNotifier extends StateNotifier<MoneySettings> {
  MoneySettingsNotifier() : super(const MoneySettings()) {
    _loadSettings();
  }

  static const String _settingsKey = 'money_settings';

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final Map<String, dynamic> data = json.decode(settingsJson);
        state = MoneySettings.fromJson(data);
      }
    } catch (e) {
      // If loading fails, keep default settings
      print('Error loading money settings: $e');
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(state.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      print('Error saving money settings: $e');
    }
  }

  /// Update settings
  Future<void> updateSettings(MoneySettings newSettings) async {
    state = newSettings;
    await _saveSettings();
  }

  /// Update primary currency
  Future<void> updatePrimaryCurrency(String currency) async {
    state = state.copyWith(primaryCurrency: currency);
    await _saveSettings();
  }

  /// Update currency symbol position
  Future<void> updateCurrencySymbolPosition(CurrencySymbolPosition position) async {
    state = state.copyWith(currencySymbolPosition: position);
    await _saveSettings();
  }

  /// Update decimal places
  Future<void> updateDecimalPlaces(int places) async {
    state = state.copyWith(decimalPlaces: places);
    await _saveSettings();
  }

  /// Update thousand separator usage
  Future<void> updateUseThousandSeparator(bool use) async {
    state = state.copyWith(useThousandSeparator: use);
    await _saveSettings();
  }

  /// Update default account
  Future<void> updateDefaultAccount(String accountId) async {
    state = state.copyWith(defaultAccountId: accountId);
    await _saveSettings();
  }

  /// Update auto-categorize setting
  Future<void> updateAutoCategorize(bool enable) async {
    state = state.copyWith(autoCategorize: enable);
    await _saveSettings();
  }

  /// Update budget alerts setting
  Future<void> updateBudgetAlerts(bool enable) async {
    state = state.copyWith(enableBudgetAlerts: enable);
    await _saveSettings();
  }

  /// Update transaction reminders setting
  Future<void> updateTransactionReminders(bool enable) async {
    state = state.copyWith(enableTransactionReminders: enable);
    await _saveSettings();
  }

  /// Update low balance alerts setting
  Future<void> updateLowBalanceAlerts(bool enable) async {
    state = state.copyWith(enableLowBalanceAlerts: enable);
    await _saveSettings();
  }

  /// Update low balance threshold
  Future<void> updateLowBalanceThreshold(double threshold) async {
    state = state.copyWith(lowBalanceThreshold: threshold);
    await _saveSettings();
  }

  /// Update reminder time
  Future<void> updateReminderTime(int hour) async {
    state = state.copyWith(reminderTime: hour);
    await _saveSettings();
  }

  /// Update date format
  Future<void> updateDateFormat(String format) async {
    state = state.copyWith(dateFormat: format);
    await _saveSettings();
  }

  /// Update time format
  Future<void> updateTimeFormat(String format) async {
    state = state.copyWith(timeFormat: format);
    await _saveSettings();
  }

  /// Update dashboard settings
  Future<void> updateDashboardSettings({
    bool? showAccountBalance,
    bool? showRecentTransactions,
    int? transactionCount,
  }) async {
    state = state.copyWith(
      showAccountBalanceOnDashboard: showAccountBalance,
      showRecentTransactionsOnDashboard: showRecentTransactions,
      dashboardTransactionCount: transactionCount,
    );
    await _saveSettings();
  }

  /// Update PIN requirement
  Future<void> updateRequirePin(bool require, {String? pinHash}) async {
    state = state.copyWith(
      requirePin: require,
      pinHash: pinHash ?? state.pinHash,
    );
    await _saveSettings();
  }

  /// Update hide amounts setting
  Future<void> updateHideAmounts(bool hide) async {
    state = state.copyWith(hideAmountsInOverview: hide);
    await _saveSettings();
  }

  /// Update biometric auth setting
  Future<void> updateBiometricAuth(bool enable) async {
    state = state.copyWith(enableBiometricAuth: enable);
    await _saveSettings();
  }

  /// Update auto lock minutes
  Future<void> updateAutoLockMinutes(int minutes) async {
    state = state.copyWith(autoLockMinutes: minutes);
    await _saveSettings();
  }

  /// Update backup settings
  Future<void> updateBackupSettings({
    bool? enableAutoBackup,
    int? frequencyDays,
    String? lastBackupDate,
  }) async {
    state = state.copyWith(
      enableAutoBackup: enableAutoBackup,
      backupFrequencyDays: frequencyDays,
      lastBackupDate: lastBackupDate,
    );
    await _saveSettings();
  }

  /// Update data sync setting
  Future<void> updateDataSync(bool enable) async {
    state = state.copyWith(enableDataSync: enable);
    await _saveSettings();
  }

  /// Update advanced features
  Future<void> updateAdvancedFeatures({
    bool? enableAdvancedReports,
    bool? enableBudgetForecasting,
    bool? enableCategoryInsights,
    bool? enableSpendingTrends,
  }) async {
    state = state.copyWith(
      enableAdvancedReports: enableAdvancedReports,
      enableBudgetForecasting: enableBudgetForecasting,
      enableCategoryInsights: enableCategoryInsights,
      enableSpendingTrends: enableSpendingTrends,
    );
    await _saveSettings();
  }

  /// Reset to default settings
  Future<void> resetToDefaults() async {
    state = const MoneySettings();
    await _saveSettings();
  }

  /// Export settings as JSON string
  String exportSettings() {
    return json.encode(state.toJson());
  }

  /// Import settings from JSON string
  Future<bool> importSettings(String settingsJson) async {
    try {
      final Map<String, dynamic> data = json.decode(settingsJson);
      final importedSettings = MoneySettings.fromJson(data);
      state = importedSettings;
      await _saveSettings();
      return true;
    } catch (e) {
      print('Error importing settings: $e');
      return false;
    }
  }

  /// Validate PIN hash
  bool validatePin(String pin, String storedHash) {
    // TODO: Implement proper PIN hashing and validation
    // For now, just compare directly (not secure)
    return pin == storedHash;
  }

  /// Generate PIN hash
  String generatePinHash(String pin) {
    // TODO: Implement proper PIN hashing
    // For now, just return the PIN (not secure)
    return pin;
  }
}

/// Provider for money settings
final moneySettingsProvider = StateNotifierProvider<MoneySettingsNotifier, MoneySettings>((ref) {
  return MoneySettingsNotifier();
});

/// Provider for formatted currency amounts
final currencyFormatterProvider = Provider<String Function(double)>((ref) {
  final settings = ref.watch(moneySettingsProvider);
  return (double amount) => settings.formatAmount(amount);
});

/// Provider for currency symbol
final currencySymbolProvider = Provider<String>((ref) {
  final settings = ref.watch(moneySettingsProvider);
  return settings.getCurrencySymbol();
});

/// Provider for checking if PIN is required
final isPinRequiredProvider = Provider<bool>((ref) {
  final settings = ref.watch(moneySettingsProvider);
  return settings.requirePin && settings.pinHash.isNotEmpty;
});

/// Provider for checking if amounts should be hidden
final shouldHideAmountsProvider = Provider<bool>((ref) {
  final settings = ref.watch(moneySettingsProvider);
  return settings.hideAmountsInOverview;
});

/// Provider for dashboard settings
final dashboardSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(moneySettingsProvider);
  return {
    'showAccountBalance': settings.showAccountBalanceOnDashboard,
    'showRecentTransactions': settings.showRecentTransactionsOnDashboard,
    'transactionCount': settings.dashboardTransactionCount,
  };
});

/// Provider for notification settings
final notificationSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(moneySettingsProvider);
  return {
    'budgetAlerts': settings.enableBudgetAlerts,
    'transactionReminders': settings.enableTransactionReminders,
    'lowBalanceAlerts': settings.enableLowBalanceAlerts,
    'lowBalanceThreshold': settings.lowBalanceThreshold,
    'reminderTime': settings.reminderTime,
  };
});

/// Provider for advanced features settings
final advancedFeaturesProvider = Provider<Map<String, bool>>((ref) {
  final settings = ref.watch(moneySettingsProvider);
  return {
    'advancedReports': settings.enableAdvancedReports,
    'budgetForecasting': settings.enableBudgetForecasting,
    'categoryInsights': settings.enableCategoryInsights,
    'spendingTrends': settings.enableSpendingTrends,
  };
});
