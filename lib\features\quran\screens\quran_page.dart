import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/quran_provider.dart';
import '../models/quran_models.dart';
import '../widgets/surah_list_widget.dart';
import '../widgets/verse_reader_widget.dart';
import '../widgets/search_widget.dart';
import '../widgets/bookmarks_widget.dart';
import '../widgets/settings_widget.dart';

/// Main Quran page with modern Material Design 3 UI
class QuranPage extends ConsumerStatefulWidget {
  const QuranPage({super.key});

  @override
  ConsumerState<QuranPage> createState() => _QuranPageState();
}

class _QuranPageState extends ConsumerState<QuranPage> with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quranState = ref.watch(quranProvider);
    
    return Scaffold(
      appBar: _buildAppBar(context, quranState),
      body: _buildBody(context, quranState),
      bottomNavigationBar: _buildBottomNavigation(context),
      floatingActionButton: _buildFloatingActionButton(context, quranState),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar(BuildContext context, QuranState state) {
    return AppBar(
      title: Row(
        children: [
          Icon(
            Icons.menu_book,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Text('القرآن الكريم'),
        ],
      ),
      centerTitle: false,
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      actions: [
        // Search button
        IconButton(
          onPressed: () => _showSearchDialog(context),
          icon: const Icon(Icons.search),
          tooltip: 'Search Quran',
        ),
        
        // Settings button
        IconButton(
          onPressed: () => _showSettingsDialog(context),
          icon: const Icon(Icons.settings),
          tooltip: 'Settings',
        ),
        
        // Refresh button
        if (state.error != null)
          IconButton(
            onPressed: () => ref.read(quranProvider.notifier).refresh(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
      ],
      bottom: _currentIndex == 0 && state.currentSurah != null
          ? PreferredSize(
              preferredSize: const Size.fromHeight(60),
              child: _buildCurrentSurahInfo(context, state.currentSurah!),
            )
          : null,
    );
  }

  /// Build current Surah info
  Widget _buildCurrentSurahInfo(BuildContext context, QuranSurah surah) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Text(
              surah.number.toString(),
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  surah.englishName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${surah.arabicName} • ${surah.verseCount} verses • ${surah.revelationType}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build body content
  Widget _buildBody(BuildContext context, QuranState state) {
    if (state.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading Quran...'),
          ],
        ),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading Quran',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => ref.read(quranProvider.notifier).refresh(),
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        // Reading tab
        state.currentSurah != null
            ? VerseReaderWidget(surah: state.currentSurah!)
            : SurahListWidget(
                surahs: state.surahs,
                onSurahSelected: (surah) {
                  ref.read(quranProvider.notifier).navigateToSurah(surah.number);
                },
              ),
        
        // Surahs tab
        SurahListWidget(
          surahs: state.surahs,
          onSurahSelected: (surah) {
            ref.read(quranProvider.notifier).navigateToSurah(surah.number);
            _tabController.animateTo(0); // Switch to reading tab
          },
        ),
        
        // Search tab
        SearchWidget(
          searchResults: state.searchResults,
          searchQuery: state.searchQuery,
          onSearch: (query) => ref.read(quranProvider.notifier).searchVerses(query),
          onClearSearch: () => ref.read(quranProvider.notifier).clearSearch(),
          onVerseSelected: (verse) {
            ref.read(quranProvider.notifier).navigateToVerse(
              verse.surahNumber,
              verse.verseNumber,
            );
            _tabController.animateTo(0); // Switch to reading tab
          },
        ),
        
        // Bookmarks tab
        BookmarksWidget(
          bookmarks: state.bookmarks,
          onBookmarkSelected: (bookmark) {
            ref.read(quranProvider.notifier).navigateToVerse(
              bookmark.verse.surahNumber,
              bookmark.verse.verseNumber,
            );
            _tabController.animateTo(0); // Switch to reading tab
          },
          onBookmarkDeleted: (bookmark) {
            ref.read(quranProvider.notifier).removeBookmark(bookmark.verse);
          },
        ),
      ],
    );
  }

  /// Build bottom navigation
  Widget _buildBottomNavigation(BuildContext context) {
    return NavigationBar(
      selectedIndex: _currentIndex,
      onDestinationSelected: (index) {
        _tabController.animateTo(index);
      },
      destinations: const [
        NavigationDestination(
          icon: Icon(Icons.menu_book_outlined),
          selectedIcon: Icon(Icons.menu_book),
          label: 'Read',
        ),
        NavigationDestination(
          icon: Icon(Icons.list_outlined),
          selectedIcon: Icon(Icons.list),
          label: 'Surahs',
        ),
        NavigationDestination(
          icon: Icon(Icons.search_outlined),
          selectedIcon: Icon(Icons.search),
          label: 'Search',
        ),
        NavigationDestination(
          icon: Icon(Icons.bookmark_outline),
          selectedIcon: Icon(Icons.bookmark),
          label: 'Bookmarks',
        ),
      ],
    );
  }

  /// Build floating action button
  Widget? _buildFloatingActionButton(BuildContext context, QuranState state) {
    if (_currentIndex != 0 || state.currentVerse == null) return null;
    
    return FloatingActionButton(
      onPressed: () => _toggleBookmark(state.currentVerse!),
      tooltip: state.isVerseBookmarked(state.currentVerse!) 
          ? 'Remove Bookmark' 
          : 'Add Bookmark',
      child: Icon(
        state.isVerseBookmarked(state.currentVerse!)
            ? Icons.bookmark
            : Icons.bookmark_outline,
      ),
    );
  }

  /// Show search dialog
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 400,
          height: 500,
          padding: const EdgeInsets.all(16),
          child: SearchWidget(
            searchResults: ref.read(quranProvider).searchResults,
            searchQuery: ref.read(quranProvider).searchQuery,
            onSearch: (query) => ref.read(quranProvider.notifier).searchVerses(query),
            onClearSearch: () => ref.read(quranProvider.notifier).clearSearch(),
            onVerseSelected: (verse) {
              ref.read(quranProvider.notifier).navigateToVerse(
                verse.surahNumber,
                verse.verseNumber,
              );
              Navigator.of(context).pop();
              _tabController.animateTo(0);
            },
          ),
        ),
      ),
    );
  }

  /// Show settings dialog
  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 400,
          height: 600,
          padding: const EdgeInsets.all(16),
          child: SettingsWidget(
            preferences: ref.read(quranProvider).preferences,
            onPreferencesChanged: (preferences) {
              ref.read(quranProvider.notifier).updatePreferences(preferences);
            },
          ),
        ),
      ),
    );
  }

  /// Toggle bookmark for current verse
  void _toggleBookmark(QuranVerse verse) {
    ref.read(quranProvider.notifier).toggleBookmark(verse);
    
    final isBookmarked = ref.read(quranProvider).isVerseBookmarked(verse);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isBookmarked 
              ? 'Verse bookmarked' 
              : 'Bookmark removed',
        ),
        action: isBookmarked ? SnackBarAction(
          label: 'Add Note',
          onPressed: () => _showAddNoteDialog(verse),
        ) : null,
      ),
    );
  }

  /// Show add note dialog for bookmark
  void _showAddNoteDialog(QuranVerse verse) {
    final TextEditingController noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Add Note to ${verse.address}'),
        content: TextField(
          controller: noteController,
          decoration: const InputDecoration(
            hintText: 'Enter your note...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final bookmark = ref.read(quranProvider).getBookmarkForVerse(verse);
              if (bookmark != null) {
                ref.read(quranProvider.notifier).updateBookmark(
                  bookmark.id,
                  note: noteController.text.trim(),
                );
              }
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
