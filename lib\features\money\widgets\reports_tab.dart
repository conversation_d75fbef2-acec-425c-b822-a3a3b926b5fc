import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/transaction_provider.dart';
import '../providers/account_provider.dart';
import '../providers/money_settings_provider.dart';
import '../models/transaction.dart';

/// Comprehensive reports tab for Money Flow
class ReportsTab extends ConsumerStatefulWidget {
  const ReportsTab({super.key});

  @override
  ConsumerState<ReportsTab> createState() => _ReportsTabState();
}

class _ReportsTabState extends ConsumerState<ReportsTab> {
  DateTimeRange? _selectedDateRange;
  String _selectedReportType = 'Income vs Expense';
  String _selectedPeriod = 'This Month';

  @override
  void initState() {
    super.initState();
    _setDefaultDateRange();
  }

  void _setDefaultDateRange() {
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    final transactions = ref.watch(transactionsProvider);
    final accounts = ref.watch(accountsProvider);
    final settings = ref.watch(moneySettingsProvider);

    return Scaffold(
      body: Column(
        children: [
          // Report controls
          _buildReportControls(),
          
          // Report content
          Expanded(
            child: _buildReportContent(transactions, accounts, settings),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _exportReport,
        icon: const Icon(Icons.download),
        label: const Text('Export'),
      ),
    );
  }

  Widget _buildReportControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Report type and period selectors
          Row(
            children: [
              // Report type dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedReportType,
                  decoration: const InputDecoration(
                    labelText: 'Report Type',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'Income vs Expense',
                    'Category Analysis',
                    'Monthly Summary',
                    'Account Performance',
                    'Cash Flow',
                    'Budget vs Actual',
                  ].map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedReportType = value!;
                    });
                  },
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Period selector
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: const InputDecoration(
                    labelText: 'Period',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    'This Week',
                    'This Month',
                    'Last Month',
                    'This Quarter',
                    'This Year',
                    'Custom Range',
                  ].map((period) {
                    return DropdownMenuItem(
                      value: period,
                      child: Text(period),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                      if (value != 'Custom Range') {
                        _updateDateRangeForPeriod(value);
                      }
                    });
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Date range selector
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range),
                  label: Text(
                    _selectedDateRange != null
                        ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                        : 'Select Date Range',
                  ),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: _generateReport,
                icon: const Icon(Icons.refresh),
                label: const Text('Generate Report'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReportContent(List<Transaction> transactions, List<dynamic> accounts, dynamic settings) {
    final filteredTransactions = _filterTransactionsByDateRange(transactions);
    
    switch (_selectedReportType) {
      case 'Income vs Expense':
        return _buildIncomeVsExpenseReport(filteredTransactions);
      case 'Category Analysis':
        return _buildCategoryAnalysisReport(filteredTransactions);
      case 'Monthly Summary':
        return _buildMonthlySummaryReport(filteredTransactions);
      case 'Account Performance':
        return _buildAccountPerformanceReport(filteredTransactions, accounts);
      case 'Cash Flow':
        return _buildCashFlowReport(filteredTransactions);
      case 'Budget vs Actual':
        return _buildBudgetVsActualReport(filteredTransactions);
      default:
        return _buildIncomeVsExpenseReport(filteredTransactions);
    }
  }

  Widget _buildIncomeVsExpenseReport(List<Transaction> transactions) {
    final income = transactions
        .where((t) => t.amount > 0)
        .fold(0.0, (sum, t) => sum + t.amount);
    
    final expenses = transactions
        .where((t) => t.amount < 0)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
    
    final netIncome = income - expenses;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          Row(
            children: [
              Expanded(child: _buildSummaryCard('Total Income', income, Colors.green)),
              const SizedBox(width: 16),
              Expanded(child: _buildSummaryCard('Total Expenses', expenses, Colors.red)),
              const SizedBox(width: 16),
              Expanded(child: _buildSummaryCard('Net Income', netIncome, netIncome >= 0 ? Colors.green : Colors.red)),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Income breakdown
          _buildSectionHeader('Income Breakdown'),
          _buildTransactionList(transactions.where((t) => t.amount > 0).toList()),
          
          const SizedBox(height: 24),
          
          // Expense breakdown
          _buildSectionHeader('Expense Breakdown'),
          _buildTransactionList(transactions.where((t) => t.amount < 0).toList()),
        ],
      ),
    );
  }

  Widget _buildCategoryAnalysisReport(List<Transaction> transactions) {
    final categoryTotals = <String, double>{};
    
    for (final transaction in transactions) {
      categoryTotals[transaction.category] =
          (categoryTotals[transaction.category] ?? 0) + transaction.amount.abs();
    }
    
    final sortedCategories = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Spending by Category'),
          
          ...sortedCategories.map((entry) {
            final percentage = (entry.value / categoryTotals.values.fold(0.0, (a, b) => a + b)) * 100;
            return _buildCategoryItem(
              'Category ${entry.key}', // In real app, would lookup category name
              entry.value,
              percentage,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildMonthlySummaryReport(List<Transaction> transactions) {
    final monthlyData = <String, Map<String, double>>{};
    
    for (final transaction in transactions) {
      final monthKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyData[monthKey] ??= {'income': 0, 'expenses': 0};
      
      if (transaction.amount > 0) {
        monthlyData[monthKey]!['income'] = monthlyData[monthKey]!['income']! + transaction.amount;
      } else {
        monthlyData[monthKey]!['expenses'] = monthlyData[monthKey]!['expenses']! + transaction.amount.abs();
      }
    }
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Monthly Summary'),
          
          ...monthlyData.entries.map((entry) {
            final income = entry.value['income']!;
            final expenses = entry.value['expenses']!;
            final net = income - expenses;
            
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatMonthYear(entry.key),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Income: \$${income.toStringAsFixed(2)}'),
                        Text('Expenses: \$${expenses.toStringAsFixed(2)}'),
                        Text(
                          'Net: \$${net.toStringAsFixed(2)}',
                          style: TextStyle(
                            color: net >= 0 ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildAccountPerformanceReport(List<Transaction> transactions, List<dynamic> accounts) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Account Performance'),
          
          ...accounts.map((account) {
            final accountTransactions = transactions.where((t) => t.accountId == account.id).toList();
            final balance = accountTransactions.fold(0.0, (sum, t) => sum + t.amount);
            
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                  child: Icon(
                    Icons.account_balance_wallet,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                title: Text(account.name),
                subtitle: Text('${accountTransactions.length} transactions'),
                trailing: Text(
                  '\$${balance.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: balance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildCashFlowReport(List<Transaction> transactions) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Cash Flow Analysis'),
          
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Cash Flow Chart\n(Interactive chart would be implemented here)',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetVsActualReport(List<Transaction> transactions) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Budget vs Actual'),
          
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Budget Comparison\n(Budget feature would be implemented here)',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, double amount, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '\$${amount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTransactionList(List<Transaction> transactions) {
    return Column(
      children: transactions.take(10).map((transaction) {
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: transaction.amount > 0 
              ? Colors.green.withValues(alpha: 0.2)
              : Colors.red.withValues(alpha: 0.2),
            child: Icon(
              transaction.amount > 0 ? Icons.add : Icons.remove,
              color: transaction.amount > 0 ? Colors.green : Colors.red,
            ),
          ),
          title: Text(transaction.description),
          subtitle: Text(_formatDate(transaction.date)),
          trailing: Text(
            '\$${transaction.amount.abs().toStringAsFixed(2)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: transaction.amount > 0 ? Colors.green : Colors.red,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCategoryItem(String categoryName, double amount, double percentage) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    categoryName,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey[300],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${amount.toStringAsFixed(2)}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Transaction> _filterTransactionsByDateRange(List<Transaction> transactions) {
    if (_selectedDateRange == null) return transactions;
    
    return transactions.where((transaction) {
      return transaction.date.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
             transaction.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
    }).toList();
  }

  void _updateDateRangeForPeriod(String period) {
    final now = DateTime.now();
    
    switch (period) {
      case 'This Week':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        _selectedDateRange = DateTimeRange(
          start: startOfWeek,
          end: startOfWeek.add(const Duration(days: 6)),
        );
        break;
      case 'This Month':
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0),
        );
        break;
      case 'Last Month':
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, now.month - 1, 1),
          end: DateTime(now.year, now.month, 0),
        );
        break;
      case 'This Quarter':
        final quarterStart = DateTime(now.year, ((now.month - 1) ~/ 3) * 3 + 1, 1);
        _selectedDateRange = DateTimeRange(
          start: quarterStart,
          end: DateTime(quarterStart.year, quarterStart.month + 3, 0),
        );
        break;
      case 'This Year':
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, 1, 1),
          end: DateTime(now.year, 12, 31),
        );
        break;
    }
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
        _selectedPeriod = 'Custom Range';
      });
    }
  }

  void _generateReport() {
    // Trigger report regeneration
    setState(() {});
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Report generated successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _exportReport() {
    // TODO: Implement report export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality coming soon'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatMonthYear(String monthKey) {
    final parts = monthKey.split('-');
    final year = parts[0];
    final month = int.parse(parts[1]);
    final monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${monthNames[month - 1]} $year';
  }
}
