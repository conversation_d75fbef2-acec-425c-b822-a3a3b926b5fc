import 'excel_worksheet.dart';

/// Model representing an Excel workbook with multiple worksheets
class ExcelWorkbook {
  final String fileName;
  final String filePath;
  final List<ExcelWorksheet> worksheets;
  final String activeWorksheet;
  final DateTime lastModified;
  final Map<String, dynamic> metadata;

  ExcelWorkbook({
    required this.fileName,
    required this.filePath,
    required this.worksheets,
    required this.activeWorksheet,
    DateTime? lastModified,
    Map<String, dynamic>? metadata,
  }) : lastModified = lastModified ?? DateTime.now(),
       metadata = metadata ?? const {};

  /// Get worksheet by name
  ExcelWorksheet? getWorksheet(String name) {
    try {
      return worksheets.firstWhere((ws) => ws.name == name);
    } catch (e) {
      return null;
    }
  }

  /// Get all worksheet names
  List<String> get worksheetNames => worksheets.map((ws) => ws.name).toList();

  /// Get total number of cells across all worksheets
  int get totalCells => worksheets.fold(0, (sum, ws) => sum + ws.cellCount);

  /// Get total number of formulas across all worksheets
  int get totalFormulas => worksheets.fold(0, (sum, ws) => sum + ws.formulaCount);

  /// Check if workbook has any data
  bool get hasData => worksheets.any((ws) => ws.hasData);

  /// Create a copy with updated values
  ExcelWorkbook copyWith({
    String? fileName,
    String? filePath,
    List<ExcelWorksheet>? worksheets,
    String? activeWorksheet,
    DateTime? lastModified,
    Map<String, dynamic>? metadata,
  }) {
    return ExcelWorkbook(
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      worksheets: worksheets ?? this.worksheets,
      activeWorksheet: activeWorksheet ?? this.activeWorksheet,
      lastModified: lastModified ?? this.lastModified,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'filePath': filePath,
      'worksheets': worksheets.map((ws) => ws.toJson()).toList(),
      'activeWorksheet': activeWorksheet,
      'lastModified': lastModified.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory ExcelWorkbook.fromJson(Map<String, dynamic> json) {
    return ExcelWorkbook(
      fileName: json['fileName'] ?? '',
      filePath: json['filePath'] ?? '',
      worksheets: (json['worksheets'] as List<dynamic>?)
          ?.map((ws) => ExcelWorksheet.fromJson(ws as Map<String, dynamic>))
          .toList() ?? [],
      activeWorksheet: json['activeWorksheet'] ?? '',
      lastModified: json['lastModified'] != null 
          ? DateTime.parse(json['lastModified'])
          : DateTime.now(),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Get workbook statistics
  Map<String, dynamic> getStatistics() {
    return {
      'fileName': fileName,
      'worksheetCount': worksheets.length,
      'totalCells': totalCells,
      'totalFormulas': totalFormulas,
      'hasData': hasData,
      'lastModified': lastModified,
      'fileSize': metadata['fileSize'] ?? 0,
      'worksheetNames': worksheetNames,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExcelWorkbook &&
        other.fileName == fileName &&
        other.filePath == filePath &&
        other.activeWorksheet == activeWorksheet;
  }

  @override
  int get hashCode {
    return Object.hash(fileName, filePath, activeWorksheet);
  }

  @override
  String toString() {
    return 'ExcelWorkbook(fileName: $fileName, worksheets: ${worksheets.length})';
  }
}

/// Model for Excel cell range selection
class ExcelRange {
  final String startCell;
  final String endCell;
  final String worksheetName;

  const ExcelRange({
    required this.startCell,
    required this.endCell,
    required this.worksheetName,
  });

  /// Get all cell addresses in the range
  List<String> getCellAddresses() {
    final startCoords = _parseCellAddress(startCell);
    final endCoords = _parseCellAddress(endCell);
    
    final minRow = startCoords['row']! < endCoords['row']! ? startCoords['row']! : endCoords['row']!;
    final maxRow = startCoords['row']! > endCoords['row']! ? startCoords['row']! : endCoords['row']!;
    final minCol = startCoords['col']! < endCoords['col']! ? startCoords['col']! : endCoords['col']!;
    final maxCol = startCoords['col']! > endCoords['col']! ? startCoords['col']! : endCoords['col']!;
    
    final addresses = <String>[];
    for (int row = minRow; row <= maxRow; row++) {
      for (int col = minCol; col <= maxCol; col++) {
        addresses.add(_getCellAddress(row, col));
      }
    }
    
    return addresses;
  }

  /// Parse cell address like "A1" to row/column coordinates
  Map<String, int> _parseCellAddress(String address) {
    final regex = RegExp(r'^([A-Z]+)(\d+)$');
    final match = regex.firstMatch(address);
    
    if (match != null) {
      final colStr = match.group(1)!;
      final rowStr = match.group(2)!;
      
      int col = 0;
      for (int i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.codeUnitAt(i) - 65 + 1);
      }
      
      return {
        'row': int.parse(rowStr),
        'col': col,
      };
    }
    
    return {'row': 1, 'col': 1};
  }

  /// Convert row/column coordinates to cell address
  String _getCellAddress(int row, int col) {
    String colStr = '';
    int tempCol = col;
    
    while (tempCol > 0) {
      tempCol--;
      colStr = String.fromCharCode(65 + (tempCol % 26)) + colStr;
      tempCol ~/= 26;
    }
    
    return '$colStr$row';
  }

  /// Get range string representation
  String get rangeString => '$startCell:$endCell';

  /// Get number of cells in range
  int get cellCount => getCellAddresses().length;

  @override
  String toString() {
    return 'ExcelRange($worksheetName!$rangeString)';
  }
}
