import 'package:flutter/foundation.dart';

/// Dhikr (remembrance) model
class Dhikr {
  final String id;
  final String textArabic;
  final String textTransliteration;
  final String textTranslation;
  final String category;
  final int recommendedCount;
  final String source; // Quran, Hadith, etc.
  final String benefits;
  final List<String> tags;
  final bool isFavorite;
  final int userCount;
  final DateTime? lastRecited;

  const Dhikr({
    required this.id,
    required this.textArabic,
    required this.textTransliteration,
    required this.textTranslation,
    required this.category,
    this.recommendedCount = 1,
    this.source = '',
    this.benefits = '',
    this.tags = const [],
    this.isFavorite = false,
    this.userCount = 0,
    this.lastRecited,
  });

  factory Dhikr.fromJson(Map<String, dynamic> json) {
    return Dhikr(
      id: json['id'] as String,
      textArabic: json['text_arabic'] as String,
      textTransliteration: json['text_transliteration'] as String? ?? '',
      textTranslation: json['text_translation'] as String? ?? '',
      category: json['category'] as String,
      recommendedCount: json['recommended_count'] as int? ?? 1,
      source: json['source'] as String? ?? '',
      benefits: json['benefits'] as String? ?? '',
      tags: (json['tags'] as List<dynamic>?)?.map((t) => t as String).toList() ?? [],
      isFavorite: json['is_favorite'] as bool? ?? false,
      userCount: json['user_count'] as int? ?? 0,
      lastRecited: json['last_recited'] != null 
          ? DateTime.parse(json['last_recited'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text_arabic': textArabic,
      'text_transliteration': textTransliteration,
      'text_translation': textTranslation,
      'category': category,
      'recommended_count': recommendedCount,
      'source': source,
      'benefits': benefits,
      'tags': tags,
      'is_favorite': isFavorite,
      'user_count': userCount,
      'last_recited': lastRecited?.toIso8601String(),
    };
  }

  Dhikr copyWith({
    String? id,
    String? textArabic,
    String? textTransliteration,
    String? textTranslation,
    String? category,
    int? recommendedCount,
    String? source,
    String? benefits,
    List<String>? tags,
    bool? isFavorite,
    int? userCount,
    DateTime? lastRecited,
  }) {
    return Dhikr(
      id: id ?? this.id,
      textArabic: textArabic ?? this.textArabic,
      textTransliteration: textTransliteration ?? this.textTransliteration,
      textTranslation: textTranslation ?? this.textTranslation,
      category: category ?? this.category,
      recommendedCount: recommendedCount ?? this.recommendedCount,
      source: source ?? this.source,
      benefits: benefits ?? this.benefits,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      userCount: userCount ?? this.userCount,
      lastRecited: lastRecited ?? this.lastRecited,
    );
  }
}

/// Dhikr category model
class DhikrCategory {
  final String id;
  final String name;
  final String nameArabic;
  final String description;
  final String icon;
  final String color;
  final int dhikrCount;
  final bool isDefault;

  const DhikrCategory({
    required this.id,
    required this.name,
    required this.nameArabic,
    required this.description,
    this.icon = 'prayer_beads',
    this.color = '#4CAF50',
    this.dhikrCount = 0,
    this.isDefault = false,
  });

  factory DhikrCategory.fromJson(Map<String, dynamic> json) {
    return DhikrCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      nameArabic: json['name_arabic'] as String? ?? '',
      description: json['description'] as String? ?? '',
      icon: json['icon'] as String? ?? 'prayer_beads',
      color: json['color'] as String? ?? '#4CAF50',
      dhikrCount: json['dhikr_count'] as int? ?? 0,
      isDefault: json['is_default'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_arabic': nameArabic,
      'description': description,
      'icon': icon,
      'color': color,
      'dhikr_count': dhikrCount,
      'is_default': isDefault,
    };
  }
}

/// Dhikr session model for tracking recitation sessions
class DhikrSession {
  final String id;
  final String dhikrId;
  final int targetCount;
  final int currentCount;
  final DateTime startTime;
  final DateTime? endTime;
  final bool isCompleted;
  final int totalDuration; // in seconds
  final List<DateTime> recitationTimes;

  const DhikrSession({
    required this.id,
    required this.dhikrId,
    required this.targetCount,
    this.currentCount = 0,
    required this.startTime,
    this.endTime,
    this.isCompleted = false,
    this.totalDuration = 0,
    this.recitationTimes = const [],
  });

  factory DhikrSession.fromJson(Map<String, dynamic> json) {
    return DhikrSession(
      id: json['id'] as String,
      dhikrId: json['dhikr_id'] as String,
      targetCount: json['target_count'] as int,
      currentCount: json['current_count'] as int? ?? 0,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time'] as String)
          : null,
      isCompleted: json['is_completed'] as bool? ?? false,
      totalDuration: json['total_duration'] as int? ?? 0,
      recitationTimes: (json['recitation_times'] as List<dynamic>?)
          ?.map((t) => DateTime.parse(t as String))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dhikr_id': dhikrId,
      'target_count': targetCount,
      'current_count': currentCount,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'is_completed': isCompleted,
      'total_duration': totalDuration,
      'recitation_times': recitationTimes.map((t) => t.toIso8601String()).toList(),
    };
  }

  DhikrSession copyWith({
    String? id,
    String? dhikrId,
    int? targetCount,
    int? currentCount,
    DateTime? startTime,
    DateTime? endTime,
    bool? isCompleted,
    int? totalDuration,
    List<DateTime>? recitationTimes,
  }) {
    return DhikrSession(
      id: id ?? this.id,
      dhikrId: dhikrId ?? this.dhikrId,
      targetCount: targetCount ?? this.targetCount,
      currentCount: currentCount ?? this.currentCount,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isCompleted: isCompleted ?? this.isCompleted,
      totalDuration: totalDuration ?? this.totalDuration,
      recitationTimes: recitationTimes ?? this.recitationTimes,
    );
  }

  double get progressPercentage => targetCount > 0 ? (currentCount / targetCount) * 100 : 0;
  
  Duration get sessionDuration => endTime != null 
      ? endTime!.difference(startTime)
      : DateTime.now().difference(startTime);
}

/// Dhikr statistics model
class DhikrStatistics {
  final int totalRecitations;
  final int totalSessions;
  final int totalDuration; // in seconds
  final int streakDays;
  final DateTime? lastRecitation;
  final Map<String, int> categoryStats;
  final Map<String, int> dailyStats; // date -> count
  final List<String> favoriteDhikrs;

  const DhikrStatistics({
    this.totalRecitations = 0,
    this.totalSessions = 0,
    this.totalDuration = 0,
    this.streakDays = 0,
    this.lastRecitation,
    this.categoryStats = const {},
    this.dailyStats = const {},
    this.favoriteDhikrs = const [],
  });

  factory DhikrStatistics.fromJson(Map<String, dynamic> json) {
    return DhikrStatistics(
      totalRecitations: json['total_recitations'] as int? ?? 0,
      totalSessions: json['total_sessions'] as int? ?? 0,
      totalDuration: json['total_duration'] as int? ?? 0,
      streakDays: json['streak_days'] as int? ?? 0,
      lastRecitation: json['last_recitation'] != null 
          ? DateTime.parse(json['last_recitation'] as String)
          : null,
      categoryStats: Map<String, int>.from(json['category_stats'] as Map? ?? {}),
      dailyStats: Map<String, int>.from(json['daily_stats'] as Map? ?? {}),
      favoriteDhikrs: (json['favorite_dhikrs'] as List<dynamic>?)
          ?.map((d) => d as String)
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_recitations': totalRecitations,
      'total_sessions': totalSessions,
      'total_duration': totalDuration,
      'streak_days': streakDays,
      'last_recitation': lastRecitation?.toIso8601String(),
      'category_stats': categoryStats,
      'daily_stats': dailyStats,
      'favorite_dhikrs': favoriteDhikrs,
    };
  }

  String get formattedDuration {
    final hours = totalDuration ~/ 3600;
    final minutes = (totalDuration % 3600) ~/ 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  double get averageSessionDuration => totalSessions > 0 ? totalDuration / totalSessions : 0;
}

/// Dhikr reminder model
class DhikrReminder {
  final String id;
  final String dhikrId;
  final String title;
  final String description;
  final DateTime scheduledTime;
  final List<int> repeatDays; // 1-7 for Monday-Sunday
  final bool isEnabled;
  final bool hasSound;
  final String soundPath;
  final bool hasVibration;

  const DhikrReminder({
    required this.id,
    required this.dhikrId,
    required this.title,
    this.description = '',
    required this.scheduledTime,
    this.repeatDays = const [],
    this.isEnabled = true,
    this.hasSound = true,
    this.soundPath = '',
    this.hasVibration = true,
  });

  factory DhikrReminder.fromJson(Map<String, dynamic> json) {
    return DhikrReminder(
      id: json['id'] as String,
      dhikrId: json['dhikr_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      scheduledTime: DateTime.parse(json['scheduled_time'] as String),
      repeatDays: (json['repeat_days'] as List<dynamic>?)
          ?.map((d) => d as int)
          .toList() ?? [],
      isEnabled: json['is_enabled'] as bool? ?? true,
      hasSound: json['has_sound'] as bool? ?? true,
      soundPath: json['sound_path'] as String? ?? '',
      hasVibration: json['has_vibration'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dhikr_id': dhikrId,
      'title': title,
      'description': description,
      'scheduled_time': scheduledTime.toIso8601String(),
      'repeat_days': repeatDays,
      'is_enabled': isEnabled,
      'has_sound': hasSound,
      'sound_path': soundPath,
      'has_vibration': hasVibration,
    };
  }

  bool get isRepeating => repeatDays.isNotEmpty;
  
  String get repeatDaysText {
    if (repeatDays.isEmpty) return 'Once';
    if (repeatDays.length == 7) return 'Daily';
    
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return repeatDays.map((day) => dayNames[day - 1]).join(', ');
  }
}
