import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Enhanced formula input widget with autocomplete and syntax highlighting
class FormulaInputWidget extends StatefulWidget {
  final String? initialFormula;
  final Function(String) onFormulaChanged;
  final Function(String)? onFormulaSubmitted;
  final VoidCallback? onCellPickerRequested;

  const FormulaInputWidget({
    super.key,
    this.initialFormula,
    required this.onFormulaChanged,
    this.onFormulaSubmitted,
    this.onCellPickerRequested,
  });

  @override
  State<FormulaInputWidget> createState() => _FormulaInputWidgetState();
}

class _FormulaInputWidgetState extends State<FormulaInputWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _showAutocomplete = false;
  List<FormulaFunction> _suggestions = [];
  String _currentInput = '';
  int _cursorPosition = 0;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialFormula ?? '');
    _focusNode = FocusNode();
    
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    final cursorPos = _controller.selection.baseOffset;
    
    setState(() {
      _currentInput = text;
      _cursorPosition = cursorPos;
    });
    
    widget.onFormulaChanged(text);
    
    // Show autocomplete when typing after '=' or function name
    if (text.startsWith('=') && cursorPos > 1) {
      _updateAutocomplete(text, cursorPos);
    } else {
      setState(() {
        _showAutocomplete = false;
      });
    }
  }

  void _updateAutocomplete(String text, int cursorPos) {
    final beforeCursor = text.substring(0, cursorPos);
    final lastWord = _getLastWord(beforeCursor);
    
    if (lastWord.isNotEmpty) {
      final suggestions = _getMatchingFunctions(lastWord);
      setState(() {
        _suggestions = suggestions;
        _showAutocomplete = suggestions.isNotEmpty;
      });
    } else {
      setState(() {
        _showAutocomplete = false;
      });
    }
  }

  String _getLastWord(String text) {
    final match = RegExp(r'[A-Z]*$').firstMatch(text);
    return match?.group(0) ?? '';
  }

  List<FormulaFunction> _getMatchingFunctions(String query) {
    return _allFunctions
        .where((func) => func.name.startsWith(query.toUpperCase()))
        .take(10)
        .toList();
  }

  void _insertSuggestion(FormulaFunction function) {
    final beforeCursor = _currentInput.substring(0, _cursorPosition);
    final afterCursor = _currentInput.substring(_cursorPosition);
    final lastWord = _getLastWord(beforeCursor);
    
    final newBeforeCursor = beforeCursor.substring(0, beforeCursor.length - lastWord.length);
    final insertion = '${function.name}(';
    final newText = '$newBeforeCursor$insertion$afterCursor';
    final newCursorPos = newBeforeCursor.length + insertion.length;
    
    _controller.text = newText;
    _controller.selection = TextSelection.collapsed(offset: newCursorPos);
    
    setState(() {
      _showAutocomplete = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Formula bar
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              // Formula indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
                child: Text(
                  'fx',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              
              // Formula input
              Expanded(
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 14,
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    hintText: 'Enter formula (e.g., =SUM(A1:A10))',
                  ),
                  onSubmitted: widget.onFormulaSubmitted,
                  inputFormatters: [
                    _FormulaInputFormatter(),
                  ],
                ),
              ),
              
              // Cell picker button
              IconButton(
                onPressed: widget.onCellPickerRequested,
                icon: const Icon(Icons.grid_on),
                tooltip: 'Select cells',
              ),
              
              // Help button
              IconButton(
                onPressed: _showHelp,
                icon: const Icon(Icons.help_outline),
                tooltip: 'Formula help',
              ),
            ],
          ),
        ),
        
        // Autocomplete suggestions
        if (_showAutocomplete) ...[
          const SizedBox(height: 4),
          _buildAutocompletePanel(),
        ],
        
        // Formula syntax help
        if (_currentInput.startsWith('=') && _currentInput.length > 1) ...[
          const SizedBox(height: 8),
          _buildSyntaxHelp(),
        ],
      ],
    );
  }

  Widget _buildAutocompletePanel() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _suggestions.length,
        itemBuilder: (context, index) {
          final function = _suggestions[index];
          return ListTile(
            dense: true,
            leading: Icon(
              _getFunctionIcon(function.category),
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: Text(
              function.name,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              function.description,
              style: Theme.of(context).textTheme.bodySmall,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Text(
              function.syntax,
              style: TextStyle(
                fontSize: 10,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontFamily: 'monospace',
              ),
            ),
            onTap: () => _insertSuggestion(function),
          );
        },
      ),
    );
  }

  Widget _buildSyntaxHelp() {
    final currentFunction = _getCurrentFunction();
    if (currentFunction == null) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getFunctionIcon(currentFunction.category),
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                currentFunction.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            currentFunction.syntax,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            currentFunction.description,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          if (currentFunction.example.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'Example: ${currentFunction.example}',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }

  FormulaFunction? _getCurrentFunction() {
    final beforeCursor = _currentInput.substring(0, _cursorPosition);
    final match = RegExp(r'=([A-Z]+)\([^)]*$').firstMatch(beforeCursor);
    if (match == null) return null;
    
    final functionName = match.group(1)!;
    return _allFunctions.firstWhere(
      (func) => func.name == functionName,
      orElse: () => FormulaFunction('', '', '', '', ''),
    );
  }

  IconData _getFunctionIcon(String category) {
    switch (category) {
      case 'Math':
        return Icons.calculate;
      case 'Logical':
        return Icons.psychology;
      case 'Text':
        return Icons.text_fields;
      case 'Date':
        return Icons.calendar_today;
      case 'Lookup':
        return Icons.search;
      case 'Statistical':
        return Icons.analytics;
      default:
        return Icons.functions;
    }
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => const FormulaHelpDialog(),
    );
  }
}

/// Formula input formatter for syntax highlighting
class _FormulaInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Basic validation - ensure formulas start with =
    if (newValue.text.isNotEmpty && !newValue.text.startsWith('=')) {
      if (oldValue.text.isEmpty) {
        return newValue.copyWith(text: '=${newValue.text}');
      }
    }
    
    return newValue;
  }
}

/// Formula function model
class FormulaFunction {
  final String name;
  final String category;
  final String syntax;
  final String description;
  final String example;

  const FormulaFunction(
    this.name,
    this.category,
    this.syntax,
    this.description,
    this.example,
  );
}

/// All available functions
final List<FormulaFunction> _allFunctions = [
  // Mathematical Functions
  FormulaFunction('SUM', 'Math', 'SUM(range)', 'Adds all numbers in a range', '=SUM(A1:A10)'),
  FormulaFunction('AVERAGE', 'Math', 'AVERAGE(range)', 'Calculates the average of numbers', '=AVERAGE(A1:A10)'),
  FormulaFunction('COUNT', 'Math', 'COUNT(range)', 'Counts cells containing numbers', '=COUNT(A1:A10)'),
  FormulaFunction('MAX', 'Math', 'MAX(range)', 'Returns the largest value', '=MAX(A1:A10)'),
  FormulaFunction('MIN', 'Math', 'MIN(range)', 'Returns the smallest value', '=MIN(A1:A10)'),
  FormulaFunction('ROUND', 'Math', 'ROUND(number, digits)', 'Rounds a number to specified digits', '=ROUND(A1, 2)'),
  FormulaFunction('ABS', 'Math', 'ABS(number)', 'Returns absolute value', '=ABS(A1)'),
  FormulaFunction('SQRT', 'Math', 'SQRT(number)', 'Returns square root', '=SQRT(A1)'),
  FormulaFunction('POWER', 'Math', 'POWER(base, exponent)', 'Returns base raised to exponent', '=POWER(2, 3)'),
  
  // Logical Functions
  FormulaFunction('IF', 'Logical', 'IF(condition, true_value, false_value)', 'Returns value based on condition', '=IF(A1>10, "High", "Low")'),
  FormulaFunction('AND', 'Logical', 'AND(condition1, condition2, ...)', 'Returns TRUE if all conditions are TRUE', '=AND(A1>0, B1<100)'),
  FormulaFunction('OR', 'Logical', 'OR(condition1, condition2, ...)', 'Returns TRUE if any condition is TRUE', '=OR(A1>0, B1<100)'),
  FormulaFunction('NOT', 'Logical', 'NOT(condition)', 'Returns opposite of logical value', '=NOT(A1>10)'),
  FormulaFunction('IFERROR', 'Logical', 'IFERROR(value, error_value)', 'Returns error_value if value is error', '=IFERROR(A1/B1, 0)'),
  
  // Text Functions
  FormulaFunction('CONCATENATE', 'Text', 'CONCATENATE(text1, text2, ...)', 'Joins text strings', '=CONCATENATE(A1, " ", B1)'),
  FormulaFunction('LEFT', 'Text', 'LEFT(text, num_chars)', 'Returns leftmost characters', '=LEFT(A1, 3)'),
  FormulaFunction('RIGHT', 'Text', 'RIGHT(text, num_chars)', 'Returns rightmost characters', '=RIGHT(A1, 3)'),
  FormulaFunction('MID', 'Text', 'MID(text, start, length)', 'Returns middle characters', '=MID(A1, 2, 5)'),
  FormulaFunction('LEN', 'Text', 'LEN(text)', 'Returns length of text', '=LEN(A1)'),
  FormulaFunction('UPPER', 'Text', 'UPPER(text)', 'Converts to uppercase', '=UPPER(A1)'),
  FormulaFunction('LOWER', 'Text', 'LOWER(text)', 'Converts to lowercase', '=LOWER(A1)'),
  
  // Date Functions
  FormulaFunction('TODAY', 'Date', 'TODAY()', 'Returns current date', '=TODAY()'),
  FormulaFunction('NOW', 'Date', 'NOW()', 'Returns current date and time', '=NOW()'),
  FormulaFunction('YEAR', 'Date', 'YEAR(date)', 'Returns year from date', '=YEAR(A1)'),
  FormulaFunction('MONTH', 'Date', 'MONTH(date)', 'Returns month from date', '=MONTH(A1)'),
  FormulaFunction('DAY', 'Date', 'DAY(date)', 'Returns day from date', '=DAY(A1)'),
];

/// Formula help dialog
class FormulaHelpDialog extends StatelessWidget {
  const FormulaHelpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.help_outline),
                const SizedBox(width: 8),
                Text(
                  'Formula Reference',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Expanded(
              child: DefaultTabController(
                length: 6,
                child: Column(
                  children: [
                    const TabBar(
                      isScrollable: true,
                      tabs: [
                        Tab(text: 'Math'),
                        Tab(text: 'Logical'),
                        Tab(text: 'Text'),
                        Tab(text: 'Date'),
                        Tab(text: 'Lookup'),
                        Tab(text: 'Statistical'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          _buildFunctionList('Math'),
                          _buildFunctionList('Logical'),
                          _buildFunctionList('Text'),
                          _buildFunctionList('Date'),
                          _buildFunctionList('Lookup'),
                          _buildFunctionList('Statistical'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionList(String category) {
    final functions = _allFunctions.where((f) => f.category == category).toList();
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: functions.length,
      itemBuilder: (context, index) {
        final function = functions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  function.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  function.syntax,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(height: 4),
                Text(function.description),
                if (function.example.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Example: ${function.example}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
