import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/conversion.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Conversion list provider
final conversionsProvider = StateNotifierProvider<ConversionNotifier, List<Conversion>>((ref) {
  return ConversionNotifier(ref);
});

class ConversionNotifier extends StateNotifier<List<Conversion>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  ConversionNotifier(this.ref) : super([]) {
    loadConversions();
  }

  Future<void> loadConversions() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final conversions = await _db.getAllConversions(userId: userProfile.id.toString());
      state = conversions;
    }
  }

  Future<void> addConversion(Conversion conversion) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      conversion.userId = userProfile.id.toString();
      final savedConversion = await _db.saveConversion(conversion);
      state = [savedConversion, ...state];
    }
  }

  Future<void> deleteConversion(int conversionId) async {
    await _db.deleteConversion(conversionId);
    state = state.where((c) => c.id != conversionId).toList();
  }

  List<Conversion> getConversionsByCategory(ConversionCategory category) {
    return state.where((conv) => conv.category == category).toList();
  }

  void clearConversions() {
    state = [];
  }
}

// Conversion service provider
final conversionServiceProvider = Provider<ConversionService>((ref) {
  return ConversionService();
});

class ConversionService {
  double convert(double value, ConversionCategory category, String fromUnit, String toUnit) {
    final fromUnitData = ConversionUnits.getUnit(category, fromUnit);
    final toUnitData = ConversionUnits.getUnit(category, toUnit);
    
    if (fromUnitData == null || toUnitData == null) {
      throw Exception('Invalid conversion units');
    }
    
    // Special handling for temperature
    if (category == ConversionCategory.temperature) {
      return _convertTemperature(value, fromUnitData, toUnitData);
    }
    
    // Standard conversion: convert to base unit, then to target unit
    final baseValue = value * fromUnitData.factor;
    return baseValue / toUnitData.factor;
  }
  
  double _convertTemperature(double value, ConversionUnit from, ConversionUnit to) {
    // Convert to Celsius first
    double celsius;
    if (from.symbol == '°C') {
      celsius = value;
    } else if (from.symbol == '°F') {
      celsius = (value - 32) / 1.8;
    } else if (from.symbol == 'K') {
      celsius = value - 273.15;
    } else {
      throw Exception('Unknown temperature unit: ${from.symbol}');
    }
    
    // Convert from Celsius to target
    if (to.symbol == '°C') {
      return celsius;
    } else if (to.symbol == '°F') {
      return celsius * 1.8 + 32;
    } else if (to.symbol == 'K') {
      return celsius + 273.15;
    } else {
      throw Exception('Unknown temperature unit: ${to.symbol}');
    }
  }
  
  String formatResult(double value, {int decimals = 6}) {
    // Remove trailing zeros and unnecessary decimal point
    String result = value.toStringAsFixed(decimals);
    result = result.replaceAll(RegExp(r'0*$'), '');
    result = result.replaceAll(RegExp(r'\.$'), '');
    return result;
  }
  
  List<ConversionUnit> getUnitsForCategory(ConversionCategory category) {
    return ConversionUnits.getUnitsForCategory(category);
  }
  
  List<ConversionCategory> getAllCategories() {
    return ConversionCategory.values;
  }
  
  String getCategoryDisplayName(ConversionCategory category) {
    switch (category) {
      case ConversionCategory.length:
        return 'Length';
      case ConversionCategory.weight:
        return 'Weight';
      case ConversionCategory.temperature:
        return 'Temperature';
      case ConversionCategory.volume:
        return 'Volume';
      case ConversionCategory.area:
        return 'Area';
      case ConversionCategory.speed:
        return 'Speed';
      case ConversionCategory.time:
        return 'Time';
      case ConversionCategory.energy:
        return 'Energy';
      case ConversionCategory.power:
        return 'Power';
      case ConversionCategory.pressure:
        return 'Pressure';
      case ConversionCategory.currency:
        return 'Currency';
    }
  }
}

// Conversions by category provider
final conversionsByCategoryProvider = Provider.family<List<Conversion>, ConversionCategory>((ref, category) {
  final conversions = ref.watch(conversionsProvider);
  return conversions.where((conv) => conv.category == category).toList();
});

// Conversion statistics provider
final conversionStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final conversions = ref.watch(conversionsProvider);
  
  final categoryStats = <ConversionCategory, int>{};
  for (final conv in conversions) {
    categoryStats[conv.category] = (categoryStats[conv.category] ?? 0) + 1;
  }
  
  final today = DateTime.now();
  final todayConversions = conversions.where((conv) {
    return conv.createdAt.year == today.year &&
           conv.createdAt.month == today.month &&
           conv.createdAt.day == today.day;
  }).length;
  
  return {
    'total': conversions.length,
    'today': todayConversions,
    'categories': categoryStats.length,
    'categoryStats': categoryStats,
    'mostUsedCategory': categoryStats.isNotEmpty 
        ? categoryStats.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : null,
  };
});

// Current conversion state provider
final currentConversionProvider = StateNotifierProvider<CurrentConversionNotifier, CurrentConversionState>((ref) {
  return CurrentConversionNotifier();
});

class CurrentConversionState {
  final ConversionCategory category;
  final String fromUnit;
  final String toUnit;
  final double inputValue;
  final double outputValue;
  final bool isValid;

  CurrentConversionState({
    this.category = ConversionCategory.length,
    this.fromUnit = 'm',
    this.toUnit = 'ft',
    this.inputValue = 0.0,
    this.outputValue = 0.0,
    this.isValid = true,
  });

  CurrentConversionState copyWith({
    ConversionCategory? category,
    String? fromUnit,
    String? toUnit,
    double? inputValue,
    double? outputValue,
    bool? isValid,
  }) {
    return CurrentConversionState(
      category: category ?? this.category,
      fromUnit: fromUnit ?? this.fromUnit,
      toUnit: toUnit ?? this.toUnit,
      inputValue: inputValue ?? this.inputValue,
      outputValue: outputValue ?? this.outputValue,
      isValid: isValid ?? this.isValid,
    );
  }
}

class CurrentConversionNotifier extends StateNotifier<CurrentConversionState> {
  CurrentConversionNotifier() : super(CurrentConversionState());

  void updateCategory(ConversionCategory category) {
    final units = ConversionUnits.getUnitsForCategory(category);
    if (units.isNotEmpty) {
      state = state.copyWith(
        category: category,
        fromUnit: units.first.symbol,
        toUnit: units.length > 1 ? units[1].symbol : units.first.symbol,
        inputValue: 0.0,
        outputValue: 0.0,
      );
    }
  }

  void updateFromUnit(String unit) {
    state = state.copyWith(fromUnit: unit);
    _recalculate();
  }

  void updateToUnit(String unit) {
    state = state.copyWith(toUnit: unit);
    _recalculate();
  }

  void updateInputValue(double value) {
    state = state.copyWith(inputValue: value);
    _recalculate();
  }

  void swapUnits() {
    state = state.copyWith(
      fromUnit: state.toUnit,
      toUnit: state.fromUnit,
      inputValue: state.outputValue,
    );
    _recalculate();
  }

  void _recalculate() {
    try {
      final service = ConversionService();
      final result = service.convert(
        state.inputValue,
        state.category,
        state.fromUnit,
        state.toUnit,
      );
      state = state.copyWith(outputValue: result, isValid: true);
    } catch (e) {
      state = state.copyWith(isValid: false);
    }
  }
}
