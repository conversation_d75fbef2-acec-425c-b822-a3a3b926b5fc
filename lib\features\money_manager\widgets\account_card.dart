import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/ui/unified_theme_system.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/account.dart';

/// Account card widget for displaying account information
class AccountCard extends ConsumerWidget {
  final Account account;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final bool showBalance;
  final bool compact;

  const AccountCard({
    super.key,
    required this.account,
    this.onTap,
    this.onEdit,
    this.showBalance = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: UnifiedThemeSystem.getCardElevation(ref),
      shape: RoundedRectangleBorder(
        borderRadius: UnifiedThemeSystem.getStandardBorderRadius(),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: UnifiedThemeSystem.getStandardBorderRadius(),
        child: Padding(
          padding: EdgeInsets.all(compact ? 12 : 16),
          child: compact ? _buildCompactLayout(context) : _buildFullLayout(context),
        ),
      ),
    );
  }

  Widget _buildFullLayout(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final colorScheme = ref.watch(colorSchemeProvider);
        final textTheme = ref.watch(textThemeProvider);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Account icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: account.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    account.icon,
                    color: account.color,
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // Account info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        account.name,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      if (account.description.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          account.description,
                          style: textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: account.type.defaultColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              account.type.displayName,
                              style: textTheme.bodySmall?.copyWith(
                                color: account.type.defaultColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            account.currency,
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Balance and actions
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (showBalance) ...[
                      Text(
                        account.formattedBalance,
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: account.isPositiveBalance
                              ? colorScheme.primary
                              : colorScheme.error,
                        ),
                      ),
                  if (account.hasBalanceChanged) ...[
                    const SizedBox(height: 2),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          account.balanceChange > 0 
                              ? Icons.trending_up 
                              : Icons.trending_down,
                          size: 16,
                          color: account.balanceChange > 0 
                              ? Colors.green 
                              : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          account.formattedBalanceChange,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: account.balanceChange > 0 
                                ? Colors.green 
                                : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
                if (onEdit != null) ...[
                  const SizedBox(height: 8),
                  IconButton(
                    onPressed: onEdit,
                    icon: const Icon(Icons.edit),
                    iconSize: 20,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ],
            ),
          ],
        ),
        
        // Additional info for credit cards or loans
        if (account.type == AccountType.credit || account.type == AccountType.loan) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  account.type == AccountType.credit 
                      ? Icons.credit_card 
                      : Icons.account_balance,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  account.type == AccountType.credit 
                      ? 'Credit Available: ${account.formattedBalance}'
                      : 'Outstanding: ${account.formattedBalance}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
      },
    );
  }

  Widget _buildCompactLayout(BuildContext context) {
    return Row(
      children: [
        // Account icon
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: account.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            account.icon,
            color: account.color,
            size: 20,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Account info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                account.name,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                account.type.displayName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        
        // Balance
        if (showBalance)
          Consumer(
            builder: (context, ref, child) {
              final colorScheme = ref.watch(colorSchemeProvider);
              final textTheme = ref.watch(textThemeProvider);

              return Text(
                account.formattedBalance,
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: account.isPositiveBalance
                      ? colorScheme.primary
                      : colorScheme.error,
                ),
              );
            },
          ),
      ],
    );
  }
}
