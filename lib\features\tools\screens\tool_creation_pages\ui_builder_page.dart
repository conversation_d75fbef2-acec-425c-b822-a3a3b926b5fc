import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../ui_builder/models/ui_component.dart';
import '../../ui_builder/providers/ui_builder_provider.dart';
import '../../ui_builder/widgets/components_panel.dart';
import '../../ui_builder/widgets/properties_panel.dart';
import '../../ui_builder/widgets/ui_canvas.dart';

/// Enhanced UI Builder page for creating user interfaces
class UIBuilderPage extends ConsumerStatefulWidget {
  const UIBuilderPage({super.key});

  @override
  ConsumerState<UIBuilderPage> createState() => _UIBuilderPageState();
}

class _UIBuilderPageState extends ConsumerState<UIBuilderPage> {

  @override
  Widget build(BuildContext context) {
    final uiBuilderState = ref.watch(uiBuilderProvider);
    
    return Column(
      children: [
        // Toolbar
        _buildToolbar(uiBuilderState),
        
        // Main content
        Expanded(
          child: Row(
            children: [
              // Components panel (left)
              ComponentsPanel(
                onComponentDragStart: _handleComponentDragStart,
                onComponentSelected: () => _handleComponentSelected(''),
              ),
              
              // Canvas (center)
              UICanvas(
                onComponentDropped: _handleComponentDropped,
                onComponentSelected: _handleComponentSelected,
                onComponentMoved: _handleComponentMoved,
                onComponentResized: _handleComponentResized,
              ),
              
              // Properties panel (right)
              PropertiesPanel(
                onComponentUpdated: _handleComponentUpdated,
                onCellPickerRequested: _showCellPickerDialog,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build toolbar
  Widget _buildToolbar(UIBuilderState state) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Component count
          Text(
            '${state.componentCount} components',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          
          const SizedBox(width: 16),
          
          // Layout mode
          Text(
            'Layout: ${state.layoutMode.name}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          
          const SizedBox(width: 16),
          
          // Grid size (if grid mode)
          if (state.layoutMode == LayoutMode.grid) ...[
            Text(
              'Grid: ${state.gridSize.toInt()}px',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 100,
              child: Slider(
                value: state.gridSize,
                min: 4,
                max: 32,
                divisions: 7,
                onChanged: (value) {
                  ref.read(uiBuilderProvider.notifier).setGridSize(value);
                },
              ),
            ),
          ],
          
          const Spacer(),
          
          // Preview mode indicator
          if (state.isPreviewMode)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'PREVIEW MODE',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          
          const SizedBox(width: 16),
          
          // Clear all
          ElevatedButton.icon(
            onPressed: _clearAll,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.errorContainer,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle component drag start
  void _handleComponentDragStart(UIComponent component) {
    // Optional: Show visual feedback during drag
  }

  /// Handle component dropped on canvas
  void _handleComponentDropped(UIComponent component, Offset position) {
    // Component is automatically added by the canvas
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added ${component.title} to canvas'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Handle component selection
  void _handleComponentSelected(String componentId) {
    // Selection is automatically handled by the provider
  }

  /// Handle component moved
  void _handleComponentMoved(String componentId, Offset newPosition) {
    // Position is automatically updated by the provider
  }

  /// Handle component resized
  void _handleComponentResized(String componentId, Size newSize) {
    // Size is automatically updated by the provider
  }

  /// Handle component updated
  void _handleComponentUpdated(String componentId, UIComponent updatedComponent) {
    // Component is automatically updated by the provider
  }

  /// Show cell picker dialog
  void _showCellPickerDialog() {
    showDialog(
      context: context,
      builder: (context) => _CellPickerDialog(
        onCellSelected: (cellAddress) {
          final selectedComponent = ref.read(uiBuilderProvider).selectedComponent;
          if (selectedComponent != null) {
            final updatedComponent = selectedComponent.copyWith(cellBinding: cellAddress);
            ref.read(uiBuilderProvider.notifier).updateComponent(
              selectedComponent.id,
              updatedComponent,
            );
          }
        },
      ),
    );
  }

  /// Clear all components
  void _clearAll() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Components'),
        content: const Text('Are you sure you want to remove all components? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(uiBuilderProvider.notifier).clearAll();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All components cleared'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

/// Cell picker dialog for selecting spreadsheet cells
class _CellPickerDialog extends ConsumerWidget {
  final Function(String cellAddress) onCellSelected;

  const _CellPickerDialog({required this.onCellSelected});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Cell',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            
            // Simple cell grid
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 10,
                  childAspectRatio: 1.5,
                ),
                itemCount: 100, // 10x10 grid
                itemBuilder: (context, index) {
                  final row = (index ~/ 10) + 1;
                  final col = (index % 10) + 1;
                  final cellAddress = '${String.fromCharCode(64 + col)}$row';
                  
                  return GestureDetector(
                    onTap: () {
                      onCellSelected(cellAddress);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      margin: const EdgeInsets.all(1),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        color: Colors.grey[50],
                      ),
                      child: Center(
                        child: Text(
                          cellAddress,
                          style: const TextStyle(fontSize: 10),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Close button
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
