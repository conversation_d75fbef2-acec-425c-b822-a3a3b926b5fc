import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/tool_builder_provider.dart';

/// Dashboard tab showing overview and statistics for Tool Builder
class ToolBuilderDashboardTab extends ConsumerStatefulWidget {
  const ToolBuilderDashboardTab({super.key});

  @override
  ConsumerState<ToolBuilderDashboardTab> createState() => _ToolBuilderDashboardTabState();
}

class _ToolBuilderDashboardTabState extends ConsumerState<ToolBuilderDashboardTab> {
  @override
  void initState() {
    super.initState();
    // Load dashboard data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(toolBuilderProvider.notifier).loadDashboardStats();
    });
  }

  @override
  Widget build(BuildContext context) {
    final toolBuilderState = ref.watch(toolBuilderProvider);
    
    return RefreshIndicator(
      onRefresh: () => ref.read(toolBuilderProvider.notifier).loadDashboardStats(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.build_circle,
                            size: 32,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Tools Builder Dashboard',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Create powerful custom tools with Excel-like formulas',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Statistics Section
            Text(
              'Statistics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (toolBuilderState.isLoading)
              const Center(child: CircularProgressIndicator())
            else if (toolBuilderState.error != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline, color: Colors.red),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          toolBuilderState.error!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: _getCrossAxisCount(context),
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.5,
                children: [
                  _buildStatsCard(
                    context,
                    'Total Tools',
                    toolBuilderState.stats['totalTools']?.toString() ?? '0',
                    Icons.build_circle_outlined,
                    Colors.blue,
                  ),
                  _buildStatsCard(
                    context,
                    'Active Tools',
                    toolBuilderState.stats['activeTools']?.toString() ?? '0',
                    Icons.play_circle_outline,
                    Colors.green,
                  ),
                  _buildStatsCard(
                    context,
                    'Templates Used',
                    toolBuilderState.stats['templatesUsed']?.toString() ?? '0',
                    Icons.description_outlined,
                    Colors.orange,
                  ),
                  _buildStatsCard(
                    context,
                    'Excel Imports',
                    toolBuilderState.stats['excelImports']?.toString() ?? '0',
                    Icons.table_chart_outlined,
                    Colors.purple,
                  ),
                ],
              ),
            
            const SizedBox(height: 32),

            // Recent Activity Section
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Card(
              child: Column(
                children: [
                  _buildActivityItem(
                    context,
                    'Created "Budget Calculator"',
                    '2 hours ago',
                    Icons.add_circle_outline,
                    Colors.green,
                  ),
                  const Divider(height: 1),
                  _buildActivityItem(
                    context,
                    'Imported Excel file "Financial Model"',
                    '1 day ago',
                    Icons.upload_file,
                    Colors.blue,
                  ),
                  const Divider(height: 1),
                  _buildActivityItem(
                    context,
                    'Modified "Loan Calculator"',
                    '3 days ago',
                    Icons.edit_outlined,
                    Colors.orange,
                  ),
                  const Divider(height: 1),
                  _buildActivityItem(
                    context,
                    'Used template "Investment Tracker"',
                    '1 week ago',
                    Icons.description_outlined,
                    Colors.purple,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),

            // Quick Actions Section
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2,
              children: [
                _buildQuickActionCard(
                  context,
                  'Create New Tool',
                  Icons.add_circle,
                  Colors.green,
                  () => _navigateToCreator(),
                ),
                _buildQuickActionCard(
                  context,
                  'Import Excel',
                  Icons.upload_file,
                  Colors.blue,
                  () => _navigateToImport(),
                ),
                _buildQuickActionCard(
                  context,
                  'Browse Templates',
                  Icons.description_outlined,
                  Colors.orange,
                  () => _navigateToTemplates(),
                ),
                _buildQuickActionCard(
                  context,
                  'View All Tools',
                  Icons.folder_open,
                  Colors.purple,
                  () => _navigateToSavedTools(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    String title,
    String time,
    IconData icon,
    Color color,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          size: 20,
          color: color,
        ),
      ),
      title: Text(title),
      subtitle: Text(time),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 24,
                  color: color,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    if (width > 600) return 2;
    return 1;
  }

  void _navigateToCreator() {
    // Navigate to Tools Builder tab (index 2)
    DefaultTabController.of(context).animateTo(2);
  }

  void _navigateToImport() {
    // Navigate to Tools Builder tab and trigger import
    DefaultTabController.of(context).animateTo(2);
  }

  void _navigateToTemplates() {
    // Navigate to Simple Tools tab
    DefaultTabController.of(context).animateTo(1);
  }

  void _navigateToSavedTools() {
    // Navigate to Saved Tools tab
    DefaultTabController.of(context).animateTo(3);
  }
}
