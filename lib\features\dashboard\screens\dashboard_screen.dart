import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/ui/master_layout.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../../../shared/widgets/responsive_layout.dart';
import '../../../shared/providers/user_provider.dart';
import '../../../core/navigation/app_router.dart';
import '../../../core/services/data_seeding_service.dart';
import '../../memo/providers/note_provider.dart' as memo;
import '../../memo/providers/todo_provider.dart';
import '../../memo/providers/voice_memo_provider.dart';
import '../../notes/providers/notes_provider.dart' as notes;
import '../../money/providers/account_provider.dart';
import '../../money/providers/transaction_provider.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    _checkUserProfile();
  }

  void _checkUserProfile() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final userProfile = ref.read(userProfileProvider);
      if (userProfile == null) {
        // Auto-create default user profile
        await ref.read(userProfileProvider.notifier).createProfile(
          name: 'ShadowSuite User',
          email: null,
        );

        // Get the created profile
        final newProfile = ref.read(userProfileProvider);
        if (newProfile != null) {
          // Seed sample data for the new user
          await DataSeedingService.seedSampleData(newProfile.id.toString());
          // Refresh providers to load the seeded data
          ref.invalidate(memo.notesProvider);
          ref.invalidate(todosProvider);
          ref.invalidate(voiceMemosProvider);
          ref.invalidate(notes.notesProvider);
          ref.invalidate(accountsProvider);
          ref.invalidate(transactionsProvider);
        }
      } else {
        // Seed sample data if this is an existing user without data
        await DataSeedingService.seedSampleData(userProfile.id.toString());
        // Refresh providers to load the seeded data
        ref.invalidate(memo.notesProvider);
        ref.invalidate(todosProvider);
        ref.invalidate(voiceMemosProvider);
        ref.invalidate(notes.notesProvider);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final userProfile = ref.watch(userProfileProvider);
    
    if (userProfile == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return MasterLayout(
      title: 'ShadowSuite',
      currentRoute: AppRoutes.dashboard,
      showBackButton: false,
      actions: [
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () {
            context.push(AppRoutes.settings);
          },
        ),
      ],
      body: Container(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            StandardCard(
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      userProfile.name.isNotEmpty
                          ? userProfile.name[0].toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back, ${userProfile.name}!',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Ready to be productive today?',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Quick Stats Section
            SectionHeader(
              title: 'Quick Overview',
              subtitle: 'Your productivity at a glance',
            ),
            
            Consumer(
              builder: (context, ref, child) {
                final memoNoteStats = ref.watch(memo.noteStatsProvider);
                final textNoteStats = ref.watch(notes.notesStatsProvider);
                final todoStats = ref.watch(todoStatsProvider);
                final voiceMemoStats = ref.watch(voiceMemoStatsProvider);
                final accountStats = ref.watch(accountStatsProvider);
                // final transactionStats = ref.watch(transactionStatsProvider);

                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: ResponsiveBreakpoints.getGridCrossAxisCount(
                    context,
                    mobileCount: 1,
                    tabletCount: 2,
                    desktopCount: 4,
                  ),
                  crossAxisSpacing: ResponsiveSpacing.md(context),
                  mainAxisSpacing: ResponsiveSpacing.md(context),
                  childAspectRatio: ResponsiveBreakpoints.getGridChildAspectRatio(
                    context,
                    mobileRatio: 1.5,
                    tabletRatio: 1.3,
                    desktopRatio: 1.2,
                  ),
                  children: [
                    _buildStatsCard(
                      context,
                      'Total Notes',
                      ((memoNoteStats['total'] ?? 0) + (textNoteStats['total'] ?? 0)).toString(),
                      Icons.note_alt_outlined,
                      Colors.blue,
                    ),
                    _buildStatsCard(
                      context,
                      'Active Todos',
                      todoStats['active'].toString(),
                      Icons.check_circle_outline,
                      Colors.green,
                    ),
                    _buildStatsCard(
                      context,
                      'Voice Memos',
                      voiceMemoStats['total'].toString(),
                      Icons.mic_outlined,
                      Colors.orange,
                    ),
                    _buildStatsCard(
                      context,
                      'Net Worth',
                      '\$${(accountStats['netWorth'] ?? 0.0).toStringAsFixed(0)}',
                      Icons.account_balance_wallet_outlined,
                      Colors.purple,
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 32),

            // Mini Apps Section
            SectionHeader(
              title: 'Mini Apps',
              subtitle: 'Explore all available productivity tools',
            ),
            
            // Compact Mini-App Cards with Quick Actions
            Column(
              children: [
                _buildCompactAppCard(
                  context,
                  'Memo Suite',
                  'Complete productivity suite: notes, todos, voice memos & rich text editing',
                  Icons.note_alt_outlined,
                  Colors.blue,
                  () => context.push(AppRoutes.memo),
                  quickActions: [
                    _buildQuickAction('Add Note', Icons.note_add, () => context.push('/memo/note/new')),
                    _buildQuickAction('Add Todo', Icons.add_task, () => context.push('/memo/todo/new')),
                    _buildQuickAction('Voice Memo', Icons.mic, () => context.push('/memo/voice/new')),
                  ],
                ),
                const SizedBox(height: 12),

                _buildCompactAppCard(
                  context,
                  'Islami',
                  'Dhikr, Quran reading, prayer times & Islamic remembrance',
                  Icons.mosque_outlined,
                  Colors.green,
                  () => context.push(AppRoutes.islami),
                  quickActions: [
                    _buildQuickAction('Dhikr', Icons.repeat, () => context.push('/islami')),
                    _buildQuickAction('Quran', Icons.menu_book, () => context.push('/islami')),
                    _buildQuickAction('Prayer Times', Icons.access_time, () => context.push('/islami')),
                  ],
                ),
                const SizedBox(height: 12),

                _buildCompactAppCard(
                  context,
                  'Money Flow',
                  'Financial tracking, budgets, accounts & transaction management',
                  Icons.account_balance_wallet_outlined,
                  Colors.purple,
                  () => context.push(AppRoutes.money),
                  quickActions: [
                    _buildQuickAction('Add Transaction', Icons.add, () => context.push('/money/transaction/new')),
                    _buildQuickAction('View Accounts', Icons.account_balance, () => context.push('/money/accounts')),
                    _buildQuickAction('Reports', Icons.assessment, () => context.push('/money/reports')),
                  ],
                ),
                const SizedBox(height: 12),

                _buildCompactAppCard(
                  context,
                  'Tools Builder',
                  'Custom calculators, converters, spreadsheets & tool creation',
                  Icons.build_outlined,
                  Colors.orange,
                  () => context.push(AppRoutes.tools),
                  quickActions: [
                    _buildQuickAction('Calculator', Icons.calculate, () => context.push('/tools/calculator')),
                    _buildQuickAction('Create Tool', Icons.add_circle, () => context.push('/tools/create')),
                    _buildQuickAction('Spreadsheet', Icons.table_chart, () => context.push('/tools/spreadsheet')),
                  ],
                ),
              ],
            ),
          ],
        ),
        ),
      ),
    );
  }

  Widget _buildStatsCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactAppCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap, {
    required List<Widget> quickActions,
  }) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      icon,
                      size: 24,
                      color: color,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Quick Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: quickActions,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickAction(String label, IconData icon, VoidCallback onTap) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }




}
