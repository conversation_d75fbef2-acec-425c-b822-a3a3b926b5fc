import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/quran_models.dart';

/// Service for loading and managing Quran data
class QuranService {
  static const String _quranFilePath = 'quran.txt';
  
  // Surah names mapping (first 20 surahs for demonstration)
  static const Map<int, Map<String, String>> _surahNames = {
    1: {
      'arabic': 'الفاتحة',
      'english': 'Al-Fatihah',
      'transliteration': 'Al-Faatiha',
      'type': 'Meccan',
    },
    2: {
      'arabic': 'البقرة',
      'english': 'Al-Baqarah',
      'transliteration': 'Al-Baqara',
      'type': 'Medinan',
    },
    3: {
      'arabic': 'آل عمران',
      'english': 'Ali \'Imran',
      'transliteration': 'Aal-i-Imraan',
      'type': 'Medinan',
    },
    4: {
      'arabic': 'النساء',
      'english': 'An-Nisa',
      'transliteration': 'An-Nisaa',
      'type': 'Medinan',
    },
    5: {
      'arabic': 'المائدة',
      'english': 'Al-Ma\'idah',
      'transliteration': 'Al-Maaida',
      'type': 'Medinan',
    },
    6: {
      'arabic': 'الأنعام',
      'english': 'Al-An\'am',
      'transliteration': 'Al-An\'aam',
      'type': 'Meccan',
    },
    7: {
      'arabic': 'الأعراف',
      'english': 'Al-A\'raf',
      'transliteration': 'Al-A\'raaf',
      'type': 'Meccan',
    },
    8: {
      'arabic': 'الأنفال',
      'english': 'Al-Anfal',
      'transliteration': 'Al-Anfaal',
      'type': 'Medinan',
    },
    9: {
      'arabic': 'التوبة',
      'english': 'At-Tawbah',
      'transliteration': 'At-Tawba',
      'type': 'Medinan',
    },
    10: {
      'arabic': 'يونس',
      'english': 'Yunus',
      'transliteration': 'Yunus',
      'type': 'Meccan',
    },
    11: {
      'arabic': 'هود',
      'english': 'Hud',
      'transliteration': 'Hud',
      'type': 'Meccan',
    },
    12: {
      'arabic': 'يوسف',
      'english': 'Yusuf',
      'transliteration': 'Yusuf',
      'type': 'Meccan',
    },
    13: {
      'arabic': 'الرعد',
      'english': 'Ar-Ra\'d',
      'transliteration': 'Ar-Ra\'d',
      'type': 'Medinan',
    },
    14: {
      'arabic': 'إبراهيم',
      'english': 'Ibrahim',
      'transliteration': 'Ibrahim',
      'type': 'Meccan',
    },
    15: {
      'arabic': 'الحجر',
      'english': 'Al-Hijr',
      'transliteration': 'Al-Hijr',
      'type': 'Meccan',
    },
    16: {
      'arabic': 'النحل',
      'english': 'An-Nahl',
      'transliteration': 'An-Nahl',
      'type': 'Meccan',
    },
    17: {
      'arabic': 'الإسراء',
      'english': 'Al-Isra',
      'transliteration': 'Al-Israa',
      'type': 'Meccan',
    },
    18: {
      'arabic': 'الكهف',
      'english': 'Al-Kahf',
      'transliteration': 'Al-Kahf',
      'type': 'Meccan',
    },
    19: {
      'arabic': 'مريم',
      'english': 'Maryam',
      'transliteration': 'Maryam',
      'type': 'Meccan',
    },
    20: {
      'arabic': 'طه',
      'english': 'Taha',
      'transliteration': 'Taa-Haa',
      'type': 'Meccan',
    },
  };

  /// Load Quran data from the text file
  static Future<List<QuranVerse>> loadQuranData() async {
    try {
      final String content = await rootBundle.loadString(_quranFilePath);
      final List<String> lines = content.split('\n');
      final List<QuranVerse> verses = [];

      for (final String line in lines) {
        if (line.trim().isEmpty) continue;
        
        final List<String> parts = line.split('|');
        if (parts.length >= 3) {
          final int surahNumber = int.tryParse(parts[0]) ?? 0;
          final int verseNumber = int.tryParse(parts[1]) ?? 0;
          final String arabicText = parts[2].trim();

          if (surahNumber > 0 && verseNumber > 0 && arabicText.isNotEmpty) {
            verses.add(QuranVerse(
              surahNumber: surahNumber,
              verseNumber: verseNumber,
              arabicText: arabicText,
            ));
          }
        }
      }

      return verses;
    } catch (e) {
      throw Exception('Failed to load Quran data: $e');
    }
  }

  /// Get all Surahs with their verses
  static Future<List<QuranSurah>> loadSurahs() async {
    final verses = await loadQuranData();
    final Map<int, List<QuranVerse>> surahVersesMap = {};

    // Group verses by Surah
    for (final verse in verses) {
      if (!surahVersesMap.containsKey(verse.surahNumber)) {
        surahVersesMap[verse.surahNumber] = [];
      }
      surahVersesMap[verse.surahNumber]!.add(verse);
    }

    // Create Surah objects
    final List<QuranSurah> surahs = [];
    for (final entry in surahVersesMap.entries) {
      final surahNumber = entry.key;
      final surahVerses = entry.value;
      final surahInfo = _surahNames[surahNumber];

      if (surahInfo != null) {
        surahs.add(QuranSurah(
          number: surahNumber,
          arabicName: surahInfo['arabic']!,
          englishName: surahInfo['english']!,
          transliteration: surahInfo['transliteration']!,
          verseCount: surahVerses.length,
          revelationType: surahInfo['type']!,
          verses: surahVerses,
        ));
      } else {
        // Fallback for surahs not in our mapping
        surahs.add(QuranSurah(
          number: surahNumber,
          arabicName: 'سورة $surahNumber',
          englishName: 'Surah $surahNumber',
          transliteration: 'Surah $surahNumber',
          verseCount: surahVerses.length,
          revelationType: 'Unknown',
          verses: surahVerses,
        ));
      }
    }

    // Sort by Surah number
    surahs.sort((a, b) => a.number.compareTo(b.number));
    return surahs;
  }

  /// Get a specific Surah by number
  static Future<QuranSurah?> getSurah(int surahNumber) async {
    final surahs = await loadSurahs();
    try {
      return surahs.firstWhere((surah) => surah.number == surahNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get a specific verse
  static Future<QuranVerse?> getVerse(int surahNumber, int verseNumber) async {
    final surah = await getSurah(surahNumber);
    if (surah == null) return null;
    
    try {
      return surah.verses.firstWhere((verse) => verse.verseNumber == verseNumber);
    } catch (e) {
      return null;
    }
  }

  /// Search verses by Arabic text
  static Future<List<QuranSearchResult>> searchVerses(String query) async {
    if (query.trim().isEmpty) return [];
    
    final verses = await loadQuranData();
    final List<QuranSearchResult> results = [];
    final String normalizedQuery = query.trim().toLowerCase();

    for (final verse in verses) {
      final String normalizedText = verse.arabicText.toLowerCase();
      final int matchIndex = normalizedText.indexOf(normalizedQuery);
      
      if (matchIndex != -1) {
        results.add(QuranSearchResult(
          verse: verse,
          matchedText: query,
          matchStart: matchIndex,
          matchEnd: matchIndex + query.length,
          relevanceScore: _calculateRelevanceScore(verse.arabicText, query, matchIndex),
        ));
      }
    }

    // Sort by relevance score (highest first)
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    return results;
  }

  /// Search Surahs by name
  static Future<List<QuranSurah>> searchSurahs(String query) async {
    if (query.trim().isEmpty) return [];
    
    final surahs = await loadSurahs();
    final String normalizedQuery = query.trim().toLowerCase();
    
    return surahs.where((surah) {
      return surah.englishName.toLowerCase().contains(normalizedQuery) ||
             surah.transliteration.toLowerCase().contains(normalizedQuery) ||
             surah.arabicName.contains(query.trim()) ||
             surah.number.toString() == query.trim();
    }).toList();
  }

  /// Calculate relevance score for search results
  static double _calculateRelevanceScore(String text, String query, int matchIndex) {
    double score = 1.0;
    
    // Boost score if match is at the beginning
    if (matchIndex == 0) {
      score += 0.5;
    }
    
    // Boost score for shorter verses (more specific matches)
    if (text.length < 100) {
      score += 0.3;
    } else if (text.length < 200) {
      score += 0.1;
    }
    
    // Boost score for exact word matches
    final words = text.split(' ');
    final queryWords = query.split(' ');
    int exactMatches = 0;
    
    for (final queryWord in queryWords) {
      if (words.any((word) => word.toLowerCase() == queryWord.toLowerCase())) {
        exactMatches++;
      }
    }
    
    score += (exactMatches / queryWords.length) * 0.5;
    
    return score;
  }

  /// Get verse count for a specific Surah
  static Future<int> getSurahVerseCount(int surahNumber) async {
    final surah = await getSurah(surahNumber);
    return surah?.verseCount ?? 0;
  }

  /// Get total verse count in the Quran
  static Future<int> getTotalVerseCount() async {
    final verses = await loadQuranData();
    return verses.length;
  }

  /// Get total Surah count
  static Future<int> getTotalSurahCount() async {
    final surahs = await loadSurahs();
    return surahs.length;
  }
}
