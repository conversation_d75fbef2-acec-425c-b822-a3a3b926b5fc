import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quran_models.dart';
import '../services/quran_service.dart';


// Enhanced Quran service provider
final quranServiceProvider = Provider<QuranService>((ref) {
  return QuranService.instance;
});

// Audio service removed - handled by separate audio providers if needed

// Quran data loading provider
final quranDataLoadingProvider = FutureProvider<bool>((ref) async {
  final service = ref.read(quranServiceProvider);
  await service.loadQuranData();
  return service.isLoaded;
});

// All surahs provider
final allSurahsProvider = Provider<List<Surah>>((ref) {
  final service = ref.read(quranServiceProvider);
  ref.watch(quranDataLoadingProvider); // Ensure data is loaded
  return service.surahs;
});

// Verses for surah provider
final versesForSurahProvider = Provider.family<List<Verse>, int>((ref, surahNumber) {
  final service = ref.read(quranServiceProvider);
  ref.watch(quranDataLoadingProvider); // Ensure data is loaded
  return service.getVersesForSurah(surahNumber);
});

// Enhanced Quran bookmarks provider
final quranBookmarksProvider = StateNotifierProvider<QuranBookmarkNotifier, List<QuranBookmark>>((ref) {
  return QuranBookmarkNotifier(ref);
});

class QuranBookmarkNotifier extends StateNotifier<List<QuranBookmark>> {
  final Ref ref;

  QuranBookmarkNotifier(this.ref) : super([]) {
    loadBookmarks();
  }

  Future<void> loadBookmarks() async {
    // For now, use in-memory storage
    // In a full implementation, this would load from Isar database
    state = [];
  }

  Future<void> addBookmark(QuranBookmark bookmark) async {
    state = [...state, bookmark];
  }

  Future<void> updateBookmark(QuranBookmark bookmark) async {
    state = state.map((b) =>
      b.surahNumber == bookmark.surahNumber && b.verseNumber == bookmark.verseNumber
        ? bookmark
        : b
    ).toList();
  }

  Future<void> deleteBookmark(int surahNumber, int verseNumber) async {
    state = state.where((b) =>
      !(b.surahNumber == surahNumber && b.verseNumber == verseNumber)
    ).toList();
  }

  bool isBookmarked(int surahNumber, int verseNumber) {
    return state.any((bookmark) =>
      bookmark.surahNumber == surahNumber && bookmark.verseNumber == verseNumber
    );
  }

  QuranBookmark? getBookmark(int surahNumber, int verseNumber) {
    try {
      return state.firstWhere((bookmark) =>
        bookmark.surahNumber == surahNumber && bookmark.verseNumber == verseNumber
      );
    } catch (e) {
      return null;
    }
  }
}

// Reading progress provider
final readingProgressProvider = StateProvider<ReadingProgress?>((ref) => null);

// Current reading position provider
final currentReadingPositionProvider = StateProvider<Map<String, int>>((ref) {
  return {'surah': 1, 'verse': 1};
});

// Quran search provider
final quranSearchProvider = StateProvider<String>((ref) => '');

// Search results provider
final quranSearchResultsProvider = FutureProvider<List<SearchResult>>((ref) async {
  final searchQuery = ref.watch(quranSearchProvider);
  final service = ref.read(quranServiceProvider);

  if (searchQuery.isEmpty) {
    return [];
  }

  return service.searchVerses(searchQuery);
});

// Quran statistics provider
final quranStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final bookmarks = ref.watch(quranBookmarksProvider);
  final service = ref.read(quranServiceProvider);

  return {
    'totalBookmarks': bookmarks.length,
    'totalSurahs': service.surahs.length,
    'totalVerses': service.surahs.fold(0, (total, surah) => total + surah.verses.length),
    'isLoaded': service.isLoaded,
  };
});

// Audio providers removed - can be added back when audio service is implemented

// Random verse provider
final randomVerseProvider = FutureProvider<Verse?>((ref) async {
  final service = ref.read(quranServiceProvider);
  return service.getRandomVerse();
});

// Specific verse provider
final specificVerseProvider = Provider.family<Verse?, Map<String, int>>((ref, params) {
  final service = ref.read(quranServiceProvider);
  final surahNumber = params['surah'] ?? 1;
  final verseNumber = params['verse'] ?? 1;
  return service.getVerse(surahNumber, verseNumber);
});
