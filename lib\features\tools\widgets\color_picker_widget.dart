import 'package:flutter/material.dart';

/// Color picker widget for component styling
class ColorPickerWidget extends StatefulWidget {
  final String label;
  final String? currentColor;
  final Function(String) onColorChanged;

  const ColorPickerWidget({
    super.key,
    required this.label,
    this.currentColor,
    required this.onColorChanged,
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> {
  late TextEditingController _colorController;
  Color _selectedColor = Colors.blue;

  @override
  void initState() {
    super.initState();
    _colorController = TextEditingController();
    _updateFromCurrentColor();
  }

  @override
  void didUpdateWidget(ColorPickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentColor != oldWidget.currentColor) {
      _updateFromCurrentColor();
    }
  }

  void _updateFromCurrentColor() {
    if (widget.currentColor != null && widget.currentColor!.isNotEmpty) {
      try {
        _selectedColor = _colorFromHex(widget.currentColor!);
        _colorController.text = widget.currentColor!;
      } catch (e) {
        _selectedColor = Colors.blue;
        _colorController.text = '#2196F3';
      }
    } else {
      _selectedColor = Colors.blue;
      _colorController.text = '#2196F3';
    }
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // Color preview
            GestureDetector(
              onTap: _showColorPicker,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _selectedColor,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _selectedColor.computeLuminance() > 0.5
                    ? const Icon(Icons.palette, color: Colors.black54)
                    : const Icon(Icons.palette, color: Colors.white70),
              ),
            ),
            const SizedBox(width: 12),
            
            // Hex input field
            Expanded(
              child: TextField(
                controller: _colorController,
                decoration: InputDecoration(
                  labelText: 'Hex Color',
                  hintText: '#RRGGBB',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  prefixText: '#',
                ),
                onChanged: _onHexChanged,
                maxLength: 7,
                buildCounter: (context, {required currentLength, required isFocused, maxLength}) => null,
              ),
            ),
            
            const SizedBox(width: 8),
            
            // Clear button
            IconButton(
              onPressed: _clearColor,
              icon: const Icon(Icons.clear),
              tooltip: 'Clear color',
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Preset colors
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _buildPresetColors(),
        ),
      ],
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Choose ${widget.label}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Material colors
              _buildMaterialColorPicker(),
              const SizedBox(height: 16),
              
              // Custom color input
              TextField(
                controller: _colorController,
                decoration: const InputDecoration(
                  labelText: 'Custom Hex Color',
                  hintText: '#RRGGBB',
                  border: OutlineInputBorder(),
                ),
                onChanged: _onHexChanged,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.onColorChanged(_colorToHex(_selectedColor));
              Navigator.of(context).pop();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  Widget _buildMaterialColorPicker() {
    final materialColors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
      Colors.black,
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: materialColors.length,
      itemBuilder: (context, index) {
        final color = materialColors[index];
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedColor = color;
              _colorController.text = _colorToHex(color);
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: _selectedColor == color
                  ? Border.all(color: Colors.white, width: 3)
                  : Border.all(color: Colors.grey.shade300),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildPresetColors() {
    final presetColors = [
      Colors.transparent,
      Colors.white,
      Colors.black,
      Colors.grey,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    return presetColors.map((color) {
      return GestureDetector(
        onTap: () {
          setState(() {
            _selectedColor = color;
            _colorController.text = _colorToHex(color);
          });
          widget.onColorChanged(_colorToHex(color));
        },
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: color,
            border: Border.all(
              color: Theme.of(context).colorScheme.outline,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(6),
          ),
          child: color == Colors.transparent
              ? const Icon(Icons.block, color: Colors.red, size: 16)
              : null,
        ),
      );
    }).toList();
  }

  void _onHexChanged(String value) {
    try {
      final cleanValue = value.replaceAll('#', '');
      if (cleanValue.length == 6) {
        final color = _colorFromHex('#$cleanValue');
        setState(() {
          _selectedColor = color;
        });
        widget.onColorChanged('#$cleanValue');
      }
    } catch (e) {
      // Invalid color format, ignore
    }
  }

  void _clearColor() {
    setState(() {
      _selectedColor = Colors.transparent;
      _colorController.text = '';
    });
    widget.onColorChanged('');
  }

  Color _colorFromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  String _colorToHex(Color color) {
    if (color == Colors.transparent) return '';
    return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2)}';
  }
}
