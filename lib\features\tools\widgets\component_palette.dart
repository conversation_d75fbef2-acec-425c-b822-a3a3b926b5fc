import 'package:flutter/material.dart';
import '../models/ui_component.dart';

/// Enhanced component palette with categorized components and search
class ComponentPalette extends StatefulWidget {
  final Function(ComponentType) onComponentSelected;
  final String currentTheme;

  const ComponentPalette({
    super.key,
    required this.onComponentSelected,
    this.currentTheme = 'material',
  });

  @override
  State<ComponentPalette> createState() => _ComponentPaletteState();
}

class _ComponentPaletteState extends State<ComponentPalette>
    with TickerProviderStateMixin {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'All';
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  final Map<String, List<ComponentDefinition>> _componentCategories = {
    'Input': [
      ComponentDefinition(
        type: ComponentType.textField,
        name: 'Text Field',
        icon: Icons.text_fields,
        description: 'Single line text input',
        color: Colors.blue,
      ),
      ComponentDefinition(
        type: ComponentType.textArea,
        name: 'Text Area',
        icon: Icons.notes,
        description: 'Multi-line text input',
        color: Colors.blue,
      ),
      ComponentDefinition(
        type: ComponentType.dropdown,
        name: 'Dropdown',
        icon: Icons.arrow_drop_down_circle,
        description: 'Selection dropdown menu',
        color: Colors.indigo,
      ),
      ComponentDefinition(
        type: ComponentType.slider,
        name: 'Slider',
        icon: Icons.tune,
        description: 'Range slider input',
        color: Colors.purple,
      ),
      ComponentDefinition(
        type: ComponentType.datePicker,
        name: 'Date Picker',
        icon: Icons.calendar_today,
        description: 'Date selection input',
        color: Colors.green,
      ),
      ComponentDefinition(
        type: ComponentType.colorPicker,
        name: 'Color Picker',
        icon: Icons.palette,
        description: 'Color selection input',
        color: Colors.orange,
      ),
    ],
    'Output': [
      ComponentDefinition(
        type: ComponentType.outputField,
        name: 'Output Field',
        icon: Icons.output,
        description: 'Display calculated values',
        color: Colors.teal,
      ),
      ComponentDefinition(
        type: ComponentType.label,
        name: 'Label',
        icon: Icons.label,
        description: 'Static text display',
        color: Colors.grey,
      ),
      ComponentDefinition(
        type: ComponentType.progressBar,
        name: 'Progress Bar',
        icon: Icons.linear_scale,
        description: 'Progress indicator',
        color: Colors.lightGreen,
      ),
      ComponentDefinition(
        type: ComponentType.gauge,
        name: 'Gauge',
        icon: Icons.speed,
        description: 'Circular gauge display',
        color: Colors.amber,
      ),
    ],
    'Charts': [
      ComponentDefinition(
        type: ComponentType.lineChart,
        name: 'Line Chart',
        icon: Icons.show_chart,
        description: 'Line graph visualization',
        color: Colors.blue,
      ),
      ComponentDefinition(
        type: ComponentType.barChart,
        name: 'Bar Chart',
        icon: Icons.bar_chart,
        description: 'Bar graph visualization',
        color: Colors.green,
      ),
      ComponentDefinition(
        type: ComponentType.pieChart,
        name: 'Pie Chart',
        icon: Icons.pie_chart,
        description: 'Pie chart visualization',
        color: Colors.orange,
      ),
      ComponentDefinition(
        type: ComponentType.scatterChart,
        name: 'Scatter Plot',
        icon: Icons.scatter_plot,
        description: 'Scatter plot visualization',
        color: Colors.purple,
      ),
    ],
    'Layout': [
      ComponentDefinition(
        type: ComponentType.container,
        name: 'Container',
        icon: Icons.crop_square,
        description: 'Layout container',
        color: Colors.blueGrey,
      ),
      ComponentDefinition(
        type: ComponentType.card,
        name: 'Card',
        icon: Icons.credit_card,
        description: 'Material card container',
        color: Colors.cyan,
      ),
      ComponentDefinition(
        type: ComponentType.divider,
        name: 'Divider',
        icon: Icons.horizontal_rule,
        description: 'Visual separator',
        color: Colors.grey,
      ),
      ComponentDefinition(
        type: ComponentType.spacer,
        name: 'Spacer',
        icon: Icons.space_bar,
        description: 'Flexible spacing',
        color: Colors.transparent,
      ),
    ],
    'Actions': [
      ComponentDefinition(
        type: ComponentType.button,
        name: 'Button',
        icon: Icons.smart_button,
        description: 'Action button',
        color: Colors.blue,
      ),
      ComponentDefinition(
        type: ComponentType.iconButton,
        name: 'Icon Button',
        icon: Icons.radio_button_unchecked,
        description: 'Icon-only button',
        color: Colors.indigo,
      ),
      ComponentDefinition(
        type: ComponentType.checkbox,
        name: 'Checkbox',
        icon: Icons.check_box,
        description: 'Boolean checkbox',
        color: Colors.green,
      ),
      ComponentDefinition(
        type: ComponentType.toggle,
        name: 'Toggle Switch',
        icon: Icons.toggle_on,
        description: 'Boolean toggle switch',
        color: Colors.orange,
      ),
      ComponentDefinition(
        type: ComponentType.radioButton,
        name: 'Radio Button',
        icon: Icons.radio_button_checked,
        description: 'Single selection option',
        color: Colors.purple,
      ),
    ],
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
    });
  }

  List<ComponentDefinition> _getFilteredComponents() {
    List<ComponentDefinition> allComponents = [];
    
    if (_selectedCategory == 'All') {
      for (final category in _componentCategories.values) {
        allComponents.addAll(category);
      }
    } else {
      allComponents = _componentCategories[_selectedCategory] ?? [];
    }

    if (_searchQuery.isNotEmpty) {
      allComponents = allComponents.where((component) {
        return component.name.toLowerCase().contains(_searchQuery) ||
               component.description.toLowerCase().contains(_searchQuery);
      }).toList();
    }

    return allComponents;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildSearchBar(),
          _buildCategoryTabs(),
          Expanded(child: _buildComponentList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.widgets,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            'Component Palette',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search components...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }

  Widget _buildCategoryTabs() {
    final categories = ['All', ..._componentCategories.keys];
    
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = _selectedCategory == category;
          
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              backgroundColor: isSelected 
                  ? Theme.of(context).colorScheme.primary
                  : null,
              labelStyle: TextStyle(
                color: isSelected 
                    ? Theme.of(context).colorScheme.onPrimary
                    : null,
                fontWeight: isSelected ? FontWeight.bold : null,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildComponentList() {
    final components = _getFilteredComponents();
    
    if (components.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No components found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or category filter',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: components.length,
      itemBuilder: (context, index) {
        final component = components[index];
        return _buildComponentTile(component);
      },
    );
  }

  Widget _buildComponentTile(ComponentDefinition component) {
    return Draggable<ComponentType>(
      data: component.type,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: component.color,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                component.icon,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                component.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: _buildComponentCard(component),
      ),
      child: _buildComponentCard(component),
    );
  }

  Widget _buildComponentCard(ComponentDefinition component) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      child: InkWell(
        onTap: () {
          _animationController.forward().then((_) {
            _animationController.reverse();
          });
          widget.onComponentSelected(component.type);
        },
        borderRadius: BorderRadius.circular(8),
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: component.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    component.icon,
                    color: component.color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        component.name,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        component.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.drag_indicator,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Component definition for the palette
class ComponentDefinition {
  final ComponentType type;
  final String name;
  final IconData icon;
  final String description;
  final Color color;

  const ComponentDefinition({
    required this.type,
    required this.name,
    required this.icon,
    required this.description,
    required this.color,
  });
}
