import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/ui/master_layout.dart';
import '../../../core/navigation/app_router.dart';
import '../models/todo.dart';
import '../providers/note_provider.dart';
import '../providers/todo_provider.dart';
import '../providers/voice_memo_provider.dart';
import 'voice_memo_recorder_screen.dart';

class MemoDashboardScreen extends ConsumerStatefulWidget {
  const MemoDashboardScreen({super.key});

  @override
  ConsumerState<MemoDashboardScreen> createState() => _MemoDashboardScreenState();
}

class _MemoDashboardScreenState extends ConsumerState<MemoDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MasterLayout(
      title: 'Memo Suite',
      currentRoute: AppRoutes.memo,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            // TODO: Implement search
          },
        ),
      ],
      body: Scaffold(
        appBar: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.note_alt), text: 'Notes'),
            Tab(icon: Icon(Icons.check_circle), text: 'Todos'),
            Tab(icon: Icon(Icons.mic), text: 'Voice Memos'),
          ],
        ),
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildNotesTab(),
            _buildTodosTab(),
            _buildVoiceMemosTab(),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            switch (_tabController.index) {
              case 0:
                context.push(AppRoutes.noteEditor);
                break;
              case 1:
                context.push(AppRoutes.todoEditor);
                break;
              case 2:
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const VoiceMemoRecorderScreen(),
                  ),
                );
                break;
            }
          },
          child: Icon(_tabController.index == 2 ? Icons.mic : Icons.add),
        ),
      ),
    );
  }

  Widget _buildNotesTab() {
    final notes = ref.watch(notesProvider);
    final filteredNotes = ref.watch(filteredNotesProvider);

    if (notes.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.note_alt_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No notes yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Tap + to create your first note',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredNotes.length,
      itemBuilder: (context, index) {
        final note = filteredNotes[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(
              note.title.isEmpty ? 'Untitled' : note.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (note.content.isNotEmpty)
                  Text(
                    note.content,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    if (note.isPinned)
                      const Icon(Icons.push_pin, size: 16, color: Colors.orange),
                    if (note.isFavorite)
                      const Icon(Icons.favorite, size: 16, color: Colors.red),
                    if (note.tags.isNotEmpty) ...[
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          note.tags.join(', '),
                          style: Theme.of(context).textTheme.bodySmall,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
            onTap: () {
              context.push('${AppRoutes.noteEditor}?id=${note.id}');
            },
          ),
        );
      },
    );
  }

  Widget _buildTodosTab() {
    final activeTodos = ref.watch(activeTodosProvider);
    final completedTodos = ref.watch(completedTodosProvider);

    if (activeTodos.isEmpty && completedTodos.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No todos yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Tap + to create your first todo',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Active Todos Section
          if (activeTodos.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Active Tasks (${activeTodos.length})',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                if (completedTodos.isNotEmpty)
                  TextButton.icon(
                    onPressed: () => _showClearCompletedDialog(),
                    icon: const Icon(Icons.clear_all, size: 18),
                    label: const Text('Clear Completed'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            ...activeTodos.map((todo) => _buildTodoCard(todo, false)),
            const SizedBox(height: 24),
          ],

          // Completed Todos Section
          if (completedTodos.isNotEmpty) ...[
            ExpansionTile(
              title: Text(
                'Completed Tasks (${completedTodos.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
              leading: const Icon(Icons.check_circle, color: Colors.green),
              initiallyExpanded: false,
              children: [
                ...completedTodos.map((todo) => _buildTodoCard(todo, true)),
                const SizedBox(height: 8),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTodoCard(Todo todo, bool isInCompletedSection) {
    final isCompleted = todo.isCompleted;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Checkbox(
          value: isCompleted,
          onChanged: (value) {
            ref.read(todosProvider.notifier).toggleCompletion(todo.id);
          },
        ),
        title: Text(
          todo.title,
          style: TextStyle(
            decoration: isCompleted ? TextDecoration.lineThrough : null,
            color: isCompleted ? Colors.grey : null,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (todo.description.isNotEmpty)
              Text(
                todo.description,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            if (todo.dueDate != null)
              Text(
                'Due: ${todo.dueDate!.day}/${todo.dueDate!.month}/${todo.dueDate!.year}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            if (isCompleted && todo.completedAt != null)
              Text(
                'Completed: ${todo.completedAt!.day}/${todo.completedAt!.month}/${todo.completedAt!.year}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                ),
              ),
          ],
        ),
        trailing: isInCompletedSection
            ? PopupMenuButton<String>(
                onSelected: (value) => _handleCompletedTodoAction(value, todo),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'duplicate',
                    child: Row(
                      children: [
                        Icon(Icons.copy, color: Colors.blue),
                        SizedBox(width: 8),
                        Text('Duplicate'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'restore',
                    child: Row(
                      children: [
                        Icon(Icons.restore, color: Colors.orange),
                        SizedBox(width: 8),
                        Text('Restore'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, color: Colors.green),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete'),
                      ],
                    ),
                  ),
                ],
              )
            : Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _getPriorityColor(todo.priority),
                ),
              ),
        onTap: () {
          context.push('${AppRoutes.todoEditor}?id=${todo.id}');
        },
      ),
    );
  }

  Widget _buildVoiceMemosTab() {
    final memos = ref.watch(filteredVoiceMemosProvider);

    if (memos.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mic_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No voice memos yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Tap + to record your first memo',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: memos.length,
      itemBuilder: (context, index) {
        final memo = memos[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const Icon(Icons.mic),
            title: Text(
              memo.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text('Duration: ${memo.formattedDuration}'),
            trailing: Text('${DateTime.now().day}/${DateTime.now().month}'),
            onTap: () {
              // TODO: Play voice memo
            },
          ),
        );
      },
    );
  }

  Color _getPriorityColor(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.low:
        return Colors.green;
      case TodoPriority.medium:
        return Colors.orange;
      case TodoPriority.high:
        return Colors.red;
      case TodoPriority.urgent:
        return Colors.purple;
    }
  }

  void _handleCompletedTodoAction(String action, Todo todo) async {
    switch (action) {
      case 'duplicate':
        try {
          await ref.read(todosProvider.notifier).duplicateCompletedTodo(todo.id);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Todo "${todo.title}" duplicated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to duplicate todo: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
        break;
      case 'restore':
        try {
          await ref.read(todosProvider.notifier).restoreCompletedTodo(todo.id);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Todo "${todo.title}" restored to active'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to restore todo: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
        break;
      case 'edit':
        context.push('${AppRoutes.todoEditor}?id=${todo.id}');
        break;
      case 'delete':
        _showDeleteCompletedTodoDialog(todo);
        break;
    }
  }

  void _showDeleteCompletedTodoDialog(Todo todo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Completed Todo'),
        content: Text('Are you sure you want to permanently delete "${todo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(todosProvider.notifier).deleteCompletedTodo(todo.id);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Todo "${todo.title}" deleted permanently'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete todo: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showClearCompletedDialog() {
    final completedTodos = ref.read(completedTodosProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Completed Todos'),
        content: Text('Are you sure you want to permanently delete all ${completedTodos.length} completed todos?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(todosProvider.notifier).clearAllCompletedTodos();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('All completed todos cleared'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to clear completed todos: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}
