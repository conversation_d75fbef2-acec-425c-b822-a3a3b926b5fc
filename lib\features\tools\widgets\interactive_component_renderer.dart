import 'package:flutter/material.dart';
import '../models/ui_component.dart';
import '../models/mobile_app_theme.dart';
import '../services/formula_engine_service.dart';
import '../services/app_preview_service.dart';

// Import the UIComponentData class from the UI builder screen
class UIComponentData {
  final String id;
  ComponentType type;
  String label;
  double x;
  double y;
  double width;
  double height;
  String? cellBinding;
  Color? customColor;

  UIComponentData({
    required this.id,
    required this.type,
    required this.label,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.cellBinding,
    this.customColor,
  });
}

/// Enhanced component renderer with full interactivity
class InteractiveComponentRenderer extends StatefulWidget {
  final UIComponentData component;
  final MobileAppTheme? theme;
  final Map<String, dynamic> cellValues;
  final Map<String, String> cellBindings;
  final FormulaEngineService formulaEngine;
  final AppPreviewService previewService;
  final Function(String componentId, String eventType, dynamic value)? onInteraction;

  const InteractiveComponentRenderer({
    super.key,
    required this.component,
    this.theme,
    this.cellValues = const {},
    this.cellBindings = const {},
    required this.formulaEngine,
    required this.previewService,
    this.onInteraction,
  });

  @override
  State<InteractiveComponentRenderer> createState() => _InteractiveComponentRendererState();
}

class _InteractiveComponentRendererState extends State<InteractiveComponentRenderer> {
  late dynamic _currentValue;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeValue();
  }

  void _initializeValue() {
    final appState = widget.previewService.getAppState();
    _currentValue = appState[widget.component.id] ?? _getDefaultValue();
  }

  dynamic _getDefaultValue() {
    switch (widget.component.type) {
      case ComponentType.textField:
        return '';
      case ComponentType.toggle:
      case ComponentType.checkbox:
        return false;
      case ComponentType.slider:
        return 0.0;
      case ComponentType.dropdown:
        return null;
      case ComponentType.outputField:
        return _getOutputValue();
      default:
        return widget.component.label;
    }
  }

  dynamic _getOutputValue() {
    final cellBinding = widget.cellBindings[widget.component.id];
    if (cellBinding != null && widget.cellValues.containsKey(cellBinding)) {
      return widget.cellValues[cellBinding];
    }
    return widget.component.label;
  }

  void _handleInteraction(String eventType, dynamic value) {
    setState(() {
      _currentValue = value;
      _errorMessage = null;
    });

    // Update preview service
    widget.previewService.handleComponentInteraction(
      widget.component.id,
      eventType,
      value,
    );

    // Notify parent
    widget.onInteraction?.call(widget.component.id, eventType, value);

    // Handle special interactions
    _handleSpecialInteractions(eventType, value);
  }

  void _handleSpecialInteractions(String eventType, dynamic value) {
    if (widget.component.type == ComponentType.button && eventType == 'onPressed') {
      _handleButtonPress();
    }
  }

  Future<void> _handleButtonPress() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final label = widget.component.label.toLowerCase();
      
      if (label.contains('submit') || label.contains('save')) {
        await _handleFormSubmission();
      } else if (label.contains('calculate') || label.contains('compute')) {
        await _handleCalculation();
      } else if (label.contains('clear') || label.contains('reset')) {
        _handleClear();
      } else {
        // Generic button action
        await Future.delayed(const Duration(milliseconds: 300));
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Action failed: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleFormSubmission() async {
    // Simulate form validation and submission
    await Future.delayed(const Duration(milliseconds: 800));
    
    final success = DateTime.now().millisecond % 10 != 0; // 90% success rate
    if (!success) {
      throw Exception('Network error');
    }
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Form submitted successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _handleCalculation() async {
    // Simulate complex calculation
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Trigger recalculation of all formula components
    // This would update the cell values and refresh output components
  }

  void _handleClear() {
    // Clear all form fields
    widget.previewService.handleComponentInteraction(
      'clear_all',
      'action',
      null,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = widget.theme?.toThemeData() ?? Theme.of(context);
    
    return Theme(
      data: theme,
      child: _buildComponent(),
    );
  }

  Widget _buildComponent() {
    switch (widget.component.type) {
      case ComponentType.textField:
        return _buildTextField();
      case ComponentType.outputField:
        return _buildOutputField();
      case ComponentType.button:
        return _buildButton();
      case ComponentType.label:
        return _buildLabel();
      case ComponentType.toggle:
        return _buildToggle();
      case ComponentType.checkbox:
        return _buildCheckbox();
      case ComponentType.slider:
        return _buildSlider();
      case ComponentType.dropdown:
        return _buildDropdown();
      case ComponentType.chart:
        return _buildChart();
      default:
        return _buildPlaceholder();
    }
  }

  Widget _buildTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          decoration: InputDecoration(
            labelText: widget.component.label,
            border: const OutlineInputBorder(),
            errorText: _errorMessage,
            suffixIcon: _isLoading ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ) : null,
          ),
          onChanged: (value) => _handleInteraction('onChanged', value),
          controller: TextEditingController(text: _currentValue.toString()),
        ),
        if (widget.cellBindings[widget.component.id] != null) ...[
          const SizedBox(height: 4),
          Text(
            'Bound to: ${widget.cellBindings[widget.component.id]}',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildOutputField() {
    final value = _getOutputValue();
    final cellBinding = widget.cellBindings[widget.component.id];
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.component.label,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value.toString(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
              fontFamily: 'monospace',
            ),
          ),
          if (cellBinding != null) ...[
            const SizedBox(height: 4),
            Text(
              'Cell: $cellBinding',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : () => _handleInteraction('onPressed', null),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Text(widget.component.label),
      ),
    );
  }

  Widget _buildLabel() {
    return Text(
      widget.component.label,
      style: Theme.of(context).textTheme.bodyLarge,
    );
  }

  Widget _buildToggle() {
    return SwitchListTile(
      title: Text(widget.component.label),
      value: _currentValue as bool? ?? false,
      onChanged: (value) => _handleInteraction('onChanged', value),
    );
  }

  Widget _buildCheckbox() {
    return CheckboxListTile(
      title: Text(widget.component.label),
      value: _currentValue as bool? ?? false,
      onChanged: (value) => _handleInteraction('onChanged', value ?? false),
    );
  }

  Widget _buildSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${widget.component.label}: ${(_currentValue as double? ?? 0.0).toStringAsFixed(1)}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Slider(
          value: _currentValue as double? ?? 0.0,
          min: 0.0,
          max: 100.0,
          divisions: 100,
          onChanged: (value) => _handleInteraction('onChanged', value),
        ),
      ],
    );
  }

  Widget _buildDropdown() {
    final items = ['Option 1', 'Option 2', 'Option 3', 'Option 4'];
    
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: widget.component.label,
        border: const OutlineInputBorder(),
      ),
      value: _currentValue as String?,
      items: items.map((item) => DropdownMenuItem(
        value: item,
        child: Text(item),
      )).toList(),
      onChanged: (value) => _handleInteraction('onChanged', value),
    );
  }

  Widget _buildChart() {
    return Card(
      child: Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              widget.component.label,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.show_chart,
                        size: 48,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Interactive Chart',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      if (widget.cellBindings[widget.component.id] != null)
                        Text(
                          'Data: ${widget.cellBindings[widget.component.id]}',
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.widgets,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            widget.component.type.name.toUpperCase(),
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            widget.component.label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
