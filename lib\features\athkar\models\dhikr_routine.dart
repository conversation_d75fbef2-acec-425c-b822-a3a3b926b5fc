import 'package:flutter/material.dart';

enum DhikrType { tasbih, tahmid, takbir, tahlil, istighfar, salawat, custom }

class DhikrStep {
  int id;
  String text;
  String transliteration;
  String translation;
  int targetCount;
  int currentCount;
  Color color;
  DhikrType type;
  String? audioPath;
  bool hasVibration;
  bool hasSound;
  Duration? reminderInterval;
  
  DhikrStep({
    required this.id,
    required this.text,
    required this.transliteration,
    required this.translation,
    required this.targetCount,
    this.currentCount = 0,
    this.color = Colors.green,
    this.type = DhikrType.custom,
    this.audioPath,
    this.hasVibration = true,
    this.hasSound = true,
    this.reminderInterval,
  });

  double get progress => targetCount > 0 ? (currentCount / targetCount).clamp(0.0, 1.0) : 0.0;
  bool get isCompleted => currentCount >= targetCount;
  int get remainingCount => (targetCount - currentCount).clamp(0, targetCount);

  void increment() {
    if (currentCount < targetCount) {
      currentCount++;
    }
  }

  void decrement() {
    if (currentCount > 0) {
      currentCount--;
    }
  }

  void reset() {
    currentCount = 0;
  }

  void complete() {
    currentCount = targetCount;
  }

  DhikrStep copyWith({
    String? text,
    String? transliteration,
    String? translation,
    int? targetCount,
    int? currentCount,
    Color? color,
    DhikrType? type,
    String? audioPath,
    bool? hasVibration,
    bool? hasSound,
    Duration? reminderInterval,
  }) {
    return DhikrStep(
      id: id,
      text: text ?? this.text,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      targetCount: targetCount ?? this.targetCount,
      currentCount: currentCount ?? this.currentCount,
      color: color ?? this.color,
      type: type ?? this.type,
      audioPath: audioPath ?? this.audioPath,
      hasVibration: hasVibration ?? this.hasVibration,
      hasSound: hasSound ?? this.hasSound,
      reminderInterval: reminderInterval ?? this.reminderInterval,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'transliteration': transliteration,
      'translation': translation,
      'targetCount': targetCount,
      'currentCount': currentCount,
      'color': (color.r * 255).round() << 16 | (color.g * 255).round() << 8 | (color.b * 255).round() | (color.a * 255).round() << 24,
      'type': type.name,
      'audioPath': audioPath,
      'hasVibration': hasVibration,
      'hasSound': hasSound,
      'reminderInterval': reminderInterval?.inSeconds,
    };
  }

  factory DhikrStep.fromJson(Map<String, dynamic> json) {
    return DhikrStep(
      id: json['id'],
      text: json['text'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      targetCount: json['targetCount'],
      currentCount: json['currentCount'] ?? 0,
      color: Color(json['color']),
      type: DhikrType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => DhikrType.custom,
      ),
      audioPath: json['audioPath'],
      hasVibration: json['hasVibration'] ?? true,
      hasSound: json['hasSound'] ?? true,
      reminderInterval: json['reminderInterval'] != null 
          ? Duration(seconds: json['reminderInterval']) 
          : null,
    );
  }
}

class DhikrRoutine {
  int id;
  String name;
  String description;
  List<DhikrStep> steps;
  int currentStepIndex;
  DateTime createdAt;
  DateTime updatedAt;
  DateTime? lastCompletedAt;
  int completionCount;
  bool isActive;
  String userId;
  
  // Routine settings
  bool autoAdvance;
  bool loopRoutine;
  Duration? sessionDuration;
  TimeOfDay? reminderTime;
  List<int> reminderDays; // 1-7 for Monday-Sunday
  
  // Statistics
  int totalSessions;
  Duration totalTimeSpent;
  Map<String, int> dailyCompletions;

  DhikrRoutine({
    required this.id,
    required this.name,
    required this.description,
    required this.steps,
    required this.userId,
    this.currentStepIndex = 0,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.lastCompletedAt,
    this.completionCount = 0,
    this.isActive = true,
    this.autoAdvance = false,
    this.loopRoutine = false,
    this.sessionDuration,
    this.reminderTime,
    List<int>? reminderDays,
    this.totalSessions = 0,
    Duration? totalTimeSpent,
    Map<String, int>? dailyCompletions,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       reminderDays = reminderDays ?? [],
       totalTimeSpent = totalTimeSpent ?? Duration.zero,
       dailyCompletions = dailyCompletions ?? {};

  DhikrStep? get currentStep => 
      currentStepIndex < steps.length ? steps[currentStepIndex] : null;
  
  bool get isCompleted => steps.every((step) => step.isCompleted);
  
  double get overallProgress {
    if (steps.isEmpty) return 0.0;
    final totalProgress = steps.fold(0.0, (sum, step) => sum + step.progress);
    return totalProgress / steps.length;
  }
  
  int get totalTargetCount => steps.fold(0, (sum, step) => sum + step.targetCount);
  int get totalCurrentCount => steps.fold(0, (sum, step) => sum + step.currentCount);
  
  Duration get estimatedDuration {
    // Estimate 1 second per dhikr
    return Duration(seconds: totalTargetCount);
  }

  void nextStep() {
    if (currentStepIndex < steps.length - 1) {
      currentStepIndex++;
    } else if (loopRoutine) {
      currentStepIndex = 0;
    }
    updatedAt = DateTime.now();
  }

  void previousStep() {
    if (currentStepIndex > 0) {
      currentStepIndex--;
    } else if (loopRoutine && steps.isNotEmpty) {
      currentStepIndex = steps.length - 1;
    }
    updatedAt = DateTime.now();
  }

  void goToStep(int index) {
    if (index >= 0 && index < steps.length) {
      currentStepIndex = index;
      updatedAt = DateTime.now();
    }
  }

  void resetRoutine() {
    for (final step in steps) {
      step.reset();
    }
    currentStepIndex = 0;
    updatedAt = DateTime.now();
  }

  void completeSession() {
    lastCompletedAt = DateTime.now();
    completionCount++;
    totalSessions++;
    
    // Update daily completions
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
    dailyCompletions[dateKey] = (dailyCompletions[dateKey] ?? 0) + 1;
    
    updatedAt = DateTime.now();
  }

  void addTimeSpent(Duration duration) {
    totalTimeSpent += duration;
    updatedAt = DateTime.now();
  }

  void addStep(DhikrStep step) {
    steps.add(step);
    updatedAt = DateTime.now();
  }

  void removeStep(int stepId) {
    steps.removeWhere((step) => step.id == stepId);
    if (currentStepIndex >= steps.length) {
      currentStepIndex = steps.length - 1;
    }
    updatedAt = DateTime.now();
  }

  void updateStep(DhikrStep updatedStep) {
    final index = steps.indexWhere((step) => step.id == updatedStep.id);
    if (index != -1) {
      steps[index] = updatedStep;
      updatedAt = DateTime.now();
    }
  }

  void reorderSteps(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final step = steps.removeAt(oldIndex);
    steps.insert(newIndex, step);
    
    // Adjust current step index if needed
    if (currentStepIndex == oldIndex) {
      currentStepIndex = newIndex;
    } else if (oldIndex < currentStepIndex && newIndex >= currentStepIndex) {
      currentStepIndex--;
    } else if (oldIndex > currentStepIndex && newIndex <= currentStepIndex) {
      currentStepIndex++;
    }
    
    updatedAt = DateTime.now();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'steps': steps.map((step) => step.toJson()).toList(),
      'currentStepIndex': currentStepIndex,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastCompletedAt': lastCompletedAt?.toIso8601String(),
      'completionCount': completionCount,
      'isActive': isActive,
      'userId': userId,
      'autoAdvance': autoAdvance,
      'loopRoutine': loopRoutine,
      'sessionDuration': sessionDuration?.inSeconds,
      'reminderTime': reminderTime != null 
          ? '${reminderTime!.hour}:${reminderTime!.minute}' 
          : null,
      'reminderDays': reminderDays,
      'totalSessions': totalSessions,
      'totalTimeSpent': totalTimeSpent.inSeconds,
      'dailyCompletions': dailyCompletions,
    };
  }

  factory DhikrRoutine.fromJson(Map<String, dynamic> json) {
    return DhikrRoutine(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      steps: (json['steps'] as List).map((stepJson) => DhikrStep.fromJson(stepJson)).toList(),
      currentStepIndex: json['currentStepIndex'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      lastCompletedAt: json['lastCompletedAt'] != null 
          ? DateTime.parse(json['lastCompletedAt']) 
          : null,
      completionCount: json['completionCount'] ?? 0,
      isActive: json['isActive'] ?? true,
      userId: json['userId'],
      autoAdvance: json['autoAdvance'] ?? false,
      loopRoutine: json['loopRoutine'] ?? false,
      sessionDuration: json['sessionDuration'] != null 
          ? Duration(seconds: json['sessionDuration']) 
          : null,
      reminderTime: json['reminderTime'] != null 
          ? _parseTimeOfDay(json['reminderTime']) 
          : null,
      reminderDays: List<int>.from(json['reminderDays'] ?? []),
      totalSessions: json['totalSessions'] ?? 0,
      totalTimeSpent: Duration(seconds: json['totalTimeSpent'] ?? 0),
      dailyCompletions: Map<String, int>.from(json['dailyCompletions'] ?? {}),
    );
  }

  static TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }
}

// Predefined dhikr templates
class DhikrTemplates {
  static List<DhikrStep> get morningAdhkar => [
    DhikrStep(
      id: 1,
      text: 'سُبْحَانَ اللَّهِ',
      transliteration: 'Subhan Allah',
      translation: 'Glory be to Allah',
      targetCount: 33,
      type: DhikrType.tasbih,
      color: Colors.green,
    ),
    DhikrStep(
      id: 2,
      text: 'الْحَمْدُ لِلَّهِ',
      transliteration: 'Alhamdulillah',
      translation: 'Praise be to Allah',
      targetCount: 33,
      type: DhikrType.tahmid,
      color: Colors.blue,
    ),
    DhikrStep(
      id: 3,
      text: 'اللَّهُ أَكْبَرُ',
      transliteration: 'Allahu Akbar',
      translation: 'Allah is the Greatest',
      targetCount: 34,
      type: DhikrType.takbir,
      color: Colors.orange,
    ),
  ];

  static List<DhikrStep> get eveningAdhkar => [
    DhikrStep(
      id: 4,
      text: 'أَسْتَغْفِرُ اللَّهَ',
      transliteration: 'Astaghfirullah',
      translation: 'I seek forgiveness from Allah',
      targetCount: 100,
      type: DhikrType.istighfar,
      color: Colors.purple,
    ),
    DhikrStep(
      id: 5,
      text: 'لَا إِلَهَ إِلَّا اللَّهُ',
      transliteration: 'La ilaha illa Allah',
      translation: 'There is no god but Allah',
      targetCount: 100,
      type: DhikrType.tahlil,
      color: Colors.teal,
    ),
  ];

  static List<DhikrStep> get salawatOnProphet => [
    DhikrStep(
      id: 6,
      text: 'اللَّهُمَّ صَلِّ عَلَى مُحَمَّدٍ وَعَلَى آلِ مُحَمَّدٍ',
      transliteration: 'Allahumma salli ala Muhammad wa ala ali Muhammad',
      translation: 'O Allah, send prayers upon Muhammad and the family of Muhammad',
      targetCount: 10,
      type: DhikrType.salawat,
      color: Colors.indigo,
    ),
  ];
}
