import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/standardized_scaffold.dart';
import '../../../shared/widgets/standardized_components.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';
import '../widgets/account_icon_picker.dart';

class AccountFormScreen extends ConsumerStatefulWidget {
  final Account? account;

  const AccountFormScreen({
    super.key,
    this.account,
  });

  @override
  ConsumerState<AccountFormScreen> createState() => _AccountFormScreenState();
}

class _AccountFormScreenState extends ConsumerState<AccountFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _balanceController;
  late TextEditingController _bankNameController;
  late TextEditingController _accountNumberController;
  
  AccountType _selectedType = AccountType.checking;
  String _selectedCurrency = 'USD';
  bool _isActive = true;
  bool _includeInTotal = true;
  bool _allowNegativeBalance = false;
  IconData _selectedIcon = Icons.account_balance_wallet;
  Color _selectedColor = Colors.blue;

  late List<Map<String, String>> _currencies;

  @override
  void initState() {
    super.initState();

    // Initialize currencies from Account model
    _currencies = Account.getAvailableCurrencies();

    _nameController = TextEditingController();
    _descriptionController = TextEditingController();
    _balanceController = TextEditingController(text: '0.00');
    _bankNameController = TextEditingController();
    _accountNumberController = TextEditingController();

    if (widget.account != null) {
      _loadAccountData();
    }
  }

  void _loadAccountData() {
    final account = widget.account!;
    _nameController.text = account.name;
    _descriptionController.text = account.description;
    _balanceController.text = account.balance.toString();
    _bankNameController.text = account.bankName ?? '';
    _accountNumberController.text = account.accountNumber ?? '';
    _selectedType = account.type;
    _selectedCurrency = account.currency;
    _isActive = account.isActive;
    _includeInTotal = account.includeInTotal;
    _allowNegativeBalance = account.allowNegativeBalance;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _balanceController.dispose();
    _bankNameController.dispose();
    _accountNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.account == null ? 'Add Account' : 'Edit Account'),
        actions: [
          if (widget.account != null)
            IconButton(
              onPressed: _deleteAccount,
              icon: const Icon(Icons.delete),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information
              StandardCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Basic Information',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                      
                      // Account Name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Account Name',
                          border: OutlineInputBorder(),
                          hintText: 'e.g., Main Checking, Savings Account',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter an account name';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description (optional)',
                          border: OutlineInputBorder(),
                          hintText: 'Brief description of this account',
                        ),
                        maxLines: 2,
                      ),
                    ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Account Type and Currency
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Account Type & Currency',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      
                      // Account Type
                      DropdownButtonFormField<AccountType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'Account Type',
                          border: OutlineInputBorder(),
                        ),
                        items: AccountType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Row(
                              children: [
                                Icon(_getAccountTypeIcon(type)),
                                const SizedBox(width: 8),
                                Text(_getAccountTypeDisplayName(type)),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Currency
                      DropdownButtonFormField<String>(
                        value: _selectedCurrency,
                        decoration: const InputDecoration(
                          labelText: 'Currency',
                          border: OutlineInputBorder(),
                        ),
                        items: _currencies.map((currency) {
                          return DropdownMenuItem(
                            value: currency['code']!,
                            child: Row(
                              children: [
                                Text(currency['symbol']!),
                                const SizedBox(width: 8),
                                Text('${currency['code']} - ${currency['name']}'),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCurrency = value!;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Balance and Bank Information
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Balance & Bank Information',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),
                      
                      // Current Balance
                      TextFormField(
                        controller: _balanceController,
                        decoration: InputDecoration(
                          labelText: 'Current Balance',
                          prefixText: _getCurrencySymbol(_selectedCurrency),
                          border: const OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^-?\d+\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter the current balance';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Please enter a valid amount';
                          }
                          return null;
                        },
                      ),
                      

                      // Bank fields - only show for bank account types
                      if (_selectedType == AccountType.checking || _selectedType == AccountType.savings) ...[
                        const SizedBox(height: 16),

                        // Bank Name
                        TextFormField(
                          controller: _bankNameController,
                          decoration: const InputDecoration(
                            labelText: 'Bank Name (optional)',
                            border: OutlineInputBorder(),
                            hintText: 'e.g., Chase, Bank of America',
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Account Number
                        TextFormField(
                          controller: _accountNumberController,
                          decoration: const InputDecoration(
                            labelText: 'Account Number (optional)',
                            border: OutlineInputBorder(),
                            hintText: 'Last 4 digits or full number',
                          ),
                          obscureText: true,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),

              // Icon and Color Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Appearance',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 16),

                      // Icon Selection
                      Row(
                        children: [
                          const Text('Icon: '),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: _showIconPicker,
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(_selectedIcon, size: 24),
                            ),
                          ),
                          const SizedBox(width: 16),
                          const Text('Color: '),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: _showColorPicker,
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: _selectedColor,
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Account Settings
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Account Settings',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      
                      // Active Account
                      SwitchListTile(
                        title: const Text('Active Account'),
                        subtitle: const Text('Include in transactions and calculations'),
                        value: _isActive,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                      
                      // Include in Total
                      SwitchListTile(
                        title: const Text('Include in Net Worth'),
                        subtitle: const Text('Include this account in total calculations'),
                        value: _includeInTotal,
                        onChanged: (value) {
                          setState(() {
                            _includeInTotal = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),

                      // Allow Negative Balance
                      SwitchListTile(
                        title: const Text('Allow Negative Balance'),
                        subtitle: const Text('Allow this account to have negative balance'),
                        value: _allowNegativeBalance,
                        onChanged: (value) {
                          setState(() {
                            _allowNegativeBalance = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveAccount,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      widget.account == null ? 'Add Account' : 'Update Account',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getAccountTypeDisplayName(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return 'Checking Account';
      case AccountType.savings:
        return 'Savings Account';
      case AccountType.credit:
        return 'Credit Card';
      case AccountType.investment:
        return 'Investment Account';
      case AccountType.cash:
        return 'Cash';
      case AccountType.loan:
        return 'Loan Account';
      case AccountType.other:
        return 'Other';
    }
  }

  IconData _getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.credit:
        return Icons.credit_card;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.cash:
        return Icons.money;
      case AccountType.loan:
        return Icons.money_off;
      case AccountType.other:
        return Icons.account_balance_wallet;
    }
  }

  String _getCurrencySymbol(String currency) {
    switch (currency) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'CAD':
        return 'C\$';
      case 'AUD':
        return 'A\$';
      default:
        return '\$';
    }
  }

  void _saveAccount() {
    if (!_formKey.currentState!.validate()) return;
    
    final name = _nameController.text.trim();
    final description = _descriptionController.text.trim();
    final balance = double.parse(_balanceController.text);
    final bankName = _bankNameController.text.trim();
    final accountNumber = _accountNumberController.text.trim();
    
    if (widget.account == null) {
      // Create new account
      final account = Account.create(
        name: name,
        type: _selectedType,
        userId: '', // Will be set by provider
        description: description,
        balance: balance,
        currency: _selectedCurrency,
        bankName: bankName.isEmpty ? null : bankName,
        accountNumber: accountNumber.isEmpty ? null : accountNumber,
        isActive: _isActive,
        includeInTotal: _includeInTotal,
        allowNegativeBalance: _allowNegativeBalance,
      );
      
      ref.read(accountsProvider.notifier).addAccount(account);
    } else {
      // Update existing account
      widget.account!.updateAccount(
        name: name,
        description: description,
        type: _selectedType,
        currency: _selectedCurrency,
        bankName: bankName.isEmpty ? null : bankName,
        accountNumber: accountNumber.isEmpty ? null : accountNumber,
        isActive: _isActive,
        includeInTotal: _includeInTotal,
        allowNegativeBalance: _allowNegativeBalance,
      );
      
      // Update balance separately
      widget.account!.updateBalance(balance);
      
      ref.read(accountsProvider.notifier).updateAccount(widget.account!);
    }
    
    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.account == null 
              ? 'Account added successfully' 
              : 'Account updated successfully',
        ),
      ),
    );
  }

  void _showIconPicker() {
    showDialog(
      context: context,
      builder: (context) => AccountIconPicker(
        selectedIcon: _selectedIcon,
        selectedColor: _selectedColor,
        onIconSelected: (icon) {
          setState(() {
            _selectedIcon = icon;
          });
        },
      ),
    );
  }

  void _showColorPicker() {
    final availableColors = [
      Colors.blue,
      Colors.green,
      Colors.red,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.brown,
      Colors.grey,
      Colors.cyan,
      Colors.amber,
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Color'),
        content: SizedBox(
          width: double.maxFinite,
          height: 200,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: availableColors.length,
            itemBuilder: (context, index) {
              final color = availableColors[index];
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                  Navigator.of(context).pop();
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(
                      color: _selectedColor == color ? Colors.black : Colors.grey,
                      width: _selectedColor == color ? 3 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _deleteAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text('Are you sure you want to delete this account? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(accountsProvider.notifier).deleteAccount(widget.account!.id);
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close form
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Account deleted')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
