import 'package:flutter/foundation.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';



enum AuthenticationMethod {
  none,
  pin,
  password,
  biometric,
  pattern,
}

enum BiometricType {
  none,
  fingerprint,
  face,
  iris,
  weak,
  strong,
}

class AuthenticationService {
  static final AuthenticationService _instance = AuthenticationService._internal();
  factory AuthenticationService() => _instance;
  AuthenticationService._internal();

  static AuthenticationService get instance => _instance;

  final LocalAuthentication _localAuth = LocalAuthentication();
  
  // Keys for SharedPreferences
  static const String _keyAuthMethod = 'auth_method';
  static const String _keyPinHash = 'pin_hash';
  static const String _keyPasswordHash = 'password_hash';
  static const String _keyBiometricEnabled = 'biometric_enabled';
  static const String _keyAutoLockMinutes = 'auto_lock_minutes';
  static const String _keyLastActiveTime = 'last_active_time';
  static const String _keyFailedAttempts = 'failed_attempts';
  static const String _keyLockoutTime = 'lockout_time';

  // Security settings
  static const int maxFailedAttempts = 5;
  static const int lockoutDurationMinutes = 30;

  /// Check if device supports biometric authentication
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      return availableBiometrics.map((biometric) {
        // Convert platform BiometricType to our BiometricType
        final biometricString = biometric.toString();
        if (biometricString.contains('fingerprint')) {
          return BiometricType.fingerprint;
        } else if (biometricString.contains('face')) {
          return BiometricType.face;
        } else if (biometricString.contains('iris')) {
          return BiometricType.iris;
        } else if (biometricString.contains('weak')) {
          return BiometricType.weak;
        } else if (biometricString.contains('strong')) {
          return BiometricType.strong;
        } else {
          return BiometricType.none;
        }
      }).toList();
    } catch (e) {
      debugPrint('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Authenticate using biometrics
  Future<bool> authenticateWithBiometrics({
    String reason = 'Please authenticate to access the app',
  }) async {
    try {
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: true,
        ),
      );

      if (isAuthenticated) {
        await _resetFailedAttempts();
        await _updateLastActiveTime();
      }

      return isAuthenticated;
    } catch (e) {
      debugPrint('Biometric authentication error: $e');
      return false;
    }
  }

  /// Set up PIN authentication
  Future<bool> setupPin(String pin) async {
    try {
      if (pin.length < 4) {
        throw Exception('PIN must be at least 4 digits');
      }

      final prefs = await SharedPreferences.getInstance();
      final hashedPin = _hashString(pin);
      
      await prefs.setString(_keyPinHash, hashedPin);
      await prefs.setString(_keyAuthMethod, AuthenticationMethod.pin.name);
      
      return true;
    } catch (e) {
      debugPrint('Error setting up PIN: $e');
      return false;
    }
  }

  /// Authenticate using PIN
  Future<bool> authenticateWithPin(String pin) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedHash = prefs.getString(_keyPinHash);
      
      if (storedHash == null) {
        return false;
      }

      final inputHash = _hashString(pin);
      final isValid = storedHash == inputHash;

      if (isValid) {
        await _resetFailedAttempts();
        await _updateLastActiveTime();
      } else {
        await _incrementFailedAttempts();
      }

      return isValid;
    } catch (e) {
      debugPrint('PIN authentication error: $e');
      return false;
    }
  }

  /// Set up password authentication
  Future<bool> setupPassword(String password) async {
    try {
      if (password.length < 6) {
        throw Exception('Password must be at least 6 characters');
      }

      final prefs = await SharedPreferences.getInstance();
      final hashedPassword = _hashString(password);
      
      await prefs.setString(_keyPasswordHash, hashedPassword);
      await prefs.setString(_keyAuthMethod, AuthenticationMethod.password.name);
      
      return true;
    } catch (e) {
      debugPrint('Error setting up password: $e');
      return false;
    }
  }

  /// Authenticate using password
  Future<bool> authenticateWithPassword(String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedHash = prefs.getString(_keyPasswordHash);
      
      if (storedHash == null) {
        return false;
      }

      final inputHash = _hashString(password);
      final isValid = storedHash == inputHash;

      if (isValid) {
        await _resetFailedAttempts();
        await _updateLastActiveTime();
      } else {
        await _incrementFailedAttempts();
      }

      return isValid;
    } catch (e) {
      debugPrint('Password authentication error: $e');
      return false;
    }
  }

  /// Get current authentication method
  Future<AuthenticationMethod> getAuthenticationMethod() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final methodName = prefs.getString(_keyAuthMethod);
      
      if (methodName == null) {
        return AuthenticationMethod.none;
      }

      return AuthenticationMethod.values.firstWhere(
        (method) => method.name == methodName,
        orElse: () => AuthenticationMethod.none,
      );
    } catch (e) {
      debugPrint('Error getting authentication method: $e');
      return AuthenticationMethod.none;
    }
  }

  /// Check if biometric authentication is enabled
  Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyBiometricEnabled) ?? false;
    } catch (e) {
      debugPrint('Error checking biometric enabled: $e');
      return false;
    }
  }

  /// Enable/disable biometric authentication
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyBiometricEnabled, enabled);
    } catch (e) {
      debugPrint('Error setting biometric enabled: $e');
    }
  }

  /// Check if app should be locked based on auto-lock settings
  Future<bool> shouldLockApp() async {
    try {
      final authMethod = await getAuthenticationMethod();
      if (authMethod == AuthenticationMethod.none) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final autoLockMinutes = prefs.getInt(_keyAutoLockMinutes) ?? 5;
      final lastActiveTime = prefs.getInt(_keyLastActiveTime) ?? 0;
      
      if (lastActiveTime == 0) {
        return false;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = now - lastActiveTime;
      final minutesPassed = timeDifference / (1000 * 60);

      return minutesPassed >= autoLockMinutes;
    } catch (e) {
      debugPrint('Error checking if app should lock: $e');
      return false;
    }
  }

  /// Update last active time
  Future<void> updateLastActiveTime() async {
    await _updateLastActiveTime();
  }

  /// Check if account is locked due to failed attempts
  Future<bool> isAccountLocked() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedAttempts = prefs.getInt(_keyFailedAttempts) ?? 0;
      final lockoutTime = prefs.getInt(_keyLockoutTime) ?? 0;

      if (failedAttempts < maxFailedAttempts) {
        return false;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = now - lockoutTime;
      final minutesPassed = timeDifference / (1000 * 60);

      if (minutesPassed >= lockoutDurationMinutes) {
        // Lockout period has expired, reset failed attempts
        await _resetFailedAttempts();
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error checking account lock status: $e');
      return false;
    }
  }

  /// Get remaining lockout time in minutes
  Future<int> getRemainingLockoutMinutes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lockoutTime = prefs.getInt(_keyLockoutTime) ?? 0;
      
      if (lockoutTime == 0) {
        return 0;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = now - lockoutTime;
      final minutesPassed = timeDifference / (1000 * 60);
      
      final remainingMinutes = lockoutDurationMinutes - minutesPassed.ceil();
      return remainingMinutes > 0 ? remainingMinutes : 0;
    } catch (e) {
      debugPrint('Error getting remaining lockout time: $e');
      return 0;
    }
  }

  /// Clear all authentication data
  Future<void> clearAuthenticationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyAuthMethod);
      await prefs.remove(_keyPinHash);
      await prefs.remove(_keyPasswordHash);
      await prefs.remove(_keyBiometricEnabled);
      await prefs.remove(_keyFailedAttempts);
      await prefs.remove(_keyLockoutTime);
    } catch (e) {
      debugPrint('Error clearing authentication data: $e');
    }
  }

  // Private helper methods

  String _hashString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<void> _updateLastActiveTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_keyLastActiveTime, now);
    } catch (e) {
      debugPrint('Error updating last active time: $e');
    }
  }

  Future<void> _incrementFailedAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentAttempts = prefs.getInt(_keyFailedAttempts) ?? 0;
      final newAttempts = currentAttempts + 1;
      
      await prefs.setInt(_keyFailedAttempts, newAttempts);
      
      if (newAttempts >= maxFailedAttempts) {
        final now = DateTime.now().millisecondsSinceEpoch;
        await prefs.setInt(_keyLockoutTime, now);
      }
    } catch (e) {
      debugPrint('Error incrementing failed attempts: $e');
    }
  }

  Future<void> _resetFailedAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyFailedAttempts);
      await prefs.remove(_keyLockoutTime);
    } catch (e) {
      debugPrint('Error resetting failed attempts: $e');
    }
  }
}
