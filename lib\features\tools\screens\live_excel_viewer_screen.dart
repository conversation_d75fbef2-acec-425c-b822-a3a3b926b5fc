import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/excel_workbook.dart';
import '../models/excel_worksheet.dart';
import '../models/spreadsheet_cell.dart';
import '../services/enhanced_excel_service.dart';

/// Live Excel viewer and editor with tool creation capabilities
class LiveExcelViewerScreen extends ConsumerStatefulWidget {
  const LiveExcelViewerScreen({super.key});

  @override
  ConsumerState<LiveExcelViewerScreen> createState() => _LiveExcelViewerScreenState();
}

class _LiveExcelViewerScreenState extends ConsumerState<LiveExcelViewerScreen>
    with TickerProviderStateMixin {
  final _excelService = EnhancedExcelService();
  
  ExcelWorkbook? _workbook;
  String _activeWorksheet = '';
  bool _isLoading = false;
  String? _error;
  
  // Selection and editing
  String? _selectedCell;
  Set<String> _selectedRange = {};
  final _cellEditController = TextEditingController();
  bool _isEditingCell = false;
  
  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _cellEditController.dispose();
    super.dispose();
  }

  Future<void> _openExcelFile() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final workbook = await _excelService.openExcelFile();
      setState(() {
        _workbook = workbook;
        _activeWorksheet = workbook.activeWorksheet;
        _isLoading = false;
      });
      
      _fadeController.forward();
      _slideController.forward();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_workbook?.fileName ?? 'Excel Viewer'),
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        elevation: 0,
        actions: [
          if (_workbook != null) ...[
            IconButton(
              onPressed: _saveWorkbook,
              icon: const Icon(Icons.save),
              tooltip: 'Save Changes',
            ),
            IconButton(
              onPressed: _exportWorkbook,
              icon: const Icon(Icons.download),
              tooltip: 'Export Workbook',
            ),
            IconButton(
              onPressed: _createToolFromSelection,
              icon: const Icon(Icons.build_circle),
              tooltip: 'Create Tool from Selection',
            ),
          ],
          IconButton(
            onPressed: _showWorkbookInfo,
            icon: const Icon(Icons.info_outline),
            tooltip: 'Workbook Info',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _workbook == null
          ? FloatingActionButton.extended(
              onPressed: _openExcelFile,
              icon: const Icon(Icons.file_open),
              label: const Text('Open Excel File'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading Excel file...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading Excel file',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium,
  
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _openExcelFile,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (_workbook == null) {
      return _buildWelcomeScreen();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            _buildWorksheetTabs(),
            _buildToolbar(),
            Expanded(child: _buildSpreadsheetView()),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.table_chart,
            size: 120,
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 24),
          Text(
            'Excel Live Viewer & Editor',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Open Excel files to view, edit, and create custom tools',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),

          ),
          const SizedBox(height: 32),
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Icon(Icons.lightbulb_outline, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Features',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text('• Live Excel editing with real-time updates'),
                  const Text('• Select ranges to create custom tools'),
                  const Text('• Clone worksheets as templates'),
                  const Text('• Export modified workbooks'),
                  const Text('• Professional spreadsheet interface'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorksheetTabs() {
    if (_workbook == null || _workbook!.worksheets.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _workbook!.worksheets.length,
        itemBuilder: (context, index) {
          final worksheet = _workbook!.worksheets[index];
          final isActive = worksheet.name == _activeWorksheet;
          
          return GestureDetector(
            onTap: () => _switchWorksheet(worksheet.name),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isActive 
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    worksheet.name,
                    style: TextStyle(
                      color: isActive 
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  if (worksheet.cellCount > 0) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: isActive 
                            ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2)
                            : Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${worksheet.cellCount}',
                        style: TextStyle(
                          fontSize: 10,
                          color: isActive 
                              ? Theme.of(context).colorScheme.onPrimary
                              : Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Row(
        children: [
          // Cell address display
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              _selectedCell ?? 'A1',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontFamily: 'monospace',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Formula bar
          Expanded(
            child: TextField(
              controller: _cellEditController,
              decoration: InputDecoration(
                hintText: 'Enter value or formula...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                prefixIcon: _isEditingCell 
                    ? const Icon(Icons.edit, size: 16)
                    : const Icon(Icons.functions, size: 16),
              ),
              onSubmitted: _updateCellValue,
              onChanged: (value) {
                setState(() {
                  _isEditingCell = value.isNotEmpty;
                });
              },
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Action buttons
          IconButton(
            onPressed: _selectedRange.isNotEmpty ? _createToolFromSelection : null,
            icon: const Icon(Icons.build_circle),
            tooltip: 'Create Tool from Selection',
          ),
          IconButton(
            onPressed: _cloneWorksheet,
            icon: const Icon(Icons.copy),
            tooltip: 'Clone Worksheet',
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetView() {
    final worksheet = _getCurrentWorksheet();
    if (worksheet == null) {
      return const Center(
        child: Text('No worksheet selected'),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SingleChildScrollView(
          child: _buildSpreadsheetGrid(worksheet),
        ),
      ),
    );
  }

  Widget _buildSpreadsheetGrid(ExcelWorksheet worksheet) {
    const int visibleRows = 50;
    const int visibleCols = 26;
    
    return Column(
      children: [
        // Column headers
        Row(
          children: [
            // Corner cell
            Container(
              width: 60,
              height: 32,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                border: Border.all(color: Theme.of(context).colorScheme.outline),
              ),
            ),
            // Column headers
            ...List.generate(visibleCols, (index) {
              final colLetter = String.fromCharCode(65 + index);
              return Container(
                width: 100,
                height: 32,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                ),
                child: Center(
                  child: Text(
                    colLetter,
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
        
        // Data rows
        ...List.generate(visibleRows, (rowIndex) {
          final rowNumber = rowIndex + 1;
          return Row(
            children: [
              // Row header
              Container(
                width: 60,
                height: 32,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                ),
                child: Center(
                  child: Text(
                    '$rowNumber',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              
              // Data cells
              ...List.generate(visibleCols, (colIndex) {
                final colLetter = String.fromCharCode(65 + colIndex);
                final cellAddress = '$colLetter$rowNumber';
                final cell = worksheet.getCell(cellAddress);
                final isSelected = _selectedCell == cellAddress;
                final isInRange = _selectedRange.contains(cellAddress);
                
                return GestureDetector(
                  onTap: () => _selectCell(cellAddress, cell),
                  child: Container(
                    width: 100,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primaryContainer
                          : isInRange
                              ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
                              : Theme.of(context).colorScheme.surface,
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(4),
                      child: Text(
                        cell?.value?.toString() ?? '',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: cell?.type == CellType.formula
                              ? Theme.of(context).colorScheme.primary
                              : null,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                );
              }),
            ],
          );
        }),
      ],
    );
  }

  ExcelWorksheet? _getCurrentWorksheet() {
    return _workbook?.getWorksheet(_activeWorksheet);
  }

  void _switchWorksheet(String worksheetName) {
    setState(() {
      _activeWorksheet = worksheetName;
      _selectedCell = null;
      _selectedRange.clear();
      _cellEditController.clear();
    });
  }

  void _selectCell(String cellAddress, SpreadsheetCell? cell) {
    setState(() {
      _selectedCell = cellAddress;
      _cellEditController.text = cell?.formula ?? cell?.value?.toString() ?? '';
      _isEditingCell = false;
    });
  }

  Future<void> _updateCellValue(String value) async {
    if (_selectedCell == null) return;

    try {
      await _excelService.updateCellValue(_activeWorksheet, _selectedCell!, value);
      setState(() {
        _workbook = _excelService.currentWorkbook;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating cell: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveWorkbook() async {
    try {
      await _excelService.saveWorkbook();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Workbook saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving workbook: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportWorkbook() async {
    // TODO: Implement export dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  void _createToolFromSelection() {
    if (_workbook == null) return;
    
    final worksheet = _getCurrentWorksheet();
    if (worksheet == null) return;

    // Navigate to tool creation with selected data
    final selectedCells = <String, SpreadsheetCell>{};
    for (final cellAddress in _selectedRange) {
      final cell = worksheet.getCell(cellAddress);
      if (cell != null) {
        selectedCells[cellAddress] = cell;
      }
    }

    context.go('/tools/create', extra: {
      'preloadedData': selectedCells,
      'sourceName': '${_workbook!.fileName} - $_activeWorksheet',
    });
  }

  void _cloneWorksheet() {
    // TODO: Implement worksheet cloning dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Clone worksheet functionality coming soon')),
    );
  }

  void _showWorkbookInfo() {
    if (_workbook == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Workbook Information'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('File: ${_workbook!.fileName}'),
              Text('Worksheets: ${_workbook!.worksheets.length}'),
              Text('Total Cells: ${_workbook!.totalCells}'),
              Text('Total Formulas: ${_workbook!.totalFormulas}'),
              Text('Last Modified: ${_workbook!.lastModified}'),
              const SizedBox(height: 16),
              const Text('Worksheets:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...(_workbook!.worksheets.map((ws) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text('• ${ws.name} (${ws.cellCount} cells)'),
              ))),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
