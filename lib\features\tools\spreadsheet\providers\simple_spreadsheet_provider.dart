import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/models/simple_worksheet.dart';
import '../core/models/simple_cell.dart';
import '../engine/formula_engine.dart';

/// Simple spreadsheet state
class SimpleSpreadsheetState {
  final Map<String, SimpleWorksheet> worksheets;
  final String activeWorksheetId;
  final bool isLoading;
  final String? error;
  final DateTime lastModified;

  const SimpleSpreadsheetState({
    this.worksheets = const {},
    this.activeWorksheetId = '',
    this.isLoading = false,
    this.error,
    required this.lastModified,
  });

  /// Create initial state
  factory SimpleSpreadsheetState.initial() {
    return SimpleSpreadsheetState(
      lastModified: DateTime.now(),
    );
  }

  /// Copy with modifications
  SimpleSpreadsheetState copyWith({
    Map<String, SimpleWorksheet>? worksheets,
    String? activeWorksheetId,
    bool? isLoading,
    String? error,
    DateTime? lastModified,
  }) {
    return SimpleSpreadsheetState(
      worksheets: worksheets ?? this.worksheets,
      activeWorksheetId: activeWorksheetId ?? this.activeWorksheetId,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// Check if has error
  bool get hasError => error != null;

  /// Get worksheet count
  int get worksheetCount => worksheets.length;

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'worksheets': worksheets.map((key, value) => MapEntry(key, value.toJson())),
      'activeWorksheetId': activeWorksheetId,
      'isLoading': isLoading,
      'error': error,
      'lastModified': lastModified.toIso8601String(),
    };
  }

  /// Create from JSON
  factory SimpleSpreadsheetState.fromJson(Map<String, dynamic> json) {
    final worksheetsJson = json['worksheets'] as Map<String, dynamic>? ?? {};
    final worksheets = worksheetsJson.map(
      (key, value) => MapEntry(key, SimpleWorksheet.fromJson(value as Map<String, dynamic>)),
    );

    return SimpleSpreadsheetState(
      worksheets: worksheets,
      activeWorksheetId: json['activeWorksheetId'] as String? ?? '',
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
      lastModified: DateTime.parse(json['lastModified'] as String),
    );
  }
}

/// Simple spreadsheet notifier
class SimpleSpreadsheetNotifier extends StateNotifier<SimpleSpreadsheetState> {
  SimpleSpreadsheetNotifier() : super(SimpleSpreadsheetState.initial());

  final FormulaEngine _formulaEngine = FormulaEngine();

  /// Add new worksheet
  void addWorksheet(String name) {
    final worksheet = SimpleWorksheet.empty(name);
    final updatedWorksheets = Map<String, SimpleWorksheet>.from(state.worksheets);
    updatedWorksheets[worksheet.id] = worksheet;

    state = state.copyWith(
      worksheets: updatedWorksheets,
      activeWorksheetId: state.activeWorksheetId.isEmpty ? worksheet.id : state.activeWorksheetId,
      lastModified: DateTime.now(),
    );
  }

  /// Remove worksheet
  void removeWorksheet(String worksheetId) {
    if (state.worksheets.length <= 1) return; // Keep at least one worksheet

    final updatedWorksheets = Map<String, SimpleWorksheet>.from(state.worksheets);
    updatedWorksheets.remove(worksheetId);

    String newActiveId = state.activeWorksheetId;
    if (newActiveId == worksheetId) {
      newActiveId = updatedWorksheets.keys.first;
    }

    state = state.copyWith(
      worksheets: updatedWorksheets,
      activeWorksheetId: newActiveId,
      lastModified: DateTime.now(),
    );
  }

  /// Set active worksheet
  void setActiveWorksheet(String worksheetId) {
    if (state.worksheets.containsKey(worksheetId)) {
      state = state.copyWith(
        activeWorksheetId: worksheetId,
        lastModified: DateTime.now(),
      );
    }
  }

  /// Rename worksheet
  void renameWorksheet(String worksheetId, String newName) {
    final worksheet = state.worksheets[worksheetId];
    if (worksheet == null) return;

    final updatedWorksheet = worksheet.copyWith(name: newName);
    final updatedWorksheets = Map<String, SimpleWorksheet>.from(state.worksheets);
    updatedWorksheets[worksheetId] = updatedWorksheet;

    state = state.copyWith(
      worksheets: updatedWorksheets,
      lastModified: DateTime.now(),
    );
  }

  /// Update cell
  void updateCell(String worksheetId, String address, dynamic value) {
    final worksheet = state.worksheets[worksheetId];
    if (worksheet == null) return;

    SimpleCell cell;
    if (value is String && value.startsWith('=')) {
      // Use formula engine to evaluate formula
      _formulaEngine.setCellFormula(address, value);
      final calculatedValue = _formulaEngine.evaluateFormula(value, address);
      cell = SimpleCell.withFormula(address, value, calculatedValue);
    } else {
      // Set regular value and update formula engine
      _formulaEngine.setCellValue(address, value);
      cell = SimpleCell.withValue(address, value);
    }

    final updatedWorksheet = worksheet.setCell(address, cell);
    final updatedWorksheets = Map<String, SimpleWorksheet>.from(state.worksheets);
    updatedWorksheets[worksheetId] = updatedWorksheet;

    state = state.copyWith(
      worksheets: updatedWorksheets,
      lastModified: DateTime.now(),
    );
  }

  /// Remove cell
  void removeCell(String worksheetId, String address) {
    final worksheet = state.worksheets[worksheetId];
    if (worksheet == null) return;

    final updatedWorksheet = worksheet.removeCell(address);
    final updatedWorksheets = Map<String, SimpleWorksheet>.from(state.worksheets);
    updatedWorksheets[worksheetId] = updatedWorksheet;

    state = state.copyWith(
      worksheets: updatedWorksheets,
      lastModified: DateTime.now(),
    );
  }

  /// Get cell
  SimpleCell? getCell(String worksheetId, String address) {
    final worksheet = state.worksheets[worksheetId];
    return worksheet?.getCell(address);
  }

  /// Clear all data
  void clear() {
    state = SimpleSpreadsheetState.initial();
  }

  /// Validate formula using formula engine
  ValidationResult validateFormula(String formula) {
    if (!formula.startsWith('=')) {
      return ValidationResult(isValid: false, error: 'Formula must start with =');
    }

    try {
      // Try to evaluate the formula to check for syntax errors
      final result = _formulaEngine.evaluateFormula(formula, 'TEMP');

      // Check if result is an error
      if (result is String && result.startsWith('#')) {
        return ValidationResult(isValid: false, error: 'Formula error: $result');
      }

      return ValidationResult(isValid: true);
    } catch (e) {
      return ValidationResult(isValid: false, error: 'Invalid formula syntax');
    }
  }

  /// Evaluate formula and get result
  dynamic evaluateFormula(String formula, String cellAddress) {
    return _formulaEngine.evaluateFormula(formula, cellAddress);
  }

  /// Get cell value from formula engine
  dynamic getCellValueFromEngine(String cellAddress) {
    return _formulaEngine.getCellValue(cellAddress);
  }

  /// Get function suggestions (simplified)
  List<FunctionSuggestion> getFunctionSuggestions(String input) {
    final functions = [
      FunctionSuggestion('SUM', 'SUM(range)', 'Adds all numbers in a range'),
      FunctionSuggestion('AVERAGE', 'AVERAGE(range)', 'Calculates the average'),
      FunctionSuggestion('COUNT', 'COUNT(range)', 'Counts numbers in a range'),
      FunctionSuggestion('MAX', 'MAX(range)', 'Returns the largest value'),
      FunctionSuggestion('MIN', 'MIN(range)', 'Returns the smallest value'),
    ];

    if (input.isEmpty) return functions;

    return functions
        .where((f) => f.name.toLowerCase().startsWith(input.toLowerCase()))
        .toList();
  }
}

/// Validation result
class ValidationResult {
  final bool isValid;
  final String? error;

  const ValidationResult({required this.isValid, this.error});
}

/// Function suggestion
class FunctionSuggestion {
  final String name;
  final String syntax;
  final String description;

  const FunctionSuggestion(this.name, this.syntax, this.description);
}

/// Provider for simple spreadsheet
final simpleSpreadsheetProvider = StateNotifierProvider<SimpleSpreadsheetNotifier, SimpleSpreadsheetState>((ref) {
  return SimpleSpreadsheetNotifier();
});
