import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/app_state.dart';

// Generic state notifier for managing app states
class StateNotifier<T> extends Notifier<AppState<T>> {
  @override
  AppState<T> build() {
    return AppState.loading();
  }

  // State transitions
  void setLoading({
    LoadingType type = LoadingType.initial,
    String? message,
    T? data,
  }) {
    state = AppState.loading(type: type, message: message, data: data);
  }

  void setSuccess(T data, {String? message}) {
    state = AppState.success(data, message: message);
  }

  void setEmpty({String? message, Map<String, dynamic>? metadata}) {
    state = AppState.empty(message: message, metadata: metadata);
  }

  void setError(
    dynamic error, {
    ErrorType type = ErrorType.unknown,
    String? message,
    StackTrace? stackTrace,
    bool canRetry = true,
    VoidCallback? onRetry,
    T? data,
  }) {
    state = AppState.error(
      error,
      type: type,
      message: message,
      stackTrace: stackTrace,
      canRetry: canRetry,
      onRetry: onRetry,
      data: data,
    );
  }

  void setOffline({String? message, VoidCallback? onRetry, T? data}) {
    state = AppState.offline(message: message, onRetry: onRetry, data: data);
  }

  void setMaintenance({String? message, Map<String, dynamic>? metadata}) {
    state = AppState.maintenance(message: message, metadata: metadata);
  }

  // Async operation wrapper
  Future<void> executeAsync<R>(
    Future<R> Function() operation, {
    R Function(R result)? onSuccess,
    void Function(dynamic error)? onError,
    LoadingType loadingType = LoadingType.initial,
    String? loadingMessage,
    bool preserveDataOnError = false,
  }) async {
    try {
      setLoading(type: loadingType, message: loadingMessage, data: preserveDataOnError ? state.data : null);
      
      final result = await operation();
      
      if (onSuccess != null) {
        final processedResult = onSuccess(result);
        if (processedResult is T) {
          setSuccess(processedResult);
        }
      } else if (result is T) {
        setSuccess(result);
      }
    } catch (error, stackTrace) {
      final errorType = _determineErrorType(error);
      
      setError(
        error,
        type: errorType,
        stackTrace: stackTrace,
        canRetry: true,
        onRetry: () => executeAsync(
          operation,
          onSuccess: onSuccess,
          onError: onError,
          loadingType: LoadingType.refresh,
          loadingMessage: loadingMessage,
          preserveDataOnError: preserveDataOnError,
        ),
        data: preserveDataOnError ? state.data : null,
      );
      
      onError?.call(error);
    }
  }

  // Refresh operation
  Future<void> refresh(Future<T> Function() operation) async {
    await executeAsync(
      operation,
      loadingType: LoadingType.refresh,
      preserveDataOnError: true,
    );
  }

  // Background operation (doesn't change loading state)
  Future<void> executeInBackground(Future<T> Function() operation) async {
    try {
      final result = await operation();
      setSuccess(result);
    } catch (error) {
      // Silently handle background errors or show minimal feedback
      debugPrint('Background operation failed: $error');
    }
  }

  ErrorType _determineErrorType(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('socket')) {
      return ErrorType.network;
    }
    
    if (errorString.contains('timeout')) {
      return ErrorType.timeout;
    }
    
    if (errorString.contains('permission') || 
        errorString.contains('unauthorized') ||
        errorString.contains('forbidden')) {
      return ErrorType.permission;
    }
    
    if (errorString.contains('not found') || 
        errorString.contains('404')) {
      return ErrorType.notFound;
    }
    
    if (errorString.contains('validation') || 
        errorString.contains('invalid')) {
      return ErrorType.validation;
    }
    
    if (errorString.contains('server') || 
        errorString.contains('500') ||
        errorString.contains('503')) {
      return ErrorType.server;
    }
    
    return ErrorType.unknown;
  }
}

// List state notifier
class ListStateNotifier<T> extends Notifier<ListState<T>> {
  @override
  ListState<T> build() {
    return ListState.loading();
  }

  void setLoading({
    LoadingType type = LoadingType.initial,
    String? message,
    List<T>? data,
  }) {
    state = ListState.loading(type: type, message: message, data: data);
  }

  void setSuccess(List<T> data, {String? message}) {
    if (data.isEmpty) {
      state = ListState.empty(message: message ?? 'No items found');
    } else {
      state = ListState.success(data, message: message);
    }
  }

  void setEmpty({String? message}) {
    state = ListState.empty(message: message ?? 'No items found');
  }

  void setError(
    dynamic error, {
    ErrorType type = ErrorType.unknown,
    String? message,
    StackTrace? stackTrace,
    bool canRetry = true,
    VoidCallback? onRetry,
    List<T>? data,
  }) {
    state = ListState.error(
      error,
      type: type,
      message: message,
      stackTrace: stackTrace,
      canRetry: canRetry,
      onRetry: onRetry,
      data: data,
    );
  }

  // List-specific operations
  void addItem(T item) {
    if (state.isSuccess && state.data != null) {
      final newList = [...state.data!, item];
      setSuccess(newList);
    }
  }

  void removeItem(T item) {
    if (state.isSuccess && state.data != null) {
      final newList = state.data!.where((i) => i != item).toList();
      setSuccess(newList);
    }
  }

  void updateItem(T oldItem, T newItem) {
    if (state.isSuccess && state.data != null) {
      final newList = state.data!.map((i) => i == oldItem ? newItem : i).toList();
      setSuccess(newList);
    }
  }

  void replaceItems(List<T> newItems) {
    setSuccess(newItems);
  }

  // Async list operations
  Future<void> loadItems(Future<List<T>> Function() operation) async {
    try {
      setLoading();
      final items = await operation();
      setSuccess(items);
    } catch (error, stackTrace) {
      setError(
        error,
        stackTrace: stackTrace,
        canRetry: true,
        onRetry: () => loadItems(operation),
      );
    }
  }

  Future<void> refreshItems(Future<List<T>> Function() operation) async {
    try {
      setLoading(type: LoadingType.refresh, data: state.data);
      final items = await operation();
      setSuccess(items);
    } catch (error, stackTrace) {
      setError(
        error,
        stackTrace: stackTrace,
        canRetry: true,
        onRetry: () => refreshItems(operation),
        data: state.data,
      );
    }
  }
}

// Paginated state notifier
class PaginatedStateNotifier<T> extends Notifier<PaginatedState<T>> {
  @override
  PaginatedState<T> build() {
    return PaginatedState.loading();
  }

  void setLoading({
    LoadingType type = LoadingType.initial,
    String? message,
    List<T>? data,
  }) {
    state = PaginatedState.loading(type: type, message: message, data: data);
  }

  void setSuccess(
    List<T> data, {
    String? message,
    bool hasMore = false,
    int currentPage = 1,
    int totalPages = 1,
    int totalItems = 0,
  }) {
    if (data.isEmpty && currentPage == 1) {
      state = PaginatedState.success(
        [],
        message: message ?? 'No items found',
        hasMore: false,
        currentPage: currentPage,
        totalPages: totalPages,
        totalItems: totalItems,
      );
    } else {
      state = PaginatedState.success(
        data,
        message: message,
        hasMore: hasMore,
        currentPage: currentPage,
        totalPages: totalPages,
        totalItems: totalItems,
      );
    }
  }

  void setLoadingMore() {
    if (state.isSuccess && state.data != null) {
      state = PaginatedState.loadingMore(
        state.data!,
        hasMore: state.hasMore,
        currentPage: state.currentPage,
      );
    }
  }

  void appendItems(
    List<T> newItems, {
    bool hasMore = false,
    int? currentPage,
    int? totalPages,
    int? totalItems,
  }) {
    if (state.isSuccess && state.data != null) {
      final allItems = [...state.data!, ...newItems];
      state = PaginatedState.success(
        allItems,
        hasMore: hasMore,
        currentPage: currentPage ?? state.currentPage + 1,
        totalPages: totalPages ?? state.totalPages,
        totalItems: totalItems ?? state.totalItems,
      );
    }
  }

  // Paginated operations
  Future<void> loadFirstPage(
    Future<PaginationResult<T>> Function(int page) operation,
  ) async {
    try {
      setLoading();
      final result = await operation(1);
      setSuccess(
        result.items,
        hasMore: result.hasMore,
        currentPage: 1,
        totalPages: result.totalPages,
        totalItems: result.totalItems,
      );
    } catch (error) {
      state = PaginatedState.loading(
        type: LoadingType.initial,
        message: error.toString(),
      );
    }
  }

  Future<void> loadNextPage(
    Future<PaginationResult<T>> Function(int page) operation,
  ) async {
    if (!state.hasMore || state.isLoadingMore) return;

    try {
      setLoadingMore();
      final nextPage = state.currentPage + 1;
      final result = await operation(nextPage);
      appendItems(
        result.items,
        hasMore: result.hasMore,
        currentPage: nextPage,
        totalPages: result.totalPages,
        totalItems: result.totalItems,
      );
    } catch (error) {
      // Revert loading more state on error
      state = state.copyWithPagination(isLoadingMore: false);
      debugPrint('Failed to load next page: $error');
    }
  }

  Future<void> refresh(
    Future<PaginationResult<T>> Function(int page) operation,
  ) async {
    try {
      setLoading(type: LoadingType.refresh, data: state.data);
      final result = await operation(1);
      setSuccess(
        result.items,
        hasMore: result.hasMore,
        currentPage: 1,
        totalPages: result.totalPages,
        totalItems: result.totalItems,
      );
    } catch (error) {
      state = PaginatedState.loading(
        type: LoadingType.initial,
        message: error.toString(),
        data: state.data,
        hasMore: state.hasMore,
        currentPage: state.currentPage,
      );
    }
  }
}

// Pagination result model
class PaginationResult<T> {
  final List<T> items;
  final bool hasMore;
  final int totalPages;
  final int totalItems;

  const PaginationResult({
    required this.items,
    required this.hasMore,
    required this.totalPages,
    required this.totalItems,
  });
}

// Helper providers for common state patterns
final loadingStateProvider = Provider<AppState<void>>((ref) {
  return AppState.loading();
});

final emptyStateProvider = Provider<AppState<void>>((ref) {
  return AppState.empty();
});

final errorStateProvider = Provider.family<AppState<void>, String>((ref, message) {
  return AppState.error(message, canRetry: false);
});
