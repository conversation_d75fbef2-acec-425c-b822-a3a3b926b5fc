@echo off
echo ========================================
echo ShadowSuite Tool Builder - Production Build
echo ========================================
echo.

:: Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and try again
    pause
    exit /b 1
)

echo [1/6] Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Failed to clean project
    pause
    exit /b 1
)

echo [2/6] Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)

echo [3/6] Running code generation...
flutter packages pub run build_runner build --delete-conflicting-outputs
if %errorlevel% neq 0 (
    echo WARNING: Code generation failed, continuing...
)

echo [4/6] Building Windows release...
flutter build windows --release --verbose
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Windows release
    pause
    exit /b 1
)

echo [5/6] Creating distribution folder...
if not exist "dist" mkdir dist
if exist "dist\ShadowSuite_Tool_Builder" rmdir /s /q "dist\ShadowSuite_Tool_Builder"
mkdir "dist\ShadowSuite_Tool_Builder"

echo [6/6] Copying build files...
xcopy "build\windows\x64\runner\Release\*" "dist\ShadowSuite_Tool_Builder\" /E /I /Y
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy build files
    pause
    exit /b 1
)

:: Rename the executable
if exist "dist\ShadowSuite_Tool_Builder\shadowsuite.exe" (
    ren "dist\ShadowSuite_Tool_Builder\shadowsuite.exe" "ShadowSuite Tool Builder.exe"
)

:: Create a simple installer script
echo @echo off > "dist\ShadowSuite_Tool_Builder\install.bat"
echo echo Installing ShadowSuite Tool Builder... >> "dist\ShadowSuite_Tool_Builder\install.bat"
echo copy "ShadowSuite Tool Builder.exe" "%USERPROFILE%\Desktop\" >> "dist\ShadowSuite_Tool_Builder\install.bat"
echo echo Desktop shortcut created! >> "dist\ShadowSuite_Tool_Builder\install.bat"
echo pause >> "dist\ShadowSuite_Tool_Builder\install.bat"

:: Create README for distribution
echo ShadowSuite Tool Builder - Production Release > "dist\ShadowSuite_Tool_Builder\README.txt"
echo. >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo INSTALLATION: >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo 1. Run install.bat to create a desktop shortcut >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo 2. Or run "ShadowSuite Tool Builder.exe" directly >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo. >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo SYSTEM REQUIREMENTS: >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - Windows 10 or later >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - 4GB RAM minimum, 8GB recommended >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - 500MB free disk space >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo. >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo FEATURES: >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - Drag-and-drop app builder >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - 300+ Excel functions >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - Mobile app themes (Android, iOS, Web) >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - Export to Flutter projects >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo - Real-time preview >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo. >> "dist\ShadowSuite_Tool_Builder\README.txt"
echo Copyright (C) 2025 ShadowSuite. All rights reserved. >> "dist\ShadowSuite_Tool_Builder\README.txt"

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Distribution folder: dist\ShadowSuite_Tool_Builder
echo Executable: ShadowSuite Tool Builder.exe
echo.
echo To test the build:
echo 1. Navigate to dist\ShadowSuite_Tool_Builder
echo 2. Run "ShadowSuite Tool Builder.exe"
echo.
echo To create an installer:
echo 1. Use a tool like Inno Setup or NSIS
echo 2. Package the entire dist\ShadowSuite_Tool_Builder folder
echo.
pause
