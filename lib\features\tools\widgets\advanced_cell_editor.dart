import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/spreadsheet_selection_provider.dart';
import '../models/spreadsheet_cell.dart';

/// Advanced cell editor with auto-completion and formula assistance
class AdvancedCellEditor extends ConsumerStatefulWidget {
  final String cellAddress;
  final String? initialValue;
  final Function(String value) onValueChanged;
  final Function() onEditingComplete;
  final Function() onEditingCanceled;

  const AdvancedCellEditor({
    super.key,
    required this.cellAddress,
    this.initialValue,
    required this.onValueChanged,
    required this.onEditingComplete,
    required this.onEditingCanceled,
  });

  @override
  ConsumerState<AdvancedCellEditor> createState() => _AdvancedCellEditorState();
}

class _AdvancedCellEditorState extends ConsumerState<AdvancedCellEditor> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  OverlayEntry? _overlayEntry;
  List<FormulaCompletion> _completions = [];
  int _selectedCompletionIndex = -1;
  bool _showCompletions = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _focusNode = FocusNode();
    
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
      if (_controller.text.isNotEmpty) {
        _controller.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _controller.text.length,
        );
      }
    });
  }

  @override
  void dispose() {
    _removeOverlay();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    widget.onValueChanged(_controller.text);
    _updateCompletions();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _removeOverlay();
    }
  }

  void _updateCompletions() {
    final text = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;
    
    if (text.startsWith('=') && cursorPosition > 0) {
      final beforeCursor = text.substring(0, cursorPosition);
      final lastWord = _getLastWord(beforeCursor);
      
      if (lastWord.isNotEmpty) {
        _completions = _getFormulaCompletions(lastWord);
        _selectedCompletionIndex = _completions.isNotEmpty ? 0 : -1;
        _showCompletions = _completions.isNotEmpty;
        _showCompletionOverlay();
      } else {
        _hideCompletions();
      }
    } else {
      _hideCompletions();
    }
  }

  String _getLastWord(String text) {
    final match = RegExp(r'[A-Za-z_][A-Za-z0-9_]*$').firstMatch(text);
    return match?.group(0) ?? '';
  }

  List<FormulaCompletion> _getFormulaCompletions(String prefix) {
    final allFunctions = [
      // Math functions
      FormulaCompletion('SUM', 'SUM(range)', 'Adds all numbers in a range'),
      FormulaCompletion('AVERAGE', 'AVERAGE(range)', 'Calculates the average of numbers'),
      FormulaCompletion('MIN', 'MIN(range)', 'Returns the minimum value'),
      FormulaCompletion('MAX', 'MAX(range)', 'Returns the maximum value'),
      FormulaCompletion('COUNT', 'COUNT(range)', 'Counts cells containing numbers'),
      FormulaCompletion('ROUND', 'ROUND(number, digits)', 'Rounds a number to specified digits'),
      FormulaCompletion('ABS', 'ABS(number)', 'Returns absolute value'),
      FormulaCompletion('SQRT', 'SQRT(number)', 'Returns square root'),
      FormulaCompletion('POWER', 'POWER(base, exponent)', 'Raises number to a power'),
      
      // Logical functions
      FormulaCompletion('IF', 'IF(condition, true_value, false_value)', 'Returns value based on condition'),
      FormulaCompletion('AND', 'AND(condition1, condition2, ...)', 'Returns TRUE if all conditions are true'),
      FormulaCompletion('OR', 'OR(condition1, condition2, ...)', 'Returns TRUE if any condition is true'),
      FormulaCompletion('NOT', 'NOT(condition)', 'Reverses the logic of a condition'),
      
      // Lookup functions
      FormulaCompletion('VLOOKUP', 'VLOOKUP(lookup_value, table_array, col_index, exact_match)', 'Vertical lookup'),
      FormulaCompletion('INDEX', 'INDEX(array, row, column)', 'Returns value at specified position'),
      FormulaCompletion('MATCH', 'MATCH(lookup_value, lookup_array, match_type)', 'Returns position of value'),
      
      // Text functions
      FormulaCompletion('LEFT', 'LEFT(text, num_chars)', 'Returns leftmost characters'),
      FormulaCompletion('RIGHT', 'RIGHT(text, num_chars)', 'Returns rightmost characters'),
      FormulaCompletion('MID', 'MID(text, start, length)', 'Returns middle characters'),
      FormulaCompletion('LEN', 'LEN(text)', 'Returns length of text'),
      FormulaCompletion('UPPER', 'UPPER(text)', 'Converts to uppercase'),
      FormulaCompletion('LOWER', 'LOWER(text)', 'Converts to lowercase'),
      
      // Date functions
      FormulaCompletion('TODAY', 'TODAY()', 'Returns current date'),
      FormulaCompletion('NOW', 'NOW()', 'Returns current date and time'),
      FormulaCompletion('YEAR', 'YEAR(date)', 'Returns year from date'),
      FormulaCompletion('MONTH', 'MONTH(date)', 'Returns month from date'),
      FormulaCompletion('DAY', 'DAY(date)', 'Returns day from date'),
    ];

    return allFunctions
        .where((func) => func.name.toLowerCase().startsWith(prefix.toLowerCase()))
        .toList();
  }

  void _showCompletionOverlay() {
    _removeOverlay();
    
    if (!_showCompletions || _completions.isEmpty) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: 0,
        top: 0,
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 300,
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _completions.length,
              itemBuilder: (context, index) {
                final completion = _completions[index];
                final isSelected = index == _selectedCompletionIndex;
                
                return Container(
                  color: isSelected 
                      ? Theme.of(context).colorScheme.primaryContainer
                      : null,
                  child: ListTile(
                    dense: true,
                    title: Text(
                      completion.name,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isSelected 
                            ? Theme.of(context).colorScheme.onPrimaryContainer
                            : null,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          completion.syntax,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                            color: isSelected 
                                ? Theme.of(context).colorScheme.onPrimaryContainer
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Text(
                          completion.description,
                          style: TextStyle(
                            fontSize: 11,
                            color: isSelected 
                                ? Theme.of(context).colorScheme.onPrimaryContainer
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    onTap: () => _insertCompletion(completion),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideCompletions() {
    _showCompletions = false;
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _insertCompletion(FormulaCompletion completion) {
    final text = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;
    final beforeCursor = text.substring(0, cursorPosition);
    final afterCursor = text.substring(cursorPosition);
    
    final lastWordMatch = RegExp(r'[A-Za-z_][A-Za-z0-9_]*$').firstMatch(beforeCursor);
    if (lastWordMatch != null) {
      final newText = beforeCursor.substring(0, lastWordMatch.start) + 
                     completion.name + '(' + afterCursor;
      _controller.text = newText;
      _controller.selection = TextSelection.collapsed(
        offset: lastWordMatch.start + completion.name.length + 1,
      );
    }
    
    _hideCompletions();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: Theme.of(context).colorScheme.primary,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: _handleKeyEvent,
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          style: TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          decoration: InputDecoration(
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4,
            ),
            hintText: 'Enter value or formula (=)',
            hintStyle: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          onSubmitted: (_) => widget.onEditingComplete(),
        ),
      ),
    );
  }

  void _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (_showCompletions && _completions.isNotEmpty) {
        switch (event.logicalKey) {
          case LogicalKeyboardKey.arrowUp:
            setState(() {
              _selectedCompletionIndex = (_selectedCompletionIndex - 1)
                  .clamp(0, _completions.length - 1);
            });
            _showCompletionOverlay();
            return;
          case LogicalKeyboardKey.arrowDown:
            setState(() {
              _selectedCompletionIndex = (_selectedCompletionIndex + 1)
                  .clamp(0, _completions.length - 1);
            });
            _showCompletionOverlay();
            return;
          case LogicalKeyboardKey.tab:
          case LogicalKeyboardKey.enter:
            if (_selectedCompletionIndex >= 0) {
              _insertCompletion(_completions[_selectedCompletionIndex]);
              return;
            }
            break;
          case LogicalKeyboardKey.escape:
            _hideCompletions();
            return;
        }
      }

      switch (event.logicalKey) {
        case LogicalKeyboardKey.escape:
          widget.onEditingCanceled();
          break;
        case LogicalKeyboardKey.enter:
          widget.onEditingComplete();
          break;
      }
    }
  }
}

/// Formula completion data
class FormulaCompletion {
  final String name;
  final String syntax;
  final String description;

  const FormulaCompletion(this.name, this.syntax, this.description);
}
