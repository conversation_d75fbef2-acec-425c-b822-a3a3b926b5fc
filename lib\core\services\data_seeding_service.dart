import '../database/database_service.dart';
import '../../features/memo/models/note.dart';
import '../../features/memo/models/todo.dart';
import '../../features/memo/models/voice_memo.dart';
import '../../features/athkar/models/dhikr.dart';
import '../../features/athkar/models/prayer_time.dart';
import '../../features/tools/models/calculator.dart';
import '../../features/tools/models/conversion.dart';
import '../../features/tools/models/calculation_history.dart';
import '../../features/money/models/account.dart';
import '../../features/money/models/transaction.dart';
import '../../features/money/models/budget.dart';
import '../../features/money/models/financial_goal.dart';

class DataSeedingService {
  static Future<void> seedSampleData(String userId) async {
    final db = DatabaseService.instance;
    
    // Check if data already exists
    final existingNotes = await db.getAllNotes(userId: userId);
    if (existingNotes.isNotEmpty) return; // Data already seeded
    
    // Seed sample notes
    await _seedNotes(userId);
    
    // Seed sample todos
    await _seedTodos(userId);
    
    // Seed sample voice memos (metadata only)
    await _seedVoiceMemos(userId);

    // Seed Athkar data
    await _seedDhikrs(userId);
    await _seedPrayerTimes(userId);

    // Seed Tools data
    await _seedCalculators(userId);
    await _seedConversions(userId);
    await _seedCalculationHistory(userId);

    // Seed Money Flow data
    await _seedAccounts(userId);
    await _seedTransactions(userId);
    await _seedBudgets(userId);
    await _seedFinancialGoals(userId);
  }
  
  static Future<void> _seedNotes(String userId) async {
    final db = DatabaseService.instance;
    
    final sampleNotes = [
      Note.create(
        title: 'Welcome to ShadowSuite!',
        content: '''Welcome to your new productivity suite! 

ShadowSuite combines multiple mini-apps to help you stay organized:

📝 **Memo Suite**: Notes, todos, and voice memos
🕌 **Athkar Pro**: Islamic remembrance and dhikr (coming soon)
🔧 **Tools Builder**: Custom calculators and converters (coming soon)
💰 **Money Flow**: Financial tracking and budgets (coming soon)

This note demonstrates the rich text capabilities. You can:
- Create colorful notes
- Add tags for organization
- Pin important notes
- Mark favorites

Try creating your own note by tapping the + button!''',
        userId: userId,
        color: 'blue',
        isPinned: true,
        tags: ['welcome', 'getting-started'],
      ),
      
      Note.create(
        title: 'Meeting Notes - Project Alpha',
        content: '''Project Alpha Kickoff Meeting
Date: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}

Attendees:
- John Smith (Project Manager)
- Sarah Johnson (Lead Developer)
- Mike Chen (Designer)

Key Points:
1. Project timeline: 3 months
2. Budget approved: \$50,000
3. First milestone: End of month 1
4. Weekly standup meetings on Mondays

Action Items:
- Set up project repository (Sarah)
- Create initial wireframes (Mike)
- Schedule client meeting (John)

Next Meeting: Next Monday at 10 AM''',
        userId: userId,
        color: 'yellow',
        tags: ['work', 'meetings', 'project-alpha'],
      ),
      
      Note.create(
        title: 'Recipe: Chocolate Chip Cookies',
        content: '''Ingredients:
- 2¼ cups all-purpose flour
- 1 tsp baking soda
- 1 tsp salt
- 1 cup butter, softened
- ¾ cup granulated sugar
- ¾ cup brown sugar
- 2 large eggs
- 2 tsp vanilla extract
- 2 cups chocolate chips

Instructions:
1. Preheat oven to 375°F
2. Mix flour, baking soda, and salt in a bowl
3. Cream butter and sugars until fluffy
4. Beat in eggs and vanilla
5. Gradually blend in flour mixture
6. Stir in chocolate chips
7. Drop rounded tablespoons onto ungreased cookie sheets
8. Bake 9-11 minutes until golden brown

Makes about 5 dozen cookies!''',
        userId: userId,
        color: 'orange',
        isFavorite: true,
        tags: ['recipes', 'baking', 'desserts'],
      ),
      
      Note.create(
        title: 'Book Ideas',
        content: '''Ideas for my next novel:

1. **Time Travel Café**
   - A café where each table takes you to a different time period
   - Protagonist is a barista who discovers the secret
   - Romance subplot with a customer from the 1920s

2. **Digital Detox Island**
   - Mysterious island where technology doesn't work
   - People rediscover human connections
   - Thriller element: someone is controlling the island

3. **Memory Thief**
   - Dystopian future where memories can be stolen
   - Protagonist loses their memories and must recover them
   - Discovers a conspiracy involving memory manipulation

4. **The Last Library**
   - Post-apocalyptic world where books are banned
   - Underground network of librarians preserving knowledge
   - Young protagonist discovers their family's secret''',
        userId: userId,
        color: 'purple',
        tags: ['writing', 'ideas', 'creative'],
      ),
      
      Note.create(
        title: 'Travel Bucket List',
        content: '''Places I want to visit:

🌍 **Europe**
- Iceland (Northern Lights)
- Norway (Fjords)
- Greece (Santorini)
- Italy (Tuscany)

🌏 **Asia**
- Japan (Cherry Blossoms)
- Thailand (Temples)
- Nepal (Everest Base Camp)
- India (Taj Mahal)

🌎 **Americas**
- Peru (Machu Picchu)
- Chile (Atacama Desert)
- Canada (Banff National Park)
- USA (Yellowstone)

🌍 **Africa**
- Morocco (Sahara Desert)
- Kenya (Safari)
- Egypt (Pyramids)
- South Africa (Cape Town)

💰 Estimated budget: \$25,000
📅 Goal: Visit 2-3 places per year''',
        userId: userId,
        color: 'green',
        tags: ['travel', 'bucket-list', 'goals'],
      ),
    ];
    
    for (final note in sampleNotes) {
      await db.saveNote(note);
    }
  }
  
  static Future<void> _seedTodos(String userId) async {
    final db = DatabaseService.instance;
    
    final sampleTodos = [
      Todo.create(
        title: 'Complete project proposal',
        description: 'Finish the Q1 project proposal and send to stakeholders',
        userId: userId,
        dueDate: DateTime.now().add(const Duration(days: 3)),
        priority: TodoPriority.high,
        category: 'work',
        tags: ['urgent', 'project'],
      ),
      
      Todo.create(
        title: 'Buy groceries',
        description: 'Milk, bread, eggs, fruits, vegetables',
        userId: userId,
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: TodoPriority.medium,
        category: 'personal',
        tags: ['shopping', 'food'],
      ),
      
      Todo.create(
        title: 'Schedule dentist appointment',
        description: 'Annual checkup and cleaning',
        userId: userId,
        priority: TodoPriority.low,
        category: 'health',
        tags: ['health', 'appointment'],
      ),
      
      Todo.create(
        title: 'Learn Flutter animations',
        description: 'Complete the Flutter animations course on YouTube',
        userId: userId,
        dueDate: DateTime.now().add(const Duration(days: 7)),
        priority: TodoPriority.medium,
        category: 'education',
        tags: ['learning', 'flutter', 'development'],
      ),
      
      Todo.create(
        title: 'Plan weekend trip',
        description: 'Research destinations and book accommodation',
        userId: userId,
        dueDate: DateTime.now().add(const Duration(days: 5)),
        priority: TodoPriority.low,
        category: 'personal',
        tags: ['travel', 'planning'],
      ),
      
      // Some completed todos
      Todo.create(
        title: 'Set up development environment',
        description: 'Install Flutter, VS Code, and necessary extensions',
        userId: userId,
        priority: TodoPriority.high,
        category: 'work',
        tags: ['setup', 'development'],
      )..complete(),
      
      Todo.create(
        title: 'Read "Clean Code" book',
        description: 'Finish reading the Clean Code book by Robert Martin',
        userId: userId,
        priority: TodoPriority.medium,
        category: 'education',
        tags: ['reading', 'programming'],
      )..complete(),
    ];
    
    for (final todo in sampleTodos) {
      await db.saveTodo(todo);
    }
  }
  
  static Future<void> _seedVoiceMemos(String userId) async {
    final db = DatabaseService.instance;
    
    final sampleMemos = [
      VoiceMemo.create(
        title: 'Meeting recap - Client call',
        filePath: '/fake/path/meeting_recap.aac',
        durationMs: 180000, // 3 minutes
        fileSizeBytes: 1024 * 512, // 512 KB
        userId: userId,
        description: 'Quick recap of the client call discussing project requirements',
        tags: ['work', 'client', 'meeting'],
      ),
      
      VoiceMemo.create(
        title: 'Song idea - Melody',
        filePath: '/fake/path/song_melody.aac',
        durationMs: 45000, // 45 seconds
        fileSizeBytes: 1024 * 128, // 128 KB
        userId: userId,
        description: 'Humming a melody that came to mind',
        isFavorite: true,
        tags: ['music', 'creative', 'ideas'],
      ),
      
      VoiceMemo.create(
        title: 'Grocery list',
        filePath: '/fake/path/grocery_list.aac',
        durationMs: 30000, // 30 seconds
        fileSizeBytes: 1024 * 64, // 64 KB
        userId: userId,
        description: 'Voice note of items to buy at the store',
        tags: ['shopping', 'personal'],
      ),
    ];
    
    for (final memo in sampleMemos) {
      await db.saveVoiceMemo(memo);
    }
  }

  static Future<void> _seedDhikrs(String userId) async {
    final db = DatabaseService.instance;

    final sampleDhikrs = [
      // Morning Dhikrs
      Dhikr.create(
        arabicText: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
        transliteration: 'Subhan Allah wa bihamdihi',
        translation: 'Glory is to Allah and praise is to Him',
        userId: userId,
        category: 'morning',
        recommendedCount: 100,
        reference: 'Sahih Muslim',
      ),

      Dhikr.create(
        arabicText: 'سُبْحَانَ اللَّهِ الْعَظِيمِ وَبِحَمْدِهِ',
        transliteration: 'Subhan Allah al-Azeem wa bihamdihi',
        translation: 'Glory is to Allah, the Magnificent, and praise is to Him',
        userId: userId,
        category: 'morning',
        recommendedCount: 100,
        reference: 'Sahih Bukhari',
      ),

      Dhikr.create(
        arabicText: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ',
        transliteration: 'La ilaha illa Allah wahdahu la shareeka lah',
        translation: 'There is no god but Allah alone, with no partner',
        userId: userId,
        category: 'morning',
        recommendedCount: 100,
        reference: 'Sahih Bukhari',
      ),

      // Evening Dhikrs
      Dhikr.create(
        arabicText: 'أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ وَأَتُوبُ إِلَيْهِ',
        transliteration: 'Astaghfir Allah al-Azeem alladhi la ilaha illa huwa al-Hayy al-Qayyum wa atubu ilayh',
        translation: 'I seek forgiveness from Allah the Magnificent, there is no god but He, the Living, the Eternal, and I repent to Him',
        userId: userId,
        category: 'evening',
        recommendedCount: 100,
        reference: 'Abu Dawud',
      ),

      Dhikr.create(
        arabicText: 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ',
        transliteration: 'Allahumma anta rabbi la ilaha illa ant',
        translation: 'O Allah, You are my Lord, there is no god but You',
        userId: userId,
        category: 'evening',
        recommendedCount: 100,
        reference: 'Sahih Bukhari',
      ),

      // General Dhikrs
      Dhikr.create(
        arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        transliteration: 'Alhamdu lillahi rabbil alameen',
        translation: 'All praise is due to Allah, Lord of all the worlds',
        userId: userId,
        category: 'general',
        recommendedCount: 33,
        reference: 'Quran 1:2',
      ),

      Dhikr.create(
        arabicText: 'اللَّهُ أَكْبَرُ',
        transliteration: 'Allahu Akbar',
        translation: 'Allah is the Greatest',
        userId: userId,
        category: 'general',
        recommendedCount: 34,
        reference: 'Sahih Muslim',
      ),

      Dhikr.create(
        arabicText: 'سُبْحَانَ اللَّهِ',
        transliteration: 'Subhan Allah',
        translation: 'Glory be to Allah',
        userId: userId,
        category: 'general',
        recommendedCount: 33,
        reference: 'Sahih Muslim',
      ),

      // After Prayer Dhikrs
      Dhikr.create(
        arabicText: 'أَسْتَغْفِرُ اللَّهَ',
        transliteration: 'Astaghfir Allah',
        translation: 'I seek forgiveness from Allah',
        userId: userId,
        category: 'after-prayer',
        recommendedCount: 3,
        reference: 'Sahih Muslim',
      ),

      Dhikr.create(
        arabicText: 'اللَّهُمَّ أَنْتَ السَّلَامُ وَمِنْكَ السَّلَامُ',
        transliteration: 'Allahumma anta as-salam wa minka as-salam',
        translation: 'O Allah, You are Peace and from You comes peace',
        userId: userId,
        category: 'after-prayer',
        recommendedCount: 1,
        reference: 'Sahih Muslim',
      ),
    ];

    for (final dhikr in sampleDhikrs) {
      await db.saveDhikr(dhikr);
    }
  }

  static Future<void> _seedPrayerTimes(String userId) async {
    final db = DatabaseService.instance;

    // Generate sample prayer times for today
    final today = DateTime.now();
    final samplePrayerTimes = [
      PrayerTime.create(
        prayerType: PrayerType.fajr,
        prayerTime: DateTime(today.year, today.month, today.day, 5, 30),
        userId: userId,
        locationName: 'Sample Location',
      ),

      PrayerTime.create(
        prayerType: PrayerType.dhuhr,
        prayerTime: DateTime(today.year, today.month, today.day, 12, 15),
        userId: userId,
        locationName: 'Sample Location',
      ),

      PrayerTime.create(
        prayerType: PrayerType.asr,
        prayerTime: DateTime(today.year, today.month, today.day, 15, 45),
        userId: userId,
        locationName: 'Sample Location',
      ),

      PrayerTime.create(
        prayerType: PrayerType.maghrib,
        prayerTime: DateTime(today.year, today.month, today.day, 18, 20),
        userId: userId,
        locationName: 'Sample Location',
      ),

      PrayerTime.create(
        prayerType: PrayerType.isha,
        prayerTime: DateTime(today.year, today.month, today.day, 19, 45),
        userId: userId,
        locationName: 'Sample Location',
      ),
    ];

    for (final prayerTime in samplePrayerTimes) {
      await db.savePrayerTime(prayerTime);
    }
  }

  static Future<void> _seedCalculators(String userId) async {
    final db = DatabaseService.instance;

    final sampleCalculators = [
      Calculator.create(
        name: 'Basic Calculator',
        description: 'Standard arithmetic operations',
        type: CalculatorType.basic,
        userId: userId,
        configuration: {
          'precision': 10,
          'memoryEnabled': true,
        },
      ),

      Calculator.create(
        name: 'Scientific Calculator',
        description: 'Advanced mathematical functions',
        type: CalculatorType.scientific,
        userId: userId,
        configuration: {
          'trigonometricFunctions': true,
          'logarithmicFunctions': true,
          'exponentialFunctions': true,
        },
      ),

      Calculator.create(
        name: 'Mortgage Calculator',
        description: 'Calculate loan payments and interest',
        type: CalculatorType.mortgage,
        userId: userId,
        configuration: {
          'defaultTerm': 30,
          'defaultRate': 3.5,
          'currency': 'USD',
        },
      ),

      Calculator.create(
        name: 'Tip Calculator',
        description: 'Calculate tips and split bills',
        type: CalculatorType.tip,
        userId: userId,
        configuration: {
          'defaultTipPercentage': 15,
          'maxPeople': 20,
        },
      ),

      Calculator.create(
        name: 'BMI Calculator',
        description: 'Body Mass Index calculator',
        type: CalculatorType.bmi,
        userId: userId,
        configuration: {
          'units': 'metric',
          'showCategories': true,
        },
      ),
    ];

    for (final calculator in sampleCalculators) {
      await db.saveCalculator(calculator);
    }
  }

  static Future<void> _seedConversions(String userId) async {
    final db = DatabaseService.instance;

    final sampleConversions = [
      Conversion.create(
        category: ConversionCategory.length,
        fromUnit: 'm',
        toUnit: 'ft',
        inputValue: 1.0,
        outputValue: 3.28084,
        userId: userId,
      ),

      Conversion.create(
        category: ConversionCategory.weight,
        fromUnit: 'kg',
        toUnit: 'lb',
        inputValue: 1.0,
        outputValue: 2.20462,
        userId: userId,
      ),

      Conversion.create(
        category: ConversionCategory.temperature,
        fromUnit: '°C',
        toUnit: '°F',
        inputValue: 0.0,
        outputValue: 32.0,
        userId: userId,
      ),

      Conversion.create(
        category: ConversionCategory.volume,
        fromUnit: 'l',
        toUnit: 'gal',
        inputValue: 1.0,
        outputValue: 0.264172,
        userId: userId,
      ),

      Conversion.create(
        category: ConversionCategory.length,
        fromUnit: 'mi',
        toUnit: 'km',
        inputValue: 1.0,
        outputValue: 1.60934,
        userId: userId,
      ),
    ];

    for (final conversion in sampleConversions) {
      await db.saveConversion(conversion);
    }
  }

  static Future<void> _seedCalculationHistory(String userId) async {
    final db = DatabaseService.instance;

    final sampleHistory = [
      CalculationHistory.create(
        type: CalculationType.basic,
        expression: '15 + 25',
        result: '40',
        userId: userId,
        calculatorName: 'Basic Calculator',
        inputs: {'operand1': 15, 'operator': '+', 'operand2': 25},
        outputs: {'result': 40},
      ),

      CalculationHistory.create(
        type: CalculationType.basic,
        expression: '100 - 37',
        result: '63',
        userId: userId,
        calculatorName: 'Basic Calculator',
        inputs: {'operand1': 100, 'operator': '-', 'operand2': 37},
        outputs: {'result': 63},
      ),

      CalculationHistory.create(
        type: CalculationType.basic,
        expression: '12 × 8',
        result: '96',
        userId: userId,
        calculatorName: 'Basic Calculator',
        inputs: {'operand1': 12, 'operator': '×', 'operand2': 8},
        outputs: {'result': 96},
      ),

      CalculationHistory.create(
        type: CalculationType.conversion,
        expression: '5 km to miles',
        result: '3.11 mi',
        userId: userId,
        calculatorName: 'Unit Converter',
        inputs: {'value': 5, 'fromUnit': 'km', 'toUnit': 'mi'},
        outputs: {'result': 3.10686, 'formattedResult': '3.11 mi'},
      ),

      CalculationHistory.create(
        type: CalculationType.tip,
        expression: 'Tip for \$50 bill (18%)',
        result: '\$9.00',
        userId: userId,
        calculatorName: 'Tip Calculator',
        inputs: {'billAmount': 50, 'tipPercentage': 18, 'people': 1},
        outputs: {'tipAmount': 9.0, 'totalAmount': 59.0},
      ),
    ];

    for (final history in sampleHistory) {
      await db.saveCalculationHistory(history);
    }
  }

  static Future<void> _seedAccounts(String userId) async {
    final db = DatabaseService.instance;

    final sampleAccounts = [
      Account.create(
        name: 'Main Checking',
        type: AccountType.checking,
        userId: userId,
        description: 'Primary checking account for daily expenses',
        balance: 2500.00,
        currency: 'USD',
        bankName: 'Chase Bank',
        accountNumber: '****1234',
        isActive: true,
        includeInTotal: true,
      ),

      Account.create(
        name: 'Emergency Savings',
        type: AccountType.savings,
        userId: userId,
        description: 'Emergency fund savings account',
        balance: 15000.00,
        currency: 'USD',
        bankName: 'Chase Bank',
        accountNumber: '****5678',
        isActive: true,
        includeInTotal: true,
      ),

      Account.create(
        name: 'Credit Card',
        type: AccountType.credit,
        userId: userId,
        description: 'Main credit card for purchases',
        balance: -850.00,
        currency: 'USD',
        bankName: 'Capital One',
        accountNumber: '****9012',
        isActive: true,
        includeInTotal: true,
      ),

      Account.create(
        name: 'Investment Portfolio',
        type: AccountType.investment,
        userId: userId,
        description: 'Stock and bond investments',
        balance: 25000.00,
        currency: 'USD',
        bankName: 'Fidelity',
        accountNumber: '****3456',
        isActive: true,
        includeInTotal: true,
      ),

      Account.create(
        name: 'Cash Wallet',
        type: AccountType.cash,
        userId: userId,
        description: 'Physical cash on hand',
        balance: 150.00,
        currency: 'USD',
        isActive: true,
        includeInTotal: true,
      ),
    ];

    for (final account in sampleAccounts) {
      await db.saveAccount(account);
    }
  }

  static Future<void> _seedTransactions(String userId) async {
    final db = DatabaseService.instance;

    final now = DateTime.now();
    final sampleTransactions = [
      // Recent income
      Transaction.create(
        type: TransactionType.income,
        amount: 3500.00,
        description: 'Monthly Salary',
        category: 'Salary',
        accountId: 1, // Main Checking
        userId: userId,
        transactionDate: DateTime(now.year, now.month, 1),
        tags: ['work', 'monthly'],
      ),

      // Recent expenses
      Transaction.create(
        type: TransactionType.expense,
        amount: 85.50,
        description: 'Grocery Shopping',
        category: 'Food & Dining',
        accountId: 1,
        userId: userId,
        transactionDate: now.subtract(const Duration(days: 1)),
        tags: ['groceries', 'weekly'],
      ),

      Transaction.create(
        type: TransactionType.expense,
        amount: 45.00,
        description: 'Gas Station',
        category: 'Transportation',
        accountId: 1,
        userId: userId,
        transactionDate: now.subtract(const Duration(days: 2)),
        tags: ['gas', 'car'],
      ),

      Transaction.create(
        type: TransactionType.expense,
        amount: 12.99,
        description: 'Netflix Subscription',
        category: 'Entertainment',
        accountId: 3, // Credit Card
        userId: userId,
        transactionDate: now.subtract(const Duration(days: 3)),
        tags: ['subscription', 'streaming'],
        isRecurring: true,
        recurringPattern: 'monthly',
      ),

      Transaction.create(
        type: TransactionType.expense,
        amount: 150.00,
        description: 'Electric Bill',
        category: 'Bills & Utilities',
        accountId: 1,
        userId: userId,
        transactionDate: now.subtract(const Duration(days: 5)),
        tags: ['utilities', 'monthly'],
      ),

      Transaction.create(
        type: TransactionType.expense,
        amount: 25.00,
        description: 'Coffee Shop',
        category: 'Food & Dining',
        accountId: 1,
        userId: userId,
        transactionDate: now.subtract(const Duration(days: 7)),
        tags: ['coffee', 'treat'],
      ),

      // Transfer
      Transaction.create(
        type: TransactionType.transfer,
        amount: 500.00,
        description: 'Transfer to Savings',
        category: 'Transfer',
        accountId: 1, // From checking
        toAccountId: 2, // To savings
        userId: userId,
        transactionDate: now.subtract(const Duration(days: 10)),
        tags: ['savings', 'monthly'],
      ),
    ];

    for (final transaction in sampleTransactions) {
      await db.saveTransaction(transaction);
    }
  }

  static Future<void> _seedBudgets(String userId) async {
    final db = DatabaseService.instance;

    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);

    final sampleBudgets = [
      Budget.create(
        name: 'Food & Dining Budget',
        category: 'Food & Dining',
        budgetAmount: 400.00,
        period: BudgetPeriod.monthly,
        userId: userId,
        description: 'Monthly budget for groceries and dining out',
        startDate: monthStart,
        warningThreshold: 0.8,
        notificationsEnabled: true,
      ),

      Budget.create(
        name: 'Transportation Budget',
        category: 'Transportation',
        budgetAmount: 200.00,
        period: BudgetPeriod.monthly,
        userId: userId,
        description: 'Monthly budget for gas and car expenses',
        startDate: monthStart,
        warningThreshold: 0.75,
        notificationsEnabled: true,
      ),

      Budget.create(
        name: 'Entertainment Budget',
        category: 'Entertainment',
        budgetAmount: 100.00,
        period: BudgetPeriod.monthly,
        userId: userId,
        description: 'Monthly budget for entertainment and subscriptions',
        startDate: monthStart,
        warningThreshold: 0.9,
        notificationsEnabled: true,
      ),

      Budget.create(
        name: 'Bills & Utilities Budget',
        category: 'Bills & Utilities',
        budgetAmount: 300.00,
        period: BudgetPeriod.monthly,
        userId: userId,
        description: 'Monthly budget for utilities and bills',
        startDate: monthStart,
        warningThreshold: 0.85,
        notificationsEnabled: false,
      ),
    ];

    // Set spent amounts based on transactions
    sampleBudgets[0].spentAmount = 110.50; // Food & Dining
    sampleBudgets[1].spentAmount = 45.00;  // Transportation
    sampleBudgets[2].spentAmount = 12.99;  // Entertainment
    sampleBudgets[3].spentAmount = 150.00; // Bills & Utilities

    for (final budget in sampleBudgets) {
      await db.saveBudget(budget);
    }
  }

  static Future<void> _seedFinancialGoals(String userId) async {
    final db = DatabaseService.instance;

    final now = DateTime.now();
    final sampleGoals = [
      FinancialGoal.create(
        name: 'Emergency Fund',
        type: GoalType.emergency,
        targetAmount: 20000.00,
        targetDate: DateTime(now.year + 1, now.month, now.day),
        userId: userId,
        description: '6 months of expenses for emergency fund',
        currentAmount: 15000.00,
        monthlyContribution: 500.00,
        autoContribute: true,
        linkedAccountId: 2, // Emergency Savings
      ),

      FinancialGoal.create(
        name: 'Vacation Fund',
        type: GoalType.savings,
        targetAmount: 5000.00,
        targetDate: DateTime(now.year, now.month + 8, now.day),
        userId: userId,
        description: 'Save for summer vacation to Europe',
        currentAmount: 1200.00,
        monthlyContribution: 400.00,
        autoContribute: false,
      ),

      FinancialGoal.create(
        name: 'New Car Down Payment',
        type: GoalType.purchase,
        targetAmount: 8000.00,
        targetDate: DateTime(now.year + 1, now.month + 6, now.day),
        userId: userId,
        description: 'Save for down payment on new car',
        currentAmount: 2500.00,
        monthlyContribution: 300.00,
        autoContribute: true,
      ),

      FinancialGoal.create(
        name: 'Credit Card Payoff',
        type: GoalType.debt,
        targetAmount: 850.00,
        targetDate: DateTime(now.year, now.month + 4, now.day),
        userId: userId,
        description: 'Pay off credit card debt completely',
        currentAmount: 200.00,
        monthlyContribution: 200.00,
        autoContribute: true,
        linkedAccountId: 3, // Credit Card
      ),
    ];

    for (final goal in sampleGoals) {
      await db.saveFinancialGoal(goal);
    }
  }
}
