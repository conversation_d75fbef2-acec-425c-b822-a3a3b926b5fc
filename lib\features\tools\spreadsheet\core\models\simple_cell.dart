import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Simple cell model for basic spreadsheet functionality
@immutable
class SimpleCell {
  final String address;
  final dynamic value;
  final String? formula;
  final CellType type;
  final DateTime lastModified;

  const SimpleCell({
    required this.address,
    this.value,
    this.formula,
    this.type = CellType.text,
    required this.lastModified,
  });

  /// Create cell with value
  factory SimpleCell.withValue(String address, dynamic value) {
    CellType type = CellType.text;
    
    if (value is num) {
      type = CellType.number;
    } else if (value is bool) {
      type = CellType.boolean;
    } else if (value is DateTime) {
      type = CellType.date;
    }

    return SimpleCell(
      address: address,
      value: value,
      type: type,
      lastModified: DateTime.now(),
    );
  }

  /// Create cell with formula
  factory SimpleCell.withFormula(String address, String formula, [dynamic calculatedValue]) {
    return SimpleCell(
      address: address,
      value: calculatedValue,
      formula: formula,
      type: CellType.formula,
      lastModified: DateTime.now(),
    );
  }

  /// Get display value
  String get displayValue {
    // For formula cells, show the calculated value if available
    if (type == CellType.formula && value != null) {
      if (value is num) {
        return value.toString();
      } else if (value is bool) {
        return value ? 'TRUE' : 'FALSE';
      } else if (value is DateTime) {
        return '${value.day}/${value.month}/${value.year}';
      } else {
        return value.toString();
      }
    }

    // For non-formula cells or formulas without calculated values
    if (value == null) return '';

    if (value is num) {
      return value.toString();
    } else if (value is bool) {
      return value ? 'TRUE' : 'FALSE';
    } else if (value is DateTime) {
      return '${value.day}/${value.month}/${value.year}';
    } else {
      return value.toString();
    }
  }

  /// Get formula for editing (shows the actual formula)
  String get editValue {
    if (formula != null && formula!.isNotEmpty) {
      return formula!;
    }
    return displayValue;
  }

  /// Check if cell is empty
  bool get isEmpty => value == null && (formula == null || formula!.isEmpty);

  /// Copy with modifications
  SimpleCell copyWith({
    String? address,
    dynamic value,
    String? formula,
    CellType? type,
    DateTime? lastModified,
  }) {
    return SimpleCell(
      address: address ?? this.address,
      value: value ?? this.value,
      formula: formula ?? this.formula,
      type: type ?? this.type,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'value': value,
      'formula': formula,
      'type': type.name,
      'lastModified': lastModified.toIso8601String(),
    };
  }

  /// Create from JSON
  factory SimpleCell.fromJson(Map<String, dynamic> json) {
    return SimpleCell(
      address: json['address'],
      value: json['value'],
      formula: json['formula'],
      type: CellType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CellType.text,
      ),
      lastModified: DateTime.parse(json['lastModified']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SimpleCell &&
        other.address == address &&
        other.value == value &&
        other.formula == formula &&
        other.type == type;
  }

  @override
  int get hashCode => Object.hash(address, value, formula, type);

  @override
  String toString() => 'SimpleCell($address: $displayValue)';
}

/// Cell types
enum CellType {
  text,
  number,
  boolean,
  date,
  formula,
  error,
}

/// Cell coordinates helper
class CellCoordinates {
  final int row;
  final int column;

  const CellCoordinates(this.row, this.column);

  /// Convert to address (e.g., A1, B2)
  String get address {
    String columnName = '';
    int col = column;
    
    while (col > 0) {
      col--;
      columnName = String.fromCharCode(65 + (col % 26)) + columnName;
      col ~/= 26;
    }
    
    return '$columnName$row';
  }

  /// Create from address
  factory CellCoordinates.fromAddress(String address) {
    final match = RegExp(r'^([A-Z]+)(\d+)$').firstMatch(address.toUpperCase());
    if (match == null) {
      throw ArgumentError('Invalid cell address: $address');
    }

    final columnStr = match.group(1)!;
    final rowStr = match.group(2)!;

    int column = 0;
    for (int i = 0; i < columnStr.length; i++) {
      column = column * 26 + (columnStr.codeUnitAt(i) - 64);
    }

    final row = int.parse(rowStr);

    return CellCoordinates(row, column);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CellCoordinates && other.row == row && other.column == column;
  }

  @override
  int get hashCode => Object.hash(row, column);

  @override
  String toString() => address;
}
