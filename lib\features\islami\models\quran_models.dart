import 'package:flutter/foundation.dart';

/// Quran Surah model
class Surah {
  final int number;
  final String nameArabic;
  final String nameEnglish;
  final String nameTransliteration;
  final int versesCount;
  final String revelationType; // 'Meccan' or 'Medinan'
  final int revelationOrder;
  final List<Verse> verses;

  const Sur<PERSON>({
    required this.number,
    required this.nameArabic,
    required this.nameEnglish,
    required this.nameTransliteration,
    required this.versesCount,
    required this.revelationType,
    required this.revelationOrder,
    this.verses = const [],
  });

  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'] as int,
      nameArabic: json['name_arabic'] as String,
      nameEnglish: json['name_english'] as String,
      nameTransliteration: json['name_transliteration'] as String,
      versesCount: json['verses_count'] as int,
      revelationType: json['revelation_type'] as String,
      revelationOrder: json['revelation_order'] as int,
      verses: (json['verses'] as List<dynamic>?)
          ?.map((v) => Verse.fromJson(v as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name_arabic': nameArabic,
      'name_english': nameEnglish,
      'name_transliteration': nameTransliteration,
      'verses_count': versesCount,
      'revelation_type': revelationType,
      'revelation_order': revelationOrder,
      'verses': verses.map((v) => v.toJson()).toList(),
    };
  }
}

/// Quran Verse model
class Verse {
  final int number;
  final String textArabic;
  final String textTransliteration;
  final String textTranslation;
  final int surahNumber;
  final int juzNumber;
  final int hizbNumber;
  final int rukuNumber;
  final int manzilNumber;
  final int pageNumber;

  const Verse({
    required this.number,
    required this.textArabic,
    required this.textTransliteration,
    required this.textTranslation,
    required this.surahNumber,
    this.juzNumber = 1,
    this.hizbNumber = 1,
    this.rukuNumber = 1,
    this.manzilNumber = 1,
    this.pageNumber = 1,
  });

  factory Verse.fromJson(Map<String, dynamic> json) {
    return Verse(
      number: json['number'] as int,
      textArabic: json['text_arabic'] as String,
      textTransliteration: json['text_transliteration'] as String? ?? '',
      textTranslation: json['text_translation'] as String? ?? '',
      surahNumber: json['surah_number'] as int,
      juzNumber: json['juz_number'] as int? ?? 1,
      hizbNumber: json['hizb_number'] as int? ?? 1,
      rukuNumber: json['ruku_number'] as int? ?? 1,
      manzilNumber: json['manzil_number'] as int? ?? 1,
      pageNumber: json['page_number'] as int? ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text_arabic': textArabic,
      'text_transliteration': textTransliteration,
      'text_translation': textTranslation,
      'surah_number': surahNumber,
      'juz_number': juzNumber,
      'hizb_number': hizbNumber,
      'ruku_number': rukuNumber,
      'manzil_number': manzilNumber,
      'page_number': pageNumber,
    };
  }

  String get verseReference => '$surahNumber:$number';
}

/// Bookmark model for Quran verses
class QuranBookmark {
  final String id;
  final int surahNumber;
  final int verseNumber;
  final String title;
  final String note;
  final DateTime createdAt;
  final DateTime? lastReadAt;
  final List<String> tags;

  const QuranBookmark({
    required this.id,
    required this.surahNumber,
    required this.verseNumber,
    required this.title,
    this.note = '',
    required this.createdAt,
    this.lastReadAt,
    this.tags = const [],
  });

  factory QuranBookmark.fromJson(Map<String, dynamic> json) {
    return QuranBookmark(
      id: json['id'] as String,
      surahNumber: json['surah_number'] as int,
      verseNumber: json['verse_number'] as int,
      title: json['title'] as String,
      note: json['note'] as String? ?? '',
      createdAt: DateTime.parse(json['created_at'] as String),
      lastReadAt: json['last_read_at'] != null 
          ? DateTime.parse(json['last_read_at'] as String)
          : null,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((t) => t as String)
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surah_number': surahNumber,
      'verse_number': verseNumber,
      'title': title,
      'note': note,
      'created_at': createdAt.toIso8601String(),
      'last_read_at': lastReadAt?.toIso8601String(),
      'tags': tags,
    };
  }

  String get verseReference => '$surahNumber:$verseNumber';
}

/// Reading progress model
class ReadingProgress {
  final String id;
  final int surahNumber;
  final int verseNumber;
  final DateTime lastReadAt;
  final int totalReadingTime; // in seconds
  final int sessionCount;
  final double completionPercentage;

  const ReadingProgress({
    required this.id,
    required this.surahNumber,
    required this.verseNumber,
    required this.lastReadAt,
    this.totalReadingTime = 0,
    this.sessionCount = 0,
    this.completionPercentage = 0.0,
  });

  factory ReadingProgress.fromJson(Map<String, dynamic> json) {
    return ReadingProgress(
      id: json['id'] as String,
      surahNumber: json['surah_number'] as int,
      verseNumber: json['verse_number'] as int,
      lastReadAt: DateTime.parse(json['last_read_at'] as String),
      totalReadingTime: json['total_reading_time'] as int? ?? 0,
      sessionCount: json['session_count'] as int? ?? 0,
      completionPercentage: (json['completion_percentage'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surah_number': surahNumber,
      'verse_number': verseNumber,
      'last_read_at': lastReadAt.toIso8601String(),
      'total_reading_time': totalReadingTime,
      'session_count': sessionCount,
      'completion_percentage': completionPercentage,
    };
  }

  String get verseReference => '$surahNumber:$verseNumber';
}

/// Search result model
class QuranSearchResult {
  final Verse verse;
  final Surah surah;
  final String highlightedText;
  final double relevanceScore;

  const QuranSearchResult({
    required this.verse,
    required this.surah,
    required this.highlightedText,
    this.relevanceScore = 0.0,
  });

  String get verseReference => '${surah.nameEnglish} ${verse.number}';
}

/// Quran reading settings
class QuranSettings {
  final String arabicFont;
  final double arabicFontSize;
  final double translationFontSize;
  final bool showTransliteration;
  final bool showTranslation;
  final String translationLanguage;
  final bool nightMode;
  final double lineSpacing;
  final String backgroundColor;
  final String textColor;
  final bool autoScroll;
  final int autoScrollSpeed;

  const QuranSettings({
    this.arabicFont = 'Amiri',
    this.arabicFontSize = 24.0,
    this.translationFontSize = 16.0,
    this.showTransliteration = true,
    this.showTranslation = true,
    this.translationLanguage = 'en',
    this.nightMode = false,
    this.lineSpacing = 1.5,
    this.backgroundColor = '#FFFFFF',
    this.textColor = '#000000',
    this.autoScroll = false,
    this.autoScrollSpeed = 1,
  });

  factory QuranSettings.fromJson(Map<String, dynamic> json) {
    return QuranSettings(
      arabicFont: json['arabic_font'] as String? ?? 'Amiri',
      arabicFontSize: (json['arabic_font_size'] as num?)?.toDouble() ?? 24.0,
      translationFontSize: (json['translation_font_size'] as num?)?.toDouble() ?? 16.0,
      showTransliteration: json['show_transliteration'] as bool? ?? true,
      showTranslation: json['show_translation'] as bool? ?? true,
      translationLanguage: json['translation_language'] as String? ?? 'en',
      nightMode: json['night_mode'] as bool? ?? false,
      lineSpacing: (json['line_spacing'] as num?)?.toDouble() ?? 1.5,
      backgroundColor: json['background_color'] as String? ?? '#FFFFFF',
      textColor: json['text_color'] as String? ?? '#000000',
      autoScroll: json['auto_scroll'] as bool? ?? false,
      autoScrollSpeed: json['auto_scroll_speed'] as int? ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'arabic_font': arabicFont,
      'arabic_font_size': arabicFontSize,
      'translation_font_size': translationFontSize,
      'show_transliteration': showTransliteration,
      'show_translation': showTranslation,
      'translation_language': translationLanguage,
      'night_mode': nightMode,
      'line_spacing': lineSpacing,
      'background_color': backgroundColor,
      'text_color': textColor,
      'auto_scroll': autoScroll,
      'auto_scroll_speed': autoScrollSpeed,
    };
  }

  QuranSettings copyWith({
    String? arabicFont,
    double? arabicFontSize,
    double? translationFontSize,
    bool? showTransliteration,
    bool? showTranslation,
    String? translationLanguage,
    bool? nightMode,
    double? lineSpacing,
    String? backgroundColor,
    String? textColor,
    bool? autoScroll,
    int? autoScrollSpeed,
  }) {
    return QuranSettings(
      arabicFont: arabicFont ?? this.arabicFont,
      arabicFontSize: arabicFontSize ?? this.arabicFontSize,
      translationFontSize: translationFontSize ?? this.translationFontSize,
      showTransliteration: showTransliteration ?? this.showTransliteration,
      showTranslation: showTranslation ?? this.showTranslation,
      translationLanguage: translationLanguage ?? this.translationLanguage,
      nightMode: nightMode ?? this.nightMode,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      autoScroll: autoScroll ?? this.autoScroll,
      autoScrollSpeed: autoScrollSpeed ?? this.autoScrollSpeed,
    );
  }
}
