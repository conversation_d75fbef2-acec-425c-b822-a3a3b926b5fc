import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/notification.dart';
import '../providers/notification_provider.dart';

class NotificationWidget extends ConsumerWidget {
  final AppNotification notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const NotificationWidget({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.read(notificationActionsProvider);
    
    return Dismissible(
      key: Key('notification_${notification.id}'),
      direction: DismissDirection.endToStart,
      onDismissed: (_) {
        actions.dismiss(notification.id);
        onDismiss?.call();
      },
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        color: Colors.red,
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: InkWell(
          onTap: () {
            if (!notification.isRead) {
              actions.markAsRead(notification.id);
            }
            onTap?.call();
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    _buildIcon(),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            notification.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            notification.body,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildPriorityIndicator(),
                        const SizedBox(height: 4),
                        Text(
                          _formatTime(notification.scheduledAt),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ],
                ),
                if (notification.actions.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  _buildActions(context, ref),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    IconData iconData;
    Color iconColor = notification.color ?? Colors.blue;

    switch (notification.type) {
      case NotificationType.todoReminder:
        iconData = Icons.task_alt;
        break;
      case NotificationType.dhikrReminder:
        iconData = Icons.self_improvement;
        break;
      case NotificationType.voiceMemoTranscription:
        iconData = Icons.mic;
        break;
      case NotificationType.achievement:
        iconData = Icons.emoji_events;
        break;
      case NotificationType.dailyReview:
        iconData = Icons.rate_review;
        break;
      case NotificationType.weeklyReport:
        iconData = Icons.analytics;
        break;
      case NotificationType.backupComplete:
        iconData = Icons.backup;
        break;
      case NotificationType.syncComplete:
        iconData = Icons.sync;
        break;
      default:
        iconData = Icons.notifications;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  Widget _buildPriorityIndicator() {
    Color color;
    switch (notification.priority) {
      case NotificationPriority.urgent:
        color = Colors.red;
        break;
      case NotificationPriority.high:
        color = Colors.orange;
        break;
      case NotificationPriority.normal:
        color = Colors.blue;
        break;
      case NotificationPriority.low:
        color = Colors.grey;
        break;
    }

    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildActions(BuildContext context, WidgetRef ref) {
    final actions = ref.read(notificationActionsProvider);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: notification.actions.map((action) {
        return Padding(
          padding: const EdgeInsets.only(left: 8),
          child: OutlinedButton.icon(
            onPressed: () => actions.handleAction(notification.id, action.id),
            icon: action.icon != null ? Icon(action.icon, size: 16) : const SizedBox(),
            label: Text(action.title),
            style: OutlinedButton.styleFrom(
              foregroundColor: action.color,
              side: BorderSide(color: action.color ?? Colors.blue),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
        );
      }).toList(),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class NotificationsList extends ConsumerWidget {
  final bool showOnlyUnread;

  const NotificationsList({
    super.key,
    this.showOnlyUnread = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsAsync = ref.watch(notificationsProvider);

    return notificationsAsync.when(
      data: (notifications) {
        final filteredNotifications = showOnlyUnread
            ? notifications.where((n) => !n.isRead).toList()
            : notifications;

        if (filteredNotifications.isEmpty) {
          return _buildEmptyState(context);
        }

        return ListView.builder(
          itemCount: filteredNotifications.length,
          itemBuilder: (context, index) {
            final notification = filteredNotifications[index];
            return NotificationWidget(
              notification: notification,
              onTap: () => _handleNotificationTap(context, notification),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading notifications: $error'),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            showOnlyUnread ? Icons.mark_email_read : Icons.notifications_none,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            showOnlyUnread ? 'No unread notifications' : 'No notifications yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            showOnlyUnread 
                ? 'All caught up!'
                : 'Notifications will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  void _handleNotificationTap(BuildContext context, AppNotification notification) {
    // Handle navigation based on notification type
    switch (notification.type) {
      case NotificationType.todoReminder:
        // Navigate to todo details
        break;
      case NotificationType.dhikrReminder:
        // Navigate to dhikr screen
        break;
      case NotificationType.voiceMemoTranscription:
        // Navigate to voice memo details
        break;
      case NotificationType.achievement:
        // Show achievement details
        _showAchievementDialog(context, notification);
        break;
      default:
        // Default action
        break;
    }
  }

  void _showAchievementDialog(BuildContext context, AppNotification notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.emoji_events, color: Colors.amber),
            const SizedBox(width: 8),
            const Text('Achievement Unlocked!'),
          ],
        ),
        content: Text(notification.body),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Awesome!'),
          ),
        ],
      ),
    );
  }
}

class NotificationBadge extends ConsumerWidget {
  final Widget child;

  const NotificationBadge({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final unreadCount = ref.watch(unreadNotificationCountProvider);

    return Badge(
      isLabelVisible: unreadCount > 0,
      label: Text(unreadCount > 99 ? '99+' : unreadCount.toString()),
      child: child,
    );
  }
}
