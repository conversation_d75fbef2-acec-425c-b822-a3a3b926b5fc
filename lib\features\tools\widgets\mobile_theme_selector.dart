import 'package:flutter/material.dart';
import '../models/mobile_theme.dart';

/// Mobile theme selector widget
class MobileThemeSelector extends StatelessWidget {
  final MobileTheme? selectedTheme;
  final Function(MobileTheme) onThemeSelected;

  const MobileThemeSelector({
    super.key,
    this.selectedTheme,
    required this.onThemeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final themes = _getDefaultThemes();

    return Column(
      children: [
        // Theme type selector
        SegmentedButton<MobileThemeType>(
          segments: const [
            ButtonSegment(
              value: MobileThemeType.android,
              label: Text('Android'),
              icon: Icon(Icons.android),
            ),
            ButtonSegment(
              value: MobileThemeType.ios,
              label: Text('iOS'),
              icon: Icon(Icons.phone_iphone),
            ),
            ButtonSegment(
              value: MobileThemeType.hybrid,
              label: Text('Hybrid'),
              icon: Icon(Icons.devices),
            ),
          ],
          selected: {selectedTheme?.type ?? MobileThemeType.android},
          onSelectionChanged: (Set<MobileThemeType> selection) {
            final type = selection.first;
            final themeForType = themes.where((t) => t.type == type).first;
            onThemeSelected(themeForType);
          },
        ),

        const SizedBox(height: 24),

        // Theme grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: themes.length,
          itemBuilder: (context, index) {
            final theme = themes[index];
            final isSelected = selectedTheme?.id == theme.id;

            return _buildThemeCard(context, theme, isSelected);
          },
        ),
      ],
    );
  }

  Widget _buildThemeCard(BuildContext context, MobileTheme theme, bool isSelected) {
    return Card(
      elevation: isSelected ? 8 : 2,
      child: InkWell(
        onTap: () => onThemeSelected(theme),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: Column(
            children: [
              // Theme preview
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.lightColorScheme.surface,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                  ),
                  child: _buildThemePreview(theme),
                ),
              ),

              // Theme info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              theme.name,
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          if (isSelected)
                            Icon(
                              Icons.check_circle,
                              color: Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        theme.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const Spacer(),
                      Row(
                        children: [
                          Icon(
                            _getThemeTypeIcon(theme.type),
                            size: 16,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            theme.type.name.toUpperCase(),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThemePreview(MobileTheme theme) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          // App bar preview
          Container(
            height: 24,
            decoration: BoxDecoration(
              color: theme.lightColorScheme.primary,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                const SizedBox(width: 8),
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: theme.lightColorScheme.onPrimary,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Spacer(),
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: theme.lightColorScheme.onPrimary,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: theme.lightColorScheme.onPrimary,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),

          const SizedBox(height: 4),

          // Content preview
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.lightColorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: Column(
                  children: [
                    // Card preview
                    Container(
                      height: 16,
                      decoration: BoxDecoration(
                        color: theme.lightColorScheme.surface,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Container(
                      height: 16,
                      decoration: BoxDecoration(
                        color: theme.lightColorScheme.surface,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const Spacer(),
                    // Button preview
                    Container(
                      height: 12,
                      decoration: BoxDecoration(
                        color: theme.lightColorScheme.secondary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getThemeTypeIcon(MobileThemeType type) {
    switch (type) {
      case MobileThemeType.android:
        return Icons.android;
      case MobileThemeType.ios:
        return Icons.phone_iphone;
      case MobileThemeType.hybrid:
        return Icons.devices;
    }
  }

  List<MobileTheme> _getDefaultThemes() {
    final now = DateTime.now();

    return [
      // Android themes
      MobileTheme(
        id: 'android_material',
        name: 'Material Design',
        description: 'Google\'s Material Design 3',
        type: MobileThemeType.android,
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        layoutConfig: const MobileLayoutConfig(
          navigationStyle: NavigationStyle.bottomNav,
          appBarStyle: AppBarStyle.elevated,
        ),
        typography: const MobileTypography(
          fontFamily: 'Roboto',
        ),
        components: const MobileComponents(
          buttonStyle: ButtonStyle.filled,
          inputStyle: InputStyle.outlined,
        ),
        isDefault: true,
        createdAt: now,
      ),

      MobileTheme(
        id: 'android_dark',
        name: 'Dark Material',
        description: 'Dark Material Design theme',
        type: MobileThemeType.android,
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.deepPurple,
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.deepPurple,
          brightness: Brightness.dark,
        ),
        layoutConfig: const MobileLayoutConfig(
          navigationStyle: NavigationStyle.drawer,
          appBarStyle: AppBarStyle.flat,
        ),
        typography: const MobileTypography(
          fontFamily: 'Roboto',
        ),
        components: const MobileComponents(
          buttonStyle: ButtonStyle.outlined,
          inputStyle: InputStyle.filled,
        ),
        createdAt: now,
      ),

      // iOS themes
      MobileTheme(
        id: 'ios_cupertino',
        name: 'Cupertino',
        description: 'Apple\'s iOS design language',
        type: MobileThemeType.ios,
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        layoutConfig: const MobileLayoutConfig(
          navigationStyle: NavigationStyle.tabs,
          appBarStyle: AppBarStyle.transparent,
          borderRadius: 8.0,
        ),
        typography: const MobileTypography(
          fontFamily: 'SF Pro',
        ),
        components: const MobileComponents(
          buttonStyle: ButtonStyle.filled,
          inputStyle: InputStyle.outlined,
          cardStyle: CardStyle.outlined,
        ),
        createdAt: now,
      ),

      MobileTheme(
        id: 'ios_minimal',
        name: 'iOS Minimal',
        description: 'Clean and minimal iOS style',
        type: MobileThemeType.ios,
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.grey,
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.grey,
          brightness: Brightness.dark,
        ),
        layoutConfig: const MobileLayoutConfig(
          navigationStyle: NavigationStyle.tabs,
          appBarStyle: AppBarStyle.flat,
          borderRadius: 12.0,
        ),
        typography: const MobileTypography(
          fontFamily: 'SF Pro',
        ),
        components: const MobileComponents(
          buttonStyle: ButtonStyle.text,
          inputStyle: InputStyle.underlined,
          cardStyle: CardStyle.filled,
        ),
        createdAt: now,
      ),

      // Hybrid themes
      MobileTheme(
        id: 'hybrid_modern',
        name: 'Modern Hybrid',
        description: 'Best of both platforms',
        type: MobileThemeType.hybrid,
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.teal,
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.teal,
          brightness: Brightness.dark,
        ),
        layoutConfig: const MobileLayoutConfig(
          navigationStyle: NavigationStyle.rail,
          appBarStyle: AppBarStyle.gradient,
          borderRadius: 16.0,
        ),
        typography: const MobileTypography(
          fontFamily: 'Inter',
        ),
        components: const MobileComponents(
          buttonStyle: ButtonStyle.elevated,
          inputStyle: InputStyle.outlined,
          cardStyle: CardStyle.elevated,
        ),
        createdAt: now,
      ),

      MobileTheme(
        id: 'hybrid_professional',
        name: 'Professional',
        description: 'Business-focused design',
        type: MobileThemeType.hybrid,
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1565C0),
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1565C0),
          brightness: Brightness.dark,
        ),
        layoutConfig: const MobileLayoutConfig(
          navigationStyle: NavigationStyle.drawer,
          appBarStyle: AppBarStyle.elevated,
          borderRadius: 8.0,
        ),
        typography: const MobileTypography(
          fontFamily: 'Source Sans Pro',
          headingWeight: FontWeight.w700,
        ),
        components: const MobileComponents(
          buttonStyle: ButtonStyle.filled,
          inputStyle: InputStyle.outlined,
          cardStyle: CardStyle.elevated,
        ),
        createdAt: now,
      ),
    ];
  }
}
