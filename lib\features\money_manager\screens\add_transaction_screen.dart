import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/transaction.dart';
import '../models/account.dart';
import '../models/category.dart';

/// Screen for adding new transactions
class AddTransactionScreen extends StatefulWidget {
  final List<Account> accounts;
  final List<Category> categories;
  final TransactionType? initialType;
  final Function(Transaction) onSave;

  const AddTransactionScreen({
    super.key,
    required this.accounts,
    required this.categories,
    this.initialType,
    required this.onSave,
  });

  @override
  State<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends State<AddTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _tagsController = TextEditingController();

  TransactionType _type = TransactionType.expense;
  Account? _fromAccount;
  Account? _toAccount;
  Category? _category;
  DateTime _date = DateTime.now();
  List<String> _tags = [];

  @override
  void initState() {
    super.initState();
    if (widget.initialType != null) {
      _type = widget.initialType!;
    }
    
    // Set default account
    if (widget.accounts.isNotEmpty) {
      _fromAccount = widget.accounts.first;
    }
    
    // Set default category based on type
    _updateDefaultCategory();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _updateDefaultCategory() {
    final categoryType = _type == TransactionType.income 
        ? CategoryType.income 
        : CategoryType.expense;
    
    final availableCategories = widget.categories
        .where((c) => c.type == categoryType)
        .toList();
    
    if (availableCategories.isNotEmpty) {
      _category = availableCategories.first;
    } else {
      _category = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Transaction'),
        actions: [
          TextButton(
            onPressed: _saveTransaction,
            child: const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction Type
              Text(
                'Transaction Type',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              SegmentedButton<TransactionType>(
                segments: [
                  ButtonSegment(
                    value: TransactionType.income,
                    label: Text('Income'),
                    icon: Icon(Icons.add_circle, color: Colors.green),
                  ),
                  ButtonSegment(
                    value: TransactionType.expense,
                    label: Text('Expense'),
                    icon: Icon(Icons.remove_circle, color: Colors.red),
                  ),
                  ButtonSegment(
                    value: TransactionType.transfer,
                    label: Text('Transfer'),
                    icon: Icon(Icons.swap_horiz, color: Colors.blue),
                  ),
                ],
                selected: {_type},
                onSelectionChanged: (Set<TransactionType> selection) {
                  setState(() {
                    _type = selection.first;
                    _updateDefaultCategory();
                  });
                },
              ),
              
              const SizedBox(height: 24),
              
              // Amount
              Text(
                'Amount',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _amountController,
                decoration: InputDecoration(
                  labelText: 'Amount',
                  hintText: '0.00',
                  prefixText: '\$ ',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // Title
              Text(
                'Title',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _titleController,
                decoration: InputDecoration(
                  labelText: 'Transaction title',
                  hintText: 'Enter a descriptive title',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // From Account
              Text(
                _type == TransactionType.transfer ? 'From Account' : 'Account',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              DropdownButtonFormField<Account>(
                value: _fromAccount,
                decoration: InputDecoration(
                  labelText: 'Select account',
                  border: OutlineInputBorder(),
                ),
                items: widget.accounts.map((account) => DropdownMenuItem(
                  value: account,
                  child: Row(
                    children: [
                      Icon(account.icon, color: account.color, size: 20),
                      const SizedBox(width: 8),
                      Expanded(child: Text(account.name)),
                      Text(
                        account.formattedBalance,
                        style: TextStyle(
                          color: account.isPositiveBalance ? Colors.green : Colors.red,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                )).toList(),
                onChanged: (account) {
                  setState(() {
                    _fromAccount = account;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select an account';
                  }
                  return null;
                },
              ),
              
              // To Account (for transfers)
              if (_type == TransactionType.transfer) ...[
                const SizedBox(height: 24),
                
                Text(
                  'To Account',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                
                DropdownButtonFormField<Account>(
                  value: _toAccount,
                  decoration: InputDecoration(
                    labelText: 'Select destination account',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.accounts
                      .where((account) => account.id != _fromAccount?.id)
                      .map((account) => DropdownMenuItem(
                    value: account,
                    child: Row(
                      children: [
                        Icon(account.icon, color: account.color, size: 20),
                        const SizedBox(width: 8),
                        Expanded(child: Text(account.name)),
                        Text(
                          account.formattedBalance,
                          style: TextStyle(
                            color: account.isPositiveBalance ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  )).toList(),
                  onChanged: (account) {
                    setState(() {
                      _toAccount = account;
                    });
                  },
                  validator: (value) {
                    if (_type == TransactionType.transfer && value == null) {
                      return 'Please select a destination account';
                    }
                    return null;
                  },
                ),
              ],
              
              const SizedBox(height: 24),
              
              // Category
              Text(
                'Category',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              DropdownButtonFormField<Category>(
                value: _category,
                decoration: InputDecoration(
                  labelText: 'Select category',
                  border: OutlineInputBorder(),
                ),
                items: widget.categories
                    .where((category) => 
                        _type == TransactionType.transfer ||
                        (_type == TransactionType.income && category.type == CategoryType.income) ||
                        (_type == TransactionType.expense && category.type == CategoryType.expense))
                    .map((category) => DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(category.icon, color: category.color, size: 20),
                      const SizedBox(width: 8),
                      Text(category.name),
                    ],
                  ),
                )).toList(),
                onChanged: (category) {
                  setState(() {
                    _category = category;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select a category';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // Date
              Text(
                'Date',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Transaction date',
                    border: OutlineInputBorder(),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(_formatDate(_date)),
                      Icon(Icons.calendar_today),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Description
              Text(
                'Description (Optional)',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelText: 'Description',
                  hintText: 'Add additional details...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 24),
              
              // Tags
              Text(
                'Tags (Optional)',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              TextFormField(
                controller: _tagsController,
                decoration: InputDecoration(
                  labelText: 'Tags',
                  hintText: 'work, food, travel (comma separated)',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  _tags = value.split(',').map((tag) => tag.trim()).where((tag) => tag.isNotEmpty).toList();
                },
              ),
              
              const SizedBox(height: 32),
              
              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveTransaction,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    'Save Transaction',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _date,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() {
        _date = date;
      });
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month]} ${date.day}, ${date.year}';
  }

  void _saveTransaction() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text);
    
    final transaction = Transaction(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      type: _type,
      amount: amount,
      fromAccountId: _fromAccount!.id,
      toAccountId: _toAccount?.id,
      categoryId: _category!.id,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      date: _date,
      tags: _tags,
      createdAt: DateTime.now(),
    );

    widget.onSave(transaction);
    Navigator.of(context).pop();
  }
}
