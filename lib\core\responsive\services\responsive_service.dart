import 'dart:async';
import 'package:flutter/material.dart';
import '../models/breakpoints.dart';

class ResponsiveService {
  static final ResponsiveService _instance = ResponsiveService._internal();
  factory ResponsiveService() => _instance;
  ResponsiveService._internal();

  ResponsiveConfig? _currentConfig;
  final StreamController<ResponsiveConfig> _configController = 
      StreamController<ResponsiveConfig>.broadcast();

  // Getters
  Stream<ResponsiveConfig> get configStream => _configController.stream;
  ResponsiveConfig? get currentConfig => _currentConfig;
  bool get isInitialized => _currentConfig != null;

  // Initialize with screen size
  void initialize(Size screenSize) {
    updateScreenSize(screenSize);
  }

  // Update screen size
  void updateScreenSize(Size screenSize) {
    final newConfig = ResponsiveConfig.fromSize(screenSize);
    
    if (_currentConfig != newConfig) {
      _currentConfig = newConfig;
      _configController.add(newConfig);
    }
  }

  // Layout helpers
  Widget buildResponsiveLayout({
    Widget? xs,
    Widget? sm,
    Widget? md,
    Widget? lg,
    Widget? xl,
    Widget? xxl,
    required Widget fallback,
  }) {
    if (_currentConfig == null) return fallback;

    return _currentConfig!.responsive<Widget>(
      xs: xs,
      sm: sm,
      md: md,
      lg: lg,
      xl: xl,
      xxl: xxl,
      fallback: fallback,
    );
  }

  Widget buildDeviceLayout({
    Widget? mobile,
    Widget? tablet,
    Widget? desktop,
    Widget? tv,
    required Widget fallback,
  }) {
    if (_currentConfig == null) return fallback;

    return _currentConfig!.device<Widget>(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      tv: tv,
      fallback: fallback,
    );
  }

  // Grid helpers
  Widget buildResponsiveGrid({
    required List<Widget> children,
    double? spacing,
    double? runSpacing,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
  }) {
    if (_currentConfig == null) {
      return Wrap(children: children);
    }

    final columns = _currentConfig!.getColumns();
    final itemSpacing = spacing ?? _currentConfig!.getCardSpacing();
    final lineSpacing = runSpacing ?? _currentConfig!.getCardSpacing();

    if (_currentConfig!.shouldUseGridLayout()) {
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          crossAxisSpacing: itemSpacing,
          mainAxisSpacing: lineSpacing,
          childAspectRatio: 1.0,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      );
    } else {
      return Column(
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: mainAxisAlignment,
        children: children
            .map((child) => Padding(
                  padding: EdgeInsets.only(bottom: lineSpacing),
                  child: child,
                ))
            .toList(),
      );
    }
  }

  // Navigation helpers
  Widget buildResponsiveNavigation({
    required List<NavigationItem> items,
    required int currentIndex,
    required ValueChanged<int> onTap,
    Widget? drawer,
  }) {
    if (_currentConfig == null) {
      return const SizedBox.shrink();
    }

    if (_currentConfig!.shouldUseBottomNavigation()) {
      return BottomNavigationBar(
        items: items.map((item) => BottomNavigationBarItem(
          icon: Icon(item.icon),
          label: item.label,
          tooltip: item.tooltip,
        )).toList(),
        currentIndex: currentIndex,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
      );
    } else if (_currentConfig!.shouldUseNavigationRail()) {
      return NavigationRail(
        destinations: items.map((item) => NavigationRailDestination(
          icon: Icon(item.icon),
          label: Text(item.label),
        )).toList(),
        selectedIndex: currentIndex,
        onDestinationSelected: onTap,
        extended: _currentConfig!.screenSize.index >= ScreenSize.lg.index,
      );
    } else {
      return drawer ?? const SizedBox.shrink();
    }
  }

  // Content layout helpers
  Widget buildResponsiveContent({
    required Widget content,
    Widget? sidebar,
    bool centerContent = true,
  }) {
    if (_currentConfig == null) return content;

    final maxWidth = _currentConfig!.getContentWidth();
    final padding = _currentConfig!.getPagePadding();

    Widget wrappedContent = Container(
      constraints: BoxConstraints(maxWidth: maxWidth),
      padding: padding,
      child: content,
    );

    if (centerContent && _currentConfig!.screenSize.index >= ScreenSize.lg.index) {
      wrappedContent = Center(child: wrappedContent);
    }

    if (sidebar != null && _currentConfig!.shouldUseSidebar()) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(flex: 3, child: wrappedContent),
          const SizedBox(width: 24),
          Expanded(flex: 1, child: sidebar),
        ],
      );
    }

    return wrappedContent;
  }

  // Card layout helpers
  Widget buildResponsiveCard({
    required Widget child,
    VoidCallback? onTap,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    if (_currentConfig == null) {
      return Card(child: child);
    }

    final cardPadding = padding ?? _currentConfig!.getPagePadding();
    final cardMargin = margin ?? EdgeInsets.all(_currentConfig!.getCardSpacing());
    final elevation = _currentConfig!.getCardElevation();
    final borderRadius = _currentConfig!.getCardBorderRadius();

    return Container(
      margin: cardMargin,
      child: Card(
        elevation: elevation,
        shape: RoundedRectangleBorder(borderRadius: borderRadius),
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius,
          child: Padding(
            padding: cardPadding,
            child: child,
          ),
        ),
      ),
    );
  }

  // List layout helpers
  Widget buildResponsiveList({
    required List<Widget> children,
    bool shrinkWrap = true,
    ScrollPhysics? physics,
    EdgeInsets? padding,
  }) {
    if (_currentConfig == null) {
      return ListView(children: children);
    }

    final listPadding = padding ?? _currentConfig!.getPagePadding();

    if (_currentConfig!.shouldUseListLayout()) {
      return ListView.builder(
        shrinkWrap: shrinkWrap,
        physics: physics,
        padding: listPadding,
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      );
    } else {
      return buildResponsiveGrid(children: children);
    }
  }

  // Dialog helpers
  Future<T?> showResponsiveDialog<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
    String? barrierLabel,
  }) {
    if (_currentConfig == null || _currentConfig!.shouldUseModalDialogs()) {
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        barrierLabel: barrierLabel,
        builder: (context) => Dialog(child: child),
      );
    } else {
      // Use full-screen dialog for larger screens
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        barrierLabel: barrierLabel,
        builder: (context) => Dialog.fullscreen(child: child),
      );
    }
  }

  Future<T?> showResponsiveBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = true,
  }) {
    if (_currentConfig == null || _currentConfig!.shouldUseBottomSheets()) {
      return showModalBottomSheet<T>(
        context: context,
        isScrollControlled: isScrollControlled,
        builder: (context) => child,
      );
    } else {
      // Use dialog for larger screens
      return showResponsiveDialog<T>(
        context: context,
        child: child,
      );
    }
  }

  // Text scaling helpers
  TextStyle getResponsiveTextStyle(TextStyle baseStyle) {
    if (_currentConfig == null) return baseStyle;

    final multiplier = _currentConfig!.getFontSizeMultiplier();
    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * multiplier,
    );
  }

  // Icon sizing helpers
  double getResponsiveIconSize([double? baseSize]) {
    if (_currentConfig == null) return baseSize ?? 24;
    return _currentConfig!.getIconSize();
  }

  // Button sizing helpers
  Size getResponsiveButtonSize([Size? baseSize]) {
    if (_currentConfig == null) return baseSize ?? const Size(88, 48);
    
    final height = _currentConfig!.getButtonHeight();
    return Size(baseSize?.width ?? 88, height);
  }

  // Animation helpers
  Duration getResponsiveAnimationDuration([Duration? baseDuration]) {
    if (_currentConfig == null) return baseDuration ?? const Duration(milliseconds: 300);
    return _currentConfig!.getAnimationDuration();
  }

  // Utility methods
  bool isMobile() => _currentConfig?.deviceType == DeviceType.mobile;
  bool isTablet() => _currentConfig?.deviceType == DeviceType.tablet;
  bool isDesktop() => _currentConfig?.deviceType == DeviceType.desktop;
  bool isTV() => _currentConfig?.deviceType == DeviceType.tv;

  bool isPortrait() => _currentConfig?.isPortrait ?? true;
  bool isLandscape() => _currentConfig?.isLandscape ?? false;

  bool isSmallScreen() => (_currentConfig?.screenSize.index ?? 0) < ScreenSize.md.index;
  bool isMediumScreen() => _currentConfig?.screenSize == ScreenSize.md;
  bool isLargeScreen() => (_currentConfig?.screenSize.index ?? 0) >= ScreenSize.lg.index;

  // Dispose resources
  void dispose() {
    _configController.close();
  }
}

// Navigation item model
class NavigationItem {
  final IconData icon;
  final String label;
  final String? tooltip;
  final VoidCallback? onTap;

  const NavigationItem({
    required this.icon,
    required this.label,
    this.tooltip,
    this.onTap,
  });
}
