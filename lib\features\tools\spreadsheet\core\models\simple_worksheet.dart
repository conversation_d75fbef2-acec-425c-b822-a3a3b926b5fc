import 'package:flutter/foundation.dart';
import 'simple_cell.dart';
import 'cell_range.dart';

/// Simple worksheet model for basic spreadsheet functionality
@immutable
class SimpleWorksheet {
  final String id;
  final String name;
  final Map<String, SimpleCell> cells;
  final DateTime lastModified;

  const SimpleWorksheet({
    required this.id,
    required this.name,
    this.cells = const {},
    required this.lastModified,
  });

  /// Create empty worksheet
  factory SimpleWorksheet.empty(String name) {
    return SimpleWorksheet(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      cells: {},
      lastModified: DateTime.now(),
    );
  }

  /// Get cell at address
  SimpleCell? getCell(String address) {
    return cells[address];
  }

  /// Set cell at address
  SimpleWorksheet setCell(String address, SimpleCell cell) {
    final updatedCells = Map<String, SimpleCell>.from(cells);
    updatedCells[address] = cell;

    return copyWith(
      cells: updatedCells,
      lastModified: DateTime.now(),
    );
  }

  /// Remove cell at address
  SimpleWorksheet removeCell(String address) {
    final updatedCells = Map<String, SimpleCell>.from(cells);
    updatedCells.remove(address);

    return copyWith(
      cells: updatedCells,
      lastModified: DateTime.now(),
    );
  }

  /// Get used range
  CellRange? get usedRange {
    if (cells.isEmpty) return null;

    final addresses = cells.keys.toList();
    final coordinates = addresses.map((addr) => CellCoordinates.fromAddress(addr)).toList();

    final minRow = coordinates.map((c) => c.row).reduce((a, b) => a < b ? a : b);
    final maxRow = coordinates.map((c) => c.row).reduce((a, b) => a > b ? a : b);
    final minCol = coordinates.map((c) => c.column).reduce((a, b) => a < b ? a : b);
    final maxCol = coordinates.map((c) => c.column).reduce((a, b) => a > b ? a : b);

    return CellRange(
      startRow: minRow,
      startColumn: minCol,
      endRow: maxRow,
      endColumn: maxCol,
    );
  }

  /// Clear all cells
  SimpleWorksheet clear() {
    return copyWith(
      cells: {},
      lastModified: DateTime.now(),
    );
  }

  /// Copy with modifications
  SimpleWorksheet copyWith({
    String? id,
    String? name,
    Map<String, SimpleCell>? cells,
    DateTime? lastModified,
  }) {
    return SimpleWorksheet(
      id: id ?? this.id,
      name: name ?? this.name,
      cells: cells ?? this.cells,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'cells': cells.map((key, value) => MapEntry(key, value.toJson())),
      'lastModified': lastModified.toIso8601String(),
    };
  }

  /// Create from JSON
  factory SimpleWorksheet.fromJson(Map<String, dynamic> json) {
    final cellsJson = json['cells'] as Map<String, dynamic>? ?? {};
    final cells = cellsJson.map(
      (key, value) => MapEntry(key, SimpleCell.fromJson(value as Map<String, dynamic>)),
    );

    return SimpleWorksheet(
      id: json['id'] as String,
      name: json['name'] as String,
      cells: cells,
      lastModified: DateTime.parse(json['lastModified'] as String),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SimpleWorksheet &&
        other.id == id &&
        other.name == name &&
        mapEquals(other.cells, cells) &&
        other.lastModified == lastModified;
  }

  @override
  int get hashCode => Object.hash(id, name, cells, lastModified);

  @override
  String toString() {
    return 'SimpleWorksheet(id: $id, name: $name, cells: ${cells.length})';
  }
}
