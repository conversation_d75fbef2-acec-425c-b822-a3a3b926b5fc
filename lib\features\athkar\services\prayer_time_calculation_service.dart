import 'dart:math' as math;
import '../models/prayer_time.dart';
import '../models/location_settings.dart';

class PrayerTimeCalculationService {
  static const double _degreesToRadians = math.pi / 180.0;
  static const double _radiansToDegrees = 180.0 / math.pi;

  // Calculate prayer times for a given date and location
  static List<PrayerTime> calculatePrayerTimes({
    required DateTime date,
    required LocationSettings location,
    required String userId,
  }) {
    final julianDate = _getJulianDate(date);
    final sunPosition = _getSunPosition(julianDate, location.latitude, location.longitude);
    
    // Get calculation parameters based on method
    final params = _getCalculationParameters(location.calculationMethod);
    
    // Calculate prayer times
    final fajrTime = _calculateFajrTime(date, location, params);
    final dhuhrTime = _calculateDhuhrTime(date, location, sunPosition);
    final asrTime = _calculateAsrTime(date, location, sunPosition);
    final maghribTime = _calculateMaghribTime(date, location, sunPosition);
    final ishaTime = _calculateIshaTime(date, location, params, maghribTime);
    
    // Apply adjustments
    final adjustedFajr = fajrTime.add(Duration(minutes: location.fajrAdjustment));
    final adjustedDhuhr = dhuhrTime.add(Duration(minutes: location.dhuhrAdjustment));
    final adjustedAsr = asrTime.add(Duration(minutes: location.asrAdjustment));
    final adjustedMaghrib = maghribTime.add(Duration(minutes: location.maghribAdjustment));
    final adjustedIsha = ishaTime.add(Duration(minutes: location.ishaAdjustment));
    
    return [
      PrayerTime.create(
        prayerType: PrayerType.fajr,
        prayerTime: adjustedFajr,
        userId: userId,
        latitude: location.latitude,
        longitude: location.longitude,
        locationName: location.displayLocation,
      ),
      PrayerTime.create(
        prayerType: PrayerType.dhuhr,
        prayerTime: adjustedDhuhr,
        userId: userId,
        latitude: location.latitude,
        longitude: location.longitude,
        locationName: location.displayLocation,
      ),
      PrayerTime.create(
        prayerType: PrayerType.asr,
        prayerTime: adjustedAsr,
        userId: userId,
        latitude: location.latitude,
        longitude: location.longitude,
        locationName: location.displayLocation,
      ),
      PrayerTime.create(
        prayerType: PrayerType.maghrib,
        prayerTime: adjustedMaghrib,
        userId: userId,
        latitude: location.latitude,
        longitude: location.longitude,
        locationName: location.displayLocation,
      ),
      PrayerTime.create(
        prayerType: PrayerType.isha,
        prayerTime: adjustedIsha,
        userId: userId,
        latitude: location.latitude,
        longitude: location.longitude,
        locationName: location.displayLocation,
      ),
    ];
  }

  // Get default Jordan prayer times for today
  static List<PrayerTime> getDefaultJordanPrayerTimes(String userId) {
    final today = DateTime.now();
    final jordanLocation = LocationSettings.createDefaultJordan(userId);
    
    // Use simplified calculation for Jordan
    return [
      PrayerTime.create(
        prayerType: PrayerType.fajr,
        prayerTime: DateTime(today.year, today.month, today.day, 5, 30),
        userId: userId,
        latitude: jordanLocation.latitude,
        longitude: jordanLocation.longitude,
        locationName: 'Amman, Jordan',
      ),
      PrayerTime.create(
        prayerType: PrayerType.dhuhr,
        prayerTime: DateTime(today.year, today.month, today.day, 12, 15),
        userId: userId,
        latitude: jordanLocation.latitude,
        longitude: jordanLocation.longitude,
        locationName: 'Amman, Jordan',
      ),
      PrayerTime.create(
        prayerType: PrayerType.asr,
        prayerTime: DateTime(today.year, today.month, today.day, 15, 45),
        userId: userId,
        latitude: jordanLocation.latitude,
        longitude: jordanLocation.longitude,
        locationName: 'Amman, Jordan',
      ),
      PrayerTime.create(
        prayerType: PrayerType.maghrib,
        prayerTime: DateTime(today.year, today.month, today.day, 18, 20),
        userId: userId,
        latitude: jordanLocation.latitude,
        longitude: jordanLocation.longitude,
        locationName: 'Amman, Jordan',
      ),
      PrayerTime.create(
        prayerType: PrayerType.isha,
        prayerTime: DateTime(today.year, today.month, today.day, 19, 45),
        userId: userId,
        latitude: jordanLocation.latitude,
        longitude: jordanLocation.longitude,
        locationName: 'Amman, Jordan',
      ),
    ];
  }

  // Helper methods for calculations
  static double _getJulianDate(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year - a;
    final m = date.month + 12 * a - 3;
    
    return date.day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 - 32045;
  }

  static Map<String, double> _getSunPosition(double julianDate, double latitude, double longitude) {
    final n = julianDate - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = ((357.528 + 0.9856003 * n) % 360) * _degreesToRadians;
    final lambda = (l + 1.915 * math.sin(g) + 0.020 * math.sin(2 * g)) * _degreesToRadians;
    
    final beta = 0.0;
    final epsilon = (23.439 - 0.0000004 * n) * _degreesToRadians;
    
    final alpha = math.atan2(math.cos(epsilon) * math.sin(lambda), math.cos(lambda)) * _radiansToDegrees;
    final delta = math.asin(math.sin(epsilon) * math.sin(lambda)) * _radiansToDegrees;
    
    return {
      'declination': delta,
      'rightAscension': alpha,
    };
  }

  static Map<String, double> _getCalculationParameters(CalculationMethod method) {
    switch (method) {
      case CalculationMethod.jordan:
        return {'fajrAngle': 18.0, 'ishaAngle': 18.0, 'ishaInterval': 0};
      case CalculationMethod.muslimWorldLeague:
        return {'fajrAngle': 18.0, 'ishaAngle': 17.0, 'ishaInterval': 0};
      case CalculationMethod.egyptian:
        return {'fajrAngle': 19.5, 'ishaAngle': 17.5, 'ishaInterval': 0};
      case CalculationMethod.karachi:
        return {'fajrAngle': 18.0, 'ishaAngle': 18.0, 'ishaInterval': 0};
      case CalculationMethod.ummAlQura:
        return {'fajrAngle': 18.5, 'ishaAngle': 0.0, 'ishaInterval': 90};
      default:
        return {'fajrAngle': 18.0, 'ishaAngle': 18.0, 'ishaInterval': 0};
    }
  }

  static DateTime _calculateFajrTime(DateTime date, LocationSettings location, Map<String, double> params) {
    // Simplified calculation - in a real app, use proper astronomical calculations
    final baseTime = DateTime(date.year, date.month, date.day, 5, 30);
    return baseTime;
  }

  static DateTime _calculateDhuhrTime(DateTime date, LocationSettings location, Map<String, double> sunPosition) {
    // Simplified calculation - Dhuhr is approximately at solar noon
    final baseTime = DateTime(date.year, date.month, date.day, 12, 15);
    return baseTime;
  }

  static DateTime _calculateAsrTime(DateTime date, LocationSettings location, Map<String, double> sunPosition) {
    // Simplified calculation
    final baseTime = DateTime(date.year, date.month, date.day, 15, 45);
    return baseTime;
  }

  static DateTime _calculateMaghribTime(DateTime date, LocationSettings location, Map<String, double> sunPosition) {
    // Simplified calculation - Maghrib is at sunset
    final baseTime = DateTime(date.year, date.month, date.day, 18, 20);
    return baseTime;
  }

  static DateTime _calculateIshaTime(DateTime date, LocationSettings location, Map<String, double> params, DateTime maghribTime) {
    // Simplified calculation
    if (params['ishaInterval']! > 0) {
      // Use interval after Maghrib
      return maghribTime.add(Duration(minutes: params['ishaInterval']!.toInt()));
    } else {
      // Use angle-based calculation (simplified)
      final baseTime = DateTime(date.year, date.month, date.day, 19, 45);
      return baseTime;
    }
  }

  // Get prayer times for multiple days
  static List<PrayerTime> calculatePrayerTimesForRange({
    required DateTime startDate,
    required DateTime endDate,
    required LocationSettings location,
    required String userId,
  }) {
    final prayerTimes = <PrayerTime>[];
    
    for (var date = startDate; date.isBefore(endDate) || date.isAtSameMomentAs(endDate); date = date.add(const Duration(days: 1))) {
      prayerTimes.addAll(calculatePrayerTimes(
        date: date,
        location: location,
        userId: userId,
      ));
    }
    
    return prayerTimes;
  }

  // Validate prayer time order
  static bool validatePrayerTimes(List<PrayerTime> prayerTimes) {
    if (prayerTimes.length != 5) return false;
    
    final sortedTimes = List<PrayerTime>.from(prayerTimes)
      ..sort((a, b) => a.prayerTime.compareTo(b.prayerTime));
    
    final expectedOrder = [
      PrayerType.fajr,
      PrayerType.dhuhr,
      PrayerType.asr,
      PrayerType.maghrib,
      PrayerType.isha,
    ];
    
    for (int i = 0; i < sortedTimes.length; i++) {
      if (sortedTimes[i].prayerType != expectedOrder[i]) {
        return false;
      }
    }
    
    return true;
  }
}
