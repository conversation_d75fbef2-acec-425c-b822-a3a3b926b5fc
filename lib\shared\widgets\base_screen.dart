import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'universal_sidebar.dart';
import '../../core/navigation/app_router.dart';

class BaseScreen extends ConsumerWidget {
  final Widget body;
  final String title;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final PreferredSizeWidget? bottom;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final bool showSidebar;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;

  const BaseScreen({
    super.key,
    required this.body,
    required this.title,
    this.actions,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.bottom,
    this.showBackButton = true,
    this.onBackPressed,
    this.showSidebar = true,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPath = GoRouter.of(context).routerDelegate.currentConfiguration.uri.path;
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1024;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    // Handle back button behavior
    Widget buildScaffold() {
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
          _handleBackNavigation(context);
        },
        child: Scaffold(
          appBar: _buildAppBar(context, currentPath),
          drawer: (!isDesktop && !isTablet && showSidebar) 
              ? UniversalSidebar(currentRoute: currentPath) 
              : null,
          body: body,
          floatingActionButton: floatingActionButton,
          floatingActionButtonLocation: floatingActionButtonLocation,
          backgroundColor: backgroundColor,
          resizeToAvoidBottomInset: resizeToAvoidBottomInset,
        ),
      );
    }

    // For desktop and tablet, show sidebar alongside content
    if ((isDesktop || isTablet) && showSidebar) {
      return Row(
        children: [
          UniversalSidebar(
            currentRoute: currentPath,
            isCompact: isTablet,
          ),
          Expanded(child: buildScaffold()),
        ],
      );
    }

    return buildScaffold();
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, String currentPath) {
    return AppBar(
      title: Text(title),
      actions: actions,
      bottom: bottom,
      leading: _buildLeading(context, currentPath),
      backgroundColor: backgroundColor,
    );
  }

  Widget? _buildLeading(BuildContext context, String currentPath) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1024;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    // On desktop/tablet with sidebar, don't show menu button
    if ((isDesktop || isTablet) && showSidebar) {
      if (showBackButton && _shouldShowBackButton(currentPath)) {
        return IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _handleBackNavigation(context),
        );
      }
      return null;
    }

    // On mobile, show menu button or back button
    if (showBackButton && _shouldShowBackButton(currentPath)) {
      return IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => _handleBackNavigation(context),
      );
    }

    // Default menu button for drawer
    if (showSidebar) {
      return null; // Let Scaffold handle the drawer button
    }

    return null;
  }

  bool _shouldShowBackButton(String currentPath) {
    // Show back button if not on main screens
    final mainRoutes = [
      AppRoutes.dashboard,
      AppRoutes.memo,
      AppRoutes.islami,
      AppRoutes.tools,
      AppRoutes.money,
      AppRoutes.settings,
    ];

    return !mainRoutes.contains(currentPath);
  }

  void _handleBackNavigation(BuildContext context) {
    if (onBackPressed != null) {
      onBackPressed!();
      return;
    }

    final currentPath = GoRouter.of(context).routerDelegate.currentConfiguration.uri.path;
    
    // Custom back navigation logic
    if (currentPath == AppRoutes.dashboard) {
      _showExitConfirmation(context);
    } else if (currentPath.startsWith(AppRoutes.memo) && currentPath != AppRoutes.memo) {
      context.go(AppRoutes.memo);
    } else if (currentPath.startsWith(AppRoutes.islami) && currentPath != AppRoutes.islami) {
      context.go(AppRoutes.islami);
    } else if (currentPath.startsWith(AppRoutes.tools) && currentPath != AppRoutes.tools) {
      context.go(AppRoutes.tools);
    } else if (currentPath.startsWith(AppRoutes.money) && currentPath != AppRoutes.money) {
      context.go(AppRoutes.money);
    } else if (currentPath.startsWith(AppRoutes.settings) && currentPath != AppRoutes.settings) {
      context.go(AppRoutes.settings);
    } else {
      // Go back to dashboard from main mini-app screens
      context.go(AppRoutes.dashboard);
    }
  }

  void _showExitConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit ShadowSuite'),
          content: const Text('Are you sure you want to exit the application?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                SystemNavigator.pop();
              },
              child: const Text('Exit'),
            ),
          ],
        );
      },
    );
  }
}

// Specialized base screens for different mini-apps
class MemoBaseScreen extends BaseScreen {
  const MemoBaseScreen({
    super.key,
    required super.body,
    required super.title,
    super.actions,
    super.floatingActionButton,
    super.floatingActionButtonLocation,
    super.bottom,
    super.showBackButton = true,
    super.onBackPressed,
  });
}

class AthkarBaseScreen extends BaseScreen {
  const AthkarBaseScreen({
    super.key,
    required super.body,
    required super.title,
    super.actions,
    super.floatingActionButton,
    super.floatingActionButtonLocation,
    super.bottom,
    super.showBackButton = true,
    super.onBackPressed,
  });
}

class ToolsBaseScreen extends BaseScreen {
  const ToolsBaseScreen({
    super.key,
    required super.body,
    required super.title,
    super.actions,
    super.floatingActionButton,
    super.floatingActionButtonLocation,
    super.bottom,
    super.showBackButton = true,
    super.onBackPressed,
  });
}

class MoneyBaseScreen extends BaseScreen {
  const MoneyBaseScreen({
    super.key,
    required super.body,
    required super.title,
    super.actions,
    super.floatingActionButton,
    super.floatingActionButtonLocation,
    super.bottom,
    super.showBackButton = true,
    super.onBackPressed,
  });
}

class SettingsBaseScreen extends BaseScreen {
  const SettingsBaseScreen({
    super.key,
    required super.body,
    required super.title,
    super.actions,
    super.floatingActionButton,
    super.floatingActionButtonLocation,
    super.bottom,
    super.showBackButton = true,
    super.onBackPressed,
  });
}

// Quick access widget for floating action buttons
class QuickAccessFAB extends StatelessWidget {
  final List<QuickAction> actions;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const QuickAccessFAB({
    super.key,
    required this.actions,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (actions.length == 1) {
      final action = actions.first;
      return FloatingActionButton(
        onPressed: action.onPressed,
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        tooltip: action.tooltip,
        child: Icon(action.icon),
      );
    }

    return FloatingActionButton(
      onPressed: () => _showQuickActions(context),
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      child: const Icon(Icons.add),
    );
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ...actions.map((action) => ListTile(
              leading: Icon(action.icon),
              title: Text(action.title),
              subtitle: action.subtitle != null ? Text(action.subtitle!) : null,
              onTap: () {
                Navigator.of(context).pop();
                action.onPressed();
              },
            )),
          ],
        ),
      ),
    );
  }
}

class QuickAction {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback onPressed;
  final String? tooltip;

  const QuickAction({
    required this.title,
    this.subtitle,
    required this.icon,
    required this.onPressed,
    this.tooltip,
  });
}
