{"@@locale": "en", "appTitle": "ShadowSuite", "@appTitle": {"description": "The title of the application"}, "dashboard": "Dashboard", "@dashboard": {"description": "Dashboard screen title"}, "athkar": "<PERSON><PERSON><PERSON>", "@athkar": {"description": "<PERSON><PERSON><PERSON>/D<PERSON> screen title"}, "quran": "Quran", "@quran": {"description": "Quran screen title"}, "prayerTimes": "Prayer Times", "@prayerTimes": {"description": "Prayer times screen title"}, "tools": "Tools", "@tools": {"description": "Tools screen title"}, "memo": "Memo Suite", "@memo": {"description": "Memo suite screen title"}, "moneyFlow": "Money Flow", "@moneyFlow": {"description": "Money flow screen title"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "profile": "Profile", "@profile": {"description": "Profile screen title"}, "dhikr": "Dhikr", "@dhikr": {"description": "Dhikr tab title"}, "progress": "Progress", "@progress": {"description": "Progress tab title"}, "notes": "Notes", "@notes": {"description": "Notes section title"}, "todos": "Todos", "@todos": {"description": "Todos section title"}, "voiceMemos": "Voice Memos", "@voiceMemos": {"description": "Voice memos section title"}, "calculator": "Calculator", "@calculator": {"description": "Calculator tool title"}, "converter": "Converter", "@converter": {"description": "Converter tool title"}, "toolBuilder": "Tool Builder", "@toolBuilder": {"description": "Tool builder title"}, "accounts": "Accounts", "@accounts": {"description": "Accounts section title"}, "transactions": "Transactions", "@transactions": {"description": "Transactions section title"}, "budgets": "Budgets", "@budgets": {"description": "Budgets section title"}, "reports": "Reports", "@reports": {"description": "Reports section title"}, "add": "Add", "@add": {"description": "Add button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "loading": "Loading...", "@loading": {"description": "Loading text"}, "error": "Error", "@error": {"description": "Error title"}, "success": "Success", "@success": {"description": "Success message"}, "warning": "Warning", "@warning": {"description": "Warning message"}, "info": "Information", "@info": {"description": "Information message"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "filter": "Filter", "@filter": {"description": "Filter button text"}, "sort": "Sort", "@sort": {"description": "Sort button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "share": "Share", "@share": {"description": "Share button text"}, "export": "Export", "@export": {"description": "Export button text"}, "import": "Import", "@import": {"description": "Import button text"}, "backup": "Backup", "@backup": {"description": "Backup button text"}, "restore": "Rest<PERSON>", "@restore": {"description": "Restore button text"}, "sync": "Sync", "@sync": {"description": "Sync button text"}, "signIn": "Sign In", "@signIn": {"description": "Sign in button text"}, "signUp": "Sign Up", "@signUp": {"description": "Sign up button text"}, "signOut": "Sign Out", "@signOut": {"description": "Sign out button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "name": "Name", "@name": {"description": "Name field label"}, "title": "Title", "@title": {"description": "Title field label"}, "description": "Description", "@description": {"description": "Description field label"}, "date": "Date", "@date": {"description": "Date field label"}, "time": "Time", "@time": {"description": "Time field label"}, "amount": "Amount", "@amount": {"description": "Amount field label"}, "category": "Category", "@category": {"description": "Category field label"}, "priority": "Priority", "@priority": {"description": "Priority field label"}, "status": "Status", "@status": {"description": "Status field label"}, "language": "Language", "@language": {"description": "Language setting label"}, "theme": "Theme", "@theme": {"description": "Theme setting label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications setting label"}, "privacy": "Privacy", "@privacy": {"description": "Privacy setting label"}, "security": "Security", "@security": {"description": "Security setting label"}, "about": "About", "@about": {"description": "About section label"}, "version": "Version", "@version": {"description": "Version label"}, "help": "Help", "@help": {"description": "Help section label"}, "support": "Support", "@support": {"description": "Support section label"}, "feedback": "<PERSON><PERSON><PERSON>", "@feedback": {"description": "Feedback section label"}, "rateApp": "Rate App", "@rateApp": {"description": "Rate app button text"}, "translationSettings": "Translation Settings", "@translationSettings": {"description": "Translation settings title"}, "displayMode": "Display Mode", "@displayMode": {"description": "Display mode setting label"}, "arabicOnly": "Arabic Only", "@arabicOnly": {"description": "Arabic only display mode"}, "arabicWithTranslation": "Arabic + Translation", "@arabicWithTranslation": {"description": "Arabic with translation display mode"}, "arabicWithTransliteration": "Arabic + Transliteration", "@arabicWithTransliteration": {"description": "Arabic with transliteration display mode"}, "arabicWithBoth": "Arabic + Translation + Transliteration", "@arabicWithBoth": {"description": "Arabic with both translation and transliteration display mode"}, "translationOnly": "Translation Only", "@translationOnly": {"description": "Translation only display mode"}, "fontSize": "Font Size", "@fontSize": {"description": "Font size setting label"}, "fontFamily": "Font Family", "@fontFamily": {"description": "Font family setting label"}, "showReference": "Show Reference", "@showReference": {"description": "Show reference setting label"}, "showRecommendedCount": "Show Recommended Count", "@showRecommendedCount": {"description": "Show recommended count setting label"}, "rightToLeftText": "Right-to-Left Text", "@rightToLeftText": {"description": "Right-to-left text setting label"}, "resetToDefaults": "Reset to Defaults", "@resetToDefaults": {"description": "Reset to defaults button text"}, "completedTasks": "Completed Tasks ({count})", "@completedTasks": {"description": "Completed tasks section title with count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "activeTasks": "Active Tasks ({count})", "@activeTasks": {"description": "Active tasks section title with count", "placeholders": {"count": {"type": "int", "example": "3"}}}, "clearCompleted": "Clear Completed", "@clearCompleted": {"description": "Clear completed tasks button text"}, "duplicate": "Duplicate", "@duplicate": {"description": "Duplicate button text"}, "tapToCount": "Tap to Count", "@tapToCount": {"description": "Tap to count button text"}, "completed": "Completed!", "@completed": {"description": "Completed status text"}, "nextPrayer": "Next Prayer", "@nextPrayer": {"description": "Next prayer label"}, "todaysPrayerTimes": "Today's Prayer Times", "@todaysPrayerTimes": {"description": "Today's prayer times title"}, "currentLocation": "Current Location", "@currentLocation": {"description": "Current location label"}, "locationSettings": "Location Settings", "@locationSettings": {"description": "Location settings title"}, "fetchFromAPI": "Fetch from API", "@fetchFromAPI": {"description": "Fetch from API button text"}, "useSample": "Use Sample", "@useSample": {"description": "Use sample data button text"}, "updateTimes": "Update Times", "@updateTimes": {"description": "Update times button text"}, "changeLocation": "Change Location", "@changeLocation": {"description": "Change location button text"}}