import 'package:flutter/material.dart';
import '../models/ui_component.dart';
import '../models/mobile_app_theme.dart';
import '../services/mobile_app_theme_service.dart';

/// Enhanced settings panel with comprehensive customization options
class EnhancedSettingsPanel extends StatefulWidget {
  final UIComponent? selectedComponent;
  final MobileAppTheme? selectedTheme;
  final Function(String key, dynamic value)? onPropertyChanged;
  final Function(MobileAppTheme theme)? onThemeChanged;
  final Function(String key, dynamic value)? onGlobalSettingChanged;

  const EnhancedSettingsPanel({
    super.key,
    this.selectedComponent,
    this.selectedTheme,
    this.onPropertyChanged,
    this.onThemeChanged,
    this.onGlobalSettingChanged,
  });

  @override
  State<EnhancedSettingsPanel> createState() => _EnhancedSettingsPanelState();
}

class _EnhancedSettingsPanelState extends State<EnhancedSettingsPanel>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final MobileAppThemeService _themeService = MobileAppThemeService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _themeService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 350,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          left: BorderSide(color: Theme.of(context).colorScheme.outline),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Settings & Properties',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Tooltip(
                  message: 'Customize your app components and themes',
                  child: Icon(
                    Icons.help_outline,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(color: Theme.of(context).colorScheme.outline),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabs: const [
                Tab(icon: Icon(Icons.widgets), text: 'Component'),
                Tab(icon: Icon(Icons.palette), text: 'Theme'),
                Tab(icon: Icon(Icons.style), text: 'Style'),
                Tab(icon: Icon(Icons.settings_applications), text: 'Global'),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildComponentTab(),
                _buildThemeTab(),
                _buildStyleTab(),
                _buildGlobalTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComponentTab() {
    if (widget.selectedComponent == null) {
      return _buildEmptyState(
        icon: Icons.touch_app,
        title: 'No Component Selected',
        subtitle: 'Select a component to edit its properties',
      );
    }

    final component = widget.selectedComponent!;
    
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Component Info
        _buildSectionHeader('Component Information'),
        _buildInfoCard([
          _buildInfoRow('Type', component.type.name.toUpperCase()),
          _buildInfoRow('ID', component.id),
          _buildInfoRow('Label', component.label),
        ]),
        
        const SizedBox(height: 16),

        // Basic Properties
        _buildSectionHeader('Basic Properties'),
        _buildPropertyField(
          'Label',
          component.label,
          'The display text for this component',
          (value) => widget.onPropertyChanged?.call('label', value),
        ),
        
        if (component.bindToCell != null) ...[
          const SizedBox(height: 12),
          _buildPropertyField(
            'Cell Binding',
            component.bindToCell ?? '',
            'Excel cell reference (e.g., A1, B2)',
            (value) => widget.onPropertyChanged?.call('bindToCell', value),
            suffixIcon: IconButton(
              icon: const Icon(Icons.grid_view),
              onPressed: _showCellPicker,
              tooltip: 'Pick cell from spreadsheet',
            ),
          ),
        ],

        const SizedBox(height: 16),

        // Component-Specific Properties
        _buildSectionHeader('Component Properties'),
        ..._buildComponentSpecificProperties(component),

        const SizedBox(height: 16),

        // Position & Size
        _buildSectionHeader('Position & Size'),
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                'X',
                component.position.x,
                'Horizontal position',
                (value) => widget.onPropertyChanged?.call('x', value),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildNumberField(
                'Y',
                component.position.y,
                'Vertical position',
                (value) => widget.onPropertyChanged?.call('y', value),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                'Width',
                component.position.width,
                'Component width',
                (value) => widget.onPropertyChanged?.call('width', value),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildNumberField(
                'Height',
                component.position.height,
                'Component height',
                (value) => widget.onPropertyChanged?.call('height', value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildThemeTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('App Theme'),
        Text(
          'Choose a theme that matches your target platform',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 16),

        // Theme Selection
        ..._themeService.getThemes().map((theme) => _buildThemeCard(theme)),

        const SizedBox(height: 16),

        // Custom Theme Options
        if (widget.selectedTheme != null) ...[
          _buildSectionHeader('Theme Customization'),
          _buildColorPicker(
            'Primary Color',
            widget.selectedTheme!.colorScheme.primary,
            'Main brand color for your app',
            (color) => _updateThemeColor('primary', color),
          ),
          const SizedBox(height: 12),
          _buildColorPicker(
            'Secondary Color',
            widget.selectedTheme!.colorScheme.secondary,
            'Accent color for highlights',
            (color) => _updateThemeColor('secondary', color),
          ),
          const SizedBox(height: 12),
          _buildColorPicker(
            'Surface Color',
            widget.selectedTheme!.colorScheme.surface,
            'Background color for cards and surfaces',
            (color) => _updateThemeColor('surface', color),
          ),
        ],
      ],
    );
  }

  Widget _buildStyleTab() {
    if (widget.selectedComponent == null) {
      return _buildEmptyState(
        icon: Icons.style,
        title: 'No Component Selected',
        subtitle: 'Select a component to customize its appearance',
      );
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Appearance'),
        
        // Background Color
        _buildColorPicker(
          'Background Color',
          _parseColor(widget.selectedComponent!.style.backgroundColor),
          'Component background color',
          (color) => widget.onPropertyChanged?.call('backgroundColor', _colorToHex(color)),
        ),
        
        const SizedBox(height: 12),
        
        // Text Color
        _buildColorPicker(
          'Text Color',
          _parseColor(widget.selectedComponent!.style.textColor),
          'Text and icon color',
          (color) => widget.onPropertyChanged?.call('textColor', _colorToHex(color)),
        ),

        const SizedBox(height: 16),

        // Typography
        _buildSectionHeader('Typography'),
        _buildDropdownField(
          'Font Size',
          widget.selectedComponent!.style.fontSize ?? '14',
          ['10', '12', '14', '16', '18', '20', '24', '28', '32'],
          'Text size in pixels',
          (value) => widget.onPropertyChanged?.call('fontSize', value),
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildSwitchField(
                'Bold',
                widget.selectedComponent!.style.bold,
                'Make text bold',
                (value) => widget.onPropertyChanged?.call('bold', value),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSwitchField(
                'Italic',
                widget.selectedComponent!.style.italic,
                'Make text italic',
                (value) => widget.onPropertyChanged?.call('italic', value),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Border & Spacing
        _buildSectionHeader('Border & Spacing'),
        _buildNumberField(
          'Border Radius',
          widget.selectedComponent!.style.borderRadius ?? 0,
          'Corner roundness in pixels',
          (value) => widget.onPropertyChanged?.call('borderRadius', value),
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildNumberField(
                'Padding',
                widget.selectedComponent!.style.padding ?? 8,
                'Internal spacing',
                (value) => widget.onPropertyChanged?.call('padding', value),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildNumberField(
                'Margin',
                widget.selectedComponent!.style.margin ?? 0,
                'External spacing',
                (value) => widget.onPropertyChanged?.call('margin', value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGlobalTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('App Configuration'),
        
        _buildPropertyField(
          'App Name',
          'My Tool App',
          'The name of your generated app',
          (value) => widget.onGlobalSettingChanged?.call('appName', value),
        ),
        
        const SizedBox(height: 12),
        
        _buildPropertyField(
          'App Description',
          'Built with ShadowSuite Tool Builder',
          'Description for your app',
          (value) => widget.onGlobalSettingChanged?.call('appDescription', value),
        ),

        const SizedBox(height: 16),

        _buildSectionHeader('Export Settings'),
        
        _buildSwitchField(
          'Include Debug Info',
          false,
          'Include debugging information in exported code',
          (value) => widget.onGlobalSettingChanged?.call('includeDebugInfo', value),
        ),
        
        const SizedBox(height: 12),
        
        _buildSwitchField(
          'Optimize for Size',
          true,
          'Generate smaller but less readable code',
          (value) => widget.onGlobalSettingChanged?.call('optimizeForSize', value),
        ),

        const SizedBox(height: 16),

        _buildSectionHeader('Grid & Snapping'),
        
        _buildSwitchField(
          'Show Grid',
          true,
          'Display alignment grid in canvas',
          (value) => widget.onGlobalSettingChanged?.call('showGrid', value),
        ),
        
        const SizedBox(height: 12),
        
        _buildSwitchField(
          'Snap to Grid',
          true,
          'Automatically align components to grid',
          (value) => widget.onGlobalSettingChanged?.call('snapToGrid', value),
        ),
        
        const SizedBox(height: 12),
        
        _buildNumberField(
          'Grid Size',
          10,
          'Grid spacing in pixels',
          (value) => widget.onGlobalSettingChanged?.call('gridSize', value),
        ),
      ],
    );
  }

  // Helper methods for building UI components
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: children,
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyField(
    String label,
    String value,
    String tooltip,
    Function(String) onChanged, {
    Widget? suffixIcon,
  }) {
    return Tooltip(
      message: tooltip,
      child: TextField(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: suffixIcon,
          isDense: true,
        ),
        controller: TextEditingController(text: value),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildNumberField(
    String label,
    double value,
    String tooltip,
    Function(double) onChanged,
  ) {
    return Tooltip(
      message: tooltip,
      child: TextField(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          isDense: true,
        ),
        controller: TextEditingController(text: value.toString()),
        keyboardType: TextInputType.number,
        onChanged: (text) {
          final parsed = double.tryParse(text);
          if (parsed != null) onChanged(parsed);
        },
      ),
    );
  }

  Widget _buildDropdownField(
    String label,
    String value,
    List<String> options,
    String tooltip,
    Function(String) onChanged,
  ) {
    return Tooltip(
      message: tooltip,
      child: DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          isDense: true,
        ),
        value: options.contains(value) ? value : options.first,
        items: options.map((option) => DropdownMenuItem(
          value: option,
          child: Text(option),
        )).toList(),
        onChanged: (newValue) {
          if (newValue != null) onChanged(newValue);
        },
      ),
    );
  }

  Widget _buildSwitchField(
    String label,
    bool value,
    String tooltip,
    Function(bool) onChanged,
  ) {
    return Tooltip(
      message: tooltip,
      child: SwitchListTile(
        title: Text(label),
        value: value,
        onChanged: onChanged,
        dense: true,
      ),
    );
  }

  Widget _buildColorPicker(
    String label,
    Color? color,
    String tooltip,
    Function(Color) onChanged,
  ) {
    final currentColor = color ?? Colors.blue;
    
    return Tooltip(
      message: tooltip,
      child: ListTile(
        title: Text(label),
        trailing: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: currentColor,
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onTap: () => _showColorPicker(currentColor, onChanged),
        dense: true,
      ),
    );
  }

  Widget _buildThemeCard(MobileAppTheme theme) {
    final isSelected = widget.selectedTheme?.id == theme.id;
    
    return Card(
      elevation: isSelected ? 4 : 1,
      color: isSelected ? theme.colorScheme.primaryContainer : null,
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getThemeIcon(theme.type),
            color: theme.colorScheme.onPrimary,
          ),
        ),
        title: Text(theme.name),
        subtitle: Text(theme.description),
        trailing: isSelected ? const Icon(Icons.check_circle) : null,
        onTap: () => widget.onThemeChanged?.call(theme),
      ),
    );
  }

  List<Widget> _buildComponentSpecificProperties(UIComponent component) {
    // Return component-specific property fields based on type
    switch (component.type) {
      case ComponentType.textField:
        return [
          _buildPropertyField(
            'Placeholder',
            component.getProperty('placeholder', 'Enter text...'),
            'Hint text shown when field is empty',
            (value) => widget.onPropertyChanged?.call('placeholder', value),
          ),
        ];
      case ComponentType.button:
        return [
          _buildDropdownField(
            'Button Style',
            component.getProperty('style', 'elevated'),
            ['elevated', 'outlined', 'text'],
            'Visual style of the button',
            (value) => widget.onPropertyChanged?.call('style', value),
          ),
        ];
      default:
        return [];
    }
  }

  // Helper methods
  Color? _parseColor(String? colorString) {
    if (colorString == null) return null;
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return null;
    }
  }

  String _colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2)}';
  }

  IconData _getThemeIcon(MobileAppThemeType type) {
    switch (type) {
      case MobileAppThemeType.android:
        return Icons.android;
      case MobileAppThemeType.ios:
        return Icons.phone_iphone;
      case MobileAppThemeType.web:
        return Icons.web;
      case MobileAppThemeType.custom:
        return Icons.palette;
    }
  }

  void _showCellPicker() {
    // Implementation for cell picker dialog
  }

  void _showColorPicker(Color currentColor, Function(Color) onChanged) {
    // Implementation for color picker dialog
  }

  void _updateThemeColor(String colorType, Color color) {
    // Implementation for updating theme colors
  }
}
