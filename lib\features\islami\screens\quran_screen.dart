import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quran_models.dart';
import '../services/quran_service.dart';
import 'surah_detail_screen.dart';
import 'quran_search_screen.dart';

/// Quran main screen with Surah list and navigation
class QuranScreen extends ConsumerStatefulWidget {
  const QuranScreen({super.key});

  @override
  ConsumerState<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends ConsumerState<QuranScreen> {
  final QuranService _quranService = QuranService();
  List<Surah> _surahs = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _initializeQuran();
  }

  Future<void> _initializeQuran() async {
    try {
      await _quranService.initialize();
      setState(() {
        _surahs = _quranService.getSurahs();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading Quran: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading Quran...'),
          ],
        ),
      );
    }

    final filteredSurahs = _searchQuery.isEmpty
        ? _surahs
        : _surahs.where((surah) =>
            surah.nameEnglish.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            surah.nameArabic.contains(_searchQuery) ||
            surah.nameTransliteration.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            surah.number.toString().contains(_searchQuery)).toList();

    return Column(
      children: [
        // Quick actions and search
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Verse of the day card
              FutureBuilder<Verse?>(
                future: _quranService.getVerseOfTheDay(),
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data != null) {
                    return _buildVerseOfTheDayCard(snapshot.data!);
                  }
                  return const SizedBox.shrink();
                },
              ),
              
              const SizedBox(height: 16),
              
              // Quick actions
              Row(
                children: [
                  Expanded(
                    child: _buildQuickActionCard(
                      context,
                      'Continue Reading',
                      'Resume from last position',
                      Icons.play_circle_fill,
                      () => _continueReading(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildQuickActionCard(
                      context,
                      'Search',
                      'Find verses and topics',
                      Icons.search,
                      () => _openSearch(),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Search bar
              TextField(
                decoration: InputDecoration(
                  hintText: 'Search Surahs...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ],
          ),
        ),

        // Surah list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: filteredSurahs.length,
            itemBuilder: (context, index) {
              final surah = filteredSurahs[index];
              return _buildSurahTile(surah);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVerseOfTheDayCard(Verse verse) {
    return Card(
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primaryContainer,
              Theme.of(context).colorScheme.secondaryContainer,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Verse of the Day',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              verse.textArabic,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                height: 1.8,
              ),
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              verse.textTranslation,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              verse.verseReference,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSurahTile(Surah surah) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              surah.number.toString(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    surah.nameEnglish,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    surah.nameTransliteration,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              surah.nameArabic,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.w600,
              ),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Row(
            children: [
              Icon(
                surah.revelationType == 'Meccan' ? Icons.location_on : Icons.account_balance,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 4),
              Text(
                surah.revelationType,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.format_list_numbered,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 4),
              Text(
                '${surah.versesCount} verses',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _openSurah(surah),
      ),
    );
  }

  void _continueReading() {
    // Get last reading position and navigate to it
    final progress = _quranService.getReadingProgress();
    if (progress.isNotEmpty) {
      final lastProgress = progress.first;
      final surah = _quranService.getSurah(lastProgress.surahNumber);
      if (surah != null) {
        _openSurah(surah, startVerse: lastProgress.verseNumber);
      }
    } else {
      // Start from Al-Fatihah if no progress
      final surah = _quranService.getSurah(1);
      if (surah != null) {
        _openSurah(surah);
      }
    }
  }

  void _openSearch() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const QuranSearchScreen(),
      ),
    );
  }

  void _openSurah(Surah surah, {int? startVerse}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SurahDetailScreen(
          surah: surah,
          startVerse: startVerse,
        ),
      ),
    );
  }
}
