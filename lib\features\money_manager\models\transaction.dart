import 'package:flutter/material.dart';

/// Transaction model for Money Manager
class Transaction {
  final String id;
  final TransactionType type;
  final double amount;
  final String fromAccountId;
  final String? toAccountId; // For transfers
  final String categoryId;
  final String title;
  final String description;
  final DateTime date;
  final List<String> tags;
  final List<TransactionAttachment> attachments;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;
  final DateTime? modifiedAt;
  final bool isRecurring;
  final RecurrencePattern? recurrencePattern;
  final String? parentTransactionId; // For recurring transactions
  final TransactionStatus status;

  const Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.fromAccountId,
    this.toAccountId,
    required this.categoryId,
    required this.title,
    this.description = '',
    required this.date,
    this.tags = const [],
    this.attachments = const [],
    this.metadata = const {},
    required this.createdAt,
    this.modifiedAt,
    this.isRecurring = false,
    this.recurrencePattern,
    this.parentTransactionId,
    this.status = TransactionStatus.completed,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String,
      type: TransactionType.values[json['type'] as int],
      amount: (json['amount'] as num).toDouble(),
      fromAccountId: json['from_account_id'] as String,
      toAccountId: json['to_account_id'] as String?,
      categoryId: json['category_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      date: DateTime.parse(json['date'] as String),
      tags: (json['tags'] as List<dynamic>?)?.map((t) => t as String).toList() ?? [],
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((a) => TransactionAttachment.fromJson(a as Map<String, dynamic>))
          .toList() ?? [],
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
      modifiedAt: json['modified_at'] != null 
          ? DateTime.parse(json['modified_at'] as String)
          : null,
      isRecurring: json['is_recurring'] as bool? ?? false,
      recurrencePattern: json['recurrence_pattern'] != null
          ? RecurrencePattern.fromJson(json['recurrence_pattern'] as Map<String, dynamic>)
          : null,
      parentTransactionId: json['parent_transaction_id'] as String?,
      status: TransactionStatus.values[json['status'] as int? ?? 0],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'amount': amount,
      'from_account_id': fromAccountId,
      'to_account_id': toAccountId,
      'category_id': categoryId,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'tags': tags,
      'attachments': attachments.map((a) => a.toJson()).toList(),
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'modified_at': modifiedAt?.toIso8601String(),
      'is_recurring': isRecurring,
      'recurrence_pattern': recurrencePattern?.toJson(),
      'parent_transaction_id': parentTransactionId,
      'status': status.index,
    };
  }

  Transaction copyWith({
    String? id,
    TransactionType? type,
    double? amount,
    String? fromAccountId,
    String? toAccountId,
    String? categoryId,
    String? title,
    String? description,
    DateTime? date,
    List<String>? tags,
    List<TransactionAttachment>? attachments,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? modifiedAt,
    bool? isRecurring,
    RecurrencePattern? recurrencePattern,
    String? parentTransactionId,
    TransactionStatus? status,
  }) {
    return Transaction(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      fromAccountId: fromAccountId ?? this.fromAccountId,
      toAccountId: toAccountId ?? this.toAccountId,
      categoryId: categoryId ?? this.categoryId,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      tags: tags ?? this.tags,
      attachments: attachments ?? this.attachments,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? DateTime.now(),
      isRecurring: isRecurring ?? this.isRecurring,
      recurrencePattern: recurrencePattern ?? this.recurrencePattern,
      parentTransactionId: parentTransactionId ?? this.parentTransactionId,
      status: status ?? this.status,
    );
  }

  bool get isTransfer => type == TransactionType.transfer;
  bool get isIncome => type == TransactionType.income;
  bool get isExpense => type == TransactionType.expense;
  
  String get formattedAmount {
    final prefix = isExpense ? '-' : (isIncome ? '+' : '');
    return '$prefix\$${amount.toStringAsFixed(2)}';
  }

  Color get typeColor {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  IconData get typeIcon {
    switch (type) {
      case TransactionType.income:
        return Icons.add_circle;
      case TransactionType.expense:
        return Icons.remove_circle;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.completed:
        return 'Completed';
      case TransactionStatus.cancelled:
        return 'Cancelled';
      case TransactionStatus.failed:
        return 'Failed';
    }
  }

  Color get statusColor {
    switch (status) {
      case TransactionStatus.pending:
        return Colors.orange;
      case TransactionStatus.completed:
        return Colors.green;
      case TransactionStatus.cancelled:
        return Colors.grey;
      case TransactionStatus.failed:
        return Colors.red;
    }
  }
}

enum TransactionType {
  income,
  expense,
  transfer,
}

enum TransactionStatus {
  completed,
  pending,
  cancelled,
  failed,
}

/// Transaction attachment model
class TransactionAttachment {
  final String id;
  final String fileName;
  final String filePath;
  final String mimeType;
  final int fileSize;
  final DateTime uploadedAt;

  const TransactionAttachment({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.mimeType,
    required this.fileSize,
    required this.uploadedAt,
  });

  factory TransactionAttachment.fromJson(Map<String, dynamic> json) {
    return TransactionAttachment(
      id: json['id'] as String,
      fileName: json['file_name'] as String,
      filePath: json['file_path'] as String,
      mimeType: json['mime_type'] as String,
      fileSize: json['file_size'] as int,
      uploadedAt: DateTime.parse(json['uploaded_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'file_name': fileName,
      'file_path': filePath,
      'mime_type': mimeType,
      'file_size': fileSize,
      'uploaded_at': uploadedAt.toIso8601String(),
    };
  }

  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  bool get isImage => mimeType.startsWith('image/');
  bool get isPdf => mimeType == 'application/pdf';
  bool get isDocument => mimeType.contains('document') || mimeType.contains('text');

  IconData get icon {
    if (isImage) return Icons.image;
    if (isPdf) return Icons.picture_as_pdf;
    if (isDocument) return Icons.description;
    return Icons.attach_file;
  }
}

/// Recurrence pattern for recurring transactions
class RecurrencePattern {
  final RecurrenceType type;
  final int interval;
  final List<int>? daysOfWeek; // For weekly recurrence
  final int? dayOfMonth; // For monthly recurrence
  final DateTime? endDate;
  final int? maxOccurrences;

  const RecurrencePattern({
    required this.type,
    this.interval = 1,
    this.daysOfWeek,
    this.dayOfMonth,
    this.endDate,
    this.maxOccurrences,
  });

  factory RecurrencePattern.fromJson(Map<String, dynamic> json) {
    return RecurrencePattern(
      type: RecurrenceType.values[json['type'] as int],
      interval: json['interval'] as int? ?? 1,
      daysOfWeek: (json['days_of_week'] as List<dynamic>?)?.map((d) => d as int).toList(),
      dayOfMonth: json['day_of_month'] as int?,
      endDate: json['end_date'] != null ? DateTime.parse(json['end_date'] as String) : null,
      maxOccurrences: json['max_occurrences'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.index,
      'interval': interval,
      'days_of_week': daysOfWeek,
      'day_of_month': dayOfMonth,
      'end_date': endDate?.toIso8601String(),
      'max_occurrences': maxOccurrences,
    };
  }

  String get displayName {
    switch (type) {
      case RecurrenceType.daily:
        return interval == 1 ? 'Daily' : 'Every $interval days';
      case RecurrenceType.weekly:
        return interval == 1 ? 'Weekly' : 'Every $interval weeks';
      case RecurrenceType.monthly:
        return interval == 1 ? 'Monthly' : 'Every $interval months';
      case RecurrenceType.yearly:
        return interval == 1 ? 'Yearly' : 'Every $interval years';
    }
  }
}

enum RecurrenceType {
  daily,
  weekly,
  monthly,
  yearly,
}
