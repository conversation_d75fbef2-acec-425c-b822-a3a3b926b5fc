import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/custom_dhikr_routine.dart';
import '../providers/custom_dhikr_provider.dart';
import 'dhikr_routine_runner_screen.dart';

class CustomDhikrBuilderScreen extends ConsumerStatefulWidget {
  final CustomDhikrRoutine? routine;

  const CustomDhikrBuilderScreen({
    super.key,
    this.routine,
  });

  @override
  ConsumerState<CustomDhikrBuilderScreen> createState() => _CustomDhikrBuilderScreenState();
}

class _CustomDhikrBuilderScreenState extends ConsumerState<CustomDhikrBuilderScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  late CustomDhikrRoutine _routine;
  Color _selectedColor = Colors.blue;
  bool _enableReminders = false;
  bool _repeatRoutine = false;
  List<TimeOfDay> _reminderTimes = [];

  @override
  void initState() {
    super.initState();
    
    if (widget.routine != null) {
      _routine = widget.routine!;
      _loadRoutineData();
    } else {
      _routine = CustomDhikrRoutine.create(
        name: '',
        userId: '',
      );
    }
  }

  void _loadRoutineData() {
    _nameController.text = _routine.name;
    _descriptionController.text = _routine.description;
    _selectedColor = _routine.themeColor;
    _enableReminders = _routine.enableReminders;
    _repeatRoutine = _routine.repeatRoutine;
    _reminderTimes = List.from(_routine.reminderTimes);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.routine == null ? 'Create Custom Dhikr' : 'Edit Custom Dhikr'),
        backgroundColor: _selectedColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _previewRoutine,
            icon: const Icon(Icons.play_arrow),
          ),
          IconButton(
            onPressed: _saveRoutine,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic Information
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Routine Information',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            
                            TextFormField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: 'Routine Name',
                                border: OutlineInputBorder(),
                                hintText: 'e.g., Morning Dhikr, Evening Routine',
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a routine name';
                                }
                                return null;
                              },
                            ),
                            
                            const SizedBox(height: 16),
                            
                            TextFormField(
                              controller: _descriptionController,
                              decoration: const InputDecoration(
                                labelText: 'Description (optional)',
                                border: OutlineInputBorder(),
                                hintText: 'Brief description of this routine',
                              ),
                              maxLines: 2,
                            ),
                            
                            const SizedBox(height: 16),
                            
                            // Color Selection
                            Text(
                              'Theme Color',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              children: [
                                Colors.blue,
                                Colors.green,
                                Colors.purple,
                                Colors.orange,
                                Colors.red,
                                Colors.teal,
                                Colors.indigo,
                                Colors.brown,
                              ].map((color) {
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedColor = color;
                                    });
                                  },
                                  child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: color,
                                      shape: BoxShape.circle,
                                      border: _selectedColor == color
                                          ? Border.all(color: Colors.black, width: 3)
                                          : null,
                                    ),
                                    child: _selectedColor == color
                                        ? const Icon(Icons.check, color: Colors.white)
                                        : null,
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Settings
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Settings',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 8),
                            
                            SwitchListTile(
                              title: const Text('Repeat Routine'),
                              subtitle: const Text('Automatically restart when completed'),
                              value: _repeatRoutine,
                              onChanged: (value) {
                                setState(() {
                                  _repeatRoutine = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            
                            SwitchListTile(
                              title: const Text('Enable Reminders'),
                              subtitle: const Text('Set reminder times for this routine'),
                              value: _enableReminders,
                              onChanged: (value) {
                                setState(() {
                                  _enableReminders = value;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                            
                            if (_enableReminders) ...[
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Text(
                                    'Reminder Times',
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  const Spacer(),
                                  IconButton(
                                    onPressed: _addReminderTime,
                                    icon: const Icon(Icons.add),
                                  ),
                                ],
                              ),
                              ..._reminderTimes.map((time) => ListTile(
                                leading: const Icon(Icons.access_time),
                                title: Text(time.format(context)),
                                trailing: IconButton(
                                  onPressed: () => _removeReminderTime(time),
                                  icon: const Icon(Icons.delete),
                                ),
                                contentPadding: EdgeInsets.zero,
                              )),
                            ],
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Steps Section
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Dhikr Steps',
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                                const Spacer(),
                                IconButton(
                                  onPressed: _addStep,
                                  icon: const Icon(Icons.add),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            
                            if (_routine.steps.isEmpty) ...[
                              Container(
                                padding: const EdgeInsets.all(32),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.add_circle_outline,
                                      size: 48,
                                      color: Theme.of(context).colorScheme.outline,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'No steps added yet',
                                      style: Theme.of(context).textTheme.titleMedium,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Tap the + button to add your first dhikr step',
                                      style: Theme.of(context).textTheme.bodyMedium,
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            ] else ...[
                              ReorderableListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: _routine.steps.length,
                                onReorder: (oldIndex, newIndex) {
                                  setState(() {
                                    _routine.reorderSteps(oldIndex, newIndex);
                                  });
                                },
                                itemBuilder: (context, index) {
                                  final step = _routine.steps[index];
                                  return _buildStepCard(step, index);
                                },
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Bottom Action Bar
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _addStep,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Step'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _saveRoutine,
                      icon: const Icon(Icons.save),
                      label: const Text('Save Routine'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _selectedColor,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepCard(DhikrStep step, int index) {
    return Card(
      key: ValueKey(step.id),
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: step.color,
          child: Icon(
            _getStepIcon(step.type),
            color: Colors.white,
          ),
        ),
        title: Text(
          step.displayText.isEmpty ? 'Empty ${step.typeDisplayName}' : step.displayText,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          step.type == DhikrStepType.dhikr 
              ? '${step.targetCount} times'
              : step.type == DhikrStepType.pause
                  ? '${step.duration.inSeconds} seconds'
                  : 'Reminder',
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _editStep(step),
              icon: const Icon(Icons.edit),
            ),
            IconButton(
              onPressed: () => _deleteStep(step.id),
              icon: const Icon(Icons.delete),
            ),
            const Icon(Icons.drag_handle),
          ],
        ),
        onTap: () => _editStep(step),
      ),
    );
  }

  IconData _getStepIcon(DhikrStepType type) {
    switch (type) {
      case DhikrStepType.dhikr:
        return Icons.favorite;
      case DhikrStepType.pause:
        return Icons.pause;
      case DhikrStepType.reminder:
        return Icons.notifications;
    }
  }

  void _addStep() {
    _showStepEditor(null);
  }

  void _editStep(DhikrStep step) {
    _showStepEditor(step);
  }

  void _deleteStep(int stepId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Step'),
        content: const Text('Are you sure you want to delete this step?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _routine.removeStep(stepId);
              });
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showStepEditor(DhikrStep? step) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DhikrStepEditorScreen(
          step: step,
          onSave: (editedStep) {
            setState(() {
              if (step == null) {
                _routine.addStep(editedStep);
              } else {
                final index = _routine.steps.indexWhere((s) => s.id == step.id);
                if (index != -1) {
                  _routine.steps[index] = editedStep;
                }
              }
            });
          },
        ),
      ),
    );
  }

  void _addReminderTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    
    if (time != null && !_reminderTimes.contains(time)) {
      setState(() {
        _reminderTimes.add(time);
      });
    }
  }

  void _removeReminderTime(TimeOfDay time) {
    setState(() {
      _reminderTimes.remove(time);
    });
  }

  void _previewRoutine() {
    if (_routine.steps.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Add at least one step to preview')),
      );
      return;
    }

    // Update routine with current form data
    _routine.name = _nameController.text;
    _routine.description = _descriptionController.text;
    _routine.themeColor = _selectedColor;
    _routine.enableReminders = _enableReminders;
    _routine.repeatRoutine = _repeatRoutine;
    _routine.reminderTimes = List.from(_reminderTimes);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DhikrRoutineRunnerScreen(
          routine: _routine,
          isPreview: true,
        ),
      ),
    );
  }

  void _saveRoutine() {
    if (!_formKey.currentState!.validate()) return;
    
    if (_routine.steps.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one step')),
      );
      return;
    }

    _routine.name = _nameController.text.trim();
    _routine.description = _descriptionController.text.trim();
    _routine.themeColor = _selectedColor;
    _routine.enableReminders = _enableReminders;
    _routine.repeatRoutine = _repeatRoutine;
    _routine.reminderTimes = _reminderTimes;

    if (widget.routine == null) {
      ref.read(customDhikrRoutinesProvider.notifier).addRoutine(_routine);
    } else {
      ref.read(customDhikrRoutinesProvider.notifier).updateRoutine(_routine);
    }

    Navigator.of(context).pop();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          widget.routine == null 
              ? 'Custom dhikr routine created!' 
              : 'Custom dhikr routine updated!',
        ),
      ),
    );
  }
}

class DhikrStepEditorScreen extends StatefulWidget {
  final DhikrStep? step;
  final Function(DhikrStep) onSave;

  const DhikrStepEditorScreen({
    super.key,
    this.step,
    required this.onSave,
  });

  @override
  State<DhikrStepEditorScreen> createState() => _DhikrStepEditorScreenState();
}

class _DhikrStepEditorScreenState extends State<DhikrStepEditorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _arabicController = TextEditingController();
  final _transliterationController = TextEditingController();
  final _translationController = TextEditingController();
  final _reminderController = TextEditingController();

  DhikrStepType _selectedType = DhikrStepType.dhikr;
  int _targetCount = 1;
  Duration _duration = const Duration(seconds: 30);
  Color _selectedColor = Colors.blue;
  bool _playSound = true;
  double _volume = 0.7;

  @override
  void initState() {
    super.initState();

    if (widget.step != null) {
      _loadStepData();
    }
  }

  void _loadStepData() {
    final step = widget.step!;
    _arabicController.text = step.arabicText;
    _transliterationController.text = step.transliteration;
    _translationController.text = step.translation;
    _reminderController.text = step.reminderText;
    _selectedType = step.type;
    _targetCount = step.targetCount;
    _duration = step.duration;
    _selectedColor = step.color;
    _playSound = step.playSound;
    _volume = step.volume;
  }

  @override
  void dispose() {
    _arabicController.dispose();
    _transliterationController.dispose();
    _translationController.dispose();
    _reminderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.step == null ? 'Add Step' : 'Edit Step'),
        backgroundColor: _selectedColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _saveStep,
            icon: const Icon(Icons.save),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Step Type Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Step Type',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),

                      SegmentedButton<DhikrStepType>(
                        segments: const [
                          ButtonSegment(
                            value: DhikrStepType.dhikr,
                            label: Text('Dhikr'),
                            icon: Icon(Icons.favorite),
                          ),
                          ButtonSegment(
                            value: DhikrStepType.pause,
                            label: Text('Pause'),
                            icon: Icon(Icons.pause),
                          ),
                          ButtonSegment(
                            value: DhikrStepType.reminder,
                            label: Text('Reminder'),
                            icon: Icon(Icons.notifications),
                          ),
                        ],
                        selected: {_selectedType},
                        onSelectionChanged: (Set<DhikrStepType> selection) {
                          setState(() {
                            _selectedType = selection.first;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Content based on type
              if (_selectedType == DhikrStepType.dhikr) ...[
                _buildDhikrContent(),
              ] else if (_selectedType == DhikrStepType.pause) ...[
                _buildPauseContent(),
              ] else if (_selectedType == DhikrStepType.reminder) ...[
                _buildReminderContent(),
              ],

              const SizedBox(height: 16),

              // Appearance Settings
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Appearance',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),

                      // Color Selection
                      Text(
                        'Color',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: [
                          Colors.blue,
                          Colors.green,
                          Colors.red,
                          Colors.orange,
                          Colors.purple,
                          Colors.teal,
                          Colors.indigo,
                          Colors.brown,
                        ].map((color) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedColor = color;
                              });
                            },
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: color,
                                shape: BoxShape.circle,
                                border: _selectedColor == color
                                    ? Border.all(color: Colors.black, width: 3)
                                    : null,
                              ),
                              child: _selectedColor == color
                                  ? const Icon(Icons.check, color: Colors.white)
                                  : null,
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Audio Settings
              if (_selectedType == DhikrStepType.dhikr) ...[
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Audio Settings',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),

                        SwitchListTile(
                          title: const Text('Play Sound'),
                          subtitle: const Text('Play sound when counting'),
                          value: _playSound,
                          onChanged: (value) {
                            setState(() {
                              _playSound = value;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),

                        if (_playSound) ...[
                          const SizedBox(height: 16),
                          Text(
                            'Volume: ${(_volume * 100).round()}%',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          Slider(
                            value: _volume,
                            onChanged: (value) {
                              setState(() {
                                _volume = value;
                              });
                            },
                            activeColor: _selectedColor,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _saveStep,
                  icon: const Icon(Icons.save),
                  label: const Text('Save Step'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _selectedColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDhikrContent() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dhikr Content',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            // Arabic Text
            TextFormField(
              controller: _arabicController,
              decoration: const InputDecoration(
                labelText: 'Arabic Text',
                border: OutlineInputBorder(),
                hintText: 'Enter Arabic dhikr text',
              ),
              textDirection: TextDirection.rtl,
              style: const TextStyle(fontSize: 18),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter Arabic text';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Transliteration
            TextFormField(
              controller: _transliterationController,
              decoration: const InputDecoration(
                labelText: 'Transliteration',
                border: OutlineInputBorder(),
                hintText: 'Enter transliteration',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter transliteration';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Translation
            TextFormField(
              controller: _translationController,
              decoration: const InputDecoration(
                labelText: 'Translation',
                border: OutlineInputBorder(),
                hintText: 'Enter English translation',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter translation';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Target Count
            TextFormField(
              initialValue: _targetCount.toString(),
              decoration: const InputDecoration(
                labelText: 'Target Count',
                border: OutlineInputBorder(),
                hintText: 'Number of repetitions',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                final count = int.tryParse(value);
                if (count != null && count > 0) {
                  _targetCount = count;
                }
              },
              validator: (value) {
                final count = int.tryParse(value ?? '');
                if (count == null || count <= 0) {
                  return 'Please enter a valid count';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPauseContent() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pause Duration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            Text(
              'Duration: ${_duration.inSeconds} seconds',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),

            Slider(
              value: _duration.inSeconds.toDouble(),
              min: 5,
              max: 300,
              divisions: 59,
              label: '${_duration.inSeconds}s',
              onChanged: (value) {
                setState(() {
                  _duration = Duration(seconds: value.toInt());
                });
              },
              activeColor: _selectedColor,
            ),

            const SizedBox(height: 16),

            // Quick duration buttons
            Wrap(
              spacing: 8,
              children: [10, 30, 60, 120, 300].map((seconds) {
                return FilterChip(
                  label: Text('${seconds}s'),
                  selected: _duration.inSeconds == seconds,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _duration = Duration(seconds: seconds);
                      });
                    }
                  },
                  selectedColor: _selectedColor.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderContent() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reminder Message',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _reminderController,
              decoration: const InputDecoration(
                labelText: 'Reminder Text',
                border: OutlineInputBorder(),
                hintText: 'Enter reminder message',
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter reminder text';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  void _saveStep() {
    if (!_formKey.currentState!.validate()) return;

    final step = widget.step ?? DhikrStep();

    step.type = _selectedType;
    step.color = _selectedColor;
    step.playSound = _playSound;
    step.volume = _volume;

    switch (_selectedType) {
      case DhikrStepType.dhikr:
        step.arabicText = _arabicController.text.trim();
        step.transliteration = _transliterationController.text.trim();
        step.translation = _translationController.text.trim();
        step.targetCount = _targetCount;
        break;
      case DhikrStepType.pause:
        step.duration = _duration;
        break;
      case DhikrStepType.reminder:
        step.reminderText = _reminderController.text.trim();
        break;
    }

    step.updateTimestamp();
    widget.onSave(step);
    Navigator.of(context).pop();
  }
}
