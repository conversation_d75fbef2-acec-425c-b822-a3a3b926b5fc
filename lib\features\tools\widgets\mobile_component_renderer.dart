import 'package:flutter/material.dart';
import '../models/mobile_app_theme.dart';
import '../services/formula_engine_service.dart';

/// Renders mobile app components with theme support
class MobileComponentRenderer extends StatelessWidget {
  final MobileComponentData component;
  final MobileAppTheme theme;
  final Map<String, dynamic> cellValues;
  final FormulaEngineService? formulaEngine;
  final VoidCallback? onTap;
  final Function(String)? onChanged;

  const MobileComponentRenderer({
    super.key,
    required this.component,
    required this.theme,
    this.cellValues = const {},
    this.formulaEngine,
    this.onTap,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: theme.toThemeData(),
      child: _buildComponent(context),
    );
  }

  Widget _buildComponent(BuildContext context) {
    switch (component.type) {
      case MobileComponentType.scaffold:
        return _buildScaffold(context);
      case MobileComponentType.appBar:
        return _buildAppBar(context);
      case MobileComponentType.container:
        return _buildContainer(context);
      case MobileComponentType.column:
        return _buildColumn(context);
      case MobileComponentType.row:
        return _buildRow(context);
      case MobileComponentType.text:
        return _buildText(context);
      case MobileComponentType.button:
        return _buildButton(context);
      case MobileComponentType.textField:
        return _buildTextField(context);
      case MobileComponentType.card:
        return _buildCard(context);
      case MobileComponentType.listTile:
        return _buildListTile(context);
      case MobileComponentType.formulaOutput:
        return _buildFormulaOutput(context);
      case MobileComponentType.chart:
        return _buildChart(context);
      case MobileComponentType.floatingActionButton:
        return _buildFAB(context);
      case MobileComponentType.bottomNavBar:
        return _buildBottomNavBar(context);
      case MobileComponentType.drawer:
        return _buildDrawer(context);
      case MobileComponentType.tabBar:
        return _buildTabBar(context);
      default:
        return _buildPlaceholder(context);
    }
  }

  Widget _buildScaffold(BuildContext context) {
    final appBar = component.children
        .where((child) => child.type == MobileComponentType.appBar)
        .firstOrNull;
    
    final body = component.children
        .where((child) => child.type != MobileComponentType.appBar)
        .toList();

    final fab = component.children
        .where((child) => child.type == MobileComponentType.floatingActionButton)
        .firstOrNull;

    return Scaffold(
      appBar: appBar != null ? _buildAppBarWidget(context, appBar) : null,
      body: body.isNotEmpty ? _buildBody(context, body) : null,
      floatingActionButton: fab != null ? _buildFABWidget(context, fab) : null,
      backgroundColor: theme.colorScheme.surface,
    );
  }

  PreferredSizeWidget _buildAppBarWidget(BuildContext context, MobileComponentData appBarComponent) {
    final title = appBarComponent.properties['title'] as String? ?? appBarComponent.label;
    final centerTitle = appBarComponent.properties['centerTitle'] as bool? ?? false;
    final showBack = appBarComponent.properties['showBack'] as bool? ?? false;
    
    return AppBar(
      title: Text(title),
      centerTitle: centerTitle,
      automaticallyImplyLeading: showBack,
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
    );
  }

  Widget _buildBody(BuildContext context, List<MobileComponentData> bodyComponents) {
    if (bodyComponents.length == 1) {
      return MobileComponentRenderer(
        component: bodyComponents.first,
        theme: theme,
        cellValues: cellValues,
        formulaEngine: formulaEngine,
        onTap: onTap,
        onChanged: onChanged,
      );
    }

    return Column(
      children: bodyComponents.map((child) => Expanded(
        child: MobileComponentRenderer(
          component: child,
          theme: theme,
          cellValues: cellValues,
          formulaEngine: formulaEngine,
          onTap: onTap,
          onChanged: onChanged,
        ),
      )).toList(),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    // This is handled in scaffold
    return const SizedBox.shrink();
  }

  Widget _buildContainer(BuildContext context) {
    final padding = component.properties['padding'] as double? ?? 16.0;
    final margin = component.properties['margin'] as double? ?? 0.0;
    final color = component.style['backgroundColor'] as Color?;

    return Container(
      padding: EdgeInsets.all(padding),
      margin: EdgeInsets.all(margin),
      color: color,
      child: _buildChildren(context),
    );
  }

  Widget _buildColumn(BuildContext context) {
    final mainAxisAlignment = _getMainAxisAlignment(
      component.properties['mainAxisAlignment'] as String? ?? 'start'
    );
    final crossAxisAlignment = _getCrossAxisAlignment(
      component.properties['crossAxisAlignment'] as String? ?? 'center'
    );

    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: component.children.map((child) => 
        MobileComponentRenderer(
          component: child,
          theme: theme,
          cellValues: cellValues,
          formulaEngine: formulaEngine,
          onTap: onTap,
          onChanged: onChanged,
        ),
      ).toList(),
    );
  }

  Widget _buildRow(BuildContext context) {
    final mainAxisAlignment = _getMainAxisAlignment(
      component.properties['mainAxisAlignment'] as String? ?? 'start'
    );
    final crossAxisAlignment = _getCrossAxisAlignment(
      component.properties['crossAxisAlignment'] as String? ?? 'center'
    );

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: component.children.map((child) => 
        Expanded(
          child: MobileComponentRenderer(
            component: child,
            theme: theme,
            cellValues: cellValues,
            formulaEngine: formulaEngine,
            onTap: onTap,
            onChanged: onChanged,
          ),
        ),
      ).toList(),
    );
  }

  Widget _buildText(BuildContext context) {
    final text = _getDisplayValue();
    final fontSize = component.properties['fontSize'] as double? ?? 14.0;
    final fontWeight = _getFontWeight(component.properties['fontWeight'] as String? ?? 'normal');
    final color = component.style['color'] as Color? ?? theme.colorScheme.onSurface;
    final alignment = _getTextAlignment(component.properties['alignment'] as String? ?? 'left');

    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: alignment,
    );
  }

  Widget _buildButton(BuildContext context) {
    final text = component.properties['text'] as String? ?? component.label;
    final style = component.properties['style'] as String? ?? 'elevated';
    
    switch (style) {
      case 'elevated':
        return ElevatedButton(
          onPressed: onTap,
          child: Text(text),
        );
      case 'outlined':
        return OutlinedButton(
          onPressed: onTap,
          child: Text(text),
        );
      case 'text':
        return TextButton(
          onPressed: onTap,
          child: Text(text),
        );
      default:
        return ElevatedButton(
          onPressed: onTap,
          child: Text(text),
        );
    }
  }

  Widget _buildTextField(BuildContext context) {
    final hint = component.properties['hint'] as String? ?? '';
    final required = component.properties['required'] as bool? ?? false;
    final keyboardType = _getKeyboardType(component.properties['keyboardType'] as String? ?? 'text');

    return TextField(
      decoration: InputDecoration(
        labelText: component.label,
        hintText: hint,
        suffixText: required ? '*' : null,
      ),
      keyboardType: keyboardType,
      onChanged: onChanged,
    );
  }

  Widget _buildCard(BuildContext context) {
    final title = component.properties['title'] as String? ?? component.label;
    final icon = component.properties['icon'] as String?;
    final value = _getDisplayValue();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (icon != null) ...[
                  Icon(_getIconData(icon)),
                  const SizedBox(width: 8),
                ],
                Text(
                  title,
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListTile(BuildContext context) {
    final showSubtitle = component.properties['showSubtitle'] as bool? ?? false;
    final showTrailing = component.properties['showTrailing'] as bool? ?? false;
    final value = _getDisplayValue();

    return ListTile(
      title: Text(component.label),
      subtitle: showSubtitle ? Text(value) : null,
      trailing: showTrailing ? const Icon(Icons.chevron_right) : null,
      onTap: onTap,
    );
  }

  Widget _buildFormulaOutput(BuildContext context) {
    final value = _getDisplayValue();
    final fontSize = component.properties['fontSize'] as double? ?? 24.0;
    final alignment = _getTextAlignment(component.properties['alignment'] as String? ?? 'right');

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outline),
        borderRadius: BorderRadius.circular(8),
        color: theme.colorScheme.surfaceContainerHighest,
      ),
      child: Text(
        value,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
          fontFamily: 'monospace',
        ),
        textAlign: alignment,
      ),
    );
  }

  Widget _buildChart(BuildContext context) {
    final chartType = component.properties['chartType'] as String? ?? 'line';
    final title = component.properties['title'] as String? ?? 'Chart';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              title,
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: theme.colorScheme.outline),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    '$chartType Chart\n(${component.cellBinding ?? 'No data'})',
                    textAlign: TextAlign.center,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAB(BuildContext context) {
    return const SizedBox.shrink(); // Handled in scaffold
  }

  Widget _buildFABWidget(BuildContext context, MobileComponentData fabComponent) {
    final icon = fabComponent.properties['icon'] as String? ?? 'add';
    final tooltip = fabComponent.properties['tooltip'] as String? ?? '';

    return FloatingActionButton(
      onPressed: onTap,
      tooltip: tooltip,
      child: Icon(_getIconData(icon)),
    );
  }

  Widget _buildBottomNavBar(BuildContext context) {
    return BottomNavigationBar(
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
        BottomNavigationBarItem(icon: Icon(Icons.search), label: 'Search'),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
      ],
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return const Drawer(
      child: Column(
        children: [
          DrawerHeader(
            child: Text('Menu'),
          ),
          ListTile(
            leading: Icon(Icons.home),
            title: Text('Home'),
          ),
          ListTile(
            leading: Icon(Icons.settings),
            title: Text('Settings'),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    final tabs = component.properties['tabs'] as List<String>? ?? ['Tab 1', 'Tab 2'];
    
    return TabBar(
      tabs: tabs.map((tab) => Tab(text: tab)).toList(),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outline, style: BorderStyle.solid),
        borderRadius: BorderRadius.circular(4),
        color: theme.colorScheme.surfaceContainerLowest,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.widgets,
            color: theme.colorScheme.onSurfaceVariant,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            component.type.name.toUpperCase(),
            style: theme.textTheme.labelMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            component.label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChildren(BuildContext context) {
    if (component.children.isEmpty) {
      return const SizedBox.shrink();
    }

    if (component.children.length == 1) {
      return MobileComponentRenderer(
        component: component.children.first,
        theme: theme,
        cellValues: cellValues,
        formulaEngine: formulaEngine,
        onTap: onTap,
        onChanged: onChanged,
      );
    }

    return Column(
      children: component.children.map((child) => 
        MobileComponentRenderer(
          component: child,
          theme: theme,
          cellValues: cellValues,
          formulaEngine: formulaEngine,
          onTap: onTap,
          onChanged: onChanged,
        ),
      ).toList(),
    );
  }

  String _getDisplayValue() {
    if (component.cellBinding != null && cellValues.containsKey(component.cellBinding)) {
      return cellValues[component.cellBinding].toString();
    }
    
    if (component.formula != null && formulaEngine != null) {
      final result = formulaEngine!.evaluateFormula(component.formula!, {});
      return result.toString();
    }
    
    return component.label;
  }

  // Helper methods for property conversion
  MainAxisAlignment _getMainAxisAlignment(String alignment) {
    switch (alignment) {
      case 'start': return MainAxisAlignment.start;
      case 'center': return MainAxisAlignment.center;
      case 'end': return MainAxisAlignment.end;
      case 'spaceBetween': return MainAxisAlignment.spaceBetween;
      case 'spaceAround': return MainAxisAlignment.spaceAround;
      case 'spaceEvenly': return MainAxisAlignment.spaceEvenly;
      default: return MainAxisAlignment.start;
    }
  }

  CrossAxisAlignment _getCrossAxisAlignment(String alignment) {
    switch (alignment) {
      case 'start': return CrossAxisAlignment.start;
      case 'center': return CrossAxisAlignment.center;
      case 'end': return CrossAxisAlignment.end;
      case 'stretch': return CrossAxisAlignment.stretch;
      default: return CrossAxisAlignment.center;
    }
  }

  FontWeight _getFontWeight(String weight) {
    switch (weight) {
      case 'light': return FontWeight.w300;
      case 'normal': return FontWeight.w400;
      case 'medium': return FontWeight.w500;
      case 'semibold': return FontWeight.w600;
      case 'bold': return FontWeight.w700;
      default: return FontWeight.w400;
    }
  }

  TextAlign _getTextAlignment(String alignment) {
    switch (alignment) {
      case 'left': return TextAlign.left;
      case 'center': return TextAlign.center;
      case 'right': return TextAlign.right;
      case 'justify': return TextAlign.justify;
      default: return TextAlign.left;
    }
  }

  TextInputType _getKeyboardType(String type) {
    switch (type) {
      case 'text': return TextInputType.text;
      case 'number': return TextInputType.number;
      case 'email': return TextInputType.emailAddress;
      case 'phone': return TextInputType.phone;
      case 'url': return TextInputType.url;
      default: return TextInputType.text;
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'add': return Icons.add;
      case 'home': return Icons.home;
      case 'settings': return Icons.settings;
      case 'person': return Icons.person;
      case 'search': return Icons.search;
      case 'attach_money': return Icons.attach_money;
      case 'people': return Icons.people;
      case 'chart': return Icons.show_chart;
      default: return Icons.widgets;
    }
  }
}
