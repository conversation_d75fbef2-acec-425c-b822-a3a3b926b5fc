enum TodoPriority { low, medium, high, urgent }

enum RecurrenceType { none, daily, weekly, monthly, yearly }

enum TodoStatus { pending, inProgress, completed, cancelled, onHold }

class Todo {
  int id = 0;
  String title = '';
  String description = '';
  bool isCompleted = false;
  TodoStatus status = TodoStatus.pending;
  DateTime? dueDate;
  DateTime? startDate;
  TodoPriority priority = TodoPriority.medium;
  RecurrenceType recurrenceType = RecurrenceType.none;
  int recurrenceInterval = 1;
  List<String> tags = [];
  String category = 'general';
  DateTime? completedAt;
  DateTime? reminderTime;
  String userId = '';

  // Subtasks and dependencies
  int? parentTodoId; // For subtasks
  List<int> subtaskIds = []; // Child todo IDs
  List<int> dependsOnIds = []; // Todo IDs this depends on
  List<int> blocksIds = []; // Todo IDs this blocks

  // Progress tracking
  double progress = 0.0; // 0.0 to 1.0
  int estimatedMinutes = 0;
  int actualMinutes = 0;

  // Additional metadata
  String? location;
  String? url;
  List<String> attachments = [];
  Map<String, dynamic> customFields = {};

  // Timestamps
  DateTime createdAt = DateTime.now();
  DateTime updatedAt = DateTime.now();
  DateTime? syncedAt;
  bool isDeleted = false;
  String? syncId;

  Todo();

  Todo.create({
    required this.title,
    required this.userId,
    this.description = '',
    this.dueDate,
    this.startDate,
    this.priority = TodoPriority.medium,
    this.recurrenceType = RecurrenceType.none,
    this.recurrenceInterval = 1,
    this.category = 'general',
    this.reminderTime,
    this.parentTodoId,
    this.estimatedMinutes = 0,
    this.location,
    this.url,
    List<String>? tags,
    List<String>? attachments,
    Map<String, dynamic>? customFields,
  }) {
    this.tags = tags ?? [];
    this.attachments = attachments ?? [];
    this.customFields = customFields ?? {};
    final now = DateTime.now();
    createdAt = now;
    updatedAt = now;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void markSynced() {
    syncedAt = DateTime.now();
  }

  void softDelete() {
    isDeleted = true;
    updateTimestamp();
  }

  void restore() {
    isDeleted = false;
    updateTimestamp();
  }

  bool get needsSync => syncedAt == null || updatedAt.isAfter(syncedAt!);

  void updateTodo({
    String? title,
    String? description,
    DateTime? dueDate,
    DateTime? startDate,
    TodoPriority? priority,
    TodoStatus? status,
    RecurrenceType? recurrenceType,
    int? recurrenceInterval,
    String? category,
    DateTime? reminderTime,
    List<String>? tags,
    double? progress,
    int? estimatedMinutes,
    int? actualMinutes,
    String? location,
    String? url,
    List<String>? attachments,
    Map<String, dynamic>? customFields,
  }) {
    if (title != null) this.title = title;
    if (description != null) this.description = description;
    if (dueDate != null) this.dueDate = dueDate;
    if (startDate != null) this.startDate = startDate;
    if (priority != null) this.priority = priority;
    if (status != null) this.status = status;
    if (recurrenceType != null) this.recurrenceType = recurrenceType;
    if (recurrenceInterval != null) this.recurrenceInterval = recurrenceInterval;
    if (category != null) this.category = category;
    if (reminderTime != null) this.reminderTime = reminderTime;
    if (tags != null) this.tags = tags;
    if (progress != null) this.progress = progress.clamp(0.0, 1.0);
    if (estimatedMinutes != null) this.estimatedMinutes = estimatedMinutes;
    if (actualMinutes != null) this.actualMinutes = actualMinutes;
    if (location != null) this.location = location;
    if (url != null) this.url = url;
    if (attachments != null) this.attachments = attachments;
    if (customFields != null) this.customFields = customFields;

    updateTimestamp();
  }

  // Subtask management
  void addSubtask(int subtaskId) {
    if (!subtaskIds.contains(subtaskId)) {
      subtaskIds.add(subtaskId);
      updateTimestamp();
    }
  }

  void removeSubtask(int subtaskId) {
    if (subtaskIds.remove(subtaskId)) {
      updateTimestamp();
    }
  }

  bool get hasSubtasks => subtaskIds.isNotEmpty;
  bool get isSubtask => parentTodoId != null;

  // Dependency management
  void addDependency(int todoId) {
    if (!dependsOnIds.contains(todoId)) {
      dependsOnIds.add(todoId);
      updateTimestamp();
    }
  }

  void removeDependency(int todoId) {
    if (dependsOnIds.remove(todoId)) {
      updateTimestamp();
    }
  }

  void addBlocks(int todoId) {
    if (!blocksIds.contains(todoId)) {
      blocksIds.add(todoId);
      updateTimestamp();
    }
  }

  void removeBlocks(int todoId) {
    if (blocksIds.remove(todoId)) {
      updateTimestamp();
    }
  }

  bool get hasDependencies => dependsOnIds.isNotEmpty;
  bool get blocksOthers => blocksIds.isNotEmpty;

  // Progress management
  void updateProgress(double newProgress) {
    progress = newProgress.clamp(0.0, 1.0);
    if (progress >= 1.0 && !isCompleted) {
      complete();
    } else if (progress < 1.0 && isCompleted) {
      uncomplete();
    }
    updateTimestamp();
  }

  void addTimeSpent(int minutes) {
    actualMinutes += minutes;
    updateTimestamp();
  }

  double get timeEfficiency {
    if (estimatedMinutes == 0) return 1.0;
    return estimatedMinutes / actualMinutes;
  }

  bool get isOnTime {
    if (dueDate == null) return true;
    return !DateTime.now().isAfter(dueDate!);
  }

  void complete() {
    if (!isCompleted) {
      isCompleted = true;
      status = TodoStatus.completed;
      progress = 1.0;
      completedAt = DateTime.now();
      updateTimestamp();

      // Create next occurrence if recurring
      if (recurrenceType != RecurrenceType.none) {
        _scheduleNextOccurrence();
      }
    }
  }

  void uncomplete() {
    if (isCompleted) {
      isCompleted = false;
      status = TodoStatus.pending;
      progress = 0.0;
      completedAt = null;
      updateTimestamp();
    }
  }

  void start() {
    if (status == TodoStatus.pending) {
      status = TodoStatus.inProgress;
      startDate ??= DateTime.now();
      updateTimestamp();
    }
  }

  void pause() {
    if (status == TodoStatus.inProgress) {
      status = TodoStatus.onHold;
      updateTimestamp();
    }
  }

  void cancel() {
    status = TodoStatus.cancelled;
    updateTimestamp();
  }

  void resume() {
    if (status == TodoStatus.onHold) {
      status = TodoStatus.inProgress;
      updateTimestamp();
    }
  }

  void _scheduleNextOccurrence() {
    if (dueDate == null) return;
    
    DateTime nextDue;
    switch (recurrenceType) {
      case RecurrenceType.daily:
        nextDue = dueDate!.add(Duration(days: recurrenceInterval));
        break;
      case RecurrenceType.weekly:
        nextDue = dueDate!.add(Duration(days: 7 * recurrenceInterval));
        break;
      case RecurrenceType.monthly:
        nextDue = DateTime(
          dueDate!.year,
          dueDate!.month + recurrenceInterval,
          dueDate!.day,
          dueDate!.hour,
          dueDate!.minute,
        );
        break;
      case RecurrenceType.yearly:
        nextDue = DateTime(
          dueDate!.year + recurrenceInterval,
          dueDate!.month,
          dueDate!.day,
          dueDate!.hour,
          dueDate!.minute,
        );
        break;
      case RecurrenceType.none:
        return;
    }
    
    dueDate = nextDue;
    if (reminderTime != null) {
      final timeDiff = reminderTime!.difference(dueDate!);
      reminderTime = nextDue.add(timeDiff);
    }
  }

  bool get isOverdue {
    if (dueDate == null || isCompleted) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  bool get isDueToday {
    if (dueDate == null || isCompleted) return false;
    final now = DateTime.now();
    final due = dueDate!;
    return now.year == due.year && now.month == due.month && now.day == due.day;
  }

  bool containsSearchTerm(String searchTerm) {
    final term = searchTerm.toLowerCase();
    return title.toLowerCase().contains(term) ||
           description.toLowerCase().contains(term) ||
           category.toLowerCase().contains(term) ||
           tags.any((tag) => tag.toLowerCase().contains(term));
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'isCompleted': isCompleted,
      'status': status.name,
      'dueDate': dueDate?.toIso8601String(),
      'startDate': startDate?.toIso8601String(),
      'priority': priority.name,
      'recurrenceType': recurrenceType.name,
      'recurrenceInterval': recurrenceInterval,
      'tags': tags,
      'category': category,
      'completedAt': completedAt?.toIso8601String(),
      'reminderTime': reminderTime?.toIso8601String(),
      'userId': userId,
      'parentTodoId': parentTodoId,
      'subtaskIds': subtaskIds,
      'dependsOnIds': dependsOnIds,
      'blocksIds': blocksIds,
      'progress': progress,
      'estimatedMinutes': estimatedMinutes,
      'actualMinutes': actualMinutes,
      'location': location,
      'url': url,
      'attachments': attachments,
      'customFields': customFields,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'syncedAt': syncedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'syncId': syncId,
    };
  }

  factory Todo.fromJson(Map<String, dynamic> json) {
    final todo = Todo();
    todo.id = json['id'] ?? 0;
    todo.title = json['title'] ?? '';
    todo.description = json['description'] ?? '';
    todo.isCompleted = json['isCompleted'] ?? false;
    todo.status = TodoStatus.values.firstWhere(
      (s) => s.name == json['status'],
      orElse: () => TodoStatus.pending,
    );
    todo.dueDate = json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null;
    todo.startDate = json['startDate'] != null ? DateTime.parse(json['startDate']) : null;
    todo.priority = TodoPriority.values.firstWhere(
      (p) => p.name == json['priority'],
      orElse: () => TodoPriority.medium,
    );
    todo.recurrenceType = RecurrenceType.values.firstWhere(
      (r) => r.name == json['recurrenceType'],
      orElse: () => RecurrenceType.none,
    );
    todo.recurrenceInterval = json['recurrenceInterval'] ?? 1;
    todo.tags = List<String>.from(json['tags'] ?? []);
    todo.category = json['category'] ?? 'general';
    todo.completedAt = json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null;
    todo.reminderTime = json['reminderTime'] != null ? DateTime.parse(json['reminderTime']) : null;
    todo.userId = json['userId'] ?? '';
    todo.parentTodoId = json['parentTodoId'];
    todo.subtaskIds = List<int>.from(json['subtaskIds'] ?? []);
    todo.dependsOnIds = List<int>.from(json['dependsOnIds'] ?? []);
    todo.blocksIds = List<int>.from(json['blocksIds'] ?? []);
    todo.progress = (json['progress'] ?? 0.0).toDouble();
    todo.estimatedMinutes = json['estimatedMinutes'] ?? 0;
    todo.actualMinutes = json['actualMinutes'] ?? 0;
    todo.location = json['location'];
    todo.url = json['url'];
    todo.attachments = List<String>.from(json['attachments'] ?? []);
    todo.customFields = Map<String, dynamic>.from(json['customFields'] ?? {});
    todo.createdAt = DateTime.parse(json['createdAt']);
    todo.updatedAt = DateTime.parse(json['updatedAt']);
    todo.syncedAt = json['syncedAt'] != null ? DateTime.parse(json['syncedAt']) : null;
    todo.isDeleted = json['isDeleted'] ?? false;
    todo.syncId = json['syncId'];
    return todo;
  }
}
