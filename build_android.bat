@echo off
echo ========================================
echo ShadowSuite Tool Builder - Android Build
echo ========================================
echo.

:: Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter and try again
    pause
    exit /b 1
)

:: Check if Android SDK is configured
flutter doctor --android-licenses >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Android SDK may not be properly configured
    echo Please run 'flutter doctor' to check Android setup
)

echo [1/5] Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Failed to clean project
    pause
    exit /b 1
)

echo [2/5] Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)

echo [3/5] Building Android APK (Release)...
flutter build apk --release --verbose
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Android APK
    pause
    exit /b 1
)

echo [4/5] Building Android App Bundle (Release)...
flutter build appbundle --release --verbose
if %errorlevel% neq 0 (
    echo WARNING: Failed to build Android App Bundle, continuing...
)

echo [5/5] Creating distribution folder...
if not exist "dist" mkdir dist
if not exist "dist\android" mkdir "dist\android"

:: Copy APK
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    copy "build\app\outputs\flutter-apk\app-release.apk" "dist\android\ShadowSuite_Tool_Builder.apk"
    echo APK copied to: dist\android\ShadowSuite_Tool_Builder.apk
)

:: Copy App Bundle
if exist "build\app\outputs\bundle\release\app-release.aab" (
    copy "build\app\outputs\bundle\release\app-release.aab" "dist\android\ShadowSuite_Tool_Builder.aab"
    echo App Bundle copied to: dist\android\ShadowSuite_Tool_Builder.aab
)

:: Create installation instructions
echo ShadowSuite Tool Builder - Android Release > "dist\android\README.txt"
echo. >> "dist\android\README.txt"
echo INSTALLATION: >> "dist\android\README.txt"
echo. >> "dist\android\README.txt"
echo APK Installation (Direct): >> "dist\android\README.txt"
echo 1. Enable "Unknown Sources" in Android Settings >> "dist\android\README.txt"
echo 2. Transfer ShadowSuite_Tool_Builder.apk to your Android device >> "dist\android\README.txt"
echo 3. Tap the APK file to install >> "dist\android\README.txt"
echo. >> "dist\android\README.txt"
echo App Bundle (Google Play Store): >> "dist\android\README.txt"
echo 1. Upload ShadowSuite_Tool_Builder.aab to Google Play Console >> "dist\android\README.txt"
echo 2. Follow Google Play publishing guidelines >> "dist\android\README.txt"
echo. >> "dist\android\README.txt"
echo SYSTEM REQUIREMENTS: >> "dist\android\README.txt"
echo - Android 5.0 (API level 21) or later >> "dist\android\README.txt"
echo - 2GB RAM minimum, 4GB recommended >> "dist\android\README.txt"
echo - 100MB free storage space >> "dist\android\README.txt"
echo. >> "dist\android\README.txt"
echo FEATURES: >> "dist\android\README.txt"
echo - Touch-optimized interface >> "dist\android\README.txt"
echo - Mobile app builder >> "dist\android\README.txt"
echo - Excel formula engine >> "dist\android\README.txt"
echo - Real-time preview >> "dist\android\README.txt"
echo - Export capabilities >> "dist\android\README.txt"
echo. >> "dist\android\README.txt"
echo Copyright (C) 2025 ShadowSuite. All rights reserved. >> "dist\android\README.txt"

echo.
echo ========================================
echo ANDROID BUILD COMPLETED!
echo ========================================
echo.
echo Distribution folder: dist\android
if exist "dist\android\ShadowSuite_Tool_Builder.apk" (
    echo APK: ShadowSuite_Tool_Builder.apk
)
if exist "dist\android\ShadowSuite_Tool_Builder.aab" (
    echo App Bundle: ShadowSuite_Tool_Builder.aab
)
echo.
echo To test the APK:
echo 1. Install on Android device or emulator
echo 2. Enable "Unknown Sources" if installing directly
echo.
echo To publish to Google Play:
echo 1. Use the .aab file (App Bundle)
echo 2. Upload to Google Play Console
echo 3. Follow Google Play publishing guidelines
echo.
pause
