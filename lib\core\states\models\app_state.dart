import 'package:flutter/material.dart';

enum StateType {
  loading,
  empty,
  error,
  success,
  offline,
  maintenance,
}

enum LoadingType {
  initial,
  refresh,
  loadMore,
  action,
  background,
}

enum ErrorType {
  network,
  server,
  validation,
  permission,
  notFound,
  timeout,
  unknown,
}

class AppState<T> {
  final StateType type;
  final T? data;
  final String? message;
  final dynamic error;
  final StackTrace? stackTrace;
  final LoadingType? loadingType;
  final ErrorType? errorType;
  final bool canRetry;
  final VoidCallback? onRetry;
  final Map<String, dynamic>? metadata;

  const AppState._({
    required this.type,
    this.data,
    this.message,
    this.error,
    this.stackTrace,
    this.loadingType,
    this.errorType,
    this.canRetry = false,
    this.onRetry,
    this.metadata,
  });

  // Factory constructors
  factory AppState.loading({
    LoadingType type = LoadingType.initial,
    String? message,
    T? data,
  }) {
    return AppState._(
      type: StateType.loading,
      loadingType: type,
      message: message,
      data: data,
    );
  }

  factory AppState.success(T data, {String? message}) {
    return AppState._(
      type: StateType.success,
      data: data,
      message: message,
    );
  }

  factory AppState.empty({
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    return AppState._(
      type: StateType.empty,
      message: message,
      metadata: metadata,
    );
  }

  factory AppState.error(
    dynamic error, {
    ErrorType type = ErrorType.unknown,
    String? message,
    StackTrace? stackTrace,
    bool canRetry = true,
    VoidCallback? onRetry,
    T? data,
  }) {
    return AppState._(
      type: StateType.error,
      error: error,
      errorType: type,
      message: message,
      stackTrace: stackTrace,
      canRetry: canRetry,
      onRetry: onRetry,
      data: data,
    );
  }

  factory AppState.offline({
    String? message,
    VoidCallback? onRetry,
    T? data,
  }) {
    return AppState._(
      type: StateType.offline,
      message: message ?? 'No internet connection',
      canRetry: true,
      onRetry: onRetry,
      data: data,
    );
  }

  factory AppState.maintenance({
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    return AppState._(
      type: StateType.maintenance,
      message: message ?? 'App is under maintenance',
      metadata: metadata,
    );
  }

  // Getters
  bool get isLoading => type == StateType.loading;
  bool get isSuccess => type == StateType.success;
  bool get isEmpty => type == StateType.empty;
  bool get isError => type == StateType.error;
  bool get isOffline => type == StateType.offline;
  bool get isMaintenance => type == StateType.maintenance;
  bool get hasData => data != null;

  // State transformations
  AppState<T> copyWith({
    StateType? type,
    T? data,
    String? message,
    dynamic error,
    StackTrace? stackTrace,
    LoadingType? loadingType,
    ErrorType? errorType,
    bool? canRetry,
    VoidCallback? onRetry,
    Map<String, dynamic>? metadata,
  }) {
    return AppState._(
      type: type ?? this.type,
      data: data ?? this.data,
      message: message ?? this.message,
      error: error ?? this.error,
      stackTrace: stackTrace ?? this.stackTrace,
      loadingType: loadingType ?? this.loadingType,
      errorType: errorType ?? this.errorType,
      canRetry: canRetry ?? this.canRetry,
      onRetry: onRetry ?? this.onRetry,
      metadata: metadata ?? this.metadata,
    );
  }

  AppState<U> map<U>(U Function(T) mapper) {
    if (data == null) {
      return AppState._(
        type: type,
        message: message,
        error: error,
        stackTrace: stackTrace,
        loadingType: loadingType,
        errorType: errorType,
        canRetry: canRetry,
        onRetry: onRetry,
        metadata: metadata,
      );
    }

    return AppState._(
      type: type,
      data: mapper(data!),
      message: message,
      error: error,
      stackTrace: stackTrace,
      loadingType: loadingType,
      errorType: errorType,
      canRetry: canRetry,
      onRetry: onRetry,
      metadata: metadata,
    );
  }

  // When pattern matching
  R when<R>({
    required R Function(T? data, LoadingType? type, String? message) loading,
    required R Function(T data, String? message) success,
    required R Function(String? message, Map<String, dynamic>? metadata) empty,
    required R Function(dynamic error, ErrorType? type, String? message, bool canRetry, VoidCallback? onRetry) error,
    required R Function(String? message, VoidCallback? onRetry) offline,
    required R Function(String? message, Map<String, dynamic>? metadata) maintenance,
  }) {
    switch (type) {
      case StateType.loading:
        return loading(data, loadingType, message);
      case StateType.success:
        return success(data!, message);
      case StateType.empty:
        return empty(message, metadata);
      case StateType.error:
        return error(this.error, errorType, message, canRetry, onRetry);
      case StateType.offline:
        return offline(message, onRetry);
      case StateType.maintenance:
        return maintenance(message, metadata);
    }
  }

  // Maybe pattern matching (returns null for unhandled cases)
  R? maybeWhen<R>({
    R Function(T? data, LoadingType? type, String? message)? loading,
    R Function(T data, String? message)? success,
    R Function(String? message, Map<String, dynamic>? metadata)? empty,
    R Function(dynamic error, ErrorType? type, String? message, bool canRetry, VoidCallback? onRetry)? error,
    R Function(String? message, VoidCallback? onRetry)? offline,
    R Function(String? message, Map<String, dynamic>? metadata)? maintenance,
    R Function()? orElse,
  }) {
    switch (type) {
      case StateType.loading:
        return loading?.call(data, loadingType, message) ?? orElse?.call();
      case StateType.success:
        return success?.call(data!, message) ?? orElse?.call();
      case StateType.empty:
        return empty?.call(message, metadata) ?? orElse?.call();
      case StateType.error:
        return error?.call(this.error, errorType, message, canRetry, onRetry) ?? orElse?.call();
      case StateType.offline:
        return offline?.call(message, onRetry) ?? orElse?.call();
      case StateType.maintenance:
        return maintenance?.call(message, metadata) ?? orElse?.call();
    }
  }

  @override
  String toString() {
    return 'AppState<$T>('
        'type: $type, '
        'hasData: $hasData, '
        'message: $message, '
        'error: $error'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppState<T> &&
        other.type == type &&
        other.data == data &&
        other.message == message &&
        other.error == error &&
        other.loadingType == loadingType &&
        other.errorType == errorType &&
        other.canRetry == canRetry;
  }

  @override
  int get hashCode {
    return Object.hash(
      type,
      data,
      message,
      error,
      loadingType,
      errorType,
      canRetry,
    );
  }
}

// Specialized states for common use cases
class ListState<T> extends AppState<List<T>> {
  ListState._(AppState<List<T>> state) : super._(
    type: state.type,
    data: state.data,
    message: state.message,
    error: state.error,
    stackTrace: state.stackTrace,
    loadingType: state.loadingType,
    errorType: state.errorType,
    canRetry: state.canRetry,
    onRetry: state.onRetry,
    metadata: state.metadata,
  );

  factory ListState.loading({
    LoadingType type = LoadingType.initial,
    String? message,
    List<T>? data,
  }) {
    return ListState._(AppState.loading(
      type: type,
      message: message,
      data: data,
    ));
  }

  factory ListState.success(List<T> data, {String? message}) {
    return ListState._(AppState.success(data, message: message));
  }

  factory ListState.empty({String? message}) {
    return ListState._(AppState.empty(message: message));
  }

  factory ListState.error(
    dynamic error, {
    ErrorType type = ErrorType.unknown,
    String? message,
    StackTrace? stackTrace,
    bool canRetry = true,
    VoidCallback? onRetry,
    List<T>? data,
  }) {
    return ListState._(AppState.error(
      error,
      type: type,
      message: message,
      stackTrace: stackTrace,
      canRetry: canRetry,
      onRetry: onRetry,
      data: data,
    ));
  }

  bool get isEmptyList => isSuccess && (data?.isEmpty ?? true);
  int get itemCount => data?.length ?? 0;
  bool get hasItems => itemCount > 0;
}

// Pagination state
class PaginatedState<T> extends ListState<T> {
  final bool hasMore;
  final bool isLoadingMore;
  final int currentPage;
  final int totalPages;
  final int totalItems;

  PaginatedState._({
    required AppState<List<T>> state,
    this.hasMore = false,
    this.isLoadingMore = false,
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalItems = 0,
  }) : super._(state);

  factory PaginatedState.loading({
    LoadingType type = LoadingType.initial,
    String? message,
    List<T>? data,
    bool hasMore = false,
    int currentPage = 1,
  }) {
    return PaginatedState._(
      state: AppState.loading(type: type, message: message, data: data),
      hasMore: hasMore,
      currentPage: currentPage,
    );
  }

  factory PaginatedState.success(
    List<T> data, {
    String? message,
    bool hasMore = false,
    int currentPage = 1,
    int totalPages = 1,
    int totalItems = 0,
  }) {
    return PaginatedState._(
      state: AppState.success(data, message: message),
      hasMore: hasMore,
      currentPage: currentPage,
      totalPages: totalPages,
      totalItems: totalItems,
    );
  }

  factory PaginatedState.loadingMore(
    List<T> currentData, {
    bool hasMore = true,
    int currentPage = 1,
  }) {
    return PaginatedState._(
      state: AppState.success(currentData),
      hasMore: hasMore,
      isLoadingMore: true,
      currentPage: currentPage,
    );
  }

  PaginatedState<T> copyWithPagination({
    bool? hasMore,
    bool? isLoadingMore,
    int? currentPage,
    int? totalPages,
    int? totalItems,
  }) {
    return PaginatedState._(
      state: this,
      hasMore: hasMore ?? this.hasMore,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalItems: totalItems ?? this.totalItems,
    );
  }
}
