import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note.dart';
import '../providers/notes_provider.dart';

class CategoryFilterChips extends ConsumerWidget {
  const CategoryFilterChips({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedCategory = ref.watch(selectedCategoryProvider);
    final allCategories = ref.read(notesProvider.notifier).getAllCategories();
    
    if (allCategories.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          // All categories chip
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: const Text('All'),
              selected: selectedCategory == null,
              onSelected: (selected) {
                ref.read(selectedCategoryProvider.notifier).state = null;
              },
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              selectedColor: Colors.deepPurple.withValues(alpha: 0.2),
              checkmarkColor: Colors.deepPurple,
              labelStyle: TextStyle(
                color: selectedCategory == null ? Colors.deepPurple : null,
                fontWeight: selectedCategory == null ? FontWeight.bold : null,
              ),
            ),
          ),
          
          // Category chips
          ...allCategories.map((categoryName) {
            final category = _getCategoryFromName(categoryName);
            final isSelected = selectedCategory == categoryName;
            
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      category.icon,
                      size: 16,
                      color: isSelected ? Colors.white : category.color,
                    ),
                    const SizedBox(width: 4),
                    Text(categoryName),
                  ],
                ),
                selected: isSelected,
                onSelected: (selected) {
                  ref.read(selectedCategoryProvider.notifier).state = 
                      selected ? categoryName : null;
                },
                backgroundColor: category.color.withValues(alpha: 0.1),
                selectedColor: category.color,
                checkmarkColor: Colors.white,
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : category.color,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                ),
                side: BorderSide(
                  color: category.color.withValues(alpha: 0.5),
                  width: 1,
                ),
              ),
            );
          }),
          
          // Add category button
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: ActionChip(
              label: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.add, size: 16),
                  SizedBox(width: 4),
                  Text('Add Category'),
                ],
              ),
              onPressed: () => _showAddCategoryDialog(context, ref),
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              side: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
              ),
            ),
          ),
        ],
      ),
    );
  }

  NoteCategory _getCategoryFromName(String categoryName) {
    return NoteCategory.values.firstWhere(
      (cat) => cat.displayName == categoryName,
      orElse: () => NoteCategory.custom,
    );
  }

  void _showAddCategoryDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => const AddCategoryDialog(),
    );
  }
}

class AddCategoryDialog extends ConsumerStatefulWidget {
  const AddCategoryDialog({super.key});

  @override
  ConsumerState<AddCategoryDialog> createState() => _AddCategoryDialogState();
}

class _AddCategoryDialogState extends ConsumerState<AddCategoryDialog> {
  final TextEditingController _nameController = TextEditingController();
  NoteCategory _selectedTemplate = NoteCategory.custom;
  Color _selectedColor = Colors.blue;
  IconData _selectedIcon = Icons.category;

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Custom Category'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category name
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Category Name',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            
            // Template selection
            Text(
              'Template',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: NoteCategory.values.map((category) {
                final isSelected = _selectedTemplate == category;
                return FilterChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(category.icon, size: 16),
                      const SizedBox(width: 4),
                      Text(category.displayName),
                    ],
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedTemplate = category;
                        _selectedColor = category.color;
                        _selectedIcon = category.icon;
                      });
                    }
                  },
                  backgroundColor: category.color.withValues(alpha: 0.1),
                  selectedColor: category.color.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            
            // Color selection
            Text(
              'Color',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                Colors.blue, Colors.green, Colors.orange, Colors.red,
                Colors.purple, Colors.teal, Colors.pink, Colors.brown,
                Colors.indigo, Colors.cyan, Colors.lime, Colors.amber,
              ].map((color) {
                final isSelected = _selectedColor == color;
                return GestureDetector(
                  onTap: () => setState(() => _selectedColor = color),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: isSelected ? Border.all(
                        color: Colors.black,
                        width: 3,
                      ) : null,
                    ),
                    child: isSelected ? const Icon(
                      Icons.check,
                      color: Colors.white,
                    ) : null,
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            
            // Icon selection
            Text(
              'Icon',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                Icons.category, Icons.work, Icons.school, Icons.home,
                Icons.favorite, Icons.star, Icons.lightbulb, Icons.shopping_cart,
                Icons.fitness_center, Icons.music_note, Icons.camera, Icons.travel_explore,
              ].map((icon) {
                final isSelected = _selectedIcon == icon;
                return GestureDetector(
                  onTap: () => setState(() => _selectedIcon = icon),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isSelected ? _selectedColor.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected ? Border.all(
                        color: _selectedColor,
                        width: 2,
                      ) : null,
                    ),
                    child: Icon(
                      icon,
                      color: isSelected ? _selectedColor : Colors.grey,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _nameController.text.isNotEmpty ? _addCategory : null,
          child: const Text('Add'),
        ),
      ],
    );
  }

  void _addCategory() {
    final categoryName = _nameController.text.trim();
    if (categoryName.isNotEmpty) {
      // TODO: Add custom category to database/storage
      // For now, just close the dialog
      Navigator.of(context).pop();
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Category "$categoryName" added successfully!'),
          backgroundColor: _selectedColor,
        ),
      );
    }
  }
}
