import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/appearance_settings.dart' as app_settings;

/// Provider for managing appearance customization settings
class AppearanceSettingsNotifier extends StateNotifier<app_settings.AppearanceSettings> {
  static const String _storageKey = 'appearance_settings';

  AppearanceSettingsNotifier() : super(const app_settings.AppearanceSettings()) {
    _loadSettings();
  }

  /// Load settings from persistent storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);
      
      if (jsonString != null) {
        state = app_settings.AppearanceSettings.fromJsonString(jsonString);
      }
    } catch (e) {
      // If loading fails, keep default settings
      print('Failed to load appearance settings: $e');
    }
  }

  /// Save settings to persistent storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_storageKey, state.toJsonString());
    } catch (e) {
      print('Failed to save appearance settings: $e');
    }
  }

  /// Update icon settings
  Future<void> updateIconSettings({
    double? iconSize,
    app_settings.IconStyle? iconStyle,
    double? iconSpacing,
  }) async {
    state = state.copyWith(
      iconSize: iconSize,
      iconStyle: iconStyle,
      iconSpacing: iconSpacing,
    );
    await _saveSettings();
  }

  /// Update tab settings
  Future<void> updateTabSettings({
    double? tabSize,
    double? tabPadding,
    double? tabBorderRadius,
    Color? tabActiveColor,
    Color? tabInactiveColor,
  }) async {
    state = state.copyWith(
      tabSize: tabSize,
      tabPadding: tabPadding,
      tabBorderRadius: tabBorderRadius,
      tabActiveColor: tabActiveColor,
      tabInactiveColor: tabInactiveColor,
    );
    await _saveSettings();
  }

  /// Update border settings
  Future<void> updateBorderSettings({
    double? borderThickness,
    Color? borderColor,
    app_settings.BorderStyle? borderStyle,
  }) async {
    state = state.copyWith(
      borderThickness: borderThickness,
      borderColor: borderColor,
      borderStyle: borderStyle,
    );
    await _saveSettings();
  }

  /// Update header settings
  Future<void> updateHeaderSettings({
    double? headerHeight,
    double? headerThickness,
    Color? headerBackground,
    double? headerTextSize,
  }) async {
    state = state.copyWith(
      headerHeight: headerHeight,
      headerThickness: headerThickness,
      headerBackground: headerBackground,
      headerTextSize: headerTextSize,
    );
    await _saveSettings();
  }

  /// Update spacing settings
  Future<void> updateSpacingSettings({
    double? marginHorizontal,
    double? marginVertical,
    double? paddingHorizontal,
    double? paddingVertical,
  }) async {
    state = state.copyWith(
      marginHorizontal: marginHorizontal,
      marginVertical: marginVertical,
      paddingHorizontal: paddingHorizontal,
      paddingVertical: paddingVertical,
    );
    await _saveSettings();
  }

  /// Update color settings
  Future<void> updateColorSettings({
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? surfaceColor,
    Color? errorColor,
  }) async {
    state = state.copyWith(
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundColor: backgroundColor,
      surfaceColor: surfaceColor,
      errorColor: errorColor,
    );
    await _saveSettings();
  }

  /// Update typography settings
  Future<void> updateTypographySettings({
    double? bodyTextSize,
    double? headingTextSize,
    FontWeight? textWeight,
    String? fontFamily,
  }) async {
    state = state.copyWith(
      bodyTextSize: bodyTextSize,
      headingTextSize: headingTextSize,
      textWeight: textWeight,
      fontFamily: fontFamily,
    );
    await _saveSettings();
  }

  /// Update animation settings
  Future<void> updateAnimationSettings({
    double? animationSpeed,
    bool? animationsEnabled,
    Curve? animationCurve,
  }) async {
    state = state.copyWith(
      animationSpeed: animationSpeed,
      animationsEnabled: animationsEnabled,
      animationCurve: animationCurve,
    );
    await _saveSettings();
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    state = const app_settings.AppearanceSettings();
    await _saveSettings();
  }

  /// Import settings from JSON string
  Future<void> importSettings(String jsonString) async {
    try {
      state = app_settings.AppearanceSettings.fromJsonString(jsonString);
      await _saveSettings();
    } catch (e) {
      throw Exception('Failed to import settings: $e');
    }
  }

  /// Export settings as JSON string
  String exportSettings() {
    return state.toJsonString();
  }
}

/// Provider for appearance settings
final appearanceSettingsProvider = StateNotifierProvider<AppearanceSettingsNotifier, app_settings.AppearanceSettings>(
  (ref) => AppearanceSettingsNotifier(),
);

/// Computed providers for specific setting categories
final iconSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(appearanceSettingsProvider);
  return {
    'iconSize': settings.iconSize,
    'iconStyle': settings.iconStyle,
    'iconSpacing': settings.iconSpacing,
  };
});

final tabSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(appearanceSettingsProvider);
  return {
    'tabSize': settings.tabSize,
    'tabPadding': settings.tabPadding,
    'tabBorderRadius': settings.tabBorderRadius,
    'tabActiveColor': settings.tabActiveColor,
    'tabInactiveColor': settings.tabInactiveColor,
  };
});

final colorSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(appearanceSettingsProvider);
  return {
    'primaryColor': settings.primaryColor,
    'secondaryColor': settings.secondaryColor,
    'backgroundColor': settings.backgroundColor,
    'surfaceColor': settings.surfaceColor,
    'errorColor': settings.errorColor,
  };
});

final typographySettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(appearanceSettingsProvider);
  return {
    'bodyTextSize': settings.bodyTextSize,
    'headingTextSize': settings.headingTextSize,
    'textWeight': settings.textWeight,
    'fontFamily': settings.fontFamily,
  };
});

final animationSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(appearanceSettingsProvider);
  return {
    'animationSpeed': settings.animationSpeed,
    'animationsEnabled': settings.animationsEnabled,
    'animationCurve': settings.animationCurve,
  };
});
