import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr_session.dart';
import '../../../core/database/database_service.dart';
import '../../../shared/providers/user_provider.dart';

// Dhikr sessions provider
final dhikrSessionsProvider = StateNotifierProvider<DhikrSessionNotifier, List<DhikrSession>>((ref) {
  return DhikrSessionNotifier(ref);
});

class DhikrSessionNotifier extends StateNotifier<List<DhikrSession>> {
  final Ref ref;
  final DatabaseService _db = DatabaseService.instance;

  DhikrSessionNotifier(this.ref) : super([]) {
    loadSessions();
  }

  Future<void> loadSessions() async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final sessions = await _db.getAllDhikrSessions(userId: userProfile.id.toString());
      state = sessions;
    }
  }

  Future<DhikrSession> startSession(int dhikrId, int targetCount) async {
    final userProfile = ref.read(userProfileProvider);
    if (userProfile != null) {
      final session = DhikrSession.create(
        dhikrId: dhikrId,
        targetCount: targetCount,
        userId: userProfile.id.toString(),
      );
      
      final savedSession = await _db.saveDhikrSession(session);
      state = [savedSession, ...state];
      return savedSession;
    }
    throw Exception('User not found');
  }

  Future<void> updateSession(DhikrSession session) async {
    final savedSession = await _db.saveDhikrSession(session);
    state = state.map((s) => s.id == savedSession.id ? savedSession : s).toList();
  }

  Future<void> incrementCount(int sessionId) async {
    final sessionIndex = state.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      final session = state[sessionIndex];
      session.increment();
      await updateSession(session);
    }
  }

  Future<void> decrementCount(int sessionId) async {
    final sessionIndex = state.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      final session = state[sessionIndex];
      session.decrement();
      await updateSession(session);
    }
  }

  Future<void> setCount(int sessionId, int count) async {
    final sessionIndex = state.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      final session = state[sessionIndex];
      session.setCount(count);
      await updateSession(session);
    }
  }

  Future<void> resetSession(int sessionId) async {
    final sessionIndex = state.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      final session = state[sessionIndex];
      session.reset();
      await updateSession(session);
    }
  }

  Future<void> completeSession(int sessionId) async {
    final sessionIndex = state.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      final session = state[sessionIndex];
      session.complete();
      await updateSession(session);
    }
  }

  Future<void> deleteSession(int sessionId) async {
    await _db.deleteDhikrSession(sessionId);
    state = state.where((s) => s.id != sessionId).toList();
  }

  List<DhikrSession> getActiveSessions() {
    return state.where((session) => !session.isCompleted).toList();
  }

  List<DhikrSession> getCompletedSessions() {
    return state.where((session) => session.isCompleted).toList();
  }

  DhikrSession? getActiveSessionForDhikr(int dhikrId) {
    try {
      return state.firstWhere(
        (session) => session.dhikrId == dhikrId && !session.isCompleted,
      );
    } catch (e) {
      return null;
    }
  }
}

// Active sessions provider
final activeSessionsProvider = Provider<List<DhikrSession>>((ref) {
  final sessions = ref.watch(dhikrSessionsProvider);
  return sessions.where((session) => !session.isCompleted).toList();
});

// Completed sessions provider
final completedSessionsProvider = Provider<List<DhikrSession>>((ref) {
  final sessions = ref.watch(dhikrSessionsProvider);
  return sessions.where((session) => session.isCompleted).toList();
});

// Session statistics provider
final sessionStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final sessions = ref.watch(dhikrSessionsProvider);
  final completedSessions = sessions.where((s) => s.isCompleted).toList();
  
  final totalCount = completedSessions.fold<int>(
    0, 
    (sum, session) => sum + session.currentCount,
  );
  
  final totalDuration = completedSessions.fold<int>(
    0,
    (sum, session) => sum + session.durationSeconds,
  );
  
  final averageDuration = completedSessions.isNotEmpty 
      ? totalDuration / completedSessions.length 
      : 0.0;
  
  final today = DateTime.now();
  final todaySessions = completedSessions.where((session) {
    return session.endTime != null &&
           session.endTime!.year == today.year &&
           session.endTime!.month == today.month &&
           session.endTime!.day == today.day;
  }).toList();
  
  final todayCount = todaySessions.fold<int>(
    0,
    (sum, session) => sum + session.currentCount,
  );
  
  return {
    'totalSessions': sessions.length,
    'completedSessions': completedSessions.length,
    'activeSessions': sessions.length - completedSessions.length,
    'totalCount': totalCount,
    'totalDurationMinutes': (totalDuration / 60).round(),
    'averageDurationMinutes': (averageDuration / 60).round(),
    'todaySessions': todaySessions.length,
    'todayCount': todayCount,
  };
});

// Current active session provider for a specific dhikr
final activeSessionForDhikrProvider = Provider.family<DhikrSession?, int>((ref, dhikrId) {
  final sessions = ref.watch(dhikrSessionsProvider);
  try {
    return sessions.firstWhere(
      (session) => session.dhikrId == dhikrId && !session.isCompleted,
    );
  } catch (e) {
    return null;
  }
});
