import 'dart:convert';
import 'package:flutter/material.dart';
import '../models/tool_definition.dart';
import '../models/spreadsheet_cell.dart';
import '../models/ui_component.dart';

/// Service for managing tool templates
class TemplateService {
  static final TemplateService _instance = TemplateService._internal();
  factory TemplateService() => _instance;
  TemplateService._internal();

  /// Get all available templates
  List<ToolTemplate> getTemplates() {
    return [
      _createCalculatorTemplate(),
      _createBudgetPlannerTemplate(),
      _createLoanCalculatorTemplate(),
      _createGradeCalculatorTemplate(),
      _createBMICalculatorTemplate(),
      _createCurrencyConverterTemplate(),
      _createTipCalculatorTemplate(),
      _createCompoundInterestTemplate(),
      _createProjectTimelineTemplate(),
      _createInventoryTrackerTemplate(),
    ];
  }

  /// Get template by ID
  ToolTemplate? getTemplate(String id) {
    try {
      return getTemplates().firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get templates by category
  List<ToolTemplate> getTemplatesByCategory(String category) {
    return getTemplates().where((template) => template.category == category).toList();
  }

  /// Get all categories
  List<String> getCategories() {
    final categories = getTemplates().map((template) => template.category).toSet().toList();
    categories.sort();
    return categories;
  }

  /// Create tool from template
  ToolDefinition createToolFromTemplate(ToolTemplate template, String toolName) {
    return ToolDefinition(
      name: toolName,
      description: template.description,
      backendData: jsonEncode(template.backendData.map((key, value) => MapEntry(key, value.toJson()))),
      uiLayout: jsonEncode(template.uiComponents.map((component) => component.toJson()).toList()),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      userId: 'user_001', // TODO: Get actual user ID
    );
  }

  // Template creation methods
  ToolTemplate _createCalculatorTemplate() {
    final backendData = <String, SpreadsheetCell>{
      'A1': SpreadsheetCell(address: 'A1', value: 0, type: CellType.number),
      'A2': SpreadsheetCell(address: 'A2', value: 0, type: CellType.number),
      'A3': SpreadsheetCell(address: 'A3', value: '=A1+A2', type: CellType.formula, formula: '=A1+A2'),
      'A4': SpreadsheetCell(address: 'A4', value: '=A1-A2', type: CellType.formula, formula: '=A1-A2'),
      'A5': SpreadsheetCell(address: 'A5', value: '=A1*A2', type: CellType.formula, formula: '=A1*A2'),
      'A6': SpreadsheetCell(address: 'A6', value: '=A1/A2', type: CellType.formula, formula: '=A1/A2'),
    };

    final uiComponents = [
      UIComponent(
        id: 'input1',
        type: ComponentType.textField,
        label: 'First Number',
        bindToCell: 'A1',
        properties: {'placeholder': 'Enter first number'},
        position: const ComponentPosition(x: 50, y: 50, width: 200, height: 60),
      ),
      UIComponent(
        id: 'input2',
        type: ComponentType.textField,
        label: 'Second Number',
        bindToCell: 'A2',
        properties: {'placeholder': 'Enter second number'},
        position: const ComponentPosition(x: 50, y: 130, width: 200, height: 60),
      ),
      UIComponent(
        id: 'add_result',
        type: ComponentType.outputField,
        label: 'Addition',
        bindToCell: 'A3',
        properties: {},
        position: const ComponentPosition(x: 300, y: 50, width: 150, height: 40),
      ),
      UIComponent(
        id: 'sub_result',
        type: ComponentType.outputField,
        label: 'Subtraction',
        bindToCell: 'A4',
        properties: {},
        position: const ComponentPosition(x: 300, y: 100, width: 150, height: 40),
      ),
      UIComponent(
        id: 'mul_result',
        type: ComponentType.outputField,
        label: 'Multiplication',
        bindToCell: 'A5',
        properties: {},
        position: const ComponentPosition(x: 300, y: 150, width: 150, height: 40),
      ),
      UIComponent(
        id: 'div_result',
        type: ComponentType.outputField,
        label: 'Division',
        bindToCell: 'A6',
        properties: {},
        position: const ComponentPosition(x: 300, y: 200, width: 150, height: 40),
      ),
    ];

    return ToolTemplate(
      id: 'calculator',
      name: 'Basic Calculator',
      description: 'Simple calculator with basic arithmetic operations',
      category: 'Math',
      icon: Icons.calculate,
      color: Colors.blue,
      backendData: backendData,
      uiComponents: uiComponents,
      tags: ['math', 'calculator', 'arithmetic'],
      difficulty: TemplateDifficulty.beginner,
    );
  }

  ToolTemplate _createBudgetPlannerTemplate() {
    final backendData = <String, SpreadsheetCell>{
      'A1': SpreadsheetCell(address: 'A1', value: 'Income', type: CellType.text),
      'B1': SpreadsheetCell(address: 'B1', value: 5000, type: CellType.number),
      'A2': SpreadsheetCell(address: 'A2', value: 'Housing', type: CellType.text),
      'B2': SpreadsheetCell(address: 'B2', value: 1500, type: CellType.number),
      'A3': SpreadsheetCell(address: 'A3', value: 'Food', type: CellType.text),
      'B3': SpreadsheetCell(address: 'B3', value: 600, type: CellType.number),
      'A4': SpreadsheetCell(address: 'A4', value: 'Transportation', type: CellType.text),
      'B4': SpreadsheetCell(address: 'B4', value: 400, type: CellType.number),
      'A5': SpreadsheetCell(address: 'A5', value: 'Total Expenses', type: CellType.text),
      'B5': SpreadsheetCell(address: 'B5', value: '=B2+B3+B4', type: CellType.formula, formula: '=B2+B3+B4'),
      'A6': SpreadsheetCell(address: 'A6', value: 'Remaining', type: CellType.text),
      'B6': SpreadsheetCell(address: 'B6', value: '=B1-B5', type: CellType.formula, formula: '=B1-B5'),
    };

    final uiComponents = [
      UIComponent(
        id: 'income_input',
        type: ComponentType.textField,
        label: 'Monthly Income',
        bindToCell: 'B1',
        properties: {'placeholder': 'Enter monthly income'},
        position: const ComponentPosition(x: 50, y: 50, width: 200, height: 60),
      ),
      UIComponent(
        id: 'housing_input',
        type: ComponentType.textField,
        label: 'Housing Costs',
        bindToCell: 'B2',
        properties: {'placeholder': 'Enter housing costs'},
        position: const ComponentPosition(x: 50, y: 130, width: 200, height: 60),
      ),
      UIComponent(
        id: 'food_input',
        type: ComponentType.textField,
        label: 'Food Expenses',
        bindToCell: 'B3',
        properties: {'placeholder': 'Enter food expenses'},
        position: const ComponentPosition(x: 50, y: 210, width: 200, height: 60),
      ),
      UIComponent(
        id: 'transport_input',
        type: ComponentType.textField,
        label: 'Transportation',
        bindToCell: 'B4',
        properties: {'placeholder': 'Enter transportation costs'},
        position: const ComponentPosition(x: 50, y: 290, width: 200, height: 60),
      ),
      UIComponent(
        id: 'total_expenses',
        type: ComponentType.outputField,
        label: 'Total Expenses',
        bindToCell: 'B5',
        properties: {},
        position: const ComponentPosition(x: 300, y: 150, width: 200, height: 60),
      ),
      UIComponent(
        id: 'remaining_budget',
        type: ComponentType.outputField,
        label: 'Remaining Budget',
        bindToCell: 'B6',
        properties: {},
        position: const ComponentPosition(x: 300, y: 230, width: 200, height: 60),
      ),
    ];

    return ToolTemplate(
      id: 'budget_planner',
      name: 'Budget Planner',
      description: 'Track income and expenses to manage your budget',
      category: 'Finance',
      icon: Icons.account_balance_wallet,
      color: Colors.green,
      backendData: backendData,
      uiComponents: uiComponents,
      tags: ['finance', 'budget', 'money', 'planning'],
      difficulty: TemplateDifficulty.intermediate,
    );
  }

  ToolTemplate _createLoanCalculatorTemplate() {
    final backendData = <String, SpreadsheetCell>{
      'A1': SpreadsheetCell(address: 'A1', value: 100000, type: CellType.number), // Principal
      'A2': SpreadsheetCell(address: 'A2', value: 5.5, type: CellType.number), // Interest rate
      'A3': SpreadsheetCell(address: 'A3', value: 30, type: CellType.number), // Years
      'A4': SpreadsheetCell(address: 'A4', value: '=A3*12', type: CellType.formula, formula: '=A3*12'), // Months
      'A5': SpreadsheetCell(address: 'A5', value: '=A2/100/12', type: CellType.formula, formula: '=A2/100/12'), // Monthly rate
      'A6': SpreadsheetCell(address: 'A6', value: '=A1*(A5*(1+A5)^A4)/((1+A5)^A4-1)', type: CellType.formula, formula: '=A1*(A5*(1+A5)^A4)/((1+A5)^A4-1)'), // Monthly payment
      'A7': SpreadsheetCell(address: 'A7', value: '=A6*A4', type: CellType.formula, formula: '=A6*A4'), // Total payment
      'A8': SpreadsheetCell(address: 'A8', value: '=A7-A1', type: CellType.formula, formula: '=A7-A1'), // Total interest
    };

    final uiComponents = [
      UIComponent(
        id: 'principal_input',
        type: ComponentType.textField,
        label: 'Loan Amount',
        bindToCell: 'A1',
        properties: {'placeholder': 'Enter loan amount'},
        position: const ComponentPosition(x: 50, y: 50, width: 200, height: 60),
      ),
      UIComponent(
        id: 'rate_input',
        type: ComponentType.textField,
        label: 'Interest Rate (%)',
        bindToCell: 'A2',
        properties: {'placeholder': 'Enter annual interest rate'},
        position: const ComponentPosition(x: 50, y: 130, width: 200, height: 60),
      ),
      UIComponent(
        id: 'years_input',
        type: ComponentType.textField,
        label: 'Loan Term (Years)',
        bindToCell: 'A3',
        properties: {'placeholder': 'Enter loan term in years'},
        position: const ComponentPosition(x: 50, y: 210, width: 200, height: 60),
      ),
      UIComponent(
        id: 'monthly_payment',
        type: ComponentType.outputField,
        label: 'Monthly Payment',
        bindToCell: 'A6',
        properties: {},
        position: const ComponentPosition(x: 300, y: 50, width: 200, height: 60),
      ),
      UIComponent(
        id: 'total_payment',
        type: ComponentType.outputField,
        label: 'Total Payment',
        bindToCell: 'A7',
        properties: {},
        position: const ComponentPosition(x: 300, y: 130, width: 200, height: 60),
      ),
      UIComponent(
        id: 'total_interest',
        type: ComponentType.outputField,
        label: 'Total Interest',
        bindToCell: 'A8',
        properties: {},
        position: const ComponentPosition(x: 300, y: 210, width: 200, height: 60),
      ),
    ];

    return ToolTemplate(
      id: 'loan_calculator',
      name: 'Loan Calculator',
      description: 'Calculate monthly payments and total interest for loans',
      category: 'Finance',
      icon: Icons.monetization_on,
      color: Colors.orange,
      backendData: backendData,
      uiComponents: uiComponents,
      tags: ['finance', 'loan', 'mortgage', 'calculator'],
      difficulty: TemplateDifficulty.intermediate,
    );
  }

  ToolTemplate _createGradeCalculatorTemplate() {
    final backendData = <String, SpreadsheetCell>{
      'A1': SpreadsheetCell(address: 'A1', value: 85, type: CellType.number),
      'A2': SpreadsheetCell(address: 'A2', value: 92, type: CellType.number),
      'A3': SpreadsheetCell(address: 'A3', value: 78, type: CellType.number),
      'A4': SpreadsheetCell(address: 'A4', value: 88, type: CellType.number),
      'A5': SpreadsheetCell(address: 'A5', value: '=AVERAGE(A1:A4)', type: CellType.formula, formula: '=AVERAGE(A1:A4)'),
      'A6': SpreadsheetCell(address: 'A6', value: '=IF(A5>=90,"A",IF(A5>=80,"B",IF(A5>=70,"C",IF(A5>=60,"D","F"))))', type: CellType.formula, formula: '=IF(A5>=90,"A",IF(A5>=80,"B",IF(A5>=70,"C",IF(A5>=60,"D","F"))))'),
    };

    final uiComponents = [
      UIComponent(
        id: 'grade1',
        type: ComponentType.textField,
        label: 'Assignment 1',
        bindToCell: 'A1',
        properties: {'placeholder': 'Enter grade'},
        position: const ComponentPosition(x: 50, y: 50, width: 150, height: 60),
      ),
      UIComponent(
        id: 'grade2',
        type: ComponentType.textField,
        label: 'Assignment 2',
        bindToCell: 'A2',
        properties: {'placeholder': 'Enter grade'},
        position: const ComponentPosition(x: 50, y: 130, width: 150, height: 60),
      ),
      UIComponent(
        id: 'grade3',
        type: ComponentType.textField,
        label: 'Assignment 3',
        bindToCell: 'A3',
        properties: {'placeholder': 'Enter grade'},
        position: const ComponentPosition(x: 50, y: 210, width: 150, height: 60),
      ),
      UIComponent(
        id: 'grade4',
        type: ComponentType.textField,
        label: 'Assignment 4',
        bindToCell: 'A4',
        properties: {'placeholder': 'Enter grade'},
        position: const ComponentPosition(x: 50, y: 290, width: 150, height: 60),
      ),
      UIComponent(
        id: 'average',
        type: ComponentType.outputField,
        label: 'Average',
        bindToCell: 'A5',
        properties: {},
        position: const ComponentPosition(x: 250, y: 150, width: 150, height: 60),
      ),
      UIComponent(
        id: 'letter_grade',
        type: ComponentType.outputField,
        label: 'Letter Grade',
        bindToCell: 'A6',
        properties: {},
        position: const ComponentPosition(x: 250, y: 230, width: 150, height: 60),
      ),
    ];

    return ToolTemplate(
      id: 'grade_calculator',
      name: 'Grade Calculator',
      description: 'Calculate average grades and letter grades',
      category: 'Education',
      icon: Icons.school,
      color: Colors.purple,
      backendData: backendData,
      uiComponents: uiComponents,
      tags: ['education', 'grades', 'school', 'calculator'],
      difficulty: TemplateDifficulty.beginner,
    );
  }

  ToolTemplate _createBMICalculatorTemplate() {
    final backendData = <String, SpreadsheetCell>{
      'A1': SpreadsheetCell(address: 'A1', value: 70, type: CellType.number), // Weight in kg
      'A2': SpreadsheetCell(address: 'A2', value: 175, type: CellType.number), // Height in cm
      'A3': SpreadsheetCell(address: 'A3', value: '=A2/100', type: CellType.formula, formula: '=A2/100'), // Height in meters
      'A4': SpreadsheetCell(address: 'A4', value: '=A1/(A3*A3)', type: CellType.formula, formula: '=A1/(A3*A3)'), // BMI
      'A5': SpreadsheetCell(address: 'A5', value: '=IF(A4<18.5,"Underweight",IF(A4<25,"Normal",IF(A4<30,"Overweight","Obese")))', type: CellType.formula, formula: '=IF(A4<18.5,"Underweight",IF(A4<25,"Normal",IF(A4<30,"Overweight","Obese")))'),
    };

    final uiComponents = [
      UIComponent(
        id: 'weight_input',
        type: ComponentType.textField,
        label: 'Weight (kg)',
        bindToCell: 'A1',
        properties: {'placeholder': 'Enter weight in kg'},
        position: const ComponentPosition(x: 50, y: 50, width: 200, height: 60),
      ),
      UIComponent(
        id: 'height_input',
        type: ComponentType.textField,
        label: 'Height (cm)',
        bindToCell: 'A2',
        properties: {'placeholder': 'Enter height in cm'},
        position: const ComponentPosition(x: 50, y: 130, width: 200, height: 60),
      ),
      UIComponent(
        id: 'bmi_result',
        type: ComponentType.outputField,
        label: 'BMI',
        bindToCell: 'A4',
        properties: {},
        position: const ComponentPosition(x: 300, y: 50, width: 150, height: 60),
      ),
      UIComponent(
        id: 'category_result',
        type: ComponentType.outputField,
        label: 'Category',
        bindToCell: 'A5',
        properties: {},
        position: const ComponentPosition(x: 300, y: 130, width: 150, height: 60),
      ),
    ];

    return ToolTemplate(
      id: 'bmi_calculator',
      name: 'BMI Calculator',
      description: 'Calculate Body Mass Index and health category',
      category: 'Health',
      icon: Icons.fitness_center,
      color: Colors.red,
      backendData: backendData,
      uiComponents: uiComponents,
      tags: ['health', 'fitness', 'bmi', 'calculator'],
      difficulty: TemplateDifficulty.beginner,
    );
  }

  // Additional template methods would continue here...
  ToolTemplate _createCurrencyConverterTemplate() {
    // Implementation for currency converter
    return ToolTemplate(
      id: 'currency_converter',
      name: 'Currency Converter',
      description: 'Convert between different currencies',
      category: 'Finance',
      icon: Icons.currency_exchange,
      color: Colors.teal,
      backendData: {},
      uiComponents: [],
      tags: ['finance', 'currency', 'converter'],
      difficulty: TemplateDifficulty.intermediate,
    );
  }

  ToolTemplate _createTipCalculatorTemplate() {
    // Implementation for tip calculator
    return ToolTemplate(
      id: 'tip_calculator',
      name: 'Tip Calculator',
      description: 'Calculate tips and split bills',
      category: 'Finance',
      icon: Icons.restaurant,
      color: Colors.amber,
      backendData: {},
      uiComponents: [],
      tags: ['finance', 'tip', 'restaurant', 'calculator'],
      difficulty: TemplateDifficulty.beginner,
    );
  }

  ToolTemplate _createCompoundInterestTemplate() {
    // Implementation for compound interest calculator
    return ToolTemplate(
      id: 'compound_interest',
      name: 'Compound Interest Calculator',
      description: 'Calculate compound interest and investment growth',
      category: 'Finance',
      icon: Icons.trending_up,
      color: Colors.indigo,
      backendData: {},
      uiComponents: [],
      tags: ['finance', 'investment', 'interest', 'calculator'],
      difficulty: TemplateDifficulty.advanced,
    );
  }

  ToolTemplate _createProjectTimelineTemplate() {
    // Implementation for project timeline
    return ToolTemplate(
      id: 'project_timeline',
      name: 'Project Timeline',
      description: 'Track project milestones and deadlines',
      category: 'Productivity',
      icon: Icons.timeline,
      color: Colors.cyan,
      backendData: {},
      uiComponents: [],
      tags: ['productivity', 'project', 'timeline', 'planning'],
      difficulty: TemplateDifficulty.advanced,
    );
  }

  ToolTemplate _createInventoryTrackerTemplate() {
    // Implementation for inventory tracker
    return ToolTemplate(
      id: 'inventory_tracker',
      name: 'Inventory Tracker',
      description: 'Track inventory levels and stock management',
      category: 'Business',
      icon: Icons.inventory,
      color: Colors.brown,
      backendData: {},
      uiComponents: [],
      tags: ['business', 'inventory', 'stock', 'management'],
      difficulty: TemplateDifficulty.advanced,
    );
  }
}

/// Tool template model
class ToolTemplate {
  final String id;
  final String name;
  final String description;
  final String category;
  final IconData icon;
  final Color color;
  final Map<String, SpreadsheetCell> backendData;
  final List<UIComponent> uiComponents;
  final List<String> tags;
  final TemplateDifficulty difficulty;

  const ToolTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.icon,
    required this.color,
    required this.backendData,
    required this.uiComponents,
    required this.tags,
    required this.difficulty,
  });
}

/// Template difficulty levels
enum TemplateDifficulty {
  beginner,
  intermediate,
  advanced,
}

extension TemplateDifficultyExtension on TemplateDifficulty {
  String get displayName {
    switch (this) {
      case TemplateDifficulty.beginner:
        return 'Beginner';
      case TemplateDifficulty.intermediate:
        return 'Intermediate';
      case TemplateDifficulty.advanced:
        return 'Advanced';
    }
  }

  Color get color {
    switch (this) {
      case TemplateDifficulty.beginner:
        return Colors.green;
      case TemplateDifficulty.intermediate:
        return Colors.orange;
      case TemplateDifficulty.advanced:
        return Colors.red;
    }
  }
}
