import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/theme_customization.dart';
import '../models/app_layout_config.dart';

/// Service for managing app customization and theming
class CustomizationService {
  static final CustomizationService _instance = CustomizationService._internal();
  factory CustomizationService() => _instance;
  CustomizationService._internal();

  List<ThemeCustomization> _themes = [];
  List<AppLayoutConfig> _layouts = [];
  ThemeCustomization? _currentTheme;
  AppLayoutConfig? _currentLayout;
  bool _isDarkMode = false;
  bool _isInitialized = false;

  // Getters
  List<ThemeCustomization> get themes => List.unmodifiable(_themes);
  List<AppLayoutConfig> get layouts => List.unmodifiable(_layouts);
  ThemeCustomization? get currentTheme => _currentTheme;
  AppLayoutConfig? get currentLayout => _currentLayout;
  bool get isDarkMode => _isDarkMode;
  bool get isInitialized => _isInitialized;

  /// Initialize the customization service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadThemes();
      await _loadLayouts();
      await _loadCurrentSettings();
      _isInitialized = true;
    } catch (e) {
      print('Error initializing customization service: $e');
      await _initializeDefaults();
      _isInitialized = true;
    }
  }

  /// Load themes from storage
  Future<void> _loadThemes() async {
    try {
      // Load from local storage or create defaults
      _themes = await _getDefaultThemes();
    } catch (e) {
      print('Error loading themes: $e');
      _themes = await _getDefaultThemes();
    }
  }

  /// Load layouts from storage
  Future<void> _loadLayouts() async {
    try {
      // Load from local storage or create defaults
      _layouts = await _getDefaultLayouts();
    } catch (e) {
      print('Error loading layouts: $e');
      _layouts = await _getDefaultLayouts();
    }
  }

  /// Load current settings
  Future<void> _loadCurrentSettings() async {
    try {
      // Load current theme and layout IDs from preferences
      _currentTheme = _themes.isNotEmpty ? _themes.first : null;
      _currentLayout = _layouts.isNotEmpty ? _layouts.first : null;
      _isDarkMode = false; // Load from preferences
    } catch (e) {
      print('Error loading current settings: $e');
      _currentTheme = _themes.isNotEmpty ? _themes.first : null;
      _currentLayout = _layouts.isNotEmpty ? _layouts.first : null;
      _isDarkMode = false;
    }
  }

  /// Initialize with default themes and layouts
  Future<void> _initializeDefaults() async {
    _themes = await _getDefaultThemes();
    _layouts = await _getDefaultLayouts();
    _currentTheme = _themes.isNotEmpty ? _themes.first : null;
    _currentLayout = _layouts.isNotEmpty ? _layouts.first : null;
    _isDarkMode = false;
  }

  /// Get default themes
  Future<List<ThemeCustomization>> _getDefaultThemes() async {
    final now = DateTime.now();
    
    return [
      // Default Material 3 Theme
      ThemeCustomization(
        id: 'default',
        name: 'Default',
        description: 'Material 3 default theme',
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        textTheme: const TextTheme(),
        appBarTheme: const AppBarTheme(),
        cardTheme: const CardTheme(),
        elevatedButtonTheme: const ElevatedButtonThemeData(),
        outlinedButtonTheme: const OutlinedButtonThemeData(),
        textButtonTheme: const TextButtonThemeData(),
        inputDecorationTheme: const InputDecorationTheme(),
        fabTheme: const FloatingActionButtonThemeData(),
        navigationBarTheme: const NavigationBarThemeData(),
        navigationRailTheme: const NavigationRailThemeData(),
        drawerTheme: const DrawerThemeData(),
        bottomNavTheme: const BottomNavigationBarThemeData(),
        tabBarTheme: const TabBarTheme(),
        chipTheme: const ChipThemeData(),
        dialogTheme: const DialogTheme(),
        snackBarTheme: const SnackBarThemeData(),
        tooltipTheme: const TooltipThemeData(),
        popupMenuTheme: const PopupMenuThemeData(),
        listTileTheme: const ListTileThemeData(),
        expansionTileTheme: const ExpansionTileThemeData(),
        switchTheme: const SwitchThemeData(),
        checkboxTheme: const CheckboxThemeData(),
        radioTheme: const RadioThemeData(),
        sliderTheme: const SliderThemeData(),
        progressIndicatorTheme: const ProgressIndicatorThemeData(),
        dividerTheme: const DividerThemeData(),
        isCustom: false,
        createdAt: now,
      ),

      // Professional Theme
      ThemeCustomization(
        id: 'professional',
        name: 'Professional',
        description: 'Clean and professional theme for business use',
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1565C0),
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1565C0),
          brightness: Brightness.dark,
        ),
        textTheme: const TextTheme(),
        appBarTheme: const AppBarTheme(),
        cardTheme: const CardTheme(),
        elevatedButtonTheme: const ElevatedButtonThemeData(),
        outlinedButtonTheme: const OutlinedButtonThemeData(),
        textButtonTheme: const TextButtonThemeData(),
        inputDecorationTheme: const InputDecorationTheme(),
        fabTheme: const FloatingActionButtonThemeData(),
        navigationBarTheme: const NavigationBarThemeData(),
        navigationRailTheme: const NavigationRailThemeData(),
        drawerTheme: const DrawerThemeData(),
        bottomNavTheme: const BottomNavigationBarThemeData(),
        tabBarTheme: const TabBarTheme(),
        chipTheme: const ChipThemeData(),
        dialogTheme: const DialogTheme(),
        snackBarTheme: const SnackBarThemeData(),
        tooltipTheme: const TooltipThemeData(),
        popupMenuTheme: const PopupMenuThemeData(),
        listTileTheme: const ListTileThemeData(),
        expansionTileTheme: const ExpansionTileThemeData(),
        switchTheme: const SwitchThemeData(),
        checkboxTheme: const CheckboxThemeData(),
        radioTheme: const RadioThemeData(),
        sliderTheme: const SliderThemeData(),
        progressIndicatorTheme: const ProgressIndicatorThemeData(),
        dividerTheme: const DividerThemeData(),
        isCustom: false,
        createdAt: now,
      ),

      // Creative Theme
      ThemeCustomization(
        id: 'creative',
        name: 'Creative',
        description: 'Vibrant and creative theme for artistic work',
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFE91E63),
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFE91E63),
          brightness: Brightness.dark,
        ),
        textTheme: const TextTheme(),
        appBarTheme: const AppBarTheme(),
        cardTheme: const CardTheme(),
        elevatedButtonTheme: const ElevatedButtonThemeData(),
        outlinedButtonTheme: const OutlinedButtonThemeData(),
        textButtonTheme: const TextButtonThemeData(),
        inputDecorationTheme: const InputDecorationTheme(),
        fabTheme: const FloatingActionButtonThemeData(),
        navigationBarTheme: const NavigationBarThemeData(),
        navigationRailTheme: const NavigationRailThemeData(),
        drawerTheme: const DrawerThemeData(),
        bottomNavTheme: const BottomNavigationBarThemeData(),
        tabBarTheme: const TabBarTheme(),
        chipTheme: const ChipThemeData(),
        dialogTheme: const DialogTheme(),
        snackBarTheme: const SnackBarThemeData(),
        tooltipTheme: const TooltipThemeData(),
        popupMenuTheme: const PopupMenuThemeData(),
        listTileTheme: const ListTileThemeData(),
        expansionTileTheme: const ExpansionTileThemeData(),
        switchTheme: const SwitchThemeData(),
        checkboxTheme: const CheckboxThemeData(),
        radioTheme: const RadioThemeData(),
        sliderTheme: const SliderThemeData(),
        progressIndicatorTheme: const ProgressIndicatorThemeData(),
        dividerTheme: const DividerThemeData(),
        isCustom: false,
        createdAt: now,
      ),

      // Nature Theme
      ThemeCustomization(
        id: 'nature',
        name: 'Nature',
        description: 'Earth-inspired theme with natural colors',
        lightColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF4CAF50),
          brightness: Brightness.light,
        ),
        darkColorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF4CAF50),
          brightness: Brightness.dark,
        ),
        textTheme: const TextTheme(),
        appBarTheme: const AppBarTheme(),
        cardTheme: const CardTheme(),
        elevatedButtonTheme: const ElevatedButtonThemeData(),
        outlinedButtonTheme: const OutlinedButtonThemeData(),
        textButtonTheme: const TextButtonThemeData(),
        inputDecorationTheme: const InputDecorationTheme(),
        fabTheme: const FloatingActionButtonThemeData(),
        navigationBarTheme: const NavigationBarThemeData(),
        navigationRailTheme: const NavigationRailThemeData(),
        drawerTheme: const DrawerThemeData(),
        bottomNavTheme: const BottomNavigationBarThemeData(),
        tabBarTheme: const TabBarTheme(),
        chipTheme: const ChipThemeData(),
        dialogTheme: const DialogTheme(),
        snackBarTheme: const SnackBarThemeData(),
        tooltipTheme: const TooltipThemeData(),
        popupMenuTheme: const PopupMenuThemeData(),
        listTileTheme: const ListTileThemeData(),
        expansionTileTheme: const ExpansionTileThemeData(),
        switchTheme: const SwitchThemeData(),
        checkboxTheme: const CheckboxThemeData(),
        radioTheme: const RadioThemeData(),
        sliderTheme: const SliderThemeData(),
        progressIndicatorTheme: const ProgressIndicatorThemeData(),
        dividerTheme: const DividerThemeData(),
        isCustom: false,
        createdAt: now,
      ),
    ];
  }

  /// Get default layouts
  Future<List<AppLayoutConfig>> _getDefaultLayouts() async {
    final now = DateTime.now();
    
    return [
      // Default Layout
      AppLayoutConfig(
        id: 'default',
        name: 'Default',
        description: 'Standard adaptive layout',
        layoutType: LayoutType.adaptive,
        navigationType: NavigationType.rail,
        sidebarPosition: SidebarPosition.left,
        sidebarWidth: 280.0,
        showSidebarLabels: true,
        compactMode: false,
        showQuickActions: true,
        showBreadcrumbs: true,
        showStatusBar: true,
        headerStyle: HeaderStyle.elevated,
        footerStyle: FooterStyle.minimal,
        contentPadding: 16.0,
        cardSpacing: 12.0,
        cardBorderRadius: const BorderRadius.all(Radius.circular(12.0)),
        elevation: 2.0,
        enableAnimations: true,
        animationDuration: const Duration(milliseconds: 300),
        animationCurve: Curves.easeInOut,
        enableHapticFeedback: true,
        enableSounds: false,
        iconSize: 24.0,
        buttonHeight: 48.0,
        inputHeight: 56.0,
        screenPadding: const EdgeInsets.all(16.0),
        cardPadding: const EdgeInsets.all(16.0),
        isDefault: true,
        createdAt: now,
      ),

      // Compact Layout
      AppLayoutConfig(
        id: 'compact',
        name: 'Compact',
        description: 'Space-efficient compact layout',
        layoutType: LayoutType.adaptive,
        navigationType: NavigationType.rail,
        sidebarPosition: SidebarPosition.left,
        sidebarWidth: 240.0,
        showSidebarLabels: false,
        compactMode: true,
        showQuickActions: false,
        showBreadcrumbs: false,
        showStatusBar: true,
        headerStyle: HeaderStyle.minimal,
        footerStyle: FooterStyle.none,
        contentPadding: 12.0,
        cardSpacing: 8.0,
        cardBorderRadius: const BorderRadius.all(Radius.circular(8.0)),
        elevation: 1.0,
        enableAnimations: true,
        animationDuration: const Duration(milliseconds: 200),
        animationCurve: Curves.easeInOut,
        enableHapticFeedback: true,
        enableSounds: false,
        iconSize: 20.0,
        buttonHeight: 40.0,
        inputHeight: 48.0,
        screenPadding: const EdgeInsets.all(12.0),
        cardPadding: const EdgeInsets.all(12.0),
        isDefault: false,
        createdAt: now,
      ),

      // Spacious Layout
      AppLayoutConfig(
        id: 'spacious',
        name: 'Spacious',
        description: 'Generous spacing for comfortable viewing',
        layoutType: LayoutType.adaptive,
        navigationType: NavigationType.rail,
        sidebarPosition: SidebarPosition.left,
        sidebarWidth: 320.0,
        showSidebarLabels: true,
        compactMode: false,
        showQuickActions: true,
        showBreadcrumbs: true,
        showStatusBar: true,
        headerStyle: HeaderStyle.prominent,
        footerStyle: FooterStyle.detailed,
        contentPadding: 24.0,
        cardSpacing: 20.0,
        cardBorderRadius: const BorderRadius.all(Radius.circular(16.0)),
        elevation: 4.0,
        enableAnimations: true,
        animationDuration: const Duration(milliseconds: 400),
        animationCurve: Curves.easeInOut,
        enableHapticFeedback: true,
        enableSounds: false,
        iconSize: 28.0,
        buttonHeight: 56.0,
        inputHeight: 64.0,
        screenPadding: const EdgeInsets.all(24.0),
        cardPadding: const EdgeInsets.all(20.0),
        isDefault: false,
        createdAt: now,
      ),
    ];
  }

  /// Apply theme
  Future<void> applyTheme(String themeId) async {
    final theme = _themes.where((t) => t.id == themeId).firstOrNull;
    if (theme != null) {
      _currentTheme = theme;
      await _saveCurrentSettings();
    }
  }

  /// Apply layout
  Future<void> applyLayout(String layoutId) async {
    final layout = _layouts.where((l) => l.id == layoutId).firstOrNull;
    if (layout != null) {
      _currentLayout = layout;
      await _saveCurrentSettings();
    }
  }

  /// Toggle dark mode
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    await _saveCurrentSettings();
  }

  /// Set dark mode
  Future<void> setDarkMode(bool isDark) async {
    _isDarkMode = isDark;
    await _saveCurrentSettings();
  }

  /// Create custom theme
  Future<ThemeCustomization> createCustomTheme({
    required String name,
    required String description,
    required ColorScheme lightColorScheme,
    required ColorScheme darkColorScheme,
  }) async {
    final theme = ThemeCustomization(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      lightColorScheme: lightColorScheme,
      darkColorScheme: darkColorScheme,
      textTheme: const TextTheme(),
      appBarTheme: const AppBarTheme(),
      cardTheme: const CardTheme(),
      elevatedButtonTheme: const ElevatedButtonThemeData(),
      outlinedButtonTheme: const OutlinedButtonThemeData(),
      textButtonTheme: const TextButtonThemeData(),
      inputDecorationTheme: const InputDecorationTheme(),
      fabTheme: const FloatingActionButtonThemeData(),
      navigationBarTheme: const NavigationBarThemeData(),
      navigationRailTheme: const NavigationRailThemeData(),
      drawerTheme: const DrawerThemeData(),
      bottomNavTheme: const BottomNavigationBarThemeData(),
      tabBarTheme: const TabBarTheme(),
      chipTheme: const ChipThemeData(),
      dialogTheme: const DialogTheme(),
      snackBarTheme: const SnackBarThemeData(),
      tooltipTheme: const TooltipThemeData(),
      popupMenuTheme: const PopupMenuThemeData(),
      listTileTheme: const ListTileThemeData(),
      expansionTileTheme: const ExpansionTileThemeData(),
      switchTheme: const SwitchThemeData(),
      checkboxTheme: const CheckboxThemeData(),
      radioTheme: const RadioThemeData(),
      sliderTheme: const SliderThemeData(),
      progressIndicatorTheme: const ProgressIndicatorThemeData(),
      dividerTheme: const DividerThemeData(),
      isCustom: true,
      createdAt: DateTime.now(),
    );

    _themes.add(theme);
    await _saveThemes();
    return theme;
  }

  /// Create custom layout
  Future<AppLayoutConfig> createCustomLayout({
    required String name,
    required String description,
    required AppLayoutConfig baseLayout,
  }) async {
    final layout = baseLayout.copyWith(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      isDefault: false,
      createdAt: DateTime.now(),
    );

    _layouts.add(layout);
    await _saveLayouts();
    return layout;
  }

  /// Delete custom theme
  Future<void> deleteCustomTheme(String themeId) async {
    _themes.removeWhere((t) => t.id == themeId && t.isCustom);
    if (_currentTheme?.id == themeId) {
      _currentTheme = _themes.isNotEmpty ? _themes.first : null;
    }
    await _saveThemes();
    await _saveCurrentSettings();
  }

  /// Delete custom layout
  Future<void> deleteCustomLayout(String layoutId) async {
    _layouts.removeWhere((l) => l.id == layoutId && !l.isDefault);
    if (_currentLayout?.id == layoutId) {
      _currentLayout = _layouts.isNotEmpty ? _layouts.first : null;
    }
    await _saveLayouts();
    await _saveCurrentSettings();
  }

  /// Export theme settings
  Future<String> exportThemeSettings() async {
    final data = {
      'themes': _themes.map((t) => t.toJson()).toList(),
      'current_theme_id': _currentTheme?.id,
      'is_dark_mode': _isDarkMode,
      'exported_at': DateTime.now().toIso8601String(),
    };
    return json.encode(data);
  }

  /// Import theme settings
  Future<void> importThemeSettings(String jsonData) async {
    try {
      final data = json.decode(jsonData) as Map<String, dynamic>;
      final importedThemes = (data['themes'] as List<dynamic>)
          .map((t) => ThemeCustomization.fromJson(t as Map<String, dynamic>))
          .toList();

      // Add imported themes (avoid duplicates)
      for (final theme in importedThemes) {
        if (!_themes.any((t) => t.id == theme.id)) {
          _themes.add(theme);
        }
      }

      await _saveThemes();
    } catch (e) {
      throw Exception('Failed to import theme settings: $e');
    }
  }

  /// Export layout settings
  Future<String> exportLayoutSettings() async {
    final data = {
      'layouts': _layouts.map((l) => l.toJson()).toList(),
      'current_layout_id': _currentLayout?.id,
      'exported_at': DateTime.now().toIso8601String(),
    };
    return json.encode(data);
  }

  /// Import layout settings
  Future<void> importLayoutSettings(String jsonData) async {
    try {
      final data = json.decode(jsonData) as Map<String, dynamic>;
      final importedLayouts = (data['layouts'] as List<dynamic>)
          .map((l) => AppLayoutConfig.fromJson(l as Map<String, dynamic>))
          .toList();

      // Add imported layouts (avoid duplicates)
      for (final layout in importedLayouts) {
        if (!_layouts.any((l) => l.id == layout.id)) {
          _layouts.add(layout);
        }
      }

      await _saveLayouts();
    } catch (e) {
      throw Exception('Failed to import layout settings: $e');
    }
  }

  /// Get current theme data
  ThemeData getCurrentThemeData() {
    if (_currentTheme == null) {
      return ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: _isDarkMode ? Brightness.dark : Brightness.light,
        ),
      );
    }
    return _currentTheme!.toThemeData(isDark: _isDarkMode);
  }

  /// Save methods (implement with actual storage)
  Future<void> _saveThemes() async {
    // Implement saving themes to local storage
  }

  Future<void> _saveLayouts() async {
    // Implement saving layouts to local storage
  }

  Future<void> _saveCurrentSettings() async {
    // Implement saving current settings to preferences
  }

  /// Reset to defaults
  Future<void> resetToDefaults() async {
    _themes = await _getDefaultThemes();
    _layouts = await _getDefaultLayouts();
    _currentTheme = _themes.isNotEmpty ? _themes.first : null;
    _currentLayout = _layouts.isNotEmpty ? _layouts.first : null;
    _isDarkMode = false;
    
    await _saveThemes();
    await _saveLayouts();
    await _saveCurrentSettings();
  }
}
