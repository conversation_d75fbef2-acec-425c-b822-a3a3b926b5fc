import 'package:flutter/material.dart';
import 'dart:convert';

/// Comprehensive appearance customization settings
class AppearanceSettings {
  // Icon Controls
  final double iconSize;
  final IconStyle iconStyle;
  final double iconSpacing;
  
  // Tab Controls
  final double tabSize;
  final double tabPadding;
  final double tabBorderRadius;
  final Color tabActiveColor;
  final Color tabInactiveColor;
  
  // Border Controls
  final double borderThickness;
  final Color borderColor;
  final BorderStyle borderStyle;
  
  // Header Controls
  final double headerHeight;
  final double headerThickness;
  final Color headerBackground;
  final double headerTextSize;
  
  // Spacing Controls
  final double marginHorizontal;
  final double marginVertical;
  final double paddingHorizontal;
  final double paddingVertical;
  
  // Color Controls
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color surfaceColor;
  final Color errorColor;
  
  // Typography Controls
  final double bodyTextSize;
  final double headingTextSize;
  final FontWeight textWeight;
  final String fontFamily;
  
  // Animation Controls
  final double animationSpeed;
  final bool animationsEnabled;
  final Curve animationCurve;

  const AppearanceSettings({
    // Icon defaults
    this.iconSize = 24.0,
    this.iconStyle = IconStyle.outlined,
    this.iconSpacing = 8.0,
    
    // Tab defaults
    this.tabSize = 48.0,
    this.tabPadding = 16.0,
    this.tabBorderRadius = 12.0,
    this.tabActiveColor = Colors.blue,
    this.tabInactiveColor = Colors.grey,
    
    // Border defaults
    this.borderThickness = 1.0,
    this.borderColor = Colors.grey,
    this.borderStyle = BorderStyle.solid,
    
    // Header defaults
    this.headerHeight = 56.0,
    this.headerThickness = 2.0,
    this.headerBackground = Colors.blue,
    this.headerTextSize = 20.0,
    
    // Spacing defaults
    this.marginHorizontal = 16.0,
    this.marginVertical = 8.0,
    this.paddingHorizontal = 16.0,
    this.paddingVertical = 8.0,
    
    // Color defaults
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.blueAccent,
    this.backgroundColor = Colors.white,
    this.surfaceColor = Colors.grey,
    this.errorColor = Colors.red,
    
    // Typography defaults
    this.bodyTextSize = 14.0,
    this.headingTextSize = 24.0,
    this.textWeight = FontWeight.normal,
    this.fontFamily = 'Roboto',
    
    // Animation defaults
    this.animationSpeed = 300.0,
    this.animationsEnabled = true,
    this.animationCurve = Curves.easeInOut,
  });

  AppearanceSettings copyWith({
    double? iconSize,
    IconStyle? iconStyle,
    double? iconSpacing,
    double? tabSize,
    double? tabPadding,
    double? tabBorderRadius,
    Color? tabActiveColor,
    Color? tabInactiveColor,
    double? borderThickness,
    Color? borderColor,
    BorderStyle? borderStyle,
    double? headerHeight,
    double? headerThickness,
    Color? headerBackground,
    double? headerTextSize,
    double? marginHorizontal,
    double? marginVertical,
    double? paddingHorizontal,
    double? paddingVertical,
    Color? primaryColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? surfaceColor,
    Color? errorColor,
    double? bodyTextSize,
    double? headingTextSize,
    FontWeight? textWeight,
    String? fontFamily,
    double? animationSpeed,
    bool? animationsEnabled,
    Curve? animationCurve,
  }) {
    return AppearanceSettings(
      iconSize: iconSize ?? this.iconSize,
      iconStyle: iconStyle ?? this.iconStyle,
      iconSpacing: iconSpacing ?? this.iconSpacing,
      tabSize: tabSize ?? this.tabSize,
      tabPadding: tabPadding ?? this.tabPadding,
      tabBorderRadius: tabBorderRadius ?? this.tabBorderRadius,
      tabActiveColor: tabActiveColor ?? this.tabActiveColor,
      tabInactiveColor: tabInactiveColor ?? this.tabInactiveColor,
      borderThickness: borderThickness ?? this.borderThickness,
      borderColor: borderColor ?? this.borderColor,
      borderStyle: borderStyle ?? this.borderStyle,
      headerHeight: headerHeight ?? this.headerHeight,
      headerThickness: headerThickness ?? this.headerThickness,
      headerBackground: headerBackground ?? this.headerBackground,
      headerTextSize: headerTextSize ?? this.headerTextSize,
      marginHorizontal: marginHorizontal ?? this.marginHorizontal,
      marginVertical: marginVertical ?? this.marginVertical,
      paddingHorizontal: paddingHorizontal ?? this.paddingHorizontal,
      paddingVertical: paddingVertical ?? this.paddingVertical,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      errorColor: errorColor ?? this.errorColor,
      bodyTextSize: bodyTextSize ?? this.bodyTextSize,
      headingTextSize: headingTextSize ?? this.headingTextSize,
      textWeight: textWeight ?? this.textWeight,
      fontFamily: fontFamily ?? this.fontFamily,
      animationSpeed: animationSpeed ?? this.animationSpeed,
      animationsEnabled: animationsEnabled ?? this.animationsEnabled,
      animationCurve: animationCurve ?? this.animationCurve,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'iconSize': iconSize,
      'iconStyle': iconStyle.name,
      'iconSpacing': iconSpacing,
      'tabSize': tabSize,
      'tabPadding': tabPadding,
      'tabBorderRadius': tabBorderRadius,
      'tabActiveColor': tabActiveColor.value,
      'tabInactiveColor': tabInactiveColor.value,
      'borderThickness': borderThickness,
      'borderColor': borderColor.value,
      'borderStyle': borderStyle.name,
      'headerHeight': headerHeight,
      'headerThickness': headerThickness,
      'headerBackground': headerBackground.value,
      'headerTextSize': headerTextSize,
      'marginHorizontal': marginHorizontal,
      'marginVertical': marginVertical,
      'paddingHorizontal': paddingHorizontal,
      'paddingVertical': paddingVertical,
      'primaryColor': primaryColor.value,
      'secondaryColor': secondaryColor.value,
      'backgroundColor': backgroundColor.value,
      'surfaceColor': surfaceColor.value,
      'errorColor': errorColor.value,
      'bodyTextSize': bodyTextSize,
      'headingTextSize': headingTextSize,
      'textWeight': textWeight.index,
      'fontFamily': fontFamily,
      'animationSpeed': animationSpeed,
      'animationsEnabled': animationsEnabled,
      'animationCurve': animationCurve.toString(),
    };
  }

  factory AppearanceSettings.fromJson(Map<String, dynamic> json) {
    return AppearanceSettings(
      iconSize: json['iconSize']?.toDouble() ?? 24.0,
      iconStyle: IconStyle.values.firstWhere(
        (e) => e.name == json['iconStyle'],
        orElse: () => IconStyle.outlined,
      ),
      iconSpacing: json['iconSpacing']?.toDouble() ?? 8.0,
      tabSize: json['tabSize']?.toDouble() ?? 48.0,
      tabPadding: json['tabPadding']?.toDouble() ?? 16.0,
      tabBorderRadius: json['tabBorderRadius']?.toDouble() ?? 12.0,
      tabActiveColor: Color(json['tabActiveColor'] ?? Colors.blue.value),
      tabInactiveColor: Color(json['tabInactiveColor'] ?? Colors.grey.value),
      borderThickness: json['borderThickness']?.toDouble() ?? 1.0,
      borderColor: Color(json['borderColor'] ?? Colors.grey.value),
      borderStyle: BorderStyle.values.firstWhere(
        (e) => e.name == json['borderStyle'],
        orElse: () => BorderStyle.solid,
      ),
      headerHeight: json['headerHeight']?.toDouble() ?? 56.0,
      headerThickness: json['headerThickness']?.toDouble() ?? 2.0,
      headerBackground: Color(json['headerBackground'] ?? Colors.blue.value),
      headerTextSize: json['headerTextSize']?.toDouble() ?? 20.0,
      marginHorizontal: json['marginHorizontal']?.toDouble() ?? 16.0,
      marginVertical: json['marginVertical']?.toDouble() ?? 8.0,
      paddingHorizontal: json['paddingHorizontal']?.toDouble() ?? 16.0,
      paddingVertical: json['paddingVertical']?.toDouble() ?? 8.0,
      primaryColor: Color(json['primaryColor'] ?? Colors.blue.value),
      secondaryColor: Color(json['secondaryColor'] ?? Colors.blueAccent.value),
      backgroundColor: Color(json['backgroundColor'] ?? Colors.white.value),
      surfaceColor: Color(json['surfaceColor'] ?? Colors.grey.value),
      errorColor: Color(json['errorColor'] ?? Colors.red.value),
      bodyTextSize: json['bodyTextSize']?.toDouble() ?? 14.0,
      headingTextSize: json['headingTextSize']?.toDouble() ?? 24.0,
      textWeight: FontWeight.values[json['textWeight'] ?? FontWeight.normal.index],
      fontFamily: json['fontFamily'] ?? 'Roboto',
      animationSpeed: json['animationSpeed']?.toDouble() ?? 300.0,
      animationsEnabled: json['animationsEnabled'] ?? true,
      animationCurve: Curves.easeInOut, // Default curve
    );
  }

  String toJsonString() => jsonEncode(toJson());

  factory AppearanceSettings.fromJsonString(String jsonString) {
    return AppearanceSettings.fromJson(jsonDecode(jsonString));
  }
}

enum IconStyle {
  filled,
  outlined,
  rounded,
  sharp,
  twoTone,
}

enum BorderStyle {
  solid,
  dashed,
  dotted,
  none,
}
