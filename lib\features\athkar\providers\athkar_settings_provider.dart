import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/athkar_settings.dart';

/// Athkar settings notifier
class AthkarSettingsNotifier extends StateNotifier<AthkarSettings> {
  AthkarSettingsNotifier() : super(AthkarSettings()) {
    _loadSettings();
  }

  static const String _settingsKey = 'athkar_settings';

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final Map<String, dynamic> data = json.decode(settingsJson);
        state = AthkarSettings.fromJson(data);
      }
    } catch (e) {
      // If loading fails, keep default settings
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(state.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      // If saving fails, continue silently
    }
  }

  /// Update settings
  Future<void> updateSettings(AthkarSettings newSettings) async {
    state = newSettings;
    await _saveSettings();
  }

  /// Update display mode
  Future<void> updateDisplayMode(AthkarDisplayMode mode) async {
    state = state.copyWith(displayMode: mode);
    await _saveSettings();
  }

  /// Update text sizes
  Future<void> updateTextSizes({
    TextSize? arabicSize,
    TextSize? translationSize,
  }) async {
    state = state.copyWith(
      arabicTextSize: arabicSize,
      translationTextSize: translationSize,
    );
    await _saveSettings();
  }

  /// Update font families
  Future<void> updateFontFamilies({
    String? arabicFont,
    String? translationFont,
  }) async {
    state = state.copyWith(
      arabicFontFamily: arabicFont,
      translationFontFamily: translationFont,
    );
    await _saveSettings();
  }

  /// Update language settings
  Future<void> updateLanguageSettings({
    bool? showTranslation,
    bool? showTransliteration,
    AthkarLanguage? primaryLanguage,
  }) async {
    state = state.copyWith(
      showTranslation: showTranslation,
      showTransliteration: showTransliteration,
      primaryLanguage: primaryLanguage,
    );
    await _saveSettings();
  }

  /// Update counter settings
  Future<void> updateCounterSettings({
    CounterStyle? style,
    bool? enableVibration,
    bool? enableSound,
    String? soundFile,
    bool? showProgress,
    bool? autoIncrement,
    bool? resetDaily,
    bool? saveProgress,
  }) async {
    state = state.copyWith(
      counterStyle: style,
      enableVibration: enableVibration,
      enableSound: enableSound,
      soundFile: soundFile,
      showProgress: showProgress,
      enableAutoIncrement: autoIncrement,
      resetCounterDaily: resetDaily,
      saveCounterProgress: saveProgress,
    );
    await _saveSettings();
  }

  /// Update reading settings
  Future<void> updateReadingSettings({
    bool? autoScroll,
    double? scrollSpeed,
    bool? highlightVerse,
    bool? nightMode,
    double? lineSpacing,
    bool? fullscreen,
    bool? keepScreenOn,
    bool? enableGestures,
  }) async {
    state = state.copyWith(
      enableAutoScroll: autoScroll,
      autoScrollSpeed: scrollSpeed,
      highlightCurrentVerse: highlightVerse,
      enableNightMode: nightMode,
      lineSpacing: lineSpacing,
      enableFullscreen: fullscreen,
      keepScreenOn: keepScreenOn,
      enableGestures: enableGestures,
    );
    await _saveSettings();
  }

  /// Update content settings
  Future<void> updateContentSettings({
    bool? showArabic,
    bool? showTranslation,
    bool? showTransliteration,
    bool? showBenefits,
    bool? showReferences,
    bool? enableCustom,
  }) async {
    state = state.copyWith(
      showArabicText: showArabic,
      showEnglishTranslation: showTranslation,
      showTransliteration: showTransliteration,
      showBenefits: showBenefits,
      showReferences: showReferences,
      enableCustomAthkar: enableCustom,
    );
    await _saveSettings();
  }

  /// Update reminder settings
  Future<void> updateReminderSettings({
    bool? enableReminders,
    ReminderFrequency? frequency,
    bool? enableQuietHours,
    String? reminderSound,
    bool? enableVibration,
  }) async {
    state = state.copyWith(
      enableReminders: enableReminders,
      reminderFrequency: frequency,
      enableQuietHours: enableQuietHours,
      reminderSound: reminderSound,
      enableReminderVibration: enableVibration,
    );
    await _saveSettings();
  }

  /// Update progress tracking
  Future<void> updateProgressSettings({
    bool? trackProgress,
    bool? showDaily,
    bool? showWeekly,
    bool? showMonthly,
    bool? enableStreaks,
    bool? enableAchievements,
    bool? enableSharing,
  }) async {
    state = state.copyWith(
      trackProgress: trackProgress,
      showDailyStats: showDaily,
      showWeeklyStats: showWeekly,
      showMonthlyStats: showMonthly,
      enableStreaks: enableStreaks,
      enableAchievements: enableAchievements,
      enableProgressSharing: enableSharing,
    );
    await _saveSettings();
  }

  /// Update backup settings
  Future<void> updateBackupSettings({
    bool? enableAutoBackup,
    bool? backupToCloud,
    bool? syncDevices,
    int? frequencyDays,
    bool? syncFavorites,
    bool? syncProgress,
    bool? syncSettings,
  }) async {
    state = state.copyWith(
      enableAutoBackup: enableAutoBackup,
      backupToCloud: backupToCloud,
      syncAcrossDevices: syncDevices,
      backupFrequencyDays: frequencyDays,
      syncFavorites: syncFavorites,
      syncProgress: syncProgress,
      syncSettings: syncSettings,
    );
    await _saveSettings();
  }

  /// Update privacy settings
  Future<void> updatePrivacySettings({
    bool? privacyMode,
    bool? hideFromRecents,
    bool? requirePin,
    String? pinHash,
    bool? biometric,
    int? autoLockMinutes,
    bool? screenshotProtection,
  }) async {
    state = state.copyWith(
      enablePrivacyMode: privacyMode,
      hideFromRecents: hideFromRecents,
      requirePinForAccess: requirePin,
      pinHash: pinHash,
      enableBiometricAuth: biometric,
      autoLockMinutes: autoLockMinutes,
      enableScreenshotProtection: screenshotProtection,
    );
    await _saveSettings();
  }

  /// Update advanced settings
  Future<void> updateAdvancedSettings({
    bool? debugMode,
    bool? betaFeatures,
    bool? analytics,
    bool? crashReporting,
    bool? performanceMonitoring,
    bool? offlineMode,
    bool? preloadContent,
    int? cacheSize,
  }) async {
    state = state.copyWith(
      enableDebugMode: debugMode,
      enableBetaFeatures: betaFeatures,
      enableAnalytics: analytics,
      enableCrashReporting: crashReporting,
      enablePerformanceMonitoring: performanceMonitoring,
      enableOfflineMode: offlineMode,
      preloadContent: preloadContent,
      cacheSize: cacheSize,
    );
    await _saveSettings();
  }

  /// Add favorite athkar
  Future<void> addFavorite(String athkarId) async {
    final favorites = List<String>.from(state.favoriteAthkar);
    if (!favorites.contains(athkarId)) {
      favorites.add(athkarId);
      state = state.copyWith(favoriteAthkar: favorites);
      await _saveSettings();
    }
  }

  /// Remove favorite athkar
  Future<void> removeFavorite(String athkarId) async {
    final favorites = List<String>.from(state.favoriteAthkar);
    favorites.remove(athkarId);
    state = state.copyWith(favoriteAthkar: favorites);
    await _saveSettings();
  }

  /// Add custom athkar
  Future<void> addCustomAthkar(String athkarText) async {
    final custom = List<String>.from(state.customAthkar);
    custom.add(athkarText);
    state = state.copyWith(customAthkar: custom);
    await _saveSettings();
  }

  /// Remove custom athkar
  Future<void> removeCustomAthkar(String athkarText) async {
    final custom = List<String>.from(state.customAthkar);
    custom.remove(athkarText);
    state = state.copyWith(customAthkar: custom);
    await _saveSettings();
  }

  /// Reset to default settings
  Future<void> resetToDefaults() async {
    state = AthkarSettings();
    await _saveSettings();
  }

  /// Export settings as JSON string
  String exportSettings() {
    return json.encode(state.toJson());
  }

  /// Import settings from JSON string
  Future<bool> importSettings(String settingsJson) async {
    try {
      final Map<String, dynamic> data = json.decode(settingsJson);
      final importedSettings = AthkarSettings.fromJson(data);
      state = importedSettings;
      await _saveSettings();
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Provider for athkar settings
final athkarSettingsProvider = StateNotifierProvider<AthkarSettingsNotifier, AthkarSettings>((ref) {
  return AthkarSettingsNotifier();
});

/// Provider for display settings
final athkarDisplayProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(athkarSettingsProvider);
  return {
    'mode': settings.displayMode,
    'arabicTextSize': settings.arabicTextSize,
    'translationTextSize': settings.translationTextSize,
    'arabicFont': settings.arabicFontFamily,
    'translationFont': settings.translationFontFamily,
    'showTranslation': settings.showTranslation,
    'showTransliteration': settings.showTransliteration,
    'primaryLanguage': settings.primaryLanguage,
    'darkMode': settings.enableDarkMode,
  };
});

/// Provider for counter settings
final athkarCounterProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(athkarSettingsProvider);
  return {
    'style': settings.counterStyle,
    'vibration': settings.enableVibration,
    'sound': settings.enableSound,
    'soundFile': settings.soundFile,
    'showProgress': settings.showProgress,
    'autoIncrement': settings.enableAutoIncrement,
    'resetDaily': settings.resetCounterDaily,
    'saveProgress': settings.saveCounterProgress,
  };
});

/// Provider for reading settings
final athkarReadingProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(athkarSettingsProvider);
  return {
    'autoScroll': settings.enableAutoScroll,
    'scrollSpeed': settings.autoScrollSpeed,
    'highlightVerse': settings.highlightCurrentVerse,
    'nightMode': settings.enableNightMode,
    'lineSpacing': settings.lineSpacing,
    'fullscreen': settings.enableFullscreen,
    'keepScreenOn': settings.keepScreenOn,
    'gestures': settings.enableGestures,
  };
});

/// Provider for reminder settings
final athkarReminderProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(athkarSettingsProvider);
  return {
    'enabled': settings.enableReminders,
    'frequency': settings.reminderFrequency,
    'morningTime': settings.morningReminderTime,
    'eveningTime': settings.eveningReminderTime,
    'customTime': settings.customReminderTime,
    'quietHours': settings.enableQuietHours,
    'quietStart': settings.quietHoursStart,
    'quietEnd': settings.quietHoursEnd,
    'sound': settings.reminderSound,
    'vibration': settings.enableReminderVibration,
  };
});

/// Provider for progress settings
final athkarProgressProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(athkarSettingsProvider);
  return {
    'track': settings.trackProgress,
    'daily': settings.showDailyStats,
    'weekly': settings.showWeeklyStats,
    'monthly': settings.showMonthlyStats,
    'streaks': settings.enableStreaks,
    'achievements': settings.enableAchievements,
    'sharing': settings.enableProgressSharing,
    'goals': settings.dailyGoals,
  };
});

/// Provider for favorites
final athkarFavoritesProvider = Provider<List<String>>((ref) {
  final settings = ref.watch(athkarSettingsProvider);
  return settings.favoriteAthkar;
});

/// Provider for custom athkar
final athkarCustomProvider = Provider<List<String>>((ref) {
  final settings = ref.watch(athkarSettingsProvider);
  return settings.customAthkar;
});
