import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/memo_settings.dart';

/// Memo settings notifier
class MemoSettingsNotifier extends StateNotifier<MemoSettings> {
  MemoSettingsNotifier() : super(const MemoSettings()) {
    _loadSettings();
  }

  static const String _settingsKey = 'memo_settings';

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final Map<String, dynamic> data = json.decode(settingsJson);
        state = MemoSettings.fromJson(data);
      }
    } catch (e) {
      // If loading fails, keep default settings
      print('Error loading memo settings: $e');
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(state.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      print('Error saving memo settings: $e');
    }
  }

  /// Update settings
  Future<void> updateSettings(MemoSettings newSettings) async {
    state = newSettings;
    await _saveSettings();
  }

  /// Update note view mode
  Future<void> updateNoteViewMode(NoteViewMode mode) async {
    state = state.copyWith(noteViewMode: mode);
    await _saveSettings();
  }

  /// Update font size
  Future<void> updateFontSize(double size) async {
    state = state.copyWith(fontSize: size);
    await _saveSettings();
  }

  /// Update font family
  Future<void> updateFontFamily(String family) async {
    state = state.copyWith(fontFamily: family);
    await _saveSettings();
  }

  /// Update auto save settings
  Future<void> updateAutoSave(bool enabled, [int? intervalSeconds]) async {
    state = state.copyWith(
      autoSaveNotes: enabled,
      autoSaveIntervalSeconds: intervalSeconds,
    );
    await _saveSettings();
  }

  /// Update note sorting
  Future<void> updateNoteSorting(NoteSortBy sortBy, bool ascending) async {
    state = state.copyWith(
      noteSortBy: sortBy,
      noteSortAscending: ascending,
    );
    await _saveSettings();
  }

  /// Update todo sorting
  Future<void> updateTodoSorting(TodoSortBy sortBy, bool ascending) async {
    state = state.copyWith(
      todoSortBy: sortBy,
      todoSortAscending: ascending,
    );
    await _saveSettings();
  }

  /// Update voice memo quality
  Future<void> updateVoiceMemoQuality(VoiceMemoQuality quality) async {
    state = state.copyWith(voiceMemoQuality: quality);
    await _saveSettings();
  }

  /// Update backup settings
  Future<void> updateBackupSettings({
    bool? enableAutoBackup,
    int? frequencyHours,
    bool? backupToCloud,
    bool? syncOnWifiOnly,
  }) async {
    state = state.copyWith(
      enableAutoBackup: enableAutoBackup,
      backupFrequencyHours: frequencyHours,
      backupToCloud: backupToCloud,
      syncOnWifiOnly: syncOnWifiOnly,
    );
    await _saveSettings();
  }

  /// Update security settings
  Future<void> updateSecuritySettings({
    bool? enableEncryption,
    bool? requirePin,
    String? pinHash,
    bool? enableBiometric,
    int? autoLockMinutes,
  }) async {
    state = state.copyWith(
      enableEncryption: enableEncryption,
      requirePinForAccess: requirePin,
      pinHash: pinHash,
      enableBiometricAuth: enableBiometric,
      autoLockMinutes: autoLockMinutes,
    );
    await _saveSettings();
  }

  /// Update notification settings
  Future<void> updateNotificationSettings({
    bool? enableNotifications,
    bool? enableDueDateReminders,
    bool? enableDailyDigest,
    bool? enableQuietHours,
  }) async {
    state = state.copyWith(
      enableNotifications: enableNotifications,
      enableDueDateReminders: enableDueDateReminders,
      enableDailyDigest: enableDailyDigest,
      enableQuietHours: enableQuietHours,
    );
    await _saveSettings();
  }

  /// Update display settings
  Future<void> updateDisplaySettings({
    bool? showNotePreview,
    bool? showWordCount,
    bool? showCharacterCount,
    bool? enableMarkdownPreview,
    bool? enableSyntaxHighlighting,
    bool? showLineNumbers,
    bool? enableWordWrap,
  }) async {
    state = state.copyWith(
      showNotePreview: showNotePreview,
      showWordCount: showWordCount,
      showCharacterCount: showCharacterCount,
      enableMarkdownPreview: enableMarkdownPreview,
      enableSyntaxHighlighting: enableSyntaxHighlighting,
      showLineNumbers: showLineNumbers,
      enableWordWrap: enableWordWrap,
    );
    await _saveSettings();
  }

  /// Update organization settings
  Future<void> updateOrganizationSettings({
    bool? enableCategories,
    bool? enableTags,
    bool? enableFavorites,
    bool? enableArchive,
    bool? enableTrash,
    int? trashRetentionDays,
  }) async {
    state = state.copyWith(
      enableCategories: enableCategories,
      enableTags: enableTags,
      enableFavorites: enableFavorites,
      enableArchive: enableArchive,
      enableTrash: enableTrash,
      trashRetentionDays: trashRetentionDays,
    );
    await _saveSettings();
  }

  /// Update advanced settings
  Future<void> updateAdvancedSettings({
    bool? enableDebugMode,
    bool? enableBetaFeatures,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    bool? enablePerformanceMonitoring,
    int? maxUndoSteps,
    bool? enableCollaboration,
  }) async {
    state = state.copyWith(
      enableDebugMode: enableDebugMode,
      enableBetaFeatures: enableBetaFeatures,
      enableAnalytics: enableAnalytics,
      enableCrashReporting: enableCrashReporting,
      enablePerformanceMonitoring: enablePerformanceMonitoring,
      maxUndoSteps: maxUndoSteps,
      enableCollaboration: enableCollaboration,
    );
    await _saveSettings();
  }

  /// Reset to default settings
  Future<void> resetToDefaults() async {
    state = const MemoSettings();
    await _saveSettings();
  }

  /// Export settings as JSON string
  String exportSettings() {
    return json.encode(state.toJson());
  }

  /// Import settings from JSON string
  Future<bool> importSettings(String settingsJson) async {
    try {
      final Map<String, dynamic> data = json.decode(settingsJson);
      final importedSettings = MemoSettings.fromJson(data);
      state = importedSettings;
      await _saveSettings();
      return true;
    } catch (e) {
      print('Error importing memo settings: $e');
      return false;
    }
  }
}

/// Provider for memo settings
final memoSettingsProvider = StateNotifierProvider<MemoSettingsNotifier, MemoSettings>((ref) {
  return MemoSettingsNotifier();
});

/// Provider for note view mode
final noteViewModeProvider = Provider<NoteViewMode>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return settings.noteViewMode;
});

/// Provider for font settings
final fontSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'fontSize': settings.fontSize,
    'fontFamily': settings.fontFamily,
    'useSystemFont': settings.useSystemFont,
    'lineHeight': settings.lineHeight,
  };
});

/// Provider for auto save settings
final autoSaveSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'enabled': settings.autoSaveNotes,
    'intervalSeconds': settings.autoSaveIntervalSeconds,
  };
});

/// Provider for note sorting settings
final noteSortingProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'sortBy': settings.noteSortBy,
    'ascending': settings.noteSortAscending,
  };
});

/// Provider for todo sorting settings
final todoSortingProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'sortBy': settings.todoSortBy,
    'ascending': settings.todoSortAscending,
    'showCompleted': settings.showCompletedTodos,
  };
});

/// Provider for voice memo settings
final voiceMemoSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'quality': settings.voiceMemoQuality,
    'format': settings.audioFormat,
    'autoTranscription': settings.enableAutoTranscription,
    'maxMinutes': settings.maxRecordingMinutes,
    'noiseReduction': settings.enableNoiseReduction,
    'autoStop': settings.enableAutoStop,
    'saveToCloud': settings.saveToCloud,
  };
});

/// Provider for security settings
final memoSecurityProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'encryption': settings.enableEncryption,
    'requirePin': settings.requirePinForAccess,
    'biometric': settings.enableBiometricAuth,
    'autoLock': settings.autoLockMinutes,
    'hideInRecents': settings.hideContentInRecents,
    'screenshotProtection': settings.enableScreenshotProtection,
  };
});

/// Provider for notification settings
final memoNotificationProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'enabled': settings.enableNotifications,
    'dueDateReminders': settings.enableDueDateReminders,
    'dailyDigest': settings.enableDailyDigest,
    'quietHours': settings.enableQuietHours,
    'reminderMinutes': settings.reminderMinutesBefore,
  };
});

/// Provider for backup settings
final memoBackupProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'autoBackup': settings.enableAutoBackup,
    'frequency': settings.backupFrequencyHours,
    'cloud': settings.backupToCloud,
    'sync': settings.syncAcrossDevices,
    'wifiOnly': settings.syncOnWifiOnly,
    'maxFiles': settings.maxBackupFiles,
  };
});

/// Provider for display settings
final memoDisplayProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'preview': settings.showNotePreview,
    'wordCount': settings.showWordCount,
    'characterCount': settings.showCharacterCount,
    'markdown': settings.enableMarkdownPreview,
    'syntaxHighlighting': settings.enableSyntaxHighlighting,
    'lineNumbers': settings.showLineNumbers,
    'wordWrap': settings.enableWordWrap,
  };
});

/// Provider for organization features
final memoOrganizationProvider = Provider<Map<String, dynamic>>((ref) {
  final settings = ref.watch(memoSettingsProvider);
  return {
    'categories': settings.enableCategories,
    'tags': settings.enableTags,
    'favorites': settings.enableFavorites,
    'archive': settings.enableArchive,
    'trash': settings.enableTrash,
    'search': settings.enableSearch,
    'globalSearch': settings.enableGlobalSearch,
  };
});
