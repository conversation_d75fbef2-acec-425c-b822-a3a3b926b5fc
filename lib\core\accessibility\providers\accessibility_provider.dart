import 'package:flutter/material.dart' hide NavigationMode;
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/accessibility_settings.dart';
import '../services/accessibility_service.dart';

// Accessibility service provider
final accessibilityServiceProvider = Provider<AccessibilityService>((ref) {
  return AccessibilityService();
});

// Accessibility settings stream provider
final accessibilitySettingsProvider = StreamProvider<AccessibilitySettings>((ref) {
  final service = ref.watch(accessibilityServiceProvider);
  return service.settingsStream;
});

// Accessibility announcements stream provider
final accessibilityAnnouncementsProvider = StreamProvider<String>((ref) {
  final service = ref.watch(accessibilityServiceProvider);
  return service.announcementStream;
});

// Current accessibility settings provider
final currentAccessibilitySettingsProvider = Provider<AccessibilitySettings>((ref) {
  final settingsAsync = ref.watch(accessibilitySettingsProvider);
  return settingsAsync.when(
    data: (settings) => settings,
    loading: () => AccessibilitySettings(),
    error: (_, __) => AccessibilitySettings(),
  );
});

// Accessibility actions provider
final accessibilityActionsProvider = Provider<AccessibilityActions>((ref) {
  final service = ref.watch(accessibilityServiceProvider);
  return AccessibilityActions(service);
});

// Specific accessibility feature providers
final highContrastProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return settings.highContrastMode;
});

final largeTextProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return settings.largeTextMode || settings.extraLargeTextMode;
});

final reduceMotionProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return settings.reduceMotion || settings.pauseAnimations;
});

final screenReaderProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return settings.screenReaderEnabled;
});

final keyboardNavigationProvider = Provider<bool>((ref) {
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return settings.usesKeyboardNavigation;
});

final buttonSizeProvider = Provider<double>((ref) {
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return settings.buttonSize;
});

final textScaleFactorProvider = Provider<double>((ref) {
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return settings.textScaleFactor;
});

// Accessibility helper class
class AccessibilityActions {
  final AccessibilityService _service;

  AccessibilityActions(this._service);

  // Settings updates
  Future<void> updateSettings(AccessibilitySettings settings) async {
    await _service.updateSettings(settings);
  }

  Future<void> toggleHighContrast() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      highContrastMode: !current.highContrastMode,
    ));
  }

  Future<void> toggleLargeText() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      largeTextMode: !current.largeTextMode,
    ));
  }

  Future<void> toggleReduceMotion() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      reduceMotion: !current.reduceMotion,
    ));
  }

  Future<void> toggleScreenReader() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      screenReaderEnabled: !current.screenReaderEnabled,
    ));
  }

  Future<void> setButtonSize(double size) async {
    final current = _service.settings;
    await updateSettings(current.copyWith(buttonSize: size));
  }

  Future<void> setTextScaleFactor(double factor) async {
    final current = _service.settings;
    await updateSettings(current.copyWith(textScaleFactor: factor));
  }

  Future<void> setColorBlindnessType(ColorBlindnessType type) async {
    final current = _service.settings;
    await updateSettings(current.copyWith(colorBlindnessType: type));
  }

  Future<void> setNavigationMode(AppNavigationMode mode) async {
    final current = _service.settings;
    await updateSettings(current.copyWith(primaryNavigationMode: mode));
  }

  // Announcements
  void announce(String message, {bool interrupt = false}) {
    _service.announce(message, interrupt: interrupt);
  }

  void announcePageChange(String pageName) {
    _service.announcePageChange(pageName);
  }

  void announceAction(String action) {
    _service.announceAction(action);
  }

  void announceError(String error) {
    _service.announceError(error);
  }

  void announceSuccess(String message) {
    _service.announceSuccess(message);
  }

  // Focus management
  void setCurrentFocus(FocusNode focusNode, {String? name}) {
    _service.setCurrentFocus(focusNode, name: name);
  }

  void focusPrevious() {
    _service.focusPrevious();
  }

  void focusNext() {
    _service.focusNext();
  }

  void focusNamed(String name) {
    _service.focusNamed(name);
  }

  // Color and styling
  Color adjustColorForAccessibility(Color color) {
    return _service.adjustColorForAccessibility(color);
  }

  double getScaledTextSize(double baseSize) {
    return _service.getScaledTextSize(baseSize);
  }

  TextStyle getAccessibleTextStyle(TextStyle baseStyle) {
    return _service.getAccessibleTextStyle(baseStyle);
  }

  double getAccessibleTouchTargetSize(double baseSize) {
    return _service.getAccessibleTouchTargetSize(baseSize);
  }

  EdgeInsets getAccessiblePadding(EdgeInsets basePadding) {
    return _service.getAccessiblePadding(basePadding);
  }

  // Animation
  Duration getAccessibleAnimationDuration(Duration baseDuration) {
    return _service.getAccessibleAnimationDuration(baseDuration);
  }

  Curve getAccessibleAnimationCurve(Curve baseCurve) {
    return _service.getAccessibleAnimationCurve(baseCurve);
  }

  // Haptic feedback
  void performHapticFeedback(HapticFeedback type) {
    _service.performHapticFeedback(type);
  }

  // Preset configurations
  Future<void> applyLowVisionPreset() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      highContrastMode: true,
      largeTextMode: true,
      textScaleFactor: 1.5,
      buttonSize: 1.3,
      touchTargetSize: 48.0,
      boldTextMode: true,
      focusIndicators: true,
      showTooltips: true,
    ));
    announce('Low vision preset applied');
  }

  Future<void> applyMotorImpairmentPreset() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      buttonSize: 1.5,
      touchTargetSize: 56.0,
      stickyKeys: true,
      longPressEnabled: true,
      longPressDuration: 800,
      swipeGestures: false,
      confirmActions: true,
      extendedTimeouts: true,
      timeoutDuration: 60,
    ));
    announce('Motor impairment preset applied');
  }

  Future<void> applyCognitiveImpairmentPreset() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      simplifiedInterface: true,
      showHelpText: true,
      confirmActions: true,
      autoSave: true,
      autoSaveInterval: 15,
      breadcrumbs: true,
      progressIndicators: true,
      extendedTimeouts: true,
      pauseAnimations: true,
      autoplayMedia: false,
    ));
    announce('Cognitive impairment preset applied');
  }

  Future<void> applyScreenReaderPreset() async {
    final current = _service.settings;
    await updateSettings(current.copyWith(
      screenReaderEnabled: true,
      announceChanges: true,
      announceNotifications: true,
      tabNavigation: true,
      arrowKeyNavigation: true,
      skipLinks: true,
      landmarkNavigation: true,
      headingNavigation: true,
      focusIndicators: true,
      primaryNavigationMode: AppNavigationMode.keyboard,
      enabledNavigationModes: [AppNavigationMode.keyboard, AppNavigationMode.touch],
    ));
    announce('Screen reader preset applied');
  }

  Future<void> resetToDefaults() async {
    await updateSettings(AccessibilitySettings());
    announce('Accessibility settings reset to defaults');
  }
}

// Accessibility widget helpers
final accessibilityHelpersProvider = Provider<AccessibilityHelpers>((ref) {
  final actions = ref.watch(accessibilityActionsProvider);
  final settings = ref.watch(currentAccessibilitySettingsProvider);
  return AccessibilityHelpers(actions, settings);
});

class AccessibilityHelpers {
  final AccessibilityActions _actions;
  final AccessibilitySettings _settings;

  AccessibilityHelpers(this._actions, this._settings);

  // Widget builders
  Widget buildAccessibleButton({
    required Widget child,
    required VoidCallback? onPressed,
    String? tooltip,
    String? semanticsLabel,
    ButtonStyle? style,
  }) {
    final accessibleSize = _actions.getAccessibleTouchTargetSize(44.0);
    
    Widget button = ElevatedButton(
      onPressed: onPressed,
      style: style?.copyWith(
        minimumSize: WidgetStateProperty.all(Size(accessibleSize, accessibleSize)),
      ) ?? ElevatedButton.styleFrom(
        minimumSize: Size(accessibleSize, accessibleSize),
      ),
      child: child,
    );

    if (tooltip != null && _settings.showTooltips) {
      button = Tooltip(
        message: tooltip,
        child: button,
      );
    }

    if (semanticsLabel != null) {
      button = Semantics(
        label: semanticsLabel,
        button: true,
        child: button,
      );
    }

    return button;
  }

  Widget buildAccessibleText(
    String text, {
    TextStyle? style,
    String? semanticsLabel,
    bool isHeading = false,
  }) {
    final accessibleStyle = _actions.getAccessibleTextStyle(
      style ?? const TextStyle(fontSize: 14),
    );

    Widget textWidget = Text(text, style: accessibleStyle);

    if (semanticsLabel != null || isHeading) {
      textWidget = Semantics(
        label: semanticsLabel ?? text,
        header: isHeading,
        child: textWidget,
      );
    }

    return textWidget;
  }

  Widget buildAccessibleCard({
    required Widget child,
    String? semanticsLabel,
    VoidCallback? onTap,
  }) {
    final accessiblePadding = _actions.getAccessiblePadding(
      const EdgeInsets.all(16),
    );

    Widget card = Card(
      child: Padding(
        padding: accessiblePadding,
        child: child,
      ),
    );

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        child: card,
      );
    }

    if (semanticsLabel != null) {
      card = Semantics(
        label: semanticsLabel,
        child: card,
      );
    }

    return card;
  }

  Widget buildSkipLink({
    required String text,
    required VoidCallback onPressed,
  }) {
    if (!_settings.skipLinks) return const SizedBox.shrink();

    return Positioned(
      top: -100,
      left: 0,
      child: Focus(
        onFocusChange: (hasFocus) {
          if (hasFocus) {
            // Move skip link into view when focused
          }
        },
        child: ElevatedButton(
          onPressed: onPressed,
          child: Text(text),
        ),
      ),
    );
  }

  Widget buildProgressIndicator({
    required double value,
    String? semanticsLabel,
  }) {
    if (!_settings.progressIndicators) return const SizedBox.shrink();

    return Semantics(
      label: semanticsLabel ?? 'Progress: ${(value * 100).round()}%',
      value: (value * 100).round().toString(),
      child: LinearProgressIndicator(value: value),
    );
  }

  Widget buildBreadcrumbs(List<String> breadcrumbs) {
    if (!_settings.breadcrumbs || breadcrumbs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Semantics(
      label: 'Breadcrumb navigation: ${breadcrumbs.join(' > ')}',
      child: Row(
        children: breadcrumbs
            .asMap()
            .entries
            .map((entry) => [
                  Text(entry.value),
                  if (entry.key < breadcrumbs.length - 1)
                    const Icon(Icons.chevron_right),
                ])
            .expand((widgets) => widgets)
            .toList(),
      ),
    );
  }
}

// Initialize accessibility service provider
final initializeAccessibilityServiceProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(accessibilityServiceProvider);
  await service.initialize();
});
