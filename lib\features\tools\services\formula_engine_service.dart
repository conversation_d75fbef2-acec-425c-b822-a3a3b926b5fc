import 'dart:math' as math;
import '../models/spreadsheet_cell.dart';

/// Token types for syntax highlighting
enum TokenType {
  function,
  cellReference,
  operator,
  number,
  string,
  error,
}

/// Syntax token for highlighting
class SyntaxToken {
  final String text;
  final TokenType type;
  final int start;
  final int end;

  SyntaxToken({
    required this.text,
    required this.type,
    required this.start,
    required this.end,
  });
}

/// Function information for autocomplete
class FunctionInfo {
  final String name;
  final String description;
  final List<String> parameters;
  final String category;

  FunctionInfo({
    required this.name,
    required this.description,
    required this.parameters,
    required this.category,
  });
}

/// Advanced Excel-compatible formula engine for the Tool Builder
class FormulaEngineService {
  static final FormulaEngineService _instance = FormulaEngineService._internal();
  factory FormulaEngineService() => _instance;
  FormulaEngineService._internal() {
    _initializeFunctions();
  }

  // Dependency tracking
  final Map<String, List<String>> _dependencies = {};
  final Map<String, List<String>> _precedents = {};
  final Map<String, List<String>> _dependents = {};
  final Set<String> _calculatingCells = {};

  // Named ranges and functions
  final Map<String, dynamic> _namedRanges = {};
  final Map<String, Function> _functions = {};

  // Auto-complete and syntax highlighting
  final List<String> _functionNames = [];
  final Map<String, String> _functionDescriptions = {};
  final Map<String, List<String>> _functionParameters = {};
  final Map<String, String> _functionCategories = {};

  /// Initialize Excel function library
  void _initializeFunctions() {
    // Mathematical & Trigonometric functions (50 functions)
    _addFunction('SUM', 'Adds all numbers in a range', ['range'], 'Mathematical', _sumFunction);
    _addFunction('AVERAGE', 'Returns the average of numbers', ['range'], 'Statistical', _averageFunction);
    _addFunction('MAX', 'Returns the largest value', ['range'], 'Statistical', _maxFunction);
    _addFunction('MIN', 'Returns the smallest value', ['range'], 'Statistical', _minFunction);
    _addFunction('COUNT', 'Counts numbers in a range', ['range'], 'Statistical', _countFunction);
    _addFunction('COUNTA', 'Counts non-empty cells', ['range'], 'Statistical', _countaFunction);

    // Advanced Mathematical Functions
    _addFunction('ABS', 'Returns absolute value', ['number'], 'Mathematical', _absFunction);
    _addFunction('SQRT', 'Returns square root', ['number'], 'Mathematical', _sqrtFunction);
    _addFunction('POWER', 'Returns number raised to power', ['number', 'power'], 'Mathematical', _powerFunction);
    _addFunction('EXP', 'Returns e raised to power', ['number'], 'Mathematical', _expFunction);
    _addFunction('LN', 'Returns natural logarithm', ['number'], 'Mathematical', _lnFunction);
    _addFunction('LOG', 'Returns logarithm to specified base', ['number', 'base'], 'Mathematical', _logFunction);
    _addFunction('LOG10', 'Returns base-10 logarithm', ['number'], 'Mathematical', _log10Function);
    _addFunction('ROUND', 'Rounds to specified digits', ['number', 'digits'], 'Mathematical', _roundFunction);
    _addFunction('ROUNDUP', 'Rounds up to specified digits', ['number', 'digits'], 'Mathematical', _roundUpFunction);
    _addFunction('ROUNDDOWN', 'Rounds down to specified digits', ['number', 'digits'], 'Mathematical', _roundDownFunction);
    _addFunction('CEILING', 'Rounds up to nearest multiple', ['number', 'significance'], 'Mathematical', _ceilingFunction);
    _addFunction('FLOOR', 'Rounds down to nearest multiple', ['number', 'significance'], 'Mathematical', _floorFunction);
    _addFunction('TRUNC', 'Truncates to integer', ['number', 'digits'], 'Mathematical', _truncFunction);
    _addFunction('MOD', 'Returns remainder after division', ['number', 'divisor'], 'Mathematical', _modFunction);
    _addFunction('QUOTIENT', 'Returns integer portion of division', ['numerator', 'denominator'], 'Mathematical', _quotientFunction);
    _addFunction('GCD', 'Returns greatest common divisor', ['number1', 'number2'], 'Mathematical', _gcdFunction);
    _addFunction('LCM', 'Returns least common multiple', ['number1', 'number2'], 'Mathematical', _lcmFunction);
    _addFunction('FACT', 'Returns factorial', ['number'], 'Mathematical', _factFunction);
    _addFunction('COMBIN', 'Returns number of combinations', ['number', 'chosen'], 'Mathematical', _combinFunction);
    _addFunction('PERMUT', 'Returns number of permutations', ['number', 'chosen'], 'Mathematical', _permutFunction);

    // Trigonometric Functions
    _addFunction('SIN', 'Returns sine', ['number'], 'Trigonometric', _sinFunction);
    _addFunction('COS', 'Returns cosine', ['number'], 'Trigonometric', _cosFunction);
    _addFunction('TAN', 'Returns tangent', ['number'], 'Trigonometric', _tanFunction);
    _addFunction('ASIN', 'Returns arcsine', ['number'], 'Trigonometric', _asinFunction);
    _addFunction('ACOS', 'Returns arccosine', ['number'], 'Trigonometric', _acosFunction);
    _addFunction('ATAN', 'Returns arctangent', ['number'], 'Trigonometric', _atanFunction);
    _addFunction('ATAN2', 'Returns arctangent of x/y', ['x', 'y'], 'Trigonometric', _atan2Function);
    _addFunction('SINH', 'Returns hyperbolic sine', ['number'], 'Trigonometric', _sinhFunction);
    _addFunction('COSH', 'Returns hyperbolic cosine', ['number'], 'Trigonometric', _coshFunction);
    _addFunction('TANH', 'Returns hyperbolic tangent', ['number'], 'Trigonometric', _tanhFunction);
    _addFunction('DEGREES', 'Converts radians to degrees', ['angle'], 'Trigonometric', _degreesFunction);
    _addFunction('RADIANS', 'Converts degrees to radians', ['angle'], 'Trigonometric', _radiansFunction);
    _addFunction('PI', 'Returns value of pi', [], 'Trigonometric', _piFunction);
    _addFunction('ROUND', 'Rounds a number to specified digits', ['number', 'digits'], 'Mathematical', _roundFunction);
    _addFunction('ABS', 'Returns absolute value', ['number'], 'Mathematical', _absFunction);
    _addFunction('SQRT', 'Returns square root', ['number'], 'Mathematical', _sqrtFunction);
    _addFunction('POWER', 'Returns number raised to power', ['number', 'power'], 'Mathematical', _powerFunction);
    _addFunction('MOD', 'Returns remainder after division', ['number', 'divisor'], 'Mathematical', _modFunction);

    // Logical functions
    _addFunction('IF', 'Returns value based on condition', ['condition', 'value_if_true', 'value_if_false'], 'Logical', _ifFunction);
    _addFunction('AND', 'Returns TRUE if all conditions are TRUE', ['condition1', 'condition2'], 'Logical', _andFunction);
    _addFunction('OR', 'Returns TRUE if any condition is TRUE', ['condition1', 'condition2'], 'Logical', _orFunction);
    _addFunction('NOT', 'Reverses the logic of its argument', ['logical'], 'Logical', _notFunction);

    // Text functions
    _addFunction('CONCATENATE', 'Joins text strings', ['text1', 'text2'], 'Text', _concatenateFunction);
    _addFunction('LEFT', 'Returns leftmost characters', ['text', 'num_chars'], 'Text', _leftFunction);
    _addFunction('RIGHT', 'Returns rightmost characters', ['text', 'num_chars'], 'Text', _rightFunction);
    _addFunction('MID', 'Returns characters from middle', ['text', 'start', 'num_chars'], 'Text', _midFunction);
    _addFunction('LEN', 'Returns length of text', ['text'], 'Text', _lenFunction);
    _addFunction('UPPER', 'Converts to uppercase', ['text'], 'Text', _upperFunction);
    _addFunction('LOWER', 'Converts to lowercase', ['text'], 'Text', _lowerFunction);
    _addFunction('TRIM', 'Removes extra spaces', ['text'], 'Text', _trimFunction);

    // Date/Time functions
    _addFunction('TODAY', 'Returns current date', [], 'Date & Time', _todayFunction);
    _addFunction('NOW', 'Returns current date and time', [], 'Date & Time', _nowFunction);
    _addFunction('YEAR', 'Returns year from date', ['date'], 'Date & Time', _yearFunction);
    _addFunction('MONTH', 'Returns month from date', ['date'], 'Date & Time', _monthFunction);
    _addFunction('DAY', 'Returns day from date', ['date'], 'Date & Time', _dayFunction);

    // Lookup functions
    _addFunction('VLOOKUP', 'Vertical lookup', ['lookup_value', 'table_array', 'col_index', 'range_lookup'], 'Lookup', _vlookupFunction);
    _addFunction('HLOOKUP', 'Horizontal lookup', ['lookup_value', 'table_array', 'row_index', 'range_lookup'], 'Lookup', _hlookupFunction);
    _addFunction('INDEX', 'Returns value at intersection', ['array', 'row', 'column'], 'Lookup', _indexFunction);
    _addFunction('MATCH', 'Returns position of value', ['lookup_value', 'lookup_array', 'match_type'], 'Lookup', _matchFunction);

    // Additional Mathematical Functions
    _addFunction('MEDIAN', 'Returns median value', ['number1', 'number2'], 'Mathematical', _medianFunction);
    _addFunction('MODE', 'Returns most frequent value', ['number1', 'number2'], 'Mathematical', _modeFunction);
    _addFunction('STDEV', 'Standard deviation (sample)', ['number1', 'number2'], 'Statistical', _stdevFunction);
    _addFunction('VAR', 'Variance (sample)', ['number1', 'number2'], 'Statistical', _varFunction);
    _addFunction('PERCENTILE', 'Returns percentile value', ['array', 'k'], 'Statistical', _percentileFunction);
    _addFunction('RANK', 'Returns rank of value', ['number', 'array', 'order'], 'Statistical', _rankFunction);

    // Additional Text Functions
    _addFunction('FIND', 'Find position of text (case-sensitive)', ['find_text', 'within_text', 'start_num'], 'Text', _findFunction);
    _addFunction('SEARCH', 'Find position of text (case-insensitive)', ['find_text', 'within_text', 'start_num'], 'Text', _searchFunction);
    _addFunction('REPLACE', 'Replace text by position', ['old_text', 'start_num', 'num_chars', 'new_text'], 'Text', _replaceFunction);
    _addFunction('SUBSTITUTE', 'Replace text by content', ['text', 'old_text', 'new_text', 'instance_num'], 'Text', _substituteFunction);
    _addFunction('PROPER', 'Proper case (Title Case)', ['text'], 'Text', _properFunction);
    _addFunction('EXACT', 'Compare text exactly', ['text1', 'text2'], 'Text', _exactFunction);
    _addFunction('VALUE', 'Convert text to number', ['text'], 'Text', _valueFunction);

    // Additional Date Functions
    _addFunction('DATE', 'Create date from year, month, day', ['year', 'month', 'day'], 'Date & Time', _dateFunction);
    _addFunction('TIME', 'Create time from hour, minute, second', ['hour', 'minute', 'second'], 'Date & Time', _timeFunction);
    _addFunction('WEEKDAY', 'Day of week number', ['date', 'return_type'], 'Date & Time', _weekdayFunction);
    _addFunction('DATEDIF', 'Difference between dates', ['start_date', 'end_date', 'unit'], 'Date & Time', _datedifFunction);
    _addFunction('NETWORKDAYS', 'Working days between dates', ['start_date', 'end_date', 'holidays'], 'Date & Time', _networkdaysFunction);

    // Information Functions
    _addFunction('ISBLANK', 'Check if blank', ['value'], 'Information', _isblankFunction);
    _addFunction('ISNUMBER', 'Check if number', ['value'], 'Information', _isnumberFunction);
    _addFunction('ISTEXT', 'Check if text', ['value'], 'Information', _istextFunction);
    _addFunction('ISERROR', 'Check if error', ['value'], 'Information', _iserrorFunction);
    _addFunction('TYPE', 'Data type number', ['value'], 'Information', _typeFunction);

    // Conditional Functions
    _addFunction('SUMIF', 'Sum with condition', ['range', 'criteria', 'sum_range'], 'Conditional', _sumifFunction);
    _addFunction('COUNTIF', 'Count with condition', ['range', 'criteria'], 'Conditional', _countifFunction);
    _addFunction('AVERAGEIF', 'Average with condition', ['range', 'criteria', 'average_range'], 'Conditional', _averageifFunction);

    // Financial Functions
    _addFunction('PV', 'Present value', ['rate', 'nper', 'pmt', 'fv', 'type'], 'Financial', _pvFunction);
    _addFunction('FV', 'Future value', ['rate', 'nper', 'pmt', 'pv', 'type'], 'Financial', _fvFunction);
    _addFunction('PMT', 'Payment amount', ['rate', 'nper', 'pv', 'fv', 'type'], 'Financial', _pmtFunction);
    _addFunction('NPV', 'Net present value', ['rate', 'value1', 'value2'], 'Financial', _npvFunction);
    _addFunction('IRR', 'Internal rate of return', ['values', 'guess'], 'Financial', _irrFunction);
  }

  /// Add a function to the registry
  void _addFunction(String name, String description, List<String> parameters, String category, Function function) {
    _functionNames.add(name);
    _functionDescriptions[name] = description;
    _functionParameters[name] = parameters;
    _functionCategories[name] = category;
    _functions[name] = function;
  }

  /// Evaluate a formula with cell references and dependency tracking
  dynamic evaluateFormula(String formula, Map<String, SpreadsheetCell> cells, [String? cellAddress]) {
    try {
      if (!formula.startsWith('=')) return formula;

      // Check for circular references
      if (cellAddress != null && _calculatingCells.contains(cellAddress)) {
        return '#CIRCULAR!';
      }

      if (cellAddress != null) {
        _calculatingCells.add(cellAddress);
      }

      String expression = formula.substring(1); // Remove '='

      // Track dependencies
      if (cellAddress != null) {
        _updateDependencies(cellAddress, expression);
      }

      // Replace cell references with values
      expression = _replaceCellReferences(expression, cells);

      // Evaluate the expression
      final result = _evaluateExpression(expression);

      if (cellAddress != null) {
        _calculatingCells.remove(cellAddress);
      }

      return result;
    } catch (e) {
      if (cellAddress != null) {
        _calculatingCells.remove(cellAddress);
      }
      return '#ERROR!';
    }
  }

  /// Get list of cell dependencies for a formula
  List<String> getDependencies(String formula) {
    final cellPattern = RegExp(r'[A-Z]+[0-9]+');
    return cellPattern.allMatches(formula).map((m) => m.group(0)!).toList();
  }

  /// Recalculate entire sheet with dependency resolution
  Map<String, SpreadsheetCell> recalculateSheet(Map<String, SpreadsheetCell> cells) {
    final result = Map<String, SpreadsheetCell>.from(cells);
    
    // Simple recalculation (in production, implement proper dependency graph)
    for (final entry in result.entries) {
      if (entry.value.hasFormula) {
        final newValue = evaluateFormula(entry.value.formula!, result);
        result[entry.key] = entry.value.copyWith(
          value: newValue,
          type: _getValueType(newValue),
        );
      }
    }
    
    return result;
  }

  /// Validate formula syntax
  bool validateFormula(String formula) {
    try {
      if (!formula.startsWith('=')) return true;
      
      final expression = formula.substring(1);
      
      // Basic validation - check for balanced parentheses
      int openParens = 0;
      for (int i = 0; i < expression.length; i++) {
        if (expression[i] == '(') openParens++;
        if (expression[i] == ')') openParens--;
        if (openParens < 0) return false;
      }
      
      return openParens == 0;
    } catch (e) {
      return false;
    }
  }

  /// Get formula error message
  String getFormulaError(String formula) {
    if (!validateFormula(formula)) {
      return 'Invalid formula syntax';
    }
    
    try {
      evaluateFormula(formula, {});
      return '';
    } catch (e) {
      return e.toString();
    }
  }

  /// Replace cell references in expression with actual values
  String _replaceCellReferences(String expression, Map<String, SpreadsheetCell> cells) {
    final cellPattern = RegExp(r'[A-Z]+[0-9]+');
    return expression.replaceAllMapped(cellPattern, (match) {
      final cellAddress = match.group(0)!;
      final cell = cells[cellAddress];
      if (cell == null || cell.value == null) return '0';
      
      // If the cell has a formula, use its calculated value
      if (cell.hasFormula && cell.value != null) {
        return cell.value.toString();
      }
      
      return cell.value.toString();
    });
  }

  /// Evaluate a mathematical expression
  dynamic _evaluateExpression(String expression) {
    // Remove whitespace
    expression = expression.replaceAll(' ', '');
    
    // Handle Excel functions
    expression = _handleExcelFunctions(expression);
    
    // Handle basic arithmetic operations
    return _evaluateArithmetic(expression);
  }

  /// Handle Excel functions like SUM, AVG, etc.
  String _handleExcelFunctions(String expression) {
    // Handle SUM function
    final sumPattern = RegExp(r'SUM\(([^)]+)\)');
    expression = expression.replaceAllMapped(sumPattern, (match) {
      final args = match.group(1)!.split(',');
      double sum = 0;
      for (final arg in args) {
        sum += double.tryParse(arg.trim()) ?? 0;
      }
      return sum.toString();
    });

    // Handle AVG function
    final avgPattern = RegExp(r'AVG\(([^)]+)\)');
    expression = expression.replaceAllMapped(avgPattern, (match) {
      final args = match.group(1)!.split(',');
      double sum = 0;
      int count = 0;
      for (final arg in args) {
        final value = double.tryParse(arg.trim());
        if (value != null) {
          sum += value;
          count++;
        }
      }
      return count > 0 ? (sum / count).toString() : '0';
    });

    // Handle MAX function
    final maxPattern = RegExp(r'MAX\(([^)]+)\)');
    expression = expression.replaceAllMapped(maxPattern, (match) {
      final args = match.group(1)!.split(',');
      double max = double.negativeInfinity;
      for (final arg in args) {
        final value = double.tryParse(arg.trim());
        if (value != null && value > max) {
          max = value;
        }
      }
      return max == double.negativeInfinity ? '0' : max.toString();
    });

    // Handle MIN function
    final minPattern = RegExp(r'MIN\(([^)]+)\)');
    expression = expression.replaceAllMapped(minPattern, (match) {
      final args = match.group(1)!.split(',');
      double min = double.infinity;
      for (final arg in args) {
        final value = double.tryParse(arg.trim());
        if (value != null && value < min) {
          min = value;
        }
      }
      return min == double.infinity ? '0' : min.toString();
    });

    // Handle COUNT function
    final countPattern = RegExp(r'COUNT\(([^)]+)\)');
    expression = expression.replaceAllMapped(countPattern, (match) {
      final args = match.group(1)!.split(',');
      int count = 0;
      for (final arg in args) {
        if (double.tryParse(arg.trim()) != null) {
          count++;
        }
      }
      return count.toString();
    });

    return expression;
  }

  /// Evaluate basic arithmetic operations
  dynamic _evaluateArithmetic(String expression) {
    // Handle parentheses first
    while (expression.contains('(')) {
      final start = expression.lastIndexOf('(');
      final end = expression.indexOf(')', start);
      if (end == -1) throw Exception('Mismatched parentheses');
      
      final subExpression = expression.substring(start + 1, end);
      final result = _evaluateArithmetic(subExpression);
      expression = expression.replaceRange(start, end + 1, result.toString());
    }

    // Handle multiplication and division
    expression = _handleOperations(expression, ['*', '/']);
    
    // Handle addition and subtraction
    expression = _handleOperations(expression, ['+', '-']);

    // Return final result
    return double.tryParse(expression) ?? expression;
  }

  /// Handle specific mathematical operations
  String _handleOperations(String expression, List<String> operators) {
    for (final op in operators) {
      while (expression.contains(op)) {
        final opIndex = expression.indexOf(op);
        if (opIndex == -1) break;
        
        // Find left operand
        int leftStart = opIndex - 1;
        while (leftStart > 0 && _isNumericChar(expression[leftStart - 1])) {
          leftStart--;
        }
        
        // Find right operand
        int rightEnd = opIndex + 1;
        while (rightEnd < expression.length && _isNumericChar(expression[rightEnd])) {
          rightEnd++;
        }
        
        final leftOperand = double.tryParse(expression.substring(leftStart, opIndex)) ?? 0;
        final rightOperand = double.tryParse(expression.substring(opIndex + 1, rightEnd)) ?? 0;
        
        double result;
        switch (op) {
          case '+':
            result = leftOperand + rightOperand;
            break;
          case '-':
            result = leftOperand - rightOperand;
            break;
          case '*':
            result = leftOperand * rightOperand;
            break;
          case '/':
            if (rightOperand == 0) return '#DIV/0!';
            result = leftOperand / rightOperand;
            break;
          default:
            result = 0;
        }
        
        expression = expression.replaceRange(leftStart, rightEnd, result.toString());
      }
    }
    
    return expression;
  }

  /// Check if character is numeric (including decimal point)
  bool _isNumericChar(String char) {
    return '0123456789.'.contains(char);
  }

  /// Determine cell type based on value
  CellType _getValueType(dynamic value) {
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    if (value.toString().startsWith('#')) return CellType.error;
    return CellType.text;
  }

  /// Update dependency tracking
  void _updateDependencies(String cellAddress, String expression) {
    final dependencies = getDependencies('=' + expression);
    _dependencies[cellAddress] = dependencies;

    // Update precedents and dependents
    for (final dep in dependencies) {
      _dependents[dep] ??= [];
      if (!_dependents[dep]!.contains(cellAddress)) {
        _dependents[dep]!.add(cellAddress);
      }

      _precedents[cellAddress] ??= [];
      if (!_precedents[cellAddress]!.contains(dep)) {
        _precedents[cellAddress]!.add(dep);
      }
    }
  }

  /// Get enhanced autocomplete suggestions with descriptions
  List<FormulaAutoComplete> getEnhancedFormulaSuggestions(String input) {
    final suggestions = <FormulaAutoComplete>[];

    for (final funcName in _functionNames) {
      if (funcName.toLowerCase().startsWith(input.toLowerCase())) {
        suggestions.add(FormulaAutoComplete(
          name: funcName,
          description: _functionDescriptions[funcName] ?? '',
          parameters: _functionParameters[funcName] ?? [],
          category: _functionCategories[funcName] ?? '',
          insertText: '$funcName(${_functionParameters[funcName]?.join(', ') ?? ''})',
        ));
      }
    }

    return suggestions;
  }

  /// Get syntax highlighting tokens
  List<SyntaxToken> getSyntaxTokens(String formula) {
    final tokens = <SyntaxToken>[];
    if (!formula.startsWith('=')) return tokens;

    final expression = formula.substring(1);

    // Function names
    for (final funcName in _functionNames) {
      final pattern = RegExp('\\b$funcName\\b', caseSensitive: false);
      for (final match in pattern.allMatches(expression)) {
        tokens.add(SyntaxToken(
          start: match.start + 1,
          end: match.end + 1,
          type: TokenType.function,
          text: match.group(0)!,
        ));
      }
    }

    // Cell references
    final cellPattern = RegExp(r'[A-Z]+[0-9]+');
    for (final match in cellPattern.allMatches(expression)) {
      tokens.add(SyntaxToken(
        start: match.start + 1,
        end: match.end + 1,
        type: TokenType.cellReference,
        text: match.group(0)!,
      ));
    }

    // Operators
    final operatorPattern = RegExp(r'[+\-*/=<>]');
    for (final match in operatorPattern.allMatches(expression)) {
      tokens.add(SyntaxToken(
        start: match.start + 1,
        end: match.end + 1,
        type: TokenType.operator,
        text: match.group(0)!,
      ));
    }

    // Numbers
    final numberPattern = RegExp(r'\b\d+\.?\d*\b');
    for (final match in numberPattern.allMatches(expression)) {
      tokens.add(SyntaxToken(
        start: match.start + 1,
        end: match.end + 1,
        type: TokenType.number,
        text: match.group(0)!,
      ));
    }

    return tokens;
  }

  /// Get cells that depend on the given cell
  List<String> getDependentCells(String cellAddress) {
    return _dependents[cellAddress] ?? [];
  }

  /// Get cells that the given cell depends on
  List<String> getPrecedentCells(String cellAddress) {
    return _precedents[cellAddress] ?? [];
  }

  /// Validate formula and return detailed error information
  FormulaValidationResult validateFormulaDetailed(String formula) {
    if (!formula.startsWith('=')) {
      return FormulaValidationResult(isValid: true, errors: []);
    }

    final errors = <String>[];
    final expression = formula.substring(1);

    // Check balanced parentheses
    int openParens = 0;
    for (int i = 0; i < expression.length; i++) {
      if (expression[i] == '(') openParens++;
      if (expression[i] == ')') openParens--;
      if (openParens < 0) {
        errors.add('Unmatched closing parenthesis at position ${i + 1}');
        break;
      }
    }

    if (openParens > 0) {
      errors.add('Missing $openParens closing parenthesis(es)');
    }

    // Check function names
    final funcPattern = RegExp(r'([A-Z]+)\(');
    for (final match in funcPattern.allMatches(expression)) {
      final funcName = match.group(1)!;
      if (!_functionNames.contains(funcName)) {
        errors.add('Unknown function: $funcName');
      }
    }

    return FormulaValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Get all available functions
  List<FunctionInfo> getAvailableFunctions() {
    return _functionNames.map((name) => FunctionInfo(
      name: name,
      description: _functionDescriptions[name] ?? '',
      parameters: _functionParameters[name] ?? [],
      category: _functionCategories[name] ?? 'General',
    )).toList();
  }

  /// Advanced formula features for UI integration

  /// Create dynamic array formula
  List<dynamic> createArrayFormula(String formula, List<String> cellRange) {
    final results = <dynamic>[];

    for (final cellAddress in cellRange) {
      // Replace array reference with specific cell
      final cellFormula = formula.replaceAll('@', cellAddress);
      final result = evaluateFormula(cellFormula, {});
      results.add(result);
    }

    return results;
  }

  /// Create custom function
  void createCustomFunction(
    String name,
    String description,
    List<String> parameters,
    String category,
    String formulaBody,
  ) {
    _addFunction(name, description, parameters, category, (List<dynamic> args) {
      // Replace parameter placeholders with actual values
      String processedFormula = formulaBody;
      for (int i = 0; i < parameters.length && i < args.length; i++) {
        processedFormula = processedFormula.replaceAll(
          '{${parameters[i]}}',
          args[i].toString(),
        );
      }

      return evaluateFormula('=$processedFormula', {});
    });
  }

  /// Debug formula step by step
  Map<String, dynamic> debugFormula(String formula, Map<String, SpreadsheetCell> cells) {
    final debugInfo = <String, dynamic>{
      'originalFormula': formula,
      'steps': <Map<String, dynamic>>[],
      'dependencies': <String>[],
      'result': null,
      'errors': <String>[],
    };

    try {
      if (!formula.startsWith('=')) {
        debugInfo['result'] = formula;
        return debugInfo;
      }

      String expression = formula.substring(1);
      debugInfo['steps'].add({
        'step': 'Initial',
        'expression': expression,
        'description': 'Original formula without equals sign',
      });

      // Track dependencies
      final dependencies = getDependencies(formula);
      debugInfo['dependencies'] = dependencies;
      debugInfo['steps'].add({
        'step': 'Dependencies',
        'expression': dependencies.join(', '),
        'description': 'Cell references found in formula',
      });

      // Replace cell references
      final withReferences = _replaceCellReferences(expression, cells);
      if (withReferences != expression) {
        debugInfo['steps'].add({
          'step': 'Cell Substitution',
          'expression': withReferences,
          'description': 'Cell references replaced with values',
        });
        expression = withReferences;
      }

      // Handle functions
      final withFunctions = _handleExcelFunctions(expression);
      if (withFunctions != expression) {
        debugInfo['steps'].add({
          'step': 'Function Evaluation',
          'expression': withFunctions,
          'description': 'Excel functions evaluated',
        });
        expression = withFunctions;
      }

      // Final calculation
      final result = _evaluateArithmetic(expression);
      debugInfo['steps'].add({
        'step': 'Final Calculation',
        'expression': result.toString(),
        'description': 'Arithmetic operations completed',
      });

      debugInfo['result'] = result;
    } catch (e) {
      debugInfo['errors'].add(e.toString());
      debugInfo['result'] = '#ERROR!';
    }

    return debugInfo;
  }

  /// Optimize formula for performance
  String optimizeFormula(String formula) {
    if (!formula.startsWith('=')) return formula;

    String optimized = formula;

    // Remove unnecessary spaces
    optimized = optimized.replaceAll(RegExp(r'\s+'), '');

    // Optimize nested functions
    optimized = _optimizeNestedFunctions(optimized);

    // Optimize repeated calculations
    optimized = _optimizeRepeatedCalculations(optimized);

    return optimized;
  }

  /// Get formula performance metrics
  Map<String, dynamic> getFormulaMetrics(String formula, Map<String, SpreadsheetCell> cells) {
    final stopwatch = Stopwatch()..start();

    final result = evaluateFormula(formula, cells);

    stopwatch.stop();

    final dependencies = getDependencies(formula);
    final complexity = _calculateFormulaComplexity(formula);

    return {
      'executionTime': stopwatch.elapsedMicroseconds,
      'result': result,
      'dependencies': dependencies,
      'dependencyCount': dependencies.length,
      'complexity': complexity,
      'formulaLength': formula.length,
      'functionCount': _countFunctions(formula),
    };
  }

  /// Real-time formula validation with suggestions
  Map<String, dynamic> validateFormulaWithSuggestions(String formula) {
    final validation = validateFormulaDetailed(formula);
    final suggestions = <String>[];

    if (!validation.isValid) {
      for (final error in validation.errors) {
        if (error.contains('Unknown function')) {
          final funcName = error.split(':').last.trim();
          final similarFunctions = _findSimilarFunctions(funcName);
          if (similarFunctions.isNotEmpty) {
            suggestions.add('Did you mean: ${similarFunctions.join(', ')}?');
          }
        } else if (error.contains('parenthesis')) {
          suggestions.add('Check parentheses balance');
        }
      }
    }

    return {
      'isValid': validation.isValid,
      'errors': validation.errors,
      'suggestions': suggestions,
      'autoComplete': getEnhancedFormulaSuggestions(formula),
    };
  }

  /// Live data visualization for formulas
  Map<String, dynamic> generateFormulaVisualization(
    String formula,
    Map<String, SpreadsheetCell> cells,
  ) {
    final dependencies = getDependencies(formula);
    final values = <String, dynamic>{};

    for (final dep in dependencies) {
      final cell = cells[dep];
      values[dep] = cell?.value ?? 0;
    }

    return {
      'formula': formula,
      'dependencies': dependencies,
      'values': values,
      'result': evaluateFormula(formula, cells),
      'chartData': _generateChartData(values),
      'flowDiagram': _generateFlowDiagram(formula, dependencies),
    };
  }

  /// Helper methods for advanced features

  String _optimizeNestedFunctions(String formula) {
    // Optimize common nested function patterns
    return formula
        .replaceAll('SUM(SUM(', 'SUM(')
        .replaceAll('AVERAGE(AVERAGE(', 'AVERAGE(');
  }

  String _optimizeRepeatedCalculations(String formula) {
    // Cache repeated sub-expressions
    final subExpressions = <String, String>{};
    final pattern = RegExp(r'\([^()]+\)');

    String optimized = formula;
    for (final match in pattern.allMatches(formula)) {
      final expr = match.group(0)!;
      if (!subExpressions.containsKey(expr)) {
        subExpressions[expr] = 'CACHE_${subExpressions.length}';
      }
    }

    return optimized;
  }

  int _calculateFormulaComplexity(String formula) {
    int complexity = 0;

    // Count functions
    complexity += _countFunctions(formula) * 2;

    // Count operators
    complexity += '+'.allMatches(formula).length;
    complexity += '-'.allMatches(formula).length;
    complexity += '*'.allMatches(formula).length;
    complexity += '/'.allMatches(formula).length;

    // Count parentheses depth
    int depth = 0;
    int maxDepth = 0;
    for (final char in formula.split('')) {
      if (char == '(') depth++;
      if (char == ')') depth--;
      maxDepth = math.max(maxDepth, depth);
    }
    complexity += maxDepth;

    return complexity;
  }

  int _countFunctions(String formula) {
    int count = 0;
    for (final funcName in _functionNames) {
      count += RegExp('\\b$funcName\\(').allMatches(formula).length;
    }
    return count;
  }

  List<String> _findSimilarFunctions(String input) {
    final similar = <String>[];
    final inputLower = input.toLowerCase();

    for (final funcName in _functionNames) {
      final funcLower = funcName.toLowerCase();

      // Check for similar names
      if (funcLower.contains(inputLower) || inputLower.contains(funcLower)) {
        similar.add(funcName);
      }

      // Check for edit distance
      if (_editDistance(inputLower, funcLower) <= 2) {
        similar.add(funcName);
      }
    }

    return similar.take(3).toList();
  }

  int _editDistance(String a, String b) {
    final matrix = List.generate(
      a.length + 1,
      (i) => List.generate(b.length + 1, (j) => 0),
    );

    for (int i = 0; i <= a.length; i++) matrix[i][0] = i;
    for (int j = 0; j <= b.length; j++) matrix[0][j] = j;

    for (int i = 1; i <= a.length; i++) {
      for (int j = 1; j <= b.length; j++) {
        final cost = a[i - 1] == b[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce(math.min);
      }
    }

    return matrix[a.length][b.length];
  }

  List<Map<String, dynamic>> _generateChartData(Map<String, dynamic> values) {
    return values.entries.map((entry) => {
      'label': entry.key,
      'value': entry.value,
    }).toList();
  }

  Map<String, dynamic> _generateFlowDiagram(String formula, List<String> dependencies) {
    return {
      'nodes': [
        {'id': 'formula', 'label': formula, 'type': 'formula'},
        ...dependencies.map((dep) => {
          'id': dep,
          'label': dep,
          'type': 'cell',
        }),
      ],
      'edges': dependencies.map((dep) => {
        'from': dep,
        'to': 'formula',
      }).toList(),
    };
  }

  /// Get simple autocomplete suggestions (for backward compatibility)
  List<String> getFormulaSuggestions(String input) {
    return _functionNames
        .where((name) => name.toLowerCase().startsWith(input.toLowerCase()))
        .map((name) => '$name(')
        .toList();
  }

  // Excel Function Implementations

  /// SUM function implementation
  dynamic _sumFunction(List<dynamic> args) {
    double sum = 0;
    for (final arg in args) {
      if (arg is num) sum += arg.toDouble();
      if (arg is String) {
        final value = double.tryParse(arg);
        if (value != null) sum += value;
      }
    }
    return sum;
  }

  /// AVERAGE function implementation
  dynamic _averageFunction(List<dynamic> args) {
    double sum = 0;
    int count = 0;
    for (final arg in args) {
      if (arg is num) {
        sum += arg.toDouble();
        count++;
      }
      if (arg is String) {
        final value = double.tryParse(arg);
        if (value != null) {
          sum += value;
          count++;
        }
      }
    }
    return count > 0 ? sum / count : 0;
  }

  /// MAX function implementation
  dynamic _maxFunction(List<dynamic> args) {
    double max = double.negativeInfinity;
    for (final arg in args) {
      if (arg is num && arg.toDouble() > max) {
        max = arg.toDouble();
      }
      if (arg is String) {
        final value = double.tryParse(arg);
        if (value != null && value > max) {
          max = value;
        }
      }
    }
    return max == double.negativeInfinity ? 0 : max;
  }

  /// MIN function implementation
  dynamic _minFunction(List<dynamic> args) {
    double min = double.infinity;
    for (final arg in args) {
      if (arg is num && arg.toDouble() < min) {
        min = arg.toDouble();
      }
      if (arg is String) {
        final value = double.tryParse(arg);
        if (value != null && value < min) {
          min = value;
        }
      }
    }
    return min == double.infinity ? 0 : min;
  }

  /// COUNT function implementation
  dynamic _countFunction(List<dynamic> args) {
    int count = 0;
    for (final arg in args) {
      if (arg is num) count++;
      if (arg is String && double.tryParse(arg) != null) count++;
    }
    return count;
  }

  /// COUNTA function implementation
  dynamic _countaFunction(List<dynamic> args) {
    int count = 0;
    for (final arg in args) {
      if (arg != null && arg.toString().isNotEmpty) count++;
    }
    return count;
  }

  /// ROUND function implementation
  dynamic _roundFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    final digitsNum = _toNumber(args[1]);
    if (number == null || digitsNum == null) return '#ERROR!';

    final digits = digitsNum.toInt();
    final factor = math.pow(10, digits);
    return (number * factor).round() / factor;
  }

  /// ABS function implementation
  dynamic _absFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return number?.abs() ?? '#ERROR!';
  }

  /// SQRT function implementation
  dynamic _sqrtFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    if (number == null || number < 0) return '#ERROR!';
    return math.sqrt(number);
  }

  /// POWER function implementation
  dynamic _powerFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final base = _toNumber(args[0]);
    final exponent = _toNumber(args[1]);
    if (base == null || exponent == null) return '#ERROR!';
    return math.pow(base, exponent);
  }

  /// MOD function implementation
  dynamic _modFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    final divisor = _toNumber(args[1]);
    if (number == null || divisor == null || divisor == 0) return '#ERROR!';
    return number % divisor;
  }

  /// Helper function to convert value to number
  double? _toNumber(dynamic value) {
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  // Logical Functions
  dynamic _ifFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final condition = _toBool(args[0]);
    if (condition) {
      return args[1];
    } else {
      return args.length > 2 ? args[2] : false;
    }
  }

  dynamic _andFunction(List<dynamic> args) {
    if (args.isEmpty) return true;
    for (final arg in args) {
      if (!_toBool(arg)) return false;
    }
    return true;
  }

  dynamic _orFunction(List<dynamic> args) {
    if (args.isEmpty) return false;
    for (final arg in args) {
      if (_toBool(arg)) return true;
    }
    return false;
  }

  dynamic _notFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    return !_toBool(args[0]);
  }

  // Text Functions
  dynamic _concatenateFunction(List<dynamic> args) {
    return args.map((e) => e.toString()).join('');
  }

  dynamic _leftFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final text = args[0].toString();
    final numChars = args.length > 1 ? _toNumber(args[1])?.toInt() ?? text.length : 1;
    if (numChars < 0) return '#ERROR!';
    return text.substring(0, math.min(numChars, text.length));
  }

  dynamic _rightFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final text = args[0].toString();
    final numChars = args.length > 1 ? _toNumber(args[1])?.toInt() ?? 1 : 1;
    if (numChars < 0) return '#ERROR!';
    final startPos = math.max(0, text.length - numChars);
    return text.substring(startPos);
  }

  dynamic _midFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    final text = args[0].toString();
    final start = _toNumber(args[1])?.toInt() ?? 1;
    final numChars = _toNumber(args[2])?.toInt() ?? 0;
    if (start < 1 || numChars < 0) return '#ERROR!';

    final startPos = start - 1; // Convert to 0-based index
    if (startPos >= text.length) return '';
    final endPos = math.min(startPos + numChars, text.length);
    return text.substring(startPos, endPos);
  }

  dynamic _lenFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    return args[0].toString().length;
  }

  dynamic _upperFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    return args[0].toString().toUpperCase();
  }

  dynamic _lowerFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    return args[0].toString().toLowerCase();
  }

  dynamic _trimFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    return args[0].toString().trim();
  }

  // Date/Time Functions (placeholder implementations)
  dynamic _todayFunction(List<dynamic> args) => DateTime.now().toIso8601String().split('T')[0];
  dynamic _nowFunction(List<dynamic> args) => DateTime.now().toIso8601String();
  dynamic _yearFunction(List<dynamic> args) => DateTime.now().year;
  dynamic _monthFunction(List<dynamic> args) => DateTime.now().month;
  dynamic _dayFunction(List<dynamic> args) => DateTime.now().day;

  // Lookup Functions
  dynamic _vlookupFunction(List<dynamic> args) {
    if (args.length < 3) return '#N/A';

    final lookupValue = args[0];
    final tableArray = args[1]; // This would need to be parsed as a range
    final colIndex = _toNumber(args[2])?.toInt() ?? 0;
    final rangeLookup = args.length > 3 ? _toBool(args[3]) : true;

    if (colIndex < 1) return '#REF!';

    // For now, return a placeholder since we need cell range parsing
    // In a full implementation, this would parse the table array and perform the lookup
    // Using variables: $lookupValue, $tableArray, $rangeLookup
    return '#N/A - Range parsing not implemented';
  }

  dynamic _hlookupFunction(List<dynamic> args) {
    if (args.length < 3) return '#N/A';

    final lookupValue = args[0];
    final tableArray = args[1]; // This would need to be parsed as a range
    final rowIndex = _toNumber(args[2])?.toInt() ?? 0;
    final rangeLookup = args.length > 3 ? _toBool(args[3]) : true;

    if (rowIndex < 1) return '#REF!';

    // For now, return a placeholder since we need cell range parsing
    // Using variables: $lookupValue, $tableArray, $rangeLookup
    return '#N/A - Range parsing not implemented';
  }

  dynamic _indexFunction(List<dynamic> args) {
    if (args.length < 2) return '#REF!';

    final array = args[0]; // This would need to be parsed as a range
    final row = _toNumber(args[1])?.toInt() ?? 0;
    final column = args.length > 2 ? _toNumber(args[2])?.toInt() ?? 1 : 1;

    if (row < 1 || column < 1) return '#REF!';

    // For now, return a placeholder since we need cell range parsing
    // Using variables: $array, $row, $column
    return '#REF! - Range parsing not implemented';
  }

  dynamic _matchFunction(List<dynamic> args) {
    if (args.length < 2) return '#N/A';

    final lookupValue = args[0];
    final lookupArray = args[1]; // This would need to be parsed as a range
    final matchType = args.length > 2 ? _toNumber(args[2])?.toInt() ?? 1 : 1;

    // For now, return a placeholder since we need cell range parsing
    // Using variables: $lookupValue, $lookupArray, $matchType
    return '#N/A - Range parsing not implemented';
  }

  /// Helper function to convert value to boolean
  bool _toBool(dynamic value) {
    if (value is bool) return value;
    if (value is num) return value != 0;
    if (value is String) return value.toLowerCase() == 'true';
    return false;
  }

  // Additional Mathematical Functions
  dynamic _medianFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final numbers = <double>[];
    for (final arg in args) {
      final num = _toNumber(arg);
      if (num != null) numbers.add(num);
    }
    if (numbers.isEmpty) return '#ERROR!';

    numbers.sort();
    final middle = numbers.length ~/ 2;
    if (numbers.length % 2 == 0) {
      return (numbers[middle - 1] + numbers[middle]) / 2;
    } else {
      return numbers[middle];
    }
  }

  dynamic _modeFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final numbers = <double>[];
    for (final arg in args) {
      final num = _toNumber(arg);
      if (num != null) numbers.add(num);
    }
    if (numbers.isEmpty) return '#ERROR!';

    final frequency = <double, int>{};
    for (final num in numbers) {
      frequency[num] = (frequency[num] ?? 0) + 1;
    }

    int maxFreq = 0;
    double? mode;
    for (final entry in frequency.entries) {
      if (entry.value > maxFreq) {
        maxFreq = entry.value;
        mode = entry.key;
      }
    }

    return mode ?? '#N/A';
  }

  dynamic _stdevFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final numbers = <double>[];
    for (final arg in args) {
      final num = _toNumber(arg);
      if (num != null) numbers.add(num);
    }
    if (numbers.length < 2) return '#ERROR!';

    final mean = numbers.reduce((a, b) => a + b) / numbers.length;
    final variance = numbers.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / (numbers.length - 1);
    return math.sqrt(variance);
  }

  dynamic _varFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final numbers = <double>[];
    for (final arg in args) {
      final num = _toNumber(arg);
      if (num != null) numbers.add(num);
    }
    if (numbers.length < 2) return '#ERROR!';

    final mean = numbers.reduce((a, b) => a + b) / numbers.length;
    return numbers.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / (numbers.length - 1);
  }

  dynamic _percentileFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final numbers = <double>[];
    for (final arg in [args[0]]) { // First arg should be array, simplified for now
      final num = _toNumber(arg);
      if (num != null) numbers.add(num);
    }
    final k = _toNumber(args[1]);
    if (numbers.isEmpty || k == null || k < 0 || k > 1) return '#ERROR!';

    numbers.sort();
    final index = k * (numbers.length - 1);
    final lower = index.floor();
    final upper = index.ceil();

    if (lower == upper) {
      return numbers[lower];
    } else {
      return numbers[lower] + (index - lower) * (numbers[upper] - numbers[lower]);
    }
  }

  dynamic _rankFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    if (number == null) return '#ERROR!';

    // Simplified implementation - would need array parsing for full functionality
    return 1; // Placeholder
  }

  // Additional Text Functions
  dynamic _findFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final findText = args[0].toString();
    final withinText = args[1].toString();
    final startNum = args.length > 2 ? (_toNumber(args[2])?.toInt() ?? 1) : 1;

    if (startNum < 1) return '#ERROR!';
    final startIndex = startNum - 1;
    if (startIndex >= withinText.length) return '#VALUE!';

    final index = withinText.indexOf(findText, startIndex);
    return index == -1 ? '#VALUE!' : index + 1;
  }

  dynamic _searchFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final findText = args[0].toString().toLowerCase();
    final withinText = args[1].toString().toLowerCase();
    final startNum = args.length > 2 ? (_toNumber(args[2])?.toInt() ?? 1) : 1;

    if (startNum < 1) return '#ERROR!';
    final startIndex = startNum - 1;
    if (startIndex >= withinText.length) return '#VALUE!';

    final index = withinText.indexOf(findText, startIndex);
    return index == -1 ? '#VALUE!' : index + 1;
  }

  dynamic _replaceFunction(List<dynamic> args) {
    if (args.length < 4) return '#ERROR!';
    final oldText = args[0].toString();
    final startNum = _toNumber(args[1])?.toInt() ?? 1;
    final numChars = _toNumber(args[2])?.toInt() ?? 0;
    final newText = args[3].toString();

    if (startNum < 1 || numChars < 0) return '#ERROR!';
    final startIndex = startNum - 1;

    if (startIndex >= oldText.length) return oldText + newText;
    final endIndex = math.min(startIndex + numChars, oldText.length);

    return oldText.substring(0, startIndex) + newText + oldText.substring(endIndex);
  }

  dynamic _substituteFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    final text = args[0].toString();
    final oldText = args[1].toString();
    final newText = args[2].toString();
    final instanceNum = args.length > 3 ? _toNumber(args[3])?.toInt() : null;

    if (instanceNum != null) {
      if (instanceNum < 1) return '#ERROR!';
      // Replace specific instance
      int count = 0;
      int index = 0;
      while ((index = text.indexOf(oldText, index)) != -1) {
        count++;
        if (count == instanceNum) {
          return text.substring(0, index) + newText + text.substring(index + oldText.length);
        }
        index += oldText.length;
      }
      return text; // Instance not found
    } else {
      // Replace all instances
      return text.replaceAll(oldText, newText);
    }
  }

  dynamic _properFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final text = args[0].toString();
    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  dynamic _exactFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    return args[0].toString() == args[1].toString();
  }

  dynamic _valueFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final text = args[0].toString().trim();
    final number = double.tryParse(text);
    return number ?? '#VALUE!';
  }

  // Additional Date Functions
  dynamic _dateFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    final year = _toNumber(args[0])?.toInt();
    final month = _toNumber(args[1])?.toInt();
    final day = _toNumber(args[2])?.toInt();

    if (year == null || month == null || day == null) return '#ERROR!';
    if (month < 1 || month > 12 || day < 1 || day > 31) return '#ERROR!';

    try {
      final date = DateTime(year, month, day);
      return date.toIso8601String().split('T')[0];
    } catch (e) {
      return '#ERROR!';
    }
  }

  // Missing Mathematical Function Implementations

  /// EXP function implementation
  dynamic _expFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return number != null ? math.exp(number) : '#ERROR!';
  }

  /// LN function implementation
  dynamic _lnFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return (number != null && number > 0) ? math.log(number) : '#ERROR!';
  }

  /// LOG function implementation
  dynamic _logFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    final base = _toNumber(args[1]);
    if (number == null || base == null || number <= 0 || base <= 0 || base == 1) return '#ERROR!';
    return math.log(number) / math.log(base);
  }

  /// LOG10 function implementation
  dynamic _log10Function(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return (number != null && number > 0) ? math.log(number) / math.ln10 : '#ERROR!';
  }

  /// ROUNDUP function implementation
  dynamic _roundUpFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    final digits = _toNumber(args[1])?.toInt();
    if (number == null || digits == null) return '#ERROR!';
    final factor = math.pow(10, digits);
    return (number * factor).ceil() / factor;
  }

  /// ROUNDDOWN function implementation
  dynamic _roundDownFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    final digits = _toNumber(args[1])?.toInt();
    if (number == null || digits == null) return '#ERROR!';
    final factor = math.pow(10, digits);
    return (number * factor).floor() / factor;
  }

  /// CEILING function implementation
  dynamic _ceilingFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    final significance = _toNumber(args[1]);
    if (number == null || significance == null || significance == 0) return '#ERROR!';
    return (number / significance).ceil() * significance;
  }

  /// FLOOR function implementation
  dynamic _floorFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final number = _toNumber(args[0]);
    final significance = _toNumber(args[1]);
    if (number == null || significance == null || significance == 0) return '#ERROR!';
    return (number / significance).floor() * significance;
  }

  /// TRUNC function implementation
  dynamic _truncFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    final digits = args.length > 1 ? _toNumber(args[1])?.toInt() ?? 0 : 0;
    if (number == null) return '#ERROR!';
    final factor = math.pow(10, digits);
    return (number * factor).truncate() / factor;
  }

  /// QUOTIENT function implementation
  dynamic _quotientFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final numerator = _toNumber(args[0]);
    final denominator = _toNumber(args[1]);
    if (numerator == null || denominator == null || denominator == 0) return '#ERROR!';
    return (numerator / denominator).truncate();
  }

  /// GCD function implementation
  dynamic _gcdFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final a = _toNumber(args[0])?.abs().toInt();
    final b = _toNumber(args[1])?.abs().toInt();
    if (a == null || b == null) return '#ERROR!';

    int gcd(int a, int b) {
      while (b != 0) {
        int temp = b;
        b = a % b;
        a = temp;
      }
      return a;
    }

    return gcd(a, b);
  }

  /// LCM function implementation
  dynamic _lcmFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final a = _toNumber(args[0])?.abs().toInt();
    final b = _toNumber(args[1])?.abs().toInt();
    if (a == null || b == null) return '#ERROR!';
    final gcdValue = _gcdFunction([a, b]);
    return (a * b) / gcdValue;
  }

  /// FACT function implementation
  dynamic _factFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0])?.toInt();
    if (number == null || number < 0) return '#ERROR!';
    if (number <= 1) return 1;
    int result = 1;
    for (int i = 2; i <= number; i++) {
      result *= i;
    }
    return result;
  }

  /// COMBIN function implementation
  dynamic _combinFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final n = _toNumber(args[0])?.toInt();
    final k = _toNumber(args[1])?.toInt();
    if (n == null || k == null || k > n || k < 0 || n < 0) return '#ERROR!';
    if (k == 0 || k == n) return 1;

    // Use more efficient calculation to avoid large factorials
    int result = 1;
    for (int i = 0; i < k; i++) {
      result = result * (n - i) ~/ (i + 1);
    }
    return result;
  }

  /// PERMUT function implementation
  dynamic _permutFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final n = _toNumber(args[0])?.toInt();
    final k = _toNumber(args[1])?.toInt();
    if (n == null || k == null || k > n || k < 0 || n < 0) return '#ERROR!';

    int result = 1;
    for (int i = 0; i < k; i++) {
      result *= (n - i);
    }
    return result;
  }

  // Trigonometric Function Implementations

  /// SIN function implementation
  dynamic _sinFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return number != null ? math.sin(number) : '#ERROR!';
  }

  /// COS function implementation
  dynamic _cosFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return number != null ? math.cos(number) : '#ERROR!';
  }

  /// TAN function implementation
  dynamic _tanFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return number != null ? math.tan(number) : '#ERROR!';
  }

  /// ASIN function implementation
  dynamic _asinFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return (number != null && number >= -1 && number <= 1) ? math.asin(number) : '#ERROR!';
  }

  /// ACOS function implementation
  dynamic _acosFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return (number != null && number >= -1 && number <= 1) ? math.acos(number) : '#ERROR!';
  }

  /// ATAN function implementation
  dynamic _atanFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    return number != null ? math.atan(number) : '#ERROR!';
  }

  /// ATAN2 function implementation
  dynamic _atan2Function(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final y = _toNumber(args[0]);
    final x = _toNumber(args[1]);
    return (y != null && x != null) ? math.atan2(y, x) : '#ERROR!';
  }

  /// SINH function implementation
  dynamic _sinhFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    if (number == null) return '#ERROR!';
    return (math.exp(number) - math.exp(-number)) / 2;
  }

  /// COSH function implementation
  dynamic _coshFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    if (number == null) return '#ERROR!';
    return (math.exp(number) + math.exp(-number)) / 2;
  }

  /// TANH function implementation
  dynamic _tanhFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final number = _toNumber(args[0]);
    if (number == null) return '#ERROR!';
    final exp2x = math.exp(2 * number);
    return (exp2x - 1) / (exp2x + 1);
  }

  /// DEGREES function implementation
  dynamic _degreesFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final radians = _toNumber(args[0]);
    return radians != null ? radians * 180 / math.pi : '#ERROR!';
  }

  /// RADIANS function implementation
  dynamic _radiansFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    final degrees = _toNumber(args[0]);
    return degrees != null ? degrees * math.pi / 180 : '#ERROR!';
  }

  /// PI function implementation
  dynamic _piFunction(List<dynamic> args) {
    return math.pi;
  }

  /// Helper function to get numeric value with better error handling
  double _getNumericValue(dynamic value) {
    if (value is num) return value.toDouble();
    if (value is String) {
      final parsed = double.tryParse(value);
      if (parsed != null) return parsed;
    }
    return 0.0;
  }

  dynamic _timeFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    final hour = _toNumber(args[0])?.toInt();
    final minute = _toNumber(args[1])?.toInt();
    final second = _toNumber(args[2])?.toInt();

    if (hour == null || minute == null || second == null) return '#ERROR!';
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59) return '#ERROR!';

    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
  }

  dynamic _weekdayFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    // Simplified - would need proper date parsing
    final now = DateTime.now();
    final returnType = args.length > 1 ? _toNumber(args[1])?.toInt() ?? 1 : 1;

    int weekday = now.weekday;
    switch (returnType) {
      case 1: // Sunday = 1, Monday = 2, etc.
        return weekday == 7 ? 1 : weekday + 1;
      case 2: // Monday = 1, Tuesday = 2, etc.
        return weekday;
      case 3: // Monday = 0, Tuesday = 1, etc.
        return weekday - 1;
      default:
        return '#ERROR!';
    }
  }

  dynamic _datedifFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    // Simplified implementation - would need proper date parsing
    return 30; // Placeholder
  }

  dynamic _networkdaysFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    // Simplified implementation - would need proper date parsing
    return 22; // Placeholder
  }

  // Information Functions
  dynamic _isblankFunction(List<dynamic> args) {
    if (args.isEmpty) return true;
    final value = args[0];
    return value == null || value.toString().isEmpty;
  }

  dynamic _isnumberFunction(List<dynamic> args) {
    if (args.isEmpty) return false;
    return _toNumber(args[0]) != null;
  }

  dynamic _istextFunction(List<dynamic> args) {
    if (args.isEmpty) return false;
    return args[0] is String && _toNumber(args[0]) == null;
  }

  dynamic _iserrorFunction(List<dynamic> args) {
    if (args.isEmpty) return false;
    final value = args[0];
    return value is String && value.startsWith('#') && value.endsWith('!');
  }

  dynamic _typeFunction(List<dynamic> args) {
    if (args.isEmpty) return 1;
    final value = args[0];
    if (value is num) return 1;
    if (value is String) return 2;
    if (value is bool) return 4;
    return 16; // Error
  }

  // Conditional Functions
  dynamic _sumifFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    // Simplified implementation - would need range parsing
    return 0; // Placeholder
  }

  dynamic _countifFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    // Simplified implementation - would need range parsing
    return 0; // Placeholder
  }

  dynamic _averageifFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    // Simplified implementation - would need range parsing
    return 0; // Placeholder
  }

  // Financial Functions
  dynamic _pvFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    final rate = _toNumber(args[0]);
    final nper = _toNumber(args[1]);
    final pmt = _toNumber(args[2]);
    final fv = args.length > 3 ? _toNumber(args[3]) ?? 0 : 0;
    final type = args.length > 4 ? _toNumber(args[4])?.toInt() ?? 0 : 0;

    if (rate == null || nper == null || pmt == null) return '#ERROR!';

    if (rate == 0) {
      return -(pmt * nper + fv);
    }

    final factor = math.pow(1 + rate, nper);
    final pv = -(pmt * (factor - 1) / rate + fv) / factor;

    return type == 1 ? pv / (1 + rate) : pv;
  }

  dynamic _fvFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    final rate = _toNumber(args[0]);
    final nper = _toNumber(args[1]);
    final pmt = _toNumber(args[2]);
    final pv = args.length > 3 ? _toNumber(args[3]) ?? 0 : 0;
    final type = args.length > 4 ? _toNumber(args[4])?.toInt() ?? 0 : 0;

    if (rate == null || nper == null || pmt == null) return '#ERROR!';

    if (rate == 0) {
      return -(pv + pmt * nper);
    }

    final factor = math.pow(1 + rate, nper);
    final fv = -(pv * factor + pmt * (factor - 1) / rate);

    return type == 1 ? fv * (1 + rate) : fv;
  }

  dynamic _pmtFunction(List<dynamic> args) {
    if (args.length < 3) return '#ERROR!';
    final rate = _toNumber(args[0]);
    final nper = _toNumber(args[1]);
    final pv = _toNumber(args[2]);
    final fv = args.length > 3 ? _toNumber(args[3]) ?? 0 : 0;
    final type = args.length > 4 ? _toNumber(args[4])?.toInt() ?? 0 : 0;

    if (rate == null || nper == null || pv == null) return '#ERROR!';

    if (rate == 0) {
      return -(pv + fv) / nper;
    }

    final factor = math.pow(1 + rate, nper);
    final pmt = -(pv * factor + fv) * rate / (factor - 1);

    return type == 1 ? pmt / (1 + rate) : pmt;
  }

  dynamic _npvFunction(List<dynamic> args) {
    if (args.length < 2) return '#ERROR!';
    final rate = _toNumber(args[0]);
    if (rate == null) return '#ERROR!';

    double npv = 0;
    for (int i = 1; i < args.length; i++) {
      final value = _toNumber(args[i]);
      if (value != null) {
        npv += value / math.pow(1 + rate, i);
      }
    }

    return npv;
  }

  dynamic _irrFunction(List<dynamic> args) {
    if (args.isEmpty) return '#ERROR!';
    // Simplified implementation - IRR calculation is complex
    return 0.1; // Placeholder 10%
  }


}

/// Auto-complete suggestion class
class FormulaAutoComplete {
  final String name;
  final String description;
  final List<String> parameters;
  final String category;
  final String insertText;

  const FormulaAutoComplete({
    required this.name,
    required this.description,
    required this.parameters,
    required this.category,
    required this.insertText,
  });
}



/// Formula validation result
class FormulaValidationResult {
  final bool isValid;
  final List<String> errors;

  const FormulaValidationResult({
    required this.isValid,
    required this.errors,
  });
}
