import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class PermissionsService {
  static final PermissionsService _instance = PermissionsService._internal();
  factory PermissionsService() => _instance;
  PermissionsService._internal();

  static PermissionsService get instance => _instance;

  // Cache permission status to avoid repeated checks
  final Map<Permission, PermissionStatus> _permissionCache = {};
  
  /// Check if a specific permission is granted
  Future<bool> isPermissionGranted(Permission permission) async {
    try {
      final status = await permission.status;
      _permissionCache[permission] = status;
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('Error checking permission ${permission.toString()}: $e');
      return false;
    }
  }

  /// Request a specific permission
  Future<PermissionStatus> requestPermission(Permission permission) async {
    try {
      final status = await permission.request();
      _permissionCache[permission] = status;
      debugPrint('Permission ${permission.toString()} status: $status');
      return status;
    } catch (e) {
      debugPrint('Error requesting permission ${permission.toString()}: $e');
      return PermissionStatus.denied;
    }
  }

  /// Request multiple permissions at once
  Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(
    List<Permission> permissions,
  ) async {
    try {
      final statuses = await permissions.request();
      _permissionCache.addAll(statuses);
      return statuses;
    } catch (e) {
      debugPrint('Error requesting multiple permissions: $e');
      return {};
    }
  }

  /// Check microphone permission
  Future<bool> hasMicrophonePermission() async {
    return await isPermissionGranted(Permission.microphone);
  }

  /// Request microphone permission
  Future<PermissionStatus> requestMicrophonePermission() async {
    return await requestPermission(Permission.microphone);
  }

  /// Check storage permission
  Future<bool> hasStoragePermission() async {
    // For Android 13+ (API 33+), we need to check different permissions
    if (defaultTargetPlatform == TargetPlatform.android) {
      // Check if we're on Android 13+ and use appropriate permissions
      try {
        final hasPhotos = await isPermissionGranted(Permission.photos);
        final hasVideos = await isPermissionGranted(Permission.videos);
        final hasAudio = await isPermissionGranted(Permission.audio);
        
        // If any of the new permissions are granted, we have storage access
        if (hasPhotos || hasVideos || hasAudio) {
          return true;
        }
        
        // Fallback to legacy storage permission
        return await isPermissionGranted(Permission.storage);
      } catch (e) {
        // Fallback to legacy storage permission
        return await isPermissionGranted(Permission.storage);
      }
    } else {
      // For iOS, use photos permission
      return await isPermissionGranted(Permission.photos);
    }
  }

  /// Request storage permission
  Future<Map<Permission, PermissionStatus>> requestStoragePermission() async {
    if (defaultTargetPlatform == TargetPlatform.android) {
      try {
        // Request granular permissions for Android 13+
        return await requestMultiplePermissions([
          Permission.photos,
          Permission.videos,
          Permission.audio,
        ]);
      } catch (e) {
        // Fallback to legacy storage permission
        final status = await requestPermission(Permission.storage);
        return {Permission.storage: status};
      }
    } else {
      // For iOS, request photos permission
      final status = await requestPermission(Permission.photos);
      return {Permission.photos: status};
    }
  }

  /// Check notification permission
  Future<bool> hasNotificationPermission() async {
    return await isPermissionGranted(Permission.notification);
  }

  /// Request notification permission
  Future<PermissionStatus> requestNotificationPermission() async {
    return await requestPermission(Permission.notification);
  }

  /// Check camera permission
  Future<bool> hasCameraPermission() async {
    return await isPermissionGranted(Permission.camera);
  }

  /// Request camera permission
  Future<PermissionStatus> requestCameraPermission() async {
    return await requestPermission(Permission.camera);
  }

  /// Check location permission
  Future<bool> hasLocationPermission() async {
    final whenInUse = await isPermissionGranted(Permission.locationWhenInUse);
    final always = await isPermissionGranted(Permission.locationAlways);
    return whenInUse || always;
  }

  /// Request location permission
  Future<PermissionStatus> requestLocationPermission() async {
    return await requestPermission(Permission.locationWhenInUse);
  }

  /// Check contacts permission
  Future<bool> hasContactsPermission() async {
    return await isPermissionGranted(Permission.contacts);
  }

  /// Request contacts permission
  Future<PermissionStatus> requestContactsPermission() async {
    return await requestPermission(Permission.contacts);
  }

  /// Check calendar permission
  Future<bool> hasCalendarPermission() async {
    return await isPermissionGranted(Permission.calendar);
  }

  /// Request calendar permission
  Future<PermissionStatus> requestCalendarPermission() async {
    return await requestPermission(Permission.calendar);
  }

  /// Get all permission statuses
  Future<Map<String, PermissionStatus>> getAllPermissionStatuses() async {
    final permissions = [
      Permission.microphone,
      Permission.camera,
      Permission.notification,
      Permission.locationWhenInUse,
      Permission.contacts,
      Permission.calendar,
    ];

    // Add storage permissions based on platform
    if (defaultTargetPlatform == TargetPlatform.android) {
      permissions.addAll([
        Permission.storage,
        Permission.photos,
        Permission.videos,
        Permission.audio,
      ]);
    } else {
      permissions.add(Permission.photos);
    }

    final Map<String, PermissionStatus> statuses = {};
    
    for (final permission in permissions) {
      try {
        final status = await permission.status;
        statuses[permission.toString()] = status;
      } catch (e) {
        debugPrint('Error getting status for ${permission.toString()}: $e');
        statuses[permission.toString()] = PermissionStatus.denied;
      }
    }

    return statuses;
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  /// Check if permission is permanently denied
  bool isPermissionPermanentlyDenied(Permission permission) {
    final status = _permissionCache[permission];
    return status == PermissionStatus.permanentlyDenied;
  }

  /// Get user-friendly permission name
  String getPermissionDisplayName(Permission permission) {
    switch (permission) {
      case Permission.microphone:
        return 'Microphone';
      case Permission.camera:
        return 'Camera';
      case Permission.storage:
        return 'Storage';
      case Permission.photos:
        return 'Photos';
      case Permission.videos:
        return 'Videos';
      case Permission.audio:
        return 'Audio Files';
      case Permission.notification:
        return 'Notifications';
      case Permission.locationWhenInUse:
        return 'Location';
      case Permission.locationAlways:
        return 'Location (Always)';
      case Permission.contacts:
        return 'Contacts';
      case Permission.calendar:
        return 'Calendar';
      default:
        return permission.toString().split('.').last;
    }
  }

  /// Get permission description
  String getPermissionDescription(Permission permission) {
    switch (permission) {
      case Permission.microphone:
        return 'Required for recording voice memos and audio notes';
      case Permission.camera:
        return 'Required for taking photos and scanning documents';
      case Permission.storage:
      case Permission.photos:
      case Permission.videos:
      case Permission.audio:
        return 'Required for saving and accessing files';
      case Permission.notification:
        return 'Required for reminders and important alerts';
      case Permission.locationWhenInUse:
      case Permission.locationAlways:
        return 'Required for location-based features and prayer times';
      case Permission.contacts:
        return 'Required for contact management features';
      case Permission.calendar:
        return 'Required for calendar integration and scheduling';
      default:
        return 'Required for app functionality';
    }
  }

  /// Request all essential permissions for the app
  Future<Map<Permission, PermissionStatus>> requestEssentialPermissions() async {
    final essentialPermissions = [
      Permission.microphone,
      Permission.notification,
    ];

    // Add storage permissions based on platform
    if (defaultTargetPlatform == TargetPlatform.android) {
      essentialPermissions.addAll([
        Permission.photos,
        Permission.audio,
      ]);
    } else {
      essentialPermissions.add(Permission.photos);
    }

    return await requestMultiplePermissions(essentialPermissions);
  }

  /// Check if all essential permissions are granted
  Future<bool> hasAllEssentialPermissions() async {
    final hasMicrophone = await hasMicrophonePermission();
    final hasStorage = await hasStoragePermission();
    final hasNotifications = await hasNotificationPermission();

    return hasMicrophone && hasStorage && hasNotifications;
  }

  /// Clear permission cache
  void clearCache() {
    _permissionCache.clear();
  }

  /// Handle permission denial with user-friendly message
  String getPermissionDenialMessage(Permission permission) {
    final name = getPermissionDisplayName(permission);
    final description = getPermissionDescription(permission);
    
    if (isPermissionPermanentlyDenied(permission)) {
      return '$name permission is permanently denied. $description\n\nPlease enable it in Settings > Apps > ShadowSuite > Permissions.';
    } else {
      return '$name permission is required. $description\n\nPlease grant permission to continue.';
    }
  }

  /// Show permission rationale
  String getPermissionRationale(Permission permission) {
    final name = getPermissionDisplayName(permission);
    final description = getPermissionDescription(permission);
    
    return 'ShadowSuite needs $name access to provide you with the best experience.\n\n$description\n\nYour privacy is important to us - we only use permissions for their intended features.';
  }
}
