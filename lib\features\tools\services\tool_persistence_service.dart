import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/custom_tool.dart';
import '../models/spreadsheet_cell.dart';

/// Service for persisting tool data and ensuring data integrity
class ToolPersistenceService {
  static final ToolPersistenceService _instance = ToolPersistenceService._internal();
  factory ToolPersistenceService() => _instance;
  ToolPersistenceService._internal();

  static const String _toolsKey = 'custom_tools';
  static const String _backendDataPrefix = 'tool_backend_';
  static const String _uiDataPrefix = 'tool_ui_';
  static const String _settingsPrefix = 'tool_settings_';

  /// Save a custom tool with all its data
  Future<bool> saveTool(CustomTool tool) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save tool metadata
      final tools = await getAllTools();
      final existingIndex = tools.indexWhere((t) => t.id == tool.id);
      
      if (existingIndex >= 0) {
        tools[existingIndex] = tool;
      } else {
        tools.add(tool);
      }
      
      // Save tools list
      final toolsJson = tools.map((t) => t.toJson()).toList();
      await prefs.setString(_toolsKey, jsonEncode(toolsJson));
      
      // Save to file system for backup
      await _saveToolToFile(tool);
      
      return true;
    } catch (e) {
      print('Error saving tool: $e');
      return false;
    }
  }

  /// Load a specific tool by ID
  Future<CustomTool?> loadTool(String toolId) async {
    try {
      final tools = await getAllTools();
      return tools.firstWhere(
        (tool) => tool.id == toolId,
        orElse: () => throw StateError('Tool not found'),
      );
    } catch (e) {
      print('Error loading tool: $e');
      return null;
    }
  }

  /// Get all tools
  Future<List<CustomTool>> getAllTools() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final toolsJson = prefs.getString(_toolsKey);
      
      if (toolsJson == null) return [];
      
      final toolsList = jsonDecode(toolsJson) as List<dynamic>;
      return toolsList
          .map((json) => CustomTool.fromJson(json as Map<String, dynamic>))
          .where((tool) => tool.isActive)
          .toList();
    } catch (e) {
      print('Error loading tools: $e');
      return [];
    }
  }

  /// Save backend spreadsheet data for a tool
  Future<bool> saveBackendData(String toolId, Map<String, SpreadsheetCell> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = data.map((key, cell) => MapEntry(key, cell.toJson()));
      await prefs.setString('$_backendDataPrefix$toolId', jsonEncode(dataJson));
      
      // Also save to file for backup
      await _saveBackendDataToFile(toolId, data);
      
      return true;
    } catch (e) {
      print('Error saving backend data: $e');
      return false;
    }
  }

  /// Load backend spreadsheet data for a tool
  Future<Map<String, SpreadsheetCell>> loadBackendData(String toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = prefs.getString('$_backendDataPrefix$toolId');
      
      if (dataJson == null) return {};
      
      final dataMap = jsonDecode(dataJson) as Map<String, dynamic>;
      return dataMap.map((key, value) => 
          MapEntry(key, SpreadsheetCell.fromJson(value as Map<String, dynamic>)));
    } catch (e) {
      print('Error loading backend data: $e');
      return {};
    }
  }

  /// Save UI component data for a tool
  Future<bool> saveUIData(String toolId, List<ToolComponent> components) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final componentsJson = components.map((c) => c.toJson()).toList();
      await prefs.setString('$_uiDataPrefix$toolId', jsonEncode(componentsJson));
      
      return true;
    } catch (e) {
      print('Error saving UI data: $e');
      return false;
    }
  }

  /// Load UI component data for a tool
  Future<List<ToolComponent>> loadUIData(String toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = prefs.getString('$_uiDataPrefix$toolId');
      
      if (dataJson == null) return [];
      
      final componentsList = jsonDecode(dataJson) as List<dynamic>;
      return componentsList
          .map((json) => ToolComponent.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error loading UI data: $e');
      return [];
    }
  }

  /// Save tool settings
  Future<bool> saveToolSettings(String toolId, Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('$_settingsPrefix$toolId', jsonEncode(settings));
      return true;
    } catch (e) {
      print('Error saving tool settings: $e');
      return false;
    }
  }

  /// Load tool settings
  Future<Map<String, dynamic>> loadToolSettings(String toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('$_settingsPrefix$toolId');
      
      if (settingsJson == null) return {};
      
      return Map<String, dynamic>.from(jsonDecode(settingsJson));
    } catch (e) {
      print('Error loading tool settings: $e');
      return {};
    }
  }

  /// Delete a tool and all its data
  Future<bool> deleteTool(String toolId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Soft delete the tool
      final tool = await loadTool(toolId);
      if (tool != null) {
        final updatedTool = tool.copyWith(isActive: false);
        await saveTool(updatedTool);
      }
      
      // Remove associated data
      await prefs.remove('$_backendDataPrefix$toolId');
      await prefs.remove('$_uiDataPrefix$toolId');
      await prefs.remove('$_settingsPrefix$toolId');
      
      // Remove file backups
      await _deleteToolFiles(toolId);
      
      return true;
    } catch (e) {
      print('Error deleting tool: $e');
      return false;
    }
  }

  /// Export tool data to JSON
  Future<Map<String, dynamic>> exportTool(String toolId) async {
    try {
      final tool = await loadTool(toolId);
      if (tool == null) throw Exception('Tool not found');
      
      final backendData = await loadBackendData(toolId);
      final uiData = await loadUIData(toolId);
      final settings = await loadToolSettings(toolId);
      
      return {
        'tool': tool.toJson(),
        'backendData': backendData.map((key, cell) => MapEntry(key, cell.toJson())),
        'uiData': uiData.map((c) => c.toJson()).toList(),
        'settings': settings,
        'exportedAt': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
    } catch (e) {
      print('Error exporting tool: $e');
      rethrow;
    }
  }

  /// Import tool data from JSON
  Future<bool> importTool(Map<String, dynamic> toolData) async {
    try {
      final tool = CustomTool.fromJson(toolData['tool'] as Map<String, dynamic>);
      
      // Save tool
      await saveTool(tool);
      
      // Import backend data
      if (toolData['backendData'] != null) {
        final backendMap = toolData['backendData'] as Map<String, dynamic>;
        final backendData = backendMap.map((key, value) => 
            MapEntry(key, SpreadsheetCell.fromJson(value as Map<String, dynamic>)));
        await saveBackendData(tool.id, backendData);
      }
      
      // Import UI data
      if (toolData['uiData'] != null) {
        final uiList = toolData['uiData'] as List<dynamic>;
        final uiData = uiList
            .map((json) => ToolComponent.fromJson(json as Map<String, dynamic>))
            .toList();
        await saveUIData(tool.id, uiData);
      }
      
      // Import settings
      if (toolData['settings'] != null) {
        await saveToolSettings(tool.id, toolData['settings'] as Map<String, dynamic>);
      }
      
      return true;
    } catch (e) {
      print('Error importing tool: $e');
      return false;
    }
  }

  /// Backup all tools to file system
  Future<bool> backupAllTools() async {
    try {
      final tools = await getAllTools();
      final directory = await _getBackupDirectory();
      
      for (final tool in tools) {
        final toolData = await exportTool(tool.id);
        final file = File('${directory.path}/tool_${tool.id}_backup.json');
        await file.writeAsString(jsonEncode(toolData));
      }
      
      return true;
    } catch (e) {
      print('Error backing up tools: $e');
      return false;
    }
  }

  /// Restore tools from backup
  Future<bool> restoreFromBackup() async {
    try {
      final directory = await _getBackupDirectory();
      final files = directory.listSync().where((f) => f.path.endsWith('_backup.json'));
      
      for (final file in files) {
        if (file is File) {
          final content = await file.readAsString();
          final toolData = jsonDecode(content) as Map<String, dynamic>;
          await importTool(toolData);
        }
      }
      
      return true;
    } catch (e) {
      print('Error restoring from backup: $e');
      return false;
    }
  }

  /// Clear all tool data (for testing/reset)
  Future<bool> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => 
          key.startsWith(_toolsKey) ||
          key.startsWith(_backendDataPrefix) ||
          key.startsWith(_uiDataPrefix) ||
          key.startsWith(_settingsPrefix));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      // Clear file backups
      final directory = await _getBackupDirectory();
      if (await directory.exists()) {
        await directory.delete(recursive: true);
      }
      
      return true;
    } catch (e) {
      print('Error clearing data: $e');
      return false;
    }
  }

  /// Get backup directory
  Future<Directory> _getBackupDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/tool_backups');
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }
    return backupDir;
  }

  /// Save tool to file system
  Future<void> _saveToolToFile(CustomTool tool) async {
    try {
      final directory = await _getBackupDirectory();
      final file = File('${directory.path}/tool_${tool.id}.json');
      await file.writeAsString(jsonEncode(tool.toJson()));
    } catch (e) {
      print('Error saving tool to file: $e');
    }
  }

  /// Save backend data to file system
  Future<void> _saveBackendDataToFile(String toolId, Map<String, SpreadsheetCell> data) async {
    try {
      final directory = await _getBackupDirectory();
      final file = File('${directory.path}/backend_${toolId}.json');
      final dataJson = data.map((key, cell) => MapEntry(key, cell.toJson()));
      await file.writeAsString(jsonEncode(dataJson));
    } catch (e) {
      print('Error saving backend data to file: $e');
    }
  }

  /// Delete tool files
  Future<void> _deleteToolFiles(String toolId) async {
    try {
      final directory = await _getBackupDirectory();
      final toolFile = File('${directory.path}/tool_$toolId.json');
      final backendFile = File('${directory.path}/backend_$toolId.json');
      final backupFile = File('${directory.path}/tool_${toolId}_backup.json');
      
      if (await toolFile.exists()) await toolFile.delete();
      if (await backendFile.exists()) await backendFile.delete();
      if (await backupFile.exists()) await backupFile.delete();
    } catch (e) {
      print('Error deleting tool files: $e');
    }
  }
}
