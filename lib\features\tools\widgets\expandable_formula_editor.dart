import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/formula_assistance_provider.dart';

/// Expandable formula editor with syntax highlighting and assistance
class ExpandableFormulaEditor extends ConsumerStatefulWidget {
  final String? initialFormula;
  final Function(String formula) onFormulaChanged;
  final Function(String formula) onFormulaSubmitted;
  final bool isExpanded;
  final Function(bool expanded) onExpandedChanged;

  const ExpandableFormulaEditor({
    super.key,
    this.initialFormula,
    required this.onFormulaChanged,
    required this.onFormulaSubmitted,
    this.isExpanded = false,
    required this.onExpandedChanged,
  });

  @override
  ConsumerState<ExpandableFormulaEditor> createState() => _ExpandableFormulaEditorState();
}

class _ExpandableFormulaEditorState extends ConsumerState<ExpandableFormulaEditor> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _showSuggestions = false;
  List<FunctionInfo> _suggestions = [];
  String _naturalLanguageQuery = '';

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialFormula ?? '');
    _focusNode = FocusNode();
    
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    widget.onFormulaChanged(_controller.text);
    _updateSuggestions();
  }

  void _updateSuggestions() {
    final text = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;
    
    if (text.startsWith('=') && cursorPosition > 0) {
      final beforeCursor = text.substring(0, cursorPosition);
      final lastWord = _getLastWord(beforeCursor);
      
      if (lastWord.isNotEmpty) {
        final assistanceNotifier = ref.read(formulaAssistanceProvider.notifier);
        _suggestions = assistanceNotifier.getFunctionSuggestions(lastWord);
        _showSuggestions = _suggestions.isNotEmpty;
      } else {
        _showSuggestions = false;
      }
    } else {
      _showSuggestions = false;
    }
    
    setState(() {});
  }

  String _getLastWord(String text) {
    final match = RegExp(r'[A-Za-z_][A-Za-z0-9_]*$').firstMatch(text);
    return match?.group(0) ?? '';
  }

  void _insertFunction(FunctionInfo function) {
    final text = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;
    final beforeCursor = text.substring(0, cursorPosition);
    final afterCursor = text.substring(cursorPosition);
    
    final lastWordMatch = RegExp(r'[A-Za-z_][A-Za-z0-9_]*$').firstMatch(beforeCursor);
    if (lastWordMatch != null) {
      final newText = beforeCursor.substring(0, lastWordMatch.start) + 
                     function.name + '(' + afterCursor;
      _controller.text = newText;
      _controller.selection = TextSelection.collapsed(
        offset: lastWordMatch.start + function.name.length + 1,
      );
    }
    
    // Add to recently used
    final assistanceNotifier = ref.read(formulaAssistanceProvider.notifier);
    assistanceNotifier.addToRecentlyUsed(function.name);
    
    setState(() {
      _showSuggestions = false;
    });
  }

  void _insertTemplate(FormulaTemplate template) {
    _controller.text = template.formula;
    _controller.selection = TextSelection.collapsed(offset: template.formula.length);
    widget.onFormulaChanged(template.formula);
  }

  void _convertNaturalLanguage() {
    if (_naturalLanguageQuery.isEmpty) return;
    
    final assistanceNotifier = ref.read(formulaAssistanceProvider.notifier);
    final formula = assistanceNotifier.convertNaturalLanguage(_naturalLanguageQuery);
    
    if (formula != null) {
      _controller.text = formula;
      widget.onFormulaChanged(formula);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header with expand/collapse button
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.functions,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Formula Editor',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(widget.isExpanded ? Icons.expand_less : Icons.expand_more),
                  onPressed: () => widget.onExpandedChanged(!widget.isExpanded),
                ),
              ],
            ),
          ),
          
          // Formula input field
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              maxLines: widget.isExpanded ? 5 : 1,
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              decoration: InputDecoration(
                labelText: 'Formula',
                hintText: 'Enter formula (e.g., =SUM(A1:A10)) or describe what you want to calculate',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.calculate),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_controller.text.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _controller.clear();
                          widget.onFormulaChanged('');
                        },
                      ),
                    IconButton(
                      icon: const Icon(Icons.check),
                      onPressed: () => widget.onFormulaSubmitted(_controller.text),
                    ),
                  ],
                ),
              ),
              onSubmitted: widget.onFormulaSubmitted,
            ),
          ),
          
          // Natural language input (when expanded)
          if (widget.isExpanded) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                decoration: InputDecoration(
                  labelText: 'Describe what you want to calculate',
                  hintText: 'e.g., "sum all values in column A" or "calculate loan payment"',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.chat),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.auto_fix_high),
                    onPressed: _convertNaturalLanguage,
                  ),
                ),
                onChanged: (value) => _naturalLanguageQuery = value,
                onSubmitted: (_) => _convertNaturalLanguage(),
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Function suggestions
          if (_showSuggestions && _suggestions.isNotEmpty)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      'Function Suggestions',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 120,
                    child: ListView.builder(
                      itemCount: _suggestions.length,
                      itemBuilder: (context, index) {
                        final function = _suggestions[index];
                        return ListTile(
                          dense: true,
                          leading: CircleAvatar(
                            radius: 12,
                            backgroundColor: _getCategoryColor(function.category),
                            child: Text(
                              function.name[0],
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          title: Text(
                            function.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                function.syntax,
                                style: TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 11,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                              Text(
                                function.description,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                          onTap: () => _insertFunction(function),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          
          // Formula templates (when expanded)
          if (widget.isExpanded) ...[
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Formula Templates',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 100,
              child: Consumer(
                builder: (context, ref, child) {
                  final assistanceState = ref.watch(formulaAssistanceProvider);
                  return ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: assistanceState.templates.length,
                    itemBuilder: (context, index) {
                      final template = assistanceState.templates[index];
                      return Container(
                        width: 200,
                        margin: const EdgeInsets.only(right: 8),
                        child: Card(
                          child: InkWell(
                            onTap: () => _insertTemplate(template),
                            borderRadius: BorderRadius.circular(8),
                            child: Padding(
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    template.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    template.formula,
                                    style: TextStyle(
                                      fontFamily: 'monospace',
                                      fontSize: 10,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Expanded(
                                    child: Text(
                                      template.description,
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  Color _getCategoryColor(FunctionCategory category) {
    switch (category) {
      case FunctionCategory.math:
        return Colors.blue;
      case FunctionCategory.logical:
        return Colors.green;
      case FunctionCategory.lookup:
        return Colors.orange;
      case FunctionCategory.text:
        return Colors.purple;
      case FunctionCategory.dateTime:
        return Colors.teal;
      case FunctionCategory.statistical:
        return Colors.red;
      case FunctionCategory.financial:
        return Colors.indigo;
    }
  }
}
