import 'package:flutter/material.dart' hide Orientation;
import 'dart:math';

enum DeviceType {
  mobile,
  tablet,
  desktop,
  tv,
}

enum ScreenSize {
  xs,  // Extra small: < 576px
  sm,  // Small: 576px - 768px
  md,  // Medium: 768px - 992px
  lg,  // Large: 992px - 1200px
  xl,  // Extra large: 1200px - 1400px
  xxl, // Extra extra large: > 1400px
}

enum Orientation {
  portrait,
  landscape,
}

class Breakpoints {
  static const double xs = 576;
  static const double sm = 768;
  static const double md = 992;
  static const double lg = 1200;
  static const double xl = 1400;

  static ScreenSize getScreenSize(double width) {
    if (width < xs) return ScreenSize.xs;
    if (width < sm) return ScreenSize.sm;
    if (width < md) return ScreenSize.md;
    if (width < lg) return ScreenSize.lg;
    if (width < xl) return ScreenSize.xl;
    return ScreenSize.xxl;
  }

  static DeviceType getDeviceType(double width, double height) {
    final diagonal = _calculateDiagonal(width, height);
    
    if (diagonal < 7) return DeviceType.mobile;
    if (diagonal < 12) return DeviceType.tablet;
    if (diagonal < 32) return DeviceType.desktop;
    return DeviceType.tv;
  }

  static double _calculateDiagonal(double width, double height) {
    // Simplified diagonal calculation (in inches, assuming 160 DPI)
    final widthInches = width / 160;
    final heightInches = height / 160;
    return sqrt(widthInches * widthInches + heightInches * heightInches);
  }

  static bool isMobile(double width) => getDeviceType(width, 0) == DeviceType.mobile;
  static bool isTablet(double width) => getDeviceType(width, 0) == DeviceType.tablet;
  static bool isDesktop(double width) => getDeviceType(width, 0) == DeviceType.desktop;
  static bool isTV(double width) => getDeviceType(width, 0) == DeviceType.tv;
}

class ResponsiveConfig {
  final DeviceType deviceType;
  final ScreenSize screenSize;
  final Orientation orientation;
  final double width;
  final double height;
  final double aspectRatio;
  final bool isLandscape;
  final bool isPortrait;

  ResponsiveConfig({
    required this.deviceType,
    required this.screenSize,
    required this.orientation,
    required this.width,
    required this.height,
  }) : aspectRatio = width / height,
       isLandscape = orientation == Orientation.landscape,
       isPortrait = orientation == Orientation.portrait;

  factory ResponsiveConfig.fromSize(Size size) {
    final width = size.width;
    final height = size.height;
    final deviceType = Breakpoints.getDeviceType(width, height);
    final screenSize = Breakpoints.getScreenSize(width);
    final orientation = width > height ? Orientation.landscape : Orientation.portrait;

    return ResponsiveConfig(
      deviceType: deviceType,
      screenSize: screenSize,
      orientation: orientation,
      width: width,
      height: height,
    );
  }

  // Layout helpers
  int getColumns() {
    switch (screenSize) {
      case ScreenSize.xs:
        return 1;
      case ScreenSize.sm:
        return 2;
      case ScreenSize.md:
        return 3;
      case ScreenSize.lg:
        return 4;
      case ScreenSize.xl:
        return 5;
      case ScreenSize.xxl:
        return 6;
    }
  }

  double getContentWidth() {
    switch (screenSize) {
      case ScreenSize.xs:
        return width * 0.95;
      case ScreenSize.sm:
        return width * 0.9;
      case ScreenSize.md:
        return width * 0.85;
      case ScreenSize.lg:
        return width * 0.8;
      case ScreenSize.xl:
        return 1200;
      case ScreenSize.xxl:
        return 1400;
    }
  }

  EdgeInsets getPagePadding() {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16);
      case DeviceType.tablet:
        return const EdgeInsets.all(24);
      case DeviceType.desktop:
        return const EdgeInsets.all(32);
      case DeviceType.tv:
        return const EdgeInsets.all(48);
    }
  }

  double getCardSpacing() {
    switch (deviceType) {
      case DeviceType.mobile:
        return 8;
      case DeviceType.tablet:
        return 12;
      case DeviceType.desktop:
        return 16;
      case DeviceType.tv:
        return 24;
    }
  }

  double getFontSizeMultiplier() {
    switch (deviceType) {
      case DeviceType.mobile:
        return 1.0;
      case DeviceType.tablet:
        return 1.1;
      case DeviceType.desktop:
        return 1.0;
      case DeviceType.tv:
        return 1.3;
    }
  }

  double getIconSize() {
    switch (deviceType) {
      case DeviceType.mobile:
        return 24;
      case DeviceType.tablet:
        return 28;
      case DeviceType.desktop:
        return 24;
      case DeviceType.tv:
        return 32;
    }
  }

  double getButtonHeight() {
    switch (deviceType) {
      case DeviceType.mobile:
        return 48;
      case DeviceType.tablet:
        return 52;
      case DeviceType.desktop:
        return 40;
      case DeviceType.tv:
        return 56;
    }
  }

  // Navigation helpers
  bool shouldUseBottomNavigation() {
    return deviceType == DeviceType.mobile && isPortrait;
  }

  bool shouldUseNavigationRail() {
    return (deviceType == DeviceType.tablet && isLandscape) ||
           deviceType == DeviceType.desktop ||
           deviceType == DeviceType.tv;
  }

  bool shouldUseDrawer() {
    return deviceType == DeviceType.mobile ||
           (deviceType == DeviceType.tablet && isPortrait);
  }

  bool shouldShowFAB() {
    return deviceType == DeviceType.mobile ||
           (deviceType == DeviceType.tablet && isPortrait);
  }

  // Layout patterns
  bool shouldUseGridLayout() {
    return screenSize.index >= ScreenSize.md.index;
  }

  bool shouldUseListLayout() {
    return screenSize.index < ScreenSize.md.index;
  }

  bool shouldUseSidebar() {
    return screenSize.index >= ScreenSize.lg.index;
  }

  bool shouldUseModalDialogs() {
    return deviceType == DeviceType.mobile;
  }

  bool shouldUseBottomSheets() {
    return deviceType == DeviceType.mobile && isPortrait;
  }

  // Content density
  double getListItemHeight() {
    switch (deviceType) {
      case DeviceType.mobile:
        return 72;
      case DeviceType.tablet:
        return 80;
      case DeviceType.desktop:
        return 64;
      case DeviceType.tv:
        return 88;
    }
  }

  double getCardElevation() {
    switch (deviceType) {
      case DeviceType.mobile:
        return 2;
      case DeviceType.tablet:
        return 4;
      case DeviceType.desktop:
        return 1;
      case DeviceType.tv:
        return 8;
    }
  }

  BorderRadius getCardBorderRadius() {
    switch (deviceType) {
      case DeviceType.mobile:
        return BorderRadius.circular(12);
      case DeviceType.tablet:
        return BorderRadius.circular(16);
      case DeviceType.desktop:
        return BorderRadius.circular(8);
      case DeviceType.tv:
        return BorderRadius.circular(20);
    }
  }

  // Animation durations
  Duration getAnimationDuration() {
    switch (deviceType) {
      case DeviceType.mobile:
        return const Duration(milliseconds: 300);
      case DeviceType.tablet:
        return const Duration(milliseconds: 350);
      case DeviceType.desktop:
        return const Duration(milliseconds: 200);
      case DeviceType.tv:
        return const Duration(milliseconds: 400);
    }
  }

  // Responsive values
  T responsive<T>({
    T? xs,
    T? sm,
    T? md,
    T? lg,
    T? xl,
    T? xxl,
    required T fallback,
  }) {
    switch (screenSize) {
      case ScreenSize.xs:
        return xs ?? fallback;
      case ScreenSize.sm:
        return sm ?? xs ?? fallback;
      case ScreenSize.md:
        return md ?? sm ?? xs ?? fallback;
      case ScreenSize.lg:
        return lg ?? md ?? sm ?? xs ?? fallback;
      case ScreenSize.xl:
        return xl ?? lg ?? md ?? sm ?? xs ?? fallback;
      case ScreenSize.xxl:
        return xxl ?? xl ?? lg ?? md ?? sm ?? xs ?? fallback;
    }
  }

  T device<T>({
    T? mobile,
    T? tablet,
    T? desktop,
    T? tv,
    required T fallback,
  }) {
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? fallback;
      case DeviceType.tablet:
        return tablet ?? mobile ?? fallback;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile ?? fallback;
      case DeviceType.tv:
        return tv ?? desktop ?? tablet ?? mobile ?? fallback;
    }
  }

  @override
  String toString() {
    return 'ResponsiveConfig('
        'deviceType: $deviceType, '
        'screenSize: $screenSize, '
        'orientation: $orientation, '
        'size: ${width.toInt()}x${height.toInt()}'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ResponsiveConfig &&
        other.deviceType == deviceType &&
        other.screenSize == screenSize &&
        other.orientation == orientation &&
        other.width == width &&
        other.height == height;
  }

  @override
  int get hashCode {
    return Object.hash(deviceType, screenSize, orientation, width, height);
  }
}
