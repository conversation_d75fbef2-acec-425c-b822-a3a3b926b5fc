# 🛠️ Tool Builder Rebuild - Step by Step Implementation

## 🎯 Goal
Transform the existing Tool Builder into an Excel-like backend with visual UI builder, following the exact specifications without affecting Calculator or Converter.

## 📋 Prerequisites
- ShadowSuite project with working builds
- Flutter SDK 3.32.2+
- Existing Calculator and Converter functionality preserved

## 🚀 Implementation Steps

### Step 1: Backup and Remove Legacy Tool Builder

#### 1.1 Create Backup
```bash
mkdir backup_tool_builder_$(date +%s)
cp lib/features/tools/screens/custom_tool_builder_screen.dart backup_tool_builder_*/
cp lib/features/tools/models/custom_tool.dart backup_tool_builder_*/
cp lib/features/tools/providers/custom_tool_provider.dart backup_tool_builder_*/
```

#### 1.2 Remove Legacy Files (KEEP Calculator & Converter)
```bash
# Remove ONLY these files (preserve calculator and converter)
rm lib/features/tools/screens/custom_tool_builder_screen.dart
rm lib/features/tools/models/custom_tool.dart
rm lib/features/tools/providers/custom_tool_provider.dart
rm lib/features/tools/widgets/tool_builder_widgets.dart
```

#### 1.3 Clean Routes in app_router.dart
Remove these routes (keep calculator/converter routes):
- `/tools/builder`
- `/tools/create`
- `/tools/edit/:id`

### Step 2: Update Dependencies

#### 2.1 Add to pubspec.yaml
```yaml
dependencies:
  # Excel and spreadsheet functionality
  syncfusion_flutter_xlsio: ^26.2.14
  syncfusion_flutter_datagrid: ^26.2.14
  
  # File operations
  file_picker: ^8.1.2
  
  # Excel parsing
  excel: ^4.0.6
  
  # Formula evaluation
  expressions: ^0.2.6
  
  # Existing dependencies remain unchanged
```

#### 2.2 Run pub get
```bash
flutter pub get
```

### Step 3: Create New Data Models

#### 3.1 Create lib/features/tools/models/tool_definition.dart
```dart
import 'package:isar/isar.dart';

part 'tool_definition.g.dart';

@collection
class ToolDefinition {
  Id id = Isar.autoIncrement;
  late String name;
  late String description;
  late DateTime createdAt;
  late DateTime updatedAt;
  late String backendData; // JSON string of spreadsheet data
  late String uiLayout; // JSON string of UI components
  late String userId;
  bool isPasswordProtected = false;
  String? passwordHash;
  bool isDeleted = false;
  
  factory ToolDefinition.create({
    required String name,
    required String description,
    required String userId,
    String backendData = '{}',
    String uiLayout = '{}',
  }) {
    final now = DateTime.now();
    return ToolDefinition()
      ..name = name
      ..description = description
      ..userId = userId
      ..backendData = backendData
      ..uiLayout = uiLayout
      ..createdAt = now
      ..updatedAt = now
      ..isDeleted = false;
  }
}
```

#### 3.2 Create lib/features/tools/models/spreadsheet_cell.dart
```dart
class SpreadsheetCell {
  final String address; // A1, B2, etc.
  final dynamic value;
  final String? formula;
  final CellType type;
  
  const SpreadsheetCell({
    required this.address,
    this.value,
    this.formula,
    required this.type,
  });
  
  Map<String, dynamic> toJson() => {
    'address': address,
    'value': value,
    'formula': formula,
    'type': type.name,
  };
  
  factory SpreadsheetCell.fromJson(Map<String, dynamic> json) {
    return SpreadsheetCell(
      address: json['address'],
      value: json['value'],
      formula: json['formula'],
      type: CellType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CellType.text,
      ),
    );
  }
}

enum CellType { text, number, boolean, formula, error }
```

#### 3.3 Create lib/features/tools/models/ui_component.dart
```dart
class UIComponent {
  final String id;
  final ComponentType type;
  final String label;
  final String? bindToCell;
  final Map<String, dynamic> properties;
  
  const UIComponent({
    required this.id,
    required this.type,
    required this.label,
    this.bindToCell,
    required this.properties,
  });
  
  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'label': label,
    'bindToCell': bindToCell,
    'properties': properties,
  };
  
  factory UIComponent.fromJson(Map<String, dynamic> json) {
    return UIComponent(
      id: json['id'],
      type: ComponentType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ComponentType.label,
      ),
      label: json['label'],
      bindToCell: json['bindToCell'],
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
    );
  }
}

enum ComponentType {
  textField, outputField, button, toggle, dropdown, label
}
```

### Step 4: Create Core Services

#### 4.1 Create lib/features/tools/services/formula_engine_service.dart
```dart
import 'dart:math' as math;
import '../models/spreadsheet_cell.dart';

class FormulaEngineService {
  static final FormulaEngineService _instance = FormulaEngineService._internal();
  factory FormulaEngineService() => _instance;
  FormulaEngineService._internal();
  
  // Evaluate a formula with cell references
  dynamic evaluateFormula(String formula, Map<String, SpreadsheetCell> cells) {
    try {
      if (!formula.startsWith('=')) return formula;
      
      String expression = formula.substring(1); // Remove '='
      
      // Replace cell references with values
      expression = _replaceCellReferences(expression, cells);
      
      // Evaluate the expression
      return _evaluateExpression(expression);
    } catch (e) {
      return '#ERROR!';
    }
  }
  
  // Get list of cell dependencies for a formula
  List<String> getDependencies(String formula) {
    final cellPattern = RegExp(r'[A-Z]+[0-9]+');
    return cellPattern.allMatches(formula).map((m) => m.group(0)!).toList();
  }
  
  // Recalculate entire sheet with dependency resolution
  Map<String, SpreadsheetCell> recalculateSheet(Map<String, SpreadsheetCell> cells) {
    final result = Map<String, SpreadsheetCell>.from(cells);
    
    // Simple recalculation (in production, implement proper dependency graph)
    for (final entry in result.entries) {
      if (entry.value.hasFormula) {
        final newValue = evaluateFormula(entry.value.formula!, result);
        result[entry.key] = entry.value.copyWith(
          value: newValue,
          type: _getValueType(newValue),
        );
      }
    }
    
    return result;
  }
  
  String _replaceCellReferences(String expression, Map<String, SpreadsheetCell> cells) {
    final cellPattern = RegExp(r'[A-Z]+[0-9]+');
    return expression.replaceAllMapped(cellPattern, (match) {
      final cellAddress = match.group(0)!;
      final cell = cells[cellAddress];
      return cell?.value?.toString() ?? '0';
    });
  }
  
  dynamic _evaluateExpression(String expression) {
    // Simple expression evaluator (in production, use proper parser)
    // This is a simplified version - implement full Excel formula support
    
    // Handle basic arithmetic
    if (expression.contains('+')) {
      final parts = expression.split('+');
      return parts.fold<double>(0, (sum, part) => sum + (double.tryParse(part.trim()) ?? 0));
    }
    
    if (expression.contains('-')) {
      final parts = expression.split('-');
      if (parts.length == 2) {
        return (double.tryParse(parts[0].trim()) ?? 0) - (double.tryParse(parts[1].trim()) ?? 0);
      }
    }
    
    if (expression.contains('*')) {
      final parts = expression.split('*');
      return parts.fold<double>(1, (product, part) => product * (double.tryParse(part.trim()) ?? 1));
    }
    
    if (expression.contains('/')) {
      final parts = expression.split('/');
      if (parts.length == 2) {
        final divisor = double.tryParse(parts[1].trim()) ?? 1;
        if (divisor == 0) return '#DIV/0!';
        return (double.tryParse(parts[0].trim()) ?? 0) / divisor;
      }
    }
    
    // Handle functions
    if (expression.startsWith('SUM(') && expression.endsWith(')')) {
      final range = expression.substring(4, expression.length - 1);
      // Implement SUM function
      return 0; // Placeholder
    }
    
    // Return as number or text
    return double.tryParse(expression) ?? expression;
  }
  
  CellType _getValueType(dynamic value) {
    if (value is num) return CellType.number;
    if (value is bool) return CellType.boolean;
    if (value.toString().startsWith('#')) return CellType.error;
    return CellType.text;
  }
}
```

#### 4.2 Create lib/features/tools/services/tool_storage_service.dart
```dart
import 'package:isar/isar.dart';
import '../../../core/database/database_service.dart';
import '../models/tool_definition.dart';

class ToolStorageService {
  static final ToolStorageService _instance = ToolStorageService._internal();
  factory ToolStorageService() => _instance;
  ToolStorageService._internal();
  
  Future<void> saveTool(ToolDefinition tool) async {
    final db = DatabaseService().database;
    await db.writeTxn(() async {
      await db.toolDefinitions.put(tool);
    });
  }
  
  Future<List<ToolDefinition>> getUserTools(String userId) async {
    final db = DatabaseService().database;
    return await db.toolDefinitions
        .filter()
        .userIdEqualTo(userId)
        .and()
        .isDeletedEqualTo(false)
        .sortByUpdatedAtDesc()
        .findAll();
  }
  
  Future<ToolDefinition?> getTool(int toolId) async {
    final db = DatabaseService().database;
    return await db.toolDefinitions.get(toolId);
  }
  
  Future<void> deleteTool(int toolId) async {
    final db = DatabaseService().database;
    await db.writeTxn(() async {
      final tool = await db.toolDefinitions.get(toolId);
      if (tool != null) {
        tool.isDeleted = true;
        tool.updatedAt = DateTime.now();
        await db.toolDefinitions.put(tool);
      }
    });
  }
  
  Future<String> exportTool(int toolId) async {
    final tool = await getTool(toolId);
    if (tool == null) throw Exception('Tool not found');
    
    return jsonEncode(tool.toJson());
  }
  
  Future<ToolDefinition> importTool(String jsonData, String userId) async {
    final data = jsonDecode(jsonData);
    final tool = ToolDefinition.fromJson(data, userId);
    await saveTool(tool);
    return tool;
  }
}
```

### Step 5: Create New Screens

#### 5.1 Create lib/features/tools/screens/tool_builder_home_screen.dart
```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class ToolBuilderHomeScreen extends ConsumerWidget {
  const ToolBuilderHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tool Builder'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.build_circle,
                      size: 64,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Excel-Powered Tool Builder',
                      style: Theme.of(context).textTheme.headlineSmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create interactive tools with spreadsheet logic and visual UI builder',
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => context.go('/tools/create'),
                    icon: const Icon(Icons.add),
                    label: const Text('Create New Tool'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => context.go('/tools/import-excel'),
                    icon: const Icon(Icons.upload_file),
                    label: const Text('Import Excel'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // My Tools Section
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'My Tools',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          TextButton(
                            onPressed: () => context.go('/tools/my-tools'),
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: _buildRecentToolsList(context),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRecentToolsList(BuildContext context) {
    // Placeholder for recent tools list
    return ListView.builder(
      itemCount: 3,
      itemBuilder: (context, index) {
        return ListTile(
          leading: const Icon(Icons.build),
          title: Text('Sample Tool ${index + 1}'),
          subtitle: Text('Created ${index + 1} days ago'),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () {
            // Navigate to tool preview
          },
        );
      },
    );
  }
}
```

This step-by-step guide provides the exact implementation needed to rebuild the Tool Builder following the specifications. Each step is clearly defined and can be executed independently while preserving the Calculator and Converter functionality.
