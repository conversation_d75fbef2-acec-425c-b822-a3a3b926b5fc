import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/note.dart';
import '../providers/notes_provider.dart';
import '../widgets/rich_text_editor.dart';

class NoteEditorScreen extends ConsumerStatefulWidget {
  final TextNote note;
  final bool isNew;

  const NoteEditorScreen({
    super.key,
    required this.note,
    this.isNew = false,
  });

  @override
  ConsumerState<NoteEditorScreen> createState() => _NoteEditorScreenState();
}

class _NoteEditorScreenState extends ConsumerState<NoteEditorScreen> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  late TextNote _currentNote;
  bool _hasUnsavedChanges = false;
  bool _isFullscreen = false;
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _currentNote = widget.note;
    _titleController = TextEditingController(text: _currentNote.title);
    _contentController = TextEditingController(text: _currentNote.content);
    
    _titleController.addListener(_onTextChanged);
    _contentController.addListener(_onTextChanged);
    
    // Auto-focus title for new notes, content for existing notes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.isNew) {
        _titleFocusNode.requestFocus();
      } else {
        _contentFocusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _hasUnsavedChanges) {
          final shouldPop = await _showUnsavedChangesDialog();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: _isFullscreen ? null : _buildAppBar(),
        body: _buildBody(),
        bottomNavigationBar: _isFullscreen ? null : _buildBottomBar(),
        floatingActionButton: _isFullscreen ? _buildFullscreenFAB() : null,
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(widget.isNew ? 'New Note' : 'Edit Note'),
      backgroundColor: Colors.deepPurple,
      foregroundColor: Colors.white,
      actions: [
        if (_hasUnsavedChanges)
          IconButton(
            onPressed: _saveNote,
            icon: const Icon(Icons.save),
            tooltip: 'Save',
          ),
        IconButton(
          onPressed: _toggleFullscreen,
          icon: Icon(_isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen),
          tooltip: _isFullscreen ? 'Exit Fullscreen' : 'Fullscreen',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'pin',
              child: Row(
                children: [
                  Icon(_currentNote.isPinned ? Icons.push_pin_outlined : Icons.push_pin),
                  const SizedBox(width: 8),
                  Text(_currentNote.isPinned ? 'Unpin' : 'Pin'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'favorite',
              child: Row(
                children: [
                  Icon(_currentNote.isFavorite ? Icons.favorite : Icons.favorite_border),
                  const SizedBox(width: 8),
                  Text(_currentNote.isFavorite ? 'Remove from Favorites' : 'Add to Favorites'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'category',
              child: Row(
                children: [
                  Icon(Icons.category),
                  SizedBox(width: 8),
                  Text('Change Category'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'color',
              child: Row(
                children: [
                  Icon(Icons.palette),
                  SizedBox(width: 8),
                  Text('Change Color'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'tags',
              child: Row(
                children: [
                  Icon(Icons.local_offer),
                  SizedBox(width: 8),
                  Text('Manage Tags'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('Export/Share'),
                ],
              ),
            ),
            if (!widget.isNew)
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Container(
      color: _currentNote.getColor(),
      child: Column(
        children: [
          if (_isFullscreen) _buildFullscreenHeader(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Title field
                  TextField(
                    controller: _titleController,
                    focusNode: _titleFocusNode,
                    decoration: const InputDecoration(
                      hintText: 'Note title...',
                      border: InputBorder.none,
                      hintStyle: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    textCapitalization: TextCapitalization.sentences,
                  ),
                  const Divider(),
                  
                  // Content field with rich text editor
                  Expanded(
                    child: RichTextEditor(
                      controller: _contentController,
                      focusNode: _contentFocusNode,
                      onChanged: _onTextChanged,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullscreenHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.deepPurple.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Colors.deepPurple.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _toggleFullscreen,
            icon: const Icon(Icons.fullscreen_exit),
            tooltip: 'Exit Fullscreen',
          ),
          Expanded(
            child: Text(
              _titleController.text.isEmpty ? 'Untitled Note' : _titleController.text,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (_hasUnsavedChanges)
            IconButton(
              onPressed: _saveNote,
              icon: const Icon(Icons.save),
              tooltip: 'Save',
            ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // Word count
          Icon(
            Icons.text_fields,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Text(
            '${_getWordCount()} words, ${_contentController.text.length} chars',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const Spacer(),
          
          // Last saved indicator
          if (!_hasUnsavedChanges && !widget.isNew)
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  'Saved',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.green,
                  ),
                ),
              ],
            )
          else if (_hasUnsavedChanges)
            Row(
              children: [
                Icon(
                  Icons.circle,
                  size: 8,
                  color: Colors.orange,
                ),
                const SizedBox(width: 4),
                Text(
                  'Unsaved changes',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildFullscreenFAB() {
    return FloatingActionButton(
      onPressed: _saveNote,
      backgroundColor: Colors.deepPurple,
      foregroundColor: Colors.white,
      child: const Icon(Icons.save),
    );
  }

  int _getWordCount() {
    return _contentController.text
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .length;
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  Future<void> _saveNote() async {
    try {
      _currentNote.updateTitle(_titleController.text);
      _currentNote.updateContent(_contentController.text);
      
      if (widget.isNew) {
        await ref.read(notesProvider.notifier).addNote(_currentNote);
      } else {
        await ref.read(notesProvider.notifier).updateNote(_currentNote);
      }
      
      setState(() {
        _hasUnsavedChanges = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note saved'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving note: $e')),
        );
      }
    }
  }

  Future<bool> _showUnsavedChangesDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unsaved Changes'),
        content: const Text('You have unsaved changes. Do you want to save before leaving?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Discard'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop(false);
              await _saveNote();
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _handleMenuAction(String action) async {
    switch (action) {
      case 'pin':
        _currentNote.togglePin();
        await _saveNote();
        break;
      case 'favorite':
        _currentNote.toggleFavorite();
        await _saveNote();
        break;
      case 'category':
        _showCategoryDialog();
        break;
      case 'color':
        _showColorDialog();
        break;
      case 'tags':
        _showTagsDialog();
        break;
      case 'export':
        _exportNote();
        break;
      case 'delete':
        _deleteNote();
        break;
    }
  }

  void _showCategoryDialog() {
    // TODO: Implement category selection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Category selection coming soon!')),
    );
  }

  void _showColorDialog() {
    // TODO: Implement color selection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Color selection coming soon!')),
    );
  }

  void _showTagsDialog() {
    // TODO: Implement tags management dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Tags management coming soon!')),
    );
  }

  void _exportNote() {
    // TODO: Implement note export/share
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export feature coming soon!')),
    );
  }

  void _deleteNote() async {
    if (widget.isNew) return;
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Note'),
        content: const Text('Are you sure you want to delete this note?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(notesProvider.notifier).deleteNote(_currentNote.id);
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Note deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting note: $e')),
          );
        }
      }
    }
  }
}
