// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'مجموعة الظل';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get athkar => 'الأذكار';

  @override
  String get quran => 'القرآن';

  @override
  String get prayerTimes => 'أوقات الصلاة';

  @override
  String get tools => 'الأدوات';

  @override
  String get memo => 'مجموعة المذكرات';

  @override
  String get moneyFlow => 'تدفق الأموال';

  @override
  String get settings => 'الإعدادات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get dhikr => 'الذكر';

  @override
  String get progress => 'التقدم';

  @override
  String get notes => 'الملاحظات';

  @override
  String get todos => 'المهام';

  @override
  String get voiceMemos => 'المذكرات الصوتية';

  @override
  String get calculator => 'الآلة الحاسبة';

  @override
  String get converter => 'المحول';

  @override
  String get toolBuilder => 'منشئ الأدوات';

  @override
  String get accounts => 'الحسابات';

  @override
  String get transactions => 'المعاملات';

  @override
  String get budgets => 'الميزانيات';

  @override
  String get reports => 'التقارير';

  @override
  String get add => 'إضافة';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get ok => 'موافق';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get refresh => 'تحديث';

  @override
  String get share => 'مشاركة';

  @override
  String get export => 'تصدير';

  @override
  String get import => 'استيراد';

  @override
  String get backup => 'نسخ احتياطي';

  @override
  String get restore => 'استعادة';

  @override
  String get sync => 'مزامنة';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signUp => 'إنشاء حساب';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get name => 'الاسم';

  @override
  String get title => 'العنوان';

  @override
  String get description => 'الوصف';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get amount => 'المبلغ';

  @override
  String get category => 'الفئة';

  @override
  String get priority => 'الأولوية';

  @override
  String get status => 'الحالة';

  @override
  String get language => 'اللغة';

  @override
  String get theme => 'المظهر';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get privacy => 'الخصوصية';

  @override
  String get security => 'الأمان';

  @override
  String get about => 'حول';

  @override
  String get version => 'الإصدار';

  @override
  String get help => 'المساعدة';

  @override
  String get support => 'الدعم';

  @override
  String get feedback => 'التعليقات';

  @override
  String get rateApp => 'تقييم التطبيق';

  @override
  String get translationSettings => 'إعدادات الترجمة';

  @override
  String get displayMode => 'وضع العرض';

  @override
  String get arabicOnly => 'العربية فقط';

  @override
  String get arabicWithTranslation => 'العربية + الترجمة';

  @override
  String get arabicWithTransliteration => 'العربية + النقل الحرفي';

  @override
  String get arabicWithBoth => 'العربية + الترجمة + النقل الحرفي';

  @override
  String get translationOnly => 'الترجمة فقط';

  @override
  String get fontSize => 'حجم الخط';

  @override
  String get fontFamily => 'نوع الخط';

  @override
  String get showReference => 'إظهار المرجع';

  @override
  String get showRecommendedCount => 'إظهار العدد المُوصى به';

  @override
  String get rightToLeftText => 'النص من اليمين إلى اليسار';

  @override
  String get resetToDefaults => 'إعادة تعيين إلى الافتراضي';

  @override
  String completedTasks(int count) {
    return 'المهام المكتملة ($count)';
  }

  @override
  String activeTasks(int count) {
    return 'المهام النشطة ($count)';
  }

  @override
  String get clearCompleted => 'مسح المكتملة';

  @override
  String get duplicate => 'نسخ';

  @override
  String get tapToCount => 'اضغط للعد';

  @override
  String get completed => 'مكتمل!';

  @override
  String get nextPrayer => 'الصلاة التالية';

  @override
  String get todaysPrayerTimes => 'أوقات صلاة اليوم';

  @override
  String get currentLocation => 'الموقع الحالي';

  @override
  String get locationSettings => 'إعدادات الموقع';

  @override
  String get fetchFromAPI => 'جلب من API';

  @override
  String get useSample => 'استخدام عينة';

  @override
  String get updateTimes => 'تحديث الأوقات';

  @override
  String get changeLocation => 'تغيير الموقع';
}
