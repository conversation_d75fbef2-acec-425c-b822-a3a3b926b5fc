import 'dart:convert';

/// Service for converting formulas to various code formats
class FormulaToCodeConverter {
  static final FormulaToCodeConverter _instance = FormulaToCodeConverter._internal();
  factory FormulaToCodeConverter() => _instance;
  FormulaToCodeConverter._internal();

  /// Convert formula to Dart code
  String convertToDart(String formula, Map<String, String> cellMappings) {
    if (!formula.startsWith('=')) return 'return "$formula";';
    
    final expression = formula.substring(1);
    final dartCode = _convertExpressionToDart(expression, cellMappings);
    
    return '''
double calculateFormula(Map<String, dynamic> inputs) {
  try {
    return $dartCode;
  } catch (e) {
    return 0.0;
  }
}''';
  }

  /// Convert formula to JSON logic
  Map<String, dynamic> convertToJsonLogic(String formula, Map<String, String> cellMappings) {
    if (!formula.startsWith('=')) {
      return {'var': formula};
    }
    
    final expression = formula.substring(1);
    return _convertExpressionToJsonLogic(expression, cellMappings);
  }

  /// Convert formula to JavaScript
  String convertToJavaScript(String formula, Map<String, String> cellMappings) {
    if (!formula.startsWith('=')) return 'return "$formula";';
    
    final expression = formula.substring(1);
    final jsCode = _convertExpressionToJavaScript(expression, cellMappings);
    
    return '''
function calculateFormula(inputs) {
  try {
    return $jsCode;
  } catch (e) {
    return 0;
  }
}''';
  }

  /// Generate UI mapping configuration
  UIMappingConfig generateUIMapping(
    Map<String, String> formulas,
    Map<String, String> cellMappings,
  ) {
    final inputs = <String, UIInputConfig>{};
    final outputs = <String, UIOutputConfig>{};
    final calculations = <String, CalculationConfig>{};

    // Identify inputs (cells referenced but not calculated)
    final referencedCells = <String>{};
    final calculatedCells = formulas.keys.toSet();

    for (final formula in formulas.values) {
      referencedCells.addAll(_extractCellReferences(formula));
    }

    final inputCells = referencedCells.difference(calculatedCells);

    // Generate input configurations
    for (final cellAddress in inputCells) {
      final variableName = cellMappings[cellAddress] ?? cellAddress;
      inputs[cellAddress] = UIInputConfig(
        variableName: variableName,
        label: _generateLabel(variableName),
        type: UIInputType.number,
        defaultValue: 0,
        validation: UIValidation(
          required: true,
          min: null,
          max: null,
        ),
      );
    }

    // Generate calculation configurations
    for (final entry in formulas.entries) {
      final cellAddress = entry.key;
      final formula = entry.value;
      final variableName = cellMappings[cellAddress] ?? cellAddress;

      calculations[cellAddress] = CalculationConfig(
        variableName: variableName,
        formula: formula,
        dartCode: convertToDart(formula, cellMappings),
        jsonLogic: convertToJsonLogic(formula, cellMappings),
        dependencies: _extractCellReferences(formula),
      );
    }

    // Generate output configurations
    for (final cellAddress in calculatedCells) {
      final variableName = cellMappings[cellAddress] ?? cellAddress;
      outputs[cellAddress] = UIOutputConfig(
        variableName: variableName,
        label: _generateLabel(variableName),
        format: UIOutputFormat.number,
        precision: 2,
      );
    }

    return UIMappingConfig(
      inputs: inputs,
      outputs: outputs,
      calculations: calculations,
    );
  }

  /// Generate Flutter widget code for the tool
  String generateFlutterWidget(UIMappingConfig config, String toolName) {
    final className = _toCamelCase(toolName);
    final inputs = config.inputs.values.toList();
    final outputs = config.outputs.values.toList();
    final calculations = config.calculations.values.toList();

    return '''
import 'package:flutter/material.dart';

class ${className}Widget extends StatefulWidget {
  const ${className}Widget({super.key});

  @override
  State<${className}Widget> createState() => _${className}WidgetState();
}

class _${className}WidgetState extends State<${className}Widget> {
  final _formKey = GlobalKey<FormState>();
  ${inputs.map((input) => 'final _${input.variableName}Controller = TextEditingController();').join('\n  ')}
  
  ${outputs.map((output) => 'double _${output.variableName} = 0.0;').join('\n  ')}

  @override
  void dispose() {
    ${inputs.map((input) => '_${input.variableName}Controller.dispose();').join('\n    ')}
    super.dispose();
  }

  void _calculate() {
    if (!_formKey.currentState!.validate()) return;

    final inputs = <String, dynamic>{
      ${inputs.map((input) => "'${input.variableName}': double.tryParse(_${input.variableName}Controller.text) ?? 0.0,").join('\n      ')}
    };

    setState(() {
      ${calculations.map((calc) => '_${calc.variableName} = ${_generateCalculationCall(calc)};').join('\n      ')}
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('$toolName'),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Input fields
              ${inputs.map((input) => _generateInputWidget(input)).join('\n              ')},
              
              const SizedBox(height: 24),
              
              // Calculate button
              ElevatedButton(
                onPressed: _calculate,
                child: const Text('Calculate'),
              ),
              
              const SizedBox(height: 24),
              
              // Output fields
              ${outputs.map((output) => _generateOutputWidget(output)).join('\n              ')},
            ],
          ),
        ),
      ),
    );
  }

  ${calculations.map((calc) => _generateCalculationMethod(calc)).join('\n\n  ')}
}''';
  }

  String _convertExpressionToDart(String expression, Map<String, String> cellMappings) {
    String dartExpression = expression;

    // Replace cell references with variable names
    for (final entry in cellMappings.entries) {
      dartExpression = dartExpression.replaceAll(entry.key, 'inputs["${entry.value}"]');
    }

    // Convert Excel functions to Dart equivalents
    dartExpression = _convertFunctionsToDart(dartExpression);

    return dartExpression;
  }

  Map<String, dynamic> _convertExpressionToJsonLogic(String expression, Map<String, String> cellMappings) {
    // Simplified JSON Logic conversion
    if (expression.contains('+')) {
      final parts = expression.split('+');
      return {
        '+': parts.map((part) => _convertPartToJsonLogic(part.trim(), cellMappings)).toList()
      };
    } else if (expression.contains('*')) {
      final parts = expression.split('*');
      return {
        '*': parts.map((part) => _convertPartToJsonLogic(part.trim(), cellMappings)).toList()
      };
    }

    return _convertPartToJsonLogic(expression, cellMappings);
  }

  Map<String, dynamic> _convertPartToJsonLogic(String part, Map<String, String> cellMappings) {
    // Check if it's a cell reference
    if (cellMappings.containsKey(part)) {
      return {'var': cellMappings[part]!};
    }

    // Check if it's a number
    final number = double.tryParse(part);
    if (number != null) {
      return {'value': number};
    }

    // Default to variable
    return {'var': part};
  }

  String _convertExpressionToJavaScript(String expression, Map<String, String> cellMappings) {
    String jsExpression = expression;

    // Replace cell references with variable names
    for (final entry in cellMappings.entries) {
      jsExpression = jsExpression.replaceAll(entry.key, 'inputs["${entry.value}"]');
    }

    // Convert Excel functions to JavaScript equivalents
    jsExpression = _convertFunctionsToJavaScript(jsExpression);

    return jsExpression;
  }

  String _convertFunctionsToDart(String expression) {
    // Convert common Excel functions to Dart equivalents
    expression = expression.replaceAllMapped(
      RegExp(r'SUM\(([^)]+)\)'),
      (match) => '(${match.group(1)!.split(',').join(' + ')})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'AVERAGE\(([^)]+)\)'),
      (match) => '((${match.group(1)!.split(',').join(' + ')}) / ${match.group(1)!.split(',').length})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'MAX\(([^)]+)\)'),
      (match) => 'math.max(${match.group(1)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'MIN\(([^)]+)\)'),
      (match) => 'math.min(${match.group(1)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'ROUND\(([^,]+),\s*(\d+)\)'),
      (match) => '(${match.group(1)!} * math.pow(10, ${match.group(2)!})).round() / math.pow(10, ${match.group(2)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'ABS\(([^)]+)\)'),
      (match) => '(${match.group(1)!}).abs()',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'SQRT\(([^)]+)\)'),
      (match) => 'math.sqrt(${match.group(1)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'POWER\(([^,]+),\s*([^)]+)\)'),
      (match) => 'math.pow(${match.group(1)!}, ${match.group(2)!})',
    );

    return expression;
  }

  String _convertFunctionsToJavaScript(String expression) {
    // Convert common Excel functions to JavaScript equivalents
    expression = expression.replaceAllMapped(
      RegExp(r'SUM\(([^)]+)\)'),
      (match) => '(${match.group(1)!.split(',').join(' + ')})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'AVERAGE\(([^)]+)\)'),
      (match) => '((${match.group(1)!.split(',').join(' + ')}) / ${match.group(1)!.split(',').length})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'MAX\(([^)]+)\)'),
      (match) => 'Math.max(${match.group(1)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'MIN\(([^)]+)\)'),
      (match) => 'Math.min(${match.group(1)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'ROUND\(([^,]+),\s*(\d+)\)'),
      (match) => 'Math.round(${match.group(1)!} * Math.pow(10, ${match.group(2)!})) / Math.pow(10, ${match.group(2)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'ABS\(([^)]+)\)'),
      (match) => 'Math.abs(${match.group(1)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'SQRT\(([^)]+)\)'),
      (match) => 'Math.sqrt(${match.group(1)!})',
    );

    expression = expression.replaceAllMapped(
      RegExp(r'POWER\(([^,]+),\s*([^)]+)\)'),
      (match) => 'Math.pow(${match.group(1)!}, ${match.group(2)!})',
    );

    return expression;
  }

  List<String> _extractCellReferences(String formula) {
    final references = <String>[];
    final regex = RegExp(r'[A-Z]+\d+');
    final matches = regex.allMatches(formula);
    
    for (final match in matches) {
      references.add(match.group(0)!);
    }
    
    return references.toSet().toList();
  }

  String _generateLabel(String variableName) {
    // Convert camelCase or snake_case to readable label
    return variableName
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .replaceAll('_', ' ')
        .trim()
        .split(' ')
        .map((word) => word.isEmpty ? word : word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join(' ');
  }

  String _toCamelCase(String text) {
    return text
        .replaceAll(RegExp(r'[^a-zA-Z0-9]'), ' ')
        .split(' ')
        .where((word) => word.isNotEmpty)
        .map((word) => word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join('');
  }

  String _generateInputWidget(UIInputConfig input) {
    return '''TextFormField(
                controller: _${input.variableName}Controller,
                decoration: const InputDecoration(
                  labelText: '${input.label}',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a value';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),''';
  }

  String _generateOutputWidget(UIOutputConfig output) {
    return '''Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${output.label}:'),
                      Text(
                        _${output.variableName}.toStringAsFixed(${output.precision}),
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ],
                  ),
                ),
              ),''';
  }

  String _generateCalculationCall(CalculationConfig calc) {
    return '_calculate${_toCamelCase(calc.variableName)}(inputs)';
  }

  String _generateCalculationMethod(CalculationConfig calc) {
    return '''double _calculate${_toCamelCase(calc.variableName)}(Map<String, dynamic> inputs) {
    try {
      ${calc.dartCode.split('\n').skip(1).take(calc.dartCode.split('\n').length - 2).join('\n      ')}
    } catch (e) {
      return 0.0;
    }
  }''';
  }
}

// Data classes for UI mapping configuration
class UIMappingConfig {
  final Map<String, UIInputConfig> inputs;
  final Map<String, UIOutputConfig> outputs;
  final Map<String, CalculationConfig> calculations;

  const UIMappingConfig({
    required this.inputs,
    required this.outputs,
    required this.calculations,
  });

  Map<String, dynamic> toJson() {
    return {
      'inputs': inputs.map((key, value) => MapEntry(key, value.toJson())),
      'outputs': outputs.map((key, value) => MapEntry(key, value.toJson())),
      'calculations': calculations.map((key, value) => MapEntry(key, value.toJson())),
    };
  }
}

class UIInputConfig {
  final String variableName;
  final String label;
  final UIInputType type;
  final dynamic defaultValue;
  final UIValidation validation;

  const UIInputConfig({
    required this.variableName,
    required this.label,
    required this.type,
    required this.defaultValue,
    required this.validation,
  });

  Map<String, dynamic> toJson() {
    return {
      'variableName': variableName,
      'label': label,
      'type': type.name,
      'defaultValue': defaultValue,
      'validation': validation.toJson(),
    };
  }
}

class UIOutputConfig {
  final String variableName;
  final String label;
  final UIOutputFormat format;
  final int precision;

  const UIOutputConfig({
    required this.variableName,
    required this.label,
    required this.format,
    required this.precision,
  });

  Map<String, dynamic> toJson() {
    return {
      'variableName': variableName,
      'label': label,
      'format': format.name,
      'precision': precision,
    };
  }
}

class CalculationConfig {
  final String variableName;
  final String formula;
  final String dartCode;
  final Map<String, dynamic> jsonLogic;
  final List<String> dependencies;

  const CalculationConfig({
    required this.variableName,
    required this.formula,
    required this.dartCode,
    required this.jsonLogic,
    required this.dependencies,
  });

  Map<String, dynamic> toJson() {
    return {
      'variableName': variableName,
      'formula': formula,
      'dartCode': dartCode,
      'jsonLogic': jsonLogic,
      'dependencies': dependencies,
    };
  }
}

class UIValidation {
  final bool required;
  final double? min;
  final double? max;

  const UIValidation({
    required this.required,
    this.min,
    this.max,
  });

  Map<String, dynamic> toJson() {
    return {
      'required': required,
      'min': min,
      'max': max,
    };
  }
}

enum UIInputType { number, text, date, boolean }
enum UIOutputFormat { number, currency, percentage, text }
