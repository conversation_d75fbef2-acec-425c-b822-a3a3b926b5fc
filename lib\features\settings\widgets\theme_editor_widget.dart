import 'package:flutter/material.dart';
import '../models/theme_customization.dart';

/// Widget for editing and creating custom themes
class ThemeEditorWidget extends StatefulWidget {
  final ThemeCustomization? theme;
  final Function(ThemeCustomization) onSave;

  const ThemeEditorWidget({
    super.key,
    this.theme,
    required this.onSave,
  });

  @override
  State<ThemeEditorWidget> createState() => _ThemeEditorWidgetState();
}

class _ThemeEditorWidgetState extends State<ThemeEditorWidget> {
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  
  Color _primaryColor = Colors.blue;
  Color _secondaryColor = Colors.green;
  Color _surfaceColor = Colors.white;
  Color _errorColor = Colors.red;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.theme?.name ?? '');
    _descriptionController = TextEditingController(text: widget.theme?.description ?? '');
    
    if (widget.theme != null) {
      _primaryColor = widget.theme!.lightColorScheme.primary;
      _secondaryColor = widget.theme!.lightColorScheme.secondary;
      _surfaceColor = widget.theme!.lightColorScheme.surface;
      _errorColor = widget.theme!.lightColorScheme.error;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.theme == null ? 'Create Theme' : 'Edit Theme'),
        actions: [
          TextButton(
            onPressed: _saveTheme,
            child: const Text('Save'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic info
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Theme Name',
                hintText: 'Enter theme name',
                border: OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter theme description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            
            const SizedBox(height: 32),
            
            // Color scheme
            Text(
              'Color Scheme',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildColorPicker('Primary Color', _primaryColor, (color) {
              setState(() {
                _primaryColor = color;
              });
            }),
            
            const SizedBox(height: 16),
            
            _buildColorPicker('Secondary Color', _secondaryColor, (color) {
              setState(() {
                _secondaryColor = color;
              });
            }),
            
            const SizedBox(height: 16),
            
            _buildColorPicker('Surface Color', _surfaceColor, (color) {
              setState(() {
                _surfaceColor = color;
              });
            }),
            
            const SizedBox(height: 16),
            
            _buildColorPicker('Error Color', _errorColor, (color) {
              setState(() {
                _errorColor = color;
              });
            }),
            
            const SizedBox(height: 32),
            
            // Preview
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildPreview(),
          ],
        ),
      ),
    );
  }

  Widget _buildColorPicker(String label, Color color, Function(Color) onChanged) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        GestureDetector(
          onTap: () => _showColorPicker(color, onChanged),
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreview() {
    final lightColorScheme = ColorScheme.fromSeed(
      seedColor: _primaryColor,
      brightness: Brightness.light,
      secondary: _secondaryColor,
      surface: _surfaceColor,
      error: _errorColor,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: lightColorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
        ),
      ),
      child: Column(
        children: [
          // App bar preview
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: lightColorScheme.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                'App Bar Preview',
                style: TextStyle(
                  color: lightColorScheme.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Buttons preview
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: lightColorScheme.primary,
                    foregroundColor: lightColorScheme.onPrimary,
                  ),
                  child: const Text('Primary'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: lightColorScheme.secondary,
                    foregroundColor: lightColorScheme.onSecondary,
                  ),
                  child: const Text('Secondary'),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Card preview
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: lightColorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Card Preview',
                  style: TextStyle(
                    color: lightColorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'This is how cards will look with your theme.',
                  style: TextStyle(
                    color: lightColorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showColorPicker(Color currentColor, Function(Color) onChanged) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pick a Color'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Basic color palette
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  Colors.red,
                  Colors.pink,
                  Colors.purple,
                  Colors.deepPurple,
                  Colors.indigo,
                  Colors.blue,
                  Colors.lightBlue,
                  Colors.cyan,
                  Colors.teal,
                  Colors.green,
                  Colors.lightGreen,
                  Colors.lime,
                  Colors.yellow,
                  Colors.amber,
                  Colors.orange,
                  Colors.deepOrange,
                  Colors.brown,
                  Colors.grey,
                  Colors.blueGrey,
                  Colors.black,
                ].map((color) => GestureDetector(
                  onTap: () {
                    onChanged(color);
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: currentColor == color 
                            ? Theme.of(context).colorScheme.primary
                            : Colors.transparent,
                        width: 2,
                      ),
                    ),
                  ),
                )).toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _saveTheme() {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a theme name')),
      );
      return;
    }

    final lightColorScheme = ColorScheme.fromSeed(
      seedColor: _primaryColor,
      brightness: Brightness.light,
      secondary: _secondaryColor,
      surface: _surfaceColor,
      error: _errorColor,
    );

    final darkColorScheme = ColorScheme.fromSeed(
      seedColor: _primaryColor,
      brightness: Brightness.dark,
      secondary: _secondaryColor,
      error: _errorColor,
    );

    final theme = ThemeCustomization(
      id: widget.theme?.id ?? 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      lightColorScheme: lightColorScheme,
      darkColorScheme: darkColorScheme,
      textTheme: const TextTheme(),
      appBarTheme: const AppBarTheme(),
      cardTheme: const CardTheme(),
      elevatedButtonTheme: const ElevatedButtonThemeData(),
      outlinedButtonTheme: const OutlinedButtonThemeData(),
      textButtonTheme: const TextButtonThemeData(),
      inputDecorationTheme: const InputDecorationTheme(),
      fabTheme: const FloatingActionButtonThemeData(),
      navigationBarTheme: const NavigationBarThemeData(),
      navigationRailTheme: const NavigationRailThemeData(),
      drawerTheme: const DrawerThemeData(),
      bottomNavTheme: const BottomNavigationBarThemeData(),
      tabBarTheme: const TabBarTheme(),
      chipTheme: const ChipThemeData(),
      dialogTheme: const DialogTheme(),
      snackBarTheme: const SnackBarThemeData(),
      tooltipTheme: const TooltipThemeData(),
      popupMenuTheme: const PopupMenuThemeData(),
      listTileTheme: const ListTileThemeData(),
      expansionTileTheme: const ExpansionTileThemeData(),
      switchTheme: const SwitchThemeData(),
      checkboxTheme: const CheckboxThemeData(),
      radioTheme: const RadioThemeData(),
      sliderTheme: const SliderThemeData(),
      progressIndicatorTheme: const ProgressIndicatorThemeData(),
      dividerTheme: const DividerThemeData(),
      isCustom: true,
      createdAt: widget.theme?.createdAt ?? DateTime.now(),
      modifiedAt: DateTime.now(),
    );

    widget.onSave(theme);
    Navigator.of(context).pop();
  }
}
