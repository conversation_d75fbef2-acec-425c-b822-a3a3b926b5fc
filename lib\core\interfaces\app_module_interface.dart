import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Base class for all mini-app modules in ShadowSuite
abstract class AppModuleInterface {
  /// Unique identifier for the module
  String get id;
  
  /// Display name of the module
  String get name;
  
  /// Description of the module's functionality
  String get description;
  
  /// Version of the module
  String get version;
  
  /// Icon representing the module
  IconData get icon;
  
  /// Primary color theme for the module
  Color get primaryColor;
  
  /// List of required permissions for this module
  List<String> get requiredPermissions;
  
  /// List of dependencies (other modules or core components)
  List<String> get dependencies;
  
  /// Configuration for standalone app export
  Map<String, dynamic> get exportConfig;
  
  /// Main screen/widget for this module
  Widget get homeScreen;
  
  /// Routes defined by this module
  List<RouteBase> get routes;
  
  /// Initialize the module (setup services, providers, etc.)
  Future<void> initialize();
  
  /// Dispose of module resources
  Future<void> dispose();
  
  /// Whether this module can run as a standalone app
  bool get canRunStandalone;
  
  /// List of other modules required for this module to function
  List<String> get requiredModules;
  
  /// Get module-specific theme data
  ThemeData getThemeData(BuildContext context) {
    return Theme.of(context).copyWith(
      colorScheme: Theme.of(context).colorScheme.copyWith(
        primary: primaryColor,
      ),
    );
  }
  
  /// Validate if the module can be exported standalone
  bool canExportStandalone() {
    return canRunStandalone && requiredModules.isEmpty;
  }
  
  /// Get export configuration with defaults
  Map<String, dynamic> getExportConfig() {
    final config = Map<String, dynamic>.from(exportConfig);
    
    // Add default values if not specified
    config.putIfAbsent('appName', () => name);
    config.putIfAbsent('packageName', () => 'com.shadowsuite.${id.toLowerCase()}');
    config.putIfAbsent('description', () => description);
    config.putIfAbsent('minSdkVersion', () => 21);
    config.putIfAbsent('targetSdkVersion', () => 34);
    config.putIfAbsent('versionCode', () => 1);
    config.putIfAbsent('versionName', () => version);
    
    return config;
  }
}

/// Registry for all app modules
class AppModuleRegistry {
  static final Map<String, AppModuleInterface> _modules = {};
  
  /// Register a module
  static void register(AppModuleInterface module) {
    _modules[module.id] = module;
  }
  
  /// Get a module by ID
  static AppModuleInterface? getModule(String id) {
    return _modules[id];
  }
  
  /// Get all registered modules
  static List<AppModuleInterface> getAllModules() {
    return _modules.values.toList();
  }
  
  /// Get modules that can run standalone
  static List<AppModuleInterface> getStandaloneModules() {
    return _modules.values.where((module) => module.canRunStandalone).toList();
  }
  
  /// Get modules that can be exported standalone
  static List<AppModuleInterface> getExportableModules() {
    return _modules.values.where((module) => module.canExportStandalone()).toList();
  }
  
  /// Initialize all modules
  static Future<void> initializeAll() async {
    for (final module in _modules.values) {
      try {
        await module.initialize();
      } catch (e) {
        debugPrint('Error initializing module ${module.id}: $e');
      }
    }
  }
  
  /// Dispose all modules
  static Future<void> disposeAll() async {
    for (final module in _modules.values) {
      try {
        await module.dispose();
      } catch (e) {
        debugPrint('Error disposing module ${module.id}: $e');
      }
    }
  }
  
  /// Clear all registered modules
  static void clear() {
    _modules.clear();
  }
}
