import 'package:flutter/material.dart' as material;
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/settings/providers/settings_provider.dart';
import '../../features/settings/models/app_settings.dart';
import 'app_theme.dart';

// Theme provider that watches settings and system brightness
final themeProvider = Provider<material.ThemeData>((ref) {
  final settings = ref.watch(settingsProvider);
  final platformBrightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;

  return AppTheme.getTheme(settings, platformBrightness);
});

// Dark theme provider
final darkThemeProvider = Provider<material.ThemeData>((ref) {
  final settings = ref.watch(settingsProvider);
  return settings.highContrastMode
      ? AppTheme.highContrastDarkTheme(settings)
      : AppTheme.darkTheme(settings);
});

// Current brightness provider
final brightnessProvider = Provider<material.Brightness>((ref) {
  final settings = ref.watch(settingsProvider);
  final platformBrightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;

  switch (settings.themeMode) {
    case ThemeMode.light:
      return material.Brightness.light;
    case ThemeMode.dark:
      return material.Brightness.dark;
    case ThemeMode.system:
      return platformBrightness;
  }
});

// Theme mode provider (for MaterialApp)
final themeModeProvider = Provider<material.ThemeMode>((ref) {
  final settings = ref.watch(settingsProvider);

  switch (settings.themeMode) {
    case ThemeMode.light:
      return material.ThemeMode.light;
    case ThemeMode.dark:
      return material.ThemeMode.dark;
    case ThemeMode.system:
      return material.ThemeMode.system;
  }
});

// Color scheme provider
final colorSchemeProvider = Provider<material.ColorScheme>((ref) {
  final theme = ref.watch(themeProvider);
  return theme.colorScheme;
});

// Text theme provider
final textThemeProvider = Provider<material.TextTheme>((ref) {
  final theme = ref.watch(themeProvider);
  return theme.textTheme;
});

// Accessibility provider
final accessibilityThemeProvider = Provider<AccessibilityTheme>((ref) {
  final settings = ref.watch(settingsProvider);
  return AccessibilityTheme(
    highContrast: settings.highContrastMode,
    largeText: settings.largeTextMode,
    reduceMotion: settings.reduceMotion,
    buttonSize: settings.buttonSize,
    showTooltips: settings.showTooltips,
  );
});

// Performance provider
final performanceThemeProvider = Provider<PerformanceTheme>((ref) {
  final settings = ref.watch(settingsProvider);
  return PerformanceTheme(
    animationsEnabled: settings.animationsEnabled,
    reducedAnimations: settings.reduceMotion,
    lowPowerMode: settings.lowPowerMode,
  );
});

// Helper classes
class AccessibilityTheme {
  final bool highContrast;
  final bool largeText;
  final bool reduceMotion;
  final double buttonSize;
  final bool showTooltips;

  const AccessibilityTheme({
    required this.highContrast,
    required this.largeText,
    required this.reduceMotion,
    required this.buttonSize,
    required this.showTooltips,
  });

  AccessibilityTheme copyWith({
    bool? highContrast,
    bool? largeText,
    bool? reduceMotion,
    double? buttonSize,
    bool? showTooltips,
  }) {
    return AccessibilityTheme(
      highContrast: highContrast ?? this.highContrast,
      largeText: largeText ?? this.largeText,
      reduceMotion: reduceMotion ?? this.reduceMotion,
      buttonSize: buttonSize ?? this.buttonSize,
      showTooltips: showTooltips ?? this.showTooltips,
    );
  }
}

class PerformanceTheme {
  final bool animationsEnabled;
  final bool reducedAnimations;
  final bool lowPowerMode;

  const PerformanceTheme({
    required this.animationsEnabled,
    required this.reducedAnimations,
    required this.lowPowerMode,
  });

  PerformanceTheme copyWith({
    bool? animationsEnabled,
    bool? reducedAnimations,
    bool? lowPowerMode,
  }) {
    return PerformanceTheme(
      animationsEnabled: animationsEnabled ?? this.animationsEnabled,
      reducedAnimations: reducedAnimations ?? this.reducedAnimations,
      lowPowerMode: lowPowerMode ?? this.lowPowerMode,
    );
  }

  Duration get animationDuration {
    if (!animationsEnabled || reducedAnimations) {
      return Duration.zero;
    }
    return lowPowerMode 
        ? const Duration(milliseconds: 150)
        : const Duration(milliseconds: 300);
  }

  material.Curve get animationCurve {
    if (!animationsEnabled || reducedAnimations) {
      return material.Curves.linear;
    }
    return lowPowerMode ? material.Curves.easeInOut : material.Curves.easeInOutCubic;
  }
}

// Theme utilities
class ThemeUtils {
  static bool isDarkMode(material.BuildContext context) {
    return material.Theme.of(context).brightness == material.Brightness.dark;
  }

  static material.Color getContrastingColor(material.BuildContext context, material.Color color) {
    final theme = material.Theme.of(context);
    final luminance = color.computeLuminance();

    if (luminance > 0.5) {
      return theme.colorScheme.onSurface;
    } else {
      return theme.colorScheme.surface;
    }
  }

  // These methods would need WidgetRef instead of BuildContext
  // They are commented out for now to fix compilation
  /*
  static EdgeInsets getAccessiblePadding(WidgetRef ref, EdgeInsets basePadding) {
    final accessibility = ref.read(accessibilityThemeProvider);
    final multiplier = accessibility.buttonSize;

    return EdgeInsets.only(
      left: basePadding.left * multiplier,
      top: basePadding.top * multiplier,
      right: basePadding.right * multiplier,
      bottom: basePadding.bottom * multiplier,
    );
  }

  static double getAccessibleSize(WidgetRef ref, double baseSize) {
    final accessibility = ref.read(accessibilityThemeProvider);
    return baseSize * accessibility.buttonSize;
  }

  static Duration getAnimationDuration(WidgetRef ref) {
    final performance = ref.read(performanceThemeProvider);
    return performance.animationDuration;
  }

  static Curve getAnimationCurve(WidgetRef ref) {
    final performance = ref.read(performanceThemeProvider);
    return performance.animationCurve;
  }
  */

  // These methods are commented out to fix compilation issues
  // They would need to be refactored to use WidgetRef
  /*
  static Widget buildAccessibleWidget({
    required WidgetRef ref,
    required Widget child,
    String? tooltip,
    String? semanticsLabel,
    VoidCallback? onTap,
  }) {
    final accessibility = ref.read(accessibilityThemeProvider);

    Widget widget = child;

    // Add semantics
    if (semanticsLabel != null) {
      widget = Semantics(
        label: semanticsLabel,
        child: widget,
      );
    }

    // Add tooltip if enabled
    if (tooltip != null && accessibility.showTooltips) {
      widget = Tooltip(
        message: tooltip,
        child: widget,
      );
    }

    // Add tap handler if provided
    if (onTap != null) {
      widget = InkWell(
        onTap: onTap,
        child: widget,
      );
    }

    return widget;
  }

  static Widget buildAnimatedWidget({
    required WidgetRef ref,
    required Widget child,
    Duration? duration,
    Curve? curve,
  }) {
    final performance = ref.read(performanceThemeProvider);

    if (!performance.animationsEnabled) {
      return child;
    }

    return AnimatedContainer(
      duration: duration ?? performance.animationDuration,
      curve: curve ?? performance.animationCurve,
      child: child,
    );
  }
  */
}

// Extension methods - commented out to fix compilation
/*
extension BuildContextThemeExtensions on BuildContext {
  ThemeData get theme => Theme.of(this);
  ColorScheme get colorScheme => theme.colorScheme;
  TextTheme get textTheme => theme.textTheme;

  // These would need WidgetRef instead
  // AccessibilityTheme get accessibility => read(accessibilityThemeProvider);
  // PerformanceTheme get performance => read(performanceThemeProvider);

  bool get isDarkMode => theme.brightness == Brightness.dark;
  // bool get isHighContrast => accessibility.highContrast;
  // bool get isLargeText => accessibility.largeText;
  // bool get shouldReduceMotion => accessibility.reduceMotion;

  // Duration get animationDuration => performance.animationDuration;
  // Curve get animationCurve => performance.animationCurve;
}
*/

// Material ThemeMode alias to avoid conflicts
// typedef MaterialThemeMode = material.ThemeMode;
