import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/voice_memo.dart';
import '../providers/voice_memo_provider.dart';
import '../widgets/voice_recorder_widget.dart';
// import '../widgets/voice_memo_player.dart';
// import '../widgets/voice_memo_recorder.dart';

class EnhancedVoiceMemoScreen extends ConsumerStatefulWidget {
  const EnhancedVoiceMemoScreen({super.key});

  @override
  ConsumerState<EnhancedVoiceMemoScreen> createState() => _EnhancedVoiceMemoScreenState();
}

class _EnhancedVoiceMemoScreenState extends ConsumerState<EnhancedVoiceMemoScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String _selectedCategory = 'all';
  bool _showFavoritesOnly = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Memos'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showSearchDialog,
            icon: const Icon(Icons.search),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Recording Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_all',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export All'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'analytics',
                child: Row(
                  children: [
                    Icon(Icons.analytics),
                    SizedBox(width: 8),
                    Text('Analytics'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'All', icon: Icon(Icons.list, size: 16)),
            Tab(text: 'Record', icon: Icon(Icons.mic, size: 16)),
            Tab(text: 'Categories', icon: Icon(Icons.category, size: 16)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllMemosTab(),
          _buildRecordTab(),
          _buildCategoriesTab(),
        ],
      ),
      floatingActionButton: _tabController.index == 0 ? FloatingActionButton(
        onPressed: () => _tabController.animateTo(1),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        child: const Icon(Icons.mic),
      ) : null,
    );
  }

  Widget _buildAllMemosTab() {
    return Column(
      children: [
        _buildFilterBar(),
        Expanded(child: _buildMemosList()),
      ],
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search voice memos...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      onPressed: () => setState(() => _searchQuery = ''),
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          const SizedBox(height: 12),
          
          // Filter chips
          Row(
            children: [
              FilterChip(
                label: const Text('Favorites'),
                selected: _showFavoritesOnly,
                onSelected: (selected) => setState(() => _showFavoritesOnly = selected),
                selectedColor: Colors.orange.withValues(alpha: 0.2),
                checkmarkColor: Colors.orange,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _buildCategoryChips(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> _buildCategoryChips() {
    final categories = ['general', 'work', 'personal', 'ideas']; // Placeholder categories
    return [
      FilterChip(
        label: const Text('All'),
        selected: _selectedCategory == 'all',
        onSelected: (selected) => setState(() => _selectedCategory = 'all'),
        selectedColor: Colors.orange.withValues(alpha: 0.2),
        checkmarkColor: Colors.orange,
      ),
      const SizedBox(width: 8),
      ...categories.map((category) => Padding(
        padding: const EdgeInsets.only(right: 8),
        child: FilterChip(
          label: Text(category),
          selected: _selectedCategory == category,
          onSelected: (selected) => setState(() => _selectedCategory = selected ? category : 'all'),
          selectedColor: Colors.orange.withValues(alpha: 0.2),
          checkmarkColor: Colors.orange,
        ),
      )),
    ];
  }

  Widget _buildMemosList() {
    final allMemos = ref.watch(voiceMemosProvider);
    
    // Apply filters
    var filteredMemos = allMemos.where((memo) {
      if (_showFavoritesOnly && !memo.isFavorite) return false;
      if (_selectedCategory != 'all' && memo.category != _selectedCategory) return false;
      if (_searchQuery.isNotEmpty && !memo.containsSearchTerm(_searchQuery)) return false;
      return true;
    }).toList();

    // Sort by creation date (newest first)
    filteredMemos.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    if (filteredMemos.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredMemos.length,
      itemBuilder: (context, index) {
        final memo = filteredMemos[index];
        return VoiceMemoCard(
          memo: memo,
          onTap: () => _playMemo(memo),
          onEdit: () => _editMemo(memo),
          onDelete: () => _deleteMemo(memo),
          onShare: () => _shareMemo(memo),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic_none,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No voice memos yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the Record tab to create your first voice memo',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecordTab() {
    return const VoiceRecorderWidget();
  }

  Widget _buildCategoriesTab() {
    final categories = ['general', 'work', 'personal', 'ideas']; // Placeholder categories
    final stats = ref.watch(voiceMemoStatsProvider);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Statistics cards
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard('Total Memos', stats['total']!, Icons.mic, Colors.orange),
            _buildStatCard('Favorites', stats['favorites']!, Icons.favorite, Colors.red),
            _buildStatCard('Total Duration', '${(stats['totalDuration']! / 60000).round()}m', Icons.timer, Colors.blue),
            _buildStatCard('Categories', categories.length, Icons.category, Colors.green),
          ],
        ),
        const SizedBox(height: 24),
        
        // Categories list
        const Text(
          'Categories',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...categories.map((category) {
          final count = ref.watch(voiceMemosProvider).where((memo) => memo.category == category).length;
          return Card(
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.orange.withValues(alpha: 0.2),
                child: const Icon(Icons.folder, color: Colors.orange),
              ),
              title: Text(category),
              subtitle: Text('$count memos'),
              trailing: PopupMenuButton<String>(
                onSelected: (action) => _handleCategoryAction(category, action),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'rename',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('Rename'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
              onTap: () => setState(() => _selectedCategory = category),
            ),
          );
        }),
        
        // Add category button
        Card(
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.green.withValues(alpha: 0.2),
              child: const Icon(Icons.add, color: Colors.green),
            ),
            title: const Text('Add Category'),
            onTap: _addCategory,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, dynamic value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              '$value',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Voice Memos'),
        content: TextField(
          decoration: const InputDecoration(
            labelText: 'Search query',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) => setState(() => _searchQuery = value),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        _showRecordingSettings();
        break;
      case 'export_all':
        _exportAllMemos();
        break;
      case 'analytics':
        _showAnalytics();
        break;
    }
  }

  void _handleCategoryAction(String category, String action) {
    switch (action) {
      case 'rename':
        _renameCategory(category);
        break;
      case 'delete':
        _deleteCategory(category);
        break;
    }
  }

  void _playMemo(VoiceMemo memo) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Playing: ${memo.title}', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            const Text('Voice memo player coming soon!'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _editMemo(VoiceMemo memo) {
    // TODO: Implement memo editing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit memo feature coming soon!')),
    );
  }

  void _deleteMemo(VoiceMemo memo) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Voice Memo'),
        content: Text('Are you sure you want to delete "${memo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(voiceMemosProvider.notifier).deleteVoiceMemo(memo.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Voice memo deleted')),
        );
      }
    }
  }

  void _shareMemo(VoiceMemo memo) {
    // TODO: Implement memo sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share memo feature coming soon!')),
    );
  }

  void _addCategory() {
    // TODO: Implement add category
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add category feature coming soon!')),
    );
  }

  void _renameCategory(String category) {
    // TODO: Implement rename category
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Rename category feature coming soon!')),
    );
  }

  void _deleteCategory(String category) {
    // TODO: Implement delete category
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Delete category feature coming soon!')),
    );
  }

  void _showRecordingSettings() {
    // TODO: Implement recording settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Recording settings coming soon!')),
    );
  }

  void _exportAllMemos() {
    // TODO: Implement export all
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export all feature coming soon!')),
    );
  }

  void _showAnalytics() {
    // TODO: Implement analytics
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Analytics feature coming soon!')),
    );
  }
}

// Placeholder widgets - these would be implemented separately
class VoiceMemoCard extends StatelessWidget {
  final VoiceMemo memo;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onShare;

  const VoiceMemoCard({
    super.key,
    required this.memo,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    required this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: memo.color.withValues(alpha: 0.2),
          child: Icon(Icons.mic, color: memo.color),
        ),
        title: Text(memo.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(memo.formattedDuration),
            Text(memo.category),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (memo.isFavorite) const Icon(Icons.favorite, color: Colors.red, size: 16),
            PopupMenuButton<String>(
              onSelected: (action) {
                switch (action) {
                  case 'edit':
                    onEdit();
                    break;
                  case 'share':
                    onShare();
                    break;
                  case 'delete':
                    onDelete();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'share', child: Text('Share')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }
}
