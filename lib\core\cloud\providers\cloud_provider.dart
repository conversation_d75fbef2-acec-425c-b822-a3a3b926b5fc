import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;
import '../services/supabase_service.dart';

// Supabase service provider
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService();
});

// Auth state stream provider
final authStateProvider = StreamProvider<AuthState>((ref) {
  final service = ref.watch(supabaseServiceProvider);
  return service.authStateStream;
});

// Sync events stream provider
final syncEventsProvider = StreamProvider<SyncEvent>((ref) {
  final service = ref.watch(supabaseServiceProvider);
  return service.syncEventStream;
});

// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authStateAsync = ref.watch(authStateProvider);
  return authStateAsync.when(
    data: (authState) => authState.user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Is authenticated provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});

// Cloud sync actions provider
final cloudSyncActionsProvider = Provider<CloudSyncActions>((ref) {
  final service = ref.watch(supabaseServiceProvider);
  return CloudSyncActions(service);
});

// Cloud sync helper class
class CloudSyncActions {
  final SupabaseService _service;

  CloudSyncActions(this._service);

  // Authentication
  Future<AuthResponse> signInAnonymously() async {
    return await _service.signInAnonymously();
  }

  Future<AuthResponse> signInWithEmail(String email, String password) async {
    return await _service.signInWithEmail(email, password);
  }

  Future<AuthResponse> signUpWithEmail(String email, String password) async {
    return await _service.signUpWithEmail(email, password);
  }

  Future<void> signOut() async {
    await _service.signOut();
  }

  // Notes sync
  Future<List<Map<String, dynamic>>> syncNotes() async {
    return await _service.syncNotes();
  }

  Future<Map<String, dynamic>> uploadNote(Map<String, dynamic> note) async {
    return await _service.upsertNote(note);
  }

  Future<void> deleteNoteFromCloud(String noteId) async {
    await _service.deleteNote(noteId);
  }

  // Todos sync
  Future<List<Map<String, dynamic>>> syncTodos() async {
    return await _service.syncTodos();
  }

  Future<Map<String, dynamic>> uploadTodo(Map<String, dynamic> todo) async {
    return await _service.upsertTodo(todo);
  }

  Future<void> deleteTodoFromCloud(String todoId) async {
    await _service.deleteTodo(todoId);
  }

  // Voice memos sync
  Future<List<Map<String, dynamic>>> syncVoiceMemos() async {
    return await _service.syncVoiceMemos();
  }

  Future<Map<String, dynamic>> uploadVoiceMemo(Map<String, dynamic> memo) async {
    return await _service.upsertVoiceMemo(memo);
  }

  // Dhikr routines sync
  Future<List<Map<String, dynamic>>> syncDhikrRoutines() async {
    return await _service.syncDhikrRoutines();
  }

  Future<Map<String, dynamic>> uploadDhikrRoutine(Map<String, dynamic> routine) async {
    return await _service.upsertDhikrRoutine(routine);
  }

  // Settings sync
  Future<Map<String, dynamic>?> syncSettings() async {
    return await _service.syncSettings();
  }

  Future<Map<String, dynamic>> uploadSettings(Map<String, dynamic> settings) async {
    return await _service.upsertSettings(settings);
  }

  // Backup operations
  Future<Map<String, dynamic>> createFullBackup() async {
    return await _service.createFullBackup();
  }

  Future<List<Map<String, dynamic>>> getBackups() async {
    return await _service.getBackups();
  }

  Future<Map<String, dynamic>> restoreBackup(String backupId) async {
    return await _service.restoreBackup(backupId);
  }

  // File operations
  Future<String> uploadFile(String bucket, String path, List<int> data) async {
    return await _service.uploadFile(bucket, path, data);
  }

  Future<List<int>> downloadFile(String bucket, String path) async {
    return await _service.downloadFile(bucket, path);
  }

  Future<void> deleteFile(String bucket, String path) async {
    await _service.deleteFile(bucket, path);
  }

  // Voice memo file operations
  Future<String> uploadVoiceMemoFile(String memoId, List<int> audioData) async {
    final fileName = 'voice_memo_$memoId.aac';
    return await uploadFile('voice-memos', fileName, audioData);
  }

  Future<List<int>> downloadVoiceMemoFile(String memoId) async {
    final fileName = 'voice_memo_$memoId.aac';
    return await downloadFile('voice-memos', fileName);
  }

  Future<void> deleteVoiceMemoFile(String memoId) async {
    final fileName = 'voice_memo_$memoId.aac';
    await deleteFile('voice-memos', fileName);
  }

  // Batch operations
  Future<void> syncAllData() async {
    if (!_service.isAuthenticated) {
      throw Exception('User not authenticated');
    }

    try {
      await Future.wait([
        syncNotes(),
        syncTodos(),
        syncVoiceMemos(),
        syncDhikrRoutines(),
        syncSettings(),
      ]);
    } catch (e) {
      throw Exception('Failed to sync all data: $e');
    }
  }

  Future<void> uploadAllLocalData({
    List<Map<String, dynamic>>? notes,
    List<Map<String, dynamic>>? todos,
    List<Map<String, dynamic>>? voiceMemos,
    List<Map<String, dynamic>>? dhikrRoutines,
    Map<String, dynamic>? settings,
  }) async {
    if (!_service.isAuthenticated) {
      throw Exception('User not authenticated');
    }

    try {
      // Upload notes
      if (notes != null) {
        for (final note in notes) {
          await uploadNote(note);
        }
      }

      // Upload todos
      if (todos != null) {
        for (final todo in todos) {
          await uploadTodo(todo);
        }
      }

      // Upload voice memos
      if (voiceMemos != null) {
        for (final memo in voiceMemos) {
          await uploadVoiceMemo(memo);
        }
      }

      // Upload dhikr routines
      if (dhikrRoutines != null) {
        for (final routine in dhikrRoutines) {
          await uploadDhikrRoutine(routine);
        }
      }

      // Upload settings
      if (settings != null) {
        await uploadSettings(settings);
      }
    } catch (e) {
      throw Exception('Failed to upload local data: $e');
    }
  }

  // Conflict resolution helpers
  Future<Map<String, dynamic>> resolveNoteConflict(
    Map<String, dynamic> localNote,
    Map<String, dynamic> remoteNote,
    ConflictResolution resolution,
  ) async {
    switch (resolution) {
      case ConflictResolution.useLocal:
        return await uploadNote(localNote);
      case ConflictResolution.useRemote:
        return remoteNote;
      case ConflictResolution.merge:
        final mergedNote = _mergeNotes(localNote, remoteNote);
        return await uploadNote(mergedNote);
      default:
        throw Exception('Invalid conflict resolution');
    }
  }

  Future<Map<String, dynamic>> resolveTodoConflict(
    Map<String, dynamic> localTodo,
    Map<String, dynamic> remoteTodo,
    ConflictResolution resolution,
  ) async {
    switch (resolution) {
      case ConflictResolution.useLocal:
        return await uploadTodo(localTodo);
      case ConflictResolution.useRemote:
        return remoteTodo;
      case ConflictResolution.merge:
        final mergedTodo = _mergeTodos(localTodo, remoteTodo);
        return await uploadTodo(mergedTodo);
      default:
        throw Exception('Invalid conflict resolution');
    }
  }

  // Private merge methods
  Map<String, dynamic> _mergeNotes(
    Map<String, dynamic> local,
    Map<String, dynamic> remote,
  ) {
    final localModified = DateTime.parse(local['modified_at']);
    final remoteModified = DateTime.parse(remote['modified_at']);

    // Use the most recently modified version as base
    final base = localModified.isAfter(remoteModified) ? local : remote;
    final other = localModified.isAfter(remoteModified) ? remote : local;

    return {
      ...base,
      'content': '${base['content']}\n\n--- Merged from other device ---\n${other['content']}',
      'modified_at': DateTime.now().toIso8601String(),
    };
  }

  Map<String, dynamic> _mergeTodos(
    Map<String, dynamic> local,
    Map<String, dynamic> remote,
  ) {
    final localModified = DateTime.parse(local['modified_at']);
    final remoteModified = DateTime.parse(remote['modified_at']);

    // For todos, prefer the most recently modified version
    return localModified.isAfter(remoteModified) ? local : remote;
  }

  // Status getters
  bool get isAuthenticated => _service.isAuthenticated;
  User? get currentUser => _service.currentUser;
  bool get isInitialized => _service.isInitialized;
}

// Conflict resolution enum
enum ConflictResolution {
  useLocal,
  useRemote,
  merge,
}

// Backup list provider
final backupsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final actions = ref.watch(cloudSyncActionsProvider);
  if (!actions.isAuthenticated) return [];
  return await actions.getBackups();
});

// Auto-sync provider (triggers sync when authenticated)
final autoSyncProvider = FutureProvider<void>((ref) async {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  if (!isAuthenticated) return;

  final actions = ref.watch(cloudSyncActionsProvider);
  try {
    await actions.syncAllData();
  } catch (e) {
    // Handle sync errors silently for auto-sync
    print('Auto-sync failed: $e');
  }
});

// Initialize Supabase service provider
final initializeSupabaseServiceProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(supabaseServiceProvider);
  await service.initialize();
});
