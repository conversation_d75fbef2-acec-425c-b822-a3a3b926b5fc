import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'ShadowSuite'**
  String get appTitle;

  /// Dashboard screen title
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// Athkar/Dhikr screen title
  ///
  /// In en, this message translates to:
  /// **'Athkar'**
  String get athkar;

  /// Quran screen title
  ///
  /// In en, this message translates to:
  /// **'Quran'**
  String get quran;

  /// Prayer times screen title
  ///
  /// In en, this message translates to:
  /// **'Prayer Times'**
  String get prayerTimes;

  /// Tools screen title
  ///
  /// In en, this message translates to:
  /// **'Tools'**
  String get tools;

  /// Memo suite screen title
  ///
  /// In en, this message translates to:
  /// **'Memo Suite'**
  String get memo;

  /// Money flow screen title
  ///
  /// In en, this message translates to:
  /// **'Money Flow'**
  String get moneyFlow;

  /// Settings screen title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Profile screen title
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Dhikr tab title
  ///
  /// In en, this message translates to:
  /// **'Dhikr'**
  String get dhikr;

  /// Progress tab title
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get progress;

  /// Notes section title
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// Todos section title
  ///
  /// In en, this message translates to:
  /// **'Todos'**
  String get todos;

  /// Voice memos section title
  ///
  /// In en, this message translates to:
  /// **'Voice Memos'**
  String get voiceMemos;

  /// Calculator tool title
  ///
  /// In en, this message translates to:
  /// **'Calculator'**
  String get calculator;

  /// Converter tool title
  ///
  /// In en, this message translates to:
  /// **'Converter'**
  String get converter;

  /// Tool builder title
  ///
  /// In en, this message translates to:
  /// **'Tool Builder'**
  String get toolBuilder;

  /// Accounts section title
  ///
  /// In en, this message translates to:
  /// **'Accounts'**
  String get accounts;

  /// Transactions section title
  ///
  /// In en, this message translates to:
  /// **'Transactions'**
  String get transactions;

  /// Budgets section title
  ///
  /// In en, this message translates to:
  /// **'Budgets'**
  String get budgets;

  /// Reports section title
  ///
  /// In en, this message translates to:
  /// **'Reports'**
  String get reports;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Yes button text
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No button text
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// Loading text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Warning message
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// Information message
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get info;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Filter button text
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Sort button text
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// Refresh button text
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// Share button text
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// Export button text
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// Import button text
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get import;

  /// Backup button text
  ///
  /// In en, this message translates to:
  /// **'Backup'**
  String get backup;

  /// Restore button text
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get restore;

  /// Sync button text
  ///
  /// In en, this message translates to:
  /// **'Sync'**
  String get sync;

  /// Sign in button text
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// Sign up button text
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// Sign out button text
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get signOut;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Confirm password field label
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// Name field label
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Description field label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Date field label
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Time field label
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// Amount field label
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Priority field label
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// Status field label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Language setting label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Theme setting label
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// Notifications setting label
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Privacy setting label
  ///
  /// In en, this message translates to:
  /// **'Privacy'**
  String get privacy;

  /// Security setting label
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get security;

  /// About section label
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// Version label
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// Help section label
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get help;

  /// Support section label
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// Feedback section label
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// Rate app button text
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get rateApp;

  /// Translation settings title
  ///
  /// In en, this message translates to:
  /// **'Translation Settings'**
  String get translationSettings;

  /// Display mode setting label
  ///
  /// In en, this message translates to:
  /// **'Display Mode'**
  String get displayMode;

  /// Arabic only display mode
  ///
  /// In en, this message translates to:
  /// **'Arabic Only'**
  String get arabicOnly;

  /// Arabic with translation display mode
  ///
  /// In en, this message translates to:
  /// **'Arabic + Translation'**
  String get arabicWithTranslation;

  /// Arabic with transliteration display mode
  ///
  /// In en, this message translates to:
  /// **'Arabic + Transliteration'**
  String get arabicWithTransliteration;

  /// Arabic with both translation and transliteration display mode
  ///
  /// In en, this message translates to:
  /// **'Arabic + Translation + Transliteration'**
  String get arabicWithBoth;

  /// Translation only display mode
  ///
  /// In en, this message translates to:
  /// **'Translation Only'**
  String get translationOnly;

  /// Font size setting label
  ///
  /// In en, this message translates to:
  /// **'Font Size'**
  String get fontSize;

  /// Font family setting label
  ///
  /// In en, this message translates to:
  /// **'Font Family'**
  String get fontFamily;

  /// Show reference setting label
  ///
  /// In en, this message translates to:
  /// **'Show Reference'**
  String get showReference;

  /// Show recommended count setting label
  ///
  /// In en, this message translates to:
  /// **'Show Recommended Count'**
  String get showRecommendedCount;

  /// Right-to-left text setting label
  ///
  /// In en, this message translates to:
  /// **'Right-to-Left Text'**
  String get rightToLeftText;

  /// Reset to defaults button text
  ///
  /// In en, this message translates to:
  /// **'Reset to Defaults'**
  String get resetToDefaults;

  /// Completed tasks section title with count
  ///
  /// In en, this message translates to:
  /// **'Completed Tasks ({count})'**
  String completedTasks(int count);

  /// Active tasks section title with count
  ///
  /// In en, this message translates to:
  /// **'Active Tasks ({count})'**
  String activeTasks(int count);

  /// Clear completed tasks button text
  ///
  /// In en, this message translates to:
  /// **'Clear Completed'**
  String get clearCompleted;

  /// Duplicate button text
  ///
  /// In en, this message translates to:
  /// **'Duplicate'**
  String get duplicate;

  /// Tap to count button text
  ///
  /// In en, this message translates to:
  /// **'Tap to Count'**
  String get tapToCount;

  /// Completed status text
  ///
  /// In en, this message translates to:
  /// **'Completed!'**
  String get completed;

  /// Next prayer label
  ///
  /// In en, this message translates to:
  /// **'Next Prayer'**
  String get nextPrayer;

  /// Today's prayer times title
  ///
  /// In en, this message translates to:
  /// **'Today\'s Prayer Times'**
  String get todaysPrayerTimes;

  /// Current location label
  ///
  /// In en, this message translates to:
  /// **'Current Location'**
  String get currentLocation;

  /// Location settings title
  ///
  /// In en, this message translates to:
  /// **'Location Settings'**
  String get locationSettings;

  /// Fetch from API button text
  ///
  /// In en, this message translates to:
  /// **'Fetch from API'**
  String get fetchFromAPI;

  /// Use sample data button text
  ///
  /// In en, this message translates to:
  /// **'Use Sample'**
  String get useSample;

  /// Update times button text
  ///
  /// In en, this message translates to:
  /// **'Update Times'**
  String get updateTimes;

  /// Change location button text
  ///
  /// In en, this message translates to:
  /// **'Change Location'**
  String get changeLocation;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
