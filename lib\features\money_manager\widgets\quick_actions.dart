import 'package:flutter/material.dart';

/// Quick actions widget for common Money Manager operations
class QuickActions extends StatelessWidget {
  final VoidCallback? onAddIncome;
  final VoidCallback? onAddExpense;
  final VoidCallback? onTransfer;
  final VoidCallback? onViewReports;

  const QuickActions({
    super.key,
    this.onAddIncome,
    this.onAddExpense,
    this.onTransfer,
    this.onViewReports,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildActionCard(
            context,
            'Add Income',
            Icons.add_circle,
            Colors.green,
            onAddIncome,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildActionCard(
            context,
            'Add Expense',
            Icons.remove_circle,
            Colors.red,
            onAddExpense,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildActionCard(
            context,
            'Transfer',
            Icons.swap_horiz,
            Colors.blue,
            onTransfer,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildActionCard(
            context,
            'Reports',
            Icons.analytics,
            Colors.purple,
            onViewReports,
          ),
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
