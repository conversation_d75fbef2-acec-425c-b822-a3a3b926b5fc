import 'package:flutter/foundation.dart';

/// Currency symbol position options
enum CurrencySymbolPosition {
  before,
  after,
  beforeWithSpace,
  afterWithSpace,
}

/// Money Flow application settings
@immutable
class MoneySettings {
  // Currency Settings
  final String primaryCurrency;
  final CurrencySymbolPosition currencySymbolPosition;
  final int decimalPlaces;
  final bool useThousandSeparator;
  final String thousandSeparator;
  final String decimalSeparator;

  // Default Settings
  final String defaultAccountId;
  final bool autoCategorize;
  final bool rememberLastCategory;
  final bool rememberLastAccount;

  // Notification Settings
  final bool enableBudgetAlerts;
  final bool enableTransactionReminders;
  final bool enableLowBalanceAlerts;
  final double lowBalanceThreshold;
  final int reminderTime; // Hour of day (0-23)

  // Display Settings
  final String dateFormat;
  final String timeFormat;
  final bool showAccountBalanceOnDashboard;
  final bool showRecentTransactionsOnDashboard;
  final int dashboardTransactionCount;

  // Privacy & Security
  final bool requirePin;
  final String pinHash;
  final bool hideAmountsInOverview;
  final bool enableBiometricAuth;
  final int autoLockMinutes;

  // Data Management
  final bool enableAutoBackup;
  final int backupFrequencyDays;
  final String lastBackupDate;
  final bool enableDataSync;

  // Advanced Settings
  final bool enableAdvancedReports;
  final bool enableBudgetForecasting;
  final bool enableCategoryInsights;
  final bool enableSpendingTrends;

  const MoneySettings({
    // Currency Settings
    this.primaryCurrency = 'USD',
    this.currencySymbolPosition = CurrencySymbolPosition.before,
    this.decimalPlaces = 2,
    this.useThousandSeparator = true,
    this.thousandSeparator = ',',
    this.decimalSeparator = '.',

    // Default Settings
    this.defaultAccountId = '',
    this.autoCategorize = true,
    this.rememberLastCategory = true,
    this.rememberLastAccount = true,

    // Notification Settings
    this.enableBudgetAlerts = true,
    this.enableTransactionReminders = false,
    this.enableLowBalanceAlerts = true,
    this.lowBalanceThreshold = 100.0,
    this.reminderTime = 20, // 8 PM

    // Display Settings
    this.dateFormat = 'MM/dd/yyyy',
    this.timeFormat = '12h',
    this.showAccountBalanceOnDashboard = true,
    this.showRecentTransactionsOnDashboard = true,
    this.dashboardTransactionCount = 5,

    // Privacy & Security
    this.requirePin = false,
    this.pinHash = '',
    this.hideAmountsInOverview = false,
    this.enableBiometricAuth = false,
    this.autoLockMinutes = 5,

    // Data Management
    this.enableAutoBackup = false,
    this.backupFrequencyDays = 7,
    this.lastBackupDate = '',
    this.enableDataSync = false,

    // Advanced Settings
    this.enableAdvancedReports = true,
    this.enableBudgetForecasting = true,
    this.enableCategoryInsights = true,
    this.enableSpendingTrends = true,
  });

  /// Create a copy with modified values
  MoneySettings copyWith({
    String? primaryCurrency,
    CurrencySymbolPosition? currencySymbolPosition,
    int? decimalPlaces,
    bool? useThousandSeparator,
    String? thousandSeparator,
    String? decimalSeparator,
    String? defaultAccountId,
    bool? autoCategorize,
    bool? rememberLastCategory,
    bool? rememberLastAccount,
    bool? enableBudgetAlerts,
    bool? enableTransactionReminders,
    bool? enableLowBalanceAlerts,
    double? lowBalanceThreshold,
    int? reminderTime,
    String? dateFormat,
    String? timeFormat,
    bool? showAccountBalanceOnDashboard,
    bool? showRecentTransactionsOnDashboard,
    int? dashboardTransactionCount,
    bool? requirePin,
    String? pinHash,
    bool? hideAmountsInOverview,
    bool? enableBiometricAuth,
    int? autoLockMinutes,
    bool? enableAutoBackup,
    int? backupFrequencyDays,
    String? lastBackupDate,
    bool? enableDataSync,
    bool? enableAdvancedReports,
    bool? enableBudgetForecasting,
    bool? enableCategoryInsights,
    bool? enableSpendingTrends,
  }) {
    return MoneySettings(
      primaryCurrency: primaryCurrency ?? this.primaryCurrency,
      currencySymbolPosition: currencySymbolPosition ?? this.currencySymbolPosition,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      useThousandSeparator: useThousandSeparator ?? this.useThousandSeparator,
      thousandSeparator: thousandSeparator ?? this.thousandSeparator,
      decimalSeparator: decimalSeparator ?? this.decimalSeparator,
      defaultAccountId: defaultAccountId ?? this.defaultAccountId,
      autoCategorize: autoCategorize ?? this.autoCategorize,
      rememberLastCategory: rememberLastCategory ?? this.rememberLastCategory,
      rememberLastAccount: rememberLastAccount ?? this.rememberLastAccount,
      enableBudgetAlerts: enableBudgetAlerts ?? this.enableBudgetAlerts,
      enableTransactionReminders: enableTransactionReminders ?? this.enableTransactionReminders,
      enableLowBalanceAlerts: enableLowBalanceAlerts ?? this.enableLowBalanceAlerts,
      lowBalanceThreshold: lowBalanceThreshold ?? this.lowBalanceThreshold,
      reminderTime: reminderTime ?? this.reminderTime,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      showAccountBalanceOnDashboard: showAccountBalanceOnDashboard ?? this.showAccountBalanceOnDashboard,
      showRecentTransactionsOnDashboard: showRecentTransactionsOnDashboard ?? this.showRecentTransactionsOnDashboard,
      dashboardTransactionCount: dashboardTransactionCount ?? this.dashboardTransactionCount,
      requirePin: requirePin ?? this.requirePin,
      pinHash: pinHash ?? this.pinHash,
      hideAmountsInOverview: hideAmountsInOverview ?? this.hideAmountsInOverview,
      enableBiometricAuth: enableBiometricAuth ?? this.enableBiometricAuth,
      autoLockMinutes: autoLockMinutes ?? this.autoLockMinutes,
      enableAutoBackup: enableAutoBackup ?? this.enableAutoBackup,
      backupFrequencyDays: backupFrequencyDays ?? this.backupFrequencyDays,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
      enableDataSync: enableDataSync ?? this.enableDataSync,
      enableAdvancedReports: enableAdvancedReports ?? this.enableAdvancedReports,
      enableBudgetForecasting: enableBudgetForecasting ?? this.enableBudgetForecasting,
      enableCategoryInsights: enableCategoryInsights ?? this.enableCategoryInsights,
      enableSpendingTrends: enableSpendingTrends ?? this.enableSpendingTrends,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'primaryCurrency': primaryCurrency,
      'currencySymbolPosition': currencySymbolPosition.index,
      'decimalPlaces': decimalPlaces,
      'useThousandSeparator': useThousandSeparator,
      'thousandSeparator': thousandSeparator,
      'decimalSeparator': decimalSeparator,
      'defaultAccountId': defaultAccountId,
      'autoCategorize': autoCategorize,
      'rememberLastCategory': rememberLastCategory,
      'rememberLastAccount': rememberLastAccount,
      'enableBudgetAlerts': enableBudgetAlerts,
      'enableTransactionReminders': enableTransactionReminders,
      'enableLowBalanceAlerts': enableLowBalanceAlerts,
      'lowBalanceThreshold': lowBalanceThreshold,
      'reminderTime': reminderTime,
      'dateFormat': dateFormat,
      'timeFormat': timeFormat,
      'showAccountBalanceOnDashboard': showAccountBalanceOnDashboard,
      'showRecentTransactionsOnDashboard': showRecentTransactionsOnDashboard,
      'dashboardTransactionCount': dashboardTransactionCount,
      'requirePin': requirePin,
      'pinHash': pinHash,
      'hideAmountsInOverview': hideAmountsInOverview,
      'enableBiometricAuth': enableBiometricAuth,
      'autoLockMinutes': autoLockMinutes,
      'enableAutoBackup': enableAutoBackup,
      'backupFrequencyDays': backupFrequencyDays,
      'lastBackupDate': lastBackupDate,
      'enableDataSync': enableDataSync,
      'enableAdvancedReports': enableAdvancedReports,
      'enableBudgetForecasting': enableBudgetForecasting,
      'enableCategoryInsights': enableCategoryInsights,
      'enableSpendingTrends': enableSpendingTrends,
    };
  }

  /// Create from JSON
  factory MoneySettings.fromJson(Map<String, dynamic> json) {
    return MoneySettings(
      primaryCurrency: json['primaryCurrency'] as String? ?? 'USD',
      currencySymbolPosition: CurrencySymbolPosition.values[
          json['currencySymbolPosition'] as int? ?? 0],
      decimalPlaces: json['decimalPlaces'] as int? ?? 2,
      useThousandSeparator: json['useThousandSeparator'] as bool? ?? true,
      thousandSeparator: json['thousandSeparator'] as String? ?? ',',
      decimalSeparator: json['decimalSeparator'] as String? ?? '.',
      defaultAccountId: json['defaultAccountId'] as String? ?? '',
      autoCategorize: json['autoCategorize'] as bool? ?? true,
      rememberLastCategory: json['rememberLastCategory'] as bool? ?? true,
      rememberLastAccount: json['rememberLastAccount'] as bool? ?? true,
      enableBudgetAlerts: json['enableBudgetAlerts'] as bool? ?? true,
      enableTransactionReminders: json['enableTransactionReminders'] as bool? ?? false,
      enableLowBalanceAlerts: json['enableLowBalanceAlerts'] as bool? ?? true,
      lowBalanceThreshold: (json['lowBalanceThreshold'] as num?)?.toDouble() ?? 100.0,
      reminderTime: json['reminderTime'] as int? ?? 20,
      dateFormat: json['dateFormat'] as String? ?? 'MM/dd/yyyy',
      timeFormat: json['timeFormat'] as String? ?? '12h',
      showAccountBalanceOnDashboard: json['showAccountBalanceOnDashboard'] as bool? ?? true,
      showRecentTransactionsOnDashboard: json['showRecentTransactionsOnDashboard'] as bool? ?? true,
      dashboardTransactionCount: json['dashboardTransactionCount'] as int? ?? 5,
      requirePin: json['requirePin'] as bool? ?? false,
      pinHash: json['pinHash'] as String? ?? '',
      hideAmountsInOverview: json['hideAmountsInOverview'] as bool? ?? false,
      enableBiometricAuth: json['enableBiometricAuth'] as bool? ?? false,
      autoLockMinutes: json['autoLockMinutes'] as int? ?? 5,
      enableAutoBackup: json['enableAutoBackup'] as bool? ?? false,
      backupFrequencyDays: json['backupFrequencyDays'] as int? ?? 7,
      lastBackupDate: json['lastBackupDate'] as String? ?? '',
      enableDataSync: json['enableDataSync'] as bool? ?? false,
      enableAdvancedReports: json['enableAdvancedReports'] as bool? ?? true,
      enableBudgetForecasting: json['enableBudgetForecasting'] as bool? ?? true,
      enableCategoryInsights: json['enableCategoryInsights'] as bool? ?? true,
      enableSpendingTrends: json['enableSpendingTrends'] as bool? ?? true,
    );
  }

  /// Format amount according to settings
  String formatAmount(double amount) {
    String formattedAmount = amount.toStringAsFixed(decimalPlaces);
    
    if (useThousandSeparator && amount.abs() >= 1000) {
      final parts = formattedAmount.split('.');
      final integerPart = parts[0];
      final decimalPart = parts.length > 1 ? parts[1] : '';
      
      // Add thousand separators
      String formattedInteger = '';
      for (int i = 0; i < integerPart.length; i++) {
        if (i > 0 && (integerPart.length - i) % 3 == 0) {
          formattedInteger += thousandSeparator;
        }
        formattedInteger += integerPart[i];
      }
      
      formattedAmount = decimalPart.isNotEmpty 
          ? '$formattedInteger$decimalSeparator$decimalPart'
          : formattedInteger;
    }
    
    // Add currency symbol
    final symbol = getCurrencySymbol();
    switch (currencySymbolPosition) {
      case CurrencySymbolPosition.before:
        return '$symbol$formattedAmount';
      case CurrencySymbolPosition.after:
        return '$formattedAmount$symbol';
      case CurrencySymbolPosition.beforeWithSpace:
        return '$symbol $formattedAmount';
      case CurrencySymbolPosition.afterWithSpace:
        return '$formattedAmount $symbol';
    }
  }

  /// Get currency symbol
  String getCurrencySymbol() {
    switch (primaryCurrency) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'CAD':
        return 'C\$';
      case 'AUD':
        return 'A\$';
      case 'CHF':
        return 'CHF';
      case 'CNY':
        return '¥';
      case 'INR':
        return '₹';
      case 'BRL':
        return 'R\$';
      case 'RUB':
        return '₽';
      case 'KRW':
        return '₩';
      case 'SGD':
        return 'S\$';
      case 'HKD':
        return 'HK\$';
      case 'NOK':
        return 'kr';
      case 'SEK':
        return 'kr';
      default:
        return primaryCurrency;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MoneySettings &&
        other.primaryCurrency == primaryCurrency &&
        other.currencySymbolPosition == currencySymbolPosition &&
        other.decimalPlaces == decimalPlaces &&
        other.useThousandSeparator == useThousandSeparator &&
        other.thousandSeparator == thousandSeparator &&
        other.decimalSeparator == decimalSeparator &&
        other.defaultAccountId == defaultAccountId &&
        other.autoCategorize == autoCategorize &&
        other.rememberLastCategory == rememberLastCategory &&
        other.rememberLastAccount == rememberLastAccount &&
        other.enableBudgetAlerts == enableBudgetAlerts &&
        other.enableTransactionReminders == enableTransactionReminders &&
        other.enableLowBalanceAlerts == enableLowBalanceAlerts &&
        other.lowBalanceThreshold == lowBalanceThreshold &&
        other.reminderTime == reminderTime &&
        other.dateFormat == dateFormat &&
        other.timeFormat == timeFormat &&
        other.showAccountBalanceOnDashboard == showAccountBalanceOnDashboard &&
        other.showRecentTransactionsOnDashboard == showRecentTransactionsOnDashboard &&
        other.dashboardTransactionCount == dashboardTransactionCount &&
        other.requirePin == requirePin &&
        other.pinHash == pinHash &&
        other.hideAmountsInOverview == hideAmountsInOverview &&
        other.enableBiometricAuth == enableBiometricAuth &&
        other.autoLockMinutes == autoLockMinutes &&
        other.enableAutoBackup == enableAutoBackup &&
        other.backupFrequencyDays == backupFrequencyDays &&
        other.lastBackupDate == lastBackupDate &&
        other.enableDataSync == enableDataSync &&
        other.enableAdvancedReports == enableAdvancedReports &&
        other.enableBudgetForecasting == enableBudgetForecasting &&
        other.enableCategoryInsights == enableCategoryInsights &&
        other.enableSpendingTrends == enableSpendingTrends;
  }

  @override
  int get hashCode => Object.hashAll([
        primaryCurrency,
        currencySymbolPosition,
        decimalPlaces,
        useThousandSeparator,
        thousandSeparator,
        decimalSeparator,
        defaultAccountId,
        autoCategorize,
        rememberLastCategory,
        rememberLastAccount,
        enableBudgetAlerts,
        enableTransactionReminders,
        enableLowBalanceAlerts,
        lowBalanceThreshold,
        reminderTime,
        dateFormat,
        timeFormat,
        showAccountBalanceOnDashboard,
        showRecentTransactionsOnDashboard,
        dashboardTransactionCount,
        requirePin,
        pinHash,
        hideAmountsInOverview,
        enableBiometricAuth,
        autoLockMinutes,
        enableAutoBackup,
        backupFrequencyDays,
        lastBackupDate,
        enableDataSync,
        enableAdvancedReports,
        enableBudgetForecasting,
        enableCategoryInsights,
        enableSpendingTrends,
      ]);

  @override
  String toString() => 'MoneySettings(currency: $primaryCurrency, decimal: $decimalPlaces)';
}
