import 'package:flutter/material.dart' hide Orientation;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/breakpoints.dart';
import '../services/responsive_service.dart';

// Responsive service provider
final responsiveServiceProvider = Provider<ResponsiveService>((ref) {
  return ResponsiveService();
});

// Responsive config stream provider
final responsiveConfigProvider = StreamProvider<ResponsiveConfig>((ref) {
  final service = ref.watch(responsiveServiceProvider);
  return service.configStream;
});

// Current responsive config provider
final currentResponsiveConfigProvider = Provider<ResponsiveConfig?>((ref) {
  final configAsync = ref.watch(responsiveConfigProvider);
  return configAsync.when(
    data: (config) => config,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Device type providers
final deviceTypeProvider = Provider<DeviceType?>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.deviceType;
});

final screenSizeProvider = Provider<ScreenSize?>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.screenSize;
});

final orientationProvider = Provider<Orientation?>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.orientation;
});

// Layout decision providers
final shouldUseBottomNavigationProvider = Provider<bool>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.shouldUseBottomNavigation() ?? true;
});

final shouldUseNavigationRailProvider = Provider<bool>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.shouldUseNavigationRail() ?? false;
});

final shouldUseDrawerProvider = Provider<bool>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.shouldUseDrawer() ?? true;
});

final shouldUseSidebarProvider = Provider<bool>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.shouldUseSidebar() ?? false;
});

final shouldUseGridLayoutProvider = Provider<bool>((ref) {
  final config = ref.watch(currentResponsiveConfigProvider);
  return config?.shouldUseGridLayout() ?? false;
});

// Responsive actions provider
final responsiveActionsProvider = Provider<ResponsiveActions>((ref) {
  final service = ref.watch(responsiveServiceProvider);
  return ResponsiveActions(service);
});

// Responsive helper class
class ResponsiveActions {
  final ResponsiveService _service;

  ResponsiveActions(this._service);

  // Layout builders
  Widget buildResponsiveLayout({
    Widget? xs,
    Widget? sm,
    Widget? md,
    Widget? lg,
    Widget? xl,
    Widget? xxl,
    required Widget fallback,
  }) {
    return _service.buildResponsiveLayout(
      xs: xs,
      sm: sm,
      md: md,
      lg: lg,
      xl: xl,
      xxl: xxl,
      fallback: fallback,
    );
  }

  Widget buildDeviceLayout({
    Widget? mobile,
    Widget? tablet,
    Widget? desktop,
    Widget? tv,
    required Widget fallback,
  }) {
    return _service.buildDeviceLayout(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      tv: tv,
      fallback: fallback,
    );
  }

  Widget buildResponsiveGrid({
    required List<Widget> children,
    double? spacing,
    double? runSpacing,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
  }) {
    return _service.buildResponsiveGrid(
      children: children,
      spacing: spacing,
      runSpacing: runSpacing,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
    );
  }

  Widget buildResponsiveNavigation({
    required List<NavigationItem> items,
    required int currentIndex,
    required ValueChanged<int> onTap,
    Widget? drawer,
  }) {
    return _service.buildResponsiveNavigation(
      items: items,
      currentIndex: currentIndex,
      onTap: onTap,
      drawer: drawer,
    );
  }

  Widget buildResponsiveContent({
    required Widget content,
    Widget? sidebar,
    bool centerContent = true,
  }) {
    return _service.buildResponsiveContent(
      content: content,
      sidebar: sidebar,
      centerContent: centerContent,
    );
  }

  Widget buildResponsiveCard({
    required Widget child,
    VoidCallback? onTap,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    return _service.buildResponsiveCard(
      child: child,
      onTap: onTap,
      padding: padding,
      margin: margin,
    );
  }

  Widget buildResponsiveList({
    required List<Widget> children,
    bool shrinkWrap = true,
    ScrollPhysics? physics,
    EdgeInsets? padding,
  }) {
    return _service.buildResponsiveList(
      children: children,
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
    );
  }

  // Dialog helpers
  Future<T?> showResponsiveDialog<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
    String? barrierLabel,
  }) {
    return _service.showResponsiveDialog<T>(
      context: context,
      child: child,
      barrierDismissible: barrierDismissible,
      barrierLabel: barrierLabel,
    );
  }

  Future<T?> showResponsiveBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = true,
  }) {
    return _service.showResponsiveBottomSheet<T>(
      context: context,
      child: child,
      isScrollControlled: isScrollControlled,
    );
  }

  // Style helpers
  TextStyle getResponsiveTextStyle(TextStyle baseStyle) {
    return _service.getResponsiveTextStyle(baseStyle);
  }

  double getResponsiveIconSize([double? baseSize]) {
    return _service.getResponsiveIconSize(baseSize);
  }

  Size getResponsiveButtonSize([Size? baseSize]) {
    return _service.getResponsiveButtonSize(baseSize);
  }

  Duration getResponsiveAnimationDuration([Duration? baseDuration]) {
    return _service.getResponsiveAnimationDuration(baseDuration);
  }

  // Utility methods
  bool isMobile() => _service.isMobile();
  bool isTablet() => _service.isTablet();
  bool isDesktop() => _service.isDesktop();
  bool isTV() => _service.isTV();

  bool isPortrait() => _service.isPortrait();
  bool isLandscape() => _service.isLandscape();

  bool isSmallScreen() => _service.isSmallScreen();
  bool isMediumScreen() => _service.isMediumScreen();
  bool isLargeScreen() => _service.isLargeScreen();
}

// Responsive widget builder
class ResponsiveBuilder extends ConsumerWidget {
  final Widget Function(BuildContext context, ResponsiveConfig config) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final config = ref.watch(currentResponsiveConfigProvider);
    
    if (config == null) {
      // Initialize with current screen size
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final size = MediaQuery.of(context).size;
        ref.read(responsiveServiceProvider).initialize(size);
      });
      
      // Return a loading state or fallback
      return const Center(child: CircularProgressIndicator());
    }

    return builder(context, config);
  }
}

// Responsive layout widget
class ResponsiveLayout extends ConsumerWidget {
  final Widget? xs;
  final Widget? sm;
  final Widget? md;
  final Widget? lg;
  final Widget? xl;
  final Widget? xxl;
  final Widget fallback;

  const ResponsiveLayout({
    super.key,
    this.xs,
    this.sm,
    this.md,
    this.lg,
    this.xl,
    this.xxl,
    required this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(responsiveActionsProvider);
    
    return actions.buildResponsiveLayout(
      xs: xs,
      sm: sm,
      md: md,
      lg: lg,
      xl: xl,
      xxl: xxl,
      fallback: fallback,
    );
  }
}

// Device layout widget
class DeviceLayout extends ConsumerWidget {
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? tv;
  final Widget fallback;

  const DeviceLayout({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.tv,
    required this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(responsiveActionsProvider);
    
    return actions.buildDeviceLayout(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      tv: tv,
      fallback: fallback,
    );
  }
}

// Responsive grid widget
class ResponsiveGrid extends ConsumerWidget {
  final List<Widget> children;
  final double? spacing;
  final double? runSpacing;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisAlignment mainAxisAlignment;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing,
    this.runSpacing,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(responsiveActionsProvider);
    
    return actions.buildResponsiveGrid(
      children: children,
      spacing: spacing,
      runSpacing: runSpacing,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
    );
  }
}

// Responsive card widget
class ResponsiveCard extends ConsumerWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actions = ref.watch(responsiveActionsProvider);
    
    return actions.buildResponsiveCard(
      child: child,
      onTap: onTap,
      padding: padding,
      margin: margin,
    );
  }
}

// Initialize responsive service provider
final initializeResponsiveServiceProvider = FutureProvider<void>((ref) async {
  // The responsive service is initialized when the first widget is built
  // This provider is mainly for dependency tracking
});
