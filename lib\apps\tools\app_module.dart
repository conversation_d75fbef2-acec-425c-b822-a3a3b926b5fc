import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import '../../core/interfaces/app_module_interface.dart';
import 'routes.dart';
import 'dependencies.dart';
import 'screens/tools_screen.dart';

/// Tools Builder mini-app module
class ToolsAppModule implements AppModuleInterface {
  @override
  String get name => 'Tools Builder';
  
  @override
  String get id => 'tools';
  
  @override
  String get description => 'Create custom calculators, converters, and productivity tools with Excel-like formulas and visual UI builder';
  
  @override
  String get version => '1.0.0';
  
  @override
  IconData get icon => Icons.build_circle;
  
  @override
  Color get primaryColor => Colors.orange;
  
  @override
  List<String> get requiredPermissions => ['WRITE_EXTERNAL_STORAGE', 'READ_EXTERNAL_STORAGE'];
  
  @override
  List<String> get dependencies => ['core', 'shared'];
  
  @override
  Map<String, dynamic> get exportConfig => {
    'appName': 'ShadowSuite Tools Builder',
    'packageName': 'com.shadowsuite.tools',
    'description': 'Create custom calculators and productivity tools',
    'minSdkVersion': 21,
    'targetSdkVersion': 34,
    'versionCode': 1,
    'versionName': '1.0.0',
    'features': [
      'Excel-like formula engine',
      'Visual UI builder',
      'Import/Export Excel files',
      'Custom tool creation',
      'Real-time calculations',
    ],
    'permissions': [
      'android.permission.WRITE_EXTERNAL_STORAGE',
      'android.permission.READ_EXTERNAL_STORAGE',
    ],
  };
  
  @override
  Widget get homeScreen => const ToolsScreen();
  
  @override
  List<RouteBase> get routes => ToolsRoutes.routes;
  
  @override
  Future<void> initialize() async {
    await ToolsDependencies.initialize();
  }
  
  @override
  Future<void> dispose() async {
    await ToolsDependencies.dispose();
  }
  
  @override
  bool get canRunStandalone => true; // Tools Builder can run independently
  
  @override
  List<String> get requiredModules => []; // No other modules required

  @override
  bool canExportStandalone() {
    return canRunStandalone && requiredModules.isEmpty;
  }
  
  /// Get tool-specific theme with orange accent
  @override
  ThemeData getThemeData(BuildContext context) {
    return Theme.of(context).copyWith(
      colorScheme: Theme.of(context).colorScheme.copyWith(
        primary: primaryColor,
        secondary: Colors.deepOrange,
        tertiary: Colors.amber,
      ),
      appBarTheme: Theme.of(context).appBarTheme.copyWith(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }
  
  /// Additional export configuration for Tools Builder
  @override
  Map<String, dynamic> getExportConfig() {
    final config = Map<String, dynamic>.from(exportConfig);
    
    // Add Tools Builder specific configuration
    config['mainActivity'] = 'com.shadowsuite.tools.MainActivity';
    config['applicationClass'] = 'com.shadowsuite.tools.ToolsApplication';
    config['splashScreen'] = 'tools_splash';
    config['appIcon'] = 'tools_icon';
    config['themeColor'] = '#FF9800'; // Orange
    
    // Add required assets
    config['assets'] = [
      'assets/tools/',
      'assets/formulas/',
      'assets/templates/',
    ];
    
    // Add required dependencies for standalone operation
    config['dependencies'] = [
      'flutter_riverpod',
      'go_router',
      'shared_preferences',
      'path_provider',
      'file_picker',
      'excel',
    ];
    
    return config;
  }
}
